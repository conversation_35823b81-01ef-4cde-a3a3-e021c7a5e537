{if $cart_agreements || $checkout_accept_terms_and_conditions}
    <script type="text/javascript">
        //<![CDATA[
        Tygh.$.ceFormValidator('registerValidator', {
            class_name: 'cm-check-agreement',
            message: '{__("checkout_terms_n_conditions_alert")|escape:javascript}',
            func: function (id) {
                return $('#' + id).prop('checked');
            }
        });

        {if $iframe_mode}
        function fn_check_agreements(suffix) {
            var $ = Tygh.$;
            var checked = $('form[name=payments_form_' + suffix + '] input[type=checkbox].cm-agreement').prop('checked');
            $('#payment_method_iframe' + suffix).toggleClass('hidden', checked);
        }
        {/if}
        //]]>
    </script>
    {if $checkout_accept_terms_and_conditions}
        <div class="form-group terms">
                {assign var="legacy_url" value='pages.view?page_id=7'|fn_url}
                {assign var="legacy_link" value='<a href="'|cat:$legacy_url|cat:'">'|cat:__('w_legacy')|cat:'</a>'}
                {assign var="terms_of_use_url" value='pages.view?page_id=12'|fn_url}
                {assign var="terms_of_use_link" value='<a href="'|cat:$terms_of_use_url|cat:'">'|cat:__('w_terms_of_use')|cat:'</a>'}
                {assign var="terms_and_conditions_url" value='pages.view?page_id=8'|fn_url}
                {assign var="terms_and_conditions_link" value='<a href="'|cat:$terms_and_conditions_url|cat:'">'|cat:__('w_terms_and_conditions')|cat:'</a>'}
                <label for="id_accept_terms{$suffix}" class="cm-check-agreement">
                    <input type="checkbox"
                        id="id_accept_terms{$suffix}"
                        name="accept_terms" value="Y"
                        {if $smarty.request.accept_terms}checked="checked"{/if}
                        class="cm-agreement checkbox test-terms-and-conditions"
                        {if $iframe_mode}onclick="fn_check_agreements('{$suffix}');"{/if} />
                    {__("checkout_terms_n_conditions" , ["[legacy]" => $legacy_link , "[terms_of_use]" => $terms_of_use_link , "[terms_and_conditions]" => $terms_and_conditions_link]) nofilter}
                    {if !empty($companyTerms)}
                        <br />{__("accept_company_terms")}:
                        {foreach $companyTerms as $terms}
                            <a href="{$terms.link}" target="_blank">{$terms.name}</a>
                        {/foreach}
                    {/if}
                </label>
        </div>
    {/if}
    {if $cart_agreements}
        <div class="control-group license-agreement">
            {hook name="checkout:terms_and_conditions_downloadable"}
                <label for="product_agreements_{$suffix}" class="cm-check-agreement"><input type="checkbox"
                                                                                             id="product_agreements_{$suffix}"
                                                                                             name="agreements[]"
                                                                                             value="Y"
                                                                                             class="cm-agreement checkbox"
                                                                                             {if $iframe_mode}onclick="fn_check_agreements('{$suffix}');"{/if}/><span>{__("checkout_edp_terms_n_conditions")}</span>&nbsp;<a
                            id="sw_elm_agreements_{$suffix}"
                            class="cm-combination link-dashed">{__("license_agreement")}</a></label>
            {/hook}
            <div class="hidden" id="elm_agreements_{$suffix}">
                {foreach from=$cart_agreements item="product_agreements"}
                    {foreach from=$product_agreements item="agreement"}
                        <p>{$agreement.license nofilter}</p>
                    {/foreach}
                {/foreach}
            </div>
        </div>
    {/if}
{/if}
<p>{__("legal_payment_obligation")}</p>
