<script type="text/javascript">
    $(function () {
        $(".breadcrumb-panier li:nth-child(2)").addClass("breadcrumb-actif");
    });
</script>
<div class="step-container{if $edit}-active{/if} step-one" data-ct-checkout="user_info" id="step_one">
    {if $settings.General.checkout_style != "multi_page"}
        <h2 class="step-title{if $edit}-active{/if}{if $complete && !$edit}-complete{/if} clearfix">
            <span class="float-left">{if !$complete || $edit}1{/if}{if $complete && !$edit}<i class="icon-ok"></i>{/if}</span>

            {if $complete && !$edit}
                {hook name="checkout:edit_link"}
                    <span class="float-right">
                    {include file="buttons/button.tpl" but_meta="cm-ajax" but_href="checkout.checkout?edit_step=step_one&from_step=$edit_step" but_target_id="checkout_*" but_text=__("change") but_role="tool"}
                </span>
                {/hook}
            {/if}

            {if ($settings.General.disable_anonymous_checkout == "Y" && !$auth.user_id) || ($settings.General.disable_anonymous_checkout != "Y" && !$auth.user_id && !$contact_info_population) || $smarty.session.failed_registration == true}
                {assign var="title" value=__("please_sign_in")}
            {else}
                {if $auth.user_id != 0}
                    {if $user_data.firstname || $user_data.lastname}
                        {assign var="login_info" value="`$user_data.firstname`&nbsp;`$user_data.lastname`"}
                    {else}
                        {if $settings.General.use_email_as_login == "Y"}
                            {assign var="login_info" value="`$user_data.email`"}
                        {else}
                            {assign var="login_info" value="`$user_data.user_login`"}
                        {/if}
                    {/if}
                {else}
                    {assign var="login_info" value=__("guest")}
                {/if}

                {assign var="title" value="{__("signed_in_as")} `$login_info`"}
            {/if}

            {hook name="checkout:edit_link_title"}
                <a class="title{if $contact_info_population && !$edit} cm-ajax{/if}"
                   {if $contact_info_population && !$edit}href="{"checkout.checkout?edit_step=step_one&from_step=`$edit_step`"|fn_url}"
                   data-ca-target-id="checkout_*"{/if}>{$title nofilter}</a>
            {/hook}
        </h2>
    {/if}

    <div id="step_one_body" class="step-body{if $edit}-active{/if}{if !$edit} hidden{/if}">
        <div id="step_one_login" class="test-checkout">
            <div class="clearfix">
                {include file="views/checkout/components/checkout_login.tpl" checkout_type="one_page"}
            </div>
        </div>
    </div>
    <!--step_one--></div>
