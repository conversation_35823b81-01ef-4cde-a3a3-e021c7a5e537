{capture name="tabs_content"}
{include file="common/pagination.tpl" save_current_page=true save_current_url=true}
<table class="table table-width no-inner-border outter-border small-margin middle">
    <thead>
    <tr>
        <th style="width: 20%">{__('discuss_with')}</th>
        <th style="width: 40%">{__('discuss_last_message')}</th>
        <th style="width: 20%">{__('title')}</th>
        <th style="width: 20%">&nbsp;</th>
    </tr>
    </thead>
    <tbody>
    {foreach $discussions as $discussion }
    <tr>
        {$last_message = $last_messages[$discussion->getId()]}
        {$discussion_url = "discuss.view?discussion_id=`$discussion->getId()`"|fn_url}
        {$discussion_class = ''}
        {if !$last_message->isRead() && $last_message->getAuthor() != $auth.user_id}
            {$discussion_class = "bold"}
        {/if}
        <td>
            {$discussion_url = "discuss.view?discussion_id=`$discussion->getId()`"|fn_url}
            <a href="{$discussion_url}">{$interlocutors[$discussion->getId()]}</a>
        </td>
        <td>
            <a href="{$discussion_url}" class="{$discussion_class}">
                {$last_message->getSendDate()|date_format:"`$settings.Appearance.date_format`"} -
                {$last_message->getContent()|strip_tags|truncate}
            </a>
        </td>
        <td>
            <a href="{$discussion_url}" class="{$discussion_class}">
                {$discussion->getMetaData('title')|truncate:40}
            </a>
        </td>
        <td><a href="{"discuss.hide?discussion_id=`$discussion->getId()`"|fn_url}" class="cm-confirm">{__('discuss_hide_discussion')}</a></td>
    </tr>
    {foreachelse}
    <tr>
        <td colspan="4"><p class="no-items">{__("discuss_front_header")}</p></td>
    </tr>
    {/foreach}
    </tbody>
</table>
{include file="common/pagination.tpl" save_current_page=true save_current_url=true}
{/capture}

{include file="views/profiles/components/profile_menu.tpl" content=$smarty.capture.tabs_content active_tab=messages}
