{assign var="return_type" value=$return_type|default:"SAV"}
<div class="modal-header">
    <h2>{if $return_type=="SAV"}
            {__('w_title_popin_rma_create_SAV',['[order_id]' => $order_info.order_id])}
        {else}
            {__('w_title_popin_rma_create_litigation',['[order_id]' => $o.order_id])}
        {/if}
    </h2>
</div>
<div class="modal-body">
    <div class="row">
        <div class="col-xs-12 rma">
            <div class="rma-register">
                <p>{__("w_`$return_type`_explanation")}</p>
                {assign var="my_account_url" value='profiles.update'|fn_url}
                {assign var="my_account_link" value='<a href="'|cat:$my_account_url|cat:'">'|cat:__('my_account')|cat:'</a>'}
                <p style="margin-bottom:20px;">{__(
                    'w_SAV_email_info', [
                                '[email]'       => $user.email,
                                '[my_account]'  => $my_account_link])
                    nofilter}</p>
                <b>{'w_select_article_for_SAV'|__}</b>
                <form action="{""|fn_url}" method="post" name="return_registration_form">
                    <input name="order_id" type="hidden" value="{$smarty.request.order_id}" />
                    <input name="user_id" type="hidden" value="{$order_info.user_id}" />
                    <input name="company_id" type="hidden" value="{$order_info.company_id}" />
                    <div class="table-responsive membre-tableau">
                        <table class="table">
                            <thead>
                            <tr>
                                <th></th>
                                <th>{__("product")}</th>
                                <th></th>
                                <th>{__("options")}</th>
                                <th class="left">{__("price")}</th>
                                <th>{__("quantity")}</th>
                                <th>{__("reason")}</th>
                            </tr>
                            </thead>
                            {foreach from=$order_info.products item="oi" key="key"}
                                <tr {cycle values=",class=\"table-row\""}>
                                    <td class="center middle rma-register-id">
                                        <input type="checkbox" name="sav[{$oi.cart_id}][chosen]" id="delete_checkbox" value="Y" class="checkbox cm-item" />
                                        <input type="hidden" name="sav[{$oi.cart_id}][product_id]" value="{$oi.product_id}" /></td>
                                    {assign var='product_current_data' value=$oi.product_id|fn_get_product_data:$smarty.session.auth}
                                    <td class="middle">{include file="common/image.tpl" images=$product_current_data.main_pair image_width=80 image_height=80}</td>
                                    <td class="middle">
                                        {$product_current_data.product_features|fn_product_get_brand}<br />
                                        {$oi.product}
                                    </td>
                                    <td class="middle">
                                        {if $oi.product_options}
                                            <ul class="product-options-summary">
                                                {foreach from=$oi.product_options item="option"}
                                                    <li>{$option.variant_name}</li>
                                                {/foreach}
                                            </ul>
                                        {/if}
                                    </td>

                                    <td class="middle nowrap">
                                        <span class="w-price small-price">{include file="common/price.tpl" value=$oi.display_subtotal}</span> <br /> {$oi.amount} x {include file="common/price.tpl" value=$oi.price}
                                    </td>
                                    <td class="center middle">
                                        {$oi.amount}
                                    </td>
                                    <td class="center middle">
                                        {if $reasons}
                                            <select name="sav[{$oi.cart_id}][reason]" class="form-control reason-select">
                                                {foreach from=$reasons item="reason"}
                                                    <option value="{__($reason)}">{__($reason)}</option>
                                                {/foreach}
                                            </select>
                                        {/if}</td>
                                </tr>
                                {foreachelse}
                                <tr>
                                    <td colspan="6"><p class="no-items">{__("no_items")}</p></td>
                                </tr>
                            {/foreach}
                        </table>
                    </div>

                    <div class="form-group">
                        <label for="sav_comment_{$order_info.order_id}" class="col-xs-12 cm-required"><b>{__("type_comment")}</b></label>
                        <div class="col-md-6 col-xs-12">
                            <textarea id="sav_comment_{$order_info.order_id}" name="comment" cols="10"  class="form-control" rows="4"></textarea>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <hr/>
                        <button class="btn btn-primary cm-process-items" name="dispatch[rma.add_{$return_type}]">{__("send")}</button>
                    </div>

                </form>

                {capture name="mainbox_title"}{__("return_registration")}{/capture}
            </div>
        </div>

    </div>

</div>
