<div class="hidden" id="new_post_dialog_{$obj_prefix}{$obj_id}" title="{$new_post_title}">
    <div style="height:50px;"></div>
    <div id="connection_form_post">
        {if empty($auth.user_id)}
            {include file="views/auth/ajax_login_or_register.tpl" style="ajax" ajax=true result_ids="connection_form_post,name_area,send_button_post,"}
        {/if}
        <!--connection_form_post--></div>
<form action="{""|fn_url}" method="post" class="{if !$post_redirect_url}cm-ajax cm-form-dialog-closer{/if} posts-form" name="add_post_form" id="add_post_form_{$obj_prefix}{$obj_id}">
<input type="hidden" name="result_ids" value="posts_list,new_post,average_rating*">
<input type ="hidden" name="post_data[thread_id]" value="{$discussion.thread_id}" />
<input type ="hidden" name="redirect_url" value="{$post_redirect_url|default:$config.current_url}" />
<input type="hidden" name="selected_section" value="" />

<div id="new_post_{$obj_prefix}{$obj_id}">
    <div id="name_area">
        {if !empty($auth.user_id)}
            <div class="control-group">
                <label for="dsc_name_{$obj_prefix}{$obj_id}" class="cm-required">{__("your_pseudonym")}</label>
                <input type="text" id="dsc_name_{$obj_prefix}{$obj_id}" name="post_data[name]"  size="50" class="input-text" />
            </div>
        {/if}
        <!--name_area--></div>

{if $discussion.type == "R" || $discussion.type == "B"}
<div class="control-group">
    {$rate_id = "rating_`$obj_prefix``$obj_id`"}
    <label for="{$rate_id}" class="cm-required cm-multiple-radios">{__("your_rating")}</label>
    {include file="addons/discussion/views/discussion/components/rate.tpl" rate_id=$rate_id rate_name="post_data[rating_value]"}
</div>
{/if}

{hook name="discussion:add_post"}
{if $discussion.type == "C" || $discussion.type == "B"}
<div class="control-group">
    <label for="dsc_message_{$obj_prefix}{$obj_id}" class="cm-required">{__("your_message")}</label>
    <textarea id="dsc_message_{$obj_prefix}{$obj_id}" name="post_data[message]" class="input-textarea" rows="5" cols="72">{$discussion.post_data.message}</textarea>
</div>
{/if}
{/hook}

<!--new_post_{$obj_prefix}{$obj_id}--></div>

<div id="send_button_post">
    {include file="buttons/required_connection_button.tpl" but_text=__("submit") but_role="submit" but_name="dispatch[discussion.add]"}
<!--send_button_post--></div>


</form>
</div>
