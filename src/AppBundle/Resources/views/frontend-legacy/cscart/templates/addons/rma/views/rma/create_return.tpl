<div class="modal-header">
    <h2>{__('w_title_popin_rma_create_return',['[order_id]' => $order_info.order_id])}</h2>
</div>
<div class="modal-body">
    <div class="row">
        <div class="col-xs-12 rma">
            <div class="rma-register">
                <div class="table-responsive membre-tableau">
                    <table class="table">
                        <thead>
                        <tr>
                            <th style="width: 15%">{__("w_order_number")}</th>
                            <th style="width: 15%">{__("date")}</th>
                            <th style="width: 15%">{__("vendor")}</th>
                            <th style="width: 10%">{__("total")}</th>
                            <th style="width: 25%">{__("status")}</th>
                        </tr>
                        </thead>
                        <tr {cycle values=",class=\"table-row\""}>
                            <td>{$order_info.order_id}</td>
                            <td>{$order_info.timestamp|date_format:"`$settings.Appearance.date_format`"}</td>
                            <td>{$order_info.company_id|fn_get_company_name}</td>
                            <td>{include file="common/price.tpl" value=$order_info.total}</td>
                            <td>{include file="common/status.tpl" status=$order_info.status display="view"}</td>
                        </tr>

                    </table>
                </div>
                <br/>
        <form action="{""|fn_url}" method="post" name="return_registration_form">
        <input name="order_id" type="hidden" value="{$smarty.request.order_id}" />
        <input name="user_id" type="hidden" value="{$order_info.user_id}" />
        <input name="action" type="hidden" value="2" />
        <b>{'w_select_article_to_return'|__}</b>
            <div class="table-responsive membre-tableau">
                <table class="table">
                    <thead>
                    <tr>
                        <th></th>
                        <th>{__("product")}</th>
                        <th></th>
                        <th>{__("options")}</th>
                        <th class="left">{__("price")}</th>
                        <th>{__("quantity")}</th>
                        <th>{__("reason")}</th>
                    </tr>
                    </thead>
                    {foreach from=$order_info.products item="oi" key="key"}
                        <tr {cycle values=",class=\"table-row\""}>
                            <td class="center middle rma-register-id">
                                <input type="checkbox" name="returns[{$oi.cart_id}][chosen]" id="delete_checkbox" value="Y" class="checkbox cm-item" />
                                <input type="hidden" name="returns[{$oi.cart_id}][product_id]" value="{$oi.product_id}" /></td>
                            {assign var='product_current_data' value=$oi.product_id|fn_get_product_data:$smarty.session.auth}
                            <td class="middle">{include file="common/image.tpl" images=$product_current_data.main_pair image_width=80 image_height=80}</td>
                            <td class="middle">
                                {$product_current_data.product_features|fn_product_get_brand}<br />
                                {$oi.product}
                            </td>
                            <td class="middle">
                                {if $oi.product_options}
                                    <ul class="product-options-summary">
                                        {foreach from=$oi.product_options item="option"}
                                            <li>{$option.variant_name}</li>
                                        {/foreach}
                                    </ul>
                                {/if}
                            </td>

                            <td class="middle nowrap">
                                <span class="w-price small-price">{include file="common/price.tpl" value=$oi.display_subtotal}</span> <br /> {$oi.amount} x {include file="common/price.tpl" value=$oi.price}
                            </td>
                            <td class="center middle">
                                <input type="hidden" name="returns[{$oi.cart_id}][available_amount]" value="{$oi.amount}" />
                                <select class="form-control" name="returns[{$oi.cart_id}][amount]">
                                    {for $qty=1 to $oi.amount step 1}
                                        <option value="{$qty}">{$qty}</option>
                                    {/for}
                                </select></td>
                            <td class="center middle">
                                {if $reasons}
                                    <select name="returns[{$oi.cart_id}][reason]" class="form-control reason-select">
                                        {foreach from=$reasons item="reason" key="reason_id"}
                                            <option value="{$reason_id}">{$reason.property}</option>
                                        {/foreach}
                                    </select>
                                {/if}</td>
                        </tr>
                        {foreachelse}
                        <tr>
                            <td colspan="6"><p class="no-items">{__("no_items")}</p></td>
                        </tr>
                    {/foreach}
                </table>
            </div>

            <div class="form-group">
                <label for="comment" class="col-xs-12">{__("type_comment")}</label>
                <div class="col-md-6 col-xs-12 ">
                    <textarea name="comment" cols="10"  rows="4" class="form-control"></textarea>
                </div>
            </div>
            <div class="col-xs-12">
                <hr/>
                <button class="btn btn-primary cm-process-items" name="dispatch[rma.add_return]">{__("next_step")}</button>
            </div>
        </form>

        {capture name="mainbox_title"}{__("return_registration")}{/capture}
            </div>
        </div>
    </div>
</div>
