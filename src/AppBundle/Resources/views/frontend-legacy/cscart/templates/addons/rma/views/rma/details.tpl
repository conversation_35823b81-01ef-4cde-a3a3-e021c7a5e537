{if $return_info}
<div class="modal-header">
    <h2>{__('w_title_popin_rma_detail',['[return_id]' => $return_info.return_id])}</h2>
</div>
<div class="modal-body">
    <div class="row">
        <div class="col-xs-12">

            <form action="{""|fn_url}" method="post" name="return_info_form" />
            <input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
            <input type="hidden" name="order_id" value="{$return_info.order_id}" />
            <input type="hidden" name="total_amount" value="{$return_info.total_amount}" />
            <input type="hidden" name="return_status" value="{$return_info.status}" />

            {** RETURN PRODUCTS SECTION **}
            <div id="content_return_products" class="table-responsive membre-tableau">
                <table class="table">
                    <thead>
                    <tr>
                        <th colspan="2" style="width: 30%">{__("product")}</th>
                        <th style="width: 15%">{__("options")}</th>
                        <th style="width: 10%">{__("amount")}</th>
                        <th style="width: 5%">{__("price")}</th>
                        <th style="width: 45%">{__("reason")}</th>
                    </tr>
                    </thead>
                    {foreach from=$return_info.items[$smarty.const.RETURN_PRODUCT_ACCEPTED] item="product" key="key"}
                        <tr {cycle values=",class=\"table-row\""}>
                            {assign var='product_current_data' value=$product.product_id|fn_get_product_data:$smarty.session.auth}
                            <td style="width: 15%" class="middle">{include file="common/image.tpl" images=$product_current_data.main_pair image_width=80 image_height=80}</td>
                            <td style="width: 15%" class="middle">
                                {$product_current_data.product_features|fn_product_get_brand}<br />
                                {$product.product}
                            </td>
                            <td style="width: 15%" class="middle">
                                {if $product.product_options}
                                    <ul class="product-options-summary">
                                        {foreach from=$product.product_options item="option"}
                                            <li>{$option.variant_name}</li>
                                        {/foreach}
                                    </ul>
                                {/if}
                            </td>
                            <td style="width: 10%" class="middle">{$product.amount}</td>
                            <td style="width: 45%" class="middle"><span class="w-price small-price">{include file="common/price.tpl" value=$product.amount * $product.price}</span> <br /> {$product.amount} x {include file="common/price.tpl" value=$product.price}</td>
                            <td class="nowrap">
                                {assign var="reason_id" value=$product.reason}
                                {$reasons.$reason_id.property}</td>
                        </tr>
                    {/foreach}
                </table>

            </div>
            {** /RETURN PRODUCTS SECTION **}


            {if $return_info.comment}
                <div class="rma-comments">
                    {include file="common/subheader.tpl" title=__("comments")}
                    <div class="rma-comments-body">
                        <span class="caret"> <span class="caret-outer"></span> <span class="caret-inner"></span></span>
                        {$return_info.comment|nl2br}
                    </div>
                </div>
            {/if}
            </form>
        </div>
    </div>
</div>
{/if}