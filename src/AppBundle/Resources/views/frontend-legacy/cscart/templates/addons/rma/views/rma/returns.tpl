{capture name="tabs_content"}
    <div id="content_returns">
        <div class="container-full">
            {foreach from=$w_orders item="w_o" key="w_o_type"}
                <b>{$w_o_type}</b>
                <div class="membre-tableau table-responsive">
                    <table class="table">
                        <thead>
                        <tr>
                            <th style="width: 5%">{__("w_order_number")}</th>
                            <th style="width: 15%">{__("date")}</th>
                            <th style="width: 15%">{__("vendor")}</th>
                            <th style="width: 10%">{__("total")}</th>
                            <th style="width: 35%">{__("status")}</th>
                            <th style="width: 10%">&nbsp;</th>
                            <th style="width: 10%">&nbsp;</th>

                        </tr>
                        </thead>
                        {foreach from=$w_o item="o"}
                            <tr>
                                <td>{$o.order_id}</td>
                                <td>{$o.timestamp|date_format:"`$settings.Appearance.date_format`"}</td>
                                <td>{$o.company_id|fn_get_company_name}</td>
                                <td>{include file="common/price.tpl" value=$o.total}</td>
                                <td>{include file="common/status.tpl" status=$o.status display="view"}</td>
                                <td>
                                    <a
                                            href="{"rma.create_return?order_id=`$o.order_id`"|fn_url}"
                                            data-remote="{"rma.create_return?order_id=`$o.order_id`"|fn_url} .mainbox-body"
                                            data-toggle="modal"
                                            data-target="#generic_modal"
                                    >{__("return_registration")}</a>
                                </td>
                                </td>
                                <td>
                                    <a
                                            href="{"orders.details?order_id=`$o.order_id`"|fn_url}"
                                            data-remote="{"orders.details?order_id=`$o.order_id`"|fn_url} .mainbox-body"
                                            data-toggle="modal"
                                            data-target="#generic_modal"
                                    >{__("w_details_order")}</a>
                                </td>
                            </tr>
                            {foreachelse}
                            <tr>
                                <td colspan="7"><p class="no-items">{__("text_no_orders")}</p></td>
                            </tr>
                        {/foreach}
                    </table>
                </div>
                <br/>
                <br/>
            {/foreach}
            <div class="rma">
                <form action="{""|fn_url}" method="post" name="rma_list_form">
                    {include file="common/pagination.tpl"}

                    {assign var="c_url" value=$config.current_url|fn_query_remove:"sort_by":"sort_order"}
                    {if $search.sort_order == "asc"}
                        {assign var="sort_sign" value="<i class=\"icon-down-dir\"></i>"}
                    {else}
                        {assign var="sort_sign" value="<i class=\"icon-up-dir\"></i>"}
                    {/if}
                    <b>{__('w_rma_history')}</b>
                    <div class="membre-tableau table-responsive">

                        <table class="table">

                            <thead>
                            <tr>
                                <th style="width: 5%">{__("w_order_number")}</th>
                                <th style="width: 20%">{__("date")}</th>
                                <th style="width: 20%">{__("vendor")}</th>
                                <th style="width: 13%">{__("status")}</th>
                                <th style="width: 20%">{__("total_amount")}</th>
                                <th style="width: 11%"></th>
                                <th style="width: 11%"></th>
                            </tr>
                            </thead>
                            {foreach from=$return_requests item="request"}
                            <tbody>
                            <tr {cycle values=",class=\"table-row\""}>
                                <td class="center">{$request.order_id}</td>
                                <td>{$request.timestamp|date_format:"`$settings.Appearance.date_format`"}</td>
                                <td>{$request.company}</td>
                                <td>
                                    <input type="hidden" name="origin_statuses[{$request.return_id}]" value="{$request.status}">
                                    {include file="common/status.tpl" status=$request.status display="view" name="return_statuses[`$request.return_id`]" status_type=$smarty.const.STATUSES_RETURN}
                                </td>
                                <td>{include file="common/price.tpl" value=$request.refund_sum}</td>
                                <td>{if $request.status == RmaStatus::COMPLETED}
                                    <a href="{"rma.print_rma?return_id=`$request.return_id`"|fn_url}" target="_blank">{__('w_get_rma')}</a>{/if}&nbsp;
                                </td>
                                <td class="center">
                                    <a
                                            href="{"rma.details?return_id=`$request.return_id`"|fn_url}"
                                            data-remote="{"rma.details?return_id=`$request.return_id`"|fn_url} .mainbox-body"
                                            data-toggle="modal"
                                            data-target="#generic_modal"
                                    >{__('w_details_order')}</a>
                                </td>
                            <tr>
                                {foreachelse}
                            <tr>
                                <td colspan="6"><p class="no-items">{__("no_return_requests_found")}</p></td>
                            </tr>
                            {/foreach}
                            </tbody>
                        </table>
                    </div>
                    {include file="common/pagination.tpl"}
                </form>
            </div>
            {foreach from=$return_requests item="request"}
            <div id="popin-return-infos_{$request.return_id}" class="hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>{__('w_title_popin_rma_create_return',['[order_id]' => $request.order_data.order_id])}</h2>
                    </div>
                    <div class="modal-body">
                        {capture name="address"}
                            <div class="card bold">
                                {assign var="address_datas" value=$request.order_data.company_id|fn_w_get_rma_address}
                                <ul>
                                    <li>{$address_datas.company}</li>
                                    <li>{$address_datas.address}</li>
                                    <li>{$address_datas.zipcode} {$address_datas.city}</li>
                                </ul>
                            </div>
                        {/capture}
                        {__("w_rma_confirmation_text" , ['[address]' => $smarty.capture.address])}
                    </div>
                </div>
            </div>
            {/foreach}
            {if $smarty.request.auto_open}
                <script type="text/javascript">
                    $(function(){
                        $('#generic_modal .modal-content').replaceWith($('#popin-return-infos_{$smarty.request.auto_open}').html());
                        $('#generic_modal').modal('show');
                    });
                </script>
            {/if}

        </div>
        <!--content_returns--></div>
    <div id="content_w_post_sales_support">
        <div class="container-full">
            <p style="margin-bottom:20px;">{'w_pss_header'|__}</p>
            <b>{'w_ended_orders_select_for_SAV'|__}</b>
            <div class="membre-tableau table-responsive">
                <table class="table">
                    <thead>
                    <tr>
                        <th style="width: 5%"> {__("w_order_number")}</th>
                        <th style="width: 15%">{__("date")}</th>
                        <th style="width: 15%">{__("vendor")}</th>
                        <th style="width: 10%">{__("total")}</th>
                        <th style="width: 25%">{__("status")}</th>
                        <th style="width: 30%">&nbsp;</th>

                    </tr>
                    </thead>
                    {foreach from=$w_sav_orders item="o"}
                        {if $o.w_company_type == User::VENDOR_TYPE}
                            <tr>
                                <td>{$o.order_id}</td>
                                <td>{$o.timestamp|date_format:"`$settings.Appearance.date_format`"}</td>
                                <td>{$o.company_id|fn_get_company_name}</td>
                                <td>{include file="common/price.tpl" value=$o.total}</td>
                                <td>{include file="common/status.tpl" status=$o.status display="view"}</td>
                                <td>
                                    <a
                                            href="{"rma.create_SAV?order_id=`$o.order_id`"|fn_url}"
                                            data-remote="{"rma.create_SAV?order_id=`$o.order_id`"|fn_url} .mainbox-body"
                                            data-toggle="modal"
                                            data-target="#generic_modal"
                                    >{__("w_contact")}</a>

                                </td>
                            </tr>
                        {/if}
                        {foreachelse}
                        <tr>
                            <td colspan="7"><p class="no-items">{__("text_no_orders")}</p></td>
                        </tr>
                    {/foreach}


                </table>
            </div>


        </div>

        <br/> <br/>
        <!--content_w_post_sales_support--></div>

    <div id="content_w_litigation">
        <div class="container-full">
            <p style="margin-bottom:20px;">{'w_litigation_header'|__}</p>
            <b>{'w_ended_orders_select_for_SAV'|__}</b>
            <div class="membre-tableau table-responsive">
                <table class="table">
                    <thead>
                    <tr>
                        <th style="width: 5%">{__("w_order_number")}</th>
                        <th style="width: 15%">{__("date")}</th>
                        <th style="width: 15%">{__("vendor")}</th>
                        <th style="width: 10%">{__("total")}</th>
                        <th style="width: 25%">{__("status")}</th>
                        <th style="width: 30%">&nbsp;</th>

                    </tr>
                    </thead>
                    {foreach from=$w_sav_orders item="o"}
                        {if $o.w_company_type == User::CLIENT_TYPE}
                            <tr>
                                <td>{$o.order_id}</td>
                                <td>{$o.timestamp|date_format:"`$settings.Appearance.date_format`"}</td>
                                <td>{$o.company_id|fn_get_company_name}</td>
                                <td>{include file="common/price.tpl" value=$o.total}</td>
                                <td>{include file="common/status.tpl" status=$o.status display="view"}</td>
                                <td>
                                    <a
                                            href="{"rma.create_SAV?return_type=litigation&order_id=`$o.order_id`"|fn_url}"
                                            data-remote="{"rma.create_SAV?return_type=litigation&order_id=`$o.order_id`"|fn_url} .mainbox-body"
                                            data-toggle="modal"
                                            data-target="#generic_modal"
                                    >{__("w_details_order")}</a>
                                </td>
                            </tr>
                        {/if}
                        {foreachelse}
                        <tr>
                            <td colspan="7"><p class="no-items">{__("text_no_orders")}</p></td>
                        </tr>
                    {/foreach}
                </table>
            </div>


            <br/> <br/>
        </div>
        <!--content_w_litigation--></div>
{/capture}

{include file="views/profiles/components/profile_menu.tpl" content=$smarty.capture.tabs_content active_tab=$smarty.request.selected_section|default:'returns'}
