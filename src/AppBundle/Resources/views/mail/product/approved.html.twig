{% extends '@App/mail/layout-company.html.twig' %}

{% block content %}
    {{ 'products_approval_status_approved'|trans|raw }}<br />
    <br />
    {% for product in products %}
        {% if loop.index >= 101 %}
            ...<br />
        {% else %}
            <a href="{{ cscart_url_vendor('products.update?product_id='~product.id, true) }}">{{ product.name }}</a><br />
        {% endif %}
    {% endfor %}

    {% if reason is not empty %}
        <p>{{ 'moderation_note'|trans }} :<br />{{ reason }}</p>
    {% endif %}
    <br />
{% endblock%}
