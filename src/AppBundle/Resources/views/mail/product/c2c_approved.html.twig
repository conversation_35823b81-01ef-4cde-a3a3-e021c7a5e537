{% extends '@App/mail/layout-company.html.twig' %}

{% block content %}
    {{ 'products_approval_status_approved'|trans|raw }}<br />
    <br />
    {% set productUrls = '<ul>' %}
    {% for product in products %}
        {% if loop.index >= 101 %}
            ...<br />
        {% else %}
            {% set productUrl = cscart_url('products.view?product_id='~product.id, 'C') %}
            {% set productUrls = productUrls ~ '<li><a href="' ~ productUrl ~ '">' ~ productUrl ~ '</a></li>' %}
            <a href="{{ cscart_url('products.view?product_id='~product.id, 'C') }}">{{ product.name }}</a><br />
        {% endif %}
    {% endfor %}
    {% set productUrls = productUrls ~ '</ul>' %}

    {% if reason is not empty %}
        <p>{{ 'moderation_note'|trans }} :<br />{{ reason }}</p>
    {% endif %}

    {{ 'w_c2c_products_approval_status_approved'|trans({'[product_url]': productUrls, '[company_url]': cscart_url('companies.view?company_id='~company_id, 'C')})|raw }}<br />
{% endblock%}
