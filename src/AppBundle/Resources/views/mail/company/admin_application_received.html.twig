{% extends '@App/mail/layout-company.html.twig' %}

{% block content %}

        {{ 'vendor_candidate_notification'|trans({
            '<a>': '<a href="' ~ cscart_url_admin('companies.update?company_id=' ~ companyData.company_id) ~ '">',
            '[lastname]': lastname,
            '[firstname]': firstname,
            '[company_name]': companyData.company
        })|raw }}

    <br/><br/>

    <table>
        <tr>
            <td class="form-field-caption" nowrap>{{ 'company_name'|trans }}:&nbsp;</td>
            <td>{{ companyData.company }}</td>
        </tr>
        {% if companyData.company_description %}
            <tr>
                <td class="form-field-caption" nowrap>{{ 'description'|trans }}:&nbsp;</td>
                <td>{{ companyData.company_description }}</td>
            </tr>
        {% endif %}
        {% if companyData.request_account_name %}
        <tr>
            <td class="form-field-caption" nowrap>{{ 'account_name'|trans }}:&nbsp;</td>
            <td>{{ companyData.request_account_name }}</td>
        </tr>
        {% endif %}
        <tr>
            <td class="form-field-caption" nowrap>{{ 'first_name'|trans }}:&nbsp;</td>
            <td>{{ adminFirstName }}</td>
        </tr>
        <tr>
            <td class="form-field-caption" nowrap>{{ 'last_name'|trans }}:&nbsp;</td>
            <td>{{ adminLastName }}</td>
        </tr>
        <tr>
            <td class="form-field-caption" nowrap>{{ 'email'|trans }}:&nbsp;</td>
            <td>{{ companyData.email }}</td>
        </tr>
        <tr>
            <td class="form-field-caption" nowrap>{{ 'phone'|trans }}:&nbsp;</td>
            <td>{{ companyData.phone }}</td>
        </tr>
        {% if companyData.url %}
            <tr>
                <td class="form-field-caption" nowrap>{{ 'url'|trans }}:&nbsp;</td>
                <td><a href="{{ companyData.url }}">{{ companyData.url }}</a></td>
            </tr>
        {% endif %}
        {% if companyData.fax %}
            <tr>
                <td class="form-field-caption" nowrap>{{ 'fax'|trans }}:&nbsp;</td>
                <td>{{ companyData.fax }}</td>
            </tr>
        {% endif %}
        <tr>
            <td class="form-field-caption" nowrap>{{ 'address'|trans }}:&nbsp;</td>
            <td>
                {{ companyData.address }}, {{ companyData.zipcode }} {{ companyData.city }}
                {{ companyData.state }} {{ companyData.country }}
            </td>
        </tr>
        <tr>
            <td class="form-field-caption" nowrap>{{ 'message'|trans }}:&nbsp;</td>
            <td>{{ messageToAdmin }}</td>
        </tr>
    </table>

{% endblock%}
