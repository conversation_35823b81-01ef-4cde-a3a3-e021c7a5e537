{% extends '@App/mail/layout-company.html.twig' %}

{% block content %}

        {{ 'w_c2c_vendor_candidate_notification'|trans({
            '<a>': '<a href="' ~ cscart_url_admin('companies.update?company_id=' ~ companyData.company_id) ~ '">',
            '[lastname]': lastname,
            '[firstname]': firstname,
            '[company_name]': companyData.company
        })|raw }}

    <br/><br/>

    <table>
        <tr>
            <td class="form-field-caption" nowrap>{{ 'company_name'|trans }}:&nbsp;</td>
            <td>{{ companyData.company }}</td>
        </tr>
        {% if companyData.company_description %}
            <tr>
                <td class="form-field-caption" nowrap>{{ 'description'|trans }}:&nbsp;</td>
                <td>{{ companyData.company_description }}</td>
            </tr>
        {% endif %}
        <tr>
            <td class="form-field-caption" nowrap>{{ 'first_name'|trans }}:&nbsp;</td>
            <td>{{ firstName }}</td>
        </tr>
        <tr>
            <td class="form-field-caption" nowrap>{{ 'last_name'|trans }}:&nbsp;</td>
            <td>{{ lastName }}</td>
        </tr>
        <tr>
            <td class="form-field-caption" nowrap>{{ 'email'|trans }}:&nbsp;</td>
            <td>{{ companyData.email }}</td>
        </tr>
        <tr>
            <td class="form-field-caption" nowrap>{{ 'phone'|trans }}:&nbsp;</td>
            <td>{{ companyData.phone }}</td>
        </tr>
        <tr>
            <td class="form-field-caption" nowrap>{{ 'address'|trans }}:&nbsp;</td>
            <td>
                {{ companyData.address }}, {{ companyData.zipcode }} {{ companyData.city }}
                {{ companyData.state }} {{ companyData.country }}
            </td>
        </tr>
    </table>

{% endblock%}
