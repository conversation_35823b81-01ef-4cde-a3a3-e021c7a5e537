{% extends '@App/mail/layout-company.html.twig' %}

{% block content %}
    {% set updateInformationsUrl = cscart_url('c2c_company.update_informations', 'C') %}
    {% set manageUrl = cscart_url('c2c_products.manage', 'C') %}

    {% if hasFiles %}
        {{ 'w_c2c_new_company_notification_content'|trans({
            '[update_informations_url]': updateInformationsUrl,
            '[new_product_url]': manageUrl,
            '[lastname]': lastname,
            '[firstname]': firstname,
            '[company_name]': companyData.company})|raw }}
    {% else %}
        {{ 'w_c2c_new_company_without_doc_notification_content'|trans({'[update_informations_url]': updateInformationsUrl, '[new_product_url]': manageUrl})|raw }}
    {% endif %}
{% endblock%}
