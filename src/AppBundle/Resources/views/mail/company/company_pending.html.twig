{% extends '@App/mail/layout-company.html.twig' %}

{% block content %}
    {{ 'create_profile_notification_header'|trans({
        '[company_name]': companyData.company,
        '[lastname]': lastname,
        '[firstname]': firstname})|raw }}<br />

    <table cellpadding="0" cellspacing="0" border="0">
        <tr>
            <td valign="top">
                <table cellpadding="1" cellspacing="1" border="0" width="100%">
                    <tr>
                        <td colspan="2"><hr size="1" noshade></td>
                    </tr>
                    <tr>
                        <td class="form-field-caption" nowrap>{{ 'email'|trans }}:&nbsp;</td>
                        <td>{{ userData.email }}</td>
                    </tr>
                    {% if userData.password1 is defined %}{# We show the password only the first time, because it's generated #}
                        <tr>
                            <td class="form-field-caption" nowrap>{{ 'password'|trans }}:&nbsp;</td>
                            <td>{{ userData.password1 }}</td>
                        </tr>
                    {% endif %}
                    <tr>
                        <td class="form-field-caption" nowrap>{{ 'login'|trans }} {{ 'url'|trans }}:&nbsp;</td>
                        <td>{% set profileUrl = cscart_url_vendor('profiles.update', true) %}<a href="{{ profileUrl }}">{{ profileUrl }}</a></td>
                    </tr>
                </table>
            </td>
            <td colspan="2">&nbsp;</td>
        </tr>
        <tr>
            <td colspan="3">&nbsp;</td>
        </tr>
    </table>

    {{ 'w_create_profile_notification_footer'|trans({
        '[help_page]': cscart_url_vendor('help.view', true),
        '[company_name]': companyData.company,
        '[lastname]': lastname,
        '[firstname]': firstname
    })|raw }}
{% endblock%}
