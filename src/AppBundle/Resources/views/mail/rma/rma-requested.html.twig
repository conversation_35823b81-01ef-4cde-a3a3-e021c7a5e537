{% extends '@App/mail/layout-customer.html.twig' %}

{% block content %}
    <p>{{ 'w_rma_request'|trans({
            '[firstname]': user.firstname,
            '[lastname]': user.lastname,
            '[company_name]': company_name,
            '[order_id]': order_info.order_id
        }|merge(order_extra))|raw }}</p>

    <p>{{ 'w_rma_return_address'|trans({
            '[firstname]': user.firstname,
            '[lastname]': user.lastname,
            '[company_name]': company_name,
            '[order_id]': order_info.order_id
        })|raw }}
        <div class="info-list">
            <div>
                <span>{{ rma_address.address }}</span>
            </div>
            <div>
            <span>{{ rma_address.zipcode }} {{ rma_address.city }}
            {% if rma_address.state is defined %}
                {{ rma_address.state|cscart_state_name(rma_address.country) }}
            {% endif %}</span>
            </div>
            <div>
                <span>{{ rma_address.country|cscart_country_name }}</span>
            </div>
        </div>
    </p>

    <p>
        {{ 'w_return_products'|trans({
            '[firstname]': user.firstname,
            '[lastname]': user.lastname,
            '[company_name]': company_name,
            '[order_id]': order_info.order_id
        })|raw }}<br /><br />

        <table cellpadding="0" cellspacing="1" border="0" width="100%" style="background-color: #dddddd;">
            <tr>
                <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{{ 'sku'|trans }}</th>
                <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{{ 'product'|trans }}</th>
                <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{{ 'price'|trans }}</th>
                <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{{ 'amount'|trans }}</th>
                <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{{ 'reason'|trans }}</th>
            </tr>
            {% for key, item in attribute(return_info.items, constant('RETURN_PRODUCT_ACCEPTED')) %}
            <tr>
                <td style="padding: 5px 10px; background-color: #ffffff;">{{ attribute(order_info.products, key).product_code|default('&nbsp;') }}</td>
                <td style="padding: 5px 10px; background-color: #ffffff;">{{ item.product }}
                {% if item.product_options %}<div style="padding-top: 1px; padding-bottom: 2px;">{{ render_smarty_legacy_mail('common/options_info.tpl', {product_options: item.product_options}) }}</div>{% endif %}</td>
                <td align="center" style="padding: 5px 10px; background-color: #ffffff;">{% if not item.price %}{{ 'free'|trans }}{% else %}{{ item.price|price }}{% endif %}</td>
                <td align="center" style="padding: 5px 10px; background-color: #ffffff;">{{ item.amount }}</td>
                <td align="center" style="padding: 5px 10px; background-color: #ffffff;">
                    &nbsp;{{ attribute(reasons, item.reason).property }}&nbsp;</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="6" align="center" style="padding: 5px 10px; background-color: #ffffff;"><p style="margin: 2px 0px 3px 0px;"><b>{{ 'text_no_products_found'|trans }}</b></p></td>
            </tr>
            {% endfor %}
        </table>

    </p>

    {{ 'w_rma_post_request'|trans({
        '[firstname]': user.firstname,
        '[lastname]': user.lastname,
        '[company_name]': company_name,
        '[order_id]': order_info.order_id
    }|merge(order_extra))|raw }}

{% endblock%}
