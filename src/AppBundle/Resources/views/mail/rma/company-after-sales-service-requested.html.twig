{% extends '@App/mail/layout-company.html.twig' %}

{% block content %}
    <style type="text/css" media="screen,print">
        body,p,div {
            color: #000000;
            font: 12px Arial;
        }
        body {
            background-color: #f4f6f8;
            padding-top: 24px;
        }
        a, a:link, a:visited, a:hover, a:active {
            color: #000000;
            text-decoration: underline;
        }
        a:hover {
            text-decoration: none;
        }
        table th {
            border:1px solid;
            background-color: #dddddd;
        }
        table td {
            border:1px solid;
        }
    </style>
    <style media="print">
        body, .main-table {
            background-color: #ffffff !important;
        }
    </style>
    {{ 'w_SAV_email_content'|trans({
        '[order_id]': order.id,
        '[client]': user.firstname ~ " " ~ user.lastname,
        '[lastname]': user.lastname,
        '[firstname]': user.firstname,
        '[email]': user.email
    }|merge(order_extra))|raw }}
    <table cellpadding="0" cellspacing="1" width="100%" style="border:1px solid; border-collapse: collapse">
        <thead>
        <tr>
            <th style="border:1px solid; background-color: #dddddd">{{ 'product'|trans }}</th>
            <th style="border:1px solid; background-color: #dddddd">{{ 'options'|trans }}</th>
            <th style="border:1px solid; background-color: #dddddd">{{ 'price'|trans }}</th>
            <th style="border:1px solid; background-color: #dddddd">{{ 'quantity'|trans }}</th>
        </tr>
        </thead>
        {% for item in items %}
            <tr {% if loop.index0 is odd %}class="table-row"{% endif %}>
                <td style="width: 30%; border:1px solid" class="middle">
                    {{ item.productName }}
                </td>

                <td style="width: 20%; border:1px solid">
                    {% if item.declinationOptions %}
                    <ul>
                        {% for option in item.declinationOptions %}
                        <li>{{ option.variantName }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                </td>

                <td style="width: 20%; border:1px solid">
                    <b>{{ item.price.multiply(item.amount)|money }}</b><br /> {{ item.amount }} x {{ item.price|money }}
                </td>
                <td style="width: 10%; border:1px solid">
                    {{ item.amount }}
                </td>
            </tr>
        {% endfor %}
    </table>
    <b>{{ 'comment'|trans }} :</b><br/>
    {{ comment }}
{% endblock%}
