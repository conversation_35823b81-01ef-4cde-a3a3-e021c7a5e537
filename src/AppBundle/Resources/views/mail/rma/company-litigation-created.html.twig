{% extends '@App/mail/layout-company.html.twig' %}

{% block content %}
    <style type="text/css" media="screen,print">
        body,p,div {
            color: #000000;
            font: 12px Arial;
        }
        body {
            background-color: #f4f6f8;
            padding-top: 24px;
        }
        a, a:link, a:visited, a:hover, a:active {
            color: #000000;
            text-decoration: underline;
        }
        a:hover {
            text-decoration: none;
        }
        table th {
            border:1px solid;
            background-color: #dddddd;
        }
        table td {
            border:1px solid;
        }
    </style>
    <style media="print">
        body, .main-table {
            background-color: #ffffff !important;
        }
    </style>
    {{ 'w_SAV_email_content'|trans({
        '[order_id]': order.id,
        '[client]': user.firstname ~ user.lastname,
        '[email]': user.email,
        '[lastname]': lastname,
        '[firstname]': firstname
    }|merge(order_extra))|raw }}
    <table cellpadding="0" cellspacing="1" width="100%" style="border-collapse: collapse;">
        <thead>
        <tr>
            <th>{{ 'product'|trans }}</th>
            <th>{{ 'price'|trans }}</th>
            <th>{{ 'quantity'|trans }}</th>
            <th>{{ 'reason'|trans }}</th>
        </tr>
        </thead>
        {% for item in items %}
            <tr {% if loop.index0 is odd %}class="table-row"{% endif %}>
                <td style="width: 40%" class="middle">
                    {{ item.productName }}
                </td>

                <td style="width: 20%">
                    <b>{{ item.price.multiply(item.amount)|money }}</b><br /> {{ item.amount }} x {{ item.price|money }}
                </td>
                <td style="width: 10%">
                    {{ item.amount }}
                </td>
                <td style="width: 30%">
                    {{ comment }}
                </td>
            </tr>
        {% endfor %}
    </table>
    <b>{{ 'comment'|trans }} :</b><br/>
    {{ comment }}
    <p>
        {{ 'w_litigation_email_content_to_company'|trans|raw }}
    </p>
{% endblock%}
