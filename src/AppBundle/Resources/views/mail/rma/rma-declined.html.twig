{% extends '@App/mail/layout-customer.html.twig' %}

{% block content %}
    {{ return_status.email_header|raw }}<br />

    <p>
    {{ 'w_rma_refound_products'|trans(order_extra)|raw }}<br /><br />

    <table cellpadding="0" cellspacing="1" border="0" width="100%" style="background-color: #dddddd;">
    <tr>
        <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{{ 'sku'|trans }}</th>
        <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{{ 'product'|trans }}</th>
        <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{{ 'price'|trans }}</th>
        <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{{ 'amount'|trans }}</th>
        <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{{ 'reason'|trans }}</th>
    </tr>
    {% for key, item in attribute(return_info.items, constant('RETURN_PRODUCT_ACCEPTED')) %}
        <tr>
            <td style="padding: 5px 10px; background-color: #ffffff;">{{ attribute(order_info.products, key).product_code|default('&nbsp;') }}</td>
            <td style="padding: 5px 10px; background-color: #ffffff;">{{ item.product }}
                {% if item.product_options %}<div style="padding-top: 1px; padding-bottom: 2px;">{{ render_smarty_legacy_mail('common/options_info.tpl', {product_options: item.product_options}) }}</div>{% endif %}</td>
            <td align="center" style="padding: 5px 10px; background-color: #ffffff;">{% if not item.price %}{{ 'free'|trans }}{% else %}{{ item.price|price }}{% endif %}</td>
            <td align="center" style="padding: 5px 10px; background-color: #ffffff;">{{ item.amount }}</td>
            <td align="center" style="padding: 5px 10px; background-color: #ffffff;">
                &nbsp;{{ attribute(reasons, item.reason).property }}&nbsp;</td>
        </tr>
    {% else %}
        <tr>
            <td colspan="6" align="center" style="padding: 5px 10px; background-color: #ffffff;"><p style="margin: 2px 0px 3px 0px;"><b>{{ 'text_no_products_found'|trans }}</b></p></td>
        </tr>
    {% endfor %}
    </table>

    </p>
{% endblock%}
