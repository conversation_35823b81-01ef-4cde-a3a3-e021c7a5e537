{% extends '@App/mail/layout-company.html.twig' %}

{% block content %}
    {{ ('order_vendor_email_header_'~order_info.status)|trans({
        '[order_id]': orderId,
        '[firstname]': user.firstname,
        '[lastname]': user.lastname,
        '[email]': user.email,
        '[company_name]': company_name
    }|merge(order_extra))|raw }}<br /><br />

    {{ render_smarty_legacy_mail('C2C/orders/invoice.tpl', {
        order_info: order_info,
        shipments: shipments,
        use_shipments: use_shipments,
        payment_method: payment_method,
        status_settings: status_settings,
        profile_fields: profile_fields,
        take_surcharge_from_vendor: take_surcharge_from_vendor,
        display_order_summary: display_order_summary
    }, 'A') }}
{% endblock%}
