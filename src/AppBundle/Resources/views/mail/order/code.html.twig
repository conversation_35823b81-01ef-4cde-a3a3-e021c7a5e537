{% extends '@App/mail/layout-customer.html.twig' %}

{% block content %}
    <p>
        {{ 'w_contact_info_seller'|trans({
            '[firstname]': sellerInfo.firstname,
            '[lastname]': sellerInfo.lastname,
            '[email]': sellerInfo.email,
            '[phone]': company.phone,
            '[order_id]': orderId,
            '[seller_name]': company.company
        }|merge(order_extra))|raw }}
    </p>
    <p>{{ 'w_customer_code_explanation'|trans({
            '[lastname]': user.lastname,
            '[firstname]': user.firstname,
            '[seller_name]': company.company,
            '[phone]': company.phone,
            '[email]': sellerInfo.email
        })
        }}</p>
    <table style="
        width:50%;
        border: 1px solid #999;
        border-right-width: 0;
        border-bottom-width: 0;" cellpadding="0" cellspacing="0">
        {% set i=0 %}
        {% for code in codes %}
            {% if i%3 == 0 %}
                <tr style="padding: 0; margin: 0;">
            {% endif %}
            <td style="
                border: 0 solid #999;
                border-right-width: 1px;
                border-bottom-width: 1px;
                text-align:center;
                vertical-align: middle;
                padding:5px;
                margin:0;">
                {{ code }}
            </td>
            {% if i%3 == 2 %}
                </tr>
            {% endif %}
            {% set i=i+1 %}
        {% endfor %}
    </table>
    <br />

{% endblock%}
