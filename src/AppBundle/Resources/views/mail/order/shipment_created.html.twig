{% extends '@App/mail/layout-customer.html.twig' %}

{% block content %}
    <p>
        {{ 'product_shipped'|trans({
            '%order_id%': shipment.order.id,
            '[order_id]': shipment.order.id,
        }|merge(order_extra))|raw }}
    </p>

    {% if shipment.trackingNumber %}
        <strong>{{ 'tracking_number'|trans }}</strong>:&nbsp;{{ shipment.trackingNumber }}<br /><br />
    {% endif %}

    <p>
        <strong>{{ 'products'|trans }}:</strong> <br/>
        {% for itemElement in shipment.items %}
            {% if itemElement.amount > 0 %}
                {{ itemElement.amount }}&nbsp;x&nbsp;{{ itemElement.item.productName }}<br />
            {% endif %}
        {% endfor %}
    </p>

    {% if shipment.comments %}
        <strong>{{ 'comments'|trans }}</strong>:
        {{ shipment.comments }}
        <br/>
    {% endif %}

    <strong>{{ "shipping"|trans }}</strong>: {{ shipment.order.shippingName }}<br />

{% endblock%}
