{% extends '@App/mail/layout-company.html.twig' %}

{% block content %}
    {% if order_info['is_paid'] is same as("1") %}
        {{ 'subscription_notification_mail_vendor_success'|trans({
            '[subscriptionId]': subscriptionId,
            '[subscriptionName]': subscriptionName,
        })|raw }}
    {% else %}
        {{ 'subscription_notification_mail_vendor_fail'|trans({
            '[subscriptionId]': subscriptionId,
            '[subscriptionName]': subscriptionName,
        })|raw }}
    {% endif %}

    <br /><br />

    {{ render_smarty_legacy_mail('orders/invoice.tpl', {
        order_info: order_info,
        shipments: shipments,
        use_shipments: use_shipments,
        payment_method: payment_method,
        status_settings: status_settings,
        profile_fields: profile_fields,
        take_surcharge_from_vendor: take_surcharge_from_vendor
    }, 'A') }}
{% endblock%}
