{% extends '@App/mail/layout-admin.html.twig' %}

{% block content %}
    <table>
        {% for element in elements %}
            {% if element.element_type is constant('FORM_SEPARATOR') %}
                <tr>
                    <td colspan="2"><hr width="100%" /></td>
                </tr>
            {% elseif element.element_type is constant('FORM_HEADER') %}
                <tr>
                    <td colspan="2"><b>{{ element.description }}</b></td>
                </tr>
            {% elseif element.element_type is not constant('FORM_FILE') %}
                <tr>
                    <td>{{ element.description }}:&nbsp;</td>
                    <td>
                        {% set value = element.value %}

                        {% if element.element_type is constant('FORM_SELECT') or element.element_type is constant('FORM_RADIO') %}
                            {{ attribute(element.variants, value).description }}
                        {% elseif element.element_type is constant('FORM_CHECKBOX') %}
                            {{ (value == 'Y' ? 'yes' : 'no')|trans }}
                        {% elseif element.element_type is constant('FORM_MULTIPLE_SB') or element.element_type is constant('FORM_MULTIPLE_CB') %}
                            {% for val in value %}
                                {{ attribute(element.variants, val).description }}
                                {% if not loop.last %}
                                    ,&nbsp;
                                {% endif %}
                            {% endfor %}
                        {% elseif element.element_type is constant('FORM_TEXTAREA') %}
                            {{ value|striptags|nl2br|raw }}
                        {% elseif element.element_type is constant('FORM_DATE') %}
                            {{ value|date('d/m/Y') }}
                        {% elseif element.element_type is constant('FORM_COUNTRIES') %}
                            {{ value|cscart_country_name }}
                            {% set countryCode = value %}
                        {% elseif element.element_type is constant('FORM_STATES') %}
                            {{ value|cscart_state_name(countryCode ?? null) }}
                        {% else %}
                            {{ value }}
                        {% endif %}
                    </td>
                </tr>
            {% endif %}
        {% endfor %}
    </table>
{% endblock%}
