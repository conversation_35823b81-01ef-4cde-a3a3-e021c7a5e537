{% extends '@App/mail/layout-customer.html.twig' %}

{% block content %}
    {{ 'update_profile_notification_header'|trans({
        '[lastname]': user.lastname,
        '[firstname]': user.firstname,
        '[email]': user.email
    })|raw }}<br /><br />

    <table cellpadding="0" cellspacing="0" border="0">
        <tr>
            <td valign="top">
                <table cellpadding="1" cellspacing="1" border="0" width="100%">
                    <tr>
                        <td colspan="2" class="form-title">{{ 'user_account_info'|trans({
                                '[lastname]': user.lastname,
                                '[firstname]': user.firstname
                            }) }}<hr size="1" noshade></td>
                    </tr>
                    <tr>
                        <td class="form-field-caption" nowrap>{{ 'email'|trans }}:&nbsp;</td>
                        <td>{{ user.email }}</td>
                    </tr>
                    <tr>
                        <td class="form-field-caption" nowrap>{{ 'login'|trans }}:&nbsp;</td>
                        {% if(profileUrl is defined) %}
                            <td>{{ profileUrl }}</td>
                        {% else %}
                            {%  if user.userType == 'A' %}
                                <td>{{ cscart_url_admin('profiles.update', true) }}</td>
                            {% elseif user.userType == 'V' %}
                                <td>{{ cscart_url_vendor('profiles.update', true) }}</td>
                            {% else %}
                                <td>{{ cscart_url('profiles.update', 'C', 'http') }}</td>
                            {% endif %}
                        {% endif %}
                    </tr>
                </table>
            </td>
            <td colspan="2">&nbsp;</td>
        </tr>
    </table>

{% endblock%}
