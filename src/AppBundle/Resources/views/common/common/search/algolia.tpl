<script src="//cdn.jsdelivr.net/algoliasearch/2.7.3/algoliasearch.min.js"></script>
<script type="text/javascript">
    (function(_) {
        _.searchClient = new AlgoliaSearch("{$AlgoliaLogin}", "{$AlgoliaPassword}");
        _.searchClient.initHelper = function(index, config, callback) {
            _.searchClient.initIndex(index).search(
                    '',
                    function (success, content) {
                        if (!success) {
                            console.log('Failed to init helper');
                            return;
                        }
                        var helper = new AlgoliaSearchHelper(
                                _.searchClient,
                                index,
                                {
                                    disjunctiveFacets: Object.keys(content.facets)
                                }
                        );
                        helper.justToggleRefine = function(facet, value) {
                            //Identical to helper.toggleRefine, without call to search function
                            for (var i = 0; i < helper.options.facets.length; ++i) {
                                if (helper.options.facets[i] == facet) {
                                    var refinement = facet + ':' + value;
                                    helper.refinements[refinement] = !helper.refinements[refinement];
                                    helper.page = 0;
                                    return true;
                                }
                            }
                            helper.disjunctiveRefinements[facet] = helper.disjunctiveRefinements[facet] || {};
                            for (var j = 0; j < helper.options.disjunctiveFacets.length; ++j) {
                                if (helper.options.disjunctiveFacets[j] == facet) {
                                    helper.disjunctiveRefinements[facet][value] = !helper.disjunctiveRefinements[facet][value];
                                    helper.page = 0;
                                    return true;
                                }
                            }
                            return false;
                        };
                        callback(helper);
                    },
                    {
                        attributesToRetrieve : [],
                        attributesToHighlight: [],
                        attributesToSnippet  : [],
                        hitsPerPage          : 1,
                        maxValuesPerFacet    : 1,
                        tagFilters           : config.tag_filters || '',
                        facetFilters         : config.filter ? [[config.filter.facet + ':' + config.filter.value]] : [],
                        facets               : '*'
                    }
            );
        };

        var original_initIndex = _.searchClient.initIndex.bind(_.searchClient);
        _.searchClient.initIndex = function (indexName) {
            var index = original_initIndex(indexName);
            var original_ttAdapter = index.ttAdapter.bind(index);
            index.ttAdapter = function (params, $lat, $lng) {
                var original_search_func = original_ttAdapter(params);
                if (!$lat || !$lng) {
                    return original_search_func;
                }

                var params_nogeo = _.$.extend({}, params);
                var params_geo   = _.$.extend({}, params);
                params_nogeo.tagFilters = '-{RecordProduct::TAG_GEOLOC}';
                params_geo.aroundRadius = 2500;

                return function (query, cb) {
                    var geoloc = [$lat.val(), $lng.val()];
                    if (!geoloc[0] || !geoloc[1]) {
                        return original_search_func(query, cb);
                    }
                    params_geo.aroundLatLng = geoloc.toString();
                    var hits = [];
                    index.search(query, on_search_finished, params_nogeo);
                    index.search(query, on_search_finished, params_geo);

                    function on_search_finished(success, content) {
                        if (!success) {
                            return;
                        }
                        //heuristic to identify gelocalised request
                        hits[content.params.indexOf('LatLng') != -1 ? 'geo' : 'nogeo'] = content.hits;
                        if (Object.keys(hits).length < 2) {
                            return;
                        }
                        //Mixing results
                        var result = [];
                        var half_size = content.hitsPerPage / 2;
                        if (hits['geo'].length < half_size) {
                            result = hits['geo'].concat(hits['nogeo'].slice(0, content.hitsPerPage - hits['geo'].length));
                        } else if (hits['nogeo'].length < half_size) {
                            result = hits['geo'].slice(0, content.hitsPerPage - hits['nogeo'].length).concat(hits['nogeo']);
                        } else {
                            result = hits['geo'].slice(0, half_size).concat(hits['nogeo'].slice(0, half_size));
                        }
                        cb(result);
                    }
                };
            };
            return index;
        }
    }(Tygh));
</script>
