{if !$smarty.request.extra}
{assign var="rnd_value" value=uniqid()}
{assign var="random" value=$random|default:$rnd_value}
<script type="text/javascript">
//<![CDATA[
{if $w_select_manager}
if( $("#accordion").length > 0) {
    $("#accordion").accordion({
        collapsible: true,
        heightStyle: "content",
        active: false
    });
}
(function(_, $) {
    $.ceEvent('on', 'ce.formpost_categories_form', function(frm, elm) {
        return false;
    });
}(Tygh, Tygh.$));
{else}
(function(_, $) {
    _.tr('text_items_added', '{__("text_items_added")|escape:"javascript"}');
    var display_type = '{$smarty.request.display|escape:javascript nofilter}';
    $.ceEvent('on', 'ce.formpost_categories_form', function(frm, elm) {
        var categories = {};
        {literal}
        var checked_items = $('input.cm-item:checked', frm);
        if (checked_items.length > 0) {
            let hasDeclinations = $('input[name="hasDeclinations"]').val();
            if (typeof hasDeclinations !== undefined && Boolean(hasDeclinations) === true) {
                $.ceNotification('show', {
                    type: 'W',
                    title: _.tr('warning'),
                    message: _.tr('warning_updating_category'),
                    message_state: 'I'
                });
            }

            checked_items.each( function() {
                var id = $(this).val();
                categories[id] = $('#category_' + id).text();
            });

            $.cePicker('add_js_item', frm.data('caResultId'), categories, 'c', {
                '{category_id}': '%id',
                '{category}': '%item'
            });
            {/literal}

            if (display_type != 'radio') {
                $.ceNotification('show', {
                    type: 'N',
                    title: _.tr('notice'),
                    message: _.tr('text_items_added'),
                    message_state: 'I'
                });
            } else {
                $('#'+'{$smarty.request.data_id}').find('input.cm-picker-value')
                        .data('isTransactional', checked_items.data('isTransactional') )
                        .data('transactionMode', checked_items.data('transactionMode') )
                        .data('isTransactionModeOverridable', checked_items.data('isTransactionModeOverridable') )
                        .change()
                ;
            }
        }

        return false;
    });
}(Tygh, Tygh.$));
    {/if}
function search(target, val, page, subcat, random) {

    var option = {
        method: 'GET',
        result_ids: 'SearchResult_' + random,
        data: {
            display: '{$smarty.request.display}',
            page: page,
            search: val,
            search_in: subcat,
            search_id: random
        }
    };

    if (val.length > 2) {
        Tygh.$.ceAjax('request', target, option);
        display_search('search');
    } else {
        display_search('leave_search');
        var error_msg = '{__("w_error_search_length")}';
        $.ceNotification('show', {
            type: 'E',
            title: '{__("w_error_search")}',
            message: error_msg
        });
    }
}

function display_search(val) {
    if (val == 'search') {
        $('.onSearch').removeClass('hidden');
        $('.outSearch').addClass('hidden');
    }
    else if (val == 'leave_search') {
        $('.outSearch').removeClass('hidden');
        $('.onSearch').addClass('hidden');
    }
}

//]]></script>
{/if}
{if $w_select_manager}
    {assign var="default_category" value="`$ldelim`category`$rdelim`"}
    {assign var="default_category_id" value="`$ldelim`category_id`$rdelim`"}
    <div id="accordion">
        <h3><p style="position:relative; left:10px;"> {__('w_actual_selection')} </p></h3>
        <div>
            <div id="categories_reminder" class="manager_listener">
                <p class="cm-js-item cm-clone hidden ">
                    {$default_category}
                    <a class="icon-remove-sign cm-tooltip hand "
                            onclick="Tygh.selectManager.deleteItem('{$default_category_id}');"
                            title="{__("remove")}"></a>
                </p>
            </div>
        </div>
    </div>
{/if}
<div class="search page-header">
    {if $categories_tree}
        <div class=" inline-block" >
            <select id="subcat_select_{$random}">
                <option value="0">{__("w_all_categories")}</option>
                {foreach from=$categories_tree item=cur_cat}
                    <option value="{$cur_cat.category_id}">{$cur_cat.category}</option>
                {/foreach}
            </select>
        </div>
    {/if}
    <div class="inline-block">
        <input id="search_field_{$random}" class="input-text" type="text" onkeypress="if (event.keyCode == 13) search('{'categories.manage'|fn_url:'A':'rel' nofilter}',$('#search_field_{$random}').val(), 1, $('#subcat_select_{$random}').val(), '{$random}' );" name="search" />
    </div>
    <div class="inline-block">
        <input id="input_search_{$random}" type="submit" onclick="search('{'categories.manage'|fn_url:'A':'rel' nofilter}',$('#search_field_{$random}').val(), 1,$('#subcat_select_{$random}').val(), '{$random}')" />
    </div>
    <div class="hidden onSearch pull-right">
        <input class="btn"  value="{__("w_leave_search")}" name="leave_search" type="button" onclick="display_search(this.name)">
    </div>
</div>
    {if $w_select_manager}
<div id="select_listner">
    {/if}
    <form action="{$smarty.request.extra|fn_url}" data-ca-result-id="{$smarty.request.data_id}" method="post" name="categories_form">
        <div class="onSearch hidden" id="SearchResult_{$random}">
            <!--SearchResult_{$random}--></div>

        <div class="outSearch items-container multi-level" id="w_picker_tree">
            {if $categories_tree}
                {include file="views/categories/components/categories_tree_simple.tpl" header=true checkbox_name=$smarty.request.checkbox_name|default:"categories_ids" parent_id=$category_id display=$smarty.request.display}
            {else}
                <p class="no-items center">
                    {__("no_categories_available")}
                </p>
            {/if}
            <!--w_picker_tree--></div>

        <div class="buttons-container">
            {if $smarty.request.display == "radio"}
                {assign var="but_close_text" value=__("choose")}
            {else}
                {assign var="but_close_text" value=__("add_categories_and_close")}
            {/if}
            {include file="buttons/add_close.tpl" is_js=$smarty.request.extra|fn_is_empty}
        </div>
    </form>
    {if $w_select_manager}
    <!--select_listner--></div>
    {/if}
