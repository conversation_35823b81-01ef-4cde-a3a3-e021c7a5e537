{capture name="mainbox"}
<div id="content_w_c2c_accounting">
    {include file="common/pagination.tpl" hide_items_per_page_selector=true save_current_page=true save_current_url=true div_id=$smarty.request.content_id}

    {if $company_id}
        {if 'A' == $smarty.const.AREA}
            <p>{__('w_commission_intro')}</p>
        {else}
            <p>{__('w_commission_intro_C')}</p>
        {/if}

    {/if}
{if $data}
    {if !$company_id}
        <h4 style="text-align: center;color: #2f3b41; padding: 16px 0 8px;">
            {__('period')} {__('w_from')} {$data_label[0].start|date_format:$settings.Appearance.date_format} {__('w_to')} {($data_label[0].end-1)|date_format:$settings.Appearance.date_format}
        </h4>
        <p class="page-intro" style="margin: 20px auto 50px; width: 100%; max-width: 580px; color: #6a7479; letter-spacing: 0.02em;">
            {__('accounting_admin_intro')}
        </p>
    {/if}
<table width="100%" class="table table-middle">
    <thead>
    <tr>
        <th width="10%">
            {if !$company_id}
                {__('vendor')}
            {else}
                {__('period')}
            {/if}
        </th>
        <th width="10%">{__('payment_info')}</th>
        <th width="10%">{__('orders')}</th>
        <th width="7%">{if $access === 'includedTaxes'}{__('total_amount_ttc')}{else}{__('total_amount_ht')}{/if}</th>
        <th width="7%">
            {if $access === 'includedTaxes'}{__('accounting_commissions_ttc')}{else}{__('accounting_commissions_ht')}{/if}
            {feature_flag feature="commission.include_shipping_in_commission"}
                <small>{__('accounting_include_shipping_cost')}</small>
            {/feature_flag}
        </th>
        <th width="7%">{if $access === 'includedTaxes'}{__('w_donate_ttc')}{else}{__('w_donate_ht')}{/if}</th>
        <th width="7%">{__('tax_commission')}</th>
        <th width="7%">{if $access === 'includedTaxes'}{__('shipping_cost_ttc')}{else}{__('shipping_cost_ht')}{/if}</th>
    </tr>
    </thead>
    {foreach from=$data item="first_data" key="first_id"}
        <tr>
            <td rowspan="{$first_data|count+2}">
                {$data_label.$first_id['title']}
            </td>
        </tr>
        {foreach from=$first_data item="payment_data" key="payment_id"}
            <tr>
                <td>{$payment_data.payment_name}</td>
                <td>
                    {if $orders.$first_id.$payment_id|count > 10}
                        {if !$company_id}
                            <a href="{"orders.manage?is_search=Y&status=`$statusCompleted`&last_status_change_is_after=`$data_label[0].start`&last_status_change_is_before=`$data_label[0].end-1`&company_id=`$first_id`&payments%5B%5D=`$payment_id`"|fn_url}">{__('orders_details')}</a>
                        {else}
                            <a href="{"orders.manage?is_search=Y&status=`$statusCompleted`&last_status_change_is_after=`$data_label.$first_id['period'].start`&last_status_change_is_before=`$data_label.$first_id['period'].end-1`&payments%5B%5D=`$payment_id`"|fn_url}">{__('orders_details')}</a>
                        {/if}
                    {else}
                        {foreach from=$orders.$first_id.$payment_id item="orderId" name="orderLoop"}
                            <a href="{"orders.details?order_id=`$orderId`"|fn_url}">{$orders.$first_id.$payment_id.$orderId}</a>{if $smarty.foreach.orderLoop.last === false};{/if}
                        {/foreach}
                    {/if}
                </td>
                {if $access === 'includedTaxes'}
                    <td>{include file="common/price.tpl" value=($payment_data.donated)}</td>
                    <td>{include file="common/price.tpl" value=$payment_data.w_commission}</td>
                    <td>{include file="common/price.tpl" value=$payment_data.donated-$payment_data.w_commission}</td>
                    <td>{include file="common/price.tpl" value=$payment_data.vat}</td>
                    <td>{include file="common/price.tpl" value=$payment_data.shipping_cost}</td>
                {else}
                    {if $payment_data.donated-$totalsTaxes.$first_id.$payment_id.totalsTaxe >= 0}
                        <td>{include file="common/price.tpl" value=($payment_data.donated-$totalsTaxes.$first_id.$payment_id.totalsTaxe)}</td>
                    {else}
                        <td>{include file="common/price.tpl" value=(0)}</td>
                    {/if}
                    {if $payment_data.w_commission-$payment_data.vat >= 0}
                        <td>{include file="common/price.tpl" value=($payment_data.w_commission-$payment_data.vat)}</td>
                    {else}
                        <td>{include file="common/price.tpl" value=(0)}</td>
                    {/if}
                    <td>{include file="common/price.tpl" value=(($payment_data.donated-$payment_data.w_commission)/(1+$tax))}</td>
                    <td>{include file="common/price.tpl" value=$payment_data.vat}</td>
                    <td>{include file="common/price.tpl" value=($shippingTaxes.$first_id.$payment_id.shippingTaxe)}</td>
                {/if}
            </tr>
        {/foreach}
        <tr class="total-row">
            {if $access === 'includedTaxes'}
                <td>&nbsp;</td>
                <td class="total-cell">{__('total_ttc')}:</td>
                <td></td>
                <td>{include file="common/price.tpl" value=$aggregate.$first_id.w_commission}</td>
                <td>{include file="common/price.tpl" value=$aggregate.$first_id.donated_total-$aggregate.$first_id.w_commission}</td>
                <td>{include file="common/price.tpl" value=$aggregate.$first_id.vat}</td>
                <td>{include file="common/price.tpl" value=$aggregate.$first_id.shipping_cost}</td>
            {else}
                <td>&nbsp;</td>
                <td class="total-cell">{__('total_ht')}:</td>
                <td></td>
                <td>{include file="common/price.tpl" value=($aggregate.$first_id.w_commission-$aggregate.$first_id.vat)}</td>
                <td>{include file="common/price.tpl" value=(($aggregate.$first_id.donated_total-$aggregate.$first_id.w_commission)/(1+$tax))}</td>
                <td>{include file="common/price.tpl" value=$aggregate.$first_id.vat}</td>
                <td>{include file="common/price.tpl" value=($fullShipping.$first_id.fullShippingTaxes)}</td>
            {/if}
        </tr>
    {/foreach}
</table>
{else}
    <p class="no-value">
        {__("no_data")}
        {__('w_from')} {$data_label[0].start|date_format:$settings.Appearance.date_format}
        {__('w_to')} {($data_label[0].end-1)|date_format:$settings.Appearance.date_format}
    </p>
{/if}
<div class="clearfix">
    {include file="common/pagination.tpl" hide_items_per_page_selector=true div_id=$smarty.request.content_id}
</div>
<!--content_w_c2c_accounting--></div>
{/capture}

{if $smarty.const.AREA =='A'}
    {if $access === 'includedTaxes'}
        {$title=__("vendor_account_balance_ttc")}
    {else}
        {$title=__("vendor_account_balance_ht")}
    {/if}
    {include file="common/mainbox.tpl" title=$title content=$smarty.capture.mainbox title_extra=$smarty.capture.title_extra adv_buttons=$smarty.capture.adv_buttons select_languages=true content_id="accounting"}
{else}
    {include file="common/c2c/components/menu.tpl" content=$smarty.capture.mainbox active_tab=$smarty.request.selected_section|default:'accounting_info'}
{/if}
