{#
    This is the custom template for Warmango. They must update their hosted version (`rma_template_url`)
    with this template during refund EPIC release.
#}
<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Facture d'avoir</title>
    <style>
        .clearfix:after {
            clear: both;
        }

        .clearfix:before,
        .clearfix:after {
            content: " ";
            display: table;
        }

        .mb-5 {
            margin-bottom: 3rem;
        }

        .inline-block {
            display: inline-block;
        }

        .text-right {
            text-align: right;
        }

        .text-left {
            text-align: left;
        }

        .pl-4 {
            padding-left: 1.5rem;
        }

        body {
            background: white;
            padding: 1rem;
        }

        .rma-info {
            background: #343434;
            color: white;
            font-size: .9rem;
            padding: 1rem;
        }

        .rma-number {
            font-size: 1.05rem;
            font-weight: bold;
            margin-bottom: .6rem;
        }

        .address-item {
            width: 35%;
        }

        .address-item:first-of-type {
            padding-right: 2.5rem;
            margin-right: 2.5rem;
            border-right: 1px solid #343434;
        }

        .address-item .address-title {
            color: #8ecb4e;
            font-size: 1.15rem;
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .address-item p {
            margin: 0 0 .3rem;
            color: #343434;
        }

        thead {
            display: table-header-group
        }

        tr {
            page-break-inside: avoid
        }

        table {
            border-collapse: collapse;
            font-size: 14px;
            border: 1px solid #e8e8e8;
        }

        table.totals-table {
            page-break-inside: avoid;
        }

        table.totals-table tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        th {
            background: #8ecb4e;
            color: white;
            padding: .75rem;
            font-weight: normal;
        }

        th:not(:last-of-type) {
            border-right: 1px solid #668d2f;
        }

        table.stripped-odd tr:nth-child(odd) {
            background: #e8e8e8;
        }

        table.stripped-even tr:nth-child(even) {
            background: #e8e8e8;
        }

        td {
            padding: .75rem;
            text-align: center;
        }

        td:not(:last-of-type) {
            border-right: 1px solid #9b9a9a;
        }


        .order-lines {
            font-size: .8rem;
        }

        .totals-table {
            float: right;
            width: 50%;
        }

        .totals-table th {
            font-weight: bold;
        }

        .last-page-wrapper p {
            line-height: 1.6rem;
            margin-bottom: 2rem;
        }

        .payment-method th {
            background: #8ecb4e;
            color: white;
            font-size: 1.1rem;
            font-weight: bold;
        }

        .text-right {
            text-align: right;
        }

        .text-left {
            text-align: left;
        }

        .tva-column {
            min-width: 8rem;
        }
    </style>
</head>
<body>

{# rma #}
<div class="rma-info mb-5">
    <div class="rma-number">
        {{ 'credit_note.title'|trans }} {{ 'credit_note.number'|trans }}
        {{ creditNote.creditNoteId }} {{ 'credit_note.from'|trans }}
        {{ creditNote.getRefundDate.format('credit_note.date_format'|trans) }}
    </div>
    <div>{{ 'credit_note.3rd_party_invoice'|trans }}</div>
</div>
{# Company info #}
<div class="mb-5 clearfix">
    <div>{{ creditNote.company.name|default }}</div>
    {% if creditNote.company.legalStatus is not empty and creditNote.company.capital is not empty %}
        <div>{{ creditNote.company.legalStatus|default }} au capital de {{ creditNote.company.capital|default }} €</div>
    {% endif %}
    <div>SIRET {{ creditNote.company.siretNumber|default }}
        {% if creditNote.company.rcs %}
            - RCS {{ creditNote.company.rcs|default }}
        {% endif %}
    </div>
    <div>TVA: {{ creditNote.company.vatNumber|default }}</div>
    <div>{{ creditNote.company.address|default }}</div>
    <div>{{ creditNote.company.zipcode|default }} {{ creditNote.company.city|default }}</div>
    <div>{{ creditNote.company.country|default }}</div>
</div>
{# delivery info#}
<div class="mb-5">
    {# shipping address #}
    <div class="address-item inline-block">
        <p class="address-title">{{ 'credit_note.shipping_address'|trans }}</p>
        {% if creditNote.shippingAddress.company is not empty %}
            <p><b>{{ creditNote.shippingAddress.company|default }}</b></p>
        {% endif %}
        <p>{{ creditNote.shippingAddress.firstname|default }} {{ creditNote.shippingAddress.lastname|default }}</p>
        <p>{{ creditNote.shippingAddress.address|default }}</p>
        {% if creditNote.shippingAddress.address2|default %}
            <p>{{ creditNote.shippingAddress.address2|default }}</p>
        {% endif %}
        <p>{{ creditNote.shippingAddress.zipcode|default }} {{ creditNote.shippingAddress.city|default }}</p>
        <p>{{ creditNote.shippingAddress.country|default }}</p>
    </div>
    {# billing address #}
    <div class="address-item inline-block">
        <p class="address-title">{{ 'credit_note.billing_address'|trans }}</p>
        {% if creditNote.billingAddress.company is not empty %}
            <p><b>{{ creditNote.billingAddress.company|default }}</b></p>
        {% endif %}
        <p>{{ creditNote.billingAddress.firstname|default }} {{ creditNote.billingAddress.lastname|default }}</p>
        <p>{{ creditNote.billingAddress.address|default }}</p>
        {% if creditNote.billingAddress.address2|default %}
            <p>{{ creditNote.billingAddress.address2|default }}</p>
        {% endif %}
        <p>{{ creditNote.billingAddress.zipcode|default }} {{ creditNote.billingAddress.city|default }}</p>
        <p>{{ creditNote.billingAddress.country|default }}</p>
    </div>
</div>
{# invoice details #}
<table class="order-lines stripped-even mb-5">
    <thead>
    <tr>
        <th>{{ 'credit_note.table.product'|trans }}</th>
        <th>{{ 'credit_note.table.product_name'|trans }}</th>
        <th>{{ 'credit_note.table.quantity'|trans }}</th>
        <th>{{ 'credit_note.table.unit_price_without_taxes'|trans }}</th>
        <th>{{ 'credit_note.table.green_tax'|trans }}</th>
        <th>{{ 'credit_note.table.subtotal'|trans }}</th>
    </tr>
    </thead>
    <tbody>
    {% for item in creditNote.items %}
        <tr>
            <td>{{ item.code|default }}</td>
            <td>{{ item.name|default }}</td>
            <td>{{ item.quantity }}</td>
            <td>{{ item.unitPriceExcludingTaxes.convertedAmount }}</td>
            <td>{{ item.greenTax.convertedAmount }}</td>
            <td>{{ item.total.convertedAmount }}</td>
        </tr>
    {% endfor %}
    </tbody>
</table>
<div class="mb-5 clearfix">
    <table class="totals-table stripped-odd">
        <tr>
            <td class="pl-4 text-right">{{ 'credit_note.total.net_without_taxes'|trans }}</td>
            <td class="text-right">{{ creditNote.orderTotalExcludingTaxes.convertedAmount }}</td>
        </tr>
        {% if creditNote.orderTotalGreenTax.isZero == false %}
            <tr>
                <td class="text-right">{{ 'credit_note.total.green_tax'|trans }}</td>
                <td class="text-right">{{ creditNote.orderTotalGreenTax.convertedAmount }}</td>
            </tr>
        {% endif %}
        {% if creditNote.shippingCostExcludingTaxes.isZero == false %}
            <tr>
                <td class="text-right">{{ 'credit_note.total.shipping_cost'|trans }}</td>
                <td class="text-right">{{ creditNote.shippingCostExcludingTaxes.convertedAmount }}</td>
            </tr>
        {% endif %}
        {% for tax in creditNote.taxes %}
            <tr>
                <td class="pl-4 text-right">{{ 'credit_note.total.vat'|trans }} {{ tax.rate.toFloat }}%</td>
                <td class="text-right">{{ tax.total.convertedAmount }}</td>
            </tr>
        {% endfor %}
        <tr>
            <th class="text-right">{{ 'credit_note.total.total'|trans }}</th>
            <th class="text-right">{{ creditNote.orderTotalIncludingTaxes.convertedAmount }}</th>
        </tr>
    </table>
</div>
</body>
