<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Facture d'avoir</title>
    <style>
        .mb-5 {
            margin-bottom: 3rem;
        }

        .mb-1 {
            margin-bottom: 0.25rem;
        }

        .mb-2 {
            margin-bottom: 0.5rem;
        }

        body {
            background: white;
            padding: 1rem;
            font-family: Arial;
        }

        .rma-info {
            color: black;
            font-size: .9rem;
            padding: 1rem;
        }

        .address-item .address-title {
            color: black;
            font-size: 1.35rem;
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .address-item p {
            margin: 0 0 .3rem;
            color: #343434;
        }

        thead {
            display: table-header-group
        }

        tr {
            page-break-inside: avoid
        }

        table {
            border-collapse: collapse;
            font-size: 14px;
            border: 1px solid #e8e8e8;
        }

        table.totals-table tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        th {
            background: #eeeeee;
            color: black;
            padding: .75rem;
            font-weight: normal;
        }

        th:not(:last-of-type) {
            border-right: 1px solid #e5e5e5;
        }


        td {
            padding: .75rem;
            text-align: center;
            border-bottom: 1px solid #e5e5e5;
        }

        td:not(:last-of-type) {
            border-right: 1px solid #e5e5e5;
        }

        .order-lines {
            font-size: .8rem;
            width: 100%;
        }

        th {
            font-weight: bold;
        }

        .t-left {
            text-align: left;
        }

        .last-page-wrapper p {
            line-height: 1.6rem;
            margin-bottom: 2rem;
        }

        .f-size-title {
            font-size: 1.35rem;
        }

        .payment-method th {
            background: #343434;
            color: white;
            font-size: 1.1rem;
            font-weight: bold;
        }

        .w-100 {
            width: 100%;
        }

        .w-55 {
            width: 55%;
        }

        .w-50 {
            width: 50%;
        }
        .w-40 {
            width: 40%;
        }

        .w-30 {
            width: 30%;
        }

        .v-top {
            vertical-align: top;
        }

        .i-block{
            display: inline-block;

        }

        .t-bold {
            font-weight: bold;
        }

        .d-block {
            display: block;
        }

        .t-right {
            text-align: right;
        }

        .t-center {
            text-align: center;
        }

        .f-right {
            float: right;
        }

        .separator {
            border: #cccccc 1px solid;
            margin-top: 1%;
            margin-bottom: 1%;
        }

        .separator-detail {
            border: #cccccc 1px solid;
            margin-top: 4%;
            margin-bottom: 4%;
        }

        .clearfix {
            clear: left;
            overflow: auto;
        }
    </style>
</head>
<body>
{# rma #}
<div class="rma-info d-block mb-2">
    <h1 class="t-right">{{ 'credit_note.capital'|trans }}</h1>
    <div class="separator w-100"></div>
</div>

{# Company info #}
<div class="mb-5">
    <div class="w-50 i-block">
        <div class="t-bold">{{ creditNote.company.corporateName|default }}</div>
        {% if creditNote.company.legalStatus is not empty and creditNote.company.capital is not empty %}
            <div>{{ creditNote.company.legalStatus|default }} {{ 'legal_mention_capital'|trans }} {{ creditNote.company.capital|default }} {{ currencySign }}</div>
        {% endif %}
        <div>{{ creditNote.company.address|default }}</div>
        <div>{{ creditNote.company.zipcode|default }} {{ creditNote.company.city|default }}</div>
        <div>{{ creditNote.company.country|default }}</div>
    </div><div class="w-50 i-block v-top ">
        <div class="t-bold f-size-title">{{ 'credit_note'|trans }} #{{ creditNote.creditNoteId }}
        </div>
        <div class="t-bold f-size-title">{{ 'invoice_copy'|trans }} #{{ creditNote.orderInvoiceNumber }}</div>
        <div class="d-block">
            <div class="i-block w-40">{{ 'date'|trans ~ ':'|trans}}</div>
            <div class="i-block">{{ creditNote.getRefundDate.format('credit_note.date_format'|trans) }}</div>
        </div>
        <div class="d-block">
            <div class="i-block w-40">{{ 'customer'|trans ~ ':'|trans}}</div>
            <div class="i-block">{{ creditNote.userId }}</div>
        </div>
        <div class="d-block">
            <div class="i-block w-40">{{ 'shipping'|trans ~ ':'|trans}}</div>
            <div class="i-block">{{ creditNote.shipping }}</div>
        </div>
    </div>
</div>
{# delivery info#}
<div class="mb-5">
    {# billing address #}
    <div class="address-item w-50 i-block">
        <p class="address-title t-bold">{{ 'credit_note.billing_address'|trans ~ ':'|trans}}</p>
        {% if creditNote.billingAddress.company is not empty %}
            <p><b>{{ creditNote.billingAddress.company|default }}</b></p>
        {% endif %}
        <p>{{ creditNote.billingAddress.title.translationKey|trans }}</p>
        <p>{{ creditNote.billingAddress.firstname|default }} {{ creditNote.billingAddress.lastname|default }}</p>
        <p>{{ creditNote.billingAddress.address|default }}</p>
        {% if creditNote.billingAddress.address2|default %}
            <p>{{ creditNote.billingAddress.address2|default }}</p>
        {% endif %}
        <p>{{ creditNote.billingAddress.zipcode|default }} {{ creditNote.billingAddress.city|default }}</p>
        <p>{{ creditNote.billingAddress.country|default }}</p>
    </div>{# shipping address #}<div class="address-item w-50 i-block">
        <p class="address-title t-bold">{{ 'credit_note.shipping_address'|trans ~ ':'|trans}}</p>
        {% if creditNote.shippingAddress.company is not empty %}
            <p><b>{{ creditNote.shippingAddress.company|default }}</b></p>
        {% endif %}
        <p>{{ creditNote.shippingAddress.title.translationKey|trans }}</p>
        <p>{{ creditNote.shippingAddress.firstname|default }} {{ creditNote.shippingAddress.lastname|default }}</p>
        <p>{{ creditNote.shippingAddress.address|default }}</p>
        {% if creditNote.shippingAddress.address2|default %}
            <p>{{ creditNote.shippingAddress.address2|default }}</p>
        {% endif %}
        <p>{{ creditNote.shippingAddress.zipcode|default }} {{ creditNote.shippingAddress.city|default }}</p>
        <p>{{ creditNote.shippingAddress.country|default }}</p>
    </div>
</div>
{# invoice details #}
<table class="order-lines stripped-even mb-2">
    <thead>
    <tr>
        <th>{{ 'credit_note.table.product'|trans }}</th>
        <th>{{ 'credit_note.table.quantity'|trans }}</th>
        <th>{{ 'unit_price'|trans }}</th>
        <th>{{ 'w_ht_price'|trans }}</th>
        <th>{{ 'credit_note.table.subtotal'|trans }}</th>
    </tr>
    </thead>
    <tbody>
    {% if creditNote.items|length > 0 %}
        {% for item in creditNote.items %}
            <tr>
                <td class="t-left">{{ item.name|default }} <br>EAN: {{ item.code|default }}</td>
                <td class="t-bold">{{ item.quantity }}</td>
                <td class="t-right">{{ item.unitPriceIncludingTaxes.convertedAmount|price }}</td>
                <td class="t-right">{{ item.unitPriceExcludingTaxes.convertedAmount|price }}</td>
                <td class="t-bold t-right">{{ item.total.convertedAmount|price }}</td>
            </tr>
        {% endfor %}
    {%  else %}
        <tr>
            <td colspan="5" class="t-center">{{ 'refund.no_item_refunded'|trans }}</td>
        </tr>
    {% endif %}
    </tbody>
</table>
<div class="mb-5 clearfix">
    <div class="d-block w-30 f-right">
        <div class="d-block mb-1">
            <div class="i-block t-right w-55 t-bold">{{ 'subtotal'|trans ~ ':'|trans}}</div>
            <div class="i-block f-right">{{ creditNote.orderTotalIncludingTaxes.convertedAmount|price }}</div>
        </div>
        {% if creditNote.orderTotalGreenTax.isZero == false %}
            <div class="d-block mb-1">
                <div class="i-block t-right w-55 t-bold">{{ 'credit_note.total.green_tax'|trans ~ ':'|trans}}</div>
                <div class="i-block f-right">{{ creditNote.orderTotalGreenTax.convertedAmount|price }}</div>
            </div>
        {% endif %}
        {% if creditNote.shippingCost.isZero == false %}
            <div class="d-block mb-1">
                <div class="i-block t-right w-55 t-bold">{{ 'credit_note.total.shipping_cost'|trans ~ ':'|trans}}</div>
                <div class="i-block f-right">{{ creditNote.shippingCost.convertedAmount|price }}</div>
            </div>
        {% else %}
            <div class="d-block mb-1">
                <div class="i-block t-right w-55 t-bold">{{ 'credit_note.total.shipping_cost'|trans ~ ':'|trans}}</div>
                <div class="i-block f-right">{{ 0|price }}</div>
            </div>
        {% endif %}
        <div class="d-block">
            <div class="i-block t-right w-55 t-bold">{{ 'taxes'|trans ~ ':'|trans }}</div>
        </div>
        {% for tax in creditNote.taxes %}
            <div class="d-block">
                <div class="i-block t-right w-55">{{ 'credit_note.total.vat'|trans }} {{ tax.rate.toFloat ~ '%' ~ ':'|trans}}</div>
                <div class="i-block f-right">{{ tax.total.convertedAmount|price }}</div>
            </div>
        {% endfor %}
        {% if creditNote.discountTotal.isZero == false %}
            <div class="d-block">
                <div class="i-block t-right w-55">{{ 'credit_note.total.total_discount'|trans ~ ':'|trans}}</div>
                <div class="i-block f-right">{{ creditNote.discountTotal.convertedAmount|price }}</div>
            </div>
        {% endif %}
        <div class="separator-detail"></div>
        <div class="d-block">
            <div class="i-block t-right w-55 f-size-title">{{ 'total_cost'|trans ~ ':'|trans}}</div>
            <div class="i-block f-right t-bold f-size-title">{{ creditNote.totalIncludingTaxes.convertedAmount|price }}</div>
        </div>
    </div>
</div>
</body>
