":": ": "
2co_enable_fraud_verification: 'Enable Fraud Notification Help'
2co_fraud_fail: 'Status of orders - Verification failed (fraud)'
2co_fraud_wait: 'Status of orders awaiting verification (fraud)'
3d_secure: '3D Secure'
404: 'Error 404 - Page not found'
absolute: 'Fixed amount'
accept: Accept
accept_company_terms: 'You must also accept the conditions of the sellers'
accept_google_policy: 'I agree with Google''s content policy'
accept_products: 'Accept Products'
access_denied: 'Access denied'
access_denied_text: 'You do not have permission to access the requested page.'
access_event_key_request_subj: 'Request for access key'
access_key: 'Access key'
access_restrictions: 'Access Restrictions'
account: Account
accounting_admin_intro: 'This page allows you to take stock of completed orders, for which you must make transfers to sellers. The table is displayed in 15-day periods in order to make the accounting point at regular intervals.'
accounting_commissions: 'Commission marketplace'
accounting_commissions_ttc: Commission including tax
accounting_commissions_ht: Commission without taxes
accounting_history_menu_description: 'Summary of commissions in your shop.'
accounting_include_shipping_cost: 'included shipping costs'
account_name: 'Account name'
account_number: 'Account number'
account_type: 'Type of account'
acquirerid: 'Buyer ID'
action: Action
actions: Actions
activate_menu_tab_for: 'Enable menu item to send'
activate_selected: 'Enable selection'
activate_skrill_merchant_tools: 'Enable "Skrill Vendor Tools"'
activation_link: 'Activate link'
activation_mode: 'Activated mode'
active: Active
actives: Active
inactive: Inactive
active_categories: 'Active Categories'
active_preset: 'Active preset'
active_products: Products
active_promotions: 'Active Promotions'
act_on_behalf: 'Acting on behalf of'
add: Add
added: Added
adding_block_to_grid: 'Add a block to the grid'
adding_grid: 'Add a grid'
additional_data: 'Additional Data'
additional_images: 'Additional Images'
additional_options: 'Additional Options'
additional_parameter_not_correct: 'The additional parameter is incorrect'
additional_popup_larger_image: 'Pop-up large images of additional thumbnails'
additional_thumbnail: 'Additional thumbnails'
additional__option_thumbnail: 'Additional option thumbnail'
addon: 'Additional module (Add-on)'
addons: 'Additional Modules'
addons.tags.add_a_tag: 'Add a tag'
addon_sql_error: "An SQL query error occurred while installing the module.\_The module will not be installed."
address: Address
addresses: Addresses
address_2: 'Address (continued)'
address2: 'Additional address'
address_commercial: 'Business address'
address_residential: 'Residence address'
address_type: 'Type of address'
add_administrator: 'Add administrator (s)'
add_affiliate: 'Add affiliation (s)'
add_all_to_cart: 'Add to cart'
add_attachment: 'Add attached file (s)'
add_autoresponder: 'Add automatic reply (s)'
add_a_video: 'add a video'
video_not_found: 'Video not found'
url_attachement_not_found: 'URL attachment not found'
add_banner: 'Add banner (s)'
add_banners: 'Add banner (s)'
add_banners_and_close: 'Add banner (s) and close'
add_block: 'Add block (s)'
add_bonus: 'Add Bonus'
add_campaign: 'Add campaign (s)'
add_cart_promotion: 'Add promotion (s) to cart'
add_catalog_promotion: 'Add promotion (s) to the catalog'
add_categories: 'Add category (s)'
add_categories_and_close: 'Add category (s) and close'
add_category: 'Add category (s)'
add_chart: 'Add graphic (s)'
add_combination: 'Add combination (s)'
add_companies: 'Add vendor(s)'
add_companies_and_close: 'Add vendor(s) and close'
add_condition: 'Add condition (s)'
add_credit_card: 'Add credit card (s)'
add_currency: 'Add currency (s)'
add_customer: 'Add customer (s)'
add_datafeed: 'Add data stream'
add_domains: 'Add domain (s)'
add_elements: 'Add item (s)'
add_emails: 'Add email (s)'
add_empty_item: 'Add item (s)'
add_event: 'Add Event (s)'
add_feature: 'Add attribute (s)'
add_field: 'Add Field (s)'
add_file: 'Add file (s)'
add_filter: 'Add filter (s)'
add_filters: 'Add filters'
add_filters_and_close: 'Add filter (s) and close'
add_folder: 'Add folder (s)'
add_form: 'Add form(s)'
add_gift_certificate: 'Add gift certificates'
add_global_option: 'Add global option (s)'
add_grid_block: 'Add grid (s) or block (s)'
add_group: 'Add group (s)'
add_image: 'Add a picture'
add_ips: 'Add IP address (s)'
add_item: 'Add item (s)'
add_items: 'Add [items]'
add_language: 'Add language (s)'
add_language_variable: 'Add language variable (s)'
add_layout: 'Add layout (s) to page'
add_link: 'Add link(s)'
add_localization: 'Add Department (s)'
add_location: 'Add Address (es)'
add_mailing_lists: 'Add listing (s)'
add_menu: 'Add menu (s)'
add_new: 'Add new'
add_news: 'Add news item (s)'
add_newsletter: 'Add newsletter (s)'
add_news_and_close: 'Add news and close'
add_new_datafeed: 'Add data stream'
add_new_field: 'Add Field (s)'
add_new_order: 'Add order (s)'
add_new_variant: 'Add Variant (s)'
add_option: 'Add option (s)'
add_order: 'Add order (s)'
add_orders: 'Add order (s)'
add_orders_and_close: 'Add order (s) and close'
add_page: 'Add page(s)'
add_pages: 'Add page(s)'
add_pages_and_close: 'Add page(s) and close'
add_payment: 'Add payment mode (s)'
add_payout: 'Add installment (s)'
add_plan: 'Add program (s)'
add_poll: 'Add survey (s)'
add_polls: 'Add survey (s)'
add_polls_and_close: 'Add poll (s) and close'
add_post: 'Add post (s)'
add_product: 'Add product (s)'
add_products: 'Add product (s)'
add_products_and_close: 'Add product (s) and close'
add_products_to_section: 'Add products to this section'
add_product_filter: 'Add filter (s)'
add_promotions: 'Add promotion (s)'
add_question: 'Add question (s)'
add_reason: 'Add reason (s)'
add_recipients_from_users: 'Add recipients from users list'
add_related_product: 'Add a related product'
add_related_products: 'Add related products'
add_report: 'Add report (s)'
add_section: 'Add section (s)'
add_selected: 'Add Selection'
add_selected_to_cart: 'Add Selection'
add_shipping_method: 'Add delivery mode (s)'
add_site_map_section: 'Add section (s) to sitemap'
add_state: 'Add region (s)'
add_status: 'Add status (s)'
add_store_location: 'Add location (s)'
add_subcategory: 'Add subcategory (s)'
add_subscriber: 'Add subscriber (s)'
add_subscribers: 'Add subscriber (s)'
add_subscribers_from_users: 'Add subscribers from the users list'
add_subtract_points: 'Add and remove points'
add_tab: 'Add label (s)'
add_tag: 'Add tag (s)'
add_tax: 'Add tax (s)'
add_template: 'Add template (s)'
add_to_cart: 'Add to Basket'
add_to_compare_list: 'To be compared'
add_to_selection: 'Add to selection'
add_to_wishlist: 'Add to wish list'
add_tracking_data: 'Add tracking data'
add_user: 'Add user (s)'
add_users: 'Add user (s)'
add_users_and_close: 'Add user (s) and close'
add_vendor: 'Add vendor(s)'
add_vendor_administrator: 'Add Vendor Administrator(s)'
admin2: 'You can not import into this shop because you are using an expired trial version.'
administration: Administration
administration_panel: Dashboard
administrator: Administrator
administrators: Administrators
administrators_menu_description: 'List of administrators, users registered with an administrator account'
administrators_notified: '[Count] notified administrators.'
admin_only: 'This page is only accessible to administrators'
admin_panel: Dashboard
admin_text_letter_footer: 'The team.'
advanced: Advanced
advanced_filter: 'Advanced Filter'
advanced_search: 'advanced search'
advanced_search_options: 'Advanced search options'
affilate_banners_manager_menu_description: 'Create banners that your affiliates can place on their website'
affiliate: Affiliation
affiliates_menu_description: 'List of affiliated shops, registered users with an affiliate account'
affiliate_agree_to_terms_conditions: 'By clicking on the button "[button_name]" below, you declare that you agree to the terms of our affiliate program.'
affiliate_information: 'Affiliate Information'
affiliate_link: 'Affiliate Link'
affiliate_partners_menu_description: 'Manage your affiliations.'
affiliate_payouts: 'Affiliate payments'
affiliate_plan: 'Affiliate Program'
affiliate_text_letter_footer: 'The sales team.'
affiliate_tree: 'Affiliate tree'
aff_cookie_expiration: 'Service life of a client cookie (days)'
after_full_payment: 'After full payment'
after_sum: 'After the total'
af_big_order_total: 'The total of the order exceeds the maximum limit'
af_country_doesnt_match: 'Country does not match IP address'
af_has_failed_orders: 'Customer has REQUESTED orders'
af_has_successfull_orders: 'Customer SUCCESSFUL'
af_high_risk_country: 'High-risk countries'
af_high_risk_factor: 'The risk factor exceeds the defined threshold'
af_ip_not_found: 'Failed to determine client IP address'
af_long_distance: 'Customer located outside the safe distance'
af_low_risk_factor: 'Acceptable risk'
agent_id: 'ID Agent'
age_verification: 'Age verification'
agreement_required: 'Validation Required'
all: All
allowed: Licensing
allowed_combinations: 'Allowed combinations'
allowed_extensions: 'Extensions allowed'
allowed_extensions_hint: 'Leave this field blank to allow loading of all file types, or enter comma-separated extensions (example: jpg, bmp, gif, pdf)'
allow_api_access: 'Allow this user to use the API.'
allow_return_registration: 'Enable Record Feedback'
all_actions: 'All actions'
all_categories: 'All categories'
all_downloads: 'All Downloads'
all_features: 'All categories'
all_filters: 'All filters'
all_pages: 'All pages'
all_products_have_been_updated: 'All products have been updated'
all_stores: 'All Categories'
all_subcategories: Subcategories
all_users: 'All the users'
all_vendors: Administrator
all_words: 'All these words'
also_bought: 'Also added'
alt_text: 'Alternative text'
always: Always
american_express: 'American Express'
american_express_info: 'The CVV is on the front, below the credit card numbers (it can be on the right or left side of the credit card).'
amount: Amount
transactions.table_header.amount: Amount
transactions.table_header.date: Date
transactions.table_header.type: Type
transactions.table_header.transactions: Transactions
amount_decreased: 'Quantity decreased by'
amount_increased: 'Increased amount of'
amount_type: 'Type of quantity'
and: And
anonymous: Anonymous
answer: Reply
answers: Answers
answer_text: 'Response Text'
any: Some
any_date: 'Any date'
any_option_combinations: 'All combinations of options'
any_status: 'All statutes'
any_words: 'One of these words'
an_error_occured: 'An error has occurred'
api: API
api_access: 'API access'
api_key: 'API Key'
api_need_company_name: 'Company name required (the company param)'
api_need_company_storefront: 'Store name required (the storefront param)'
api_need_correct_company_id: 'Company ID not found (Company ID)'
api_need_feature_name: 'Name of required attribute (the feature_name param)'
api_need_feature_type: 'Name of required attribute (the feature_name param)'
api_need_id: 'Undefined ID (object)'
api_need_order_id: 'Command ID required (the order_id param)'
api_need_params: 'Missing parameters'
api_need_payment_id: 'Required payment IP'
api_need_shipping_id: 'Required carrier ID (the shipping_id param)'
api_need_store: 'Shop required (the company_id param or entity stores)'
api_not_need_id: 'ID must be defined'
api_orders_need_user_info: 'User ID (the user_id param) or user data (the user_data array) required'
api_partner: 'Partner API'
api_password: 'Password API'
api_shipments_not_allow_update: 'Only the tracking_number and the logistician parameters can be updated with the action ''Allow users to create deliveries'''
api_taxes_need_tax: 'Name of required tax (the tax param)'
api_user: 'User API'
api_carriage_paid_threshold_malformed: 'Free shipping threshold must be a number above 0'
appearance: Appearance
appearance_settings: 'Appearance settings'
appearance_type: 'Type of Appearance'
application_id: 'Application ID'
application_login: 'Connecting the application'
apply: Apply
apply_as_link: 'Apply as a link'
apply_for_vendor_account: 'Become a dealer'
apply_tax_to_products: 'Apply selected taxes to all products'
apply_to_all_languages: 'Apply to all languages'
apply_to_products: 'Apply to products'
approval: Validation
approve: Validate
approved: Valid
approve_commissions_menu_description: 'Accept or not the affiliation fee'
approve_selected: 'Accept selection'
arabic: Arab
archive: Archive
area: Region
area_pro_vendor: 'Pro seller area'
arrows: Arrows
asc: Ascending
attachments: Attachments
attention: 'Warning !'
audience: Hearing
australia_post: 'Australian Post'
authentication_failed: 'Authentication Failure'
authentication_key: 'Authentication key'
authentication_password: 'Authentication Password'
author: Author
authorization: Authorization
authorize_capture: 'Allow and Capture'
authorize_only: 'Allow only'
authstatus: 'Statutes of authorization'
auth_code: 'Authorization code'
auth_info: 'Authentication Information'
auth_logs: Authentications logs
auth_logs_id: ID
auth_logs_createdat: Date
auth_logs_login: Login
auth_logs_logins: Login(s)
auth_logs_login_sample: '<EMAIL>, <EMAIL>, ...'
auth_logs_login_doc: 'comma separated'
auth_logs_source: Authentication method
auth_logs_destination: Destination
auth_logs_status: Status
auto: Auto
autoresponders: 'Automatic replies'
auto_set_permissions_via_ftp: 'Auto-set permissions via FTP'
avail: Available
availability: Availability
available: Available
available_fields: 'Available fields'
available_for_vendor: 'Available for dealer'
available_items: 'Available items'
available_languages: 'Available Languages'
available_layouts: 'Available layouts'
available_since: 'Available from'
available_themes: 'Available Themes'
avail_from: 'Available from'
avail_till: 'Available until'
average: Average
average_depth: 'Average depth'
average_duration: 'Average duration'
avs: Avs
awaiting: Waiting
awaiting_approval: 'Waiting for validation'
backtrace: Tracing
backup: Backup
backupping_data: 'Data backup for'
backupping_schema: 'Saving the schema for'
backup_data: 'Saving data from the database'
backup_filename: 'Name of the backup file'
backup_files: 'Backup Files'
backup_schema: 'Saving the BDD schema'
back_in_stock_notification_footer: 'In stock !'
back_in_stock_notification_header: 'The following products are back in your shop'
back_to_the_notification_list: 'Back to list of notifications'
balance: Account
balance_account: 'Account balance'
balance_carried_forward: Reports
bank: Bank
bank_cic: 'CIC Group'
bank_cm: 'Mutual credit'
bank_obc: The
bank_routing_number: 'Routing number of the bank'
banner: Banner
banners: Banners
banners_menu_description: 'Create banners (graphic and text) that can be placed in the shop.'
banners_statistics: 'Banners statistics'
banner_code: 'Banner Code'
banner_code_for_some_products: 'Banner code for some products'
bar: 'Closed off'
base: Based
base_currency: 'Base Currency'
base_currency_not_deleted: 'The currency has not been deleted because it corresponds to the base currency of the shop.'
base_price: 'Starting price'
basket_amount: 'Basket total amount'
basket_title: 'your basket'
bestsellers: Bestsellers
bestselling: Bestsellers
bic: 'BIC / SWIFT'
biller_code: 'Invoice code'
billing_address: 'Billing address'
billing_options: 'Billing Options'
billing_shipping_address: 'Billing and delivery address'
bill_to: 'Billing address'
birthday: Anniversary
block: Block
blocks: Blocks
block_banners: Banners
block_banners_description: 'Banners: visual and text'
block_brands: Brands
block_brands_description: 'Attribute brand products on sale'
block_breadcrumbs: 'Contact Us'
block_breadcrumbs_description: 'Description of the waterway'
block_cart_content: Basket
block_cart_content_description: 'Description of the basket'
block_categories: Categories
block_categories_description: Categories
block_checkout: Order
block_checkout_description: 'Order Information'
block_content: 'Contents of the block'
block_currencies: 'Currencies / Coins'
block_currencies_description: 'Currency Selector'
block_disabled: 'Block not available at this location'
block_enabled: 'Block available at this location'
block_gift_certificates_verify: 'Gift Certificates'
block_gift_certificates_verify_description: 'Entering and Validating Gift Certificates'
block_gift_registry: 'Register of gift certificates'
block_gift_registry_description: 'Event Management'
block_gift_registry_key: 'Access key to events'
block_gift_registry_key_description: 'Required field for event access key'
block_html_block: 'HTML block'
block_html_block_description: 'HTML Content'
block_ip_after: 'Block the IP address after a number of unsuccessful attempts'
block_languages: Languages
block_languages_description: 'Language selector'
block_localizations: Departments
block_localizations_description: 'Departments selector'
block_mailing_lists: 'Subscription to Newsletter'
block_mailing_lists_description: 'Email field for Newsletter'
block_main: 'Main block'
block_main_description: 'Main Content (loaded by controller)'
block_manager: 'Management of blocks'
block_manager.fixed_layout: 'Fixed width'
block_manager.fluid_layout: Fluid
block_manager.full_width_layout: Width
block_manager.grid_columns: 'Column grid'
block_manager.layout_width: 'Layout Width'
block_manager.max_width: 'Maximum width'
block_manager.min_width: 'Minimum width'
block_menu: Menu
block_menu_description: 'Different types of menus'
block_my_account: 'My account'
block_my_account_description: Customer
block_name: 'Name of block'
block_news: News
block_news_description: News
block_options: 'Block options'
block_order: 'Control unit'
block_pages: Search
block_pages_description: 'Description of pages'
block_payment_methods: 'Means of payment'
block_payment_methods_description: 'Means of payment available'
block_polls: Surveys
block_polls_description: The
block_products: Products
block_products_description: Products
block_product_filters: Filters
block_product_filters_description: 'Filter Options'
block_rss_feed: 'RSS feed'
block_rss_feed_description: 'Subscribe to RSS feed'
block_settings: 'Parameter Block'
block_shipping_methods: 'Delivery Methods'
block_shipping_methods_description: 'Available delivery modes'
block_smarty_block: 'HTML block - Smarty'
block_smarty_block_description: 'HTML / Smarty Content'
block_store_locator: 'Shop locator'
block_store_locator_description: 'Block bookmark shop'
block_tags: 'Tag this product'
block_tags_description: 'Product Tags'
block_template: Template
block_template_description: 'Block with template'
block_testimonials: The
block_testimonials_description: 'User Recommendations'
block_text: 'Block text'
block_time: 'Time after which the IP address will be blocked'
block_vendors: Vendors
block_vendors_description: 'Product Description'
block_width: 'Width of block'
block_wrapper_updated: 'Updating the container'
bm_confirm: "This action will modify the [action] action of this block on all pages [location_name].\_Do you want to continue ?"
body: Message
body_html: 'HTML Body'
bonus: Bonus
bonuses: Bonus
bonus_to_apply: 'Bonus to apply'
both: Both
bought: 'Already bought'
bought_amount: 'Purchased Quantity'
box_height: 'Height of the package'
box_length: 'Length of package'
box_width: 'Width of package'
brand: Mark
brands_to_exclude: 'Marques à exclure'
brands_to_include: 'Marques à inclure'
brand_name: 'Nom de la marque'
brand_type: 'Brand / Manufacturer'
breadcrumbs: 'Contact Us'
browse: Browse...
browser: Navigator
browsers: Browsers
browser_name: 'Browser Name'
browser_upgrade_notice: "<p> We've detected that your browser does not fully support the site's dashboard.\_You can use your current browser but some features can not be displayed or can not be used correctly </p> <br> <p> Better use is possible with the following browsers </p> <br> <ul> <li> & ndash;\_<a href=\"http://windows.microsoft.com/en-US/internet-explorer/products/ie/home\" target=\"_blank\"> Internet Explorer 9 and below </a> </li> <li> & ndash;\_<a href=\"http://www.mozilla.org/en-US/\" target=\"_blank\"> Mozilla Firefox (latest release) </a> </li> <li> & ndash;\_<a href=\"https://www.google.com/intl/en/chrome/browser/\" target=\"_blank\"> Google Chrome (latest version) </a> </li> </ul> <br> <p> Click on one of the links to choose the browser of your choice.\_Once the download is complete, install the browser by running the program.\_</p> <br> <p> If you can not update your browser now you can access the dashboard but some features will not be used <br> <br> <a href = \"[url] \"> More </a> </p>"
browser_upgrade_notice_title: 'Browser Update Notice'
browser_version: 'Browser version'
bucket: Bucket
bulk_category_addition: 'Deleting an item from your cart'
bulk_print: 'Batch printing'
bulk_product_addition: 'Adding products by batch'
button_bgcolor: 'Background color button'
button_text_color: 'Text color button'
button_type: 'Push Button Type'
but_change: Change
buyer_name: 'Name of Buyer'
buy_in_advance: 'Buy in advance'
buy_now: 'Buy now'
buy_now_url: 'Buy Now'
buy_together: 'Buying together'
buy_together_calculation_information: 'Total Price: [total_price], Discount: [discount], Combination Price: [combination_price]'
buy_together_combination_cannot_be_added: 'This combination can not be added to cart'
buy_together_fill_the_mandatory_fields: 'Please specify the product''s options before adding it to your cart.'
buy_together_is_not_compatible_with_configurator: "[Product_name] can not be added to the combination.\_The bulk purchase is not compatible with the configurator (configurator addon)."
buy_together_is_not_compatible_with_recurring_billing: "[Product_name] can not be added to the combination.\_Bulk purchase is not compatible with the subscription (Recurring billing addon)."
buy_together_product_was_removed: '[Product] is not present in sufficient quantity ([amount]) and has been deleted from the basket'
by: by
bytes: Bytes
by_domain: 'By Domain'
by_fixed: 'Amount of reduction (€)'
by_percentage: 'Percent Discount (%)'
by_search_engine: 'By search engine'
c128a: "Code 128 is a high density bar code.\_<br/> Widely used throughout the world.\_<br/> The code 128 is designed to encode the 128 ASCII characters.\_<br/> Code 128-A-0-9, AZ, ASCII control codes, special characters.\_Code 128-B-0-9, AZ, az, special characters.\_<br/> Code 128-C-00-99 (double density encoding of digital data only).\_<br/>"
c128b: "Code 128 is a high density bar code.\_<br/> Widely used throughout the world.\_<br/> The code 128 is designed to encode the 128 ASCII characters.\_<br/> Code 128-A-0-9, AZ, ASCII control codes, special characters.\_Code 128-B-0-9, AZ, az, special characters.\_<br/> Code 128-C-00-99 (double density encoding of digital data only).\_<br/>"
c128c: "Code 128 is a high density bar code.\_<br/> Widely used throughout the world.\_<br/> The code 128 is designed to encode the 128 ASCII characters.\_<br/> Code 128-A-0-9, AZ, ASCII control codes, special characters.\_Code 128-B-0-9, AZ, az, special characters.\_<br/> Code 128-C-00-99 (double density encoding of digital data only).\_<br/>"
c2c_access_denied_text: 'You do not have permission to access the requested page.'
c2c_company_applied: 'Private seller account created'
c2c_delivery_place: 'Place of exchange'
c2c_euro_delivery_cost: 'Shipping in Euro'
c2c_page_title: Sale
c2c_part_type: Particular
c2c_product_creation_description_placeholder: ''
c2c_product_creation_price_placeholder: ''
c2c_product_creation_short_description_placeholder: 'Short description of the product'
c2c_product_creation_title_placeholder: ''
c2c_product_delivery: 'Delivery of the object'
c2c_product_features: 'Further information'
c2c_product_type: 'Type of product'
c2c_pro_button: 'Are you a professional ?'
c2c_pro_type: 'Professional (100% free for professionals)'
c2c_publish_ad: Advertise
c2c_punchline_1: 'Payment 100% guaranteed'
c2c_punchline_2: 'Securing Data'
c2c_punchline_3: 'Sell ​​totally free'
c2c_punchline_4: 'It''s for everyone'
c2c_punchline_know_more: 'Learn More'
c2c_your_ad: 'Your ad'
c2c_your_ad_description: 'Description of your ad'
c2c_your_ad_short_description: 'Summary of your ad'
c2c_your_ad_title: 'Ad Title'
c39: "Also known as USS Code 39, 3 of 9. The code 39 can encode alphanumeric characters.\_The illustration is used in a non-distribution environment.\_<br/> the code 39 is designed to encode the 26 capital letters, 10 digits and 7 special characters."
cache_cleared: 'The cache has been deleted'
calculate: Calculate
calculate_shipping_cost: 'Calculate shipping costs'
calendar: Calendar
came_from: 'Comes from'
came_to: 'Goes to'
campaign: Campaign
campaigns: Campaigns
campaign_stats: 'Campaign statistics'
canada_post_activation_error: 'To activate the Canadian mail delivery process, the Canadian Dollar must be available.'
cancel: Cancel
canceled_reversal: Cancel
cancelled: Canceled
cancel_url_target: 'Cancel target URL'
cannot_buy: 'You can not purchase the product with these options'
cannot_proccess_checkout: 'Your cart is empty, you can not order.'
cannot_proccess_checkout_without_payment_methods: 'There is no form of payment available, you can not order.'
cannot_update_delete_protected_attribute: 'Unable to change or delete this attribute because it is protected'
cannot_write_file: "Unable to write to file <b> [file] </b>.\_Check the write permissions of the file and directory."
cant_find_thread: 'The link can not be found.'
cant_save_percentage_price: 'You can not use a percentage as the base price.'
cant_upload_file: 'The file can not be loaded.'
cant_upload_file_not_fqdn_domain: 'File URLs must use a registered domain name resolved to the public Internet.'
can_choose_pickup_points: 'It is possible to have you delivered to a relay point close to home: '
capture: Capture
card: Map
cardholder_name: 'Name of Holder'
cardtype: 'Card Type'
card_number: 'Card number'
carousel: Carousel
carrier: Carrier
carrier_aup: 'TO P'
carrier_can: CAN
carrier_dhl: DHL
carrier_fedex: Fedex
carrier_swisspost: 'Swiss Post'
carrier_temando: Temando
carrier_ups: UPS
carrier_usps: USPS
cart: Basket
cart_content: 'Shopping Cart'
cart_contents: 'Cart Contents'
cart_info: 'CS-Cart Info'
cart_is_empty: 'Empty basket'
cart_items: 'Shopping Cart'
cart_or_wishlist: 'Shopping Cart or Wish List'
cart_subtotal: 'Subtotal basket'
cash_on_delivery: 'Cash on delivery'
catalog: Catalog
catalog_mode: 'Catalog mode'
catalog_pages: 'Catalog pages'
catalog_sync_exclude_regarding_brand: 'Exclude according to brand'
catalog_sync_exclude_regarding_category: 'Exclude according to category'
catalog_sync_exclude_specific_products: 'Exclude specific products'
catalog_sync_excluding_rules: 'Exclusion rules'
catalog_sync_including_rules: 'Inclusion rules'
catalog_sync_regarding_brand: 'Synchronize according to the brand'
catalog_sync_regarding_category: 'Synchronize according to category'
catalog_sync_rules: 'Synchronization rules'
catalog_sync_rules_menu_description: 'Rules for synchronizing products from catalog'
catalog_sync_selected_brands: 'selected brand(s)'
catalog_sync_selected_categories: 'selected category(ies)'
catalog_sync_selected_items: 'selected items'
catalog_sync_selected_mvps: 'selected multi-vendor products'
catalog_sync_specific_products: 'Synchronize specific products'
categories: Categories
categories_have_been_added: 'Categories added'
categories_menu_description: 'See the tree and the product categories used to group your products.'
categories_products: Categories
categories_selected: '[Count] selected category (s)'
categories_to_exclude: 'Catégories à exclure'
categories_to_include: 'Catégories à inclure'
category: Category
category_delimiter: 'Category delimiter'
category_description: 'Category Description'
category_is_empty: 'Please select a category'
category_location: 'Path of category'
category_name: 'Category Name'
category_not_found: 'Category not found.'
category_picker: 'Category Selector'
center: Center
central: Central
central_content: 'Central Content'
certificate: 'Gift Certificates'
certificate_already_used: 'This gift voucher has already been used.'
certificate_code_not_available: "This gift voucher can not be used.\_Please contact the administration."
certificate_code_not_valid: 'Gift voucher code is invalid.'
certificate_filename: 'Gift-card file name'
certificate_verification: 'Validate / redeem gift voucher'
change: Change
changed: Exchange
changes: Changes
change_access_permission_to_config: "It is recommended that you change the permissions of the config.local.php file.\_It should have 644 permissions (read / write for owner and read for others)."
change_addons_initialization: 'Changing module initialization'
change_declination_type_board: 'Display board view'
change_declination_type_list: 'Display list view'
change_language: 'Change the language'
change_password_notification: 'Please change your password'
change_password_notification_body: "Your [store] administration password has been changed [days] days ago.\_For security reasons, please change your password.\_To do this, follow the link:"
change_permissions: 'Change permissions'
change_points: 'Edit Points'
change_range: 'Edit Selection'
charge: Charge
charged_amount: 'Amount of charges'
charge_to_vendor_account: 'Load vendor account'
charset: Characters
chart: Graphic
charts: Graphics
chart_period: 'Date Interval'
chart_type: 'Chart type'
check: Verification
checkbox: 'Check box'
checking_account_number: 'Checking Account Number'
checkout: Order
checkout_as_guest: 'Order as guest'
checkout_edp_terms_n_conditions: 'I accept the terms contained in'
checkout_payment_error: 'Une erreur est survenue lors du paiement. Nous ne pouvons traiter votre demande actuellement, merci de bien vouloir réessayer ultérieurement'
checkout_terms_and_conditions: 'Please confirm that you have read and understood and that you accept our General Conditions of Use and our General Conditions of Sale by ticking the box.'
checkout_terms_n_conditions: 'Please confirm that you have read and understood and that you accept our [terms_of_use] and our [terms_and_conditions] by checking the box.'
checkout_terms_n_conditions_alert: 'To continue you must accept our terms and conditions.'
check_again: 'Double check'
check_all: All
check_disabled: Unavailable
check_items_text: 'To include order status, payment methods, status in the conditions list, check the required points and click the "Save" button.'
check_none: 'No'
check_number: 'Number Check'
check_server_export_settings: 'Checking attributes for export.'
check_ssl: 'SSL verification'
check_uncheck_all: 'All check / Uncheck all'
child_pages: Child
chinese: Chinese
choose: 'To choose'
choose_action: 'Choose an action'
choose_category: 'Choose a category'
choose_pickup_points: 'Selectionner un point relais.'
choose_user: 'Choose a user'
choose_your_country: 'Choose your country'
choose_your_store_mode: 'Choose your mode of use'
chp: 'Swiss Post'
chronopost_point_relais_mandatory: 'Veuillez sélectionner un point relais sur la carte'
cities: Cities
city: City
classes: Classes
clean: 'To erase'
cleanup_history: 'Clear History'
cleanup_log: 'Clear Log'
clean_logs: 'Clear Log'
clean_up_all_locations_on_import: 'Delete placements before import'
clear: 'To erase'
clear_cache: 'Clear cache'
clear_cache_info: 'If the columns have been added or modified, please <a href="[clear_cache_url]"> clear the cache </a> to regenerate prices.'
clear_cart: 'Clear the basket'
clear_conditions: 'Clear conditions'
clear_fields: 'Clear fields'
clear_list: 'Clear List'
clear_thumbnails: 'Clear generated thumbnails'
clear_wishlist: 'Clear Wish List'
clicks: News
click_vs_show: 'Percentage of clicks and banners'
client_id: 'Customer ID'
clone: 'Duplicate the product'
clone_language: 'Duplicate the language'
clone_categories: 'Copy categories'
clone_from: Duplicate
clone_layouts: 'Duplicate layout'
clone_pages: 'Duplicate pages'
clone_payments: 'Duplicate payment methods'
clone_products: 'Duplicate products'
clone_product_features: 'Duplicate Product Attributes'
clone_product_filters: 'Duplicate Filters'
clone_profile_fields: 'Duplicate fields'
clone_promotions: 'Duplicate promotions'
clone_selected: 'Duplicate selection'
clone_settings: 'Copy settings'
clone_shippings: 'Duplicate modes of transport'
clone_sitemap: 'Duplicate site map'
clone_static_data_clone: 'Duplicate menus'
clone_table: 'Duplicate table'
clone_theme: 'Duplicate theme'
clone_this_item: 'Duplicate this item'
clone_this_page: 'Duplicate this page'
close: 'To close'
cmcic_societe: 'Company CM-CIC'
cname: CNAME
code: Code
coefficient: Coefficient
collapse_bg_color: 'Reduce background color'
collapse_sublist_of_items: 'Close sub-list'
collapse_text_color: 'Reduce the color of the text'
color: Color
combination: Combination
combination_products: 'Product Combination'
comma: Comma
comment: Comment
comments: Reviews
comments_and_reviews: 'Comments and Notes'
comments_and_reviews_menu_description: 'Customer reviews, product reviews, categories, orders, etc.'
comment_by: 'by :'
comment_hint: 'Enter the comment that will appear under the option'
commission: Commission
commission_deleted: 'Commission deleted successfully.'
commission_does_not_exist: 'Commission doesn''t exist.'
commissions: Commissions
commissions_menu_description: 'Set vendors commissions'
commissions_of_last_periods: 'Commissions of the last periods'
commission_category_duplicate: 'A category appears more than once in the setting of commissions'
commission_rates: 'Commission rate'
commission_rules: "If no rules are set on vendors, the default rule applied is the <a href=\"%url%\" target=\"_blank\">marketplace commission rule</a>. If you want to apply no commission on the vendor, fill in 0 as commission percentage. "
communication: Communication
compact_list: 'Compact list'
companies: Vendors
company: Company
company_address: 'Company Address'
company_applied: 'Seller''s account created'
company_approved: 'Approved vendor account'
company_disabled: 'Seller''s account disabled'
company_id: 'Seller ID'
company_name: 'Company Name'
company_not_found: 'Company not found'
company_phone: 'Company Phone'
company_phone_intl_format: 'International format (e.g. +***********)'
company_rejected: 'Sales counter disapproved'
company_settings: 'Company Settings'
company_payout_frequency: 'Frequency of transfers'
company_trigger_payment: 'Trigger Payout for the vendor'
company_trigger_payment_modal_title: 'Warning'
company_trigger_payment_modal_disabled: ' Insufficient funds to process the bank transfer ([balance] €)'
company_trigger_payment_modal_body: 'You are about to transfer [balance] € on the seller bank account. Do you still want to proceed ?'
company_trigger_payment_modal_button_proceed: 'Proceed'
company_trigger_payment_modal_button_cancel: 'Cancel'
company_trigger_payment_success: 'Payout suceeded, the merchant will receive [balance] € on his bank account.'
company_trigger_payment_error: 'An error occured. The payout has not been processed.'
company_trigger_payment_success_api: 'Payout processed'
company_trigger_payment_wallet_balance_api: 'Wallet balance is [balance] €'
company_trigger_payment_only_admin_api: 'This API is accessible only by administrators'
technical_wallet: 'Technical Wallet'
compatible_class: 'Compatible class'
completed: Completed
compressing_backup: 'Backup File Compression'
compress_dump: 'Compress Backup File'
condition: Condition
conditions: 'Terms & Conditions'
configuration: configuration
configurator: Configurator
configurator_groups: 'Configuring Groups'
configurator_menu_description: 'Define the set of parameters and attributes for configurable products.'
configure: Configure
configure_payment_methods_helper: 'Configure the payment methods available on your marketplace. These payment methods will be available to customers.'
configure_payment_methods_title: 'PAYMENT METHODS MANAGEMENT'
confirm: Confirm
confirmation_dialog: 'Confirmation box'
confirmed: Confirmed
confirm_password: 'Confirm password'
conflicts: Conflicts
congratulations: Confirmed!
connection_ticket: 'Connection Ticket'
consult_a_vendor: 'Consult'
contact_form_submitted: 'Contact form submitted'
contact_information: 'Contact Information'
further_information: 'Further Information'
contact_us_for_price: 'No products in stock with these options'
container_options: 'Container options'
container-parameters: 'Container parameters'
content: Content
content_pages: 'Content Pages'
content_alignment: 'Aligning Content'
content_changed_for: 'This block has different contents for'
content_pages_menu_description: 'Create and publish pages.'
context: 'Context'
continue: 'Carry on'
continue_shopping: 'Continue Shopping'
controller: Replace
controller_description: 'If you want to define an SEO name for the URL, for example http://example.com/index.php?dispatch= <b> categories.catalog </b>, you must enter the "<b> categories. Catalog </b> "in the <b>" dispatch "parameter of the URL </b>" and specify the SEO name in the "<b> SEO name </b>" field.'
conversion: Conversion
converting_data_in_table: 'Convert information into an array [table]'
cookies_dismiss: OK
cookies_learnmore: 'Learn More'
cookies_message: 'Our site uses cookies to improve your user experience and provide you with relevant content.'
coordinates: 'Contact information'
copyright_shopping_cart: Marketplace
copy_from_layout: 'Copy layout data'
core: Body
cost: Cost
cost_dependences: 'Depending on price'
could_not_open_file: 'Sorry, we can not open this file'
countries: Country
countries_list: 'List of countries'
countries_menu_description: 'Manage the list of authorized countries in your shop.'
country: Country
coupon: Coupon
coupons: Coupons
coupon_already_used: 'This coupon is already used'
coupon_code: 'Coupon code'
coupon_doesnt_exists: 'The coupon code is not valid'
cpi_hash_key: 'Key hash CPI'
create: Create
created: Created
create_account: 'Create an account'
create_administrator_account: 'Create an administrator account'
create_and_close: 'Create and close'
create_file: 'Create a file'
create_folder: 'Create a directory'
create_gift_certificate: 'Create a gift voucher'
create_gift_certificate_for_customer: 'Create a gift voucher for this customer'
create_guest_account: 'Quick order with a guest account.'
create_mvp_from_product: 'Create a unified product sheet from this product'
create_new_account: 'Create a permanent account and use it to order.'
create_new_block: 'Create a new block'
create_new_order: 'Create new order'
create_process_payment: 'Create and validate payment'
create_profile: 'Create a new profile'
create_profile_notification_header: 'You are now an administrator of the store ''[company_name]'' on our marketplace. <br> Thank you for registering <br> <br> You can log in using the following credentials:'
create_table: 'Create a new table'
creating: Creation
creating_table: 'Creating the table'
creation_date: 'Creation day'
credit_card: 'Credit card'
credit_card_info: "This number is located next to the signature on the back of the card.\_This is 3 digits after the credit card number."
credit_card_number: 'Credit Card Number'
credit_memo: 'Balance Sheet'
credit_memo_id: 'ID Memo balance'
cresecureapitoken: 'CRE SecureAPIToken'
cresecureid: 'CRE SecureID'
cresecure_allowed_types: 'Authorized card type'
cron_export: 'Cron export settings'
crossed_out_price: 'Crossed out price'
csv_delimiter: 'CSV delimiter'
csv_file: 'CSV file'
currencies: Currencies
currency: Change
currencycode: 'Currency code'
currency_code_aed: 'United Arab Emirates - Dirham'
currency_code_ang: ANG
currency_code_ars: Ars
currency_code_aud: 'Australian dollar'
currency_code_awg: AWG
currency_code_bgn: BGN
currency_code_brl: 'Real Brazilian'
currency_code_byr: BYR
currency_code_cad: 'Canadian Dollar'
currency_code_chf: 'Swiss franc'
currency_code_cny: CNY
currency_code_cyr: 'Cyprus Pound'
currency_code_czk: 'Czech Koruna'
currency_code_dkk: 'Danish Kroner'
currency_code_eek: 'Estonian Kroon'
currency_code_egp: EGP
currency_code_eur: Euro
currency_code_fjd: 'Fiji Dollar'
currency_code_frf: 'Franc Francais'
currency_code_gbp: 'UK Pound'
currency_code_gel: GEL
currency_code_hkd: 'Hong Kong Dollar'
currency_code_hrk: HRK
currency_code_huf: 'Hungarian Forint'
currency_code_ils: 'Israel New Shequel'
currency_code_inr: 'Indian Rupee'
currency_code_isk: 'Iceland Krona'
currency_code_jpy: 'Japanese Yen'
currency_code_kpw: 'North Korean Won'
currency_code_krw: 'South Korea Won'
currency_code_kwd: 'Kuwaiti Dinar'
currency_code_ltl: 'Lithuanian Litas'
currency_code_lvl: 'Latvian Lat'
currency_code_mad: MAD
currency_code_mxn: 'Mexican Peso'
currency_code_myr: 'Malaysian Ringgit'
currency_code_nok: 'Norwegian Kroner'
currency_code_nzd: 'New Zealand Dollar'
currency_code_php: 'Philippine Peso'
currency_code_pln: 'Polish Zlotych'
currency_code_png: 'Papua New Guinea Kina'
currency_code_ron: RON
currency_code_rub: RUB
currency_code_rur: 'Russian Ruble'
currency_code_sar: 'Saudi Riyal'
currency_code_sbd: 'Solomon Islands Dollar'
currency_code_sek: 'Sweden Kroner'
currency_code_sgd: 'Singapore Dollar'
currency_code_sit: 'Slovenian Tolar'
currency_code_skk: 'Slovak Koruna'
currency_code_thb: 'Thai Baht'
currency_code_top: 'Tongan Pa''anga'
currency_code_try: 'Turkish Lira'
currency_code_twd: 'New Taiwan dollar'
currency_code_uah: UAH
currency_code_usd: 'US Dollar'
currency_code_vuv: 'Vanuatu Vatu'
currency_code_wst: 'Samoan Tala'
currency_code_xaf: XAF
currency_code_xof: XOF
currency_code_xpf: XPF
currency_code_zar: Rand
currency_deleted: 'The currency has been successfully removed.'
currency_rate: 'Exchange rate'
currency_rate_greater_than_null: 'The exchange rate must be greater than 0.'
currency_sign: Sign
current_amount: 'Current Amount'
current_database_size: 'Size of the current database'
current_page: 'Current page'
current_path: 'Current path'
current_period: 'Current Period'
current_theme: 'Current theme'
current_video: 'Current Video'
custom: Custom
customer: Customer
customers: Customers
customers_also_bought: 'Customers who bought this product also purchased'
customers_menu_description: 'List of customer shops, users with a customer account.'
customers_with_abandoned: 'Customer with abandon'
customer_details: 'Customer Details'
customer_email: 'Customer email'
customer_id: 'Customer ID'
customer_info: 'Customer info'
customer_information: 'Customer Information'
customer_notes: 'Customer Notes'
customer_signature: 'Client''s signature'
customer_text_letter_footer: 'See you soon! <br /> <br /> The customer service team.'
customization_mode: 'Custom mode'
customize: Personalize
customize_theme: 'Custom Theme'
custom_event_fields: 'Custom Event Fields'
custom_range: 'Customized range'
cutted_product_codes: 'The following codes are too long and have been cut to 32 characters:'
cvv2: 'CVV / CVC'
czech: Czech
daily: Daily
danish: Danish
dashboard: Dashboard
database: Database
database_data_changes: 'Changes to the database'
database_maintenance: 'Database maintenance'
database_structure_changes: 'Changes in the structure of the BDD'
datafeed_name: 'Name of the data stream'
datatrans_caa: 'Authorization with immediate implementation'
datatrans_noa: 'Authorization only'
datatrans_sign: 'Digital Signature'
data_feed: 'Data flow'
data_feeds: 'Data flow'
date: Dated
date_added: 'Date Added'
daterange_from: Date start
daterange_to: Date end
date_of_birth: 'Birth date'
loyalty_identifier: 'Loyalty identifier'
nationalities: 'Nationalities'
max_nationalities: 'Maximum 3 nationalities'
date_validation_error: 'The end date must be after the start date.'
day: day
days: days
days_old: days
db_backup_restore: 'Restore database backup'
db_name: 'database name'
dear: Dear
debit: Debit
decimals: Decimals
decline: Refuse
declined: Refuse
declined_products_information: 'Rejected Product Information'
decline_products: 'Rejected products'
decline_reason: 'Decline reason'
decline_reason_prompt: 'Please enter a reason to decline this order'
decline_selected: 'Refuse Selection'
decorator: 'Decorator / painter'
decrease: Decrease
dec_sign: 'Decimal sign'
default: Defect
default_category_layout: 'Layout of default categories'
default_language: 'Default language'
default_layout: 'Default layout'
default_long_options_template: 'Default template (long product option names)'
default_product_details_layout: 'Parent (default)'
default_template: 'Default template'
default_value: 'Default value'
default_values: 'Default values'
deferred: Deferred
defined: Defined
defined_desired_products: 'List of products'
defined_items: 'Defined elements'
define_default_commission: 'Default commission applying to all new sellers'
define_default_commission_for_all_vendors: 'Apply default commission to all sellers'
define_default_commission_for_new_vendors: 'Apply this default commission to future new sellers'
define_default_commission_for_old_vendors: 'Apply default commission to all sellers without commission'
maximum_commission_value: 'Commission’s maximum must be superior to 0 or empty.'
delay: 'Time (in seconds)'
delete: Delete
deleted: Deleted
deleted_product: 'Remove product'
delete_all_found: 'Remove all found'
delete_all_products: 'Remove all products'
delete_block: 'Delete the block'
delete_confirmation: 'Are you sure you want to delete the selected items?'
delete_grid: 'Remove gridlines'
delete_image: 'Remove image'
delete_image_pair: 'Delete even images'
delete_install_folder: "Please remove the installation directory.\_<br /> <br /> Keeping the installation directory on the server raises a security issue."
delete_range: 'Remove selection'
delete_selected: 'Remove selection'
delete_settings: 'Delete settings'
delete_this_category: 'Remove this category'
delete_this_event: 'Delete this event'
delete_this_page: 'Delete this page'
delete_this_return: 'Delete this Return'
deliver: Deliver
delivery_time: 'Delivery time'
delivery_times_text: 'Delivery time is calculated in working days'
demo: Download
demo_mode: 'The demonstration mode is on the way'
demo_mode_content_text: '<p> This basket works in <b> Demonstration </b> mode and this feature is disabled. </p>'
demo_site_panel: 'Demonstration Dashboard'
denied: Refuse
dependence: 'Select values ​​by'
deposit_amount: 'Amount repaid'
desc: Descending
description: Description
descr_avs: 'Audit Response'
descr_cvv: 'Verification Response CVV2'
design: Design
design_mode: 'Fashion design'
desired_amount: 'Desired quantity'
desktop: Desktop
destination: Destination
detailed_description: 'detailed description'
detailed_images: 'Additional Images'
detailed_info: 'Detailed information'
details: details
development: Development
development_settings: 'Development parameters'
device_type: 'Type of device'
dev_css: 'CSS mode debug'
dev_css_tooltip: "Disable the combination of CSS files in a single file.\_Note: This Widget will not work with this setting enabled."
dev_js: 'JavaScript mode debug'
dev_js_tooltip: "Unable to combine JavaScript files into a single file.\_Note: The widget will not work with this setting enabled."
dhl: DHL
diff: 'Change File'
different_only: 'Different only'
directory: Directory
direct_download: Download
dirty_state_notice: 'Unsaved modifications. Quit?'
disable: Disable
disabled: 'Off'
disabled_categories: Categories
disable_for: 'Disabled for'
disable_on_site_text_editing: 'Turn off editing text on site'
disable_selected: 'Disable selection'
disapprove: 'To disapprove of'
disapproved: Disapproved
disapprove_selected: 'Disapprove the selection'
discount: Reduction
discounted_price: 'Discounted prices'
discounts: Discounts
discount_coupon: 'Discount coupon'
discount_coupon_code: 'Reduction Coupon Code'
discussion: Discussion
discussion_manager: Discussion
discussion_title_category: Reviews
discussion_title_company: Reviews
discussion_title_giftreg: 'Golden Book'
discussion_title_home_page: Testimonials
discussion_title_home_page_menu_description: 'Manage the testimonials of your visitors.'
discussion_title_news: Reviews
discussion_title_order: Discussion
discussion_title_page: Reviews
discussion_title_product: Reviews
discussion_title_return: Discussion
discussion_type: Reviews
discuss_back_to_discussion_list: '<Back to the discussion list'
discuss_breadcrumbs: 'Internal messaging '
discuss_close_discussion: 'Ending the discussion'
discuss_front_header: 'Discussions are initiated with vendors from their product sheets. You will find here the discussions already begun.'
discuss_hide_discussion: 'Delete this thread'
discuss_initiator: 'Created by'
discuss_initiator_also_recipient: 'You can not open a discussion with yourself'
discuss_last_message: 'Last message'
discuss_list_helper: 'Find below all the messages exchanged between buyers and sellers. There may be pre-sales questions, or messages exchanged after an order has been validated. As an administrator, you can only see the conversations. '
discuss_list_helper_with_feature_flag: |
    Find below all the messages exchanged between buyers and sellers.
    There may be pre-sales questions, or messages exchanged after an order has been validated.
    As an administrator, you can delete a discussion.
    You can as well participate to it, your message will then be sent as an Administrator of the Merchant the discussion is linked to.
discuss_list_title: 'MESSAGES BETWEEN BUYERS AND SELLERS'
discuss_menu: 'Internal messaging'
discuss_message_posted: 'Private message sent'
discuss_my_messages: 'My messages'
discuss_reception_date: 'Message received on [date] at [time]'
discuss_recipient: Recipient
discuss_send_date: 'Message sent on [date] to [time]'
discuss_title_about_product: 'About the product [product]'
discuss_title_company: 'Contact [company]'
discuss_with: 'Discussion with'
discuss_your_message: 'Your message'
add_document: 'Add a document'
max_file_size_authorized: "Maximum size allowed: 25Mo"
file_format_authorized: 'Accepted formats: jpg, jpeg, png, pdf'
dispatch: Sent
dispatch_value: 'Sent value'
displayed_vendors: 'Displayed vendors'
display_bottom_buttons: 'Show Buttons'
display_delete_icons: 'Show picto de suppression'
display_in_promotions: 'Show in promotions'
display_message: 'View Post'
display_more_product_features: 'More information'
display_more_variants_count: 'Number of filtered variants displayed before the "View All" link.'
display_on: Showing
display_on_homepage: 'Show on homepage'
display_on_selected_categories: 'Show on selected categories'
display_on_subcategories_of_selected_categories: 'Show subcategories of the selected categories'
display_type: 'Display Type'
display_variants_count: 'Number of filtered variants displayed before the "Plus" link'
domain: Field
domains: Areas
domestic_usps: 'USPS Domestic'
done: fact
dont_recalculate_order: 'Do not recalculate the command'
dont_track: 'Do not follow'
dont_use_cardinal: "Do not use Cardinal Centinel & reg;\_For 3-D Secure Payer Authentication"
dots: Points
down: Low
download: Download
downloadable: 'Service or downloadable'
downloadable_shipping: 'Enable delivery if downloadable'
downloads: Downloads
downloads_max_left: 'Downloads max / restants'
download_key_expiry: 'Expiration of the download key'
download_link_expired: 'The download link has expired.'
download_overview: 'Télécharger le récapitulatif de commande'
do_not_assign_vendor: 'Do not assign to a vendor'
do_not_change: 'Do not change'
do_not_create_credit: 'Do not generate credit note number'
do_not_create_invoice: 'Do not generate invoice number'
do_not_pass_logo: 'Do not give the shopping cart logo to the vendor'
do_not_use: 'Do not use'
drlicense_number: 'Driver''s License Number'
dropdown_horizontal: 'Horizontal scrolling'
dropdown_limit: 'Minimum number if items are placed in the scroll list'
dropdown_second_level_elements: 'Elements of second level'
dropdown_third_level_elements: 'Third level elements'
dropdown_vertical: 'Vertical scrolling'
drop_existing_data: 'Delete all files from existing products before importing.'
due_date: 'Due date'
dump_cant_create_file: "Can not create the file for the dump of the database.\_Please check the permissions of the \"var / database\" directory."
dump_file_not_writable: 'The file that must contain the database backup does not have write permission.'
duplicate: 'Duplicate Transactions'
dutch: Dutch
dynamic: Dynamic
dynamic_content: 'This block will be shown on <a href="[url]"> this page </a> only.'
dynamic_tree_cat: 'Dynamic shaft'
dynamic_tree_pages: 'Dynamic shaft'
earned_points: 'Points earned per product'
earned_point_modifier: 'Earned Points Modifier'
easing: Ease
edit: Edit
editing: Edition
editing_attachment: 'Editing the attached file'
editing_banner: 'Banner Edition'
editing_block: 'Edit a block'
editing_category: 'Edit a Category'
editing_certificate: 'Edition of purchase coupons'
editing_chart: 'Edit the chart'
editing_combination: 'Edit combinations'
editing_container: 'Container Edit'
editing_currency: 'Currency Edit'
editing_defined_products: 'Edit defined products'
editing_event: 'Edit an event'
editing_file: 'Edit a file'
editing_filter: 'Edit a Filter'
editing_folder: 'Edit a folder'
editing_form: 'Edit a Form'
editing_grid: 'Edit a grid'
editing_group: 'Edit a group'
editing_item: 'Edit an item'
editing_language: 'Edit a language'
editing_layout: 'Edit the layout'
editing_link: 'Edit a link'
editing_localization: 'Edition of the department'
editing_location: 'Edit a destination'
editing_mailing_list: 'Edit a mailing list'
editing_menu: 'Edit a menu'
editing_news: 'Edit a news'
editing_option: 'Edit an option'
editing_order: 'Edit an Order'
editing_page: 'Edit a page'
editing_payment: 'Edit a Payment'
editing_poll: 'Poll Edition'
editing_product: 'Edit a Product'
editing_product_feature: 'Edit a Product Attribute'
editing_profile: 'Profile Edit'
editing_profile_field: 'Edit a profile field'
editing_promotion: 'Publishing a promotion'
editing_question: 'Edit a question'
editing_quick_menu_link: 'Edit Quick Menu Links'
editing_quick_menu_section: 'Edit a quick menu section'
editing_report: 'Edit a Report'
editing_shipping_method: 'Edit a Delivery Method'
editing_sitemap_section: 'Edit the site map section'
editing_status: 'Edit a status'
editing_store_location: 'Edit a Store Location'
editing_tab: 'Edit a table'
editing_tax: 'Edit a Tax'
editing_vendor: 'Vendor Edition'
edition_type: 'Type of edition'
edit_admin_forbidden: 'Update of an admin type user account is restricted to account owner only. Unable to save.'
edit_files: 'Editing a file'
edit_layout: 'Edit Layout'
edit_order: 'Change order'
edit_products: 'Edit a product'
edit_profile: 'Profile Edition'
edit_profile_note: 'Change email, password, delivery and billing information, and other profile settings'
edit_report: 'Edit Report'
edit_selected: 'Edit Selection'
edp_access_granted: 'Download Links:'
edp_access_subj: 'Access to downloadable products'
edp_enable_shipping: 'Enable delivery of downloadable products'
electric_city: 'Electric City'
email: E-mail
emails: Emails
email_address: 'E-mail adress'
email_cannot_be_empty: 'The email can not be empty'
email_header: 'E-mail header'
email_of_friend: 'Email of your friend'
email_subject: 'Subject of the mail'
email_to: 'Email to'
empty: Empty
empty_email_recovery: 'Please enter a valid email.'
empty_facet_value: 'Not specified'
empty_key_value: 'Value of the empty table key'
enable: Activate
enabled: Enabled
enable_catalog_mode: 'Enable Catalog mode'
enable_for: 'Enable to'
enable_or_disable_block: 'Activating / deactivating the block'
enable_or_disable_container: 'Enabling / Disabling Container'
enable_or_disable_grid: 'Enabling / Disabling the Grid'
enable_quick_view: 'Enabled Quick View'
enclosure: Attachment
encryption: Encoding
encryption_key: 'Encryption key'
end_date: 'End date'
english: English
enter_code: 'Enter the code'
enter_data: 'Enter the data'
enter_email: 'Enter your email'
enter_new_lang_code: 'Enter the new language code'
enter_other: 'Enter other'
enter_private_event: 'Entrance to the private event'
enter_your_price: 'Enter your price'
entity: Entity
entry_page: 'Entry point'
entry_point: 'Entry point'
entry_points: 'Entry Points'
envelope: Envelope
epdq_3ds_main: 'Main Window'
epdq_3ds_popup: Pop-up
error: Error
errorcode: 'Code Error'
errorname: 'Name of the error'
error_account_disabled: "Your account is disabled.\_Please contact the shop administrator."
error_account_pending: 'This account is still awaiting validation.'
error_account_remaining_try: 'Remaining attempts before account is locked: remaining_try'
error_account_blocked_mail_subject: 'IMPORTANT - User account locked'
error_account_blocked_mail_body: 'User account number [userId] related to email address [emailAddress] has been deactivated as a consequence of an excess in wrong password submitted. Please, log in your back office to examinate account activity in <a href="[urlToLogs]">authentication</a>.'
error_ip_blocked_mail_subject: 'IMPORTANT - Requests from blocked IP address'
error_ip_blocked_mail_body: 'Unsuccessful connection attempts sent from IP address [ipAddress] have been monitored in excess of authorized limitations of [maxAttempts]. IP Address [ipAddress] has been blocked for a duration of [waitingDuration] seconds. Please, log in your back office to examinate accounts activity in <a href="[urlToLogs]">authentication logs</a>.'
error_admin_cannot_create_b2c_vendor: 'Unable to create a professional sales account as administrator'
error_admin_cannot_create_c2c_product: 'Unable to create a C2C product as an administrator'
error_admin_not_created_email_already_used: "The administrator account was not created.\_The email address corresponds to another user.\_You can add the administrator of this vendor manually."
error_admin_not_created_name_already_used: "The administrator account was not created.\_The email address corresponds to another user.\_You should change the name of the administrator account."
error_hipay_not_created_email_already_used: "The Hipay account was not created.\_The email address corresponds to another user.\_You should change the email of the administrator account."
error_ajax: "Oops, a small problem ([error]).\_Please try again."
error_already_posted: 'You have already posted to this topic'
error_area_access_denied: 'You are not allowed to access this area.'
error_area_access_denied_unknown_group: 'Unable to verify your Google group. Contact the Technical Department to request that you be given the right to read groups.'
error_vendor_access_denied: 'Access denied : you have no vendor associated to your account'
error_bic: 'The entered BIC / SWIFT code is incorrect'
error_company_terms: 'Provided terms are exceeding the maximum length allowed'
error_captcha_required: ' Captcha test validation is mandatory to access the application'
error_chronopost_address: 'An address of the order is not recognized by Chronopost, please check them'
error_chronopost_delete_shipment_already_shipped: 'The package can not be canceled because it was taken care of by Chronopost'
error_confirmation_code_invalid: 'Invalid or missing confirmation code.'
error_creating_mvp_from_product: 'An error occurred while creating the unified product master record.'
error_curl_not_exists: "<b> Warning! </b> <br /> The PHP CURL <b> extension is not </b> supported by your server.\_The <b> [method] </b> will not work.\_Please contact your host."
error_curl_ssl_not_exists: "<b> Warning! </b> <br /> The PHP CURL extension is compiled <b> without </b> SSL support in your server.\_The <b> [method] </b> will not work.\_Please contact your host."
error_currency_exists: 'Currency with the code <b> [code] </b> already exists'
error_delete_theme_company: "Another shop uses this theme.\_Please edit the theme before deleting it."
error_delete_theme_layout: "The layout that this theme uses is the default theme.\_Thanks for putting another layout by default."
error_demo_mode: 'The store is in <b> DEMO </b> mode and this feature is disabled.'
error_email_already_subscribed: 'Your email is already in our database.'
error_empty_company_name: 'The name of the company can not be empty.'
error_no_administrator_name: 'The company should have at least one associated administrator.'
error_exim_cant_open_file: 'Can not open the loaded file'
error_exim_cant_read_file: 'Can not play loaded file'
error_exim_fields_not_selected: 'No field selected for export'
error_exim_file_doesnt_exist: 'The file does not exist'
error_exim_incorrect_lines: 'Your CSV file is formatted incorrectly (incorrect delimiter, missing fields, etc.) to the following lines <b> [lines] </b>'
error_exim_incorrent_delimiter: 'The delimiters in the CSV file are different from the ones you have selected.'
error_exim_invalid_count_langs: 'Multi-language lines do not match: the number of lines for some languages ​​is different.'
error_exim_layout_required_fields: 'The following fields are required and can not be removed from the layout:'
error_exim_no_data_exported: 'No data has been exported'
error_exim_invalid_format_datetime: 'The format of the date not valid ([errorType])'
error_exim_no_file_uploaded: 'No loaded files'
error_exim_pattern_definition_alt_keys: 'Invalid configuration definition: missing alternative key'
error_exim_pattern_definition_references: 'Invalid configuration definition: missing tables reference'
error_exim_pattern_dont_match: 'Your imported file does not match the configuration'
error_exim_pattern_not_found: 'Configuration not found'
error_exim_pattern_required_fields: 'Your imported file MUST have the following fields: <b> [fields] </b>'
error_exim_invalid_fields: 'The following fields are invalid : <b>[fields]<b>'
error_exim_could_not_process_line: "This line could not be processed. Please retry later."
error_giftreg_email_not_found: 'The email address entered was not found in the wish list'
error_gift_cert_code: 'The voucher code entered is invalid.'
error_hash_generation: "Hash generation error.\_Please choose another method of payment."
error_iban: 'The IBAN entered is incorrect'
error_rib_file: 'Missing RIB file'
error_country_code: 'The country code entered is incorrect'
error_images_need_located_root_dir: "Please set the image directory in the root directory of the store.\_Put the images to be imported into this root directory."
error_image_format_not_supported: "The format of the <b> [format] </b> image is not supported by the GD library installed on your server.\_The thumbnail was not created."
error_incorrect_login: 'The username or password entered is invalid.'
error_login_organisation_not_approved: "You can't login because your organization is not approved."
error_invalid_access_key: 'The access key entered is invalid'
error_invalid_emails: 'The following email addresses are invalid: <b> [emails] </b>'
error_lang_code_exists: 'The language code provided ([code]) already exists'
error_login_not_exists: "The username entered does not correspond to any account in our shop.\_Please make sure you have entered the correct username associated with your account."
error_message_not_sent: 'The message can not be sent. <br> Mailer Error:'
error_naf_code: 'Naf Code length can not be greater than 6 characters.'
error_not_logged: 'You must be logged in to access this resource!'
error_no_items_selected: "No item selected!\_At least one box must be checked to perform this action."
error_no_recipient_address: 'Please enter your friend''s email address to continue.'
error_occured: 'Error occurred'
error_occurred: 'Error occurred'
error_password_format_not_valid: "Password security requirements not respected. Password must contain at least 1 number, 1 lowercase character, 1 uppercase character, 1 special character or accented character AND be at least 10 characters long."
error_passwords_dont_match: 'Passwords do not match.'
error_password_content: 'The password must contain both letters and numbers.'
error_password_expired: "Your password has expired.\_You must change it."
error_password_expired_change: "Your password has expired.\_</p> <a href=\"[link]\" class=\"underlined\"> <b> Change Password </b> </a> </p>"
error_password_min_symbols: 'The password must contain at least <b> [number] </b> characters.'
error_password_was_used: "Your new password must not coincide with any of your last four passwords.\_Please use a different password."
error_permissions_not_changed: 'Can not change permissions'
error_status_not_changed: "Error while updating your status.\_The status has not been changed."
error_theme_manifest_missed: "The file describing the theme (\"manifest\") was not found.\_The installation was canceled."
error_user_exists: "The username or email you have chosen already exists.\_If you own the account but do not find the password, you can use the \"forgotten password\" function to re-enable it."
error_validator_color: 'The color code of the <b> [field] </b> field is invalid.'
error_validator_email: 'The email address is invalid.'
error_validator_birthday: 'The birthday is invalid.'
error_validator_integer: "The value of the <b> [field] </b> field is invalid.\_It must be an integer."
error_validator_message: 'The value of the <b> [field] </b> field is invalid.'
error_validator_multiple: 'The <b> [field] </b> field does not contain the selected options'
error_validator_password: 'The <b> [field2] </b> and <b> [field1] </b> field passwords do not match'
error_validator_phone: "The phone number for the <b> [field] </b> field is invalid.\_The expected format is 01 23 45 67 89."
error_validator_phone_number: 'Use the following format for this method of payment: ************.'
error_validator_required: 'The <b> [field] </b> field is required.'
error_validator_zipcode: "The field <b> [field] </b> is incorrect.\_The correct format is [extra]."
error_vendor_cannot_create_b2c_vendor: 'Unable to create a professional sales account as a salesperson'
error_vendor_exists: "The vendor with this email already exists.\_Please try another email."
error_w_legal_status_not_valid: 'The legal status must begin with a letter.'
error_w_siret_number_not_valid: 'The SIRET number must contain 14 digits'
etiquettes_chronopost: 'Chronopost labels'
etiquettes_mondialrelay: 'MondialRelay labels'
event: Event
events: Events
events_list: 'Guest list'
events_menu_description: 'Create new customer events and edit existing ones.'
event_add: 'Add a new event.'
event_fields: 'Custom event fields'
event_notification_subj: 'Notification of Events'
event_type: 'Event type'
exact_phrase: 'The exact phrase'
excellent: Great!
exception: Exception
exceptions: Exceptions
exceptions_invalid_quantity: 'The quantity you entered is invalid.'
exceptions_type: 'Exception type'
exception_disabled: 'No exception was selected'
exception_disregard: 'All exceptions'
exception_error: Error
exception_error_code: 'Error code:'
exception_exist: 'This combination of exception already exists'
exception_title: 'Oops! <br /> We did not find what you were looking for.'
exchange_rate: 'Public exchange rate'
exclude: Exclude
exclude_disabled_products: 'Exclude deactivated products'
exec: Execute
exim_error_structure_csv: 'Csv file is not valid'
exim_error_delimiter_csv: 'The delimiter of the csv file is not valid'
exim_error_empty_column_csv: 'Each header must have a key'
exim_error_invalid_csv: 'Csv file is not valid'
exim_error_not_access_csv: 'The file is not accessible'
exim_error_timeout_read_csv: 'The URL you entered took too long to download'
exim_error_mimetype_csv: 'The mimeType %s is invalid'
exim_error_import_already_exist: 'An import already exists'
exim_error_import_file_without_lines: 'Import does not contain import rows'
exim_error_export_already_exist: 'An export already exists. Please wait for its completion to launch another EMAIL mode export.'
exim_failed_to_process_image: 'Image import failed'
exim_failed_to_process_image_declination: 'Import of declination image failed'
exim_remove_additional_images: 'To erase'
exim_unsupported_image_extension: 'Unsupported Image Extension'
exim_unsupported_image_format: 'Unsupported Image Format'
exim_missing_required_fields: 'Field %s is missing'
exim_category_delete: 'Delete category %'
exim_category_update: 'Update category %'
exim_category_create: 'Create category %'
exim_category_create_failed: 'Can not create category %'
exim_category_image_warning: 'Error on image: %'
exim_attributes_delete: 'Delete attribute %'
exim_attributes_ignored: 'Ignored bad format for attribute %s'
exim_attributes_update: 'Update attribute %'
exim_attributes_create: 'Create attribute %'
exim_attributes_create_failed: 'Can not create attribute %'
exim_variants_delete: 'Delete variant %'
exim_variants_update: 'Update variant %'
exim_variants_create: 'Create variant %'
exim_variants_create_failed: 'Can not create variants %'
exim_link_categories_update: 'Update the category binding with %'
exim_link_categories_create: 'Creating the category link with %'
exim_link_categories_create_failed: 'Can not create link for category %'
exim_translations_create: 'Create translation %'
exim_translations_create_failed: 'Can not create translation %'
exim_translations_create_failed_local: 'Can not create translation, language not found %'
exim_product_update: 'Update product %'
exim_product_create: 'Create product %'
exim_product_create_failed: 'Can not create product %'
exim_product_invalid_max_price_adjustment: 'Max price adjustment must be an integer between 0 and 100 (or empty)'
exim_product_not_match_category: 'The product is not attached to any category'
exim_product_video_failed: 'Can not upload the video %'
exim_product_declination_create: 'Create product declination'
exim_product_unable_retrieve_shipping: 'Unable to retrieve shipping informations'
exim_product_attachments_failed: 'Can not upload the attachments'
exim_product_not_match_template: 'The template % does not exist.'
exim_product_price_update: 'Update product price %'
exim_product_declination_price_update: 'Update product declination price %'
exim_product_quantities_update: 'Update product quantity %'
exim_product_quantities_price_update: 'Update product declination quantity %'
exim_field_value_not_match_template: 'Submitted data does not match expected values. Refer to documentation.'
exim_save_price_tiers_fail: 'Unable to save price tiers.'
exim_save_price_tiers_no_feature_flag: 'The Price tiers column is not allowed. Its content was not integrated.'
exim_wrong_product_declination: 'Wrong declination for your vendor.'
exim_price_tiers_and_price: 'Inconsistency of price setting. Data "price" not integrated.'
exim_jobs: 'Reports'
exim_jobs_import: 'Imports'
exim_jobs_import_title: 'Import reports'
exim_jobs_export: 'Exports'
exim_jobs_export_title: 'Export reports'
exim_jobs_processed: 'Processed'
exim_jobs_created: 'Posted'
exim_jobs_started: 'Started'
exim_jobs_finished: 'Ended'
exim_jobs_waiting: 'Waiting'
exim_related_product_update: 'Update'
exim_related_product_create: 'Create'
exim_related_product_delete: 'Delete'
exim_related_product_delete_not_found: 'No related product corresponding to the criteria: [criteria]'
exim_related_product_error_invalid_fields: 'Value "[value]" is not valid for [name]. Accepted values: [accepted]'
exim_related_product_error_not_found_company_id: 'The company id "[company_id]" doesn''t exist'
exim_related_product_error_not_found_product_code: 'The product code "[product_code]" doesn''t exist for company id "[company_id]"'
exim_related_product_error_not_found_related_product: 'The related product "[product_code]" doesn''t exist for related company id "[company_id]"'
exim_related_product_error_required_field: 'Mandatory field: %'
exim_related_product_error_external_company: 'You cannot add products from other companies'
exim_related_product_error_same_products: 'Parent and related products must be different'
exit_point: 'Release point'
exit_points: 'Output Points'
exim_missing_divisions: 'Both included and excluded divisions shall be defined.'
exim_empty_included_divisions: 'No included division has been bound to the product.'
exim_invalid_included_divisions: 'Defined included divisions are invalid.'
exim_invalid_excluded_divisions: 'Defined excluded divisions are invalid.'
product_attributes: 'Products attributes'
exim_product_attributes_create: 'Create'
exim_product_attributes_delete: 'Delete'
exim_product_attributes_error_incoherency: 'A free attribute cannot have an attribute id'
exim_product_attributes_error_invalid_fields: '"[value]" not valid for [name]. Possible values: [accepted]'
exim_product_attributes_error_invalid_id: 'Attribute Name or Id required'
exim_product_attributes_error_invalid_value: '"[value]" not valid for [name]'
exim_product_attributes_error_not_found_attribute: Attribute "[name]" not found, inactive or does not exist for the category of the product "[product_code]"
exim_product_attributes_error_not_found_company_id: 'Company id "[company_id]" not found'
exim_product_attributes_error_not_found_product_code: 'Product code "[product_code]" not found for company id "[company_id]"'
exim_product_attributes_error_required_field: 'Mandatory field: %'
exim_product_attributes_error_too_much_values: 'Attribute "[name]" can only have one value'
exp: Expand
expanded: 'To expand'
expand_collapse_list: 'Expand / collapse list of items'
expand_sublist_of_items: 'Expand sub-list of items'
expenditure: Expenses
expired: Expired
expirepreauth: Pre-authorization
expirepreauth_description: "Pre-authorization is limited in the day.\_The maximum number is 30 days.\_With a \"Sale\" request, leave the field blank."
expiry_date: 'Expiration date'
export: Export
exported_fields: 'Exported Fields'
exported_files: 'Exported file (s)'
exported_items: 'Exported items'
exporting_data: 'Exported data'
export_by_cron: 'Uploaded by cron'
export_by_cron_to: 'Exported by cron to'
export_cron_hint: 'Use the following line to start the script according to the schedule.'
export_data: Export
export_locations: 'Exporting situations'
export_options: 'Export options'
export_selected: 'Export Selection'
export_to_ftp: 'FTP server options'
export_to_server: 'Export to server'
export_product_attributes: It is advisable to export the attributes per company if the export does not work for the whole marketplace.
extended: Elongate
external_reference: 'External reference'
extra: 'Supplement (s)'
extra_services: 'Additional Services'
facebook: Facebook
facebook_link: 'Https://www.facebook.com/'
facebook_obj_type: 'Facebook object type'
fail: Failure
failed: Failure
fair: Just
'false': Fake
faroese: Faroese
fashion_avenue: Fashion
fast: Fast
favorite_already_exist: 'The product is already in your favorites'
favorite_deleted: 'The product has been removed from your favorites'
fax: Fax
fb_activities: 'Things to Do'
fb_businesses: Business
fb_groups: Groups
fb_organizations: Organizations
fb_people: Public
fb_places: Places
fb_products_entertainment: 'Products and Entertainment'
fb_websites: 'Web sites'
feature: Attributes
feature.approve_user_by_admin: 'Users must be approved by the administrator'
feature.organisations_enabled: 'Organisations are enabled'
feature.cache_http: 'HTTP cache is enabled on API'
feature.metrics.enable: 'Metrics are enabled'
feature.allow_files_on_non_edp_products: 'Allow files to be added to non-downloadable products'
feature.marketplace_only_sell_services: 'Marketplace only sells services'
feature.carrier.chronopost.chrono13: 'Chronopost delivery mode'
feature.carrier.chronopost.chronorelais: 'Delivery mode Chrono relais'
feature.carrier.mondial_relay: 'Delivery mode Mondial Relay'
feature.enable_product_attachments: 'Product attachments tab is visible'
feature.checkout.accept_terms_and_conditions: 'Customers of the marketplace must accept the conditions when validating an order'
feature.commission.include_shipping_in_commission: 'Include delivery charges in commission'
feature.create_account.must_accept_terms: 'Marketplace customers must accept the conditions when creating an account'
feature.create_legal_wallets_for_customers: 'Creation of virtual wallets for legal entities (B2B)'
feature.enable_c2c: 'Enabling C2C mode'
feature.enable_companies_facet: 'Activating Merchant Facet'
feature.enable_company_type_facet: 'Activating the merchant type facet (Pro / private)'
feature.enable_ecommerce_analytics: 'Generation of orders sent to Google Analytics'
feature.enable_yavin: 'Enable Yavin'
feature.green_tax_is_enabled: 'Activation of eco-participation'
feature.multi_vendor_product: 'Enabling Unified Product Sheets'
feature.multi_vendor_product_auto_create_if_no_match: 'Creating a unified form from a product if none is found during automatic binding'
feature.multi_vendor_product_auto_link: 'Automatic linking of products with unified plugs'
feature.pim_sync_product_from_mvp: 'Synchronization of products from unified cards'
feature.sandbox: Sandbox
feature.activate_billing_number_auto_generation: 'Automatic billing number generation for vendors'
feature.activate_workflow_translation: 'Activating status translations from the workflow'
feature.order_adjustment: 'Order adjustment'
feature.marketplace_discounts: 'Activating marketplace discounts'
feature.subscription: 'Activating subscriptions'
feature.sso_connection_only_bo: 'Activating login with SSO only'
features: Attributes
features_delimiter: 'Attribute delimiter'
features_import_warning: "The \"Feature ID\" field must be present and valid even for the creation of attributes.\_It is therefore necessary to inject unused ids."
features_menu_description: 'See attribute and attribute values ​​that can be linked to your products'
feature_code: 'Attribute code'
feature_display_on_faceting: 'View in facets'
feature_display_on_product: 'Show in Attributes Table'
feature_flags: Options
feature_flags_helper: 'The list below shows the status of the activated or not enabled configurations for the marketplace. Their change requires technical intervention.'
feature_flags_title: 'Options de la marketplace'
feature_is_editable_only_by_admin: 'Feature editable only by the administrator'
feature_is_required: 'The attribute is mandatory for private vendors'
feature_is_searchable: 'Indexed in the search'
feature_is_used_for_recommendation: 'Used for recommendations'
fedex: Fedex
feedback_is_sent_successfully: 'The feedback was sent successfully.'
feedback_values: 'Value of feedback'
feed_description: 'Description of the flow'
feed_title: 'Title of the feed'
field: Fields
fields: Fields
fieldset_general: General
fieldset_optional: Optionnal
field_editor: 'Field editor'
field_name: 'Field name'
field_type: 'Field Type'
file: File
filename: 'File name'
files: Files
filesize: 'File size'
files_changes: 'File changes'
files_directory: 'File folder'
file_avail_after_payment: 'This file will be available after the order is paid in full.'
file_browser: 'File Browser'
file_doesnt_have_key: 'This file does not have a download key.'
file_download_limit_exceeded: 'The file can not be downloaded because the download limit equal to [limit] is exceeded.'
file_tree: 'Tree of files'
filling: Filling
filter: Filtered
filters: Filters
filters_menu_description: 'Product filters are displayed in the front and allow customers to find products faster'
filter_by: 'filter by'
filter_description: 'Filter description'
filter_name: 'Name of Filter'
finance_product_code: 'Product Finance Code'
financial_state: 'State of Finance'
find_results_with: 'Finding results with'
finish: End
finished: Completed
finnish: End
first_name: 'First name'
fixed_discount: 'Fixed discount'
max_discount: 'MAX'
flemish: Flemish
folder: Folder
folder_is_empty: 'Empty folder'
footer: Low
footer_container_not_used: "This container is NOT used.\_The footer container of the default location should be used.\_Place this location as \"default\" to use this container as footer for all placements."
for: of
forbidden: Forbidden
forbidden_combinations: 'Allow used combinations'
forgot_password_question: 'Forgot your password ?'
form: Form
format: Format
forms: Forms
form_builder: 'Forms Generator'
form_id: 'Form ID'
form_is_secure: 'The form is secure (SSL)'
form_submit_text: 'Form submission text'
for_all_found_orders: 'For orders for ALL pages'
for_this_page_orders: 'For orders on THIS page'
found_products: 'Products found'
fragile: Fragile
free: Free
free_features: 'Free Attributes'
free_features_help: "You can freely add features, they will be displayed in the data sheet of the product in the form <i> Name: Value </i> (example: <i> Material: wood </i>).\_If several characteristics have the same name, the values ​​will be displayed as a list."
free_products: 'Free products'
free_shipping: 'Free Shipping'
carriage_paid: 'Carriage Paid'
french: French
from: of
from_date: 'Date of'
from_email: 'From email'
from_name: Name
ftp: FTP
ftp_connection_problem: "Unable to connect to the FTP server.\_Please check the FTP connection information"
ftp_pass: 'FTP Password'
ftp_url: 'FTP Server'
ftp_url_hint: "The format is: FTPHOST [: PORT] [/ DIRECTORY].\_Example: ftp.yourhost.com:21/home/<USER>/folder"
ftp_user: 'FTP User Name'
fulfillment_state: 'State of execution'
full: Full
fullauth: 'Full authorization'
full_description: 'Full description'
full_list: 'Complete list'
full_tree_cat: 'Complete shaft'
full_tree_pages: 'Complete shaft'
full_width: Width
function: 'Body of function'
funny_store: 'Funny Store'
gateway: Bridge
gateways: Footbridges
gc_auto_charge: 'Enable automatic loading'
general: General
general_info: 'General information'
general_settings: 'general settings'
general_statistics: 'General Statistics'
generate: Create
generate_etiquette_chronopost: 'Generate a Chronopost label'
generate_etiquette_mondialrelay: 'Generate a MondialRelay label'
generate_submenu: 'Create submenu'
generating_pdf: "Creating the PDF.\_Please wait ..."
generating_xls: "Creating the XLS.\_Please wait ..."
geography: Geography
german: German
get_access_key: 'Get the access key'
get_access_key_title: 'Get the access key'
get_api_key: 'Get the API key'
get_rates: 'Get Rates'
giftreg: 'Gift List'
giftregistry_key: 'Gift List Key'
gift_add_products: 'Add products as a gift'
gift_certificate: 'Gift Certificates'
gift_certificates: 'Gift Certificates'
gift_certificates_menu_description: 'View and manage gift certificates that customers can use'
gift_certificates_verify: 'Gift Certificate Verification'
gift_certificate_added_to_cart: 'The gift voucher was added to your shopping cart'
gift_certificate_info: 'Gift Certificate Information'
gift_certificate_status: 'Gift Voucher Status'
gift_certificate_statuses: 'Articles of gift certificates'
gift_certificate_verification: 'Gift Certificate Verification'
gift_cert_amount_changed: 'Amount of gift voucher changed'
gift_cert_code: 'Gift coupon code'
gift_cert_debit: 'Debit balance'
gift_cert_error_amount: 'The amount can not be greater than [max] and less than [min]'
gift_cert_from: Of
gift_cert_to: AT
gift_comment: Message
gift_registry: 'Wish list'
gift_registry_key: 'Access key of the wish list'
give_coupon_subj: 'You have a new promotion'
global: Global
global_options: 'Global options'
global_status: 'Global statutes'
global_update: 'Global Update'
global_update_description: 'Positive or negative values ​​can be entered in the fields below (eg -5)'
go: Go
google: Google
google_base: 'Google Base'
google_base_export_notice: '<b> Note: </b> You can install tax and shipping rates on the <a href="http://www.google.com/merchants/taxshippingsettings"> Tax and Transportation page </a> page Your Google Vendor account'
google_callback_url: 'Google callback URL'
google_info: 'Google Information'
google_request_sent: 'The "[action]" request was sent successfully'
goto_theme_configuration: 'Topics loaded'
go_affiliate_link: 'Visit the site'
go_back: 'Go back'
go_to_the_admin_homepage: 'Go to the administration page'
go_to_the_homepage: 'Back to the homepage'
gplus_link: 'Https://plus.google.com/'
grams_in_the_unit_of_weight: 'Grams in the weight unit defined by the weight symbol'
graphic: Graphic
graphic_banner: 'Graphic Banner'
graphic_banners: 'Graphic Banners'
greek: Greek
green_tax_included: 'Including eco-tax [tax]'
grid: 'Wire rack'
grid2: Grille2
grid_options: 'Grid options'
gross_total: 'Gross total'
group: Group
groups: Groups
guest: Guest
hand_delivery_date: 'Date of hand-delivery'
has_credit_memo: 'Memo balance available'
has_invoice: 'Invoice available'
header: 'On your mind'
header_container_not_used: "This container is not used.\_The Header Container header will be used.\_Set this location as the default location to use this container as the header for all placements."
header_image: 'Image of the header'
hebrew: Hebrew
height: Height
hello: Hello
salutation: Hello,
help: Help
help_and_support: 'Support'
hidden: Hidden
hidden_categories: Categories
hide: Mask
hide_add_to_cart_button: 'Hidden Add button to cart'
hide_options: 'Masking options'
hide_option_completely: 'Hidden option completely'
hidpi: HiDPI
hipay_transfer_com_done: 'Commission transferred'
hipay_transfer_done: 'Hipay: transfer done'
hipay_transfer_vendor_done: 'Funds to the seller via Hipay'
history: Historical
home: Home
homepage: Home
home_page: Home
home_sweet_home: 'home Sweet Home'
horizontal: Horizontal
host: Host
host_code: 'Host code'
hour: Hour
hourly: Timetable
hours: Hours
how_to_send: 'How to send?'
html_block: 'HTML block'
hungarian: Hungarian
hybrid_auth_cant_create_profile: "Unfortunately, we can not create your profile for any of the following reasons: <br /> - You have logged into our shop previously using another OpenID provided with the same email used. <br /> - You have already registered in The store using your email.\_<br /> Please contact the administrator to resolve this issue."
hybrid_auth_configuration_error: 'Hybrid Authentication Configuration Error.'
hybrid_auth_connecting_provider: "We contact <b> [provider] </b>.\_Please wait ..."
hybrid_auth_failed_auth: "Authentication Failure.\_The user has canceled the authentication or the provider has refused the connection."
hybrid_auth_missing_credentials: 'Missing Application Provider Accounts'
hybrid_auth_need_update_profile: "The \"Password\" and other mandatory fields are empty at the moment.\_Please update your account."
hybrid_auth_provider_error_configuration: 'Supplier Not Configured Correctly'
hybrid_auth_social_login: 'Or registered with another vendor identity.'
hybrid_auth_unspecified_error: 'Non-specific error'
hybrid_auth_wrong_provider: 'Provider unknown or disabled'
i25: "Intervals 2 of 5 is based on standard 2 of 5 symbology.\_<br/> Intervals 2 of 5 is designed to encode only 10 digits."
iban: IBAN
icon: Picture
id: ID
identifier: 'PayBox ID'
iframe_mode: 'IFrame Mode'
illegal_item_weight: 'Weight of the illegal article'
image: Picture
images: Images
images_directory: 'Images folder'
image_pair: 'Related Image (s)'
image_verification: 'Image Verification'
image_verification_body: "To ensure that a person, not an automated program, fills this form, please type the characters you see in this picture.\_All letters will be displayed in their capital letters."
image_verification_label: 'Anti-bot validation'
immediately: 'At once'
import: Import
important: Important
importing_data: 'Importing Data'
import_data: Import
import_locations: 'Import Locations'
import_options: 'Import options'
import_product_attributes: It is recommended to have smaller files.
in: In
included: included
include_cvn: 'Include CVN'
including_discount: 'Discount included'
including_tax: 'Tax included'
income: Receipts
incompleted: Incomplete
incompleted_orders: 'Incomplete orders'
incorrect_filling_message: 'Incorrect message'
incorrect_price_warning: "The product has not been added to your cart.\_Thank you for reviewing the prize."
increase: Increase
inc_tax: 'taxes included'
index: Home
infinite_stock: Infinite stock
information: Information
inner_hint: 'Internal Tips'
input_field: 'Input fields'
insert_block: 'Add a Block'
insert_grid: 'Add a grid'
install: Install
installation_id: 'Installation ID'
installed: Installed
installed_languages: Installed
installed_upgrades: 'Installed Updates'
install_themes: 'Install themes'
'internal server error': 'Internal Server Error'
international_settings: 'International Settings'
international_usps: 'USPS International'
invalid_csrf_token: 'The action could not be performed because it expired, please try again.'
inventory: Stock
invitees: Guests
invoice: Bill
invoices: Invoices
invoice_credit_memo: 'Invoices / Memo balance'
invoice_id: 'ID Invoice'
invoice_date: Invoice date
invoice_title: BILL
in_progress: 'In progress'
in_stock: 'In stock'
ip: IP
ips: IPs
ip_address: 'IP adress'
ip_addresses: 'IP Addresses'
ip_from: 'IP of'
ip_to: 'IP to'
issuer: Distributor
issuer_info: 'Distributor Information'
is_back_in_stock: 'Back in stock!'
is_pbp: 'Allow payment by points'
italian: Italian
item: 'item'
items: Articles
items_dependences: 'Depending on the items'
items_in_box: 'Number of items in one package'
items_in_cart: 'You have %count% item in your shopping cart | You have %count% items in your cart'
items_marked_by_tag: 'Articles tagged with "[tag]":'
items_per_page: 'Elements per page'
items_title: Elements
item_number: 'Show article number'
item_quantity: 'Quantity of items'
it_will_overwrite_existing_values: 'This will overwrite the existing values'
janrain_cant_create_profile: 'Unfortunately, the profile could not be created for one of the following reasons: <br /> - You previously logged in with another OpenID provider using the same email address <br /> - You are already registered with the same Email address <br /> Please contact the administrator to resolve this issue'
janrain_general_info: '<p> Keep Values ​​and Configure Login <a href="http://janrain.com" target="_blank"> janrain.com </a> </p>'
janrain_need_update_profile: "The password and other mandatory fields are empty at the moment.\_Please update your profile."
janrain_social_login: 'Social login'
japanese: Japanese
join: 'To assemble'
json_error_ctrl_char: 'Error checking characters, possibly badly encoded.'
json_error_depth: 'The maximum "stack depth" has been exceeded'
json_error_state_mismatch: 'Invalid or malformed JSON'
json_error_syntax: 'JSON syntax or malformed error'
json_error_unknown: 'JSON error unknown'
json_error_utf8: 'UTF-8 characters malformed, possibly badly encoded.'
key: Key
key1_for_md5: 'Authorization key 1 for md5'
key2_for_md5: 'Authorization key 2 for md5'
keywords: Keywords
korean: Korean
label_text_color: 'Label color'
landing_header: 'Your order has been received'
language: Language
languagecode: 'Code Language'
languages: Languages
languages_translations_helper: 'The texts (or translations) below are used in the design of your marketplace. To edit text, search for it, then change its value and save. The text will be immediately visible on your marketplace.'
languages_translations_title: 'TEXTS OF YOUR MARKETPLACE'
language_code: 'Language code'
language_variable: 'Language Variable'
language_variables: 'Language variables'
last4ssn: 'The last 4 characters of your social security number'
last_24hours: 'Last 24 Hours'
last_days: 'Last days'
last_month: 'Last month'
last_name: Name
last_n_days: 'Last [N] days'
last_order: 'Last order'
latest_reviews: 'Latest reviews and reviews'
latitude: Latitude
latitude_short: Lat
layout: Layout
layouts: Layouts
lbl_amazon_aws_access_public_key: 'AWS public key'
lbl_amazon_aws_access_secret_key: 'AWS secret key'
lbl_amazon_background_color: 'Background color'
lbl_amazon_button_color: 'Button Color'
lbl_amazon_button_size: 'Button Size'
lbl_amazon_button_style: 'Amazon button style'
lbl_amazon_color_dark: Dark
lbl_amazon_color_light: Clear
lbl_amazon_color_orange: Orange
lbl_amazon_color_tan: Brown
lbl_amazon_color_white: White
lbl_amazon_process_order_on_failure: 'Command process on failed callback'
lbl_amazon_size_large: L
lbl_amazon_size_medium: M
lbl_amazon_size_xlarge: XL
lbl_be2bill_proxy: Proxy2Bill
left: Left
legal_payment_obligation: 'Order with obligation to pay.'
legal_representative: 'Legal representative'
legal_representative_firstname: 'First name of legal representative'
legal_representative_lastname: 'Name of legal representative'
legal_representative_address: 'Address of legal representative'
lemonway_address: '14 Rue de la Beaune, 93100 Montreuil Phone: Display number'
lemonway_bank_transfer: 'Bank transfer Lemonway'
lemonway_bank_transfer_require_check: 'Manual Action Required'
lemonway_bic: BNPAFRPPIFE
lemonway_iban: 'FR76 3000 4025 1100 0111 8625 268'
lemonway_manual_check_failed: 'Lemonway error, unable to save transaction'
lemonway_manual_commission_taken: 'Commission transferred to wallet SC'
lemonway_rib: '30004 02511 ***********'
lemonway_social_name: Lemonway
lemonway_transaction_id: 'Transaction ID Lemonway'
lemonway_bankwire: 'Lemonway bank wire'
lemonway_transfer_done: 'Funds to the seller via Lemonway'
lemonway_checkout_service_unavailable: 'Payment service unavailable (Code [code])'
lemonway_checkout_amount_unauthorised: 'Error : Amount unauthorised (Code [code])'
lemonway_checkout_payment_service_error: 'Payment service error (Code [code])'
lemonway_kyc_too_high: 'This merchant has been validated by Lemonway (KYC2), please contact Lemonway support to update wallet data.'
smoney_transaction_id: 'Transaction ID SMoney'
smoney_payout_done: 'SMoney: Payout done'
smoney_callback_url_text: 'Callback URL to define in SMoney back office for credit card payments'
hipay_callback_url_text: 'Callback URL to define in HiPay back office for credit card payments'
hipay_sepa_callback_url_text: 'Callback URL'
hipay_sepa_callback_url_warning_text: 'No setting needed in Hipay''s back office, callback URL is defined as parameter of payment requests.'
stripe_callback_url_text: 'Callback URL to define in <a href="https://dashboard.stripe.com/account/webhooks" target="_blank">Stripe back office</a> for Charge events'
lemonway_sepa_callback_url_text: 'Callback URL'
lemonway_callback_url_text: 'Callback URL to define in Lemonway back office for bankwire payments (MoneyIn: by wire received).'
length: Length
less: Less
less_filters: 'Less filters'
letter: Letter
level: Level
licence_agreement: 'License agreement'
license_agreement: 'License agreement'
license_number: 'License number'
license_number_cannot_be_empty: 'The license number can not be empty'
limit: Limit
line: Line
linear: Linear
link: Link
linkedin_link: 'Https://en.linkedin.com/'
linked_categories: 'Related categories'
linked_products: 'Related Links'
links: Connections
links_thumb: 'Link bar'
link_message_for_test_letter: 'The link will be posted in the newsletter'
link_new_affiliate: 'Link to add a new partner'
link_text: 'Link Text'
link_to: 'link to'
list: Listing
list_objects: 'List of objects'
list_of_event_invitees: 'List of guests of the event'
list_price: 'Public price'
list_price_decreased: 'Price list has been decreased'
list_price_increased: 'Price list has been increased'
list_price_short: 'List of prices'
list_quantity_count: 'Number of items in selection'
list_without_options: 'List without option'
live: Production
live_preview: Preview
load: Load
loading: Loading...
local: Local
localization: Department
localizations: Departments
localizations_menu_description: 'Manage Departments'
local_export: 'Link to'
location: Destination
locations: 'Belfast hotels'
locations_menu_description: 'Manage your store''s enabled destinations'
log: 'Sign In'
loggedin_time: 'Connected in Time'
logging: 'Sign In'
login: 'Sign In'
logos: Logos
logo_link: 'Path (URL) of the logo'
logs: Logs
log_action_backup: 'Backup logs'
log_action_create: Create
log_action_delete: Remove
log_action_deprecated: Depreciate
log_action_error: 'Error log'
log_action_failed_login: 'Connection Failure'
log_action_http: 'Http / https request'
log_action_import: Import
log_action_login: 'Sign In'
log_action_low_stock: 'Low Stock'
log_action_optimize: Optimize
log_action_progress_fail: 'Follow-up error'
log_action_progress_finaliz: Success
log_action_progress_finalize: Success
log_action_restore: Restore
log_action_runtime: 'Time counting'
log_action_session: Session
log_action_status: 'Status change'
log_action_update: Update
log_type_async: 'Asynchronous task'
log_type_categories: Categories
log_type_csv: CSV
log_type_database: Database
log_type_general: General
log_type_news: News
log_type_orders: Orders
log_type_products: Products
log_type_requests: Inquiries
log_type_users: Users
longitude: Longitude
longitude_short: long.
low_stock_subj: 'Alert threshold in stock for "[product]"'
mac_key: 'Mac Key'
mail: Home
mailing_list: 'Email List'
mailing_lists: 'Email Lists'
mailing_lists_menu_description: 'Description of the email list'
mail_message: 'Message from the mail'
mail_subject: 'Subject of the mail'
mail_variables: 'Email variables'
main: Main
mainbox_general: 'mainbox general'
mainbox_simple: 'Main Single Box'
maintenance: Maintenance
main_category: 'Main Category'
make_permanent: 'Make permanent'
manage: Management
manage_addons: 'Management of modules (Add-ons)'
manage_banners: 'Banners management'
manage_blocks: 'Management of blocks'
manage_categories: 'Category Management'
manage_existing_block: 'Management of Existing Blocks'
manage_items: 'Manage items'
manage_languages: 'Language Management'
manage_menus: 'Menu management'
manage_pages: 'Manage pages'
manage_products: 'Product Management'
manage_reports: 'Report Management'
manage_shippings: 'Management of delivery methods'
manage_sitemap: 'Site Map Management'
manage_stores: 'Shop Management'
manage_vendors: 'Vendors management'
mandatory: Required
mangopay_bankwire: 'Virement bancaire Mangopay'
mangopay_transaction_id: 'Transaction ID Mangopay'
mangopay_transfer_done: 'Funds to the seller via Mangopay'
manually: Manually
manually_recalculate_order: 'Recalculate commands manually'
manually_set_tax_rates: 'Set the tax rates manually'
map_fields: 'Definition of fields'
map_type_control: 'MapType control'
mark: 'Marked as resolved'
mark_refund_as_paid: 'Mark as paid'
mark_refund_offline: 'Refund offline'
marketing: Marketing
maximum: Maximum
maximum_per_item: 'Maximum per item'
maximum_items_in_box: 'Maximum choice in the box'
max_amount: 'Total cost'
max_box_weight: 'Maximum weight of the box'
max_downloads: 'Maximum Download'
max_item: 'Number of items'
max_order_qty: 'Maximum order quantity'
max_price_adjustment: 'Maximum price adjustment (%)'
max_uploading_file_size: 'Maximum file size to load'
max_uploading_file_size_hint: 'Leave this field empty to allow file loading of all sizes or limit the file size ("100" equals 100 Kb)'
mb_transaction_id: 'Mainbox General'
md5_checksum_failed: 'MD5 checksum Failure'
md5_hash_value: 'MD5 hash value'
menu: Menu
menus: Menu
menus_menu_description: 'Create and modify footer menus'
menu_i18n: Internationalisation
menu_items: 'Items for the menu'
merchantid: 'Vendor ID'
merchant_email: 'Email of Vendor Notification'
merchant_firstname: 'First name of the dealer'
merchant_id: 'Vendor ID'
merchant_key: 'Vendor Key'
merchant_lastname: 'Vendor Name'
merchant_login: 'Vendor Login'
merchant_name: 'Vendor Name'
merchant_pin: 'PIN Vendor'
merchant_response_was_not_received: "No reply received from vendor.\_Please check the transaction manually."
merchant_site_id: 'Vendor Site ID'
merchant_warrior_api_key: 'API Key'
merchant_warrior_api_passphrase: 'API Passphrase'
merge: Merge
merge_vendor: 'Merge vendors'
message: Message
message_of: 'Message of '
messages: Posts
meta_description: 'META Description '
meta_description_content: ''
meta_keywords: 'META Keywords'
meta_keywords_content: ''
meta_title: 'META Title'
meta_title_content: ''
method: Method
minimized: Reduce
minimum_commission_payment: 'Minimum commission for payment'
minimum_items_in_box: 'Minimum items in the box'
minutes: minutes
min_order_qty: 'Minimum order quantity'
missing_payment: 'Payment is required'
missing_required_field: 'Some fields are not properly filled.'
missing_variants_handling: 'Treatment of Missing Variants'
mobile: Mobile
moderation: Moderation
moderation_options: Moderation options
moderation_products: Moderation products
modifier: Modifier
modify_product_quantity_in_basket: 'The quantity of a product has been modified in the basket'
modify_profile: 'modify the profile'
modify_selected: 'Edit Selection'
modify_shipping_group_in_basket: 'The means of delivery have been modified in the basket'
mondial_relay.error.1: Incorrect merchant
mondial_relay.error.2: Merchant number empty
mondial_relay.error.3: Incorrect merchant account number
mondial_relay.error.5: Incorrect Merchant shipment reference
mondial_relay.error.7: Incorrect Consignee reference
mondial_relay.error.8: Incorrect password or hash
mondial_relay.error.9: Unknown or not unique city
mondial_relay.error.10: Incorrect type of collection
mondial_relay.error.11: Point Relais collection number incorrect
mondial_relay.error.12: Point Relais collection country.incorrect
mondial_relay.error.13: Incorrect type of delivery
mondial_relay.error.14: Incorrect delivery Point Relais number
mondial_relay.error.15: Point Relais delivery country incorrect
mondial_relay.error.20: Incorrect parcel weight
mondial_relay.error.21: Incorrect developped lenght (length + height)
mondial_relay.error.22: Incorrect parcel size
mondial_relay.error.24: Incorrect shipment number
mondial_relay.error.26: Incorrect assembly time
mondial_relay.error.27: Incorrect mode of collection or delivery
mondial_relay.error.28: Incorrect mode of collection
mondial_relay.error.29: Incorrect mode of delivery
mondial_relay.error.30: Incorrect address (L1)
mondial_relay.error.31: Incorrect address (L2)
mondial_relay.error.33: Incorrect address (L3)
mondial_relay.error.34: Incorrect address (L4)
mondial_relay.error.35: Incorrect city
mondial_relay.error.36: Incorrect postcode
mondial_relay.error.37: Incorrect country
mondial_relay.error.38: Incorrect phone number
mondial_relay.error.39: Incorrect e-mail
mondial_relay.error.40: Missing parameters
mondial_relay.error.42: Incorrect COD value
mondial_relay.error.43: Incorrect COD currency
mondial_relay.error.44: Incorrect shipment value
mondial_relay.error.45: Incorrect shipment value currency
mondial_relay.error.46: End of shipments number range reached
mondial_relay.error.47: Incorrect number of parcels
mondial_relay.error.48: Multi-Parcel not permitted at Point Relais®
mondial_relay.error.49: Incorrect action
mondial_relay.error.60: Incorrect text field (this error code has no impact)
mondial_relay.error.61: Incorrect notification request
mondial_relay.error.62: Incorrect extra delivery information
mondial_relay.error.63: Incorrect insurance
mondial_relay.error.64: Incorrect assembly time
mondial_relay.error.65: Incorrect appointement
mondial_relay.error.66: Incorrect take back
mondial_relay.error.67: Incorrect latitude
mondial_relay.error.68: Incorrect longitude
mondial_relay.error.69: Incorrect merchant code
mondial_relay.error.70: Incorrect Point Relais number
mondial_relay.error.71: Incorrect point of sale
mondial_relay.error.74: Incorrect language
mondial_relay.error.78: Incorrect country of collection
mondial_relay.error.79: Incorrect country of delivery
mondial_relay.error.80: Recorded parcel
mondial_relay.error.81: Parcel in process at Mondial Relay
mondial_relay.error.82: Delivered parcel
mondial_relay.error.83: Anomaly
mondial_relay.error.84: Reserved tracking code
mondial_relay.error.85: Reserved tracking code
mondial_relay.error.86: Reserved tracking code
mondial_relay.error.87: Reserved tracking code
mondial_relay.error.88: Reserved tracking code
mondial_relay.error.89: Reserved tracking code
mondial_relay.error.92: The Point Relais country code and the consignee’s country code are different.
mondial_relay.error.93: No information given by the sorting plan
mondial_relay.error.94: Unknown parcel
mondial_relay.error.95: Merchant account not activated
mondial_relay.error.97: Incorrect security key
mondial_relay.error.98: Service unavailable
mondial_relay.error.99: Service unavailable
month: Month
monthly: Monthly
months: month
month_name_1: January
month_name_10: October
month_name_11: November
month_name_12: December
month_name_2: February
month_name_3: March
month_name_4: April
month_name_5: May
month_name_6: June
month_name_7: July
month_name_8: August
month_name_9: September
month_name_abr_1: Jan
month_name_abr_10: Oct
month_name_abr_11: Nov
month_name_abr_12: Dec
month_name_abr_2: Feb
month_name_abr_3: March
month_name_abr_4: Apr
month_name_abr_5: May
month_name_abr_6: June
month_name_abr_7: Jul
month_name_abr_8: Aug
month_name_abr_9: Sept
more: More
more_filters: 'More filters'
more_info: 'More information'
more_link: '[more]'
more_sign_in_options: 'Back for more options'
more_subjects: 'Other topics (one per line. The system will choose a topic randomly for each email sent)'
more_than: 'More than'
more_w_ellipsis: 'More ...'
most_popular: 'Most popular'
most_popular_addons: 'Most Popular Module'
mr: Mr
ms: Ms
multicolumns_small: 'Small multi-columns'
multicolumn_list: 'Multi-column list'
multiple: Multiple
multiple_checkboxes: 'Multiple check boxes'
multiple_selectbox: 'Multiple Selection Lists'
multiple_selectbox_notice: "<p style = \"font-size: 10px;\"> To select more than one entry, left-click with the mouse while holding down the CTRL key.\_To deselect an item, left-click the mouse while holding down the CTRL key. </p>"
multiupload: 'Multiple Loading'
multi_tier_affiliates: 'Multi-Third-Party Affiliates'
multi_vendor_product: 'Unified Product Sheet'
multi_vendor_products: 'Unified Product Sheets'
multi_vendor_products_menu_description: 'View and edit Unified Product Sheets'
multi_vendor_product_empty_list: 'There are no records yet.'
multi_vendor_product_links: 'Products associated with the Unified Product Data Sheet'
multi_vendor_product_links_empty_list: 'No associated products.'
multi_vendor_product_products_to_link_empty_list: 'No result.'
mvps_to_exclude: 'Products to exclude'
mvps_to_include: 'Products to include'
my_account: 'My account'
my_account_links: 'My account links'
my_items_marked_by_tag: 'My articles with tag "[tag]":'
my_points: 'Fidelity Points'
my_tags: 'My Tags'
my_tags_summary: 'Summary of my tags'
my_tag_cloud: 'My tag cloud'
na: 'N / A'
naf_code: 'Naf code'
name: Name
name_of_friend: 'Name of your friend'
navigation: Navigation
navi_pages: Search
need_company_id: 'Required Company ID (the company_id param)'
neighbours: Neighbors
never: Never
new: New
newest: Novelty
news: News
newsletter: News
newsletters: Newsletters
newsletters_menu_description: 'Create and send newsletters to your customers.'
newsletter_autoresponder: 'Automatic reply Newsletter'
newsletter_autoresponders: 'Automatic replies Newsletter'
newsletter_form_placeholder: 'Your email'
newsletter_register: 'Subscribe to the newsletter'
newsletter_subscribe_already_registered: 'You are already subscribed to the newsletter'
newsletter_subscribe_failed: 'Please enter a valid email address to subscribe to the newsletter'
newsletter_subscribe_success: 'You have subscribed to the newsletter'
newsletter_template: 'Format (template) of Newsletter'
newsletter_templates: 'Newsletter templates'
news_and_emails: 'News & emails'
news_list_page: 'News page'
news_menu_description: 'Manage the news published in the news section of the shop'
news_plain: Simple
new_administrator_account_created: 'new administrator account created'
new_administrator_password: 'New Administrator Password'
new_attachment: 'New Attachment'
new_banner: 'New banner'
new_campaign: 'New campaign'
new_category: 'New category'
new_certificate: 'New voucher'
new_chart: 'New graphic'
new_combination: 'Add a new combination'
new_credit_card: 'New credit card'
new_currency: 'New currency'
new_customer: 'new customer'
new_domains: 'New Domain'
new_emails: 'Add new address'
new_event: 'New event'
new_feature: 'new feature'
new_features: 'Nouvelles fonctionnalités'
new_file: 'new file'
new_filter: 'new filter'
new_folder: 'new folder'
new_form: 'new form'
new_group: 'New group'
new_ips: 'New IPs'
new_items: 'Add new items'
new_language: 'New language'
new_language_variable: 'New language variable'
new_layout: 'New layout'
new_link: 'New link'
new_localization: 'New department'
new_location: 'New Location'
new_mailing_lists: 'New mailling list'
new_menu: 'New menu'
new_news: News
new_option: 'New option'
new_orders: 'New orders'
new_page: 'New page'
new_payments: 'Add new payment methods'
new_payout: 'Add new payment'
new_poll: 'New survey'
new_post: 'New post'
new_product: 'New product'
new_profile: 'New profile'
new_profile_field: 'New profile field'
new_profile_notification: 'Creating a sales account'
new_promotion: 'New promotion'
new_question: 'New question'
new_reason: 'Add a new reason'
new_report: 'Add new report'
new_rule: 'New rule'
new_saved_search: 'New search'
new_section: 'New section'
new_shipment: Create
new_shipment_was_created: 'Shipment of your order'
new_shipping_method: 'New delivery method'
new_site_map_section: 'Add new sitemap section'
new_states: 'Add a new canton / region to the selected country'
new_status: 'Add new status'
new_store_location: 'New store location'
new_subscribers: 'Add new subscribers'
new_tab: 'New table'
new_tag: 'New tag'
new_tax: 'New Tax'
new_usergroups: 'New User Group (s)'
new_user_profile: 'New User Profile'
new_vendor: 'New dealer'
new_visitors: 'New visitors'
new_zealand: 'New Zealand'
next: 'Next'
next_step: 'next step'
'no': 'No'
nocombination: 'Unfortunately this combination is not accessible'
none: Any
normal: Normal
norway: Norway
norwegian: Norwegian
note: Note
notes: Notes
nothing_selected: 'No selection'
notice: Note
notice_file_on_non_edp: 'This product does not have the option "service or downloadable" of activated, the files are then extras, such as a manual of use, an original invoice, etc ...'
notice_too_many_decimals: "You have specified [DECIMALS] decimals for [CURRENCY].\_The maximum number of possible decimal places is 2. The other decimal places will be displayed as 0."
notice_undeliverable_products_removed: "Unavailable products have been removed from your shopping cart.\_You can continue shopping."
notice_update_customer_details: "The email address has been changed by the customer in his profile.\_Check the box to update the order details:"
notifications: Notifications
notify_customer: 'Notify the client'
notify_orders_department: 'Notify the order service'
notify_admin: 'Notify the administrator'
notify_supplier: 'Notify the supplier'
notify_user: 'Notify the user'
notify_vendor: 'Notifiy the vendor'
notify_vendors_by_email: 'Notifiy the vendor by email'
notify_vendor_by_email: 'Notifiy the vendor by email'
notify_when_back_in_stock: 'Notify me when the product is back in stock'
not_active: 'Not active'
not_active_file_notice: 'This file will be available for download only after administrator approval'
not_approved: 'Not Approved'
not_a_basic_bundle: 'This is not a Startup Access installation'
not_a_member: Non-member
not_installed: 'Not installed'
not_scroll_automatically: 'Do not scroll automatically'
no_autoresponder: 'No automatic reply'
no_categories_available: 'No categories available'
no_category_selected: 'No category selected'
no_data: 'no data found'
no_data_found: 'No data found satisfying the requirements of this graph'
no_files: 'No file'
no_image: 'No picture'
no_invitees_found: 'No guest found'
no_items: 'Empty List'
no_items_found: 'No results matching your search criterion'
no_menus: 'No menu available'
no_payouts_found: 'No payments found'
no_posts_found: 'No posts found'
no_products_for_shipment: 'There are no products for this delivery'
no_products_selected: 'No products selected'
no_product_selected: 'No products selected'
no_rates_for_empty_cart: '<p class = "error-text" align = "center"> Unfortunately, your cart is empty and the shipping cost can not be estimated. </p>'
no_return_requests_found: 'No return request found'
no_shipping_required: 'No delivery required'
no_such_coupon: 'This coupon does not exist.'
no_template: 'No template'
no_thank_you: 'No thanks'
no_themes_available: 'No themes available'
no_users_found: 'No user found'
no_variants_for_this_option: 'No variants for this option'
no_variant_selected: 'No selected variant'
no_vendor: 'No specified vendor'
number: Number
number_of_columns: 'Number of columns in object list'
object: Object
object_does_not_exist: 'Object does not exist'
object_exists: "An object with the same name already exists.\_Do you want to crush it?"
object_not_found: '[Object] not found'
of: of
'off': 'Off'
offline: Offline
offset: Deviation
ok: OK
old_price: 'Old price'
old_visitors: 'Repeat visitors'
'on': sure
once_per_customer: 'Once per customer'
onclick_dropdown: 'On Click - scroll'
online_only: 'Online only'
on_basket_amount: 'the basket total amount'
on_off: 'On / Off'
on_products_in_basket: 'the products in the basket'
on_product_category_in_basket: 'Product categories in basket'
on_product_price: 'the product price'
on_shipping_cost: 'the shipping costs'
on_site_template_editing: 'Editing the template on site'
on_site_text_editing: 'Editing text on site'
open: Open
open_action: Open
open_faq: 'Access the FAQ'
open_file_or_create_new: 'Open a file or create a new file to start.'
open_in_new_window: 'Open in new window'
open_menu: 'Open the menu'
open_store: 'Open the front'
operating_system: 'Operating system'
operating_systems: 'Operating systems'
optimize_database: 'Optimize the database'
optimizing_table: 'Optimization of the table'
options: Options
options_for: 'Options for'
options_have_been_applied_to_products: 'The options have been successfully applied to the selected products'
options_menu_description: 'Manage global options and option variants'
options_settings: 'Options settings'
options_type: 'Type of option'
option_combinations: 'Option combinations'
option_name: 'Option Name'
option_unlinked: 'The global option "[option_name]" has been separated from the product'
option_variants: 'Option variants'
display_option_on_faceting: 'Show in facets'
display_items_in_search_filters: 'Display items in the search filters'
remove_items_in_search_filters: 'Remove items from the search filters'
change_saved: 'Change saved'
opt_currency_name: 'Show titles'
opt_currency_symbol: 'Use symbols instead of titles'
opt_language_icon: 'Use images instead of names'
opt_language_name: 'Show the name of the language'
or: or
order: Order
ordered_products: Products
orders: Orders
orders_not_allow_to_change_company: "The company has not changed.\_It is not possible to change the company name when editing the order."
orders_no_items: 'Use the "Add products" button below to add products to the order'
order_by_status: 'Orders by status'
order_date: 'Order date'
order_details: 'details of the order'
order_discount: 'Reduced order'
order_id: 'Order ID'
order_info: 'Ordering information'
order_items: 'Items in the order'
order_landing_page: 'Home page'
order_management: 'Order Management'
order_overview: 'Récapitulatif de commande'
order_placed: 'Passed order'
order_prefix: 'Order prefix'
order_proceed_without_invoice_confirm: 'Are you sure you do not want to generate an invoice number for this order?'
order_returns: 'Return Policy'
order_search: 'Search Order'
order_search_product_name: 'Search by product name'
order_search_product_code: 'Search by product code'
order_status: 'Order Status'
order_statuses: 'Order Status'
order_status_payment_failed: 'Payment failed'
payments_failed: 'Failed payments'
order_statuses_menu_description: 'Add a new or edit an order status'
order_total: Total
order_total_not_correct: 'The total amount paid does not correspond to the amount of the order.'
order_total_will_changed: 'The total order will be changed by'
order_was_not_placed: 'The order has not passed'
order_adjustment_product_not_adjustable: 'Modifications are not allowed. The product is not invoiced on actual costs.'
order_adjustment_adjustment_too_big: 'The updated price ([newTotal]) must be lower than the initial price ([amountToAdjust]).'
order_adjustment_bad_percent: 'The updated price ([newTotal]) must be lower or equal to error allowance % of the initial price ([maxPriceAdjustment]% of [amountToAdjust]).'
order_adjustment_bad_params: 'Bad request: your request must have "itemId" (int) and "newTotalWithoutTaxes" (int/float).'
order_adjustment_price_greater_than_zero: 'The price to adjust must be positive.'
order_adjustment_cant_be_adjusted_at_status: 'Order can''t be adjusted at this status ([status])'
order_email_header_c: '<p>Your order has been fully shipped.&nbsp;</p><p>Thank you for having chosen us.</p>'
order_email_subject_a: ''
order_email_subject_b: 'Order confirmation'
order_email_subject_c: 'Order fully shipped'
order_email_subject_d: ''
order_email_subject_e: 'Shipment of your order'
order_email_subject_f: ''
order_email_subject_g: ''
order_email_subject_h: ''
order_email_subject_i: ''
order_email_subject_o: ''
order_email_subject_p: 'Order confirmation'
order_email_header_p: '<p>Your order has been paid and is being processed.</p>'
order_vendor_email_subject_a: 'Order has been refunded'
order_vendor_email_subject_b: 'Order confirmation'
order_vendor_email_subject_c: 'Shipment of your order'
order_vendor_email_subject_d: 'Order canceled'
order_vendor_email_subject_e: 'Delivery of your order'
order_vendor_email_subject_f: 'Order failed'
order_vendor_email_subject_g: 'Outstanding payment'
order_vendor_email_subject_h: 'Order is closed'
order_vendor_email_subject_i: 'Your order has been canceled'
order_vendor_email_subject_o: 'Order placed successfully'
order_vendor_email_subject_p: 'Order confirmation '
order_vendor_email_subject_adjusted_p: 'Order ajdustable confirmation '
organisation: 'Organisation'
organisation_administrators_information: 'Administrators'' information'
organisation_administrator_name: 'Name'
organisation_administrator_email: 'Email'
organisation_addresses: 'Addresses'
organisation_billing_address_address: 'Billing address'
organisation_shipping_address_address: 'Shipping address'
organisation_address: 'Address'
organisation_additional_address: 'Additional address'
organisation_zipcode: 'Zip code'
organisation_city: 'City'
organisation_country: 'Country'
organisation_edition: 'Edit organisation'
organisation_information: 'Organisation''s information'
organisation_name: 'Name'
organisation_business_name: 'Business name'
organisation_business_unit_name: 'Business unit name'
organisation_business_unit_code: 'Business unit code'
organisation_siret: 'SIRET'
organisation_status: 'Status'
organisations: 'Organisations'
organisations_menu_description: 'Manage marketplace'' organisations'
original: Original
or_saved_search: 'Or saved search'
or_use: 'Or use'
others: Other
other_addons: 'Other modules'
output: Results
out_of_stock_actions: 'Out of stock actions'
out_of_stock_products: 'Sold out'
out_of_stock_product_in_cart: "This product is no longer in stock.\_It is not included in your shopping cart."
override_by_dispatch: 'Override by dispatch'
override_by_this: 'Use this content for all instances of the block'
override_gc_points: 'Ignore the global point / category value for this product'
override_gc_points_brief: 'Ignore global points / categories'
override_g_points: 'Ignore the global point value for all products in this category'
override_per: 'Ignore PER global'
override_product_data: 'Apply Values ​​to All Selected Products'
owner: Owner
p21agree: 'Please enter "AGREE" or disagree "DISAGREE" in the field'
p21agree_tooltip: "By typing AGREE in the field below, I provide my signature and authorization to Payment21 & reg;\_To electronically debit my account of the amount seized.\_I understand and authorize the Machand, if my payment by eCheck is not honored or is returned for any reason, electronically debited my account of the eCheck amount to which is added NSF fees not exceeding Legal limit.\_I print or save this page for registration.\_I can call support at ************** 24/24 7/7 for my questions about this transaction."
package: Package
packages: Packages
package_contents: 'Contents of the Package'
package_type: 'Package Type'
packing_slip: 'Shipping Bulletin'
packing_slip_for_order: 'Slip control'
page: Page
pages: Search
pages_by_visits: 'Pages per visit'
page_cloned: "This page <b> [page] </b> has been duplicated.\_You can edit this page."
page_id: 'ID Page'
page_link: 'Page URL'
page_load_speed: 'Page loading speed'
page_name: 'Name of the page'
page_not_found: 'Page not found'
page_not_found_text: 'The requested page can not be found.'
page_title: 'Title of the page'
paidup: Paid-up
paid_amount: 'Paid amount'
param: Parameter
parameter: 'Object to be analyzed'
parameter_name: 'Parameter Name'
parent_category: 'Parent Category'
parent_category_id: 'ID Parent Category'
parent_item: 'Parent article'
parent_page: 'Home Page'
parent_page_id: 'Home Page'
partner: Partner
passphrase: Pass-phrase
passport_number: 'Passport number'
password: Password
path_to_files: 'Complete path to "atos" files'
pattern_id: 'File type'
pause_delay: 'Pause delay'
pay_later: 'Pay later'
payflowcolor: 'Payflow Color'
payment: Payment
payments: Payments
payments.cresecure.location_notice: "<b> Please note: </b> <br> 1.\_This payment requires that the https option is enabled! <br> 2.\_By default, validation of the order is shown to a user at the payment location.\_To put it in a different place, create a new location by sending 'checkout.cresecure_template' in the Layout Manager <a href=\"[url]\" target=\"_blank\"> </a>."
payments.epdq.bgcolor: 'Background color'
payments.epdq.btn_bgcolor: 'Button background color'
payments.epdq.btn_textcolor: 'Button text color'
payments.epdq.font_type: 'Type of font'
payments.epdq.hash_error: 'The string hash does not match'
payments.epdq.instructions: '<ol> <li> <p> Enter the following URL in the "Technical information" section in the ePDQ administration area: </p> <p> to accept or cancel payments: [url ] </li> <li> <li> Ensure that the timing of the request is set to Always online and the Request method "SHA-OUT pass phrase" in the ePDQ administration area as you want, then duplicate it in the "SHA-IN pass phrase" field in the "Data" section And the "Pass-phrase" field below </li> <li> Ensure that the option in the ePDQ administration area "Default operation code" has the same value as the option " Transaction type </li> <li> Set the "Hash algorithm" to "SHA-1" in the "Global security parameters" section of the ePDQ administration area </li>'
payments.epdq.pspid: 'EPDQ pspid'
payments.epdq.tbl_bgcolor: 'Table background color'
payments.epdq.tbl_textcolor: 'Table text color'
payments.epdq.textcolor: 'Text Color'
payments.epdq.title: Title
payments.gc.charged_amount: 'Google Amount'
payments.gc.message: 'Google Message'
payments.gc.refund: 'Google Reimbursement'
payments.signin_to_checkout: '<a href=[url]> Register </a> to order'
payments_tab1: 'Credit card'
payments_tab2: 'Internet payment'
payments_tab3: 'Other payment options'
payment_amount: 'Payment amount'
payment_category: Category
payment_category_note: 'The payment category tables will NOT be displayed if all active payments belong to the same category.'
payment_external_reference_already_used: 'External reference %externalReference% is already used.'
payment_details: 'Payment details'
payment_form_url: 'Payment form URL'
payment_info: 'Mode of payment'
payment_information: 'Payment Information'
payment_instructions: 'Payment Instructions'
payment_method: 'Payment method'
payment_methods: 'Payment methods'
payment_reference: 'Reference'
payment_response_password: 'Password in response to payment'
payment_surcharge: 'Surcharge payment'
payment_transfer_instructions: 'Please send your bank transfer with the following information: <ul> <li> IBAN:% iban% </li> <li> RIB:% rib% </li> <li> BIC:% bic% </li> <li> Name:% label% </li> <li> Name / Company name:% social_name% </ul>'
payment_transfer_instructions_no_rib: "        Please send your bank transfer with the following information:<ul><li>Amount: %amount%</li><li>IBAN: %iban%</li><li>BIC: %bic%</li><li>Name: %label%</li><li>Name / Company name: %social_name%</li><li>Address: %address%</li></ul>\r\n        "
payment_transfer_instructions_no_rib_comment: "        Please send your bank transfer with the following information:<ul><li>Amount: %amount%</li><li>IBAN: %iban%</li><li>BIC: %bic%</li<li>Name: %label%</li><li>Name / Company name: %social_name%</li><li>Address: %address%</li></ul>\r\n        "
payment_transfer_libelle: Bank wire name
payment_type: 'Mode of payment'
payout: Payment
payouts: Payments
payouts_menu_description: 'List of all commission payments to affiliates'
payout_click: Click
payout_init_balance: 'Initial balance'
payout_new_partner: 'New Affiliate'
payout_sales: 'Sales Gain'
payout_show: 'Show banner'
payout_use_coupon: 'Use coupon'
pay_affiliates_menu_description: 'Make payments (commissions) that your affiliates have earned.'
pay_by_points: 'Allow Point Payment'
pay_from_email: 'Email client'
pay_order_again: 'Refund the order'
pay_to_email: 'Vendor email address'
pdf_info: 'Use this link to download the price list in PDF format'
pending: Waiting
per: 'By [object]'
percent: Percentage
percentage_discount: 'Percentage discount'
period: Period
per_item: 'By article'
per_page: 'Per page'
phone: 'Phone Number'
phone1_label: 'Phone Number'
phone2_label: International
phpinfo: 'PHP Information'
phrase: Sentence
pick_store: 'Choose a shop'
pie: Camembert
pie_3d: '3D Camembert'
pim_option_approved: 'Approved Option'
pim_option_rejected: 'Deprecated option'
pim_product_approved: 'Approved Product'
pim_product_changes_requested: 'Product pending change'
pim_product_rejected: 'Product disapproved'
pixels: Pixels
place_order: Checkout
placing_order: 'Pending order'
plain: United
plans_menu_description: 'Create and configure affiliate programs'
platform: Platform
please_be_patient: 'Hang on please'
please_enable_the_add_on_to_see_barcode: 'Please activate the module to see a sample code'
please_enter_license_here: 'Please enter the license number here'
please_select_one: 'Please select one'
please_sign_in: 'Please login'
points: Points
points_in_use: 'Points used'
points_in_use_lower: 'Points used'
points_lower: Points
points_to_use: 'Points to use'
point_payment: 'Point of Payment'
point_price: 'Price in points'
polish: Polish
poll: Survey
polls: Surveys
polls_answers_with_comments: 'Replies with comments'
polls_central: Central
polls_first_submited: '1st survey form submitted'
polls_have_completed: 'Sorry, you have already participated in this survey.'
polls_last_submited: 'Last survey form submitted'
polls_side: 'Box of side'
polls_total_completed: 'Number of survey forms completed'
polls_total_submited: 'Total number of survey forms submitted'
polls_total_votes: 'Total votes'
polls_votes: Votes
poll_footer: 'Poll Bottom'
poll_header: 'Top of page'
poll_results: 'Message from the full survey'
poll_results_everybody: 'Everyone (in a pop-up)'
poll_results_nobody: 'No one'
poll_results_voted: 'Users voting'
poll_show_results: 'Survey results'
poll_statistics: 'Survey Statistics'
poor: Bad
popular_tags: 'Popular tags'
popup: Pop-up
popup_larger_image: 'Large pop-up image'
portugese: Portuguese
posid: 'ID Pos'
position: Position
positions_updated: 'The positions of the articles have been updated'
position_short: Position
postal_mail: 'Mail address'
postauth: 'Post authorization'
post_url: 'Post URL'
powered_by: Production
po_file: 'Choose a PO file'
po_number: 'PO number'
preauth: Pre-Authorization
preauthorization: Pre-authorization
preorder_date: 'Preorder: available% date%'
preshared_key: 'Pre-Shared Key'
prev: Prev.
preview: Preview
previous: Previous
previous_month: 'The previous month'
previous_period: 'Previous period'
previous_week: 'Last week'
previous_year: 'The previous year'
prev_page: Back
pre_moderation: 'Products requiring validation'
pre_moderation_edit: 'Validate updated product info'
pre_moderation_edit_vendors: 'Validate updating vendor profiles'
moderation_note: 'Note'
moderation_in_progress: 'Moderation in progress'
price: Price
price_decreased: 'Price has been dropped from'
price_dec_sign_delimiter: 'Price Decimal Separator'
price_discounted: Price after discount
price_for_all: 'Price for all'
price_includes_tax: 'Price includes tax'
price_increased: 'The price was increased by'
price_in_points: 'Price in points'
price_in_points_decreased: 'Price in point has been dropped from'
price_in_points_increased: 'Price in point was increased by'
price_list: 'List of prices'
price_list_ziparchive_not_installed: "Unable to create XML price list.\_Thanks for adding a Zip support in PHP on your server.\_Follow the following link <a href=\"http://www.php.net/manual/en/zip.installation.php\"> http://www.php.net/manual/en/zip.installation.php < / A>."
price_summary: 'Price Summary'
pricing_inventory: 'Price / Stock'
primary: Main
primary_currency: 'Main Currency'
print_card: 'Print this map'
print_credit_memo: 'Print the Balance Memo'
print_etiquette_chronopost: 'Print Chronopost label'
print_etiquette_mondialrelay: 'Print MondialRelay label'
print_invoice: 'Print the invoice'
print_order_details: 'Print Order Details'
print_order_overview: 'Imprimer le récapitulatif de commande'
print_packing_slip: 'Print the order form'
print_pdf_credit_memo: 'Print Reminder Memo (pdf)'
print_pdf_invoice: 'Print the invoice (PDF)'
print_pdf_order_details: 'Print Order Details (pdf)'
print_pdf_order_overview: 'Imprimer le récapitulatif de commande (PDF)'
print_slip: 'Print the order form'
priority: Priority
private: Private
private_customer_settings: 'Settings for private clients'
private_events: 'Private events'
privilege: Privilege
privileges: Privileges
privileges.change_order_status: 'Modify order status'
privileges.create_order: 'Create an order'
privileges.database_maintenance: 'BDD Maintenance'
privileges.delete_logs: 'Remove logs'
privileges.delete_orders: 'Remove orders'
privileges.edit_blocks: 'Editing Blocks'
privileges.edit_order: 'Editing orders'
privileges.edit_templates: 'Editing templates'
privileges.exim_access: 'Import and export of data'
privileges.manage_access_restrictions: 'Access management'
privileges.manage_banners: 'Manage banners'
privileges.manage_catalog: 'Manage the catalog'
privileges.manage_currencies: 'Manage currencies'
privileges.manage_design: 'Manage the design'
privileges.manage_discussions: 'Manage comments and reviews'
privileges.manage_events: 'Manage events'
privileges.manage_gift_certificates: 'Manage gift certificates'
privileges.manage_languages: 'Manage Languages'
privileges.manage_locations: 'Manage Locations'
privileges.manage_news: 'Manage News'
privileges.manage_order_statuses: 'Manage Order Status'
privileges.manage_pages: 'Manage pages'
privileges.manage_payments: 'Manage payments'
privileges.manage_payouts: 'Manage earnings'
privileges.manage_product_premoderation: 'Approve vendor products'
privileges.manage_promotions: 'Manage the promotion system'
privileges.manage_reports: 'Manage reports'
privileges.manage_reward_points: 'Manage the points system'
privileges.manage_rma: 'Manage the RMA system'
privileges.manage_seo_rules: 'Manage SEO rules'
privileges.manage_shipping: 'Manage transport'
privileges.manage_sitemap: 'Manage Site Map'
privileges.manage_static_data: 'Manage Static Data'
privileges.manage_statistics: 'Manage statistics'
privileges.manage_storage: 'Manage storage'
privileges.manage_stores: 'Manage stores'
privileges.manage_taxes: 'Manage taxes'
privileges.manage_themes: 'Manage topics'
privileges.manage_translation: 'On-site text editing'
privileges.manage_usergroups: 'Manage user groups'
privileges.manage_users: 'Manage Users'
privileges.manage_vendors: 'Manage vendors'
privileges.update_settings: 'Update Settings'
privileges.upgrade_store: Refresh
privileges.view_banners: 'View banners'
privileges.view_catalog: 'View the catalog'
privileges.view_currencies: 'View currency'
privileges.view_discussions: 'See comments and opinions'
privileges.view_events: 'View events'
privileges.view_languages: 'View languages'
privileges.view_locations: 'View locations'
privileges.view_logs: 'View logs'
privileges.view_news: 'See the news'
privileges.view_orders: 'See the orders'
privileges.view_pages: 'See the pages'
privileges.view_payments: 'View payments'
privileges.view_payouts: 'View winnings'
privileges.view_reports: 'View Reports'
privileges.view_seo_rules: 'See SEO rules'
privileges.view_settings: 'View settings'
privileges.view_shipping: 'View Transportation'
privileges.view_static_data: 'View static data'
privileges.view_statistics: 'See statistics'
privileges.view_stores: 'View All Stores'
privileges.view_taxes: 'View Taxes'
privileges.view_usergroups: 'View user groups'
privileges.view_users: 'View Users'
privileges.view_vendors: 'See vendors'
privilege_sections.addons: Modules
privilege_sections.administration: Administration
privilege_sections.cart: Basket
privilege_sections.catalog: Catalog
privilege_sections.cms: CMS
privilege_sections.design: Design
privilege_sections.orders: Orders
privilege_sections.users: Users
privilege_sections.vendors: Vendors
proceed: Continue
proceed_to_checkout: Order
proceed_to_the_next_step: 'next step'
processed: Answer
processing: Processing
processing_order: 'Order is under treatment'
processing_upload: 'Ongoing treatment...'
processor: Processor
processor_description_cardsave: 'Vendor account provided by Cardsave <a href="http://www.cardsave.net"> www.cardsave.net </a> <br/> <br/> Email: <a href = ''mailto: ecomm @ Cardsave.net ''> <EMAIL> </a>'
processor_description_p21: '<b> P21 Simple API </b> allows you to accept payments by check. <br/> <br/> To activate this option please contact Payment21 by email <a href = "mailto: support @ payment21 .com "> <EMAIL> </a> or by phone at: ************** (US Toll Free) or 0041-71-740-1629 (International). <br/> <br/> <a href="http://www.payment21.com" target="_blank"> www.payment21.com </a>'
processor_description_stripe_sepa: '<strong>To use this payment method, you must configure the Charge webhook as described in the Configure tab</strong>'
processor_description_hipay_card: '<strong>To use this payment method, you must configure the Charge webhook as described in the Configure tab</strong>'
processor_description_smoney_card: '<strong>To use this payment method, you must configure the Charge webhook as described in the Configure tab</strong>'
processor_id: 'Processor ID'
process_selected: 'Selected process'
product: Product
products: Products
products2: Products2
products_added_to_cart: 'Products added to cart'
products_amount: Amount
products_approval_status_approved: "We are pleased to inform you that the following products have been accepted by the site administration and will be available soon in the catalog:"
products_approval_status_approved_subject: '[company_name] - [products_count] product(s) approved'
products_approval_status_changed: 'Product Moderation: [status]'
products_approval_status_disapproved: "The following products have not been accepted by the site administration:"
products_approval_status_disapproved_subject: '[company_name] - [products_count] product(s) disapproved'
products_approval_status_standby_subject: '[company_name] - [products_count] product(s) awaiting modification'
products_bar: 'Product Bar'
products_cost: 'Cost of products'
products_for_shipment_not_selected: 'Products for delivery have not been selected'
products_found: 'Products found'
products_grid: 'Product overview'
products_information: 'Product Information'
products_in_basket: 'Products in the basket'
products_in_basket_max: 'the products in the basket (max.: 100 items)'
products_in_cart: 'Products in the basket'
products_links_type: 'Type of Links Products'
products_menu_description: 'View, add and edit your products'
products_required: 'Please add product (s)'
products_selected: '[Count] selected product (s)'
products_stat: 'Product statistics'
products_weight: 'Product weight'
products_were_sent: "The products have been sent.\_You will find below information about the order"
product_added_to_cart: 'Products added to your cart'
product_added_to_cl: 'Products added in the comparator'
product_added_to_wl: 'Products added to your wish list'
product_approval: Moderation
product_approval_menu_description: 'Manage the products or groups of products supplied in the vendor''s shop.'
product_approval_status_approved: "Your product [product] has been approved by the site administration and added to the catalog.\_Visitors can now buy this product in your shop."
product_approval_status_disapproved: 'Your product [product] has not been validated by the site administration'
product_banners: 'Product banners'
product_cannot_be_added: "The product can not be added to the cart.\_Check whether the loaded files meet the requirements."
product_categories: 'Product''s categories in list'
product_code: 'Product Code'
product_columns: 'Product Columns'
product_combinations: 'Product Combination'
product_coming_soon: "This product can not be added to the cart.\_It will be available on [avail_date]"
product_coming_soon_add: 'This product will be available on [avail_date]'
product_configuration: 'Product Configuration'
product_details: 'Product Details'
product_details_layout: 'Layout of Product Details'
product_disabled: 'The product is not for sale'
product_feature_cannot_assigned: 'The attribute "[feature_name]" can not be assigned to the product "[product_name]"'
product_feature_cannot_save_array: 'The attribute "[feature_id]" can not have multiple values'
product_fields: 'Product Fields'
product_filters_free_limit: "The shop is in \"free mode\".\_Only the first 3 filters will be displayed in the client area."
product_groups: 'Product groups'
product_groups_menu_description: 'Define product groups that can be selected by affiliate banners.'
product_id: 'Product ID'
product_ids_delimiter: 'Product IDs delimiter'
product_images: 'Product images'
product_inventory: 'Product Stock'
product_in_cart: 'This product is already in your cart.'
product_in_wishlist: 'This product is already in the wish list'
product_is_linked_to_mvp: 'This product is linked to a unified product sheet'
product_is_linked_to_mvp_clickable: "This product is linked to a unified product master record.\_<a href=\"%url%\"> Click here to go to the unified product sheet </a>"
product_link: '#'
product_list: 'Product in list'
product_name: 'Product Name'
product_need_age_verification: 'You must verify your age to view this product.'
product_notification_subscribed: 'You have set an alert for this product'
product_notification_unsubscribed: 'You have successfully removed an alert for this product'
product_options: 'Product options'
product_options_forbidden_combination: "The product <strong> [product] </strong> has a non-compatible option combination.\_The product has been removed from the basket."
product_picker: 'Product Selector'
product_price: 'Product''s price'
product_price_tier_low_limit: 'Product price tier low limit'
product_price_tier_low_limit_short: 'Low limit.'
product_price_tier_unit_price: 'Product price tier unit price'
product_price_tier_unit_price_short: 'Unit price.'
product_price_tiers_management_short: 'Price tiers management'
product_price_tiers_settings_error: 'Price tiers setting is incorrect.'
product_price_tiers_settings_error_detailed: 'There was a problem with your price tiers parameters. Please report it to your administrator.'
product_price_tiers_product_edit_info: 'This is the price for the first price tier. To use standard pricing for this product, enter the dedicated page and use delete button.'
product_read_reviews: 'See the [number] reviews'
product_s: 'Product (s)'
product_shipped: 'We inform you that the order %order_id% has been shipped.'
product_shipping_price: 'Delivery from <b> [price] </b>'
product_stock_low: 'Low quantity in stock'
product_subscriptions: 'Select a product'
product_summary: 'Product Description'
product_tabs: Product
product_type: 'Type of product'
product_was_reported: 'Inappropriate product has been reported'
product_was_reported_email_intro: 'An inappropriate product has been reported by a user.'
profile: Profile
profiles: Profiles
profile_activated: 'Profile Enabled'
profile_details: 'My profile'
profile_fields: 'Profile Fields'
profile_fields_menu_description: 'Configure fields in the profile form'
profile_info: 'Profile Info'
profile_name: 'Name of the profile'
progress_fail: 'Follow-up error'
progress_finalize: 'Task performed'
project_id: 'Project ID'
project_password: 'Project password'
prolongate_download_key: 'Extend the download key up'
promotion: Promotion
promotions: Promotions
promotions_fetch_list_error: 'Unable to retrieve promotions list'
promotions_invalid: 'Promotion not valid'
promotions_menu_description: 'Promotions ans bonuses based on customers adhesion'
promotions_removed: 'Promotion removed'
promotions_saved: 'Promotion saved'
promotion_add_bonus: 'Add a bonus'
promotion_add_rule: 'Add Rule'
promotion_apply_bonus: 'Apply bonuses on'
promotion_bonuses_helper: 'Without bonuses, promotion has no effect.'
promotion_bonus_discount_on_categories: 'Discount on all products of categories'
promotion_bonus_discount_on_products: 'Discount on products'
promotion_bonus_free_products: 'Free products'
promotion_bonus_free_shipping: 'Free shipping'
promotion_bonus_gift_certificate: 'Gift certificate'
promotion_bonus_give_coupon: 'Give a voucher'
promotion_bonus_give_points: 'Give points'
promotion_bonus_give_usergroup: 'User group'
promotion_bonus_order_discount: 'Order discount'
promotion_bonus_product_discount: Discount
promotion_cond_auto_coupons: 'Voucher code automatically generated'
promotion_cond_categories: Categories
promotion_cond_country: 'Customer''s country'
promotion_cond_coupon_code: 'Voucher code'
promotion_cond_feature: 'Product''s attribute'
promotion_cond_number_of_usages: 'Usage count'
promotion_cond_once_per_customer: 'One usage per customer'
promotion_cond_payment: 'Payment method'
promotion_cond_price: 'Products price'
promotion_cond_products: Products
promotion_cond_products_number: 'Number of products in the basket'
promotion_cond_purchased_products: 'Purchased products'
promotion_cond_reward_points: 'Reward on customer account'
promotion_cond_state: 'Customer''s localization'
promotion_cond_subtotal: 'Order subtotal'
promotion_cond_total_weight: 'Total weight of products in the basket'
promotion_cond_usergroup: 'User groupe'
promotion_cond_users: Users
promotion_cond_zip_postal_code: 'Customer''s zip code'
promotion_enabled: Active
promotion_max_usage: 'Maximum number of uses'
promotion_max_usage_per_user: 'Maximum number of uses per user'
promotion_min_quantity: 'Triggerring threshold (products quantity in the basket)'
promotion_name: Name
promotion_op_amount: Amount
promotion_op_cont: Contains
promotion_op_eq: 'equal to'
promotion_op_gt: 'greater than'
promotion_op_gte: 'greater than or equal to'
promotion_op_in: unclude
promotion_op_lt: 'lesser than'
promotion_op_lte: 'lesser than or equal to'
promotion_op_ncont: 'does not contains'
promotion_op_neq: 'not equal to'
promotion_op_nin: exclude
promotion_eligible_customers: Eligible customers
promotion_rules: 'IMPLEMENTING RULES OF THE DISCOUNT'
promotion_rules_helper: 'Without rules, the promotion applies to all products.'
promotion_rules_helper_basket: 'Without rules, the promotion applies to all baskets.'
promotion_save: Save
promotion_validity_period: 'Validity period'
promo_code: 'Promo code'
promo_code_or_certificate: 'Coupon code or promotional code'
promo_text: 'Promotion''s text'
properties: Properties
provider: Provider
proxy: Proxy
pseudo: Pseudonym
pspid: PSPID
public: Business
publish_sidebar_1: '0% commission on sales and no subscription'
publish_sidebar_1_link: 'Https://www.youtube.com/watch?v=8ePsSsaq57c'
publish_sidebar_2: 'Selling is simple and safe'
punchline_security: 'Discover secured classified ads'
purchased: Acquired
purchased_in_orders: 'Bought in orders'
purchased_qty: 'Purchased Quantity'
purchasers_name: of
purchase_gift_certificate: 'Acquire a gift certificate'
qty: Qty
qty_discounts: 'Quantity discount'
qty_discount_type_tooltip: 'Define an amount / percentage removed from the price'
quantity: Amount
quantity_step: 'Number of increments'
query: Request
questions: Questions
question_text: Question
quick_checkout: 'Quick order'
quick_links: 'Quick Links'
quick_menu: 'Quick Menu'
quick_product_viewer: 'Product Viewer'
quick_view: 'Quick view'
radiogroup: 'Radio group'
random: Random
range: Interval
ranges: Intervals
range_from: Of
range_to: AT
rank_number: 'Rank (''machine'')'
rates_depend_on: 'Rates depend on'
rate_calculation: 'Calculation of the rate'
rate_calculation_manual: 'Manual (by defined destination)'
rate_calculation_realtime: 'Real time'
rate_value: 'Rate or Amount'
rating: Notation
rb_add_subscription: 'Add a subscription'
rb_annually: Annual
rb_attempt_charging: 'Attempt to pay failed order'
rb_buy_product_without_subscription: 'Buy a product without subscription'
rb_by_period: 'By period'
rb_charge_subscription: 'Subscription fees'
rb_creation_date: 'Creation date'
rb_duration: 'Duration (months)'
rb_duration_short: Time
rb_edit_subscription: 'Edit a subscription'
rb_monthly: Monthly
rb_notification: Notification
rb_notification_future_paying: 'Notification of future payment'
rb_notification_manual_paying: 'Notification of manual payment'
rb_pay: Payment
rb_period_type: 'Period Type'
rb_price: 'Recurring prices'
rb_quarterly: Quarterly
rb_recurring_period: 'Subscription Period'
rb_recurring_plan: 'Subscription Program'
rb_recurring_plans_menu_description: 'View and manage product subscriptions in your store.'
rb_start_duration: 'Beginning of term'
rb_start_price: 'starting price'
rb_subscription: Subscription
rb_subscriptions: Subscriptions
rb_subscriptions_charged: 'Subscriptions were taken into account'
rb_subscription_events_menu_description: 'See the list of events related to subscriptions'
rb_view_subscriptions: 'See subscriptions'
rb_view_subscriptions_menu_description: 'Edit subscriptions, edit details and fees'
rb_weekly: Weekly
read: Read
readme: 'Read me'
read_more_3d_secure: 'Learn more about 3-D Secure'
realtime_shippings: 'Delivery settings'
reason: Pattern
reasons: Reasons
reason_text: 'Payment Processor Response'
rebuild_cache_automatically: 'Rebuild the cache automatically'
rebuild_cache_automatically_tooltip: "The modified files are saved in real time (including files modified directly on the server) and re-cached.\_Site performance may be slightly affected.\_It is recommended to disable it in production."
rebuild_combinations: 'Rebuild combinations'
recalculate: Recalculate
recalculate_order: 'Recalculate the total order'
recalculate_rates: 'Recalculate rates'
recalculate_totals: 'Recalculate totals'
recent_activity: 'recent activity'
recent_orders: 'Recent orders'
recent_products: 'Recently Viewed Products'
recipient: Recipient
recipients_name: at
recipient_description: 'Product Description'
recommendation_from: 'Recommendation of'
recommended: Recommended
max_records: max. records
recover_password: 'Recover password'
recover_password_subj: 'Password Recovery'
recursively: Recursively
redirect_customer_from_storefront: 'Redirect visitors to this store (storefront) to the store (storefront) whose countries match the IP address'
referrer: Reference
referrers: References
referrer_domain: 'Domain Reference'
referrer_url: 'Reference URL'
refresh_packages_list: 'Refresh the list of packages'
refund: Refund
refunds: Refunds
refund_cb: 'Refund on credit card'
do_refund: 'Refund'
refunded: Refunded
refunded_amount: 'Amount refunded'
regexp: RegExp
regexp_hint: 'If you use regexp verification, it is necessary to show a message amounting to the incorrect field filling, otherwise the verification will not be done.'
region: Region
regions: The
register: 'Create an account'
registered: 'Checked in'
registered_on: 'Registered on'
registered_customers: 'Registered customers'
register_accept_terms: 'I agree to the <a href="[url]" target="_blank"> Terms & Conditions </a>'
register_autoresponder: 'E-mail to be returned if the customer answers'
register_company_accept_terms: 'I agree to the <a href="[usageUrl]" target="_blank"> Terms & Conditions </a> and <a href="[saleUrl]" target="_blank"> Terms & Conditions </a>'
register_must_accept_terms: 'To continue you must accept our terms and conditions.'
register_new_account: 'NEW CUSTOMER ?'
register_profile: 'Save profile'
register_require_full_free: 'Registration required, 100% free'
registration: Recording
regnumber: 'Registration no.'
related_gift_cert: 'Gift certificates'
related_order: 'Related command'
related_product_add_error: 'Error during related product(s) creation.'
related_product_already_exists: 'The related product already exists.'
related_product_description: 'Description'
related_product_delete_error: 'Error during related products deletion.'
related_product_ean_code: 'EAN code'
related_product_image: 'Image'
related_product_name_ean_supplier_ref: 'Name, EAN code, supplier ref.'
related_product_product: 'Product'
related_product_products_selected: 'selected product (s).'
related_product_type: 'Type'
related_product_type_accessory: 'Accessory'
related_product_type_bundle: 'Bundle'
related_product_type_link: 'Link'
related_product_type_mandatory: 'Mandatory'
related_product_type_option: 'Option'
related_product_type_other: 'Other'
related_product_type_paired: 'Paired'
related_product_type_recommended: 'Recommended'
related_product_type_service: 'Service'
related_product_type_shipping: 'Shipping'
related_product_type_similar: 'Similar'
related_products: 'Related products'
release_date: 'Release date'
relevance: Relevance
remember_me: 'Remember me'
remove: Remove
remove_backup_files: 'Delete backup files'
remove_cc_info: 'Remove CC info'
remove_image: 'Remove photo'
remove_statistics: 'Remove statistics'
remove_this_item: 'Remove This Item'
rename: Rename
repay_order: Checkout
repeat_new_visits: 'Repeat / New Visits'
reply_to: 'Answer to'
report: Report
reports: Reports
reports_interval_1: Total
reports_interval_3: Day
reports_interval_5: Week
reports_interval_7: Month
reports_interval_9: Year
reports_list: 'List of Reports'
reports_parameter_1: 'Order totals'
reports_parameter_10: Shipping
reports_parameter_11: Categories
reports_parameter_12: Products
reports_parameter_13: 'Cost of product'
reports_parameter_14: 'A number of products'
reports_parameter_2: 'A number of orders'
reports_parameter_3: Orders
reports_parameter_4: 'Order status'
reports_parameter_5: 'payment methods'
reports_parameter_6: Destination
reports_parameter_7: Users
reports_parameter_8: 'Discounts and coupons'
reports_parameter_9: Taxes
report_content: 'Report inappropriate content'
report_content_mail_subject: 'Inappropriate content has been reported'
report_content_message: 'For what reason is the content inappropriate?'
report_content_title_popin: 'Information for reporting inappropriate content'
request: Request
requests_count: 'Query Account'
requesttype: 'Type of request'
request_account_name: 'Name of the requested account'
request_statuses: 'Status of applications'
required: Required
required_images: 'You must add at least one image.'
required_not_answered: 'You must answer all mandatory questions'
required_products: 'Required Products'
required_products_added: 'The following essential product (s) has been added to your cart'
required_products_out_of_stock: 'This product can not be added to your cart because the following related products are out of stock:'
reset: 'To erase'
reset_filter: 'Remove filter'
reset_inventory: 'Put the stock at zero'
reset_password: 'Initialize password'
resolutions: Resolutions
resolved: Resolved
response: Reply
restore: Restore
restore_default: 'Use the default value'
restore_from_repository: 'Restore from original'
resultcode: 'Result code'
resultdescription: 'Description of the result'
return: 'Return to normal view'
returnable: Back
returnable_product: 'Returnable Product'
returned_product: 'Product returned'
returned_products: 'Products returned'
returning_customer: 'ALREADY CUSTOMER ?'
returns: Returns
returns_info: 'Information Back'
return_info: 'Information Back'
return_must_contain_elements: 'The return was not created because no item in the order was selected.'
return_period: 'Return period'
return_period_days: 'Return period (days)'
return_products_information: 'Product Return Information'
return_registration: 'Recording a Return'
return_requests: 'Request for Returns'
return_requests_menu_description: 'View and make requests for return of orders placed on the site'
return_requests_note: 'Go to the list of your return requests'
return_status: 'Return Status'
return_url_target: 'Return URL'
reuse: Reuse
reversed: Return
revert: Return
reviews: Reviews
reward_points: 'Fidelity Points'
reward_points_log: 'Fidelity Points'
reward_points_menu_description: 'Assigned loyalty points in your shop.'
reward_points_subj_added_to: 'Have been added to your account.'
reward_points_subj_subtracted_from: 'Have been removed from your account.'
re_order: 'Redo this command'
right: Right
right_to_left_orientation: 'Right-to-left orientation'
risk_checking: "Risk assessment in progress.\_Please reload this page in a minute."
risk_information: 'Information Risk'
rma: RMA
rma_actions: 'RMA shares'
rma_completed: 'Back completed'
rma_declined: 'Back refused'
rma_reasons: 'RMA Reasons'
rma_received: Back
rma_requested: 'Return request'
rma_request_statuses: 'Status of RMA Requests'
rma_return: Return
rma_select_product: 'At least one product must be selected to create a return'
rma_warning_update_delay: 'Update from this screen may take some time to be visible'
refund_create: 'Create refund'
robot: Robot
robots: Robots
robots_log: 'Robots log'
robot_path: 'Robot path'
romanian: Romanian
root_category: 'Root category'
root_level: 'Root level'
root_page: 'Root level page'
round_to: 'Rounded to'
route: Course
routing_code: 'Course code'
rss_created: Created
rss_display_add_to_cart: 'Show Button Add to Cart'
rss_display_image: 'View Image'
rss_display_original_price: 'Display net price'
rss_display_price: 'Display the gross price'
rss_display_sku: 'Show SKU'
rss_feed: 'RSS feed'
rss_feed_last_added: 'Latest addition'
rss_feed_last_added_updated: 'Update and update'
rss_feed_type: 'Type of Flow'
rss_sort_by: 'Sort products by'
rss_updated: Update
runtime: 'Execution time'
russian: Russian
sale: Sale
sales: Sales
sales_amount: 'Amount of sales'
sales_commission: 'Sales Commission'
sales_period: 'Sales Period'
sales_period_total: 'Total sales'
sales_reports: 'Sales Reports'
sales_reports_menu_description: 'Detailed sales statistics'
sale_vs_click: 'Percentage of sales and banner clicks'
same_as_source: 'Ditto at the source'
save: Save
saved_rules_confirmation: 'The rules have been updated'
saved_search: 'Save search'
save_and_close: 'Save and Close'
save_and_send: 'Save and Send'
save_as: 'Save as ...'
save_changes: 'Save changes'
save_commissions_by_category: 'Save the commissions by category'
save_commissions: 'Save the commissions'
save_configuration: 'Save the configuration'
save_directory: 'Backup directory'
save_discount: Save
save_layout: 'Save layout'
save_layout_as: 'Save layout as'
save_process_payment: 'Save and make payment'
save_this_search_as: 'Save this search as'
sb_share: Share
scale_control: 'Scale control'
scb_text_notice: 'Please send the following address to SCB support to be defined as PostbackURL: <br /> <b> [return_url] </b>'
screen: Screen
screen_resolution: 'Screen resolution'
script: Script
scroller: Scrollbar
scroller_direction: 'Direction scroll bar'
search: 'quick search'
search_again: Search
search_by_order: 'Search by order'
search_by_owner: 'Search by manufacturer'
search_by_price: 'Search by price'
search_by_product_features: 'Search by product attribute'
search_by_product_filters: 'Search by filter'
search_by_sku: 'Search by product code'
search_by_supplier: 'Search by supplier'
search_by_vendor: 'Search by vendor'
search_by_weight: 'Search by weight'
search_conditions: 'Search Terms'
search_engine: 'Search engine'
search_for_pattern: 'Look for'
search_in: 'Search in'
search_in_category: 'Search in category'
search_in_subcategories: 'Search in subcategories'
search_options: 'Search option'
search_phrase: 'Search for a phrase'
search_product: 'Search a product'
search_products: 'Search an article, a brand ...'
search_results: 'search results'
search_results_for: 'Search results [search]'
search_string: 'Search for a phrase'
search_tooltip: 'Search products, customers, orders, Shops and news'
search_words: 'Search words'
secret_key: 'Secret Key'
secret_string: 'String serète'
secret_word: 'Secret Word'
section: Section
section_is_not_completed: 'Section not complete'
section_links: 'Section Links'
section_name: 'Name of Section'
secure: Secured
secure_checkout: 'Guarantee the order'
secure_storefront_url: 'Secure front-end URL'
secure_storefront_url_already_exists: 'The value of the <strong> Front URL </strong> you mentioned already exists for another company.'
security: security
security_settings: 'Security Settings'
see_demo: 'See demonstration'
see_etiquettes_chronopost: 'See Chronopost labels'
see_etiquettes_mondialrelay: 'See MondialRelay labels'
see_multi_vendor_product_links: 'See related products'
select: 'To select'
selectbox: 'Selection list'
selected: Selected
selected_fields: 'Selected fields'
selected_items: 'Selected articles'
select_all: 'Select all'
select_all_product_options: 'You must choose each of the options'
select_block: 'Select a block'
select_coordinates: 'Select coordinates'
select_country: 'Select Country'
select_customer: 'Select a customer'
select_dates: 'Selection of dates'
select_orders_date: 'Order date'
select_descr_lang: 'Select language'
select_fields_to_edit: 'Select fields to edit'
select_file: 'To select'
select_image: 'Add a picture'
select_new_owner_company: 'Select a new owner for the profile data of this company:'
select_one_or_more_type: 'Select one or more options'
select_options: 'Select Options'
select_option_above: 'Select previous option first'
select_product: 'Select a product'
select_products: 'Select products'
select_profile: 'Select a profile'
select_shipping_method: 'Select Delivery Method'
select_single_type: 'Select one option'
select_state: Location
select_tables: 'Select tables'
semicolon: Semicolon
send: 'To send'
sender: Transmitter
sending_email_to: 'Send email to [email] ...'
send_feedback: 'Help improve the site'
send_message: 'Send a message'
send_notification: 'Send a notification'
send_shipment_notification_to_customer: 'Send a delivery notification to the customer'
send_shipping_address: 'Send the delivery address of the customer'
send_to: 'Send to'
send_to_test_email: 'Send to tester email'
send_via: 'Send with'
send_via_email: 'Send to email'
send_via_postal_mail: 'Send by mailing address'
sent: Sent
seo: SEO
seo_meta_data: 'Meta SEO data'
seo_name: 'Name SEO'
seo_page_title: 'Page %count%'
seo_rules: 'SEO Rules'
seo_rules_menu_description: 'Use this section to set global URL writing rules in your store.'
separator: Separator
sequential: Sequential
server: Server
server_being_used: 'Server in use'
service_not_available: 'This service is not available.'
session_images: 'Images already saved'
set: 'To confirm'
settings: Settings
settings_descriptions: 'Settings labels'
settings_notifications: 'Receive email notifications'
settings_sections: 'Settings sections'
settings_wizard: 'Wizard settings'
settings_wizard_close_tooltip: 'The Wizard can be reopened in the menu "Settings-> Settingswizard".'
settings_wizard_title: 'Wizard parameters: Step [current_step] to [total_steps]'
set_initial_balance: 'Define the initial balance'
share: Share
sharedsec: 'Share a secret'
shared_secret: 'Share a secret'
share_discount: 'Share discount'
share_via_email: 'Share by email'
sha_sign: 'Signature SHA-1'
shipment: Delivery
shipments: Shipments
shipments_menu_description: 'View and print the delivery slip created for the order.'
shipment_completed: 'The order has been delivered to %shipment_name% in full.'
shipment_created: 'A delivery has been created.'
shipment_date: 'Delivery date'
shipment_details: 'Delivery details'
shipment_has_been_created: 'Shipment has been created'
shipment_id: 'The delivery was created'
shipment_info: 'Shipping Info'
shipped: Shipped
shipped_products: 'Product Shipped'
shipper_number: 'Shipping number'
shipping: 'Delivery method'
shippings: Shipments
shippings.service_not_configured: 'Unconfigured service'
shippings.service_not_found: 'Service not found'
shippings_manage_helper: 'Configure every delivery mode that will be available for sellers.You can configure generic delivery modes such as "letter", "parcel" or "parcel with tracking" ... or specific delivery modes such as "Chronopost Parcel" or Parcel "UPS with tracking ..."'
shippings_manage_title: 'DELIVERY MODES MANAGEMENT'
shippings_taxes: 'Shipping & taxes'
shipping_address: 'Delivery address'
shipping_address_line2: ''
shipping_charges: Shipping
shipping_cost: Shipping
shipping_cost_ttc: Shipping cost including tax
shipping_cost_ht: Shipping cost without taxes
shipping_costs_will_changed: 'Shipping costs will be changed by'
shipping_estimation: 'Estimated shipping costs'
shipping_freight: Shipping
shipping_image: Logo
shipping_information: 'Shipping Information'
shipping_method: 'Delivery method'
shipping_methods: 'Delivery Methods'
shipping_methods_menu_description: 'Rules and parameters for the calculation of delivery costs'
shipping_name: 'Delivery Name'
shipping_options: 'Shipping Option'
shipping_properties: 'Delivery Properties'
shipping_rates: 'Delivery rate'
shipping_same_as_billing: 'Same shipping address as billing address'
shipping_service: 'Delivery service'
shipping_tips: "<p> The products in stock will be sent after the verification of the payment.\_The carrier is responsible for the goods during transport. </p> <p> Thank you for opening your package is to check your order when you receive it.\_If the package is damaged you can refuse it.\_</p> <p> Any questions?\_Contact our customer service by email </p>"
ship_aup_delivery_confirmation_cost: 'Confirmation of delivery costs'
ship_aup_delivery_confirmation_international_cost: 'Confirmation of international shipping costs'
ship_aup_rpi_fee: 'Registration of international fees (Registered Post International Parcels fee)'
ship_aup_use_delivery_confirmation: 'Use a confirmation for delivery'
ship_can_merchant_id: 'Vendor ID'
ship_dhl_additional_protection: 'Additional Insurance'
ship_dhl_additional_protection_ap: 'Insurance Asset'
ship_dhl_additional_protection_nr: 'Not required'
ship_dhl_cod_method: 'Payment method'
ship_dhl_cod_method_m: 'Check or Money Order'
ship_dhl_cod_method_p: 'Personal or business check'
ship_dhl_cod_payment: Activate
ship_dhl_cod_value: Surcharge
ship_dhl_height: 'Package Height (Inches)'
ship_dhl_intl_ship_key: 'Delivery key (international)'
ship_dhl_length: 'Package Length (Inches)'
ship_dhl_shipment_type: 'Type of shipment'
ship_dhl_ship_hazardous: 'Delivery of dangerous goods'
ship_dhl_ship_key: 'Delivery key'
ship_dhl_system_id: 'System ID'
ship_dhl_width: 'Package width (inches)'
ship_fedex_ancillary_endorsement: 'Additional Options Fedex'
ship_fedex_ancillary_endorsement_address_correction: 'Address correction'
ship_fedex_ancillary_endorsement_carrier_leave_if_no_response: 'Respondent''s response to non-response'
ship_fedex_ancillary_endorsement_change_service: 'Currency exchange'
ship_fedex_ancillary_endorsement_forwarding_service: 'Required redirection service'
ship_fedex_ancillary_endorsement_return_delivery: 'Return Service'
ship_fedex_customer_manifest_id: 'Claim ID'
ship_fedex_drop_off_type: 'Type of deposit'
ship_fedex_drop_off_type_regular_pickup: 'Regular removal'
ship_fedex_drop_off_type_request_courier: 'Request for delivery'
ship_fedex_drop_off_type_station: 'Station depot'
ship_fedex_height: 'Height (inches)'
ship_fedex_hub_id: 'Hub ID'
ship_fedex_indicia: Index
ship_fedex_indicia_media_mail: 'Sending of media (Parcel from 1 to 70 books Books, DVDs, CDs)'
ship_fedex_indicia_parcel_select: 'Selected parcels (1 to 70 pound parcels)'
ship_fedex_indicia_presorted_bound_printed_matter: 'Packages for printing (Package of 1 to 15 books Catalogs, directories or related objects)'
ship_fedex_indicia_presorted_standard: 'Standard Package (less than 1 pound package)'
ship_fedex_length: 'Length (inches)'
ship_fedex_meter_number: 'Counter number'
ship_fedex_package_type_fedex_10kg_box: 'FedEx Box 10kg'
ship_fedex_package_type_fedex_25kg_box: 'FedEx Box 25kg'
ship_fedex_package_type_fedex_box: 'FedEx Box'
ship_fedex_package_type_fedex_envelope: 'FedEx Envelope'
ship_fedex_package_type_fedex_pak: 'FedEx Pack'
ship_fedex_package_type_fedex_tube: 'FedEx Tube'
ship_fedex_package_type_your_packaging: 'Your packaging'
ship_fedex_smart_post: 'Intelligent Station (Fedex)'
ship_fedex_special_services: 'Special Service (delivery confirmation)'
ship_fedex_width: 'Width (inches)'
ship_height: 'Height of the package, cm'
ship_length: 'Length of the package, cm'
ship_sp_l_acknowledgement_of_delivery: 'Acknowledgment of receipt (for registered mail only)'
ship_sp_l_cash_on_delivery: 'Cash on delivery (for registered mail only)'
ship_sp_l_personal_delivery: 'Personal delivery (for registered mail only)'
ship_sp_l_registered_mail: 'Recommended Shipments'
ship_sp_pc_assurance: Insurance
ship_sp_pc_cash_on_delivery: 'Against reimbursement'
ship_sp_pc_fragile: Fragile
ship_sp_pc_manual_handling: 'Hand treatment'
ship_sp_pc_personal: Staff
ship_sp_pc_signature: Signature
ship_sp_pp_additional_insurance: 'Additional insurance (up to CHF 3000.-)'
ship_sp_pp_bulky_goods: 'Voluminous goods'
ship_sp_pp_cash_on_delivery: 'Cash on delivery (ECONOMY only)'
ship_sp_pp_manual_processing: 'Manual treatment'
ship_sp_ur_additional_insurance: 'Additional insurance for URGENT goods (up to CHF 3000.-)'
ship_swisspost_error_intl_delivery: 'This delivery method can not be used for international deliveries.'
ship_swisspost_error_private_delivery: 'This method of delivery is not available for individual deliveries'
ship_swisspost_heavy_package: 'The package is too heavy'
ship_swisspost_unable_to_open_additional_services: 'The additional_services.csv file can not be opened'
ship_swisspost_unable_to_open_service: 'The [code] file can not be opened'
ship_temando_height: 'Height of the package'
ship_temando_length: 'Length of package'
ship_temando_measurement: 'Package dimensions'
ship_temando_method: 'Delivery method'
ship_temando_package: Packaging
ship_temando_readydate: 'Parcel ready date (days)'
ship_temando_subclass: 'Categories of articles to be sent'
ship_temando_weight_measurement: 'Type of weight'
ship_temando_width: 'Width of package'
ship_to: 'Delivery address'
ship_to_another: 'Different delivery address'
ship_ups_access_key: 'Access key for UPS rates'
ship_ups_package_type_01: 'UPS Letter / UPS Express Envelope'
ship_ups_package_type_03: 'UPS Tube'
ship_ups_package_type_04: 'UPS Package'
ship_ups_package_type_21: 'UPSUPS Express Box'
ship_ups_package_type_24: 'Box UPS 25Kg'
ship_ups_package_type_25: 'Box UPS 10Kg'
ship_ups_pickup_type: 'Type of deposit'
ship_ups_pickup_type_01: 'Comes every day'
ship_ups_pickup_type_03: 'Customer meter'
ship_ups_pickup_type_06: 'One-time pickup'
ship_ups_pickup_type_07: 'Removal on call'
ship_ups_pickup_type_11: 'Suggested selling rate'
ship_ups_pickup_type_19: Letter
ship_ups_pickup_type_20: 'Center "Air Service"'
ship_usps_container: Container
ship_usps_container_express: 'Container (Express)'
ship_usps_container_express_flat_rate_envelope: 'Flat Rate Envelope'
ship_usps_container_priority: 'Container (Priority shipment)'
ship_usps_container_priority_flat_rate_box: 'Flat Rate Box'
ship_usps_container_priority_flat_rate_envelope: 'Flat Rate Envelope'
ship_usps_container_priority_lg_flat_rate_box: 'Large Flat Rate'
ship_usps_container_priority_md_flat_rate_box: 'Flat rate flat box'
ship_usps_container_priority_nonrectangular: Non-rectangular
ship_usps_container_priority_rectangular: Rectangular
ship_usps_container_priority_regional_a_rate_box: 'Regional box rate A'
ship_usps_container_priority_regional_b_rate_box: 'Regional box rate B'
ship_usps_container_priority_sm_flat_rate_box: 'Small Box Flat Rate'
ship_usps_first_class_mail_type: 'First class shipment'
ship_usps_first_class_mail_type_flat: Dish
ship_usps_first_class_mail_type_parcel: Split
ship_usps_intl_package_girth: 'Package dimensions'
ship_usps_intl_package_height: 'Height of the package'
ship_usps_intl_package_length: 'Length of package'
ship_usps_intl_package_size: 'Package Size'
ship_usps_intl_package_width: 'Width of package'
ship_usps_machinable: 'Automated (Send first class or Standard item)'
ship_usps_machinable_false: 'FALSE'
ship_usps_machinable_true: 'TRUE'
ship_usps_mailtype: 'Type of Mail'
ship_usps_mailtype_matter_for_the_blind: 'Material for the blind'
ship_usps_mailtype_postcards_or_aerogrammes: Postcards
ship_usps_package_size: 'Package Size (Width + Girth, Inches)'
ship_usps_package_size_large: 'Large (84..108)'
ship_usps_package_size_oversize: 'Very large (108..130)'
ship_usps_package_size_regular: 'Normal (0..84)'
ship_usps_priority_girth: 'Priority shipment (Size: Large, Container: non-rectangular) Overall dimensions (inches)'
ship_usps_priority_height: 'Priority shipment (Size: Large) Height (inches)'
ship_usps_priority_length: 'Priority shipment (Size: Large) Length (inches)'
ship_usps_priority_width: 'Priority shipment (Size: Large) Width (inches)'
ship_usps_username: 'user ID'
ship_width: 'Width of the package, cm'
shop_now: 'Buy now'
short_description: 'Short Description'
short_hour: H
short_list: 'Short List'
short_minute: Min
short_second: dry
show: Show
show_all: 'See everything'
show_items_in_line: 'View items online'
show_menu_on_mouse_over: 'View mouse-over menu'
show_on_checkout: 'Display on order validation'
show_on_home_page: 'Show on homepage'
show_on_registration: 'Show at recording'
show_on_separate_page: 'View in a separate page'
show_orders: 'Show commands'
show_page_in_popup: 'Show this page in a pop-up'
show_price: 'Show Price'
show_rate_for_destination: 'Show rate for destination'
show_tab_in_popup: 'Show this table in a pop-up'
sidebox_1_item: 'Block first article'
sidebox_general: 'General purpose block'
sidebox_important: 'Important side block'
signature: Signature
signed_in_as: 'Sign in as'
signup_for_newsletter: 'SIGN UP TO THE NEWSLETTER !'
sign_in: 'Log in'
sign_in_as_different: 'Sign in with another user account'
sign_in_to_buy: 'Please login to purchase'
sign_in_to_enter_tags: 'Login to enter tags'
sign_in_to_view_price: '[Sign in to view price]'
sign_out: 'Log out'
sign_up_for_notification: 'Sign up for a notification'
similar_only: 'Similar only'
simple_ultimate_companies_selector: 'Please contact technical support'
simultaneous: Simultaneous
single: Unique
single_coupon_is_allowed: 'Only one discount coupon is allowed only'
single_admin_cannot_be_deleted: 'You cannot delete this admin. You must have more than one active admin.'
sitemap: Sitemap
sitemap_available_in_customer: "The site map is available by following this link: <a href=\"[sitemap_url]\"> [sitemap_url] </a> <br /> <br /> The site map is available at the following URL <a href = \"[Http_location] /sitemap.xml\"> [http_location] /sitemap.xml </a> if the SEO module is enabled.\_If the module is disabled, you must add the following code to the \".htaccess\" file for the URL to work: <br /> RewriteRule ^ sitemap \\ .xml $ [sitemap_url] [L] <br /> Add this line after \"RewriteBase\". <br /> <br />"
sitemap_clear_cache_info: 'If products, categories ... have been added or modified, please <a href="[clear_cache_url]"> delete the cache </a> to regenerate the site map xml. <br /> <br /> [Sitemap_available_in_customer]'
sitemap_menu_description: 'Generate sitemap.xml from your shop for the search engines.'
sitemap_settings: 'Site Map Settings'
site_attendance: 'Site under maintenance'
site_number: 'Site number (EPT)'
size: Cut
skiplastpage: 'Go to the last page'
skip_payment: 'Pass the payment'
skrill_customer_id: 'Customer ID "Skrill"'
skrill_partner_link: 'Http://www.skrill.com/'
sku: 'EAN code'
slovak: Slovak
slow: Slowly
small_items: 'Small items'
smarty_block: 'HTML block on Smarty'
sms_customer_registered: 'The [name] client has been registered.'
sms_for_the_sum: 'the amount of'
sms_order_placed: 'Was placed'
snapshot_date: 'Date Preview'
sold: Sale
sort_by: 'sort by'
sort_by_bestsellers_asc: 'Top sellers: croissantes'
sort_by_bestsellers_desc: Bestsellers
sort_by_company_asc: 'Sort alphabetically: A to Z'
sort_by_company_desc: 'Alphabetical order: Z to A'
sort_by_null_asc: 'No sorting'
sort_by_position_asc: 'Sort by Position: Ascending'
sort_by_position_desc: 'Sort by Position: Descending'
sort_by_price_asc: 'Sort by price: low to high'
sort_by_price_desc: 'Sort by price: descending'
sort_by_product_asc: 'Sort alphabetically: A to Z'
sort_by_product_desc: 'Alphabetical order: Z to A'
sort_by_rating_asc: 'Sort by: ascending'
sort_by_rating_desc: 'Sort by rating: high to low'
sort_by_timestamp_asc: 'The oldest articles'
sort_by_timestamp_desc: 'Most recent articles'
sort_order_asc: Ascending
sort_order_desc: Descending
sort_images: 'Sort Images'
sort_order: 'Sort Order'
spanish: Spanish
special: Special
specific_settings: 'Specific parameters'
specify_options: 'Specific Options'
specify_url: 'Special URL'
speed: Speed
ssl_certificate: 'SSL Certificate'
staff_only_notes: 'Notes for Administration'
stage: Step
standard_sidebox: 'Standard Blocks'
start: 'To start'
start_communication: 'Starting Communication'
start_date: 'Start date'
start_price: 'Starting price'
state: Location
states: Locations
states_list: 'List of departments'
states_menu_description: 'Manage the locations of your shop.'
static_block: 'Static block'
static_data: 'Static Data'
static_data_use_item: 'Use the "link text" and "URL" values ​​of the selected item'
statistics: Statistics
statistics_by_questions: 'Statistics by questions'
status: Status
statuses: Statutes
statuses_manage_helper: 'Set each order status and associated emails that will be sent to the buyer, the seller and the administrator. The statuses names will be visible by buyers and sellers in their personal area.'
statuses_manage_title: 'ORDER PROCESS MANAGEMENT'
statusflag: Flags
status_changed: 'Modified Statutes'
status_changed_after_process_payment: 'The status of the order is indicated according to the result of the payment process.'
status_name: 'Nom du statut'
status_update_email_client: 'Email for the client'
status_update_email_vendor: 'Email for the vendor'
status_update_general: 'General options'
status_update_helper: 'For each command that changes to this status, you can configure the text of the associated notification emails. Order summary documents or invoices are automatically attached in emails.'
stat_results: Results
stat_search_term: 'Keyword Search'
stat_top_search_terms: 'Top 5 Keyword Research'
stay_connected: 'Stay connected'
stay_connected_notice: News
step_four: 'Step 4'
step_one: 'Step 1'
step_three: 'Step 3'
step_two: '2nd step'
stop_other_rules: 'Stop other rules'
storage: Storage
store: Shop
storefront: 'Front shop (storefront)'
storefront_url: 'URL Front store (storefront)'
storefront_url_already_exists: 'The URL mentioned <strong> in the shop </strong> already belongs to another company.'
storefront_url_not_defined: 'The storefront URL is not set'
stores: Shops
store_access: 'Access Restrictions'
store_admin: Shop
store_import.224_multivendor: '<ul> <li> Go to the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <p </li> <li> </li> <li> Your company''s settings have been defined by default. </li> </Li> <li> <i> <i> Input: Manual <i> <i> /I> have been placed on the "Products" slot under the main content block. </li> <li> Your customized product tables (except those added by the modules) have not been transferred. <Li> The "Description", "Features" and "Files" tables have been added to the "Product Tables" section. </li> <li> The <b> Quick Menu </b> was defined by Default. </li> </ul>'
store_import.224_professional: "<ul> <li> Go to the <a href=\"[stores_section_link]\"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> In the <a href=\"[manage_languages_link]\"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <p> </Li> <li> Your settings are set by default. </li> <li> Company settings have been set by default. </li> <li> Your design (theme, layout preset) Default </li> <li> </i> and <i> Input: Manual </i> have been placed on the < </li> <li> Your customized product tables (except those added by the modules) have not been transferred. </li> <li> The \" Description \",\" Features \"and\" Files \"have been added to the\" Product Tables \"section. </li> <li> The <b> Quick Menu </b>\_Aut. </li> </ul>"
store_import.225_multivendor: '<ul> <li> Go to the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <p </li> <li> </li> <li> Your company''s settings have been defined by default. </li> </li> <li> <i> <i> Input: Manual <i> <i> /i> have been placed in the "Products" slot under the main content block. </li> <li> Your customized product tables (except those added by the modules) have not been transferred. <li> The "Description", "Features" and "Files" tables have been added to the "Product Tables" section. </li> <li> The <b> Quick Menu </b> was defined by Default. </li> </ul>'
store_import.225_professional: "<ul> <li> Go to the <a href=\"[stores_section_link]\"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> In the <a href=\"[manage_languages_link]\"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <p> </li> <li> Your settings are set by default. </li> <li> Company settings have been set by default. </li> <li> Your design (theme, layout preset) Default </li> <li> </i> and <i> Input: Manual </i> have been placed on the < </li> <li> Your customized product tables (except those added by the modules) have not been transferred. </li> <li> The \" Description \",\" Features \"and\" Files \"have been added to the\" Product Tables \"section. </li> <li> The <b> Quick Menu </b>\_Ut. </li> </ul>"
store_import.301_multivendor: '<ul> <li> Go to the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.301_professional: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.301_ultimate: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </Li> <li> Go In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.302_multivendor: '<ul> <li> Go to the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.302_professional: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.302_ultimate: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </Li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.303_multivendor: '<ul> <li> Go to the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.303_professional: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> quick menu </b> was set by default. </li> </ul>'
store_import.303_ultimate: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> Go In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.304_multivendor: '<ul> <li> Go to the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.304_professional: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> Go In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.304_ultimate: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> Go In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.305_multivendor: '<ul> <li> Go to the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.305_professional: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> Go In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.305_ultimate: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> Go In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.306_multivendor: '<ul> <li> Go to <a href="[manage_languages_link]"> Languages ​​</a> and choose the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.306_professional: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.306_ultimate: '<ul> <li> Go to the <a href="[stores_section_link]"> Stores </a> section and enter the <b> Front URL </b> for a company. </li> <li> In the <a href="[manage_languages_link]"> Languages ​​</a> section and select the <b> country </b> for each language. </li> </ul> <ul> <li> The <b> Quick Menu </b> was set by default. </li> </ul>'
store_import.actualization_completed: 'Actualization completed successfully'
store_import.actualization_failed: 'Failed to update'
store_import.actualize_data: 'Refresh Data'
store_import.admin_url: 'Administration Table'
store_import.cannot_connect_to_database_server: 'Can not connect to the server database.'
store_import.check_company_count_failed: 'The update can not start because of changes on the store (storefront) or on the vendor'
store_import.class_not_found: 'Import from this release is not supported'
store_import.cloning_database: 'Duplicate the Database'
store_import.companies_count: 'Number of fronts (Shops)'
store_import.completed: '<p> The shop''s import is complete! </p>'
store_import.completed_text_mve: '<p> To upload, follow these steps: </p> <ul> <li> Overwrite the old shop folder with the new </li> <li> Change the values ​​of the following variables: http_host, http_path, https_host , <Https_path, customer_index, admin_index, vendor_index. </li> </ul> <p> To start a new import, click <b> Start a new shop import </b> </p>'
store_import.completed_text_ult: '<p> To upload, follow these steps: </p> <ul> <li> Overwrite the old shop folder with the new .. </li> <li> Change the values ​​of the following variables: http_host, http_path , Https_host, https_path, customer_index, admin_index, vendor_index. </li> <li> Put the shopfront URL into the Administration-> Shop Section. </li> </ul> <p> Shop, click <b> Start a New Store Import </b> </p>'
store_import.complete_configuration: 'Refresh Data'
store_import.complete_store_import: 'Total import of the shop'
store_import.configuration_test_failed: 'Configuration Test Failed'
store_import.configure_store: 'Configure your shop'
store_import.converting_orders: 'Converted commands'
store_import.db_host: 'Database host'
store_import.db_name: 'Name of the data base'
store_import.db_user: 'User of the database'
store_import.done: Valid
store_import.edition_mapping_failed: 'Importing this edition is not supported.'
store_import.fill_form: 'Check the data below before importing the data <br /> <b> Warning! </b> This <b> step will overwrite ALL (including your settings, products, design, layouts, etc.) </b> <br /> Logs and statistics will also be deleted.'
store_import.finish_store: 'Finish the shop import'
store_import.first_step: 'Select shops'
store_import.from: 'From:'
store_import.full_path_installation_directory: 'Complete path to the directory installation'
store_import.import_data: 'Importing Data'
store_import.import_failed: 'Failed to import the store'
store_import.import_from: 'Import of'
store_import.local_store: 'Path of the shop'
store_import.local_store_tooltip: "Enter path of the root directory whose data you want to import.\_For example '/ var / www / examplestore' on a Unix system or 'C: \\ examplestore' on Windows"
store_import.path_does_not_exist: "The specified directory does not exist.\_Please check if the entered path is correct."
store_import.processing_addons: 'Update modules'
store_import.progress_title: 'Refresh [to]'
store_import.second_step: 'Validate and import'
store_import.second_step_22x_notice: "<p> Note: Your original settings and design will not be transferred, except for the modules and customizable blocks under the 'products' location of the Manager block with <i> Content: Products </i> And <i> Input: Manual </i>.\_During the next step, you can locate them in the \"Products\" slot under the main block. </p>"
store_import.second_step_30x_notice: '<p> </p>'
store_import.secure_storefront_url: 'Secure Store Front URL'
store_import.select_store: Validate
store_import.start_new_store_import: 'Start a new shop import'
store_import.storefront_url: 'Front shop URL'
store_import.store_import: Shop
store_import.table_prefix: 'Table of prefixes'
store_import.text_configuration: "<p> <h4> The data from the old shop was successfully imported. </h4> <br/> Now you can exit the import tool, customize the settings and design of your shop.\_First, follow these steps: </p>"
store_import.text_configuration2: "<p> When you're ready for customization, you'll need to go back to the import tool and go directly to the next step.\_</p> <p> <strong> Caution! </strong> Updating your data will not be possible if you change, Delete or add a new [storefronts_vendors].\_In this case, you must restart the store import from the first step. </p>"
store_import.text_from: '[Product_name] [product_version] [product_edition]'
store_import.text_to: 'A: [product_edition] [product_version]'
store_import.this_is_not_cart_path: 'The specified directory is not a valid root folder of the store installation.'
store_import.updating_data: 'Update data'
store_import.updating_languages: 'Update languages'
store_import.wrong_table_prefix: 'The store tables with the prefix defined are missing from the database.'
store_locator: Location
store_mode: 'License mode'
store_mode_changed: 'The licensing mode has changed'
store_mode_will_be_changed_to_free: "Failed to verify the integrity of the installation.\_Your shop will go into free mode in 2 days.\_Please contact <a href=\"http://www.cs-cart.com/helpdesk\"> the support </a>"
store_mode_will_be_changed_to_trial: "Failed to verify the integrity of the installation.\_Your shop will be tested in 2 days.\_Please contact <a href=\"http://www.cs-cart.com/helpdesk\"> the support </a>"
store_number: 'Shop number'
store_object_denied: '[Object_type] [object_name] is not available for this store'
store_theme: 'Shopfront theme (storefront)'
subcategories: Subcategories
subcats: Subcategories
subject: Subject
subject_contact_by_client: 'A user will contact you via the form'
submit: 'To send'
submit_my_order: 'Validate my order'
subpages: Submenu
subscribe: Record
subscribed: Subscribe
subscribed_success: 'Successful registration'
subscribed_to: 'Subscribe to [num] list (s)'
subscriber: Subscriber
subscribers: Subscribers
subscribers_menu_description: 'Description of Subscribers'
subscribers_menu_item_text: Subscribers
subscribers_num: 'Number of subscribers'
subscriber_email: 'Subscriber Email'
subscriptions: Subscriptions
subtotal: Subtotal
subtotal_invoice_ttc: Subtotal (Incl. tax)
subtotal_invoice: Subtotal (excl. tax)
subtotal_sum: 'Total Subtotal'
subtract: Subtract
suburb: Suburbs
successful: Successful
successfully_registered: 'Registered successfully'
successful_login: 'You successfully logged in.'
success_registration_text: "Congratulations!\_You are registered! <br/> You now have access to all the functionalities of the shop.\_Click on \"My account\" to access it."
summary: summary
supplier: Provider
suppliers: Suppliers
supplier_id: 'Supplier ID'
supportreferenceid: 'Media reference ID'
surcharge: 'Extra charge'
surcharge_title: 'Title of Surtax'
sweden: Sweden
swedish: Swedish
swing: Exchange
switch_layout: 'Change the layout'
switch_to_customization_mode: 'Switch to customization mode'
switch_to_translation_mode: 'Switch to translation mode'
symbol: Symbol
system: System
tab: tab
table: Table
tables: Tables
table_conditions: 'Conditions of the tables'
tabs: Paintings
tag: Tag
tags: 'Bookmark and Share'
tags_menu_description: 'Manage tags added to the different pages of your shop.'
tag_cloud: 'Tag Cloud'
tag_modifications_saved: "Changes saved successfully.\_They will be applied after the administrator validates."
tax: VAT
tax_commission: VAT Commission
taxes: Taxes
taxes_manage_helper: 'Create and configure here the taxes (VAT) that vendors can apply to their products. These taxes will be visible and apllicables by vendors in their interface for each product.'
taxes_manage_helper_shipping: 'Search and select countries you want to sell to, then set the applicable tax rate for shippings of merchants belonging to these countries.'
taxes_manage_title: 'Taxes Configuration'
taxes_manage_warning: "Warning, if a VAT has its \"Price includes tax\" box checked, the price of the products that are affected by this VAT tax will be calculated including taxes.\r\nOtherwise, the price will be calculated excluding taxes."
taxes_manage_tab_shippings: 'Shipping costs'
taxes_manage_shipping_countries: 'Active countries'
taxes_manage_shipping_table_tax_header: 'Shipping tax rate'
taxes_manage_shipping_search_country: 'Search a country'
taxes_manage_shipping_updated: 'Taxe updated'
taxes_manage_shipping_updated_error: 'Cannot update tax, invalid country code or tax'
taxes_manage_shipping_deleted: 'Taxe deleted'
taxes_manage_not_found: 'Tax not found'
taxes_menu_description: 'Manage taxes.'
tax_exempt: 'Exempt from tax'
tax_rates: 'Tax rate'
bo_tax_manage:
    tax_config_title: PRICE SETTINGS
    tax_config_subtitle: Price includes tax
    tax_config_helper: |
        Products’ prices are taxes included.
        Delivery methods' price are taxes included.
    tax_config_warning: Attention, updating this information impacts prices paid by the customer.
    tax_config_on: Active
    tax_config_off: Off
    commisions_title: TAX FOR COMMISSIONS
    commisions_search: Search a tax
    commissions_updated: Commissions' tax updated
    rate_title: TAX RATES
    rate_helper: Configure the taxes available for products, commissions and shipping costs.
    rate_table_name: Name
    rate_table_status: Status
    rate_value: 'Rate ( % )'
    input_too_short: Please enter 3 or more characters
temando: Temando
temando_centimetres: Centimetres
temando_feet: foot
temando_inches: Inches
temando_method_depottodepot: 'Depot at Depot'
temando_method_doortodoor: 'door to door'
temando_metres: Meters
temando_package_backpack: Backpack
temando_package_bale: Ballot
temando_package_box: Box
temando_package_bunch: Grouping
temando_package_bundle: Package
temando_package_carton: Cardboard
temando_package_crate: Crate
temando_package_cylinder: Cylinder
temando_package_documentenvelope: Envelope
temando_package_drum: Drums
temando_package_flatpack: 'Flat pack'
temando_package_letter: Letter
temando_package_pail: Bucket
temando_package_pallet: Palette
temando_package_parcel: Parcel
temando_package_pipe: Tube
temando_package_roll: Wheel
temando_package_satchel: 'Briefcase / bag'
temando_package_skid: Sliding
temando_package_suitcase: Attaché-case
temando_package_tube: Tube
temando_package_unpackaged: 'Unpackaged or N / A'
temando_package_wheel: 'Wheel / Tire'
temando_subclass_excessbaggage: 'Excess Baggage'
temando_subclass_furniture: Home
temando_subclass_householdgoods: 'Household goods'
temando_subclass_other: 'Other (etc.)'
temando_system: 'Temando System'
temando_weight_grams: Grammes
temando_weight_kilograms: Kilograms
temando_weight_ounces: Inches
temando_weight_pounds: 'Weight (pounds)'
template: Template
templates: Templates
templates_tree: 'Template tree'
template_editor: 'Template editor'
terminal: Terminal
terminal_id: 'ID Terminal'
test: Test
testimonials: The
test_live_mode: 'Test / production mode'
test_mode: 'Test mode'
text: Text
textarea: 'Text zone'
text_2checkout_notice: "<b> Note </b>: To track your 2Checkout order with the shopping cart software, you must follow these steps: Sign in to your 2Checkout account <br /> - & nbsp; Click <u> 'Site management' </u> in the <u> 'Account' section </u>. <br /> - & nbsp; Select the <b> / B> in the sub-section <u> 'Direct Return' </u> <br /> - & nbsp;\_Enter <u> 'Approved URL' </u> and <u> 'Pending URL' to: <br /> <b> [return_url] </b> <br /> - & nbsp;\_Enter <u> 'Secret Word' </u>.\_The secret word is known only to the seller and 2CheckOut. <br />"
text_2co_ins: 'Instant Notification Service (INS)'
text_access_notice: 'You can use special characters for searches "Domain", "E-mail" and "credit card": <br /> <b> * </b> - represents several characters, including zero <br /> <b>? </b> - represents a single character.'
text_additional_detailed_image: '(Displayed in a pop-up window)'
text_additional_thumbnail: '(Displayed in the product detail page in ''Additional Images'')'
text_addon_cannot_enable: 'The module "[addon_name]" is incompatible with the following modules: [addons]. It can not be activated.'
text_addon_confclicts: 'The module "[addon_name]" is incompatible with the following modules: [addons]. These modules will be automatically deactivated.'
text_addon_confclicts_on_install: 'The "[addon_name]" module is incompatible with the following modules: [addons]. It has not been activated. Enable it manually and all incompatible modules will be disabled automatically.'
text_addon_installed: 'The <b> "[addon]" </b> module has been successfully activated'
text_addon_install_dependencies: 'The module can not be installed due to the addon dependency. Please install the [addon] module first.'
text_addon_uninstalled: 'The <b> "[addon]" </b> module has been uninstalled'
text_addon_uninstall_dependencies: 'The module could not be uninstalled because of dependencies with the following modules: [addons]'
text_addresses_wildcards: 'You can use wildcards in this field: <br> <b> ''?'' </b> - any single character <b> ''*'' </b> Example: </u> <br> <b> * street </b> & nbsp; & nbsp; (corresponds to 19 street, 102 street etc)'
text_address_not_found: 'Address not found'
text_admin_new_orders: 'This section displays commands you have not yet seen.'
text_allowed_to_upload_file_extension: 'You can load files with the following extension only <b>. [Ext] </b>.'
text_all_items_included: 'All [items] included'
text_amazon_callback_url: 'Important! You must set your <b> [callback_url] </b> URL as the Vendor URL on the Amazon''s Seller Central Website> Settings> Checkout Pipeline Settings. Otherwise, you will not be able to accept payments via Amazon Checkout into your store.'
text_amazon_failed_order: 'No replies from Amazon Checkout have yet been received, please wait. Although the order can not be created in the shop, it is likely that it was created and successfully verified by Amazon Checkout. Please contact the store team and provide them with the order ID provided by Amazon'
text_amazon_incorrect_products_count: 'The number of products in your cart differs from the number of products in the Amazon query.'
text_amazon_link_message: 'For test reasons, the vendor URL may be your shop''s HTTP link, but the active mode requires a secure connection, you must use an HTTPS link. Remember to activate production mode on Amazon on Amazon''s Seller Central website when you are ready to upload.'
text_amazon_surcharge: '<b> Note: </b> For Amazon <a href=''https://payments.amazon.com/sdui/sdui/about?nodeId=6019'' target=''_blank''> policy </a> (B4. 2), surcharges are not supported. All of your surcharges will be ignored when using Amazon Checkout.'
text_amazon_uk_warning: '<b> Note: </b> Amazon Checkout integration is currently available to US vendors only.'
text_anonymous_checkout: 'You must register to order'
text_answer_type: 'Provide a textual response'
text_applied_promotions: 'Les promotions suivantes vont être appliquées'
text_approval_notice: 'This message needs to be validated'
text_are_you_sure_to_delete_file: 'Are you sure you want to delete this article?'
text_are_you_sure_to_proceed: 'Are you sure you want to continue ?'
text_atos_notice: "<b> Note: </b>\r\n<br /> 1. Copy the certificate, which was sent to disk, to the [home_path] directory. Rename the certificate to certif.fr. [My_merchant_id], where [my_merchant_id] is the number of your website.\r\n<br /> 2. Rename the store settings file (parmcom.014213245611111 file) to parmcom [my_merchant_id].\r\n<br /> 3. Modify <b> pathfile </b> in the <b> [home_path] </b> <br /> file - Replace string '<u> D_LOGO! [Some_dir] </u> \"\" by \"\" <u> D_LOGO! [Http_path] / logo /! </u> <br /> - Replace string '<u> F_DEFAULT! [/ Some_dirs / parmcom.sogenactif] </u> \"\" by \"\" <u> F_DEFAULT! [Home_path] / parmcom.sogenactif! </u> <br /> - Replace string '<u> F_PARAM! [/ Some_dirs / parmcom] </u> \"\" with \"\" <u> F_PARAM! [Home_path] / parmcom! </u> <br /> - Replace string '<u> F_CERTIFICATE! [/ Some_dirs / Certif] </u> \"\" by \"\" <u> F_CERTIFICATE! [Home_path] / Certif! </u>\r\n<br /> 4. Edit <b> parmcom. [Some_number] </b> file in <b> [home_path] </b> <br /> - Replace channel '<u> AUTO_RESPONSE_URL! [Some_url] </u> \"\" by \"\" <u> AUTO_RESPONSE_URL! [Auto_url] </u> <br /> - Replace string '<u> CANCEL_URL! [Some_url] </u> \"\" with \"\" <u> CANCEL_URL! [Ok_url] </u> '<br /> - Replace string' <u> RETURN_URL! [Some_url] </u> \"\" with \"\" <u> RETURN_URL! [Ok_url] </u> <br /> \r\n"
text_atos_warning: '<span style="color:#FF2222">Attention!!!</span>The path to the Atos files must consist of more than 60 symbols, otherwise copy the atos_files folder to a parent directory, for example, in the basket''s home directory. Also make sure that the Atos file directory has all permissions.'
text_authentication_failed_message: 'Your bank can not authenticate the transaction. To protect against fraudulent use, this card can not be used to validate your purchases. You can complete your order by selecting another payment method.'
text_backup_filename: 'Note: this file will be located in the following directory of your server:'
text_backup_management_notice: 'This section allows you to manage the backup files. - To restore the database, tick the box corresponding to the appropriate file name and press the "Restore" button (Please note that the first file will be processed if you select more Of an entry) - To delete files, tick the boxes corresponding to the files to delete and press the "Delete" button .- To download the file to your local computer, click on the file name.'
text_banner: 'Text Banner'
text_banners: 'Text Banners'
text_billing_same_with_shipping: 'The billing and delivery addresses are the same'
text_block_trial_notice: 'Your 30-day trial period has expired. Please purchase the license or remove the software from your server.'
text_box: 'Entry field'
text_cannot_apply_points_to_this_order_because_total: 'Unfortunately you can not apply the points to this command because the total cost is equal to zero.'
text_cannot_apply_points_to_this_order_because_user: 'Unfortunately you can not apply the points to this order because you do not have enough points on this account.'
text_cannot_create_directory: "Unable to create directory <b> [directory] </b>.\_Please check the permissions."
text_cannot_create_file: 'Can not create file <b> [file] </b> .Please check the permissions of the directory.'
text_cannot_delete_directory: 'Unable to delete directory <b> [directory] </b> .Please check permissions.'
text_cannot_delete_file: "Unable to delete the <b> [file] </b> file.\_Please check the permissions."
text_cannot_rename_directory: "Unable to rename directory <b> [directory] </b>.\_Please check the permissions."
text_cannot_rename_file: "Unable to rename file <b> [file] </b>.\_Please check the permissions."
text_cannot_restore_directory: "Unable to restore the <b> [directory] </b> directory from the original.\_Please check if it exists."
text_cannot_restore_file: "Unable to restore the <b> [file] </b> file from the original.\_Please check if it exists."
text_can_be_used_once: "This coupon can only be used once.\_You have already used it."
text_cart_amount_changed: 'The number of products <b> [product] </b> in your cart has been changed to be authorized'
text_cart_amount_corrected: "The number of products in stock is insufficient for your order.\_La quantité du produit <b>[product]</b> a été changée dans votre panier."
text_cart_empty: 'your basket is empty'
text_cart_max_qty: 'The maximum quantity for "[product]" is <b> [quantity] </b>'
text_cart_min_qty: 'The minimum quantity for "[product]" is <b> [quantity] </b>'
text_cart_not_enough_inventory: 'The number of products in stock is insufficient for your order.'
text_cart_zero_inventory: 'The product <b> [product] </b> is no longer in stock and can not be added to the cart.'
text_cart_zero_inventory_and_removed: '<B> [product] </b> is out of stock and will be removed from shopping basket.'
text_catalog_mode_zero_price_action_notice: 'For the "Buy Now" button to be displayed on the detailed product page, please set "Zero Price Action" as "Allow customers to add the product to the shopping cart".'
text_categories_have_been_deleted: 'Categories successfully removed.'
text_category_delimiter: 'The delimiter of the Category path for the ''main_category'' field (ex: ''Computers /// Desktops'')'
text_category_detailed_image: '(Optional, displayed in category details page only)'
text_category_has_been_deleted: 'The category, subcategories and their products have been successfully removed.'
text_category_icon: '(Displayed in the categories list and the detail pages)'
text_exim_jobs_have_been_deleted: 'Report successfully removed.'
text_cc_number_is_blocked: 'The credit card number <b> [cc_number] </b> is blocked by the administrator. Please contact the administrator of the online store or try another number.'
text_cc_processor_connection: 'Connecting to <b> [processor] </b> ...'
text_changes_not_saved: 'Your changes have not been saved.'
text_changes_saved: 'Your changes have been saved.'
text_change_password: 'You are now logged into your account. Please change your password directly in the account information. Remember to save the changes.'
text_checkout_new_profile_notice: 'If you are a new customer, you must first register by completing the form below.'
text_cities_wildcards: 'You can use wildcards in this field: <br> <b> ''?'' </b> - Any single character <b> ''*'' </b> - Multiple characters. Example: </u> <br> <b> New Y * </b> & nbsp; & nbsp; (corresponds to New York, New Yark, etc.) & Nbsp; (Corresponds to Las Vegas, Los Angeles, etc.)'
text_ckeditor: CKEditor
text_click_here: 'Click here for more details.'
text_cmcic_notice: 'Please put the following URL in the <b> CGI2 </b> field: <b> [postback_url] </b>'
text_cmpi_frame_message: 'For security reasons, please complete the form before validating your order. Thank you for not clicking on the refresh or return buttons, the transaction could be interrupted or canceled.'
text_cmpi_go_back: Return
text_combination_out_of_stock: 'No products in stock with these options.'
text_companies_activated: 'Selected companies are activated'
text_companies_disabled: 'The selected companies are disabled'
text_company_status_active_subj: 'Your company has been activated'
text_company_status_active_to_pending: 'Your profile has been changed. Once the changes are validated, the information will appear in your shop.'
text_company_status_changed: 'The status of your company [company] has been changed to [status].'
text_company_status_disabled_subj: 'Your company is not activated'
text_company_status_new_to_active: 'Your request as a vendor [company] has been validated.'
text_company_status_new_to_active_administrator_created: 'A new administrator has been created. You can access the administration area [link] using: <br /> Login: [login] <br /> Password: [password]'
text_company_status_new_to_active_administrator_updated: 'The type of your [login] account has become an administrator. You can log into the admin area: [link]'
text_company_status_new_to_active_subj: 'Your request as a vendor has been validated.'
text_company_status_new_to_disable: 'Your request as a vendor [company] was rejected'
text_company_status_new_to_disable_subj: 'Your request as a vendor has been rejected'
text_company_status_new_to_pending: 'Congratulations ! Your vendor account has been created. As soon as we have verified your information, it will be published on our site.'
text_company_status_pending_subj: 'Your company is awaiting confirmation.'
text_company_status_pending_to_active: 'Changes to your [company] vendor profile have been validated.'
text_conditions_cleared: 'The conditions of the table have been erased'
text_confirmation_page_header: "This is a confirmation page.\_You must confirm the operation before it is executed."
text_confirm_delete_jobs: 'Are you sure to continue ? Deleting reports in pending will result in a cancellation of associated imports.'
text_confirm_delete_all_products: 'You are about to delete ALL products, this action is irreversible, are you sure you want to continue?'
text_confirm_delete_mvproducts: 'You are about to delete multi vendor products which can be linked to products, this action is irreversible, are you sure you want to continue?'
text_confirm_delete_products: 'You are about to delete products, this action is irreversible, are you sure you want to continue?'
text_confirm_passwd_recovery: 'A password recovery request has been made for your account. <br /> To customize a new password, please follow the following link: <br />'
text_confirm_delete_related_products: 'You are about to delete related products, this action is irreversible, are you sure you want to continue?'
text_confirm_pricetiers_delete: "You are about to delete your settings. Are you sure you want to continue?"
text_csrf_attack: 'Access Denied: Possible CSRF attack'
text_customer_area_logo: 'Logo for the <b> customer zone </b>'
text_data_changed: 'The changes were not saved. Click OK to continue, or Cancel to stay on the page.'
text_decrease_points_in_use: 'The points used in the order have been removed from the customer''s account.'
text_delete_recipients: 'If you delete certain guests, click the Update button to save changes to the database'
text_deltapay_notice: '<b> Note: </b> Please send the following information to DeltaPay support: <u>[Result_url] </u> ''<br /> Failure page:'' <u> [result_url] </u> ''<br /> Cancel page:'
text_directebanking_notice: 'Put ''Success link'' to: <b> [success_url] </b> <br /> Add Abort link to: <b> [abort_url] </b> <br /> Add new HTTP notifications and set '' Notification URL ''to: <b> [notification_url] </b> <br /> Enable validation on input and set'' Hash Algoritm ''to'' SHA1 ''<br />'
text_directory_created: 'The <b> [directory] </b> directory was successfully created.'
text_directory_deleted: 'The <b> [directory] </b> directory has been successfully removed'
text_directory_renamed: 'The <b> [directory] </b> directory has been renamed successfully to <b> [to_directory] </b>'
text_directory_restored: 'The <b> [directory] </b> directory has been restored from the dictionary'
text_downloads_empty: 'Your download list is empty'
text_editing: 'Text editing'
text_edp_product: 'Service or product downloadable without delivery.'
text_ekey_not_valid: 'The entered electronic key is invalid or has expired'
text_email_is_blocked: 'The <b> [email] </b> email is blocked by the administrator. Please contact the administrator of the online store or try with another address.'
text_email_sent: 'The email was successfully sent.'
text_emerchantpay_notice: 'Please add these URL fields to the configuration of your eMerchantPay eCommerce payment form: <br/> <br/> <b> BackReturn URL: </b> <br/> [backreturn_url] > Custom Approval URL: </b> <br/> [notify_url] <br/> <br/> <b> Custom Decline URL: </b> <br/> [decline_url] <br/> < <b> Activate Notification URL: </b> <br/> [process_url]'
text_enabled_testimonials_notice: 'Comments are inactive. To enable them, go to <a href="[link]"> Comments and Reviews: options </a> and select the "Comments", "Note" or "Comments and Notes" values in the "Comments" field.'
text_enets_notice: "Please send the following URL to the eNPS manager as the response URL for the three cases (success, failure, cancellation): [r_url]\r"
text_enter_access_key: 'Please enter the key to access this event'
text_enter_filename: 'Please enter a new file name'
text_error_adding_request: 'Error adding query.'
text_events_you_subscribed: 'Private events in which you are registered'
text_event_subscriber: 'You received an invitation from [owner] for participation in the gift list. Your email address was included in the mailing list for this event.'
text_event_unsubscribe: 'You have been successfully unsubscribed from the mailing list of the event.'
text_exim_data_exported: 'The data was exported successfully.'
text_exim_data_imported: 'The file is accepted and placed in the queue. The <b> [total] </b> objects are being uploaded or updated.'
text_exim_data_imported_clear: 'The data has been imported successfully.'
text_exim_export_result_option: 'When exporting a large range of data, please prefer results via EMAIL. Once generated,
    results will be provided in a download link sent by email or in the dedicated reports menu.<br/>
    There can be no more than 1 EMAIL export at a time for a given user.<br/>
    DOWNLOAD exports must be used only for small range of data. Otherwise, an error may occur.<br/>
    DOWNLOAD exports remain available during EMAIL export.<br/>
    Its results are also available in the dedicated reports menu.'
text_exim_export_notice: "Here is a list of fields that can be exported.\_Highlighted <b> fields </b> are required."
text_exim_import_features_note: '<h4>Product Attribute Import Format</h4><code>{$ldelim}%Feature ID%{$rdelim} (%Group name%) %Feature name%: %Feature type%[%Feature value%]</code>where,<dl class="dl-horizontal"><dt>%Feature ID%</dt><dd>the id of feature</dd><dt>%Group name%</dt><dd>the name of the feasture group</dd><dt>%Feature name%</dt><dd>the name of the feature</dd><dt>%Feature type%</dt><dd>Feature type (C - Checkbox, M - Multiple checkbox, S - Text select, N - Numver select, E - Extended select, T - simple text, O - Number, D - date)</dd><dt>%Feature value%</dt><dd>the value of the feature (many values can be delimited by a coma)</dd></dl><p>many values can be delimited by a semicolon.</p><h5>Example:</h5><code>ISBN: T[1233423423]; Published at: D[05/05/07]; Color: S[Red]</code>'
text_exim_import_files_note: '<h4>Import file format</h4><code>%File location%</code> where,<dl class="dl-horizontal"><dt>%File location%<dt><dd> can be the relative or absolute path on the server or an external URL</dd></dl><p>Many files can be delimited by a coma.</p><h5>Example 1:</h5><code>file1.jpg</code><h5>Example 2:</h5><code>backup/file1.zip, images/file2.jpg</code>'
text_exim_import_images_note: '<h4>Image Import Format</h4><code>%Image location%#%Alternative text%</code> où,<br><dl class="dl-horizontal"><dt>%Image location%</dt><dd>can be the relative or absolute path on the server or an external URL,</dd><dt>%Alternative text%<dt><dd>image alternative text</dd></dl><h5>Example 1:</h5><code>images/product_images/apples.jpg#Apples</code><h5>Example 2:</h5><code>http://www.site.com/apples.jpg#Apples</code>'
text_exim_import_notice: 'Here is the list of fields that your data file can contain. Highlighted fields are required.'
text_exim_import_options_note: '<h4>Product option import format</h4><code>{$ldelim}%Option ID[_L]%{$rdelim}%Option name%: %Option type%[{$ldelim}%Variant 1 ID%{$rdelim}%Variant 1%,{$ldelim}%Variant 2 ID%{$rdelim}%Variant 2%,{$ldelim}%Variant N ID%{$rdelim}%Variant N%]</code> where,<dl class="dl-horizontal"><dt>%Option ID%</dt><dd>option id</dd><dt>L</dt><dd>Option, means that it is a global option linked to the product.</dd><dt>%Option name%</dt><dd>option name,</dd><dt>%Variant N ID%</dt><dd>variant id,</dd><dt>%Variant N%</dt><dd>the name of the variant. Variants must be defined if the option is of the selectbox or radiogroup type.</dd><dt>%Option type%</dt><dd>option type, can be:<br><ul><li><b>S</b> - selectbox,</li><li><b>R</b> - radiogroup,</li><li><b>C</b> - Checkbox,</li><li><b>I</b> - simple input,</li><li><b>T</b> - text box (textarea).</li></ul></dd></dl><p>Many options can be delimited by a semicolon.</p><h5>Examples:</h5>Simple text options:<code>Your age: I; Date of birth: I; Notes: T</code><br>Options with variants:<code>Color: S[Red, Green, Blue]; Size: R[X, XL, XXL]</code>'
text_exim_import_date_format_note: '<h4>Dates format</h4><br>Here are the accepted dates formats :<br><br><ul><li><b>French format :</b> DD-MM-YYYY</li><li><b>American format :</b> MM/DD/YYYY</li><li><b>Other :</b> YYYY-MM-DD</li></ul><br><h5>Examples : </h5><ul><li>15-12-2019</li><li>12/15/2019</li><li>2019-12-15</li></ul>'
text_exim_utf8_file_format: 'Please check the encoding of your file in UTF-8 for a correct import.'
text_expired_license: 'Unfortunately, your subscription to the CS-Cart software is outdated. Please renew it as soon as possible to avoid suspension'
text_failed_gift_certificate_addition: 'The addition of gift certificates failed. Please check that the total is not at zero.'
text_fancybox: FancyBox
text_features_delimiter: 'Delimiter for product attributes.'
text_feedback_notice: 'The parameters that will be sent to our server to analyze the operation and configuration are listed below. <br /> Please note that no personal or financial information will be sent. After reading the list of parameters, click on "Send". <br />'
text_files_directory: 'The folder where the product files are located. This will be used if the "File" field is specified without a path; This must be an absolute path.'
text_file_created: 'The <b> [file] </b> file was successfully created.'
text_file_deleted: 'The <b> [file] </b> file was successfully deleted'
text_file_renamed: 'The <b> [file] </b> file was successfully renamed to <b> [to_file] </b>'
text_file_restored: 'The <b> [file] </b> file has been restored from the original'
text_file_saved: 'The <b> [file] </b> file was successfully saved'
text_fill_the_mandatory_fields: 'You must complete all required fields.'
text_forbidden_file_extension: 'It is not allowed to create / download / rename files with extension <b>. [Ext] </b>.'
text_forbidden_file_mime: 'It is not allowed to create / update / rename files of type MIME <b> [mime] </b>.'
text_forbidden_functionality: 'The functionality is available only in <strong> </strong> mode. <br/> <br/> To access all features, purchase a license and <cm-dialog-opener cm-dialog -auto-size "data-ca-target-id =" store_mode_dialog "> enable full mode </a>.'
text_forbidden_uploaded_file_extension: 'It is not possible to load the file with the extension <b>. [Ext] </b>. The allowed extensions are: <b> [exts] </b>'
text_forbidden_uploaded_file_size: 'It is not possible to load files larger than <b> [size] </b>'
text_form_cresecure_notice: 'Please complete your payment below. <br /> This is a secure page for payment.'
text_free_mode_activated: 'Your shop is now in free mode <br> <br> Please note that some features (ie some add-ons) are no longer available.'
text_frontend_theme: 'Theme of the Front'
text_full_mode_required: '<strong> Full mode </strong> required'
text_gate2shop_notice: '<p>Please set the following vendor settings in Gate2Shop :</p><p>Success, failed and canceled URL: <b>[result_url]</b></p><p>Back URL: <b>[back_url]</b></p>'
text_gd_not_avail: 'The GD <b> library is NOT INSTALLED </b> in your server. Please contact your host. <br> Only the "Table" type can be used for graphics.'
text_get_access_key_notice: 'If you do not know the access key, please enter your email address and it will be returned to you.'
text_gift_certificate_logo: 'Logo for <b> gift certificates </b>'
text_gift_cert_added_to_wishlist: 'Gift voucher added to wishlist'
text_gift_cert_amount_alert: 'The amount must be between [min] and [max]'
text_gift_cert_applied: 'Gift voucher has been applied'
text_gift_cert_cannot_delete: 'The gift voucher [code] can not be deleted because it is used in [ids] command (s).'
text_gift_cert_has_been_deleted: 'The gift voucher was successfully erased.'
text_google_notice: 'Please set the "Api callback URL" in the vendor center to <b> [return_url] </b>'
text_gr_desired_products: 'In this section you can define which products you would like to purchase by the event guests.'
text_hidpi_install: 'All new image downloads will be automatically reduced to improve the loading time on non-HiDPI devices. Existing images are not modified. It is recommended to load images in high definition.'
text_hidpi_support_enabled: 'HiDPI support is enabled'
text_hidpi_support_tooltip: 'What does HiDPI mean? HiDPI stands for High Resolution Dots Per Inch when referring to the display. For example the Retina display of the iPhone, iPad and MacBook Pro are in HiDPI.'
text_hidpi_uninstall: 'Some images can be reduced to improve display on non-HiDPI devices. You can reload images with better resolution.'
text_hsbc_notice: '<b> Client ID - </b> Your Client ID is a number between 0 and 999999999, or a valid alias of the form UK********CUR. <br> An alias name is sensitive to capital letters. <br /> <b> Caution for UNIX users: </b> <br /> Be careful to have the appropriate permissions to the following directory: chmod -R 755 [cart_dir]'
text_ideal_basic_notice: '- & nbsp; Click on <u> ''Security'' </u> in the <u> ''Profile'' section </u>. <br /> - & nbsp; <u> ''Notification'' </u> to <b> ''XML'' </b> <br /> - & nbsp; <b> [return_url] </b> <br /> - & nbsp; Put <u> ''Secret key'' </u>. A secret key is known only to the vendor and iDEAL.'
text_images_directory: 'The image storage directory <br> <br> It will be used if the image is specified without a path; It must be an absolute path, ex. : <br> [images_backup_dir]'
text_increase_points_in_use: 'The points used in the order have been added to the user''s account.'
text_innovative_notice: 'You can use the following account information for testing: <br /> username: <b> gatewaytest </b> <br /> password: <b> GateTest2002 </b>'
text_invalid_url: 'You have entered an invalid URL'
text_ips_denied: 'You are not allowed to access this area.'
text_ip_blocked_failed_login: 'Your IP was blocked after [number] connection errors.'
text_ip_is_blocked: 'Your IP address has been blocked.'
text_items_added: 'Articles have been added successfully'
text_light_box: Lightbox
text_linkpointc_notice: "Please login to LinkPoint Connect Admin and go to \"Customization\" -> \"Settings\".\_<br /> Please put the following URL in the \"Order Submission Form\" section: [return_url] <br /> Please uncheck the check boxes: <br /> - in the section 'Confirmation Page (\"Thank You\" URL '), in the' Failure Page ('Sorry' URL 'section), the <b> \"URL is a CGI Script \"</b> <br />"
text_links: 'Text links'
text_list_of_vendors: 'List of accounts <b> Vendors </b>:'
text_login_form: "<H4> You are not a member </h4> <p>\_</P>"
text_login_to_add_to_cart: 'This product can not be added to the cart because you are not logged in.'
text_mail_area_logo: 'Logo for <b> Invoices </b>'
text_mandatory_fields: '<span style = "text-decoration: underline;"> Are mandatory.</span>'
text_max_limit_of_parameters: "The maximum value of the \"limit\" field can not exceed 25 characters if the chart is not of the table type.\_The limit of this graph has been corrected."
text_mb_failed_order: "No response from Skrill has been received, please wait.\_Although the order has not yet been created in the shop, it is most likely that it was created and supported by Skrill.\_Please contact the store team and provide them with the order ID provided by Skrill."
text_mcpe_notice: "By default, the minimum and maximum values ​​for individual transactions in your account are displayed above: <table cellpadding = \"3\"> <tr> <td> <b> Limits </b> </td> <td> GBP </td> <td> <b> Minimum </b> $ 1,000 </td> <td> $ 1 </td> <td> _1500 </td> </tr> </table> Please contact your account manager to request a larger amount for your account. <p> Please specify these parameters in the merchant extranet .\_After the installation click on Account Management then: <br> - Response URL: [return_url] <br> - Return URL: [return_url] </p>"
text_min_order_amount_required: 'To continue the order process, the amount of the order'
text_min_products_amount_required: 'To validate your order the subtotal must exceed'
text_multiprofile_notice: "A user can have multiple profiles.\_For example, you can have a profile for personal orders, another for use in the office or another place for friends or family ..."
text_newsletter_sent: 'Newsletters have been sent successfully'
text_new_payment_was_created: 'A new payment has been created'
text_new_post_notification: 'This is a notification about a new post'
text_new_user_activation: 'The profile owned by "[user_login]" was newly created, you should check the details of this user (if required) and activate it using the following link: <br> [url]'
text_nothing_found: 'No results'
text_nothing_found_filter_message: "Sorry, we do not find any match for your search criteria.\_Please try a different combination of settings."
text_notification_to_inviteees: 'In this section, you can send event'
text_not_allowed_to_upload_file_extension: "It is not allowed to load a file with the extension <b>.\_[Ext] </b> here."
text_not_approved_products: "You have products waiting for validation.\_<a href=\"[link]\"> follow this link </a> to verify these products."
text_not_approved_vendors: "You have vendors waiting for validation.\_Thanks for <a href=\"[link]\"> follow this link </a> to check these vendors."
text_not_valid_cc_number: '<b> [cc_number] </b> is not a valid credit card number.'
text_not_valid_domain: '<b> [domain] </b> is not a valid domain name.'
text_not_valid_email: '<b> [email] </b> is not a valid e-mail address.'
text_not_valid_ip: '<b> [ip] </b> is not a valid IP address.'
text_no_active_promotions: 'No promotion available for the moment'
text_no_affiliate_assigned: 'No affiliate program has been assigned to your account.'
text_no_banners_found: 'No banner found'
text_no_conflicts: 'No conflicts found'
text_no_items_defined: 'None [item] defined'
text_no_matching_products_found: 'No products were found matching your search criteria'
text_no_matching_results_found: 'No results matching your search criteria'
text_no_orders: 'No order'
text_no_payments_needed: 'No payment information requested'
text_no_products: 'There are no products under this category'
text_no_products_defined: 'No product defined'
text_no_products_found: 'No products found'
text_no_shipments_found: 'No delivery information'
text_no_shipping_methods: "Unfortunately no delivery method is available for your destination.\_<br> Contact us to find a solution."
text_no_upgrades_available: 'No updates currently available'
text_objects_for_export: '<b> [total] </b> [name] will be exported.'
text_ogonedirect_notice: "Enter the SHA-1 signature twice: Here and in the Ogone configuration. <br /> <br /> For new accounts created since May 11, 2010, the new SHA is active automatically.\_<br/> Please do not forget to select \"Use new SHA signature policy\"."
text_ogoneweb_notice: 'Please set the following URL in the Technical Information section on the Ogone Configuration as a <b>Response Url</b> for all cases ("accepted", "on hold", "uncertain", "cancelled by the client", "too many rejections by the acquirer"): <b>[r_url]</b><br /> Also enter SHA-1 Signature both: here and on the Ogone Configuration. Then select Request Type: "Make this request in background and deferred".'
text_options_no_inventory: 'To create option combinations, at least one of the product options must have the "Stock" flag unchecked.'
text_order_backordered: "Your order is being resold.\_We will contact you as soon as possible."
text_order_placed_error: "Your payment has been refused by the payment operator.\_Please verify that all data entered is correct.\_<br /> Security 3D Secure may require the input of a code sent by SMS on your mobile phone."
text_order_placed_successfully: '<p> Your order has been successfully registered. </p> <p> You will receive a confirmation by e-mail very soon. <br/> A second email will be sent to you when the parcel is delivered to the carrier.</p> <p> In the space [my_account_link] you will be able to follow your order, make a return, a SAV, or modify your personal data </​​p>'
text_order_placed_successfully_for_edp: '<p> Your order has been successfully registered. </p> <p> You will receive an email confirmation shortly. </p> <p> In the space [my_account_link] Modify your personal data </​​p>'
text_order_repayed_successfully: 'Your order has been refunded successfully.'
text_order_saved_successfully: 'Your order has been saved successfully.'
text_order_status_has_not_been_changed: 'The command has not been changed because the user does not have enough points.'
text_order_status_notification: 'Please note that the notification of change of status will be sent according to the parameters of these statuses'
text_out_of_stock: 'Sold out'
text_pages_cloned: "The pages have been duplicated successfully.\_Below is the list of new pages."
text_pages_have_been_deleted: 'The pages have been deleted successfully.'
text_page_changed: "Are you sure you want to leave this page?\_The changes were not taken into account.\_Click OK to continue, or Cancel to stay on the page."
text_page_has_been_deleted: 'The page has been deleted successfully.'
text_page_loading: 'Loading ... Your request is processed, please wait.'
text_password_recovery_instructions_sent: 'If your email exists, we have just sent you the password recovery instructions.'
text_pay4later_notice: '<b> Note </b>: To track Pay4Later orders with shopping cart software, follow these steps: Click on the <u> ''</u>'' <u> ''</u> Setting to: <br /> <b> [verified_url] </b> <br /> - & nbsp; Set <u> ''Return URL (Decline) Decline_url] </u> <br /> - & nbsp; Set <u> ''Return URL (Refer)'' </u> & Nbsp; Set <u> ''Return URL (Cancel)'' </u> setting <cancel_url> setting to: <br /> <b> [process_url] </b> <br /> - & nbsp;'
text_paybox_notice: "<b> Note: </b> Please download the modulev2.cgi file from the Paybox server, copy it to <b> mode </b> and make it executable.\_<br /> <br /> <b> \_Callback_url] </b>"
text_payment_have_been_deleted: 'Payment has been successfully removed'
text_payment_have_not_been_deleted: 'Payment can not be deleted'
text_paysitecash_debug: 'Debug Information'
text_paysitecash_mode: Fashion
text_paysitecash_mode_debug_off: 'OFF'
text_paysitecash_mode_debug_on: WE
text_paysitecash_mode_live: Online
text_paysitecash_mode_test: Test
text_paysitecash_nocurrencies: Currency
text_paysitecash_nocurrencies_no: 'Allow the customer to choose his currency'
text_paysitecash_nocurrencies_yes: 'Do not show currency selection'
text_paysitecash_notice: 'Please add these URL fields to your Paysite Cash configuration: <br/> <br/> <b> URL: </b> <br/> [url] <br/> <br/> <b> Referer url : </b> <br/> <br/> <br/> <b> Canceled payment url: </b> <br/> [cancel_url] <br/> <br/> <b> Backoffice confirmation url: </b> <br/> [confirm_url]'
text_paysitecash_processor: Processor
text_paysitecash_site_id: 'Site ID'
text_payway_notice: '<b> [prepayment] </b> <br /> Browser Return URL: <b> [return] </b> <br /> Notification URL: <b> [notify] </b> <br /> Notification Post Type: leave empty </p>'
text_permissions_changed: 'Permissions changed'
text_piraeus_notice: '<b> Note: </b> Please refer to URL: ''<u> [referrer_url ] </u> ''<br /> Success URL:'' <u> [success_url] ''</u>'' <br /> Failure URL: : ''<u> [backlink_url] </u>'' <br /> IP address: ''<u> [ip_address] </u>'' <br /> <br /> Response Method: ''<br /> <br />'
text_points_cannot_applied_because_subtotal_redeemed: 'Points can not be applied because the subtotal is fully refunded.'
text_points_exceed_points_on_account: 'Points entered exceed the number of points in your account.'
text_points_exceed_points_that_can_be_applied: 'The points entered into the number of points that can be applied to this command.'
text_points_used_in_order: 'Points were used to'
text_point_in_account: 'The number of points in your account is'
text_position_updating: "Loading ... Positions have been updated.\_Please wait."
text_post_pended: 'Your message will be verified before being published.'
text_prettyphoto: Prettyphoto
text_price_dec_sign_delimiter: 'Decimal separator for product prices.'
text_products_added: 'The following products have been added to your favorites:'
text_products_cloned: "The products have been duplicated successfully.\_Below is the list of new products"
text_products_deletion_scheduled: 'Deletion of all products is in progress.'
text_products_have_been_deleted: 'The products have just been successfully removed.'
text_products_updated: 'The following products have been updated:'
text_products_updated_successfully: 'The products have been successfully updated'
text_product_cloned: "The <b> [product] </b> product was duplicated successfully.\_Now you can change the new product."
text_product_detailed_image: '(Optional, displayed in a pop-up window)'
text_product_file_has_been_deleted: 'The product file has been deleted'
text_product_filters_were_disabled: 'The <a href="[url]" target="_blank"> next filters </a> are disabled: [filters_list].'
text_product_has_been_deleted: 'The product has been successfully removed.'
text_if_attribute_has_been_updated: 'Your changes have been saved. Changes affecting the search will be applied within a maximum of one hour.'
text_product_thumbnail: '(Displayed in the detail pages and product list)'
text_profile_activated: 'Your account has now been activated, you can now log in using the username you received in the previous email.'
text_profile_benefits: '<h4> The benefits of your registration </h4> <ul> <li> You can log in at any time to know the status of your orders </li> <li> Customize your shopping </li> <li> Time for your purchases </li> </ul>'
text_profile_details: "<h4> Account details </h4> <p> On this page you can modify the personal data that will be used for your purchases. </p> <p> For the security of your account, </p> <ul> <li> A dictionary word from any language </li> <li> Words written in reverse, abbreviations, common errors </li> <li> Repeated sequences or characters.\_Examples: ********, 222222, abcdefg, or letters that follow each other on your keyboard. </li> <li> Personal information.\_Your name, birthday, security number or similar information.\_</li> </ul>"
text_profile_is_created: 'The account was successfully created.'
text_profile_is_updated: 'The account has been updated successfully.'
text_profile_should_be_approved: 'Your account has been successfully created, but must be activated by the store administrator before you can log in.'
text_promotions_group_condition: 'Si [set] ces conditions sont [set_value]'
text_proxypay_notice: "Validation script: & nbsp;\_& Nbsp;\_& Nbsp;\_<b> [validation_url] </b> - Script confirmation: & nbsp;\_& Nbsp;\_<b> [confirmation_url] </b> <br> Ok page: & nbsp;\_& Nbsp;\_& Nbsp;\_<B> [ok_url] </b> <br> Nok page: & nbsp;\_& Nbsp;"
text_qbms_notice: "<p> You should only use the \"test\" mode if you have a PTC account.\_</p> <p> - Subscribe URL: [response_url] <br /> - Change URL: [Response_url] <br /> - Cancel URL: [response_url] <br /> </p> <p> <b> Connection ticket </b> Merchantaccount.ptc.quickbooks.com/j/sdkconnection/connectionList?appid=[My_App_ID] (please replace the [My_App_ID] with your AppID number) and click the \"Use this connection\" button. </p> \"Signed certificate\" please refer to the <b> http://developer.intuit.com/QuickBooksSDK/chart.asp?id=496 </b> <br /> After you have created the certificates (Intuit CA & Client Signed ) Please do the following: <br /> 1.\_Extract the key from the CSR you created (these steps are applicable only if keytool was used) <br /> & nbsp;\_- Download the Java class to export Private key from the certificate: http://mark.foster.cc/pub/java/ExportPriv.java <br /> & nbsp;\_- Compile this class: \"javac ExportPriv.java\" <br /> & nbsp;\_- Export the Key to the file: \"java ExportPriv <keystore> <alias> <password>> exported-pkcs8.key\" <br /> 2.\_Save the client to the file (eg qbms_cert.txt).\_Save <br/> 4.\_Upload the resulting file using this form. <br />"
text_qty_discounts: 'Promotion sur la quantité'
text_recommendation_notes: "Your friend recommends this page.\_Please follow the link:"
text_recover_password: "<p> If you have forgotten your password, enter your email in the field and click <i> New password.\_</p> <p> You will receive a new password and a link to log in.\_You can then change your password.\_</p>"
text_recover_password_notice: "Please enter your username.\_The system will cancel your old password and send you a new one to your email address."
text_recover_password_title: 'New Password'
text_redactor: Editor
text_remember_access_key: 'Please keep the key for this event'
text_remove_additional_images: 'When importing new images for a product update, existing images will be deleted before importing.'
text_required_group_product: 'Please select a product for the group [group_name]'
text_reset_inventory_description: 'The quantities of all products will be removed.'
text_restore_question: 'Are you sure you want to restore the template?'
text_return_change_warning: 'The status of this return # [return_id] will be changed.'
text_sagepay_dir_notice: '<b> Note: </b> To track your orders on SagePay with shopping cart software, please enable the 3D Secure Fraud Checking option in your SagePay VSP Direct account.'
text_sagepay_notice: 'Use <b> testvendor </b> as <b> Vendor Name </b> for testing <br /> <p> Payment Type <b> PAYMENT </b> <br /> </p> <p> Transaction Type <b> DEFERRED </b> - Transactions will not be processed until the next day. Are NOT sent to the bank until there is a connection and validation (RELEASE) on the SagePay VSP administrator interface, then you have to find the transaction and click its validation. </p>'
text_secret_key_notice: 'For the security of the transaction, please complete this field.'
text_select_fields2edit_note: "You can edit multiple items at the same time.\_Select some items from the list above, uncheck the check boxes of the fields you want to change and click the \"Edit Selection\" button."
text_select_file: 'Select File'
text_select_product_image: 'Select image (Recommended size: 2000x2000 pixels)'
text_select_range: 'You can select the area for export (otherwise all [name] will be exported)'
text_share_product_features_tooltip: "Warning!\_Some data will not be copied during this operation.\_Filter categories will be lost even if you copy all categories.\_Please correct the categories manually."
text_share_product_filters_tooltip: "Warning!\_Some data will not be copied during this operation.\_The categories of product attributes will be lost even if you copy all categories.\_Please correct the categories manually."
text_share_promotions_tooltip: 'To share promotions make sure that the products are also shared (check the shared products below)'
text_shipping_packages_info: "To allow a precise calculation of your shipping costs.\_The products can be separated into several packages defined in the product parameters and in the configuration of the delivery modes.\_The following information is only useful internally."
text_shipping_rates_changed: 'Shipping costs have changed'
text_ship_to_billing: 'Order to be delivered to billing address'
text_shoppers_can_order_products: 'Visitors can now order these products.'
text_signup_for_subscriptions: 'Sign up for our newsletter!'
text_skrill_activate_quick_checkout_short_explanation: "Skrill Vendor Tools allows payments from credit cards and debit cards and over 60 options in more than 200 countries.\_Competitive rates are available at www.skrill.com."
text_skrill_activate_quick_checkout_short_explanation_1: "You made an activation request on [date].\_Thank you for taking into consideration the fact that the Vendor Tools Skill Check can take up to 72 hours.\_You will be contacted by Skrill when the verification is complete."
text_skrill_activate_quick_checkout_short_explanation_2: 'Please select a product for the group [group_name]'
text_skrill_currs_notice: 'If you select a currency that is not the base currency of your shop, please make sure you''re on <a href="http://en.wikipedia.org/wiki/ISO_4217"> ISO code 4217 </a> on The <a href="[link]"> currencies </a> page.'
text_skrill_email_is_not_registered: 'This email is not registered in Skrill.'
text_skrill_email_is_registered: 'This email is saved in Skrill.'
text_skrill_empty_input_data: "Many of the fields required for activation of Quick Checkout activation are missing.\_Please check."
text_skrill_logo_notice: 'If your HTTPS server is not launched, the logo will go through the HTTP protocol and users will see a "Partially Encrypted Connection" warning on the Skrill page.'
text_skrill_notice: 'To have access to the international Skrill network, Thank you for registering <a href="[register_url]" target="_blank"> Here </a> for a free account, if you do not already have one.'
text_skrill_payment_is_not_saved: 'To use this feature, please first save the payment method.'
text_skrill_secred_word_notice: 'The secret word must be in lowercase with no special character and must be at least 10 characters in length.'
text_skrill_secret_word_is_correct: 'The secret word is correct.'
text_skrill_secret_word_is_incorrect: 'Basic color'
text_skrill_support: '<b> Support: </b> <br /> <br /> Do you have any questions? <br /> Contact Skrill at <a href="mailto:"> <EMAIL> </a> or Telephone +44 (0) ************. <br />'
text_status_is_float: 'You can not use a time interval for the "Status" parameter because it is time-independent.'
text_storage_changed: 'Storage successfully modified'
text_store_mode_changed_to_free: "<p> The 30-day trial period is over.\_</p> <p> Note that only the basic features of eCommerce tools are available in this mode to use the full-featured software, Buy the commercial license and enter the key on the <a class=\"cm-dialog-opener cm-dialog-auto-size\" data-ca-target-id=\"store_mode_dialog\"> Licensing mode </a> </p>"
text_store_mode_changed_to_full: "<Strong> Full mode </strong> has been enabled.\_In this mode all e-commerce features are available."
text_store_mode_closed: 'The shop was <b> closed </b>'
text_store_mode_free: "Some features are not available.\_<br /> <br /> This mode can be activated at any time. <br /> <br /> This mode can be activated at any time."
text_store_mode_full: "Unrestricted access to all features.\_Put a valid license number to activate it.\_<br /> <br /> This mode can be activated at any time <br /> <br />"
text_store_mode_opened: 'The store was <b> opened </b>'
text_store_mode_trial: "Full access to functionality for 30 days, from activation day. <br /> <br /> After 30 days of testing, this mode will no longer be available.\_<br /> <br />"
text_subscriber_activated: 'Your email has been successfully activated'
text_subscriber_added: 'Your email has been successfully added to our mailing list'
text_subscriber_removed: 'Your email has been successfully removed from the newsletter'
text_successful_request: "Your request has been sent successfully.\_You will receive a reply by email as soon as possible."
text_success_subscription: 'Vous êtes inscrit avec succès à notre newsletter de mises à jour et promotions !'
text_tax_applied: 'The <b> [tax] </b> tax has been applied to all products'
text_tax_unset: 'The <b> [tax] </b> tax has been removed from all products'
text_template_changed: "The template has been modified.\_Please click \"OK\" to save the changes or \"Cancel\" to not keep these changes."
text_thaiepay_notice: 'Your return URL is <b> [return_url] </b>'
text_thank_you_for_post: 'thank you for your message'
text_thumbnail_manual_loading: 'Thumbnails will be generated automatically from the detailed images, but you can also ...'
text_tinymce: TinyMCE
text_topmenu_more: 'More ... [item] <i class="text-arrow"> & rarr; </i>'
text_topmenu_view_more: 'See more <i class="text-arrow"> & rarr; </i>'
text_totalweb_notice: 'Total Web Solutions Payment Page'
text_track_instructions_sent: 'Access instructions have been sent to your email address.'
text_track_request: 'You have requested information about the order you made on our online store.'
text_track_view_all_orders: 'To view all your orders, please click on the following link:'
text_track_view_order: 'To view the order # [order], please open this link:'
text_transaction_cancelled: 'The transaction was canceled by the customer'
text_transaction_declined: 'The transaction was canceled by the payment system or was canceled by the customer'
text_uc_another_update_process_running: "It is possible that another update is in progress.\_Please wait until the end of this update.If an error has occurred, you can delete the file [filename] and try the update again. <a href=\"[url]\"> Delete the blocked file And restart the update </a>"
text_uc_backup_database: 'The following tables in the database have been backed up.'
text_uc_backup_files: 'The following files have been backed up.'
text_uc_broken_package: "The package refresh appears to be suspended.\_Thank you for downloading it again."
text_uc_cannot_lock_upgrade_process: 'Failed to disable refresh at launch'
text_uc_cant_download_package: 'The updated package can not be downloaded'
text_uc_changed_files: 'These files have local changes and will be saved: resolve the conflicts at the end of the update.'
text_uc_conflicts: 'These files have local changes'
text_uc_db_right_needed: "Not enough permission to update the database.\_Please set the privileges of the user [db_user] in your BDD: [privileges]."
text_uc_edition_update_package_not_available: "Unfortunately, the update pack is not available at this time.\_Thank you try again later."
text_uc_emergency_restore: "Warning!\_Please copy and retain the URL below - you can use it if the update fails. <br /> <b> [href] </b>"
text_uc_enter_new_edition_license: 'Please enter the license number to update the software edition.'
'text_uc_failed_to decompress_files': 'Failed to extract files from archive'
text_uc_failed_to_backup_tables: 'The tables in the database were not saved.'
text_uc_failed_to_create_directory: 'Failed to create a new directory.'
text_uc_failed_to_decompress_files: 'Failed to Extract Archive Files'
text_uc_failed_to_ftp_copy: 'FTP Transfer Failure'
text_uc_ftp_cart_directory_not_found: 'The software was not found in the specified directory'
text_uc_ftp_connection_failed: 'FTP connection failed'
text_uc_ftp_connect_failed: "Unable to connect to the FTP server.\_Please check the host name"
text_uc_ftp_login_failed: "Unable to connect to the FTP server.\_Please check if the username and the password are correct."
text_uc_ftp_needed: 'FTP justifies access to the file and uses its own permissions automatically'
text_uc_has_conflicts: "Some of your files have local changes.\_You can find them by following this link."
text_uc_incorrect_upgrade_path: 'Incorrect path to update files'
text_uc_license_number_required: 'Please enter your CS-Cart license number'
text_uc_list_of_updates_missing: 'The list of updates is missing'
text_uc_new_license_input: 'New license number: [input]'
text_uc_non_writable_files: 'These files require permissions (manual or automatic via FTP)'
text_uc_no_enough_space_to_backup_database: 'Insufficient disk space to back up the database'
text_uc_no_ftp_module: "It seems that no FTP module is installed on your server.\_You can not use FTP if this module is not installed"
text_uc_unable_to_create_upgrade_folder: 'Failed to create an updated folder'
text_uc_unable_to_parse_uc_xml: 'Failed to parse the update descriptor uc.xml'
text_uc_unable_to_remove_file: 'Failed to delete file'
text_uc_unable_to_remove_packages_xml: 'Failed to remove package.xml descriptions'
text_uc_unable_to_remove_upgrade_lock: "Failed to delete file, update blocked.\_Please delete the file [file]."
text_uc_unable_to_update_list_of_installed_upgrades: 'Failed to update list of updates'
text_uc_upgrade_completed: "<p> Your store has been updated with success.\_</p>"
text_uc_upgrade_completed_check_and_open: "<p> Your store will be closed during refresh.\_We recommend that you check your shop after updating and only after reopening it.\_</p> <p> You can open the shop by unchecking the Store (\"storefront\") settings that are closed under Settings → General. </p>"
text_uc_upgrade_log_file_not_writable: 'Unable to write to update log file'
text_uc_upgrade_needed: 'You must implement the <b> [to_version] </b> version before applying this updated package (your version is <b> [your_version] </b>).'
text_uc_upgrade_not_selected: 'No refresh package is selected'
text_uc_upgrade_reverted: 'The refresh was successfully returned.'
text_uc_you_may_start_installation: 'You can start the installation'
text_ult_product_store_field_tooltip: "Owner.\_The owner only can edit all the fields of a shared product."
text_unable_to_parse_xml: 'Failed to parse the XML structure'
text_unsubscribe_instructions: 'We respect your privacy and do not send unsolicited mail to customers who have not asked for our news. You received this email because you requested our free updates and promotions. <br> <br> \n\n If you want to unsubscribe, please follow the link below:'
text_unsupported_currency: "The currency is not supported by the site's payment system.\_Please contact the administrator to resolve the issue."
text_vendor_profile_changes_notice: "Are you sure you want to update your profile?\_Your company information will no longer be available to customers until an administrator validates them."
text_worldpay_notice: 'Please use the following URL as the return URL: <br /> <b> [return_url] </b>'
text_your_events: 'Events you have created'
text_you_have_already_filled_this_poll: 'You have already answered this survey'
text_zipcodes_wildcards: "You can use wildcards in this field: <br> <b> '?' </b> - any single character <b> '*' </b> Example: <br> <b> 98? 78 </b> & nbsp; & nbsp; (corresponds to 98878, 98378, 98978, etc.) <b> 12 </b> & nbsp; & nbsp;\_(Corresponds to 12345, 12876, 12098, etc.)"
thanks_for_voting: 'Thank you for taking the time to complete this survey.'
theme: Theme
themes: Themes
theme_directory: 'Theme directory'
theme_editor: 'Theme editor'
theme_editor.background: Background
theme_editor.backgrounds: Backgrounds
theme_editor.background_color: 'Color Background'
theme_editor.base_color: 'Basic color'
theme_editor.body_font: Body
theme_editor.browse: Navigate
theme_editor.buttons_font: Buttons
theme_editor.close: 'Close theme editor'
theme_editor.colors: Colors
theme_editor.color_gen_algorithm: 'Color Combination'
theme_editor.color_gen_analogic: Analog
theme_editor.color_gen_base_color: 'Base color'
theme_editor.color_gen_contrast: Contrast
theme_editor.color_gen_dark_pastel: 'Dark Pastel'
theme_editor.color_gen_default: Defect
theme_editor.color_gen_light_pastel: 'Light pastel'
theme_editor.color_gen_monochromatic: Monochromatic
theme_editor.color_gen_pale: Clear
theme_editor.color_gen_pastel: Pastel
theme_editor.color_gen_title: 'Generating colors'
theme_editor.color_gen_triad: Trio
theme_editor.color_gen_variant: Variant
theme_editor.color_gen_vcontrast: Contrast
theme_editor.content_bg: Content
theme_editor.css: 'Custom CSS'
theme_editor.customize: Personalize
theme_editor.decorative_color: Decorative
theme_editor.discount_label: 'Label promotion'
theme_editor.error_preset_exists: "The preset with this name already exists.\_Please choose another name."
theme_editor.favicon: Favicon
theme_editor.favicon_size: 'The size of the Favicon must be 16x16 px'
theme_editor.fixed: Repaired
theme_editor.font: Police
theme_editor.fonts: Fonts
theme_editor.font_color: Police
theme_editor.footer: Footer
theme_editor.footer_text: 'Text footer'
theme_editor.full_width: Width
theme_editor.general: General
theme_editor.general_bg: General
theme_editor.gift_cert: 'Gift Certificates'
theme_editor.gradient: Tilt
theme_editor.header: Header
theme_editor.headings_font: 'Police Head'
theme_editor.hide_show: 'Hide / Show theme editors'
theme_editor.incorrect_preset_name: 'The preset names can not contain the following characters: "/ #%? *:; {} " "'
theme_editor.in_stock: 'In stock'
theme_editor.links_font: Connections
theme_editor.link_color: Connections
theme_editor.logos: Logos
theme_editor.mail: E-mail
theme_editor.main: 'Main content'
theme_editor.max_image_size: 'The image must not exceed 200 kB.'
theme_editor.menu_color: Menu
theme_editor.menu_links_color: 'Link to the links menu'
theme_editor.middle_bg: Middle
theme_editor.no_repeat: 'No repetition'
theme_editor.off: 'OFF'
theme_editor.on: WE
theme_editor.out_of_stock: 'Sold out'
theme_editor.pattern: Model
theme_editor.position: Position
theme_editor.presets: Presets
theme_editor.preset_data_cannot_be_saved: "The preset data can not be saved.\_You need to set the write permissions on the <b> [theme_dir] </b> directory to fix this problem"
theme_editor.preset_name: 'Name (preset)'
theme_editor.price: Price
theme_editor.price_font: 'Price Tag'
theme_editor.primary_button: 'First button'
theme_editor.repeat: Repeat
theme_editor.repeat_x: 'Repeat x'
theme_editor.repeat_y: 'Repeat y'
theme_editor.reset_backgrounds: 'Clear background (backgrounds)'
theme_editor.reset_colors: 'Clear colors'
theme_editor.reset_css: 'Clear CSS'
theme_editor.reset_fonts: 'Clear fonts'
theme_editor.reset_general: 'Clear general'
theme_editor.reset_logos: 'Clear logos'
theme_editor.rounded_corners: 'Smooth it out'
theme_editor.scroll: Scrolling
theme_editor.secondary_button: 'Secondary button'
theme_editor.sidebar: Sideboard
theme_editor.text_align: 'Aligning Text'
theme_editor.text_close_editor: 'Are you sure you want to exit the text editor?'
theme_editor.text_close_editor_unsaved: "You have unsaved changes.\_Are you sure you want to leave the editor?"
theme_editor.text_reset_changes: 'All changes after the last save will be deleted'
theme_editor.theme: Theme
theme_editor.top_panel: 'Top panel'
theme_editor.top_panel_links: 'Links Top panel'
theme_editor.top_panel_text: 'Text Top panel'
theme_editor.transparent: Transparent
theme_editor.upload_image: 'Load an image'
theme_editor_mode: 'Theme editor mode'
the_test_transaction: 'This is a TEST transaction'
this_day: 'This day'
this_month: 'This month'
this_week: 'This week'
this_year: 'This year'
ths_sign: 'This symbol'
thumb: Thumbnail
thumbnail: Thumbnail
thumbnails: Thumbnails
thumbnails_removed: 'Thumbnails have been removed'
thumbnail_manual_loading_link: 'Load them manually'
thumbnail_width: 'Thumbnail width with scrolling'
tier_account: 'Third parties'
time: Time
timeout: 'Time limit'
timestamp: 'Weather (Timestamp)'
time_interval: 'Time interval'
time_unlimited_download: 'Unlimited download time'
title: Title
titles_by_visits: 'Titles by visits'
tmpl_copyright: 'Information Copyright'
tmpl_logo: 'Logo of the shop'
tmpl_payment_icons: 'Image of the payment system'
tmpl_quick_links: 'Quick Links'
tmpl_search: 'Search field'
tmpl_subscription: 'Newsletter subscription form'
to: at
today: Today
today_events: 'The events of today'
tools: Tools
tools_addons_addons_count: 'Modules account'
tools_addons_current_state: 'Current state'
tools_addons_no_addons: 'No module'
tools_addons_no_third_party_addons: 'No third party addons'
tools_addons_restore_defaults: 'Reset the default values'
tooltip: 'Tips and Tricks'
too_many_attribute_index_notice: 'You can not use more than 50 attributes in the search (see checkbox "%feature_is_searchable%") (%total_features% enabled).'
reach_quota_attribute_index_error: 'You will soon reach the total number of 50 attributes in the search (%total_features% enabled).'
over_quota_attribute_index_error: 'You have exceeded the total number of 50 attributes in the search (%total_features% enabled).'
too_many_options: "You have selected too many different option types in the allowed combinations.\_It is not possible to display the table of variants"
top_container_not_used: "This container is not used.\_The default Top Panel container will be used instead.\_Set this as the default to use the Top panel container at all locations."
top_panel: Top
total: Total
total_ttc: Total including tax
total_ht: Total without taxes
totally_paid: 'Total paid'
totals: Totals
total_amount: 'Total amount'
total_amount_ttc: Total amount including tax
total_amount_ht: Total amount without taxes
total_amount_due: 'Total amount due'
total_commissions: 'Total commissions'
total_cost: 'Total cost'
total_items: 'Number of elements'
total_list_price: 'Total Price List'
total_pages_viewed_per_visitor: 'Total page views by the visitor'
total_payouts: 'Total payments'
total_period_payout: 'Total payments'
total_price: 'Total price'
total_product_cost: 'Total Cost of Product'
total_results: 'Total results'
total_unpaid_balance: 'Total not cashed'
balance_results: 'Balance'
to_all_subcats: 'Apply to all subcategories'
to_date: Dated
to_fixed: 'New price'
to_percentage: 'Percentage of original price'
tpe: TPE
track: 'To follow'
tracking_num: 'Tracking number #'
tracking_number: 'Tracking number'
tracks: Search
track_shipment: 'Track the parcel'
track_my_order: 'Track my order'
track_orders: 'Follow my commands'
track_request_subj: 'Tracking control'
track_without_options: 'Follow (without option)'
track_with_options: 'Follow (with options and variations)'
transactiondatetime: 'Date of transaction'
transaction_approved: 'Validated transaction'
transaction_cancelled: 'Transaction canceled'
transaction_declined: 'Transaction denied'
transaction_id: 'ID Transaction'
transaction_key: 'Transaction Key'
transaction_mode: 'Transaction mode'
transaction_mode_affiliate: Affiliates
transaction_mode_contact_only: 'Contact only'
transaction_mode_inherited: Inherited
transaction_mode_overridable: 'Modifiable transaction mode'
transaction_mode_transactional: Transactional
transaction_password: 'Transaction Password'
transaction_type: 'Transaction Type'
transaction_url: 'Transaction URL'
translate: 'Switch to translation mode'
translate_mode: 'Translation mode'
translations: Translations
translation_key: 'Translation key'
translation_mode: 'Translation mode'
translation_progress: Progress
transparent: Transparent
tranticket: 'Transaction ticket (TranTicket)'
tree: Tree
trial: Test
trial_mode_mve_disabled: 'Test mode is no longer available, you do not have an activated license for this installation.'
trial_mode_ult_disabled: 'The test mode can not be activated'
trial_notice: '<br /> <br /> <br /> <br /> <br /> <br /> <br /> <br /> <br /> <br /> <br /> <br /> <br /> <br /> <br /> <br /> <br /> <br /> If you want to stay in touch, ''Try and permanently delete this message, buy the license and <a class="cm-dialog-opener cm-dialog-auto-size" data-ca-target-id="store_mode_dialog"> enable full mode </a>.'
'true': 'TRUE'
ttc: 'Including tax'
ttc_clean_up_all_locations_on_import: 'Delete all existing placements before importing the file locations.'
ttc_feature_display_on_faceting: 'If enabled, the attribute is displayed in the search facets'
ttc_feature_is_searchable: 'If enabled, the search will take this attribute into account'
ttc_page_title: 'Title of the page displayed on the browser'
ttc_storefront_url: "All store fronts must be associated with the same IP address.\_Note: Go to \"All stores\" to edit."
ttc_transaction_mode_overridable: "By checking this box, products can ignore the transaction mode of their category and use another.\_When the checkbox is unchecked, products in this category are forced to be in the selected mode."
ttc_usergroups: 'At least one item must be selected'
ttc_type: 'type'
ttc_status: 'status'
ttc_users: 'users'
ttc_period: 'Period'
ttc_related_product_description: 'Related product description'
ttc_select_dates: 'Select dates'
tts_activate_menu_tab_for: 'The menu item will be displayed as enabled for the specified sending.'
tts_generate_submenu: 'The submenu will include the child elements of the selected object.'
tts_link_text: "Target URL.\_Can be an external URL, the URL of the shop or a referral"
tt_addons_news_and_emails_views_mailing_lists_update_from_email: 'Email from the sender'
tt_addons_news_and_emails_views_mailing_lists_update_from_name: 'Name of sender (ie name of company).'
tt_addons_news_and_emails_views_mailing_lists_update_reply_to: 'Email to send to "respond to"'
tt_addons_news_and_emails_views_newsletters_update_more_subjects: 'If several topics are specified, the subject of the email is randomly selected from the list.'
tt_addons_news_and_emails_views_newsletters_update_users: 'If the message contains an unsubscribe link, it will be empty for those users.'
tt_addons_polls_hooks_pages_tabs_content_post_poll_results: 'Message displayed after poll'
tt_addons_statistics_views_statistics_components_search_form_exclude: 'If enabled, options will be excluded from search results.'
tt_addons_statistics_views_statistics_components_search_form_limit: 'Maximum number of items in the search result'
tt_addons_twigmo_settings_settings_twgadmin_enable_geolocation: 'If enabled, the delivery and billing address fields will be linked to a location-based service on the record and to the verification pages.'
tt_addons_twigmo_settings_settings_twgadmin_home_page_content: "If \"Home Desktop Block\" is selected, all of the block types on the supported home page on the Mobile Front-End Home page are displayed.\_If 'Twigmo home page blocks' is selected, Twigmo homepage blocks are displayed (configured in 'Design' -> 'Blocks' -> 'Twigmo home page').\_If a category is selected, only its products will be displayed on the Front-End Mobile homepage."
tt_addons_wizacha_declinations_hooks_product_options_w_inventory_override_allowed_combinations: 'Back to the choice of declinations.'
tt_addons_wizacha_declinations_hooks_product_options_w_inventory_override_global_update: 'Allows to fix the stock and the price for all the product variants'
tt_addons_wizacha_declinations_hooks_product_options_w_inventory_override_w_update_amount: "The table displays the quantities (stock) directly.\_Click on the \"+\" button to access other information (prices, pictures, ...)."
tt_addons_wizacha_orders_hooks_products_w_taxes_after_post_w_green_tax: "Default is 0 €.\_Attention: the amount of eco-participation is included in the price inclusive of VAT."
tt_addons_wizacha_shippings_hooks_companies_product_details_fields_pre_w_supplier_ref: "Not required.\_Second reference possible, not used by the site, this reference is your own and can be used to find your products more easily."
tt_views_block_manager_update_block_override_by_this: 'If you save the block with this option, the contents of the current block will erase the contents of this block wherever it is used in the shopping cart.'
tt_views_block_manager_update_block_width: 'The width of the block works for blocks placed in horizontal groups, in other cases, this parameter does not work.'
tt_views_block_manager_update_location_default: "A location must be set by default.\_Top (Top) and Low (Bottom) containers will be used in all locations."
tt_views_cart_components_carts_search_form_online_only: 'If enabled, search results will include only online users at this time.'
tt_views_categories_update_product_details_layout: 'By default, the template defined in the store''s appearance settings (storefront) is used'
tt_views_currencies_update_after_sum: 'If enabled the symbol of the currency is used after the sum.'
tt_views_currencies_update_decimals: 'Number of decimals after the decimal point.'
tt_views_currencies_update_dec_sign: 'Decimal Separator'
tt_views_currencies_update_ths_sign: 'Thousands separator'
tt_views_database_manage_backup_data: 'If enabled, the database backup file includes the current data table.'
tt_views_database_manage_backup_schema: 'If enabled, the backup file includes the table structure.'
tt_views_exim_export_output: 'Choose an action on the file: "Direct Download" - to save the file on a local computer, "Screen" - to display the contents of the file, "Server" - to save the file in the file system of the server.'
tt_views_languages_manage_language_code: 'The code of the language (2 letters)'
tt_views_languages_update_country: 'The flag of the country will be used as a pictogram of the language.'
tt_views_orders_components_orders_search_form_customer_files: 'If enabled, search results will contain commands for which clients have loaded their own files.'
tt_views_payments_update_surcharge_title: 'Leave blank to use ''Payment overcharge'' as title'
tt_views_payments_update_taxes: 'If a surcharge is made on vendors, the taxes are not calculated.'
tt_views_products_components_products_shipping_settings_items_in_box: "Use this field to define the minimum and maximum number of products that can be sent in separate packages.\_Enter a number other than 0 and set the packaging dimensions below."
tt_views_products_components_products_shipping_settings_weight: 'Non-downloadable products with a zero weight are treated as having the least possible weight of 0.'
tt_views_products_update_available_since: "Date of release of the product.\_If no date: immediate availability."
tt_views_products_update_categories: 'Category under which the product will be referenced (eg computers> tablets)'
tt_views_products_update_downloadable: 'Service or intangible that does not require delivery.'
tt_views_products_update_file_file: 'Click to import, either locally or from a URL.'
tt_views_products_update_file_license_agreement: 'License Agreement: If the file has a license to validate, copy it there in the field provided and tick the box "Validation required"'
tt_views_products_update_file_max_downloads: 'Number of downloads allowed for each purchase.'
tt_views_products_update_file_name: 'Name of the file sent to the buyer.'
tt_views_products_update_file_readme: 'If you wish to send instructions to the buyer.'
tt_views_products_update_full_description: "Present at the bottom of the sheet produced or accessible after clicking on \"detailed description\".\_Images allowed."
tt_views_products_update_inventory: 'The stock is available for editing only when the "Enable Inventory Tracking" option is enabled'
tt_views_products_update_in_stock: 'Quantity of products available for sale.'
tt_views_products_update_list_price: 'Manufacturer''s Suggested Retail Price'
tt_views_products_update_max_price_adjustment: 'Let it empty to disable price adjustment on this product'
tt_views_products_update_name: 'Name under which the product will be presented to the visitors of the site (ex: iPad 2 black 16GB wifi) .100 characters max.'
tt_views_products_update_out_of_stock_actions: "Note: The 'buy ahead' option requires a positive number of products, while the 'Register for a notification' option does not ask for a positive number.\_Also, the option 'Register for a notification' is not applied to the tracked products"
tt_views_products_update_price: 'Sales price incl. VAT (incl. VAT).'
tt_views_products_update_product_details_layout: 'By default, the template defined in the main product category settings is used'
tt_views_products_update_short_description: "At the top of the product sheet (25 to 30 words).\_Images not allowed."
tt_views_products_update_sku: "EAN code.\_If it does not exist, unique reference number."
tt_views_products_update_taxes: "VAT rate, compulsory field.\_Self-entrepreneur: put a rate of 0%."
tt_views_products_update_time_unlimited_download: 'This box appears if you have checked "service or downloadable", validating it you remove the time limit for downloading the file (fixed by default to 14 days).'
tt_views_product_features_update_feature_display_on_product: 'If enabled, the attribute of the product is displayed on the product detail page'
tt_views_product_filters_update_display_more_variants_count: 'The remaining variants will be displayed in different pages.'
tt_views_product_filters_update_display_variants_count: 'The remaining variants will be hidden behind the "Plus" link'
tt_views_product_options_update_incorrect_filling_message: 'Message displayed in case of incorrect entry.'
tt_views_product_options_update_inner_hint: 'A note that indicates the value to enter'
tt_views_product_options_update_inventory: 'If enabled, the option is taken into account when you stock the products'
tt_views_product_options_update_regexp: 'Regular expression matching a pattern to match / match.'
tt_views_product_options_update_required: 'If enabled, this option is mandatory when entering.'
tt_views_promotions_update_stop_other_rules: 'Si activé, les autres promotions ne seront pas activées'
tt_views_sales_reports_table_time_interval: 'Period to be analyzed (day, week, month, year).'
tt_views_sales_reports_update_table_dependence: "Parameter to select values.\_It should match the values ​​to be displayed (ie Cost of Products - Total Cost or Number of Products - Number of Items)."
tt_views_sales_reports_update_table_limit: 'Maximum number of graphics components.'
tt_views_site_layout_logos_alt_text: 'Leave blank to use the company name as a replacement text'
tt_views_taxes_update_regnumber: 'Registration number of this tax in the shop.'
turkish: Turkish
twgadmin_access_id: 'Access ID'
twgadmin_backup_files: 'Backup Twigmo files.'
twgadmin_backup_settings: 'Saving Twigmo Module Settings'
twgadmin_basic_skin: 'Basic envelope'
twgadmin_both_tablet_and_phone: 'For telephones and tablets'
twgadmin_cant_create_file: "The [file] file can not be created.\_Please manually create it in [path] (only if you need custom twigmo mobile skin)."
twgadmin_checking_permissions: 'Checking file permissions ...'
twgadmin_check_for_updates: 'Check for updates'
twgadmin_connect: Connecting
twgadmin_connect_notification: "The <a href=\"[addon_link]\"> Twigmo </a> module is not connected to the <a href=\"http://www.twigmo.com\" target=\"_blank\"> Twigmo service </a> .\_Launch it to activate a mobile version of your store for iOS, Android and Windows Phone devices."
twgadmin_connect_to_first: 'Please connect your store to Twigmo first'
twgadmin_connect_to_first_ult: "Your store is not yet connected to the Twigmo service.\_Please ask the root shop administrator to do so."
twgadmin_connect_your_store: 'Connect your store to Twigmo service'
twgadmin_download_twigmo: "Download Twigmo package.\_Please wait ..."
twgadmin_edit_these_blocks: 'Edit these blocks'
twgadmin_enable_geolocation: 'Activate geolocation'
twgadmin_error_cannot_connect_store: "Unable to connect to the Twigmo service.\_Please try again later"
twgadmin_error_validator_required: 'This field ''[field]'' is mandatory'
twgadmin_fail_create_user: 'Failed to create user'
twgadmin_fail_post_order: 'Command Failure'
twgadmin_hide_header: 'Hide Header'
twgadmin_homepage: 'Twigmo Homepage'
twgadmin_home_page_blocks: 'Bloc Home Office'
twgadmin_home_page_content: 'Home page'
twgadmin_install_addon: 'Installing the Twigmo ...'
twgadmin_manage_settings: 'Account Information'
twgadmin_manage_storefront_settings: 'Front-End Mobile Settings'
twgadmin_mobile_favicon: 'Picto iOS home screen device'
twgadmin_mobile_logo: 'Picto Mobile Front-End'
twgadmin_mobile_version: 'Mobile version'
twgadmin_more_than_year: 'More than a year'
twgadmin_never: Never
twgadmin_no_files_permissions: "Error: The Twigmo module files can not be modified.\_Please verify and correct the permissions file manually or [link] provide your FTP access [/ link] (if already filled in, make sure the FTP user has enough rights to edit the file on the server) ."
twgadmin_object_was_not_found: 'The [object] data was not found'
twgadmin_only_req_profile_fields: 'Show only marked profile fields as required'
twgadmin_on_social: 'Twigmo on social media'
twgadmin_order_via_twigmo: '[This order was placed via Twigmo Mobile Front-End]'
twgadmin_phone: 'Only for telephone'
twgadmin_phpmod_required: "The PHP extension module [php_module_name] is required for some operations of the Twigmo module.\_Thank you for installing it."
twgadmin_reinstall: "The version of the files in the Twigmo module does not match the version in the database, so your mobile version has been disabled.\_Please reinstall the Twigmo module (click on uninstall and then install on the list of modules) to reactivate it"
twgadmin_restore_settings: 'Sort Twigmo Module Settings'
twgadmin_select_skin: Skin
twgadmin_select_store: 'Please select a shop'
twgadmin_tablet: 'Only for tablets'
twgadmin_text_store_connected: 'Your shop is connected to the Twigmo service successfully!'
twgadmin_text_updates_available: "A new version of the Twigmo module is available!\_<a href=\"[link]\"> Click here to update </a>."
twgadmin_tw_home_page_blocks: 'Twigmo Blocks Home Page'
twgadmin_uninstall_addon: 'Uninstalling the Twigmo ...'
twgadmin_update_files: 'Copying new Twigmo files ...'
twgadmin_upgrade_addon: 'The Twigmo update has begun'
twgadmin_url_for_facebook: 'URL of your Facebook page'
twgadmin_url_for_twitter: 'URL of your Twitter page'
twgadmin_url_on_appstore: 'URL of your iTunes App Store App'
twgadmin_url_on_googleplay: 'URL of your Google Play app'
twgadmin_use_mobile_frontend: 'Using Mobile Front-End'
twgadmin_wrong_api_data: 'Incorrect API data'
twgadmin_wrong_api_object_data: 'Incorrect data'
twgadmin_wrong_payment_info: 'Failure of order: Incorrect payment data'
twgadmin_wrong_shipping_info: 'Failure of order: Wrong delivery data'
twg_app_for_android: 'App for Android'
twg_app_for_ipad: 'App for iPad'
twg_app_for_iphone: 'IPhone App'
twg_billing_is_the_same: 'Same billing address'
twg_btn_load_more_items: 'Load more'
twg_btn_log_in_to_checkout: 'Login to Order'
twg_cart_info: '[Amount] ([total])'
twg_checkout_terms_n_conditions: 'I accept the terms and conditions'
twg_configurable_via_desktop: "This is a configurable product.\_It can be configured only via [linkOpen] the site office [linkClose]."
twg_contact_twigmo_support: 'Contact Twigmo Support'
twg_desktop_site: 'Office of the site'
twg_facebook: Facebook
twg_is_logged_in: Connected
twg_lbl_copy_from_billing: 'Deliver to the same address'
twg_lbl_out_of_stock: 'Sold out'
twg_login_to_add_to_cart: 'This product can not be added to the cart because you are not logged in.'
twg_min_product_quantity: 'Minimum order quantity'
twg_msg_cc_invalid: 'Please enter a valid credit card number.'
twg_msg_dateiso_invalid: 'Please enter a valid date (YYYY-MM-DD).'
twg_msg_date_invalid: 'Please enter a valid date'
twg_msg_digits_required: 'Please enter only numbers'
twg_msg_email_invalid: 'Please enter a valid email address.'
twg_msg_extension_invalid: 'Please enter a value with a valid extension'
twg_msg_field_invalid: 'Thank you for reviewing this field'
twg_msg_field_required: 'This field is required'
twg_msg_fill_required_fields: 'Please fill in all required fields'
twg_msg_max_length: 'Please do not put more than {0} characters.'
twg_msg_max_value: 'Please enter a value less than or equal to {0}.'
twg_msg_min_length: 'Please enter at least {0} characters'
twg_msg_min_value: 'Please enter a value greater than or equal to {0}.'
twg_msg_number_invalid: 'Please enter a valid number'
twg_msg_orders_were_placed: 'Your orders have been placed! <br /> The order numbers are'
twg_msg_order_was_placed: 'Your order has been placed! <br /> The order number is'
twg_msg_range_length: 'Please enter a value between {0} and {1} characters.'
twg_msg_range_value: 'Please enter a value between {0} and {1}.'
twg_msg_same_value_required: 'Please enter the same value'
twg_msg_url_invalid: 'Please enter a valid URL.'
twg_only_via_desktop: 'This product can be added to cart only by [linkOpen] the website''s office [linkClose].'
twg_post_request_fail: 'POST query failed'
twg_product_added_to_cart: 'Product added to shopping cart'
twg_requrring_via_desktop: "This is a subscription product.\_Its subscription program can be selected [linkOpen] here [linkClose]."
twg_review_and_place_order: 'View and place order'
twg_share: Share
twg_share_on_social: 'Share on social networks'
twg_sign_in_to_checkout: 'Login to Order'
twg_twitter: Twitter
twg_view_cart_checkout: 'View Cart and Order'
twg_visit_our_mobile_store: 'Visit our mobile site'
twitter_link: 'https://twitter.com/'
type: Type
type_comment: 'Your comment :'
type_comments_here: 'You can leave your comment here'
uc_ok: '[OK]'
uk_cookies_law: "Cookies are used on this site to provide a better user experience.\_If you continue, you agree with receiving cookies for this site.\_<span class=\"button button-wrap-left\"> <a href=\"[url]\"> OK </a> </span>"
ult_overwrite_variables: 'Overwhelm the values ​​of the shop'
ult_pdf_info: 'Use these links to download the price list in PDF format'
ult_shared_with: 'Shared with:'
ult_share_users_setting_disabled: 'The setting can not take this value, identical email addresses are saved in your store.'
ult_xml_info: 'Use this link to download prices in XLS format (MS Excel)'
unable_delete_vendor_orders_exists: "Unable to delete this vendor account because there are orders containing products from this vendor in the database.\_To remove the vendor please remove the linked orders first."
unable_delete_vendor_persons_exists: "Unable to delete this vendor account because there are persons attached.\_To remove the vendor please remove the linked persons first."
unable_to_assign_usergroup: "Unable to assign user group.\_Please contact the shop administrator."
unable_to_check_license: 'Unable to check license number.'
unable_to_delete_setting_description: 'Unable to remove parameter description: [reason]'
unable_to_delete_setting_variant: 'Unable to remove variants: [reason]'
unable_to_read_resource: 'Unable to read resource: [file]'
unable_to_update_setting_description: 'Unable to edit description: [reason]'
unable_to_update_setting_value: 'Unable to change the value: [reason]'
undefined: indefinite
ungroupped_features: 'Unbundled Attributes'
uninstall: Uninstall
unique_html_block: 'HTML Single Block'
unit: Unit
united_kingdom: Britain
unit_price: 'Unit price'
unknown: Unknown
unknown_category: 'Unknown category'
unknown_server_response: 'Unknown server response'
unmark: Uncheck
unregistered_customer: 'Unregistered customers'
unselect_all: 'All uncheck'
unset_tax_to_products: 'Remove selected taxes from all products'
unsubscribe: unsubscribe
unsubscribed_success: 'Successfully unsubscribe'
unsubscribe_link: 'Unsubscribe Link'
up: Top
update: Update
updates_subscription: 'Subscribe to updates and promotions'
update_categories: 'Update categories'
update_class: 'Update Class'
update_customer_info: 'Update Customer Information'
update_datafeed: 'Editing the data stream'
update_event: 'Update Event'
update_for_all_act: "The new value will be saved for ALL shops that share this item.\_Click to save only for the owner store."
update_for_all_dis: "The new value will be saved ONLY for the shop owner of the item.\_Click to save for all shops that share this item."
update_for_all_hid_act: 'Click to cancel the field update'
update_for_all_hid_dis: 'Click to enter a new field to update in all stores when saving the item.'
update_group: 'Update Group'
update_products: 'Update the product (s)'
update_profile: 'Update Profile'
update_profile_notification: 'Updating your profile'
update_profile_notification_header: "Your profile has been updated, you will find hereafter the information about you.\_You can modify them at any time in your \"my account\" space."
update_status: 'Update status'
update_text: 'Update text'
update_totals_and_inventory: 'Total update and stock'
updating: 'Currently being updated'
updating_links: 'In the process of updating the links'
upgrade: Update
upgrade_center: 'Update Center'
upgrade_center.filehash_check_failed: "Can not be refreshed due to an integrity check error.\_Please contact the support team."
upgrade_center.upgrades_are_not_available_in_trial: 'Updates are available only in <strong> Full </strong> and <strong> Free </strong> license modes. <br /> <br /> The license mode can be changed in settings → <a class="Cm-dialog-opener cm-dialog-auto-size" data-ca-target-id="store_mode_dialog"> License mode </a>.'
upgrade_flash_player: 'You need to upgrade your Flash Player'
upgrade_is_not_avail: "This update is not available for your CS-Cart license.\_Please contact <a href=\"http://www.cs-cart.com\"> CS-Cart support team </a> to update your license status."
upgrade_packages_list: 'Refresh the list of packages'
upload: Download
upload_another_file: 'Load another file'
upload_a_video: 'Submit a video'
upload_file: 'Download File'
upload_to_ftp: 'Upload to FTP server'
ups: 'United Parcel Service (UPS)'
url: URL
url_dispatch_part: 'The value of the dispatch parameter of the URL'
used: Used
user: User
usergroup: 'User group'
usergroups: 'User Groups'
usergroups_menu_description: 'Lists of existing user groups in your store'
usergroup_activated: 'User group has been activated'
usergroup_disactivated: 'The user group has been disabled'
usergroup_privileges: 'User group privileges'
usergroup_request_by_customer: 'Customer group request'
username: username
users: Users
users_accounts: 'User Accounts'
users_approval: Moderation
users_approval_menu_description: 'Filter users that have not been moderated'
users_carts: 'User baskets'
users_carts_menu_description: 'List of products that have not been purchased and reasons'
users_menu_description: 'Manage user accounts saved in your store'
users_online: 'Online users'
users_type: 'Type of users'
user_account_info: 'User account'
user_account_information: 'Account Information'
user_agent: 'User - Agent'
user_cannot_be_deleted: 'Sorry, user [user_id] can not be deleted'
admin_cannot_be_deleted: 'Deleting an administrator [user_id] account is impossible'
organisation_user_cannot_be_deleted: 'Sorry, user [user_id] can not be deleted as he belongs to an organisation'
user_class: 'User CSS Class'
user_data_required: 'Please select a customer or enter a data'
user_deleted: 'Deleted user'
user_details_page: 'User detail page'
user_group_requests: 'User Group Requests'
user_id: 'User ID'
user_uuid: 'User UUID'
user_info: 'User Information'
user_pin: 'User PIN'
user_profile_activated: 'User account enabled'
user_profile_activation_requested: 'User account waiting for activation'
user_profile_created: 'User account created'
user_profile_info: 'User profile information'
user_profile_updated: 'Upgraded user account'
user_settings: 'User settings'
user_title: Title
user_title_mr: Mister
user_title_mrs: Madam
user_unknown: 'Unknown user'
use_avail_period: 'Use availability period'
use_cardinal: "Use Cardinal Centinel & reg;\_For Secure 3-D Authentication"
use_coupons_commission: 'Coupon Commission'
use_current_link: 'Use the current page link'
use_custom_layout: 'Use a custom layout'
use_custom_weight_settings: 'Use a custom weight setting'
use_existing_block: 'Use an existing block'
use_existing_store: 'Copy an existing shop configuration'
use_negotiated_rates: 'Use negotiated prices'
use_new_sha_method: 'Use the new SHA Signature Policy'
usps: 'US Postal Service'
usps_package_size_large: Great
usps_package_size_regular: Normal
usps_service_certificate_of_mailing: 'Approval of sending'
usps_service_certificate_of_mailing_for_firm_mailing_books: 'Approval of sending for companies sending books'
usps_service_certificate_of_mailing_per_individual_article: 'Approval of shipment for individual items'
usps_service_certified: Certified
usps_service_collect_on_delivery: 'Sign / pay (collect) on delivery'
usps_service_delivery_confirmation: 'USPS Tracking / Delivery Confirmation'
usps_service_edelivery_confirmation: 'Online Delivery Confirmation (e-Delivery)'
usps_service_express_mail_insurance: 'Insurance sending mail'
usps_service_insurance: Insurance
usps_service_pick_up_on_demand: 'Remove on request'
usps_service_registered_mail: 'Sending registered'
usps_service_registered_without_insurance: 'Registered without insurance'
usps_service_registered_with_insurance: 'Registered with insurance'
usps_service_return_receipt: 'Acknowledgment of receipt'
usps_service_return_receipt_electronic: 'Electronic acknowledgment of receipt'
usps_service_return_receipt_for_merchandise: 'Acknowledgment of receipt for goods'
usps_service_signature_confirmation: 'Confirmation by signature'
usps_size: 'Please specify the width, height and length of your package for priority delivery, if the package is large only.'
valid: 'Validity period'
promotion_valid: 'Valid period'
validate_email: 'Validate the email'
validate_secret_word: 'Validate the secret word'
valid_thru: 'Valid up to (mm / yy)'
value: Value
value_to_display: 'Value to display'
variant: Variant
variants: Variations
variants_selected: '[Count] selected variant (s)'
variant_name: 'Variant name'
variant_picker: 'Variant selector'
vars: Variables
vat_eu: 'Intra-Community VAT:'
vendor: Vendor
vendors: Vendors
vendors_menu_description: 'Sort list of vendor accounts'
vendor_account_balance: 'Vendor Accounting'
vendor_account_balance_ttc: Vendor Accounting including tax
vendor_account_balance_ht: Vendor Accounting without taxes
vendor_account_balance_menu_description: 'Statistics of receipts and expenditure (Total, by period).'
vendor_administrator: 'Vendor Administrator'
vendor_administrators: 'Vendor Administrators'
vendor_administrators_menu_description: 'List of the administrators of your shop'
vendor_approval_pending: 'New vendor account application'
vendor_candidate_notification: "Hello, <br/> <br/> You have a new vendor candidate.\_This vendor account will be pending until validated.\_Follow <a> this URL to see account details </a>."
vendor_hipay_account_has_not_been_created: 'Hipay : The account has not been created. <br /> <b> Message : </b>'
vendor_commission: 'Vendor Commission'
vendor_commission_per_category: 'Commissions by category'
vendor_data_premoderation: 'Vendor Data Pre-Determination'
vendor_email_header: 'Mail header sent to seller'
vendor_email_subject: 'Subject of the mail sent to the seller'
vendor_id: 'Vendor ID'
vendor_name: 'Vendor''s name'
vendor_corporate_name: 'Corporate name'
vendor_only: 'Cette page n''est accessible qu''aux vendeurs'
vendor_pages: 'Vendor Pages'
vendor_panel: 'Vendor Dashboard'
vendor_w_apply_saved: 'Your registration request has been successfully registered'
vendor_w_id_card: 'Identity card (identity card or passport)'
vendor_w_kbis: 'Preview Kbis (INSEE receipt for autoentrepreneurs)'
vendor_w_legal_status: 'Legal status'
vendor_w_legal_status_tooltip: 'Maximum 20 characters must start with a letter'
vendor_w_rib: RIB
vendor_w_siret_number: 'SIRET Number'
vendor_w_siret_number_tooltip: '14 digits'
vendor_w_vat_number: 'VAT number'
vendor_w_vat_number_tooltip: "14 digits, non-blocking;\_Should start with FR"
vendor_w_function: 'Function'
verify: Check
version: Version
vertical: Vertical
very_good: 'Very good'
video: Video
video_popup_notice: 'Do not close the popup before the progress bar is green, and remember to back up your product.'
video_max_size: 'Maximum size of a video is %video_max_duration% mo. Beyond, the video will not be downloaded.'
view: See
viewing_feature: 'View Attributes'
viewing_filter: 'Show Filters'
views: Insights
view_all: 'See everything'
view_all_orders: 'View all orders'
view_all_product_features: 'View product attributes'
view_answers: 'View All Answers'
view_by: 'View by'
view_cart: 'See cart'
view_categories: 'View categories'
view_compare_list: 'Compare Products'
view_details: 'See the details'
view_events: 'View events'
view_event_details: 'View event details'
view_file: 'View file'
view_news_page: 'News page'
view_on_map: 'See on the map'
view_orders: 'See the orders'
view_orders_menu_description: 'View, edit and print invoices, purchase orders placed on the site'
view_page: 'See the page'
view_pages: 'See the pages'
view_product: 'See the product'
view_products: 'See the products'
view_product_features: 'View Attributes'
view_purchased_products: 'View products purchased'
view_report: 'See report'
view_results: 'View Results'
view_storefront: 'View the SHOP'
view_supplier_products: 'See the products from the supplier'
view_selected: 'View selection'
view_user_points: 'View user''s points'
view_vendor_categories: 'See the categories of the shop'
view_vendor_orders: 'View your orders'
view_vendor_products: 'See your products'
view_vendor_users: 'View your user profiles'
view_wishlist: 'View wish list'
visa_card_discover: 'Visa, MasterCard, Discover'
visited_page: 'Pages that are visited'
visitors: Home
visitors_log: 'Log visitors'
visitor_hosts: 'Host visitors'
visitor_path: 'Visitor tracking'
visits: Sightseeing
visit_time: 'Visiting time'
voided: 'Not available'
voucher_code: 'Voucher code'
warning: Warning
warning_default_language_disabled: "The default language set in your settings has been disabled or deleted.\_Please update the settings on the following page: <a href=\"[link]\"> Settings :: Appearance </a>"
warning_exim_vendor_no_data_exported: "No data can be uploaded to the Google database in vendor mode.\_You can manually load the exported product file into the Google database."
warning_features_update: "Changing the type of the attribute results in the deletion of the existing values, are you sure you want to continue?\_THIS OPERATION IS IRREVERSIBLE"
warning_gift_cert_deny: "The following gift vouchers have been deleted or canceled by the administrator and have been removed from your cart: [codes].\_Please contact the administrator or try another."
warning_https_disabled: "The secure connection control failed.\_Please check the HTTPS settings in the \"config.php\" file and verify that the SSL certificate is installed on your server."
warning_insecure_admin_script: 'It is strongly recommended that you rename the default <b> admin.php </b> script for security reasons (Check Knowledge Base)'
warning_insecure_password: "The password must be different from the username!\_<p> <a href=\"[link]\" class=\"underlined\"> <b> Change Password </b> </a> </p>"
warning_insecure_password_email: "The password must be different from the email!\_<p> <a href=\"[link]\" class=\"underlined\"> <b> Change Password </b> </a> </p>"
warning_lanvar_incorrect_name: 'The name of the language variable contains forbidden characters!'
warning_merging_companies: 'Grouping two company accounts will remove [company_name] from the database, and all profile data (products, orders, delivery method, users, promotions, etc.) will be transferred to the company account.'
warning_newsletter_no_recipients: 'The newsletter was not sent because no recipients matching the selected criterion were found.'
warning_not_deleted_default_language: 'The language [lang_name] can not be deleted because this is the default language of the shop.'
warning_of_ip_adding: 'You have entered an <b> [entered_ip] </b> IP address that denies access to your computer''s back-end (your IP is <b> [your_ip] </b>).'
warning_promotions_incorrect_condition: 'The "[condition]" condition can be used as a group with the "[set_value]" conditions only.'
warning_seo_urls_disabled: "SEO-friendly URL URLs are disabled.\_Check your web server and URL handling settings."
warning_store_optimization_dev: "Updating automatic cache of the update cache is enabled. <br /> <br /> The modified files are tracked in real time (including files modified directly on the server) and cached. <br /> <br /> Performance may be slightly affected.\_It is recommended to disable it in <a href=\"[link]\"> The template editor </a>."
warning_store_optimization_dev_disabled: 'Automatically update the cache disabled. <br /> <br /> Delete the compiled templates manually after editing the theme files by following <a href="?dispatch=template_editor.manage&ctpl"> this link </a> for Apply the changes.'
warning_subscribers_import: "Emailing lists of the input file can not be found in the database.\_Some imported subscribers will be unsubscribed."
warning_subscr_email_exists: 'This email <b> [email] </b> is already in the subscribers list'
warning_theme_clone_dir_exists: "The theme is not duplicated because the directory already exists.\_Please enter a new one."
warning_track_orders_not_allowed: 'You do not have permission to view the details of this order.'
warning_track_orders_not_found: 'The email / ID of the order you entered does not exist in our database'
warning_variants_removal: 'Clicking on save will delete the variants of the products.'
warning_w_vat_number_format: "The VAT number is not in the expected format.\_(non blocking)"
webhooks_notifications_created: 'Your notification has been created.'
webhooks_notifications_deleted: 'Your notification has been deleted.'
webhooks_notifications_updated: 'Your notification has been updated.'
webhooks_notifications_service_error: 'Webhooks service unavailable'
webhooks_notifications_service_unexpected_error: 'Webhooks: an unexpected error occured'
webhooks_notifications_service_forbidden: 'Your access has been refused'
webhooks_notifications_bad_request: 'Sent Webhook is not valid'
webhooks_notifications_delete_confirm: 'You are about to delete this Webhook. Are you sure you want to continue?'
webhooks_notifications_edit_subtitle: 'Edition: [webhook_url]'
webhooks_notifications_event_references: 'Events'
webhooks_notifications_perimeter: 'Perimeter'
webhooks_notifications_manage_subtitle: 'URL receiving Marketplace''s events'
webhooks_notifications_marketplace_perimeter: 'Marketplace'
webhooks_notifications_new_notification: 'New notification'
webhooks_notifications_select_events: 'Select events'
webhooks_notifications_update_title: 'Notification''s url'
webhooks_notifications_settings_error: 'At least one event must be associated with this Webhook'
webhooks_notifications_all_events: All events
website: Website
website_title: Wizaplace
web_pages: 'Web pages'
web_site: Website
week: Week
weekday_0: Sunday
weekday_1: Monday
weekday_2: Tuesday
weekday_3: Wednesday
weekday_4: Thursday
weekday_5: Friday
weekday_6: Saturday
weekday_abr_0: Sun
weekday_abr_1: Mon
weekday_abr_2: Tue
weekday_abr_3: Wed
weekday_abr_4: Thu
weekday_abr_5: Fri
weekday_abr_6: Sat
weekly: Weekly
weeks: 'Week (s)'
weight: Weight
weight_dependences: 'Depending on weight'
weight_limit: 'Weight Limit'
weight_modifier: 'Weight Modifiers'
weight_symbol: 'Weight symbol'
well_done: Congratulations!
we_would_like_to_inform: 'We would like to inform you that'
what_is_cvv2: 'What CVV / CVC'
what_you_would_like_to_do: 'What would you like to do?'
which_is: 'What is'
white: White
widget_code: 'Code Widget'
width: Width
wishlist: 'Wish list'
wishlist_content: 'What''s in the Wish List'
wishlist_note: 'Go to wish list'
wishlist_products: 'Products of the wish list'
without_image: 'No image'
with_contact_information: 'With contact information only'
with_image: 'With image'
wizacha_discuss_new_message_content: '<p> You have received a new message on <a href="[wizacha_url]"> our marketplace </a> </p> <p> You can view it by visiting your profile or by clicking <a href = "[Discuss_url]"> here </a> </p>'
wizacha_discuss_new_message_content_customer: "\n            <p>You have received a new message on our marketplace</a></p>\n            <p>You can view it by visiting your profile</p>\n\n        "
wizacha_discuss_new_message_subj: 'You have received a new message'
world: World
worldnettps_hash_error: "An error has occurred.\_Details: Request and Response hash do not match."
worldpay_secret: 'MD5 secret for transaction'
wrapper: Container
write: 'To write'
write_review: 'To write a comment'
wrong_email_setting: 'Please enter an email ending with% suffix%'
wt_access_note: "Please note, if you disable or uninstall the Watermarks module, you can delete these instructions from the .htaccess files manually.\_Otherwise product images will no longer be available."
wt_access_warning: "Watermark creation is disabled.\_If \"Rewrite watermarks rules\" are present in the \"images / .htaccess\" file, you should delete them manually.\_Otherwise product images will no longer be available."
wt_detailed: 'Pop-up larger images'
wt_fail_apply_graphic_watermark: "Failed to apply watermark to [image_type].\_The watermark image is not added"
wt_font: Police
wt_font_color: 'Font Color'
wt_font_size_detailed: 'Font Size (Large Image)'
wt_font_size_icon: 'Font Size (Navigation)'
wt_graphic_watermark: 'Watermark graphic'
wt_icons: Thumbnails
wt_images_access_info: 'Access Images'
wt_text_watermark: 'Watermark Text'
wt_watermarks: Watermarks
wt_watermark_detailed: 'Watermark Pop-up larger image'
wt_watermark_horizontal_position: 'Horizontal Position Watermark'
wt_watermark_icon: 'Thumbnail watermark'
wt_watermark_image: 'Image Watermark'
wt_watermark_position: 'Position Watermark'
wt_watermark_text: 'Text Watermark'
wt_watermark_vertical_position: 'Vertical position Watermark'
w_about_product_creation: '<strong> Note </strong>: Delivery methods, attributes and variants of the product are to be defined after the creation of the product.'
w_about_product_creation_price_tiers_activated: '<strong> Note </strong>: Delivery methods, attributes, price tiers and variants of the product are to be defined after the creation of the product.'
w_about_product__price_tiers_single_declination: 'To exit price tiers mode, please delete board’s content first.'
w_actual_selection: 'Current Selection'
w_add_product_in_echec_csv: 'Products were placed in the category "CSV import failure"'
w_age_limit: 'Age limit'
w_age_verification: 'Age verification'
w_age_verification_product_unavailable: 'This product is forbidden for sale to children under the age of [years].'
w_age_verification_too_young: 'The sale of certain products of your basket is forbidden to the people of less than [age] years.'
w_allow: Authorized
w_allowed_combinations: 'Management of declinations'
w_all_categories: 'All categories'
w_all_option_forbidden: 'You must allow variants before you can manage them'
w_already_sent: 'Here are the shipments already made:'
w_api_deprecated_field: 'The use of the [[field]] field is deprecated'
w_api_invalid_category: 'The category is not valid'
w_api_invalid_geolocation: 'La géolocalisation n''est pas valide (la latitude et la longitude sont obligatoires)'
w_apply_as_vendor: 'Access the registration form'
w_apply_for_vendor_button_text: 'Send my request'
w_at: at
w_automated_feeds: 'Automated flows'
w_available_date_limited: 'You can not start a campaign on this date'
w_available_date_no: 'Date unavailable: a campaign is already in progress'
w_available_date_yes: 'Available date'
w_banner_duration: 'Duration of the campaign'
w_banner_start_date: 'Start date'
w_be2bill_payment_title: 'Payment of your order'
w_billing: Billing
w_brands: Brands
w_brands_scroller: 'Fashion Show'
w_brand_required: 'You must fill in a'
w_browse_categories: 'Or browse the tree'
w_c2c_accounting: Payments
w_c2c_code: 'Order Validation Code'
w_c2c_code_max_try_reach: "> You have reached the maximum number of attempts.\_Please contact a webmaster from <a href=\"[url]\"> this page </a>."
w_c2c_code_max_try_reach_short: 'Too many tests on the order validation code'
w_c2c_company: 'My selling profile'
w_c2c_company_name: 'Pseudo seller'
w_c2c_company_update: Description
w_c2c_company_update_explanation: 'Your description as a seller.'
w_c2c_company_update_informations: 'Updating of documents'
w_c2c_company_update_informations_explanation: "Why do I need ID and RIB?\_In order to finalize the creation of your seller account, we need a piece of identification to avoid any usurpation and to guarantee the payment, and a RIB in order to return you the fruit of your sales in the future.\_This is only necessary once for all of your future sales.This is one of our major strengths in relation to classified ad sites: These are the controls that guarantee payment and cancel any risk Fraud or scam for both the buyer and the seller. We carry out these operations totally free of charge."
w_c2c_contact_title_popin: 'Your message'
w_c2c_create_new_product: 'Sell ​​a new article'
w_c2c_create_profile_notification_header: '<p> Hello and welcome [company_name], </p> <p> Your account has been validated by our team. </p> <p> You can modify your personal data <a href = "[update_informations_url ] "> Here </a> and create new products for sale <a href="[new_product_url]"> here </a>. </p> <p> Feel free to talk to us about your surroundings And on social networks, a totally free online sales service that guarantees payment, it may also interest them! </p> <p> We wish you good sales. </p>'
w_c2c_login_promo_text: 'All you have to do is sign in to start selling your products, it''s totally free.'
w_c2c_login_title: 'Already have an account ?'
w_c2c_message_signature: 'The user [user_firstname] [user_lastname]'
w_c2c_new_company: 'Before accepting your products, you must send the necessary items <a href="[url]"> here </a>'
w_c2c_new_company_notification: 'Creating a sales account'
w_c2c_new_company_notification_content: '<p> You are welcome to change your personal information <a href = "[ <a href="[new_product_url]"> here </a>. </p> <p> Upon validation of your account by our services, Products will be visible for sale.This is done in less than 24 hours. </p>'
w_c2c_new_company_without_doc_notification_content: "<p> Your sales account has been created. </p> <p> We are pleased to have you as a new member. </p> <p> It is mandatory to enter your seller information <a href = \"[update_informations_url] </p> <p> You can create products <a href=\"[new_product_url]\"> here </a>. </p> <p> Validation of your account by our services, your products will be visible for sale.\_This is done in less than 24 hours. </p>"
w_c2c_new_order: 'You have a buyer!'
w_c2c_new_order_complete_profile: "<p> Your product is sold and you only have to send or hand-deliver it to complete the transaction. </p> <p> Do not forget, so that we can make the transfer on your Account, we need complete and up-to-date information about you (coordinates, RIB ...).\_</p> <p> To enter, update or simply check this information, <a href=\"[update_informations_url]\"> please go to this address </a>. </p> <p> See you soon ! </p>"
w_c2c_new_order_content: '<p> The buyer has chosen the shipment at home. </p> <p> At this stage, the payment made by the buyer is guaranteed by us, so you can send your product to the buyer as soon as </p> <p> The buyer''s contact details and all order details are available <a href="[url]"> here </a>. </p> <p> The buyer to track the routing of the product and receive your payment as soon as possible, you will need to enter the tracking number of the package. </p>'
w_c2c_new_order_content_hand_delivery: '<p> The purchaser has chosen hand-delivery. </p> <p> At this stage, payment by the buyer is guaranteed. </p> <p> Please go to <a href = "[Url]"> this page </a> to validate the transaction, or reject it if necessary. </p>'
w_c2c_new_order_header: "<p> Your product [product_name] was sold on [date]. </p> |\_<p> Your products [product_name] were sold on [date]. </p>"
w_c2c_new_profile_notification: 'Validation of your seller account'
w_c2c_orders: 'My Account'
w_c2c_orders_explanation_e: "<p> You have validated the sale of your product.\_Now you have to deliver it to the buyer.\_</p> <p> To track the shipment to the buyer, be sure to enter the package tracking number.\_It is inscribed on the packing slip (Colissimo and Letter Max for example).\_It is this step that triggers the validation of the payment. </p>"
w_c2c_orders_explanation_generic: '<p> Your product has been sold and you have shipped it. </p>'
w_c2c_orders_explanation_p: "<p> Your product listed in the table below has been sold.\_</p> <p> Now you have to send or return the product to be paid. </p> <p> Conversely, If you can no longer or no longer wish to make the sale, click on \"refuse\": the buyer will be refunded and the sale canceled. </p>"
w_c2c_orders_manage: 'My Account'
w_c2c_part_become_seller: 'Selling with the particular account'
w_c2c_part_seller_text: '<li> 100% free </li> <li> Create a product for sale fast and easy </li> <li> 100% guaranteed payment </li> </ul>'
w_c2c_products: 'My articles'
w_c2c_products_approval_status_approved: "\n            <p> You can find all of your products for sale on your <a href=\"[company_url]\"> seller page </a>. </p>\r<p> We recommend that you share this link with your friends on social networks (<a href=\"https://www.facebook.com\"> Facebook </a>, <a href = \"https: // twitter .com / \"> Twitter </a> etc.) as well as on sites you can exchange (forums, blogs ...). </p>\r<p> Do not hesitate to ask your friends to share your ad, know that after four levels of sharing, your ad has been seen by several hundred people!</p>"
w_c2c_products_approval_status_changed: '[company_name] - [products_count] product(s) soon available'
w_c2c_products_location_explanation: 'Where do you want to return the product to the buyer?'
w_c2c_products_manage: 'Manage my items'
w_c2c_products_manage_annonce_header: 'Product Link'
w_c2c_products_update: 'New article'
w_c2c_products_update_explanation: "<p> The sale of an item is <b> totally free </b>.\_</p> <p> Moreover, unlike a classified ad, <b> payment is guaranteed </b>: when we send you the contact information Of a buyer, the product has been paid.\_You can be certain that you will receive payment after shipping or hand-delivering. </p>"
w_c2c_product_updated: 'The product has been registered.'
w_c2c_product_update_error: "A problem occurred while registering the product.\_Try Again."
w_c2c_pro_become_seller: 'Sell ​​with pro account'
w_c2c_pro_seller_text: "                <ul class=\"bullets-list\">\r\n                    <li> 100% free</li>\r\n                    <li>\r\n                         Advanced features: Catalog on .csv file, automatic update from URL, API access, promotions management ...\r\n                        \r\n                    </li>\r\n                    <li> Payment 100% guaranteed</li>\r\n                </ul>\r\n                "
w_c2c_refuse_order_notification: 'By refusing this order, we conclude that the products of the component are no longer available for sale. <br/> Their available quantity has therefore been set to 0, and they are no longer visible on the site <br/> You can Edit them <a href="[URL]"> here </a> to put them back for sale.'
w_c2c_register_promo_text: 'Create an account is essential for: <ul class="bullets-list"> <li> Sell your products for free </li> <li> Receive buyer information </li> <li> Receive your payment After the sale </li> </ul>'
w_c2c_register_title: 'No account yet?'
w_c2c_save_company: 'Save description'
w_c2c_save_company_informations: Record
w_c2c_save_product: 'Put my product on sale'
w_c2c_send_message_email_core: "Hello, <br /> This message is sent to you by [signature], about your article [product_name].\_<br /> <blockquote> reasons: [reason].\_<br /> [comment] <br /> </blockquote> You can reply directly to [user_email].\_Remember that your goal is to sell your item, so be cordial and try to be clear in your answer.\_The team wishes you a good sale!"
w_c2c_send_message_email_subject: 'A user asked you a question about your article [product_name]'
w_c2c_standby_vendor_after_invoice: '<p> Email: </p> <p> You will soon receive an email containing the tracking information as soon as it is delivered to the carrier. </p> <p> Our customer service is available Via our <a href="[form_url]"> contact form. </a> </p>'
w_c2c_standby_vendor_after_invoice_hand_delivery: '<p> Email: <email> </p> <p> You will receive by email the code to be sent to the seller to validate the hand delivery. </p> <p> Our customer service is available via our <a href="[form_url]"> contact form. </a> </p>'
w_c2c_stock: 'Number of items for sale'
w_c2c_text_no_products: 'You have no articles created yet'
w_c2c_thanks_for_declined_order: "<p> We thank you for your order on [date], unfortunately, the order was refused by the seller [vendor].\_</p>"
w_c2c_thanks_for_order: '<p> Thank you for your purchase past [date]. </p> <p> This purchase was made from a particular vendor and was registered with [order_id]. </p>'
w_c2c_try_code: Code
w_c2c_update_informations_success: 'Your documents have been sent'
w_c2c_vendor_approval_pending: Creation
w_c2c_vendor_candidate_notification: "Hello, <br/> <br/> You have a new vendor candidate.\_This vendor account will be pending until validated.\_Follow <a> this URL to see account details </a>."
w_c2c_vendor_profile: 'My Account'
w_cant_active_product_without_shippings: 'You can not actively iron a product that does not have delivery means'
w_cant_delete_category: 'This category contains products and, or unified products, and therefore cannot be deleted.'
w_cards: summary
w_cart_total: 'Total amount of order'
w_category_exist_for_option: 'You must delete the categories of the option before you delete the option'
w_category_search_message: '[Value_results] Results, [value_product_type] Product type, [value_categories] Categories'
w_certified_payment: 'Secured and guaranteed payment'
w_certified_users: 'Certified User Identity'
w_change_order_status: 'Change the status of the return request'
w_change_product_status_on_shippings: 'The product has changed status due to the lack of means of delivery.'
w_change_status: 'Validate the order'
w_change_status_order_not_allowed: "You can not make this status change.\_Ask the administrators"
w_check_shippings_before_active_product: 'Check the delivery methods before activating the product.'
w_choose_payment_method: 'Choose a payment method'
w_client_id: 'Customer number:'
w_closed_rma: 'Finished returns'
w_code_notification_subj: 'Delivery of your order n ° [id]'
w_command: Checkout
w_commission_intro: "Transfers are made 2 to 3 days after each fortnight.\_They are calculated when the order changes to \"Done\" (ie after the customer's retraction period).\_The period indicates when the order is completed and not the customer's order date.\_<P> Commissions correspond to bank charges collected by payment providers (CB, Paypal)."
w_commission_intro_c: 'Payments are calculated from the time the order changes to "terminated" status and not from the date the customer orders.'
w_company_type: 'Type of seller'
w_company_type_c: 'Private Sellers'
w_company_type_filter_c: Particular
w_company_type_filter_v: Professional
w_company_type_v: 'Professional Sellers'
w_confirm_new_feature: "Use this feature only if the attribute you want does not exist.\_The new attributes will be validated. Are you sure you want to continue?"
w_confirm_new_option: "Use this feature only if the option you want does not exist.\_The new options will be validated before they can be used. Are you sure you want to continue?"
w_connection_required: 'Please login to complete this action.'
w_contact: 'Contact the seller'
w_contact_info_seller: "The seller [seller_name] has validated your order.\_Just contact [firstname], the seller [seller_name] at [phone] or email at <a href=\"mailto:[email]\"> [email] </a> Of an appointment for the delivery of the product."
w_create_order: 'Create an order'
w_create_profile_notification_footer: "<p> You have the option to change your login and personal data in the space \"<em> my account> vendor administrator </em>\".\_<br /> You can now create new products, create the presentation page of your shop, and manage future orders.\_</p> <p> We wish you good sales. </p>"
w_csv_error_in_combination: "You have inconsistencies in the combinations of a product.\_You can go to <a href=\"[url]\"> this link </a> to correct them or contact an administrator"
w_csv_error_combination_consistency: "You have inconsistencies in the combinations of product."
w_csv_error_in_combination_async: 'Some options and variations are inconsistent in the "Combination" column for the product [productId].'
w_csv_import_product_on_failure_category: "Some of your products are in the CSV Import Chess category.\_You will probably be able to solve this problem by creating correspondences on <a href=\"[url]\"> this link </a> and re-importing your CSV afterwards"
w_csv_import_progress: 'Progress of your CSV imports'
w_csv_previous_import_log: 'Summary of your previous imports'
w_current_sending: 'Here are the products concerned by the shipment that has just been made:'
w_customer_code_explanation: "We have designed a security system that allows both the seller and the buyer a secure and guaranteed transaction, even when hand delivered. The day of hand delivery, once the product has been verified , Give this code table (that you will have printed or copied) to the seller, then he can be paid. If you finally cancel the sale and you do not leave with the product It is essential that the codes are not returned to the seller.\_Then ask for the cancellation of the sale from the contact page."
w_declined_order_before_order: 'Reminder of the contents of the canceled order:'
w_declined_order_refund: '<p> We have done the refund procedure, which should be credited on your account within a few days. <br /> </p> <p> The whole team apologizes for the inconvenience. /> We are aware of the disappointment that an order cancellation can generate. <br /> </p> <p> We set ourselves a high level of requirement, but sometimes an error can occur. </p> Our customer service is available through our <a href="[form_url]"> contact form </a>.'
w_default_category_meta_description: "Sale [category_name] at the best price.\_Secure payment.\_Great choice [category_name].\_Quick delivery."
w_default_category_meta_keywords: '[Values]'
w_default_category_page_title: '[Category_name] - Sale [category_name] - at the best price - online'
w_default_w_company_meta_description: 'Find the products of the dealer [company_name] at the best price.'
w_default_w_company_meta_keywords: '[Company_name], Dealer, Product, Search'
w_default_w_company_meta_title: '[company_name]'
w_delivery_chrono_13: 'Chrono''13 API'
w_delivery_chrono_relais: 'Chrono Relais API'
w_delivery_mondial_relay: 'Mondial Relay API'
w_delivery_text: 'Pay Per View'
w_delivery_type: 'Type of delivery'
w_deliver_other_address: 'Deliver to another address'
w_deliver_same_address: 'Deliver to the same address'
w_details_order: 'See details'
w_display_help: 'Show help'
w_donate: Gain
w_donate_ttc: Vendor share including tax
w_donate_ht: Vendor share without taxes
w_do_payment: 'Make a payment'
w_duplicate_product_code: 'You can not have two articles with the same reference.'
w_dynamic_menu: 'Dynamic menu'
w_edit_rate: 'Change shipping costs'
w_email_subject: 'The [firstname] [lastname] client wrote to you'
w_ended_orders: 'Completed orders'
w_ended_orders_returns_possible: 'Completed orders, return possible'
w_ended_orders_select_for_sav: 'Selection of the order concerned:'
w_enter_category: 'Enter a category'
w_error_search: 'Error while searching.'
w_error_search_length: 'The search must contain at least three characters.'
w_exim_categories_note: "<h3> Creating Categories </h3> <p> The <em> Category </em> field always expects the full path of the category.\_If it does not exist, it will be created with categories with just a name and the data entered on the CSV line will only be applied to the last category. </p> <h3> Renaming / moving categories </h3> <p> To rename or move categories, enter the <em> Category ID </em> field or the <em> Old Category </em> field with the id or path </p> <h3> Delete categories </h3> <p> Deletion is done by the <em> field </em> with the new path and the new name. <em> Category </em>.\_<br /> This action takes precedence over the line that concerns it </p>"
w_existing_account: 'Log in with an existing account:'
w_extend_search_distance: 'Expand search'
w_failed_shipment_code_registration: 'The removal code entered is incorrect.'
w_fail_code_registration: "The entered code is incorrect.\_You still have [remain_try] tests."
w_fail_progress: "Tracking progress [progress_id] failed.\_This failure does not indicate that the task has not been carried out but that we are not in a position to give details of its execution."
w_features_approval_status_approved: 'Here are the set of new attributes that have been approved by the team:'
w_features_approval_status_changed: 'Pre-moderation of attributes: [status]'
w_features_approval_status_disapproved: 'Here are the set of new attributes that were disapproved by the team:'
w_feature_approval: 'Pre-moderation of attributes'
w_feature_approval_status_approved: 'The proposal for attribute "[feature]" was approved by the team.'
w_feature_approval_status_disapproved: 'The proposal for attribute "[feature]" was disapproved by the team.'
w_finalize_shippment: '<p> This shipment finalizes the shipment of this order which has been delivered to the carrier in full. </p>'
w_first_page: 'First page'
w_forbidden: 'Not allowed'
w_free_sell: 'Sell ​​totally free'
w_from: of
w_generate_example_csv: 'Generate a sample CSV for these categories'
w_generate_example_csv_combination: 'Generate a sample CSV for this category'
w_get_rma: 'View the credit'
w_goto_c2c_orders: 'Go to My Sales'
w_goto_orders: 'Go to My purchases'
w_green_tax: 'Green tax'
w_grouped_products: 'Product sheets'
w_hand_deliver: 'Remettre en main propre'
w_hand_delivery: Hand-delivered
w_hand_delivery_without_code: 'Remise en main propres (sans code)'
w_hand_delivery_with_code: 'Remise en main propres (avec code)'
w_hello_and_welcome: 'Hello and welcome'
w_help_company_description: "This WYSIWYG editor allows you to include images, text, and tables.\_It is also possible to directly use HTML code. <br/> To write in columns or next to an image, use a borderless table! <br/> <br/> <em> Tip </em> : You can insert images from links (URLs) or from your hard drive <br/> Click on the icon 'image', then in the source field, insert a link directly (URL) or click on the magnifying glass to Import the images from your hard disk via the 'floppy disk' icon."
w_help_for_csv: 'Download CSV template'
w_help_for_csv_combination: 'Download a CSV template for importing products variants'
w_help_for_csv_combination_post: ' '
w_help_for_csv_combination_pre: 'Choose one category:'
w_help_for_csv_post: ''
w_help_for_csv_pre: 'For your imports csv you can use the proposed template'
w_help_import_product_combination: "<em> Parent Product code </em>: Represents the product code of the product whose declination follows <br/> <em> Product code </em>: Provides a specific EAN code for declination <br/> <em> Combination </em>: Allows you to enter options and their variants whose product needs to be updated or created.\_The field is of the form \"Option1: variant1, Option2: variante2\".\_<pre> Color: Black, Size: 54 </pre> You <strong> can not </strong> create new options via CSVs.\_However, you can propose new variants. <br/> These <strong> should not contain </strong> contain \",\" or \":\"."
w_help_page: 'Help page'
w_help_page_explanation: "<b> Reminder: </b> Menu links are automatically created from h2 tags. These can be accessed by directly editing the html from TinyMCE. <br/>\r\nEach h2 tag must have an id attribute (as id = \"controller.mode\"), corresponding to a back-office page (eg id = \"products.update\"). <br/>\r\nThese id attributes will be used to correctly manage the links at the bottom of each page of the vendor backoffice."
w_help_page_link: 'Help for this feature'
w_help_page_menu_description: 'Vendor Administrator Help Page'
w_help_products_add_hint: "Only the general tab is mandatory when creating a product.\_Nevertheless, without image or description, the product will probably be refused by moderation.\_Nothing prevents you from saving the product and completing it later."
w_help_shipping_rates: '<br/> <br/> <em> Tip </em>: If your items are easily grouped together in the same package (for example, <br /> <br/> Conversely, non-groupable products (such as fridges) will have a formula of the type 25 € + 25 € (each product adds 25 € postage).'
w_ht_price: 'VAT free price'
w_ht_subtotal: 'Sub total VAT free'
w_identification: Identification
w_images_delimiter: 'Image delimiter'
w_images_hosting: 'Host images for import via CSV file'
w_images_management: 'Managing your images'
w_csv_management: 'Managing your CSV files'
w_images_management_url: "The url for accessing your images is <a> [url] </a>, followed by the name of your image and the extension, case-sensitive.\_Example: [url] image.jpg"
w_include_products_with_delivery: 'Include products shipped home'
w_incorrect_old_password: 'Current password incorrect'
w_info_valide_order: "The order will be fully valid when you have entered an invoice number and when you have sent all the products.\_Thank you."
w_invalid_parameters: 'Parameters entered are invalid'
w_invoice_number: 'Bill number'
w_invoice_number_error: 'The bill number must not be greater than 40 characters long'
w_last_page: Top
w_latitude: latitude
w_leave_search: 'Leave search'
w_legacy: 'Legal Notice'
w_links_vendor_categories: 'Link categories'
w_litigation: Litigation
w_litigation_email_content_to_buyer: "Hello, <br /> Your complaint about purchase [order_id] has been taken care of by our services. <br /> The seller has received your message and will contact you to try to solve the problem first Live with you.\_<br /> <br /> If you can not solve your problem within 3 days, please send us an email to ask us to intervene as a trusted third party. <br /> <br />"
w_litigation_email_content_to_company: "This message is sent to you by the buyer to let you know of a problem he has encountered.\_At first, we leave you a right of reply before intervening.\_<br /> <br /> If you can solve the problem of the buyer directly, do it as quickly as possible, indeed remember that a bad evaluation can affect your future sales.\_<br /> <br /> If you can not get a solution to your problem within 3 days, please send us an email to ask us to intervene as a trusted third party.\_<br />"
w_litigation_explanation: "This section allows you to send a message to the seller.\_A copy is sent to us.\_We will be able to intervene as a mediator in case of need."
w_litigation_header: "This section will allow you to send a message to the seller, who, depending on his guarantees and the need you encounter (breakdown, breakage, operation question), will tell you the procedure to follow.\_He will then answer your questions and in case of a defective product will ask you to send it to his premises or those of the manufacturer, for a repair or an exchange."
w_litigation_mail_subject: 'Request for litigation'
w_litigation_mail_subject_recorded: 'Registered Litigation'
w_litigation_not_received: 'I have not received the package'
w_litigation_other: Other
w_litigation_packet_does_not_comply: 'I received the parcel but the merchandise is not compliant'
w_litigation_successfully_registered: 'Your dispute has been properly processed'
w_localization: Location
w_localization_label: 'city ​​or ZIP code'
w_login_promo_text: "Account creation is essential and is done in an instant.\_This will allow you to track your orders, view your invoices, make a return, an after-sales service ..."
w_longitude: longitude
w_lump_sum_recovery: 'Any payment incident is subject to a penalty for late payment - Collection fee in case of late payment: 40 €'
w_mail_code_fail_body: '<p> <a href="[order_url]"> here </a> <a href="[order_url]"> order </a> is blocked due to too many attempts to enter code from the vendor . </p> <p> The expected code was: [correct_code] </p> <p> The vendor inputs the values ​​<br/> [logs] </p> Below.'
w_mail_code_fail_subject: 'A hand-delivered order is blocked.'
w_mail_default_signature: 'an user'
w_main_image: 'Main Image'
w_main_newsletter_incitation: 'Sign up for our newsletter to discover new products, promotions, exclusive private sales ...'
w_message_send: 'Your message has been sent.'
w_meta_description_for_brand: 'Find the products of the brand [variant] at the best price'
w_meta_keywords_for_brand: '[Variant], Brands, Buy, Search'
w_missing_parameters: 'Missing parameters'
w_modify_password: 'change the password'
w_multiple_categories_select: 'You only need to select one category'
w_my_cart: 'Shopping Cart'
w_my_order: 'My command'
w_my_purchases: 'My purchases'
w_my_vendor_account: 'My selling access'
w_newsletter_reassurance: '1 to 2 emails per week, unsubscription possible in one click with each email, do not hesitate to test!'
w_new_client_notification: '<p> You have now an account and we thank you. </p> <p> We are delighted to count you among our new members and remain at your disposal to facilitate your purchases. </p> <p> Customer service is available through our <a href="[form_url]"> contact form </a>. </p>'
w_new_client_subjet: 'Your account has been created'
w_new_coupon: 'New discount code'
w_new_grouped_product: 'Create a product datasheet'
w_new_line: 'New line'
w_new_order: 'You have a new order.'
w_new_order_for_vendor: "<p> You have a new command to process.\_<br /> <a href=\"[url]\"> Link to the command </a> </p> <p> Here's a summary of the </p>"
w_new_password: 'New Password'
w_not_approved_features: "You have attributes waiting for validation.\_Please <a href=\"[link]\"> follow this link </a> to verify these attributes"
w_no_category_select: 'You must select a category'
w_no_exceptions: 'No value allowed'
w_old_password: 'Current Password'
w_one_full_shippment: '<p> We inform you that the entire order [order_id] has been delivered to the carrier. </p>'
w_options_approval: 'Pre-moderation of options'
w_options_approval_status_approved: 'Here are the set of new options that have been approved by the team:'
w_options_approval_status_changed: 'Pre-moderating options: [status]'
w_options_approval_status_disapproved: 'Here are the set of new options that were disapproved by the team:'
w_option_approval_status_approved: 'The option proposal [option] was approved by the team.'
w_option_approval_status_disapproved: 'The option proposal "[option]" was disapproved by the team.'
w_order_date: 'Passed on [date] to [time]'
w_order_declined_by_vendor: '<p> Order Cancellation <br /> <strong> Order [order_id] was canceled by the seller. </strong> </p>'
w_order_id: 'Order number'
w_order_items_amount: 'Order amount'
w_order_num: 'Order number'
w_order_number: 'Order number'
order_ref: 'Order number'
w_order_save_with_success: '<p> <u> Order Confirmation </u> </p> <p> <strong> Order # [order_id] saved successfully </strong> </p>'
w_order_summary: 'Summary of your order'
w_our_categories: 'Our categories'
w_partial_shippment: '<p> We inform you that the order [order_id] was partially delivered to the carrier. </p>'
w_partner_newsletter_incitation: 'Do you also have reviews from other travelers?'
w_payment_commission: 'Commission on means of payment'
w_phone_mandatory_explanation: 'No advertising use. <br/> Required by the carrier.'
w_pipe: Pipe
w_please_save_or_reload_to_validate_order: 'Please save changes or reload the page in order to validate the order.'
w_please_send_elements: 'To register and sell for free and without commission, please send us the following items:'
w_post_an_ad: 'Post an ad'
w_post_sales_support: 'After Sales Service'
w_premoderation_all_pages_check: 'All pages'
w_premoderation_standby: 'Waiting for modification'
w_premoderation_standby_button: 'Place pending change'
w_premoderation_standby_selected: 'Place the selection waiting for modification'
w_prices_inventory_error: 'After this modification the product can no longer be sold because its quantity and / or its price is not correct'
w_price_from: from
w_print_rma: 'Print the credit'
w_proceed_to_secured_payment: 'Secure payment'
w_processed_order: 'The order was processed'
w_processed_order_before_invoice: 'As a reminder, the contents of the command are as follows:'
w_processed_order_mail: 'Order [order_id] has been processed and handed over to the carrier <br /> <strong> You can view and print the invoice for this order <a href="[invoice_url]"> my invoice </a> </strong>'
w_processed_order_mail_for_edp: 'The order [order_id] has been fully processed. <br /> <strong> You can view and print the invoice for this order <a href="[invoice_url]"> my invoice </a> </strong>'
w_processed_order_shipments: 'You can follow the evolution of your delivery by clicking on the number (s) of packages:'
w_processing_orders: 'Current orders'
w_products_approval_status_standby: 'The following products will soon need modification from your side by the site administration:'
w_products_automated_feed: 'Product Feed'
w_products_has_been_hidden: 'Some products have changed to "hidden"'
w_products_search_stats: '<Strong> {{nbHits_plural}} s {{nbHits_plural}} in {strong> {{nbHits_plural}} < / Strong> ms'
w_products_search_stats_light: '<Strong> {{nbHits}} {{nbHits_plural}} {{nbHits_plural}} {{#'
w_products_with_delivery: 'Products delivered'
w_products_with_rmp: 'Hand-delivered Products'
w_product_condition: 'Product condition'
w_product_condition_front_n: 'New product'
w_product_condition_front_u: 'Used product'
w_product_condition_n: New
w_product_condition_u: Used
w_product_exist_in_category_add: 'Unable to add a category because it contains products:'
w_product_exist_in_category_remove: 'Unable to remove a category because it contains products:'
w_product_exist_for_option: 'This option is used by one or multiple products. To be able to delete them, please first remove this connection with these products'
w_product_exists_for_variant: 'This option variant is used by one or multiple products. To be able to delete them, please first remove this connection with these products'
w_product_moderating_status: 'Status of moderation'
w_product_price_tiers_management: 'Price tiers management'
w_product_price_automated_feed: 'Price feed'
w_product_stock_automated_feed: 'Stock feed'
w_progress_resume: '[Task_id]: [task_count] / [task_total] of which [task_error] error (s)'
w_promotions_basket: 'Promotions on baskets'
w_promotions_catalog: 'Promotions on products'
w_promotions_no_condition: 'Vous devez créer un code d''utilisation pour ce coupon dans l''onglet "Conditions".'
w_promo_code: 'Promotional code?'
w_provider: 'Provider [id]'
w_pss_header: "This section will allow you to send a message to the seller, who, depending on his guarantees and the need you encounter (breakdown, breakage, operation question), will tell you the procedure to follow.\_He will then answer your questions and in case of a defective product will ask you to send it to his premises or those of the manufacturer, for a repair or an exchange."
w_punch_line: 'Now, classified ads are secure.'
w_punch_line2: 'Your punchline. '
w_purchases: Shopping
w_question_account: 'No account?'
w_rate_first_item: Shipping
w_rate_others_items: 'Fees per additional item'
w_rate_value_negative: 'One of the costs was entered negative, it was set to 0'
w_read_reviews: 'Read customer reviews'
w_reason_groupe_divide: 'Grouping / dividing a product pack'
w_reason_other: 'Other Application'
w_reason_product: 'Product inquiry'
w_reason_question: 'Practical question'
w_reason_shipping: 'Delivery request (deadlines, packaging ...)'
w_recap: Recapitulation
w_ref: Ref.
w_refine: Filter
w_refund_link: 'Repayment Link'
w_remove_item_from_cart: 'Are you sure you want to delete the following product from your shopping cart?'
w_remove_item_from_cart_title: 'Remove item from cart title'
w_remove_this_item_from_cart: 'Are you sure you want to delete this product from your cart?'
w_restore_default_rate: 'Restore Default Fees'
w_return_products: 'As a reminder, the expected content is as follows:'
w_rma: 'To have'
w_rma_available_orders: 'Processed orders, product return possible'
w_rma_confirmation_text: '<p> Your return request has been saved successfully. </p> <p> Please send your packaged product quickly to </p> <p> [address] </p> <p> As soon as we receive your product, we will proceed to the refund. </p>'
w_rma_history: Returns
w_rma_intro_mail: 'Please return your products to the address below:'
w_rma_label_address: 'Address for products return'
w_rma_number: 'Number of assets'
w_rma_number_needed: 'A credit number is required in order to record the receipt.'
w_rma_post_request: 'Upon receipt of the returned products, you will receive a confirmation email and the refund will take place very quickly.'
w_rma_refound_products: 'As a reminder, the return was for the product (s):'
w_rma_refund: 'We have done the refund procedure, which should be effective within a few days on your account.'
w_rma_refund_subject: 'Order Reimbursement'
w_rma_request: 'Your return request for order [order_id] has been saved.'
w_rma_request_vendor: '<p> One of your orders is requested by the customer. </p> <p> The customer will proceed with the return shipping to the following address: </p> <p> <span> [address] </span> Of the part </p> [returned_products]'
w_rma_return_address: 'You must send the products to be returned to the following address:'
w_rma_return_received: 'Your order back [order_id] has been received.'
w_rma_return_requested_subj: 'Product Return Request'
w_rma_standby_refund: 'Returns awaiting payment'
w_rma_text: 'Guaranteed hand-delivery by password exchange'
w_safe_transaction: 'Return of goods without risk'
w_sandbox: Sandbox
w_save_modifications: 'Save Changes'
w_sav_email_content: 'Hello, the customer [client] is trying to contact you about his order [order_id], for a problem about:'
w_sav_email_info: 'The vendor will reply to the following address: [email] </br> If this address is not correct, please correct it in the [my_account] page.'
w_sav_explanation: 'For the fastest and most efficient processing of your request, please indicate as precisely as possible the problem you are experiencing'
w_sav_mail_subject: 'Service request'
w_sav_reason_broken: 'Broken, defective product'
w_sav_reason_malfunction: 'Problem with operating the unit'
w_sav_reason_montage: 'Mounting problem'
w_sav_reason_other: 'Another question'
w_sav_reason_out_of_order: 'Out of stock'
w_sav_successfully_registered: 'Your service request has been sent to the dealer'
w_search_button: Search
w_search_category: 'Search a category'
w_secured_payment: 'Secure payment'
w_secured_payment_page: 'Secure payment page'
w_secured_payment_text: "We rely on the latest security standards and do not have your banking information at any time.\_These are directly transmitted to our banking partner via SSL technology which guarantees a 100% secure payment."
w_see: see
w_see_full_description: 'See the detailed description'
w_see_rma: Returns
w_see_shipments: 'See the Deliveries'
w_select_article_for_sav: 'Select article:'
w_select_article_to_return: 'Select item to return:'
w_select_pickup_location: 'Choisissez votre relais'
w_select_shipping_mode: 'Selection mode of delivery'
w_select_this_pickup_location: 'Choisir ce relais'
w_self_entrepreneur_taxes_information: "VAT not applicable, Art.\_293 B of the CGI"
w_seller_code_explanation: "You have validated the order. <br/>\rYour phone numbers and email have been sent to the buyer to arrange an appointment for hand delivery. It will contact you quickly. <br/>\rTo guarantee the transaction, we developed a system of exchange after validation of a table of codes. It is very simple: <br>\rWhen hand-delivered, once the product has been verified by the buyer, claim the code table from the buyer. This is essential to receive your payment.\rIf the table is consistent with the one shown below, note the missing word: You can then deliver the product to the buyer with confidence, the transaction is guaranteed. If the code table does not conform to yours (ie: different word list) or the buyer is unable to provide it to you, do not hand over the product to the buyer. You will not be charged. <br/>"
w_set_rate1_to_rate0: "The additional cost per item has been seized higher than the postage.\_Both values ​​were equalized."
w_set_shipment_code: 'Renseigner les codes de retrait'
w_shipment_code: 'Code de retrait'
w_shippings_required: "You must have active delivery means to create a physical product.\_Click the <a href=\"[url]\"> Delivery Methods </a> link to configure them"
w_shipping_description: 'Further information'
w_shipping_modes_available: 'Delivery modes available, from'
w_shipping_price_delimiter: 'Delimiter of shipping prices'
w_shipping_total_amount: 'Shipping cost'
w_shippment_invoice: '<p> As a reminder, the contents of the command are as follows: </p>'
w_show_features: 'View available variants'
w_sold_by: 'Sold by'
w_standard_delivery: Standard
w_standby_vendor_after_invoice: "<p> Email: </p> <p> You will soon receive an email containing the tracking information as soon as it is delivered to the carrier. </p> <p> Your invoice will be available for consultation As soon as your order is shipped to your \"<em> my account </em>\" area.\_</p> <p> Our customer service is available through our <a href=\"[form_url]\"> contact form </a> </p>"
w_standby_vendor_after_invoice_for_edp: '<p> Email: <email> </p> <p> Your invoice will be emailed to you and will be available in the "<em> my account </em>" area as soon as the seller issues it . </p> <p> Our customer service is available through our <a href="[form_url]"> contact form. </a> </p>'
w_stay_in_touch: 'We keep in touch ?'
w_store_administrator: 'Administrator of your shop'
w_store_name: 'Shop name'
w_successful_code_registration: "The entered code is correct.\_The order has just been changed to \"Done\". <br/> Payment will be made to your account quickly."
w_successful_shipment_code_registration: 'Le code de retrait saisi est correct.'
w_success_progress: "The task with the identifier [task_id] is finished.\_There have been [success_step] incorrect lines and [fail_step]"
w_supplier_ref: 'Reference supplier'
w_tabs_description: Description
w_tabs_details: 'Technical sheet'
w_tabs_files: Files
w_taxes_order_details: 'Including taxes'
w_tax_required: 'You must select at least one tax'
'w_terms_and conditions': 'Terms of Sales'
w_terms_and_conditions: 'Terms of Sales'
w_terms_and_conditions_short: 'Terms'
w_terms_of_use: 'Terms of Use'
w_text_cart_amount_corrected: "The number of products in stock is insufficient for your order.\_The quantity of the product you just added has been changed in your shopping cart."
w_text_empty_uploaded_file_size: 'The image [url] is not available'
w_text_enter_address: 'Veuillez saisir votre adresse'
w_text_images_delimiter: 'Image delimiter'
w_text_not_approved_option: "You have options waiting for validation.\_<a href=\"[link]\"> follow this link </a> to verify these options."
w_text_shipping_price_delimiter: 'Used to separate the price for the first product sent from the price for each following product'
w_thanks_for_declined_order: "<p> Thank you for your order on [date], unfortunately, following an unexpected stock shortage, the order was refused by the seller [vendor].\_</p>"
w_thanks_for_order: "<p> Thank you for your order on [date]. <br /> This was saved with [order_id].\_</p>"
w_title_info_valide_order: 'To validate the order'
w_title_popin_order_detail: 'Detail of order n ° [order_id]'
w_title_popin_rma_create_litigation: 'Order # [order_id] - Litigation'
w_title_popin_rma_create_return: 'Order # [order_id] - Return'
w_title_popin_rma_create_sav: 'Order [order_id] - After Sales Service'
w_title_popin_rma_detail: 'Detail of return n ° [return_id]'
w_to: To
w_todo_shipment_late: 'What to do if your package is late or damaged? <br /> Please contact customer service via our <a href="[form_url]"> contact form. </a> or in your space <em> my account </em> ".'
w_top-search_placeholder_geoloc-input: 'city ​​or ZIP code'
w_top-search_placeholder_search-input: 'A product, a brand ...'
w_top-search_title_geoloc-input: 'Or ?'
w_top-search_title_search-input: 'What are you looking for ?'
w_totals_with_taxes: 'Totals TTC'
w_total_duty_free: 'Total HT'
w_tracking_number_required: 'The tracking number is mandatory'
w_tt_select_product_status: '1) Active: put on sale immediately (if pre-moderated) <br/> 2) Hidden: product that does not appear for sale but remains editable 3) Disabled: Appears not for sale and not editable, dimmed in the list of products.'
w_tt_select_product_template_type: "If your Product Sheet is linked to a Unified Product Sheed, then the selection of a product template on the Product Sheet is ineffective.<br/>\n The Product Sheet inherits the template from the Unified Product Sheet."
w_tt_views_products_update_brand: 'Trademark under which the product is sold (eg Apple).'
w_tva_amount: 'Includes [amount] of VAT'
w_type: Type
w_type_all: 'Available for both'
w_type_c2c: 'Reserved for individuals'
w_type_delivery: 'Delivery method'
w_type_message: 'Enter your message below:'
w_type_product_c: 'Private seller'
w_type_product_v: 'Professional reseller'
w_type_standard: 'Reserved for professionals'
w_update_amount: 'Update price'
w_update_product_rate: 'Special shipping costs'
w_upload_max_size: 'All files must not exceed'
w_use_default_rate: 'Using default charges?'
w_validate: Validate
w_validate_code: 'Validate the code'
w_validate_command: Checkout
w_vendors_category: 'Personal Name'
w_vendor_account_informations: 'My information'
w_vendor_account_informations_menu_description: 'My vendor Account Information'
w_vendor_apply_subject: 'Creating a sales account'
w_vendor_apply_text: "Hello and welcome [firstname] [lastname], <br/> <br/> We have received all the necessary elements for the creation of your seller account on our marketplace. <br/> We are delighted to count you among our new Enrolled.\_Upon validation of your documents by our sales team, an email with your login credentials will be sent to you.\_You will thus have all the necessary access to the import of your products <br/> <br/> See you very soon, <br/>"
w_vendor_candidate_come_from: 'The account was created with a tracking code of [tracking_value].'
w_vendor_capital: Capital
w_vendor_independent_declaration: 'I declare that I am a self-employed entrepreneur or self-entrepreneur and do not have an intra-Community VAT number.'
w_vendor_info: 'About the Seller'
w_vendor_rcs: RCS
w_vendor_registration_title: Subscribe
w_vendor_text_letter_footer: 'The sales team <br/> <a href="mailto:[email]"> [email] </a>'
w_view_all_results: 'View All Results'
w_view_annonce_manage_link: See
w_view_feature: 'See attributes in the first category'
w_wizacha_guarantee: 'Guarantee: payment after delivery'
w_years: years
w_your_pseudonym: 'your pseudonym'
w_your_store: 'Your shop'
w_zipcode_indication: 'City (Postal Code)'
xls_currencies_note: 'Please note: prices in XLS will be quoted in the base currency'
xml_info: 'Use this link to download prices in XLS format (MS Excel)'
year: Year
yearly: Annually
'yes': 'Yes'
yesterday: Yesterday
your_account: 'Your account'
your_age: 'Your age'
your_email: 'Your email'
your_ip_added: 'Your IP address <b> [ip] </b> has been added to the restricted access table'
your_ip_enabled: 'Your IP address <b> [ip] </b> has been activated on the restricted access table.'
your_ip_removed: 'Your IP address <b> [ip] </b> has been removed from the restricted access table.'
your_message: 'Your message'
your_name: 'Your name'
your_range: 'Your range'
your_rating: 'Your rating'
youtube_link: 'Https://www.youtube.com/'
you_can_edit_account_details: 'You can edit account details now.'
you_have_no_permissions: 'You do not have permission'
you_save: 'You save'
zero_price_action: 'Share price nil'
zipcodes: 'Postal codes'
zip_postal_code: 'Postal code'
zone: Zoned
zoom: Zoom
zoom_control: 'Zoom Control'
zpa_ask_price: 'Ask the customer to enter the price'
zpa_permit: 'Add product to cart'
zpa_refuse: 'Do not allow product to be added to cart'
wizaplace_support: 'Support'
mark_as_paid: 'Mark as paid'
organisation_group_admin_label: 'Administrator'
refuse: 'Refuse'
order_cancel: 'Order cancel'
order_completion: 'Order completion'
mark_as_completed: 'Mark as completed'
api_access_section:
    documentation_title: 'API Documentation'
    documentation_link_introduction: |
        The documentation for the marketplace's API is available through the link below:
    documentation_link_text: 'API Documentation'
    application_token_title: 'Application token'
    application_token_introduction: |
        The application to use in the <code>Application-Token</code> HTTP header is:
    application_token_keep_secret: |
        This application token should not be shared publicly because it restricts the access to the marketplace's API. If it is necessary to regenerate it please get in touch with the support.
    no_application_token: |
        The API of the marketplace is public and do not require an application token. If you wish to protect your API with an application token please get in touch with the support.
legal_documents: Legal documents
address_proof: Proof of address
updated_and_signed_status_document: Last version of Company's Articles of Association signed
document_division_of_powers: Signed recipients identification
document_division_of_powers_id_cards: ID Card of recipients
csv_export_complete: 'CSV export completed'
csv_export_complete_text: 'Your CSV file is ready to be downloaded'
csv_export_complete_notification: 'The export started, you will receive an email when the file will be ready'
stripe_checkout_submit_payment: Submit Payment
stripe_checkout_label: Credit or debit card
stripe_checkout_submit_payment_sepa_1: Submit
stripe_checkout_submit_payment_sepa_2: Continue
stripe_sepa_mandate_warning: By continuing, you confirm that you have consulted the debit mandate
stripe_mandat: Mandate
stripe_missing_kyc_fields: One of the sent fields is wrong. Please contact an administrator of your marketplace.
stripe_sepa_mandate_acceptance: |
    By signing this mandate form, you authorise [name] and Stripe to send instructions to your bank to debit your account and your bank to debit your account in accordance with the instructions from [name] and Stripe.
    As part of your rights, you are entitled to a refund from your bank under the terms and conditions of your agreement with your bank.
    A refund must be claimed within 8 weeks starting from the date on which your account was debited.
stripe_business_name: Business name
stripe_business_id: SIREN
stripe_no_iban: You don't have IBAN and SWIFT
stripe_no_id_card: You don't have an ID document
stripe_upload_fail: ID document upload failed. Please retry with a JPG or PNG file.
stripe_create_custom_account_fail: Stripe cannot create your Account. Please contact our support.
stripe_tos_link: 'I accept the <a href="https://stripe.com/fr/connect-account/legal" target="_blank">Stripe Connected Account Agreement</a>'
stripe_additional_owner: Additional owner (who own at least 25%)
personal_id_number: Personal ID number
transit_number: Transit number
cannot_change_processor_on_payment_after_creation: 'It''s impossible to change the payment processor once the payment is created. You must create a new payment if you want to change it.'
advanced_settings: Advanced settings
accounting: Accounting
billing_number_autoincrement_cannot_be_activated: You cannot enable the automatic invoice and RMA number generation feature because orders have been placed on this account.
billing_number_autoincrement_checkbox_label: Billing auto-increment number generation
billing_number_autoincrement_disabled_confirmation: If you disable this feature, and orders have already been placed on this account, you will not be able to reactivate it.
rma_number_autoincrement_cannot_be_activated: You cannot activate the automatic RMA number generation feature because orders have been placed on this account.
rma_number_autoincrement_checkbox_label: Auto-increment RMA number
rma_number_autoincrement_disabled_confirmation: If you disable this feature, and orders have already been placed on this account, you will not be able to reactivate it.
zipcode: 'Zip code'
organisation_registered: Organisation pending moderation
organisation_registered_mail_content: 'The organization [organisation_name] has been newly created, you should check the details and activate it from your administration UI.'
organisation_approved: Organisation approved
organisation_approved_mail_content: 'Your organization has been approved, you can now log in.'
organisation_disapproved: Organisation disapproved
organisation_disapproved_mail_content: 'Thank you for submitting your organization, unfortunately it has not been accepted by the site administration. For more informations, please contact the marketplace administrator.'
organisation_status_activated: 'Activated'
organisation_status_deactivated: 'Deactivated'
organisation_status_pending: 'Pending'
organisation_delete: Delete
organisation_action: Action
workflow_bank_transfer_payment_pending_redirection_to_payment_processor_processing: 'Pending redirection to payment processor'
workflow_bank_transfer_payment_pending_redirection_to_payment_processor_completed: 'Redirection to payment processor completed'
workflow_bank_transfer_payment_pending_redirection_to_payment_processor_failed: 'Redirection to payment processor failed'
workflow_bank_transfer_payment_pending_bank_validation_processing: 'Pending bank validation'
workflow_bank_transfer_payment_pending_bank_validation_completed: 'Bank validation completed'
workflow_bank_transfer_payment_pending_bank_validation_failed: 'Bank validation failed'
workflow_credit_card_payment_authorization_pending_redirection_to_payment_processor_processing: 'Pending redirection to payment processor'
workflow_credit_card_payment_authorization_pending_redirection_to_payment_processor_complete: 'Redirection to payment processor completed'
workflow_credit_card_payment_authorization_pending_redirection_to_payment_processor_failed: 'Redirection to payment processor failed'
workflow_credit_card_payment_authorization_pending_authorization_processing: 'Pending payment authorization'
workflow_credit_card_payment_authorization_pending_authorization_complete: 'Payment authorization completed'
workflow_credit_card_payment_authorization_pending_authorization_failed: 'Payment authorization failed'
workflow_credit_card_payment_capture_pending_bank_capture_processing: 'Pending payment capture'
workflow_credit_card_payment_capture_pending_bank_capture_completed: 'Payment capture completed'
workflow_credit_card_payment_capture_pending_bank_capture_failed: 'Payment capture failed'
workflow_credit_card_payment_pending_redirection_to_payment_processor_processing: 'Pending redirection to payment processor'
workflow_credit_card_payment_pending_redirection_to_payment_processor_completed: 'Redirection to payment processor completed'
workflow_credit_card_payment_pending_redirection_to_payment_processor_failed: 'Redirection to payment processor failed'
workflow_credit_card_payment_pending_bank_validation_processing: 'Pending bank validation'
workflow_credit_card_payment_pending_bank_validation_completed: 'Bank validation completed'
workflow_credit_card_payment_pending_bank_validation_failed: 'Bank validation failed'
workflow_delivery_pending_delivery_processing: 'Pending delivery'
workflow_delivery_pending_delivery_completed: 'Delivery completed'
workflow_delivery_pending_delivery_failed: 'Delivery failed'
workflow_funds_dispatch_pending_funds_dispatch_processing: 'Pending funds dispatch'
workflow_funds_dispatch_pending_funds_dispatch_completed: 'Funds dispatch completed'
workflow_funds_dispatch_pending_funds_dispatch_failed: 'Funds dispatch failed'
workflow_manual_payment_pending_manual_payment_processing: 'Pending manual payment'
workflow_manual_payment_pending_manual_payment_completed: 'Manual payment completed'
workflow_manual_payment_pending_manual_payment_failed: 'Manual payment failed'
workflow_order_commitment_pending_order_commitment_processing: 'Pending order commitment'
workflow_order_commitment_pending_order_commitment_completed: 'Order commitment completed'
workflow_order_commitment_pending_order_commitment_failed: 'Order commitment failed'
workflow_order_confirmation_pending_order_confirmation_processing: 'Pending order confirmation'
workflow_order_confirmation_pending_order_confirmation_completed: 'Order confirmed'
workflow_order_confirmation_pending_order_confirmation_failed: 'Order confirmation failed'
workflow_order_preparation_pending_vendor_preparation_end_processing: 'Pending vendor preparation end'
workflow_order_preparation_pending_vendor_preparation_end_completed: 'Vendor preparation completed'
workflow_order_preparation_pending_vendor_preparation_end_failed: 'Vendor preparation failed'
workflow_payment_deferment_authorization_pending_authorization_processing: 'Pending payment authorization'
workflow_payment_deferment_authorization_pending_authorization_completed: 'Payment authorization completed'
workflow_payment_deferment_authorization_pending_authorization_failed: 'Payment authorization failed'
workflow_order_validation_pending_vendor_validation_processing: 'Pending vendor validation'
workflow_order_validation_pending_vendor_validation_completed: 'Vendor validation completed'
workflow_order_validation_pending_vendor_validation_failed: 'Vendor validation failed'
workflow_wait_payment_deferment_pending_redirection_to_payment_processor_processing: 'Pending redirection to payment processor'
workflow_wait_payment_deferment_pending_redirection_to_payment_processor_completed: 'Redirection to payment processor completed'
workflow_wait_payment_deferment_pending_redirection_to_payment_processor_failed: 'Redirection to payment processor failed'
workflow_wait_payment_deferment_pending_bank_validation_processing: 'Pending bank validation'
workflow_wait_payment_deferment_pending_bank_validation_completed: 'Bank validation completed'
workflow_wait_payment_deferment_pending_bank_validation_failed: 'Bank validation failed'
workflow_withdrawal_period_pending_withdrawal_period_end_processing: 'Pending withdrawal period end'
workflow_withdrawal_period_pending_withdrawal_period_end_completed: 'Withdrawal period completed'
workflow_withdrawal_period_pending_withdrawal_period_end_failed: 'Failure during withdrawal period'
workflow_canceled: 'Canceled'
workflow_refunded: 'Refunded'
single_sign_on: 'Single Sign On'
invalid_image: 'Image is not valid'
extra_fields: Extra fields
search_by_reference: Search by reference
product_template_product: 'Product'
product_template_service: 'Service'
product_template_type: 'Template'
cannot_find_image: 'the image cannot be found'
imported: 'Imported'
rejected: 'Rejected'
warnings: 'Warnings'
duration: 'Duration'
target: 'Target'
external: 'external'
internal: 'internal'
available_offers_product: Available offers product
available_offers_allowed_divisions: Allowed divisions
available_offers_divisions: Divisions
available_offers_select_divisions: Select divisions
available_offers_sidebar_info: You can edit a particular division by clicking on its name in the list below.
available_offers_sidebar_note: Below are only the activated divisions.
available_offers_all_divisions: All divisions
text_available_offers_have_been_updated: Divisions saved with success
exim_available_offers_user_not_found: Unable to find the user
exim_available_offers_no_divisions_found: No division specified
exim_available_offers_division_import_success: Divisions imported with success
exim_available_offers_not_found: Some divisions are not found (%)
text_available_offers_placeholder: Search a division
js_available_offers_error_product_save: You must have a least one division enabled to save an enabled or hidden product.
js_available_offers_error_company_save: You must have a least one division enabled to save an enabled company.
js_available_offers_need_reload: The divisions have been reloaded. If you've edited them, don't forget to reopen the tab.
text_toggle_divisions: Enable/Disable all divisions
search_available_offers_products_without_divisions: Find products enabled or hidden without divisions
search_available_offers_products_in_divisions: Find products with at least one of the divisions checked
field_delimiter: Field delimiter
rma_action_1: Replace
rma_action_2: Refunds
rma_action_3: Package arrived in poor condition, damaged
rma_action_4: Does not correspond to the product ordered
rma_action_5: Broken, defective product
rma_action_6: I can't get it to work or assemble it
rma_action_7: Not the right size
rma_action_8: Order error
rma_action_9: Doesn't suit me anymore, I changed my mind
attributes_delimiter: Attribute delimiter
text_attributes_delimiter: Delimiter for product attributes.
exim_error_mvp_empty_line: Empty line
exim_error_mvp_unknown_id: MVP Id not found
exim_error_mvp_save: 'MVP: "%"'
exim_error_mvp_language_not_valid: The language field is not valid
exim_success_mvp_import: Unified product sheets imported with success
exim_warning_mvp_attributes: 'Attributes: "%"'
exim_warning_mvp_product_template_not_valid: The template is not valid
exim_warning_mvp_free_attributes: 'Free attributes: "%"'
exim_warning_mvp_image: 'Image : "%"'
exim_help_mvp_multilang_title: Import of multi-language multiple vendor product (MVP).
exim_help_mvp_multilang_desc: |
    Proceed as follow to import MVP in several languages :
    <ol>
      <li>Import MVP to be created : field <code>id</code> must remain empty, field <code>language</code> must contain a SINGLE language 2 characters code.</li>
      <li>Export MVP via CSV in order to collect previously created MVP Id.</li>
      <li>Import MVP in any other language wished. There will be a single line for each language in CSV file. Fill in the field <code>id</code> the value of <code>id</code> created at step 1 and collected at step 2. Fill in the field <code>language</code> with 2 characters code.</li>
    </ol>
    Note : for existing MVP, start from step 2.
exim_help_mvp_rules_title: Rule description
exim_help_mvp_rules_header1: Fields
exim_help_mvp_rules_header2: Rule description
exim_help_mvp_rules_header3: Required
exim_help_mvp_rules_id: When creating a new Multi-vendor Product (MVP), leave <code>id</code> field empty. An id will be generated automatically. When updating the MVP, fill it with the id given by the markeplace to identify the MVP to update.
exim_help_mvp_rules_locale: Fill in the language code.
exim_help_mvp_rules_name: MVP's name
exim_help_mvp_rules_code: EAN Code. If the field is left empty, a code is generated automatically by the marketplace. This is also the case when modificating the MVP.
exim_help_mvp_rules_supplier_reference: MVP Supplier reference
exim_help_mvp_rules_slug: SEO Name
exim_help_mvp_rules_seo_title: (SEO) Page title
exim_help_mvp_rules_short_description: Short description
exim_help_mvp_rules_description: Long description
exim_help_mvp_rules_seo_description: META description
exim_help_mvp_rules_seo_keywords: META keywords
exim_help_mvp_rules_status: <code>A</code> = Activated; <code>D</code> = Disabled ; <code>H</code> = Hidden
exim_help_mvp_rules_category_id: Id of the lowest category level of the MVP
exim_help_mvp_rules_attributes: |
    MVP's Attributes list.
    Syntax : <code>"Attribute 1 Label": "value1"///"value2"; "Attribute 2 Label": "value"</code>. Labels and values must filled in the line language. The default value separator is <code>///</code>. It can be modified in the import options section.
exim_help_mvp_rules_freeattributes: 'Syntax : <code>"Attribute 1 Label": "value"; "Attribute 2 Label": "value"</code>'
exim_help_mvp_rules_product_template_type: <code>product</code> or <code>service</code>
exim_help_mvp_rules_images_urls: URL on which the image can be downloaded. To upload several images, separate URLs with <code>####</code>. The default separator can be modified in the import options section.
exim_help_mvp_divers_title: Miscellaneous
exim_help_mvp_divers_desc: |
    The link between FPU and product cannot be updated by import<br/>
    In modification mode, an empty cell deletes the content previously imported.
exim_help_mvp_multilang: "<h4>Import of multi-language multiple vendor product (MVP).</h4>
                             Proceed as follow to import MVP in several languages :
                             <ol>
                                <li>Import MVP to be created : field <code>id</code> must remain empty, field <code>language</code> must contain a SINGLE language 2 characters code.</li>
                                <li>Export MVP via CSV in order to collect previously created MVP Id.</li>
                                <li>Import MVP in any other language wished. There will be a single line for each language in CSV file. Fill in the field <code>id</code> the value of <code>id</code> created at step 1 and collected at step 2. Fill in the field <code>language</code> with 2 characters code.</li>
                             </ol>
                             Note : for existing MVP, start from step 2."
exim_help_mvp_ean_code: "<h4>Code</h4>If this field is empty, it will be automatically generated."
label_upload_file: From local file
label_upload_url: From an URL
orders_details: Orders details
date_from: From
date_to: To
accounting_export_intro: Orders displayed in export file entered status Ended/Finished prior to the current 15 days period. Orders that entered status Ended/Finished in the current 15 days period will be available in export file from the next period. 15 days periods runs from the 1st to the 15th of the month included and from the 16th to the last day of the month
user_currency: Currency
create_profile_fields: Create translations for profile fields
create_language_translation: Create translations
missing_profile_fields_file: The translation file is missing. Create the file and restart the creation.
create_profile_fields_success: Translations for profile fields have been successfully created.
create_language_translation_success: Translations have been successfully created.
adjustments: Adjustments
oldprice: Old price excl tax
newprice: New price excl tax
oldtotal: Old total incl tax
newtotal: New total incl tax
has_acceptation_delay: Acceptance period
order_acceptation_delay: Delay in a day
w_promotions_marketplace: Marketplace promotions
marketplace_promotions_menu_description: Marketplace promotions list.
marketplace_promotions: Marketplace promotions
marketplace_promotion: Discount marketplace
start_past_date_validation_error: The start date must not be before today.
marketplace_promotion_already_exist: There is already a marketplace promotion with the same coupon and period.
marketplace_promotion_update_information: Warning, It will not be possible to update promotional coupon, validity period, rules of the discount and bonus after saving the promotion.
customer_total: Customer total
libelle: Label
total_paid: Total paid
marketplace_discount_coupon: Discount coupon
w_after_sales_service_requested: New after sales request
exim_help_marketplace_discounts_multilang_title: All promotions amount are including taxes and registered in the marketplace currency
exim_help_marketplace_discounts_rules_header1: Fields
exim_help_marketplace_discounts_rules_header2: Description
exim_help_marketplace_discounts_rules_header3: Required
exim_help_marketplace_discounts_rules_id: Technical Id of the promotion of the marketplace
exim_help_marketplace_discounts_rules_name: Promotion name
exim_help_marketplace_discounts_rules_active: "If active: Y | If inactive: N"
exim_help_marketplace_discounts_rules_start_time: Promotion start date. (The promotion will start at 00:00)
exim_help_marketplace_discounts_rules_end_time: Promotion end date. (The promotion will end at 23:59
exim_help_marketplace_discounts_rules_coupon: Coupon code defined in the promotion
exim_help_marketplace_discounts_rules_basket_has_product_in_list: "Yes |  No"
exim_help_marketplace_discounts_rules_product_list: 'If "Rule basket has product in list" contains yes, then this field contains the list of products selected. If several products are selected, they are separated by commas'
exim_help_marketplace_discounts_rules_basket_price_superior_to: Basket total including taxes
exim_help_marketplace_discounts_rules_basket_price_inferior_to: Basket total including taxes
exim_help_marketplace_discounts_rules_basket_price_superior_or_equal_to: Basket total including taxes
exim_help_marketplace_discounts_rules_basket_price_inferior_or_equal_to: Basket total including taxes
exim_help_marketplace_discounts_rules_max_usage_count: Promotion maximum number of use
exim_help_marketplace_discounts_rules_max_usage_count_per_user: Promotion maximum number of use per user
exim_help_marketplace_discounts_rules_discount_type: Reduction type defined in the promotion
exim_help_marketplace_discounts_rules_discount_value: Discount value (either a percentage or a value in case of fixed reduction)
exim_help_marketplace_discounts_rules_bonus_applied_on: Bonus calculation base
no_portrait_in_mobile: Version not available in portrait mode on your mobile. Live the experience in landscape mode.
feature.tier_pricing: 'Activation of Product Price Tiers'
supplier_reference_is_empty: Supplier reference is empty.
product_has_subscription_type: Product has subscription type
product_has_renewal: Product has renewal
system_options: System options
show_only_system_options: Show only system options
subscription_notification_subject_customer_success: 'Your subscription: payment confirmation'
subscription_notification_mail_customer_success: |
    Payment of your subscription <strong>"[subscriptionName]"</strong> was successful.<br>
    <br>
    Invoice details are available by logging into your <a href="[customerAccountLink]" target="_blank">customer account</a>.<br>
subscription_notification_subject_vendor_success: 'Subscription payment confirmation'
subscription_notification_mail_vendor_success: |
    Payment of the following subscription <strong>"[subscriptionName]"</strong> ([subscriptionId]) was successful.<br>
    <br>
    [Any/The] service related to this subscription should be maintained unless an exception is encountered.<br>
subscription_notification_subject_customer_fail: 'Your subscription: payment issue'
subscription_notification_mail_customer_fail: |
    There was an issue with the payment of your subscription <strong>"[subscriptionName]"</strong>.<br>
    Please log in to your <a href="[customerAccountLink]" target="_blank">customer account</a> or contact the sales team to proceed.<br>
    <br>
    Please note that unless this issue is corrected, related services could be suspended.
subscription_notification_subject_vendor_fail: 'Subscription payment issue'
subscription_notification_mail_vendor_fail: |
    There was an issue with the payment of the following subscription <strong>"[subscriptionName]"</strong> ([subscriptionId]).<br>
    <br>
    A notification was sent to the customer. You may check this payment issue to proceed with this subscription.<br>
credit_card_renew_user: 'Renew credit card User #'
subscriptions_menu_description: Subscriptions listing
subscription_name: Subscription
subscription_user: Customer
subscription_product: Product
subscription_next_payment: Next payment
subscription_total: Total
subscription_status: Status
subscription_frequency_label: Frequency
subscription_period: Periods
subscription_last_command: Last command
subscription_createdat: Created at
subscription_payment_history: Payments history
subscription_payment_method: Payment method
subscription_user_subscriptions: User's subscriptions
subscription_commitment_period: Commitment period
subscription_last_payment_status: Last payment status
subscription_payment_frequency: Payment frequency
subscription_autorenew: Auto renew
subscription_begin: Start date
subscription_last_payment_date: Last payment date
subscription_next_payment_at: Next payment at
subscription_commitment_end_at: Commitment end at
unit_price_ht: Unit price HT
total_price_ht: Total price HT
unit_price_ttc: Unit price TTC
total_price_ttc: Total price TTC
subscription_applied_taxes: Applied taxes
subscription_taxes: Taxes
subscription_total_taxes: Total taxes
subscription_birthday: Born on
subscription_lang_code: Lang. code
subscription_payment_status_paid: Paid
subscription_payment_status_not_paid: Not paid
quarterly: Quarterly
bi_annual: Bi-annual
annual: Annual
subscription_amount: Amount
subscription_credit_card_id: Card ID
subscription_credit_card_brand: Brand
subscription_credit_card_pan: PAN
subscription_credit_card_expiry_date: Expiry
subscription_credit_card_holder: Holder
subscription_credit_card_issuer: Issuer
subscription_credit_card_country: Country code
subscription_renew_credit_card: Update payment method
subscription_renew_credit_card_infos: Update of a bank card triggers a verification based on a 1€ authorization request on the bank account. This amount is not paid. Update may take several minutes.
subscription_frequency: Frequency
ht: Excluding taxes
subscription_nextpayment: Next payment
subscription_state: State
renew_credit_card_success: Your credit card has been successfully registered.
error_renew_credit_card: Error while trying to renew credit card.
subscription_update_status_error: Error while updating subscription status.
subscription_update_quantity_error: A subscription’s quantity must be higher than 0.
subscription_payment_history_message:  The first line displays the amount of the first order of the subscription. It possibly contains fees, amounts related to other subscriptions or products parts of this first order. It may differs from the price of your subscription.
subscription.status.active: Active
subscription.status.finished: Finished
subscription.status.defaulted: Defaulted
subscription.status.disabled: Disabled
subscription.status.suspended: Suspended
subscription.status.waiting_renew: Waiting renewal
subscription_link_status: Link with subscriptions
subscription_link_status_0: Punctual order
subscription_link_status_1: Initiating order
subscription_link_status_2: Recurring order
subscription_renew: Renew
subscription_renew_confirm_modal: This action will trigger a new payment for the client, are you sure you want to renew this subscription ?
subscription_not_found: Subscription not found
subscription_renewed: Subscription has been renewed
subscription_renew_error: An error occurred during renewal of the subscription.
subscription_error_next_payment_at: Next payment date must be superior to today included and limited to commitment end date if the subscription is not renewable.
subscription_error_next_payment_at_not_active: Next payment date can't be modified if the subscription is not active, defaulted or waiting for renew.
subscription_error_commitment_end_at: Commitment date must be today or after today.
subscription_error_commitment_end_at_not_active: Commitment date can't be modified if the subscription is not active, defaulted or waiting for renew.
exim_import_option_system_variants_fail: Import of variants for system options is not allowed
transaction_creditcard: Credit Card Payment
transaction_bank_wire: Bank wire
transaction_refund: Refund
transaction_direct_debit: Direct debit
transaction_refund_transfer: Refund Discount
transaction_transfer_coupon: Discount
transaction_transfer: Discount
transaction_offline: Offline
iban_or_bic_empty: Iban and Bic can't be empty.
bankwire_notification_failed_status_subject: Bankwire notification with status failed.
bankwire_notification_failed_status_body: |
    <p>
        We've received a bankwire error notification from %pspName%.<br/>
        The related order payment has been marked as failed.
    </p>
    <p>
        Orders ID: %ordersId%<br/>
        Transaction ID: %transactionId%<br/>
        Amount: %amount%<br/>
    </p>
bankwire_notification_failed_to_retrieve_orders_subject: 'Bank Transfer #%transactionId% received of %amount%%currency% not recognized'
bankwire_notification_failed_to_retrieve_orders_body: |
    <p>
        We received a bank transfer which couldn’t be identified. It could be for one of the following reasons:<br>
        - The reference was not submitted or doesn’t match any order awaiting payment.<br>
        - The amount doesn’t match exactly the referenced order.
    </p>
    <p>
        Transaction ID: %transactionId%<br/>
        Reference: %token%<br/>
        Amount: %amount%%currency%]
    </p>
internal_transfer_failed_subject: Error at internal PSP transfer.
internal_transfer_failed_body: |
    <p>
        An error occurred during an internal psp transfer.<br/>
        The transfer has not been proceded.
    </p>
    <p>
        Order ID: %orderId%<br/>
        Amount: %amount%<br/>
        Source transaction ID: %sourceTransactionId%<br/>
        Internal transaction ID: %internalTransactionId%<br/>
        Wallet from: %fromWallet%<br/>
        Wallet to: %toWallet%<br/>
    </p>
stripe_user_callback_url_text: Callback URL for user update IBAN (customer.source.created) that should be set on Stripe dashboard
exim_import_option_system_variants_disabled_fail: Import of disabled variants for system options is not allowed. Content has been ignored.
set_valid_email_export_email: Please enter a valid email address to receive the export.
account_is_locked_status_cannot_change: This account is locked.
credit_note: Credit note
credit_note.capital: CREDIT NOTE
credit_note.title: Credit note invoice
credit_note.order: Order
credit_note.3rd_party_invoice: Credit note invoice automatically generated for third parties
credit_note.number: no.
credit_note.from: From
credit_note.date_format: m/d/Y, H:i
credit_note.shipping_address: Shipping address
credit_note.billing_address: Billing address
credit_note.table.product: Product
credit_note.table.product_name: Name
credit_note.table.quantity: Quantity
credit_note.table.unit_price_with_taxes: Unit price
credit_note.table.unit_price_without_taxes: Unit price excl tax
credit_note.table.green_tax: incl eco part
credit_note.table.subtotal: Subtotal excl tax
credit_note.total.net_without_taxes: Total NET excl tax
credit_note.total.green_tax: Including eco partication
credit_note.total.shipping_cost: Shipping costs
credit_note.total.total_discount: Discount
credit_note.total.vat: VAT
credit_note.total.total: Total incl tax
email_notification_refund_customer_subject: Your refund
email_notification_refund_customer_body: >
    Your order [orderId] has been reimbursed.<br>
    <br>
    You refund has been launched. You will be reimbursed according to the payment method used.
email_notification_refund_company_subject: Order [orderId] has been refunded
email_notification_refund_company_body: >
    Order [orderId] has been reimbursed.<br>
    <br>
    The remaining balance is [orderBalance] [currency].<br>
    <br>
    For more information, check order details.
email_notification_refund_admin_subject: Order [orderId] has been refunded
email_notification_refund_admin_body: >
    Order [orderId] has been reimbursed.<br>
    <br>
    The remaining balance is [orderBalance] [currency].<br>
    <br>
    For more information, check order details.
order_refund_event: An order has been refunded
accounting_documents: Accounting documents
accounting_documents.name: Name
accounting_documents.date: Date
accounting_documents.amount: Amount
accounting_documents.actions: Actions
accounting_documents.view: View document
accounting_documents.download: Download file as PDF
returns_and_refunds: Returns and refunds
returns_and_refunds.returns.id: \#
returns_and_refunds.returns.status: Status
returns_and_refunds.returns.date: Date
returns_and_refunds.returns.amount: Total
returns_and_refunds.refunds.id: ID
returns_and_refunds.refunds.date: Date
returns_and_refunds.refunds.amount: Amount
returns_and_refunds.refunds.status: Status
returns_and_refunds.refunds.actions: Actions
order_refund_status_0: Not refunded
order_refund_status_1: Refund failed
order_refund_status_2: Fully refunded
order_refund_status_3: Partially refunded
order_refund_status: Refund status
back: Back
refund_view_details: See the refund details
partial_refund: Partial refund
refund_confirm_modal: Are you sure you want to refund this command?
refund_does_not_exist: This refund doesn't exist
required_action: Action required
order_refunded: Refunded
order_cannot_be_refunded: The order can't be refunded
refund_status_does_not_allow_more_refund: The refund status of the order does not allow more refund requests
order_must_be_paid_for_refund: The order must be paid to be refunded
order_status_does_not_allow_refund: The order status does not allow requested refund
refund_amount_is_zero: No amount to refund
item_number_mismatch_complete_refund: 'Complete refund: number of items to refund does not match the order'
no_shipping_complete_refund: Complete refunds must include shipping costs
no_item_partial_refund: Partial refund requested with nothing to refund
shipping_error_on_refund: Refund includes shipping costs but does not refund shipping
refund_amount_mismatch: Calculated amount does not match provided amount
refund_amount_greater_than_order_amount: 'Refunds: trying to refund more than the order total'
refund_item_quantity_error: 'Refunds: trying to refund more than item quantity'
transaction_amount_greater_than_order_amount: 'Transactions: trying to refund more than the order total'
bad_refund_item_format: 'Items must contain both "itemId" and "quantity" keys'
refund_select_products: Please select at least one product
refund_select_credit_note_number: Please enter a credit note number
refund_select_quantities: Invalid format for quantity
cannot_refund_bad_payment_method: Can't refund on a credit card a transaction that was not paid by credit card
refund_is_not_implemented: The feature Refund is not activated
cannot_refund_mp_discount_negative_amount: 'Marketplace discount: can not refund a negative amount'
cannot_refund_mp_discount_too_big: 'Marketplace discount: can not refund more than the discount amount'
feature.refund: Refund orders
feature.refund_auto: Refund auto
legal_mention_capital: with a capital of
invoice_copy: Copy of invoice
refund_fully: Full Refund
refund_payment_method_invalid: Parameter paymentMethod not implemented or invalid
refund_payment_method_does_not_match_transaction_type : Cannot pay later a refund when a credit card transaction exists. Use mark_refund_as_paid or refund_cb instead.
refund.error.original_transaction_not_found: Origin payment transaction for this order could not be found
refund.error.multiple_transactions_not_supported: Orders with multiple refundable transactions are not supported yet
refund.refund_cannot_be_made: Refund could not be made
refund.error.refund_failed: Refund transaction failed. Please contact your PSP to finalize the refund or identify the root cause of this issue.
refund.method: Refund method
refund.no_item_refunded: No item refunded
principal_address: 'Main address'
go_to_book_address: 'See address book'
add_new_address: Add new address
address_title: Address title
shipping_and_billing: shipping and billing
error_max_address: Address has not been registered – you can’t save more than 20 adresses – Please delete address to registred a new address in address book
error_country: Address has not been registered – Invalid country code
add_to_address_book: 'Add to address book'
edit_address: 'Edit address'
go_to_principal_address: see principal address
text_address_book_have_been_deleted: The address book have been successfully removed.
address_book: 'Address book'
divisions_to_enable: Divisions to enable
divisions_to_disable: Divisions to disable
divisions_bad_request : Wrong divisions sent
activate_division: Enabled divisions
excluded_division: Excluded divisions
empty_active_divisions: Empty enabled divisions
empty_excluded_divisions: Empty excluded divisions
division_sidebar_help: |
    <p>Manage divisions: define available divisions and disable divisions to exclude.</p>
    <br>
    <p>An active division includes all sub-divisions, except for its sub-divisions that have been excluded.</p>
    <br>
    <p>An excluded division includes all its sub-divisions.</p>
    <br>
    <p>Excluded divisions prevail over active divisions.</p>
available_divisions: Available divisions
enable_all_divisions: Enable all divisions
division_invalid_code_provided: Invalid division code provided.
unavailable_divisions: Unavailable divisions
all_divisions: All divisions
async_invalid_callback: Please provide existing async callback.
division_admin_edit_warning: |
    Changing the perimeter of the marketplace can have impacts on your companies.
division_vendor_edit_warning: |
    Changing the perimeter of your shop can have impacts on your products.
no_division_for_vendor: This company has no active division
no_division_for_product: This product has no active division
sepa_mandate_not_found: You do not have a registered sepa mandate
sepa_mandate_not_signed: Your SEPA mandate is not signed
order_attachments.table_header.name: Title
order_attachments.table_header.type: Type
order_attachments.table_header.created_at: Created at
order_attachments.table_header.updated_at: Updated at
order_attachments.table_header.created_by: Created by
order_attachments.table_header.updated_by: Updated by
order_attachments.table_header.tooltip: Actions
order_attachments.table_content.date_format: m/d/Y at H:i
order_attachments.table_content.type_customer_invoice: Customer invoice
order_attachments.table_content.type_delivery_bill: Delivery bill
order_attachments.table_content.type_other: Other
order_attachments.table_content.tooltip_view: See the document
order_attachments.table_content.tooltip_download: Download the document
order_attachments.table_content.tooltip_delete: Delete the document
order_attachments.add_file.title: Additional document
order_attachments.delete.message: Are you sure you want to delete this attachment?
order_attachments.exception.order_not_found: Order not found
order_attachments.exception.upload_fail: The attachment could not be uploaded
order_attachments.attachments: Attachments
authenticator_throttling: You have made too many login attempts, please wait %waitingTime% seconds before trying again
order_already_refunded: The order is already refunded
exim_unsupported_video_extension: 'Unsupported Video Extension "%ext%"'
exim_unvalid_url_video: 'URL does not point to video'
exim_video_size_exception: 'Video exceeds maximum size'
dispatch_funds_failed_subject: Funds dispatch for order %orderId% has failed
dispatch_funds_failed_failed_body: |
    <p>
        Funds dispatch for Order %orderId% and merchant %companyName% has failed.
    </p>
    <p>
        Vendor Share including tax: %partVendorTTC%<br/>
        Commission including tax: %commissionTTC%
    </p>
    <p>
        Please contact your PSP for further information on transfer failure reason and
        try dispatch funds again.
    </p>
    <p>
        Best Regards,
    </p>
    <p>
        Wizaplace Support
    </p>
division_product_unavailable: Product is unavailable
marketplace_divisions: Marketplace divisions
currencies_name: Currencies
currencies_management: Currencies management
invalid_status_type: Type's status must be boolean
currency_not_found: Currency not found
currency_updated_failed_notification_subject: Updating currency rates failed
currency_updated_failed_notification_body: |
    <p>
        An error occurred while updating currency rates.
    </p>
    <p>
        <b>Error</b>: %messageToAdmin%
    </p>
exim_unsupported_shipping_format: 'Unsupported Shipping Format : %shipping%'
no_division_for_product_in_marketplace: No company division (No marketplace division)
no_division_for_vendor_in_product: No company division
no_division_in_marketplace: No marketplace division
transaction_dispatch_funds_transfer_vendor: 'Merchant Transfer'
transaction_dispatch_funds_transfer_commission: 'Commission Transfer'
transaction_vendor_withdrawal: 'Vendor withdrawal'
paiments_refunds: 'Cash In and Refunds'
transfers: 'Transfer'
number_of_selected_items: 'Number of selected items: '
lang_code_not_valid: 'The language code provided is not valid'
inventory_prices: Products prices
inventory_quantities: Products quantities
product_quantities: Products quantities
storage_config_error: Impossible to display order's attachments due to a configuration issue
storage_file_not_exist: 'The file %file_name% does not exist'
mark_as_refunded: Mark as refunded
refunded_after_funds_dispatch: Refunded after funds dispatch
refund_customer: Refund customer
transaction_refund_creditcard_payment_method: REFUND CREDIT CARD
transaction_refund_creditcard_label: Refund on credit card
transaction_refund_offline_payment_method: REFUND OFFLINE
transaction_refund_offline_label: Offline refund
error_siret_number: The SIRET number must not contain either space or point.
customer_activity: Customer activity
bankwire_payment_notification_body: |
    <p>
        To confirm your order %orderId% on %orderDate%, please send us a bank transfer with the following:
    </p>
    <p>
        Amount: %amount% <br>
        IBAN: %rib% <br>
        BIC: %bic% <br>
        Reference: %label% <br>
        Name: %social_name% <br>
        Address: %address% <br>
        Bankwire reference : %$transactionReference% <br>
    </p>
    <p>
        You will receive an email once your bank transfer is confirmed usually under 3 working days. <br>
        To meet those deadlines, please ensure you fill the correct reference in the comment of your bank transfer.
    </p>
bankwire_payment_notification_subject: Bank transfer instructions with reference %$transactionReference% for order %orderId% of %amount%
transaction_reference: Transaction reference
parent_order_id: Parent order's number
job_title: Job Title
legal_identifier: Legal identifier
division_invalid_code: Invalid excluded and included division code.
division_included_invalid_code: Invalid included division code.
division_excluded_invalid_code: Invalid excluded division code.
professional_customer: Professional customer
api_post_mandat_need_user_number_phone: 'A phone number must be defined to finalize the mandat signature. Expected format ***********.'
nationality: Nationality
birthplace_city: Birthplace city
birthplace_country: Birthplace country
ownership_percentage: Percentage of ownership
submit_ubo: Submit
legal_representativel: Legal representative
another_legal_representativel: Another legal representative
btn_add_representative_legal: Add another legal representative
btn_remove_representative_legal: Remove the legal representative
company_mr: Mr
company_mrs: Mrs
mangopay_inscription: Mangopay Inscription
mangopay_inscription_header: |
    In order to finalize your mangopay inscription, please fill the informations of your
    Ultimate Beneficial Owner representing more than 25% of shares or voting rights.
    You can fill in up to 4 beneficial owners.
mangopay_inscription_menu_description: Finalize your Mangopay inscription
select_title: Select civility
ubo_deleted: UBO deleted successfully.
decalaration_is_submitted: Declaration n° %mangoPayDeclarationId% has been submitted.
error_ownership_required: The ownership percentage field is required.
error_ownership_percentage_minimum: Percentage of ownership must be superior to 25%.
error_ownership_percentage: OwnershipPercentage all persons cannot exceed 100%.
submit_ubo_response: UBO submission successfully completed, validation in progress
ubo_validation_failed_subject: UBO declartion n° %declarationId% has been refused.
ubo_validation_failed_content: |
    Your request for validation of UBOs has been refused:<br>
    <b>Status</b>: [status]<br>
    <b>Reason</b>: [reason]<br>
    <b>Message</B>: [message]<br>
enable_reach_psp: Unable to reach the PSP, please try again later.
company_not_pending: Company is not pending yet.
company_with_not_ubo_added: Company must have at least one UBO.
company_invalid_person_without_ownership: 'No valid persons submitted: ownership percentage is mandatory.'
company_invalid_person_wrong_ownership: 'No valid persons submitted: you must declare 25% ownership or more.'
subscription_change_history: Change history
subscription_created: Subscription created
status_updated: Status updated
auto_renew_updated: Auto-renew updated
payment_method_renewed: Payment method renewed
subscription_history_date: Date
subscription_history_time: Time
subscription_history_user: User
subscription_history_event: Event
subscription_history_old_value: Value before
subscription_history_new_value: Value after
extra_data: Extra data
file_not_found: File not Found
extra_info: Info extra
api_key_created_at: 'Creation date'
renew: 'RENEW'
confirm_change_api_key: 'Revoking this API key will block it and generate a new API key. Are you sure you want to generate a new api key?'
invalid_renew_token: 'The action could not be performed.'
new_api_token_generated  : 'A new user API token has been generated.'
bo_content_link: ''
confirm_change_product_category: Changing your product’s category will delete the existing declinations on your product. Stock and price will be reset to zero. Do you want to proceed ?
warning_updating_category: 'You have just modified the category, save your product sheet and update your options and declinations.'
api_warning_updating_category: 'The product has not been updated. Options selected are not compatible with selected category.'
exim_warning_updating_category: 'The product has not been modified because the options entered are not compatible with the selected category.'
error_updating_category: 'Declinations are removed and stock and prices are reset.'
external_transfer_trace: Transactions monitoring
external_transfer_trace_menu_description: Transactions monitoring
financial_flows_history: Transactions monitoring
financial_flows_history_menu_description: Transactions monitoring
table_transaction_header_date: Date
table_transaction_header_type: Type
table_transaction_header_origin: Origin
table_transaction_header_destination: Destination
table_transaction_header_transaction_id: Transaction Id
table_transaction_header_amount: Amount
table_transaction_header_currency: Currency
table_transaction_header_status: Status
table_transaction_header_order_id: Order Id
table_transaction_header_additional_information: Additional information
filter_transaction_ready: Ready
filter_transaction_pending: Waiting
filter_transaction_success: Success
filter_transaction_failed: Failure
all_selected: All selected
not_selected: Not selected
all_operations: All operations
must_be_vendor_to_change_this_data: You must be a vendor to change this data
must_be_vendor_to_see_this_data: You must be a vendor to see this data
must_provide_filter: At least one parameter must be provided.
api_must_provide_filter: 'At least one of this parameter must be provided : '
admin_cannot_change_company_shipping: An admin can't modify a company shipping via API.
image_resizing_forbidden: Image resizing is not accepted
user_groups_page_title: Groups
user_groups_list: Groups list
user_groups_empty_list: There is no group registered yet. Click on + to create one.
user_groups_msg_info_create_group: Enter the group name and save. You will then be able to import users into the group and export the users of the group.
user_groups_users: Users
user_groups_cant_be_updated: The group name is already used
user_groups_updated: All groups have been updated
user_groups_users_imported: Users have been imported
user_groups_users_exported: Users have been exported
user_groups_import_modal_title: Import users into group
user_groups_import_modal_body: The import removes the users present in the group and adds the imported users.
user_groups_import_modal_cancel: Cancel
user_groups_import_modal_submit: Import
user_groups_file_without_lines: File does not contain import rows
user_groups_import_warning_not_found: '% : user not found'
user_groups_import_warning_duplicate: '% : duplicate email'
user_groups_import_success: '% : user added to group'
user_groups_users_deleted: 'Users have been successfully removed from groups.'
missing_tax_configuration_subject: New transaction in a non configured country
missing_tax_configuration: |
    <p>
    Order %orderId% is shipped from a country where you have not configured the tax rate for shipping. In this case, Wizaplace uses the marketplace tax by default. You can configure the tax rate applicable to the shipping costs of this country on the Taxes page of your back office (Menu Gears / Taxes / Shipping costs tab).
    You can view the details of the order in the back office and see the tax that has been applied to the shipping costs.<br/>
    Sincerely,<br/>
    Customer support
    </p>
error_password_difference_limit: 'Password security requirements not respected. Password must be different from your %limit% previous passwords.'
password_recovery_force_change_mail_subj: Password recovery
password_recovery_force_change_mail_body: A password recovery request has been made for your account. <a href="[link]">Follow this link</a> to modify your password.
text_password_recovery_force_change_notice: Please enter your email. You will receive a link allowing you to change your password.
text_password_recovery_force_change_link: Return to the shop
text_password_recovery_force_change_title_1: Your Back Office
text_password_recovery_force_change_title_2: Edit your password
success_password_recovery_force_change: Your password has been changed.
new_password: New password
confirm_new_password: Confirm new password
validate: Validate
error_current_password_expired: |
    Your password has not been be changed for more than [passwordRenewalTimeLimit] days.
    A mail containing a link allowing you to change your password has been sent to you.
warning_password_expiry_time_left: Your password will expire in [passwordExpiryTimeLeft] days.
error_expired_password: Password expired
invoicing_settings: Invoices and credit notes setting
invoicing_settings_label: Disable invoices and credit notes generation, for all companies
invoicing_settings_button: Save configuration
invoicing_settings_modal: Are you sure you want to save the configuration?
invoicing_settings_vendor: Invoices and credit notes configuration
invoicing_settings_vendor_label: Disable invoices generation for this company
lemowany_duplicated_document_found: "you have already sent the same document."
the_product_is_not_present_in_the_catalog: 'The product is not present in the catalog.'
the_product_does_not_appear_in_search_results: 'The product does not appear in search results.'
the_product_cannot_be_ordered: 'The product cannot be ordered.'
off_status: 'Off status'
hidden_status: 'Hidden status'
off_category_%s: 'Off category "%s"'
hidden_category_%s: 'Hidden category "%s"'
moderation_status_with_semicolon: 'Moderation status: '
price_at_0: 'Price at 0'
stock_at_0: 'Stock at 0'
unknown_company: 'Unknown vendor'
inactive_company_new: 'Inactive vendor (New)'
inactive_company_pending: 'Inactive vendor (Waiting)'
inactive_company_disabled: 'Inactive vendor (Off)'
no_available_shipping: 'No available delivery method'
no_active_shipping: 'No active delivery method'
cant_payout_company: 'Vendor Withdrawal unavailable for [PSP].'
catalog_synchronisation: "Catalog synchronisation: [cpt_sync_products] products in progress"
catalog_synchronisation_in_progress_datetime: "The last update from [date] at [time] hasn't been applied yet to the catalog."
catalog_synchronisation_in_progress_minutes: "The last update from [minutes] min ago hasn't been applied yet to the catalog."
catalog_synchronisation_in_progress_hours: "The last update from [hours] hours ago hasn't been applied yet to the catalog."
cancel_exim_jobs_import: 'Cancel import'
text_confirm_cancel_exim_jobs_import: 'Do you want to proceed? Import will be stopped.'
text_exim_jobs_have_been_canceled: 'Import successfully canceled.'
discuss_title_order: About order [orderId]
transaction_chargedback_object: 'Chargeback : Payment transactionId disputed for the order parentOrderId'
transaction_chargedback: |
    <p>
    Hello,<br/>
    The payment %transactionId% for the order %parentOrderId% %childOrders% has been disputed.<br/>
    You can cancel the order if it has not been processed by the merchant or you can contact the customer to regularize the payment.<br/>
    For more information about the chargeback procedure, please contact your PSP contact.<br/>
    Sincerely,<br/>
    The support department
    </p>
discussion_deleted: 'The discussion was successfully deleted.'
admin_discussions_warning_message: You will send a message as Administrator of [companyName]
exim_failed_to_add_extra_alt_text: 'Alt text: some values have been ignored.'
discussion_create: Create a discussion
new_discussion_created: Discussion was successfully created.
email_notifications_empty_list: There are no notification.
emails_notifications: Emails notifications
notification_disabled: Notification disabled
notifications_config_update_error: An error happens while saving the notifications.
warning_notifications_update: Are you sur you want to save the changes?
notifications_config_synchronization_warning: Some notifications are missing as they are not synchronized.
synchronize_notifications_config: Synchronize now
external_transaction_empty_list: 'There is no transaction yet.'
price_tiers_info_quantity: 'Quantity'
sandbox_disk_usage_menu: 'Sandbox disk space'
sandbox_disk_usage: 'Your sandbox is using [diskUsage] Mo of [diskLimit] Mo'
sandbox_disk_warning: The disk of your sandbox is full. Catalog updates are disabled. Click here for more information.
application_version: 'Application version'
commission_monitoring: Commissions monitoring
table_transaction_header_company_id: 'Company Id'
table_transaction_header_company_name: 'Company Name'
table_transaction_header_finished_id: 'Finished on'
table_transaction_header_total_ttc: 'Totals: including taxes'
table_transaction_header_refund_ttc: 'Refunds: including taxes'
table_transaction_header_order_balance: 'Order balance'
table_transaction_header_commission_ttc: 'Commissions: including taxes'
table_transaction_header_commission_date: 'Commission dispatch date'
table_transaction_header_vendor_share_ttc: 'VendorShare: including taxes'
table_transaction_header_vendor_share_date: 'VendorShare dispatch date'
