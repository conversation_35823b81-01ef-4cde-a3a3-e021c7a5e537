var W = {
    //Use jQuery-Form-Validator
    formValidatorValidate: function formValidatorValidate ($) {
        $.validate();
    },

    toggleLoadingBox: function toggleLoadingBox (action) {
        $('.ajax-loading-box').toggle(action);
    },

    truncate: function truncate (string, length) {
        if (string && string.length > length) {
            string = string.substring(0, length-1)+'…';
        }

        return string;
    },

    toggleInput: function toggleInput (parent, display) {
        parent.find('input').each(function() {
            var input = $(this);
            if(display) {
                var disabled = input.data('__disabled__');
                if( disabled != undefined ) {
                    if( disabled ) {
                        input.attr('disabled', 'disabled').trigger('disabled:added');
                    } else {
                        input.removeAttr('disabled').trigger('disabled:removed');
                    }
                }
            } else {
                input.data('__disabled__', Boolean(input.attr('disabled')));
                input.attr('disabled', 'disabled').trigger('disabled:added');
            }
        });

        return parent.toggle(display);
    },

    generateImagesSlider: function generateImagesSlider () {
        $('.js-images-cycle').cycle({
            fx: 'fade',
            speed: 'fast',
            timeout: 0,
            nowrap: 1,
            slides: '> div'
        });
    },

    generateThumbnailsSlider: function generateThumbnailsSlider ($mainSlider) {
        // Regenerate thumbnail pager
        $('.js-vertical-pager').cycle('destroy').remove();
        var $pagerTpl = $('#js-vertical-pager-tpl');
        $('<div class="js-vertical-pager"></div>').html($pagerTpl.html()).insertAfter($pagerTpl);
        var $pagerContainer = $('.js-vertical-pager-container');
        var $mainContainer = $('.js-images-cycle-container');

        // Display slider
        $mainContainer.addClass('initialized');

        if (!$pagerContainer.find('.js-vertical-pager-next').length) {
            $pagerContainer.prepend('<a class="js-vertical-pager-next vertical-pager-next disabled"><span class="glyphicon glyphicon-menu-down" aria-hidden="true"></span></a>');
        }
        if (!$pagerContainer.find('.js-vertical-pager-prev').length) {
            $pagerContainer.prepend('<a class="js-vertical-pager-prev vertical-pager-prev disabled"><span class="glyphicon glyphicon-menu-up" aria-hidden="true"></span></a>');
        }
        $('.js-vertical-pager').cycle({
            fx: 'carousel',
            timeout: 0,
            carouselVisible: 1,
            allowWrap: false,
            next: '.js-vertical-pager-next',
            prev: '.js-vertical-pager-prev',
            speed: 'fast',
            carouselVertical: true
        }).find('img').css('opacity', 1);
    },

    /**
     * Render the ratings with stars
     */
    renderRatings: function () {
        $(".js-rating").rating({
            min:0,
            max:5,
            step:1,
            size:'sm',
            showClear: false,
            starCaptions: starCaptions,
            clearCaption: starCaptions[0]
        });
    },

    formatPrice: function formatPrice (price) {
        if (!$.isNumeric(price)) {
            return '';
        }
        price += '';
        x = price.split('.');
        x1 = x[0];
        x2 = x.length > 1 ? '.' + x[1] : '';
        var rgx = /(\d+)(\d{3})/;
        while (rgx.test(x1)) {
            x1 = x1.replace(rgx, '$1' + ' ' + '$2');
        }
        return x1 + x2;
    }
};


$(function(){
    W.formValidatorValidate($);

    //Spy scroll to display mini header
    $(window).scroll(
        function() {
            var $headerMini = $('#header-mini');
            if ($(window).scrollTop() > 50) {
                $headerMini.fadeIn(500);
            } else {
                $headerMini.fadeOut(500);
            }
        }
    );

    //Spy click to give focus back to search field on top
    $('.js-focus-search').click(function(){
        var $target = $('#search_input');
        $('body, html').animate({
            scrollTop: $target.offset().top - 50
        });
        $target.focus();
    });

    //Spy 'burger menu' click
    $('.js-toggle-menu').click(function(){
        var $rightMenu = $('#right-menu');
        var $contentWrapper = $('#content-wrapper');

        if ( !$rightMenu.data('open')) {
            openMenu();
        } else {
            closeMenu();
        }

        function openMenu () {
            $contentWrapper
                .animate({right:'75%'}, 200)
            ;
            $rightMenu
                .show()
                .css({'height':$(document).height()})
                .animate({width:'75%'}, 200)
                .data('open', true)
            ;
        }

        function closeMenu () {
            $contentWrapper.animate({right:'0'}, 200);
            $rightMenu
                .css({'height':$(document).height()})
                .animate({width:'0'}, 200, function(){
                    $rightMenu.hide();
                })
                .data('open', false)
            ;
        }
    });

    //Display full screen responsive-filters
    var $topCloseButton = $('#full-screen-top-close-button');
    var $bottomCloseButton = $('#full-screen-bottom-close-button');
    $('.button-filtre-responsive').click(function() {

        var $filtersDestination = $('#full-screen-filters');
        if (!$filtersDestination.data('filled') ) {
            var $filtersContent = $('#bloc-filtre').detach();

            $filtersDestination.append($topCloseButton.html());
            $filtersDestination.append($filtersContent);
            $filtersDestination.data('filled', true);
        }

        if (!$filtersDestination.data('open')) {
            $filtersDestination.show();
            if (window.matchMedia("(max-width: 768px)").matches) {
                $('body').addClass('fixed');
            }

            $filtersDestination.data('open', true);
        }
        $bottomCloseButton.removeClass('hidden');

        $('.close-button').on('click', function(){
            var $filtersDestination = $('#full-screen-filters');
            $filtersDestination.hide();
            $('body').removeClass('fixed');
            $bottomCloseButton.addClass('hidden');
            $filtersDestination.data('open', false);
        });

    });

    $('.js-switch').click(function(e){
        e.stopPropagation();
        $($(this).attr('data-target-show')).slideDown(200);
        $($(this).attr('data-target-hide')).slideUp(200);
    });

    $('.js-change-required').click(function(e){
        var $trigger = $(e.target);
        if($trigger.is('input')) {
            if($trigger.is(':checked')) {
                $($trigger.data('requiredTarget')).attr('required', 'required').trigger('required:added');
            } else {
                $($trigger.data('requiredTarget')).removeAttr('required').trigger('required:removed');
            }
        }
    }).click();

    $('.js-switch-availability').click(function(e){
        var $trigger = $(e.target);
        if($trigger.is('input')) {
            if($trigger.is(':checked')) {
                $($trigger.data('switchTarget')).removeAttr('disabled').trigger('disabled:removed');
            } else {
                $($trigger.data('switchTarget')).attr('disabled', 'disabled').trigger('disabled:added');
            }
        }
    }).click();

    $('.js-ajax-click').click(function (){
        var elmt   = $(this);
        var target = $('#' + elmt.attr('data-target-id'));
        if(target.length) {
            W.toggleLoadingBox(true);
            target.load(
                elmt.attr('data-url'),
                function() {W.toggleLoadingBox(false);}
            );
        } else {
            $.ajax(
                elmt.attr('data-url')
            )
        }
    });

    // Form collections
    $('.js-collection').each(function(i,elt) {

        if($(elt).attr('data-label-add')) {
            // Insert "Add new entry" button
            $(elt).append($('<button class="js-collection-add btn-primary">' + $(elt).attr('data-label-add') + '</button>'));

            // Bind "Add new entry" button
            $(elt).on('click', '.js-collection-add', function(e) {
                var collectionHolder = $(this).closest('.js-collection');
                var prototype = collectionHolder.attr('data-prototype');
                var form = prototype.replace(/__name__label__/g, $(elt).attr('data-label-entry') + ' ' + collectionHolder.children().length);
                form = form.replace(/__name__/g, collectionHolder.children().length);
                $(form).insertBefore($(elt).find('.js-collection-add'));
                return false;
            });
        }

        if( $(elt).attr('data-label-remove') ) {
            var remove_btn = '<div class="js-collection-remove"><i class="glyphicon glyphicon-remove"></i>' + $(elt).attr('data-label-remove') + '</div>';

            // Insert "Remove entry" button on default child elements
            $(elt).find('> div').append(remove_btn);

            if ($(elt).attr('data-prototype')) {
                // Insert "Remove entry" button in prototype
                var $prototype = $($(elt).attr('data-prototype'));
                $prototype.append(remove_btn);
                $(elt).attr('data-prototype', $prototype[0].outerHTML);
            }

            // Bind "Remove entry" button
            $(elt).on('click', '.js-collection-remove', function(e) {
                $(this).parent().remove();
                return false;
            });
        }
    });

    $('.js-show-password').each(function(i, elt) {
        $(elt).wrap('<div class="input-group"></div>');
       $('<div class="input-group-addon"><label> <input type="checkbox" /> ' + __('w_see') + '</label></div>').insertAfter($(elt));
        $(elt).parent().find('input[type="checkbox"]').on('change', function() {
           if($(elt).attr('type') == 'password') {
               $(elt).attr('type', 'text');
           } else {
               $(elt).attr('type', 'password');
           }
        });
    });

    // Bootstrap tabs fix
    $('a.js-tab-switch').on('click', function(e){
       $active_tab = $('[role="tab"][href="'+$(this).attr('href')+'"]');
       $active_tab.parent().parent().find('[role="presentation"]').removeClass('active');
        $active_tab.parent().addClass('active');
        $('body, html').animate({
            scrollTop: $('[role="tab"][href="' + $(this).attr('href') + '"]').offset().top - 50
        });
    });

    $('.fancybox').fancybox();

    // Modals
    $('.modal').appendTo('body');
    $('.js-open-modal-on-load').each(function(i, elt) {
        $(elt).modal('show');
    });

    W.renderRatings();

    window.onresize = function(event) {
        W.generateThumbnailsSlider($('.js-images-cycle'));
    };
    // Because we have bugs with the thumbnails
    setTimeout(function () {
        W.generateThumbnailsSlider($('.js-images-cycle'));
    }, 4000);

    $(document).on('click', '.js-vertical-pager-container img', function(){
        var index = $('.js-vertical-pager').data('cycle.API').getSlideIndex(this);
        $('.js-images-cycle').cycle('goto', index);
    });

});

if ('undefined' !== typeof window.jQuery && 'undefined' !== typeof window.jQuery.fn.on) {
    window.jQuery(document).on('ajaxStart.ss.test.behaviour', function(){
        window.__ajaxStatus = function() {
            return 'waiting';
        };
    });
    window.jQuery(document).on('ajaxComplete.ss.test.behaviour', function(e, jqXHR){
        if (null === jqXHR.getResponseHeader('X-ControllerURL')) {
            window.__ajaxStatus = function() {
                return 'no ajax';
            };
        }
    });
    window.jQuery(document).on('ajaxSuccess.ss.test.behaviour', function(e, jqXHR){
        if (null === jqXHR.getResponseHeader('X-ControllerURL')) {
            window.__ajaxStatus = function() {
                return 'success';
            };
        }
    });
}
