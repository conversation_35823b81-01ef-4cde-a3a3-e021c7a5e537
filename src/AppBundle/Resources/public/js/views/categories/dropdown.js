/**********************************************
 * JS for categories dropdown in header menu
 **********************************************/

/* Set category label */
function setCategoryLabel($categoriesMenu) {
    var max_length = 21;
    var selected_category = $categoriesMenu.find('li.current > span').last().html();
    if(selected_category.length) {
        selected_category = W.truncate(selected_category, max_length);
        $categoriesMenu.find('.main-search > span').html(selected_category);
    }
}

$(function() {
    var $categoriesMenu = $('#categories-menu');
    var $categoriesMenuContent = $('#categories-menu-content');

    $categoriesMenu.find('.main-search, .btn').click(function(event){
        event.preventDefault();
        $categoriesMenuContent.slideToggle(200);
    });
    $categoriesMenu.hover(
        function () {
            $(this).attr('data-hover', 'true');
        },
        function () {
            $(this).attr('data-hover', 'false');
        }
    );
    $('html').on('click', function(){
        if ($categoriesMenu.attr('data-hover') != 'true') {
            $categoriesMenuContent.slideUp(200);
        }
    });

    $categoriesMenu.find('li > span').on('click', function(){

        var root = $(this).closest('ul');
        var current = $(this).parent();
        var child = current.find('ul');

        /* Accordion */
        current.addClass('triggered');
        root.find('li:not(.triggered)').removeClass('current').find('ul').slideUp(200).find('li');
        if(!child.is(':visible')) {
            child.slideDown(200);
        } else {
            child.slideUp(200);
        }
        current.removeClass('triggered').toggleClass('current');

        // Set refinements
        var $selected_categories = $categoriesMenu.find('li.current:not("#all-categories-link")');
        if($selected_categories.length) {
            var categories = { };
            $selected_categories.each(function(i,elt) {
                categories[$(elt).data('field')] = $(elt).data('value');
            });
            $('#refinements').val(JSON.stringify([categories]));
        } else {
            $('#all-categories-link').addClass('current');
            $('#refinements').val('');
        }

        setCategoryLabel($categoriesMenu);
    });

});