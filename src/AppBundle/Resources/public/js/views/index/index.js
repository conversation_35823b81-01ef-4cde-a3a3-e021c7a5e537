/**
 * Wizacha search engine
 * @param $ jQuery instance
 * @param W Wizacha functions object instance
 * @param config array of parameters
 */
var FacetSearch = function($, W, config) {

    // DOM binding
    var $nbHitsPerPage = $('#nb_hits_per_page');
    var $hits = $('#hits');
    var $hits_geo = $('#hits-geo');
    var $pagination = $('#pagination');
    var $stats = $('#stats');
    var $q = $('#search_input');
    var $q_alternate = $('.js-focus-search');
    var $hitTemplate = Hogan.compile($('#hit-template').text());
    var $statsTemplate = Hogan.compile($('#stats-template').text());
    var $catMenu = $('#categories-menu');
    var $catMenuContent = $('#categories-menu-content');
    var $containerMainCategory = $("#js-facet-main_category");

    if (config.facets_enabled) {
        var $facets = $('#facets');
        var $menuTemplate = Hogan.compile($('#menu-template').text());
        var $facetTemplate = Hogan.compile($('#facet-template').text());
        var $facetValuesTemplate = Hogan.compile($('#facet_values-template').text());
        var $valueTemplate = Hogan.compile($('#value-template').text());
    }

    var geoloc = {
        distance : 2500,
        lat_lng  : '',
        delivery : true
    };
    var redraw_menu = true;

    var helper;
    var numericFilters = {};

    /*********************
     * Search functions
     *********************/
    function search() {
        W.toggleLoadingBox(true);
        var params = {
            maxValuesPerFacet: 50,
            hitsPerPage      : $nbHitsPerPage.val(),
            tagFilters       : config.tag_filters || ''
        };
        var filters = [];
        for (var filter in numericFilters) {
            if(numericFilters[filter].min.val != undefined) {
                filters.push(filter + ">=" + numericFilters[filter].min.val);
            }
            if(numericFilters[filter].max.val != undefined) {
                filters.push(filter + "<=" + numericFilters[filter].max.val);
            }
        }

        params.numericFilters = filters.join(',');

        if (config.geoloc) {
            if (geoloc.lat_lng) {
                params.aroundLatLng = geoloc.lat_lng;
                params.aroundRadius = geoloc.distance;
                if (geoloc.delivery) {
                    content_to_merge = undefined;
                    helper.search($q.val(), searchCallbackMerge, params);
                    params.tagFilters = (params.tagFilters ? (params.tagFilters + ',') : '') + '-' + config.geoloc.tag;
                    delete params.aroundLatLng;
                    delete params.aroundRadius;
                    helper.search($q.val(), searchCallbackMerge, params);
                    return;
                }
            } else if (!geoloc.delivery) {
                params.tagFilters = (params.tagFilters ? (params.tagFilters + ',') : '') + config.geoloc.tag;
            }
        }
        helper.search($q.val(), searchCallback, params);
    }

    /**
     * Callback called on each keystroke, rendering the results
     */
    function searchCallback(success, content) {
        if (!success || content.query !== $q.val()) {
            // do not consider the result if there is an error
            // or if it is outdated -> query != $q.val()
            console.log('Search failed');
            return;
        }

        //display stats
        $stats.html($statsTemplate.render(
            {
                nbHits          : content.nbHits,
                processingTimeMS: content.processingTimeMS,
                nbHits_plural   : content.nbHits >= 2
            }
        ));

        displayHits = function displayHits(hits) {
            if (hits) {
                var html='';
                for (var i = 0; i < hits.length; ++i) {
                    hits[i]['name'] = W.truncate(hits[i]['name'], 80);
                    html += $hitTemplate.render(hits[i]).trim();
                }
                return html;
            }
            return '';
        };

        $hits.html(displayHits(content.hits));
        $hits_geo.html(displayHits(content.hits_geo));
        $q_alternate.val($q.val());

        $('.js-search-result-block-title').toggleClass('hidden', ! (content.hits_geo && content.hits_geo.length && content.hits.length) );

        // facets
        if (config.facets_enabled) {

            // format all facets
            var all_facets = [];
            for (var facet_name in content.disjunctiveFacets) {
                var isRefined = !!helper.disjunctiveRefinements[facet_name];
                var new_facet = {
                    facet_id        : facet_name.replace(/\W/g,''),
                    facet_name      : facet_name,
                    is_special      : Boolean(config.facets_special[facet_name]),
                    is_category     : (facet_name == config.facets_id.main_category),
                    title           : '',
                    facet_position  : 0,
                    values          : []
                };

                if (new_facet.is_special) {
                    new_facet.title = config.facets_special[new_facet.facet_name].title;
                } else {
                    //Test to limit number of facets
                    var value_count = 0;
                    for( var value_name in content.disjunctiveFacets[facet_name])
                    {
                        if (value_name == config.empty_value) {
                            continue;
                        }
                        value_count += content.disjunctiveFacets[facet_name][value_name];
                    }

                    if (!isRefined && value_count < content.nbHits/2) {
                        continue;
                    }

                    var infos = new_facet.facet_name.split('##');
                    new_facet.title = infos.pop();
                    new_facet.facet_position = infos.pop();
                }

                // treat numerical filters
                if( content.facetStats[facet_name] ) {
                    numericFilters[facet_name] = numericFilters[facet_name] || {
                        facet_id : new_facet.facet_id,
                        min : {elm: undefined, val: undefined},
                        max : {elm: undefined, val: undefined}
                    };
                    new_facet.stats = content.facetStats[facet_name];
                } else {
                    // collect all values
                    for (var v in content.disjunctiveFacets[facet_name]) {
                        new_facet.values.push({
                            label: v,
                            is_empty: v == config.empty_value,
                            count: content.disjunctiveFacets[facet_name][v],
                            refined: helper.isRefined(facet_name, v)
                        });
                    }
                    // sort the values
                    new_facet.values.sort(function (a, b) {
                        //empty value are always last
                        if (a.is_empty != b.is_empty) {
                            if (a.is_empty) return 1;
                            if (b.is_empty) return -1;
                        }
                        // sort by the refined states first (put them on top if they are refined)
                        if (a.refined != b.refined) {
                            if (a.refined) return -1;
                            if (b.refined) return 1;
                        }
                        // then sort by number of values
                        return b.count - a.count;
                    });
                }

                //store the facets
                if (isRefined || new_facet.values.length > 1 || 'stats' in new_facet) {
                    all_facets.push(new_facet);
                }

            }
            all_facets = all_facets.sort(function(a, b){
                return parseInt(a.facet_position, 10) - parseInt(b.facet_position, 10);
            });

            //create menu structure
            renderMenu(all_facets);
            renderFacets(all_facets, content);
        }


        renderPagination(content);

         if (content.nbHits != 0) {
             $('#search_engine_results_page').show();
             $('#search_engine_no_results').hide();
         } else {
             $('#search_engine_results_page').hide();
             $('#search_engine_no_results').show();
         }

        W.toggleLoadingBox(false);

        // update URL anchor
        var refinements = [];
        for (var refine in helper.refinements) {
            if (helper.refinements[refine]) {
                var i = refine.indexOf(':');
                var r = {};
                if (i === -1) {
                    // SQL client
                    for (var value in helper.refinements[refine]) {
                        r[refine] = value;
                        refinements.push(r);
                    }
                } else {
                    r[refine.slice(0, i)] = refine.slice(i + 1);
                    refinements.push(r);
                }
            }
        }
        for (var refine in helper.disjunctiveRefinements) {
            for (var value in helper.disjunctiveRefinements[refine]) {
                if (helper.disjunctiveRefinements[refine][value]) {
                    var r = {};
                    r[refine] = value;
                    refinements.push(r);
                }
            }
        }

        var simplifiedNumericFilter = {};
        for (var numeric in numericFilters) {
            var n = numericFilters[numeric];
            if (typeof n.max.val != 'undefined' || typeof n.min.val != 'undefined') {
                simplifiedNumericFilter[numeric] = {
                    facet_id: n.facet_id,
                    max: {val: n.max.val},
                    min: {val: n.min.val}
                };
            }
        }

        var simplifiedgeoloc = {
            distance : geoloc.distance != 2500? geoloc.distance:undefined,
            localization: config.geoloc.localization.val() != ''? config.geoloc.localization.val() : undefined,
            delivery: geoloc.delivery == true ? undefined : geoloc.delivery,
            lat_lng  : geoloc.lat_lng != "" ? geoloc.lat_lng : undefined
        }

        var uri = new URI();
        var reg = /^\/+|\/+$/g;
        //Remove seo parts of uri if necessary
        var path_prefix = config.path_prefix[uri.protocol()].replace(reg,'');
        if( config.filters && uri.path().replace(reg,'') != path_prefix ) {
            for (var filter in config.filters) {
                var current_filter_exists = false;
                for (var current_refinement in refinements) {
                    if (refinements[current_refinement][filter] && refinements[current_refinement][filter] == config.filters[filter]) {
                        current_filter_exists = true;
                        break;
                    }
                }
                if (!current_filter_exists) {
                    uri.path('/'+path_prefix);
                    break;
                }
            }
        }

        var query_parameters = {
            'q' : content.query,
            'page' : content.page,
            'refinements': convertForUrlParameter(refinements),
            'numericFilters': convertForUrlParameter(simplifiedNumericFilter),
            'geoloc':  convertForUrlParameter(simplifiedgeoloc)
        }
        for (var key in query_parameters) {
            switch (query_parameters[key]) {
                case '':
                case convertForUrlParameter({}):
                case convertForUrlParameter([]):
                case 0:
                    uri.removeSearch(key);
                    delete query_parameters[key];
                    break;
                default :
                    break;
            }
        }
        // Remove useless params from main search form
        uri.removeSearch(['lat', 'lng', 'geoloc']);

        // Set uri
        uri.setSearch(query_parameters);
        history.replaceState({}, document.title, uri.toString());

        // Show the rating stars
        W.renderRatings();
    }

    /**
     * Callback called on geoloc refine
     */
    function searchCallbackMerge(success, cached_content) {
        if (!success) {
            console.log('Search failed');
            return;
        }
        if (!content_to_merge) {
            content_to_merge = cached_content;
            return;
        }
        content = $.extend(true, {}, cached_content);

        content.nbHits += content_to_merge.nbHits;
        content.hits_geo = content_to_merge.hits;
        if (content.params.indexOf('LatLng') != -1) {
            //Swap if needed
            content.hits = [content.hits_geo, content.hits_geo = content.hits][0];
        }
        if (config.facets_enabled) {
            for (var facet_name in content_to_merge.disjunctiveFacets) {

                content.disjunctiveFacets[facet_name] = content.disjunctiveFacets[facet_name] || {};
                for (var facet_value in content_to_merge.disjunctiveFacets[facet_name]) {
                    content.disjunctiveFacets[facet_name][facet_value] = content.disjunctiveFacets[facet_name][facet_value] || 0;
                    content.disjunctiveFacets[facet_name][facet_value] += content_to_merge.disjunctiveFacets[facet_name][facet_value];
                }

                if (content_to_merge.facetStats[facet_name]) {
                    content.facetStats[facet_name] = content.facetStats[facet_name] || {
                        max: Number.NEGATIVE_INFINITY,
                        min: Number.POSITIVE_INFINITY
                    };
                    content.facetStats[facet_name].min = Math.min(content.facetStats[facet_name].min, content_to_merge.facetStats[facet_name].min);
                    content.facetStats[facet_name].max = Math.max(content.facetStats[facet_name].max, content_to_merge.facetStats[facet_name].max);
                }
            }
        }
        content_to_merge = undefined;
        searchCallback(success, content);
    }

    // Clear all refinements, except config.facets_persistent and data-preserve html filter attribute
    function clearRefinements(preserve) {

        // Clear numeric filters
        for (var filter in numericFilters) {
            if ($.inArray(filter, config.facets_persistent.numeric) == -1) {
                numericFilters[filter].min.val = undefined;
                numericFilters[filter].max.val = undefined;
            }
        }

        // Clear disjunctive facets
        var persistent_disjunctive = config.facets_persistent.disjunctive.slice();
        if(preserve) {
            persistent_disjunctive = persistent_disjunctive.concat(preserve);
        }
        var newDisjunctiveRefinements = {};
        persistent_disjunctive.forEach(function(elt) {
            if(helper.disjunctiveRefinements[elt]) {
                newDisjunctiveRefinements[elt] = helper.disjunctiveRefinements[elt];
            }
        });
        helper.disjunctiveRefinements = newDisjunctiveRefinements;

        // Clear non-disjunctive facets
        helper.refinements = {};

        redraw_menu = true;
    }

    function categoryChange($elt) {
        //To prevents search with 0 result, remove all refinement before search product in specific category
        clearRefinements();
        toggleRefine($elt.data('field'), $elt.data('value'));
    }
    // Clear Refinements when multivalued category is cleared
    this.categoryMultivalueChange = function categoryMultivalueChange(elt) {
        redraw_menu = true;
        if ($(elt).hasClass('checked') && $(elt).closest('ul').find('a.checked').length == 1) {
            clearRefinements($(elt).closest('ul').data('preserve'));
        }
        $('.category-description').hide();
    };
    /*********************
     * Render functions
     *********************/
    function renderMenu(all_facets) {
        var menu = {
            facets: []
        };

        for (var i in all_facets) {
            var current_facet = all_facets[i];
            if (current_facet.is_special) {
                menu['facet_' + current_facet.facet_name] = current_facet;
            } else {
                menu.facets.push(current_facet);
            }
        }

        $facets.empty();
        $facets.html(
            $menuTemplate.render(
                menu,
                {
                    facet: $facetTemplate
                }
            )
        );


        // bind filters
        var keystroke_timeout = undefined;
        for (var filter in numericFilters) {
            for (var i=0; i<2; ++i) {
                var bound = (i ? 'min' : 'max');
                var $filter = $('#' + numericFilters[filter].facet_id + '-' + bound );
                if ($filter.length) {
                    (function(f, b) {
                        numericFilters[f][b].elm = $filter;
                        $filter.off().on('keyup change', function () {
                            clearTimeout(keystroke_timeout);
                            var value = parseFloat($(this).val());
                            value = isNaN(value) ? undefined : value;
                            if( numericFilters[f][b].val != value ) {
                                keystroke_timeout = setTimeout(function() {
                                    numericFilters[f][b].val = value;
                                    helper.setPage(0);
                                    search(helper);
                                }, 2000);
                            }
                        })
                    })(filter, bound);
                }
            }
            redraw_menu = false;
        }
    }

    function renderFacets(all_facets, content) {
        //render facets content
        var associative_facets = {};
        var default_placeholder = content.nbHits > 1000; //Algolia limit stats to 1000 first results
        var isCategoryPresent = false;
        for (var i in all_facets) {
            var current_facet = all_facets[i];
            associative_facets[current_facet.facet_name] = current_facet;
            if (current_facet.facet_name == 'main_category') {
                isCategoryPresent = !!current_facet.values.length;
            }
            $('#js-facet-'+current_facet.facet_id).show();
            if( current_facet.facet_name != config.facets_id.category_1 && current_facet.facet_name != config.facets_id.category_2 && !current_facet.stats) {
                var $facet_content = $('#js-facet-' + current_facet.facet_id + ' ul');
                $facet_content.html(
                    $facetValuesTemplate.render(
                        current_facet,
                        {
                            value: $valueTemplate
                        }
                    )
                );
            } else if(numericFilters[current_facet.facet_name]) {
                if(typeof numericFilters[current_facet.facet_name].min.elm != 'undefined') {
                    numericFilters[current_facet.facet_name].min.elm.attr(
                        'placeholder',
                        default_placeholder?'min':current_facet.stats.min
                    );
                    numericFilters[current_facet.facet_name].min.elm.val(numericFilters[current_facet.facet_name].min.val);
                }
                if(typeof numericFilters[current_facet.facet_name].max.elm != 'undefined') {
                    numericFilters[current_facet.facet_name].max.elm.attr(
                        'placeholder',
                        default_placeholder?'max':current_facet.stats.max
                    );
                    numericFilters[current_facet.facet_name].max.elm.val(numericFilters[current_facet.facet_name].max.val);
                }
            }
        }
        $('.tab-filtre').off('click').on('click', function(){
            $(this).toggleClass('active');
            $(this).parent().find('.container-filtre').slideToggle(200);
        });

        var params = new URI('?'+content.params).search(true);
        if(
            isCategoryPresent
            && (
                params.query
                || params.tagFilters
                || params.facetFilters.split(':').length > 1
                || params.facets.split(':').length > 1
            )
        ) {
            $containerMainCategory.show();
        } else {
            $containerMainCategory.hide();
        }

        // Check main checkbox of refined fields
        var $activeFacets = $('.yesboxmini').closest('li[id^="js-facet-"]');
        var $activeNumericFilters = $('.container-filtre input[type="text"]').filter(function () {
            return !!this.value;
        }).closest('li');
        $activeFacets.find('.tab-filtre').addClass('active');
        $activeNumericFilters.find('.tab-filtre').addClass('active');

        // Display content of refined fields by default
        $activeFacets.find('.container-filtre').show();
        $activeNumericFilters.find('.container-filtre').show();
    }

    function renderPagination(content) {
        // render pagination
        if (content.nbPages > 1) {
            var currentUri = new URI();
            currentUri.setSearch({ page: "page_number"});
            $pagination.show();
            // Supprime la pagination existante sinon le nombre de pages n'est pas rafraichi
            if ($pagination.children().length > 0) {
                $pagination.twbsPagination('destroy');
            }
            $pagination.twbsPagination($.extend(config.pagination, {
                totalPages  : content.nbPages,
                visiblePages: Math.min(content.nbPages, 6),
                startPage   : content.page + 1,
                initiateStartPageClick: false,
                onPageClick: function (e, page) {
                    W.toggleLoadingBox(true);
                    helper.page = + page - 1;
                    search(helper);
                    $('body, html').animate({
                        scrollTop: $('#js-main-content').offset().top
                    }, 200);
                }
            }));
        } else {
            $pagination.hide();
        }
    }

    function selectCategoriesFromFilters(filters) {
        for (var refine in filters) {
            if(refine == config.facets_id.category_1 || refine == config.facets_id.category_2) {
                $catMenuContent.find('li[data-field="' + refine + '"][data-value="'+filters[refine]+'"]').addClass('current').find('ul').show();
                $('#all-categories-link').removeClass('current');
                setCategoryLabel($catMenu);
            }
        }
    }

    /**
     * Initialize all callbacks/actions related to products search
     */
    function init() {

        // Helper initialization
        var index = config.index_name;
        W.toggleLoadingBox(true);
        selectCategoriesFromFilters(config.filters);
        W.searchClient.initHelper(
            index,
            config,
            function (new_helper) {
                helper = new_helper;

                if(config.geoloc) {

                    window.setGeoDistance = function (d) {
                        geoloc.distance = d || 2500;
                        search();
                    };
                    window.setGeoLatLngVariables = function (lat, lng) {
                        geoloc.lat_lng = (lat && lng) ? [lat, lng].toString() : '';
                    };
                    window.setGeoLatLng = function (lat, lng) {
                        setGeoLatLngVariables(lat, lng);
                        search();
                    };
                    window.setGeoDelivery = function (delivery) {
                        geoloc.delivery = delivery;
                        search();
                    };
                    setGeoLatLngVariables(config.geoloc.lat.val(), config.geoloc.lng.val());
                }

                var uri = new URI();
                if (uri.query()) {
                    var params = uri.search(true);
                    var page = undefined;
                    for (var param_name in params) {
                        var value = params[param_name];
                        switch (param_name) {
                            case 'page':
                                page = parseInt(value);
                                break;
                            case 'refinements':
                                var refinements = restoreFromUrlParameter(value);
                                for (var i = 0; i < refinements.length; ++i) {
                                    for (var refine in refinements[i]) {
                                        helper.justToggleRefine(refine, refinements[i][refine]);
                                    }
                                    selectCategoriesFromFilters(refinements[i]);
                                }
                                break;
                            case 'numericFilters':
                                numericFilters = restoreFromUrlParameter(value);
                                break;
                            case 'q':
                                $q.val(decodeURIComponent(value));
                                $q_alternate.val(decodeURIComponent(value));
                                break;
                            case 'geoloc':
                                $.extend(geoloc, restoreFromUrlParameter(value));
                                config.geoloc.extraContent.toggleClass('hidden', !geoloc.lat_lng);
                                if (geoloc.localization) {
                                    $('.js-geoloc-input').val(geoloc.localization);
                                    $('.js-geoloc-content').removeClass('hidden');
                                }
                                if (geoloc.lat_lng) {
                                    var lat = geoloc.lat_lng.split(',')[0];
                                    var lng = geoloc.lat_lng.split(',')[1];
                                    setGeoLatLngVariables(lat, lng);
                                    $('#js-geoloc-input-latitude').val(lat);
                                    $('#js-geoloc-input-longitude').val(lng);
                                }
                                $('.js-geoloc-distance li[data-value="'+geoloc.distance+'"]').addClass('yesboxmini')
                                if (geoloc.delivery === false) {
                                    $('.js-include-delivery').removeClass('yesboxmini');
                                }
                                break;
                        }
                    }
                    //Page should be set in last, because justToggleRefine set page to 0
                    if(page) {
                        helper.setPage(page);
                    }
                }

                //Optional filtering
                for (var key in config.filters) {
                    helper.addDisjunctiveRefine(key, config.filters[key]);
                }

                //input binding
                $nbHitsPerPage.change(function(){
                    search();
                });
                window.toggleRefine = function (facet, value) {
                    helper.justToggleRefine(facet, value);
                    search();
                };

                var keystroke_timeout = undefined;
                $q.on('keyup change', function () {
                    clearTimeout(keystroke_timeout);
                    keystroke_timeout = setTimeout(function() {
                        search();
                    }, 500);
                })

                // Refine search on category change
                $catMenu.find('li > span').on('click', function(){
                    var current = $(this).parent();
                    categoryChange(current);
                });

                // load results
                search();
            }
        );

    }

    function convertForUrlParameter(object) {
        return JSON.stringify(object).replace(/##/g,'|');
    }

    function restoreFromUrlParameter(value) {
        var json = null;
        try {
            json = JSON.parse((value || '{}').replace(/\|/g, '##'));
        }
        catch (e) {
            return value;
        }
        return json;
    }
    init();
}

// Geoloc
var current_distance = 0;
function distanceClicked(elmt, dist) {
    $(elmt).parent().find('.checkbox-secondaire').removeClass('yesboxmini');
    if( current_distance != dist) {
        $(elmt).addClass('yesboxmini');
        current_distance = dist;
    } else {
        current_distance = 0;
    }
    setGeoDistance(current_distance);
}

var current_delivery = true;
function deliveryClicked(elmt) {
    current_delivery = ! current_delivery;
    $(elmt).toggleClass('yesboxmini', current_delivery);
    setGeoDelivery(current_delivery);
}

$(function() {
    var $lat                = $('#js-geoloc-input-latitude');
    var $lng                = $('#js-geoloc-input-longitude');
    var $extraContentGeoLoc = $('.js-geoloc-content');
    var $topSearchGeoLoc    = $('.js-geoloc-input');

    $topSearchGeoLoc.on('wizacha:changed', function(e){
        setGeoLatLng($lat.val(), $lng.val());
        $extraContentGeoLoc.toggleClass('hidden', !$lat.val() || !$lng.val());
    });

    $('.js-geoloc-distance li').click(function(){
        distanceClicked($(this), $(this).attr('data-value'));
    });
});
