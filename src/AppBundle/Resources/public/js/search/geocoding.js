function initGeoCoding(search_index, $search_elmt, $lat_elmt, $lng_elmt, template, $postal_elmt)
{
    $postal_elmt = $postal_elmt || $('<input type="hidden" />');
    $lat_elmt.prop('type', 'hidden');
    $lng_elmt.prop('type', 'hidden');
    $postal_elmt.prop('type', 'hidden');


    var validated_hit = null;
    var _registeredHit = {
        _geoloc: {
            lat: $lat_elmt.val(),
            lng: $lng_elmt.val()
        },
        name: $search_elmt.val()
    };

    function validateHit(event, hit) {
        validated_hit = hit;
    }

    function hitName(hit) {
        return hit.name;
    }

    var bestHit;
    var bestHitSelected = false;

    $search_elmt
        .on('typeahead:selected', validateHit)
        .on('typeahead:autocompleted', validateHit)
        .on('typeahead:closed', function () {
            if('' != $(this).val()){
                if ( validated_hit || bestHit ) {
                    _registeredHit = validated_hit ? validated_hit : bestHit;
                 }

                $lat_elmt.val(_registeredHit._geoloc.lat);
                $lng_elmt.val(_registeredHit._geoloc.lng);
                $postal_elmt.val(_registeredHit.postal.shift());
                $search_elmt.val(_registeredHit.name); /* if bestHit is automatically selected, display real name */
            } else {
                _registeredHit = null;
                $lat_elmt.val('');
                $lng_elmt.val('');
                $postal_elmt.val('');
            }

            validated_hit = null;
            bestHit = null;
            $search_elmt.trigger('wizacha:changed');
        })
    ;

    $search_elmt.typeahead(
        {
        },
        [
            {
                source    : search_index.ttAdapter(
                    {
                        hitsPerPage: 8
                    }
                ),
                name      : 'geoloc',
                displayKey: hitName,
                templates : {
                    suggestion: function(hit){
                        if(!bestHitSelected){
                            bestHit = hit;
                            bestHitSelected = true;
                        }
                        var codes = hit.postal.slice(0,2);
                        if(2 < hit.postal.length){
                            codes.push('...');
                        }
                        return template.render({ville: hitName(hit), code_postal: codes.join(', ')})
                    },
                    footer: function(){
                        bestHitSelected = false;
                    }
                }
            }
        ]
    );
}
