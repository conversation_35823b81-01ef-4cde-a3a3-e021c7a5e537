.panier {
    td {
        padding: 15px;
        img {
            max-width: 100px;
            height: auto;
            display: block;
            margin: auto;
        }
    }
    tr {
        vertical-align: middle;
    }
    th {
        text-align: left;
        border-bottom: 1px solid #efefef;
        margin-bottom: 15px;
        color: @main_third_color;
        font-weight: normal;
    }
}

.breadcrumb-panier {
    padding-left: 0px;
    margin-bottom: 15px;

    li {
        background-color: #fff;
        text-align: center;
        list-style: none;

        p {
            margin: 0;
            font-size: 18px;
            text-transform: uppercase;
            color: @main_first_color;
            font-family: @headings-font-family;
            font-weight: 300;
            line-height: 62px;
            padding-left: 31px;
        }
    }
}

.breadcrumb-panier li:after {
    content: '';
    display: block;
    position: absolute;
    top: 0px;
    left: 100%;
    width: 0;
    height: 0;
    border-color: transparent transparent transparent #fff;
    border-style: solid;
    border-width: 31px;
    z-index: 10;
}

.breadcrumb-panier li:before {
    content: '';
    display: block;
    position: absolute;
    top: -4px;
    left: 100%;
    width: 0;
    height: 0;
    border-color: transparent transparent transparent #f5f5f5;
    border-style: solid;
    border-width: 35px;
    z-index: 10;
}

.breadcrumb-panier li.breadcrumb-actif {
    background-color: @main_first_color;

    p {
        color: #fff;
    }
}

.breadcrumb-panier li.breadcrumb-actif:after {
    border-color: transparent transparent transparent @main_first_color;
}

.breadcrumb-panier li:last-child:before,
.breadcrumb-panier li:last-child:after {
    display: none;
}

.product-description {
    font-size: 16px;
    font-weight: bold;
}

.product-quantity {
    border-right: 1px solid #efefef;
    border-left: 1px solid #efefef;
}

.product-price {
    font-size: 22px;
    color: @main_second_color;
    text-align: center;
    margin: 0px;
}

.product-price-number {
    font-size: 12px;
    color: @main_third_color;
}

.select-livraison {
    text-align: right;
    border-top: 1px solid #efefef;
    color: @main_third_color;
}

.conditions-panier {
    margin-top: 15px;
    ul {
        padding: 0;
        margin: 0;
        list-style: none;
    }
    li {
        img {
            display: inline-block;
            vertical-align: middle;
        }
        p {
            display: inline-block;
            vertical-align: middle;
        }
    }
}

.recap-commande {
    tr {
        border-bottom: 1px solid #efefef;
    }
}

.total-inclut {
    font-size: 12px;
}

.votre-commande {
    ul {
        padding: 0;
        margin: 0;
    }
    li:last-child {
        border-bottom: 0px solid #efefef;
    }
    li {
        padding-left: 15px;
        margin-bottom: 15px;
        border-bottom: 1px solid #efefef;
        li {
            border: none;
        }
        p {
            font-size: 13px;
        }
    }
}

.mon-panier {
    ul {
        padding-left: 0;
        margin-left: 0;
    }
}

.inline-panier {
    display: inline-block;
    vertical-align: middle;
    margin-top: 10px;
    margin-bottom: 10px;
    img {
        max-width: 75px;
        height: auto;
    }
}

@media screen and (max-width: @screen-tablet) {

    .breadcrumb-panier {
        li {
            p {
                font-size: 11px;
                line-height: 32px;
                padding-left: 16px;
            }
        }
    }

    .breadcrumb-panier li:after {
        top: 0px;
        border-width: 16px;
    }

    .breadcrumb-panier li:before {
        top: -4px;
        border-width: 20px;
    }

    .product-quantity {
        border-right: 0px solid #efefef;
        border-left: 0px solid #efefef;
        border-top: 0px solid #efefef;
        border-bottom: 0px solid #efefef;
    }

    .conditions-panier {
        li {
            img {
                display: block;
                margin: auto;
            }
            p {
                display: block;
                text-align: center;
            }
        }
    }

}

fieldset.basket-group {
    padding-bottom: 0;
}

.basket-select-shipping {
    background-color: @bg-light;
    padding: 10px 20px;
    margin-top: 20px;
    margin-left: -20px;
    width: 100%;
    text-align: right;
    box-sizing: content-box;
}

.basket-declination {
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
    margin-top: 20px;
    padding-bottom: 20px;

    &:last-child {
        border: none;
        padding-bottom: 0;
    }
    img {
        max-height: 100%;
        max-width: 100%;
        width: auto;
        height: auto;
    }

    .price-product {
        font-size: 42px;
        font-weight: bold;
        p {
            font-family: inherit;
        }
        @media (min-width: @screen-tablet) and (max-width: @screen-desktop) {
            font-size: 28px;
        }
        @media (min-width: @screen-desktop) and (max-width: @screen-lg-desktop) {
            font-size: 38px;
        }
        @media (max-width: @screen-tablet) {
            font-size: 35px;
        }
    }

}

.no-items {
    margin: 20px 0;
}

.card-content {
    margin: 10px 0 10px 20px;
}

.order-general-infos,
.basket-total {
    background-color: @main_bg_color;
    height: 180px;
    padding: 10px;
    .right {
        text-align: right;
    }
}

.order-general-infos {
    table {
        tr {
            td {
                &:nth-child(1) {
                    width: 40px;
                }
                vertical-align: middle;
                padding: 0 5px 5px 0;
            }
        }
    }
    @media (max-width: @screen-phone) {
        height: auto;
    }
}

.basket-total {
    border: 1px solid @main_third_color;
    table {
        width: 100%;
    }
}

i.icon {
    &.payment-methods-icon {
        padding-left: 15px;
        top: 4px;
        &:before {
            content: ~"url('../images/payment_methods.png')";
        }
    }
    &.marketplace-icon {
        top: 2px;
        &:before {
            content: ~"url('../images/marketplace-icon.png')";
        }
    }
    &.delete-item-icon {
        top: 2px;
        &:before {
            content: ~"url('../images/delete-icon.png')";
        }
    }
    &.secured-icon {
        top: 2px;
        &:before {
            content: ~"url('../images/secured-icon.png')";
        }
    }
    &.rma-icon {
        top: 2px;
        &:before {
            content: ~"url('../images/rma-icon.png')";
        }
    }
    &.shipping-icon {
        top: 2px;
        &:before {
            content: ~"url('../images/shipping-icon.png')";
        }
    }
}

.discount-coupon {
    .form-control {
        width: 110px;
        margin-right: 0;
    }
}
