#bloc-filtre {
    vertical-align: top;
    padding: 10px 10px 10px 10px;
    background-color: #fff;
    .mx-shadow;
    margin: 0.5rem 0 1rem 0;
    border-radius: 2px;
    li {
        text-decoration: none;
        list-style: none;
        margin-bottom: 10px;
        &:last-child {
            margin-bottom: 0;
        }

        .container-filtre {

            .checkbox-secondaire {
                padding: 10px 5px 10px 5px;
                margin-bottom: 0px;
                font-size: 12px;
                text-transform: none;
                color: #444444;
                cursor: pointer;
            }

            label {
                display: inline-block;
                min-width: 20px;
                text-align: right;
                text-transform: none;
            }

            p {
                margin: 5px 0;
            }
        }

        .tab-filtre {
            cursor: pointer;
            text-transform: uppercase;
        }
    }
}

.filtres-responsive {
    overflow: scroll;
    position: fixed;
    width: 100%;
    padding: 0 20px 0 20px;
    background-color: #fff;
    z-index: 300;
    height: 100%;

    li {
        list-style-type: none;
    }
}

.bottom-close-button {
    position: fixed;
    bottom: 0;
    left: -20px;
    box-sizing: content-box;
    padding: 0 20px;
    width: 100%;
    height: 55px;
    background: #fff;
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.5);
    z-index: 100;
    .btn {
        margin: 10px 30%;
        width: 40%;
    }
}

.top-close-button {
    position: absolute;
    right: 30px;
    top: 8px;
    z-index: 100;
    padding: 8px;
}

.title-filter-colonne p {
    margin: 5px 0px 10px 2px;
    font-size: 15px;
    color: #383838;
    text-transform: uppercase;
    text-align: center;
}

.tab-filtre {
    padding: 20px 10px 15px 10px;
    background-color: #f8f8f8;
    color: #474747;
    font-size: 13px;
    font-family: @headings-font-family;
    font-weight: bold;
}

.container-filtre {
    display: block;
    padding: 20px 10px 8px 10px;
    overflow: auto;
    max-height: 275px;
}

.tab-filtre::after {
    content: "\e250";
    float: right;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.tab-filtre.active::after {
    content: "\e252";
}

.checkbox-secondaire::before {
    content: "";
    float: left;
    margin-right: 10px;
    width: 12px;
    height: 12px;
    border-radius: 3px;
    border: 1px solid #a8a8a8;
    background-color: #fff;
}

.yesboxmini {
    font-weight: bold;

    &::before {
        background: ~"url(../images/yesw.png)";
        background-repeat: no-repeat;
        background-size: 8px 8px;
        background-position: center;
        background-color: #585858;
        border: #585858;
    }
}

.select-box p {
    font-size: 12px;
    text-transform: none;
    display: inline-block;
    vertical-align: top;
}

.box-price {
    width: 60%;

    p {
        display: inline-block;
        vertical-align: middle;
        font-size: 13px;
        color: #444444;
        text-transform: none;
    }
}

#box-localisation {
    margin-bottom: 20px;
    height: 35px;
    border-radius: 3px;
    border: 1px solid #d3d3d3;
    background-color: #fff;
}

#content-localisation p {
    font-size: 12px;
    text-transform: none;
    color: #444444;
}

@media screen and (max-width: @screen-lg-desktop) {

    .tab-filtre {
        margin-bottom: 10px;
    }

    .header-annonce {
        height: 50px;
    }

    .button-filtre-responsive {

        button {
            margin-bottom: 10px;
            height: 50px;
        }

    }
}

@media screen and (max-width: @screen-desktop) {

    .tab-filtre {
        font-size: 11px;
        margin-bottom: 0;
    }

}

@media screen and (max-width: @screen-tablet) {
    .tab-filtre {
        font-size: 13px;
    }

    #facets > li {
        border: 1px solid #eee;
    }

    #bloc-filtre {
        vertical-align: top;
        padding: 10px 10px 55px;
    }
}
