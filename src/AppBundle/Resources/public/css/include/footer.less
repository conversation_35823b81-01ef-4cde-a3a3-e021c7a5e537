@footer_bg_color: @main_bg_color;

footer {
    background-color: @footer_bg_color;
    padding: 25px 0px 50px 0px;

    .footer__title {
        font-size: 18px;
        font-family: @headings-font-family;
        margin-top: 10px;
        margin-bottom: 10px;
        color: @main_first_color;
    }

    ul {
        padding: 10px 0;
        margin: 0;
    }

    li {
        text-decoration: none;
        list-style: none;
    }

    a {
        color: @main_third_color;
        &, &:hover, &:active, &:focus {
            text-decoration: none;
        }
    }

    a:hover {
        color: @main_first_color;
    }
}

.social-footer {

    li {
        padding-top: 10px;
        display: inline-block;
        vertical-align: middle;
        margin: 0px 5px;

        img {
            max-width: 20px;
            height: auto;
            display: block;
            margin: auto;
            position: relative;
            top: 10px;
        }
    }

    a {
        color: @main_second_color;
        font-size: 15px;
        font-family: @link-font-family;
    }
}

.circle-social {
    height: 40px;
    width: 40px;
    border-radius: 50%;
    border: 1px solid @main_third_color;
}

.menu-footer-responsive {
    text-align: center;

    .footer__title {
        background-color: #fafafa;
        padding: 5px 0px;
        margin: 0px;
    }

    .social-footer {
        position: relative;
        top: 30px;
    }

}

.propulse {
    background-color: #fff;
    p {
        text-align: center;
        margin: 5px;
        color: #7f8c8d;
        font-size: 13px;
        a {
            font-family: @text-font-family;
            text-decoration: underline;
            color: #7f8c8d;
            font-size: 13px;
        }
    }
}
