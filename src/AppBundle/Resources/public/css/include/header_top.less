.floatable {
    position: fixed;
    display: block;
    z-index: 200;
    top: 0px;
    border-bottom: 0px;
}

#header-mini {
    background-color: #FFF;
    .search-form {
        padding: 3px;
        width: 100%;
        height: 35px;
        line-height: 35px;
    }
    .dropdown-toggle {
        color: #000;
    }
    button.square {
        padding: 0;
        line-height: 35px;
        width: 35px;
        height: 35px;
        &.js-toggle-menu {
            background: none;
        }
    }
    .mx-shadow;
    transition: box-shadow .25s;

    // set logo max height to top-bar height (50px)
    .logo-header {
        max-height: 50px;
    }
}

#header-classic {
    border: 1px solid transparent;
    position: fixed;
    width: 100%;
    z-index: 100;
    .btn-primary.faded {
        background-color: @faded_second_color;
    }
}

.header-top {
    height: 50px;
    line-height: 50px;
    width: 100%;

    nav {
        float: right;
    }

    li {
        display: inline-block;
        margin-left: 15px;

        a {
            &, &:hover, &:active, &:focus {
                text-decoration: none;
            }
        }
    }

    a[role="menuitem"] {
        color: #fff;
    }

    .btn-primary {
        height: 35px;
        line-height: 35px;
        padding: 0 10px;
    }

    .dropdown-menu {
        padding: 20px;
        border: none;
        line-height: 2;

        #connect-form {
            min-width: 300px;
        }
    }

}

.nav-top {

    a {
        color: #474747;
        font-family: @link-font-family;
    }

    a:hover {
        color: @main_first_color;
    }
}

#moncompte {
    text-align: left;

    input {
        margin-bottom: 10px;
        width: 100%;
    }

    button {
        width: 100%;
        margin: 15px 0px;
        font-size: 16px;
        border: 1px solid @main_second_color;
        background-color: @main_second_color;
    }

    a {
        font-size: 12px;
        color: #696969;

        .create-compte {
            text-align: center;
            font-size: 14px;
            font-family: @link-font-family;
            font-weight: bold;
            color: @main_second_color;
        }
    }

    p {
        font-size: 11px;
    }

}

#monpanier {

    button {
        width: 100%;
        margin: 5px 0px;
        font-size: 16px;
    }

}

.panier-image {
    height: 50px;
    width: 50px;
    background-color: #efefef;
}

.content-panier {
    min-width: 200px;
    text-align: left;

    p {
        font-size: 12px;
        color: @main_third_color;
    }

}

.top-menu-responsive {

    li button.square {
        width: 35px;
        height: 35px;
        padding: 0px;

        &.js-toggle-menu {
            background: none;
        }

        img {
            max-width: 25px;
            height: auto;
        }

    }

}

@media screen and (max-width: @screen-tablet) {

    .header-top {

        img {
            max-width: 175px;
            height: auto;
        }

        li {
            margin-left: 5px;
        }

        .header-scroll-elements {

            a {
                margin-right: 0px;
            }

            input {
                max-width: 100px;
            }

        }
    }
}

.header-top .dropdown .dropdown__title {
    font-family: @headings-font-family;
    font-size: 30px;
    margin: 10px 0 10px 0;
}
