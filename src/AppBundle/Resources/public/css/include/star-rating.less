/*!
 * @copyright &copy; <PERSON><PERSON><PERSON>, Krajee.com, 2013 - 2015
 * @version 3.5.4
 *
 * A simple yet powerful JQuery star rating plugin that allows rendering
 * fractional star ratings and supports Right to Left (RTL) input.
 *
 * For more JQuery/Bootstrap plugins and demos visit http://plugins.krajee.com
 * For more Yii related demos visit http://demos.krajee.com
 */
.rating-loading {
    width: 25px;
    height: 25px;
    font-size: 0px;
    color: #fff;
    background: ~"transparent url('../images/star-rating-loading.gif') top left no-repeat";
    border: none;
}

.js-rating {
    display: none;
}

/*
 * Stars
 */
.rating-fa {
    font-family: 'FontAwesome';
    padding-left: 1px;
}

.rating-fa .rating-stars:before {
    padding-left: 1px;
}

.rating-gly {
    font-family: 'Glyphicons Halflings';
}

.rating-gly-star {
    font-family: 'Glyphicons Halflings';
    padding-left: 2px;
}

.rating-gly-star .rating-stars:before {
    padding-left: 2px;
}

.rating-lg .rating-gly-star, .rating-lg .rating-gly-star .rating-stars:before {
    padding-left: 4px;
}

.rating-xl .rating-gly-star, .rating-xl .rating-gly-star .rating-stars:before {
    padding-left: 2px;
}

.rating-active {
    cursor: default;
}

.rating-disabled {
    cursor: default;
}

.rating-uni {
    font-size: 1.2em;
    margin-top: -5px;
}

.rating-container {
    position: relative;
    vertical-align: middle;
    display: inline-block;
    color: #e3e3e3;
    overflow: hidden;
}

.rating-container:before {
    content: attr(data-content);
}

.rating-container .rating-stars {
    position: absolute;
    left: 0;
    top: 0;
    white-space: nowrap;
    overflow: hidden;
    color: @text-grey;
    transition: all 0.05s ease-out;
    -o-transition: all 0.05s ease-out;
    -moz-transition: all 0.05s ease-out;
    -webkit-transition: all 0.05s ease-out;
}

.rating-container .rating-stars:before {
    content: attr(data-content);
}

.rating-container-rtl {
    position: relative;
    vertical-align: middle;
    display: inline-block;
    overflow: hidden;
    color: @text-grey;
}

.rating-container-rtl:before {
    content: attr(data-content);
}

.rating-container-rtl .rating-stars {
    position: absolute;
    left: 0;
    top: 0;
    white-space: nowrap;
    overflow: hidden;
    color: #e3e3e3;
    transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    -moz-transition: all 0.25s ease-out;
    -webkit-transition: all 0.25s ease-out;
}

.rating-container-rtl .rating-stars:before {
    content: attr(data-content);
}

/**
 * Rating sizes
 */
.rating-xl {
    font-size: 4.89em;
}

.rating-lg {
    font-size: 3.91em;
}

.rating-md {
    font-size: 3.13em;
}

.rating-sm {
    font-size: 1.4em;
}

.rating-xs {
    font-size: 1em;
}

/**
 * Clear rating button
 */
.star-rating .clear-rating, .star-rating-rtl .clear-rating {
    color: #aaa;
    cursor: not-allowed;
    display: inline-block;
    vertical-align: middle;
    font-size: 60%;
}

.clear-rating-active {
    cursor: pointer !important;
}

.clear-rating-active:hover {
    color: #843534;
}

.star-rating .clear-rating {
    padding-right: 5px;
}

/**
 * Caption
 */
.star-rating .caption, .star-rating-rtl .caption {
    color: #999;
    display: inline-block;
    vertical-align: middle;
    font-size: 55%;
}

.star-rating .caption {
    padding-left: 5px;
}

.star-rating-rtl .caption {
    padding-right: 5px;
}

/**
 * Print
 */
@media print {
    .rating-container, .rating-container:before, .rating-container-rtl .rating-stars, .rating-container-rtl .rating-stars:before {
        color: #f3f3f3 !important;
    }
    .star-rating .clear-rating, .star-rating-rtl .clear-rating {
        display: none;
    }
}
