.main-search {
    cursor: pointer;
    height: 100%;
    line-height: 55px;
    color: #888;
    background-color: #fff;
    font-size: 15px;
    font-family: @text-font-family;
    text-align: left;
    position: relative;
    .arrow-bottom {
        position: absolute;
        top: 25px;
        right: 25px;
    }
    span {
        padding: 10px;
    }
    @media screen and (max-width: @screen-phone) {
        font-size: 13px;
        span {
            padding: 25px;
        }
    }
}

.current > span {
    font-weight: bold;
}

#categories-menu-content {
    position: relative;
    z-index: 100;
    text-align: left;
    background-color: #fff;
    display: none;
    cursor: pointer;
    width: 100%;
    padding: 0 10px;

    button {
        min-width: 50%;
        height: 40px;
        margin: 10px 0;
    }

    ul {
        padding: 0;
    }

    li {
        list-style: none;
        margin-bottom: 5px;
        ul {
            padding: 10px 20px;
            display: none;
            span {
                color: #626262;
            }
        }
    }

    #active-category {
        font-weight: bold;
    }

}

.tt-dropdown-menu {
    background-color: @main_bg_color;
    .tt-cursor {
        background-color: @main_third_color;
    }
}

.search-results__title {
    font-family: @headings-font-family;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 36px;
    margin-top: 20px;
    margin-bottom: 10px;
}
