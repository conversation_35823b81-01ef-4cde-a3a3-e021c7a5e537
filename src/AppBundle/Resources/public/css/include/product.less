@productFont: "HelveticaNeue-Light", "Helvetica Neue Light", "Helvetica Neue", Helvetica, Arial, "Lucida Grande", sans-serif;

.product-thumb {
    height: 300px;
    background-color: @main_bg_color;
    .mx-shadow;
    margin: 0.5rem 0 1rem 0;
    border-radius: 2px;
    overflow: hidden;
}

.content_annonce_image {
    display: block;
    margin: auto;
    width: 265px;
    padding: 15px 0px;
    margin-bottom: 20px;
    overflow: hidden;
}

.container-annonce {
    display: block;
    background-color: #fff;
    width: 100%;
    height: 100%;

    & img {
        width: auto;
        height: 175px;
        display: block;
        margin: auto;
    }
}

.product-main-info {
    @media screen and (max-width: @screen-phone) {
        .col-xs-12 {
            width: calc(100% - 10px);
            margin-left: 5px;
        }
    }
    .container-full {
        padding: 15px;
        margin: 1rem 0 0 0;
        .mx-shadow;
        transition: box-shadow .25s;
        .product__description {
            margin-bottom: 0;
            margin-top: 0.5rem;
        }
        .product__description-link {
            &, a {
                font-family: @productFont;
                text-align: right;
            }
        }
        .product__seller-link {
            text-decoration: none;
            display: block;
            font-weight: 700;
        }
        .product__contact-link {
            text-decoration: none;
        }
        p {
            font-family: @productFont;
        }
        hr {
            margin-bottom: 10px;
            margin-top: 10px;
            visibility: hidden;
        }
        h1 {
            margin-top: 5px;
            margin-bottom: 0.5rem;
            line-height: 22px;
            font-size: 18px;
            font-weight: 400;
            font-family: @productFont;
        }
    }
    .product__signal-link {
        text-decoration: none;
        color: #b1b3ba;
        -webkit-font-smoothing: antialiased;
    }
}

.content-annonce {
    padding: 15px;
    height: 80px;
    border-top: 4px solid #fafafa;
    border-bottom: 0px solid #ededed;

    & h3.product-name {
        height: 42px;
        overflow: hidden;
        display: block;
        top: 10px;
        line-height: 1.4em;
        font-size: 14px;
        color: #414141;
        font-family: @text-font-family;
        margin: 0;
    }

    .price {
        p {
            background-color: @main_second_color;
            border: 0 none;
            color: @main_bg_color;
            float: right;
            font-size: 14px;
            margin-top: 10px;
            padding: 5px;
        }
        &::after {
            clear: both;
            content: " ";
        }
    }

    .old-price {
        p {
            float: right;
            font-size: 15px;
            margin: 10px;
            padding: 5px;
            text-decoration: line-through;
            color: #7F7F7F;
        }
    }

}

.price {
    color: @main_second_color;
}

.product-main-info {
    h1 {
        font-size: 18px;
        font-weight: 400;
    }
    .product-list-field {
        margin: 10px 0;
    }
    .product-secondary {
        &, a {
            font-family: @text-font-family;
            font-size: 12px;
        }
        a {
            text-decoration: underline;
        }
    }
    .price-product {
        .price {
            line-height: 44px;
        }
    }
    .shipping {
        margin: 10px 0;
    }
    p {
        margin: 0;
    }
    a {
        font-family: @productFont;
    }
}

.breadcrumb {
    background-color: #fff;
}

.produit {
    height: 668px;

    a, img {
        max-height: 100%;
        max-width: 100%;
        width: auto;
        height: auto;
        display: block;
        margin: auto;
    }

    .vertical-pager-container {
        position: absolute;
        left: 22px;
        padding: 13px 0;
        top: 12px;
        z-index: 101;
        width: 40px;
        height: 80px;
        img {
            border: 1px @main_third_color solid;
            margin-bottom: 10px;
            cursor: pointer;
        }
        ul {
            list-style-type: none;
            &, li {
                margin: 0;
                padding: 0;
            }
        }
    }

    @media screen and (max-width: @screen-tablet) {
        height: auto;
        max-height: 650px;
    }
}

.images-cycle-container {
    visibility: hidden;
    &.initialized {
        visibility: visible;
    }
}

.vertical-pager-next,
.vertical-pager-prev {
    &, .produit & {
        background: rgba(255, 255, 255, 0.75);
        position: absolute;
        text-align: center;
        width: 100%;
        z-index: 102;
        &.disabled {
            display: none;
        }
    }
}

.vertical-pager-prev {
    top: 0;
}

.vertical-pager-next {
    bottom: 8px;
}

.original-price {
    text-decoration: line-through;
}

.sidebar-produit {

    h2 {
        margin: 0;
        padding: 0;
    }
    .marged {
        margin-top: 15px;
    }
    .qty-container {
        margin: 6px 0;
    }
    .options-wrapper {
        label {
            margin: 0;
            font-size: 12px;
            font-family: @productFont;
            font-weight: 400;
            color: #8a8f9c;
            -webkit-font-smoothing: antialiased;
        }
        input, select {
            margin: 0 0 10px 0;
        }
    }

    .promotion-amount {
        color: @main_second_color;
        font-weight: bold;
    }

    .original-price {
        font-size: 16px;
        font-weight: bold;
        margin-top: -13px;
    }

    .mixin-button-type {
        font-size: 17px;
        height: 53px;
    }

    .product__add-to-cart {
        input.btn {
            .mixin-button-type;
        }
    }

    .product__contact-vendor {
        a {
            .mixin-button-type;
            font-family: @link-font-family;
            line-height: 24px;
            padding-top: 16px;
        }
    }
}

.price-product {
    text-align: right;
    font-size: 40px;
    font-weight: bold;

    p {
        margin: 0 0 10px 0;
    }
}

.tab-content {
    background-color: @main_bg_color;
    padding: 15px;
    margin-bottom: 15px;
}

.in-stock, .not-in-stock {
    font-weight: bold;
}

.in-stock {
    color: @green;
}

.not-in-stock {
    color: @red;
}

.nav-tabs {
    border: none;
    margin-top: 15px;
}

/* Quantity changer */
.changer .input-text-short {
    float: left;
    width: 40px;
    text-align: center;
    .visible-xs & {
        width: 70px;
    }
}

.changer label {
    margin-top: 2px;
}

.product-fields-group .qty {
    padding-top: 0;
}

.hidden-xs .changer input {
    width: 6rem;
}

/* /Quantity changer */
@media screen and (max-width: @screen-phone) {
    .sidebar-produit {
        height: auto;
        margin-top: 15px;
    }
    .produit {
        height: auto;
        max-height: 668px;
    }
}
