@cookies_background: @main_bg_color;
@cookies_main_text: @text-grey;
@cookies_link: @text-grey;
@cookies_link_hover: @cookies_link;
@cookies_button_bg: @main_third_color;
@cookies_button_hover_bg: @cookies_button_bg;
@cookies_button_text: @main_bg_color;
@cookies_button_hover_text: @main_bg_color;

.cc_banner-wrapper {
    z-index: 9001;
    position: relative;
    display: none;
    &.cc_wizaplace_banner-wrapper {
        display: block;
    }
}

.cc_container {
    position: fixed;
    display: flex;
    background: @cookies_background;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    padding: 8px 15px;
    box-shadow: 0px 0px 2px #AAA;

    .cc_content {
        display: flex;
        margin: 0 auto;
    }

    .cc_btn, .cc_btn:visited {
        padding: 6px 12px;
        background-color: @cookies_button_bg;
        display: block;
        align-self: center;
        margin: 0 0 0 10px;
        background: @cookies_background;
        box-sizing: border-box;
        color: @cookies_button_text;
        background-color: @cookies_button_bg;
        transition: background 200ms ease-in-out, color 200ms ease-in-out, box-shadow 200ms ease-in-out;
        -webkit-transition: background 200ms ease-in-out, color 200ms ease-in-out, box-shadow 200ms ease-in-out;
        border-radius: 5px;
        -webkit-border-radius: 5px;
        cursor: pointer;
        text-align: center;
        font-weight: bold;
    }

    .cc_btn:hover, .cc_btn:active {
        background-color: @cookies_button_hover_bg;
        color: @cookies_button_hover_text;
    }

    .cc_message {
        align-self: center;
        transition: font-size 200ms;
        margin: 0;
        padding: 0;
    }

    a, a:visited {
        text-decoration: none;
        color: @cookies_link;
        transition: 200ms color;
        white-space: nowrap;
    }

    a:hover, a:active {
        color: @cookies_link_hover
    }

    .cc_more_info {
        text-align: right;
    }
}

.cc_container {
    &, & a {
        font-size: 10px;
    }
}

@media screen and (min-width: 992px) {
    .cc_container {
        &, & a {
            font-size: 12px;
        }
        left: initial;
        right: 20px;
        bottom: 20px;
        width: 300px;
    }
}

@media screen and (min-width: 768px) {
    .cc_container {
        &, & a {
            font-size: 11px;
        }
    }
}

@media print {
    .cc_banner-wrapper, .cc_container {
        &, &.cc_wizaplace_banner-wrapper {
            display: none;
        }
    }
}
