.btn-input {
    padding: 8px 20px;
    margin-bottom: 30px;
    font-size: 14px;
    color: @main_bg_color;
    border: 1px solid @main_first_color;
    background-color: @main_first_color;
}

.timeline {
    height: 1000px;
    margin-top: 25px;
    background-color: #fafafa;
}

.image-collection {
    input[type=file] {
        display: inline-block;
    }
    #form_images div, #form_session_images div {
        padding: 5px 0;
        &.js-collection-remove {
            padding-left: 10px;
            display: inline-block;
            color: lighten(@text-grey, 20%);
            cursor: pointer;
        }
    }
    .js-collection-add {
        margin-top: 5px;
    }
}

.choose-categories {
    position: absolute;
    z-index: 10;
    margin-top: 25px;
    display: none;
}

.container-categories {
    padding: 20px;
    border: 1px solid #e7eaee;
    background-color: #fff;

    li {
        padding: 5px 15px;
        list-style: none;

        label {
            font-weight: normal;
        }

        a {
            display: inline-block;
            vertical-align: middle;
            font-size: 15px;
            color: #49545e;
        }

    }
}

.arrow-category {
    display: inline-block;
    vertical-align: middle;
    width: 0px;
    border: 5px solid transparent;
    border-left: 5px solid #dadada;
}

.bloc-publish {
    background-color: @main_bg_color;
    margin-top: 30px;
    margin-bottom: 30px;
    padding: 5px 30px;

    h2 {
        margin-top: 0px;
        color: @main_first_color;
    }

    label {
        color: #373b40;
    }

}

.publish-form {
    padding: 15px;
    margin-bottom: 30px;
    margin-top: 30px;
    background-color: #f7f7f7;
}

.pro-publish {
    text-align: right;

    a {
        position: relative;
        top: 15px;
        padding: 10px;
        border: 1px solid @main_first_color;
        color: @main_first_color;
    }
}

.circle-publish {
    float: right;
    margin-top: -50px;
    height: 25px;
    width: 25px;
    background-color: #fff;
}

.publier-annonce {
    margin-bottom: 30px;
}

.categories-features {
    padding-bottom: 20px;
}

.publish-sidebar {
    height: 200px;
    padding: 50px 30px;
    text-align: center;
    margin-bottom: 15px;
    background-color: #fff;

    p {
        font-family: @headings-font-family;
        font-size: 20px;
        margin-bottom: 15px;
    }

    a {
        color: @main_second_color;
        font-weight: 700;
        padding: 5px 10px;
        background-color: #efefef;
    }
}
