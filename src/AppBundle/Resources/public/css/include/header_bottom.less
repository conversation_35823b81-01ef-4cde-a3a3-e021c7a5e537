@header_bg_image: "../images/db.jpg";
@header_alpha: 0.6;

.header-bottom-search {
    background-color: #000;
    background: ~"url(" @header_bg_image ~")";
    background-size: 100% auto;
    background-position: center;
    background-repeat: no-repeat;

    .filtre-header-bottom {
        background-color: rgba(26, 36, 44, @header_alpha);
        height: 100%;
    }

    .search-form-home {
        background-color: rgba(0, 0, 0, 0.1);
        padding: 10px 10px 10px 10px;
        @media screen and (max-width: @screen-phone) {
            margin-right: 3px;
            padding-top: 0;
        }
    }

    h1 {
        font-size: 38px;
        margin-top: 75px;
        text-align: center;
        color: @main_bg_color;
    }

    h2 {
        text-align: center;
    }

    .header__baseline--secondary {
        font-family: @headings-font-family;
        font-size: 22px;
        line-height: 24px;
        text-align: center;
        color: @main_bg_color;
        margin-top: 20px;
        margin-bottom: 10px;
    }

    form#header-search-form {
        margin: 40px 0;
        text-align: center;

        .home-header & {
            margin: 35px 0 85px;
        }

        p {
            color: #888;
            position: relative;
            display: inline-block;
            vertical-align: middle;
            top: 3px;
        }

        input {
            height: 55px;
            width: 100%;
            padding: 10px;
            border: 0px solid #f8f8f8;
            font-size: 15px;
            font-family: @text-font-family;
            margin: 0;

            &:focus {
                outline: none;
            }
        }

        button {
            height: 55px;
            width: 100%;
            color: @main_bg_color;
            border: none;
            text-transform: uppercase;
            background-color: @main_first_color;
        }

        .search-category {
            height: 55px;
            background-color: #f3f3f3;
        }

    }
}

.arrow-bottom {
    width: 0;
    height: 0;
    display: inline-block;
    vertical-align: middle;
    position: relative;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #888;
}

.header-bottom-concept {
    background-color: rgba(26, 36, 44, 0.6);
    height: 80px;
    padding: 10px 0;
    transition: box-shadow .25s;
    border-radius: 2px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16), 0 2px 10px 0 rgba(0, 0, 0, .12);

    img {
        position: relative;
        top: 15px;
    }

    h2 {
        margin: 0 0 0 13px;
        padding: 0;
        color: #fff;
        font-size: 18px;
        font-family: @headings-font-family;
        line-height: 1.2em;
        position: relative;
        top: 9px;
    }

    button {
        background: none;
        border: 1px solid @main_third_color;
        color: @main_third_color;
        font-size: 14px;
        height: 55px;
        display: block;
        margin: 0 auto;
    }
}

@media screen and (max-width: @screen-lg-desktop) {

    .header-bottom-concept {
        height: 70px;
        p {
            font-size: 14px;
        }
        img {
            top: 10px;
        }
    }
}

@media screen and (max-width: @screen-desktop) {

    .header-bottom-search {

        h1 {
            font-size: 34px;
            margin-top: 50px;
        }

        h2 {
            font-size: 14px;
            margin-left: 20px;
        }

        form#header-search-form {
            margin: 30px 0;
            .home-header & {
                margin: 45px 0 60px 0;
            }
        }
    }

    .header-bottom-concept {

        p {
            font-size: 12px;
            margin-left: 25px;
            top: 5px;
        }
    }

}

@media screen and (max-width: @screen-tablet) {

    .header-bottom-search {
        height: auto;
        background-size: 140% auto;

        h1 {
            display: none;
        }

        h2 {
            display: none;
        }

        form#header-search-form {
            &, .home-header & {
                height: auto;
                margin-top: 15px;
                margin-bottom: 15px;
            }

            p {
                font-size: 12px;
            }

            input {
                font-size: 13px;
                margin-top: 10px;
                height: 55px;
            }

            button {
                margin-top: 10px;
                img {
                    max-width: 25px;
                    height: auto;
                    opacity: 0.7;
                }
            }

            .search-category {
                margin-top: 10px;
            }

        }
    }

    .responsive-search-hide {
        display: none;

        input {
            margin-top: 10px;
        }

    }

    .header-bottom-concept {
        height: auto;
        padding: 5px 0px;
    }
}

@media screen and (max-width: @screen-phone) {
    .header-bottom-concept {
        background-color: transparent;
    }

    .header-bottom-search {
        background-size: cover;

        form#header-search-form {
            input {
                text-align: center;
            }
        }
    }
}

@media screen and (min-width: @screen-tablet) {
    .more-search {
        padding-left: 5px !important;
        padding-right: 5px !important;
    }

    .more-search-margin-right {
        padding-right: 5px;
    }

    .more-search-margin-left {
        padding-left: 5px;
    }
}
