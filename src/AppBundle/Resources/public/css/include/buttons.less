button, .btn, [class*=btn-] {

    &, a& {
        font-family: @link-font-family;
        font-weight: bold;
        padding: 8px 10px 8px 10px;
        background-color: @main_third_color;
        border: none;
        color: @main_bg_color;
        display: inline-block;
        box-shadow: none;
        cursor: pointer;
        max-width: 100%;
        white-space: normal;
        &, &:hover, &:active, &:focus {
            text-decoration: none;
        }
    }

    &.btn-primary {

        background-color: @main_second_color;

    }

    &.btn-secondary {

        background-color: @main_first_color;

    }

    &.btn-third {

        background-color: @main_third_color;

    }

    &.btn-lg {
        font-size: 22px;
    }

    &.btn-small {
        font-size: 14px;
    }

    &[disabled] {
        background-color: #bbb;
        cursor: not-allowed;
    }
}
