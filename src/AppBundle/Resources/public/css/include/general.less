/*Media queries breakpoints, defined from bootstrap*/
@screen-phone: 480px;
@screen-tablet: 767px;
@screen-desktop: 992px;
@screen-lg-desktop: 1200px;

* {
    padding: 0;
    margin: 0;
}

body {
    width: 100%;
    margin: 0;
    padding: 0;
    text-decoration: none;
    list-style: none;
    background-color: @body_bg_color;
}

h1 {
    font-family: @headings-font-family;
    text-transform: uppercase;
    font-weight: bold;
}

h2, h3, h4, h5, h6 {
    font-family: @headings-font-family;
    font-weight: normal;
}

p {
    font-family: @text-font-family;
    color: #474747;
    &.spaced {
        margin: 15px 0;
    }
}

a {
    font-family: @link-font-family;
    font-size: 14px;
    color: #474747;
    cursor: pointer;
    text-decoration: underline;
}

a[role="tab"],
.no-underline {
    &, &:hover, &:active, &:focus {
        text-decoration: none;
    }
}

fieldset {
    margin: 15px 0px;
    padding: 0px 20px 20px 20px;
    background-color: #fff;
    border: 1px solid #e7eaee;
    border-radius: 5px;
    display: table-cell;

    h2 {
        width: 100%;
        color: @main_first_color;
        font-family: @headings-font-family;
        background-color: @bg-light;
        font-size: 20px;
        font-weight: bold;
        margin-left: -20px;
        padding: 10px 20px;
        margin-top: 0;
        margin-bottom: 15px;
        box-sizing: content-box;
    }
}

label {
    font-family: @text-font-family;
    font-size: 13px;
    color: @text-grey;
    &.required::after, span.required::after {
        content: "*";
        color: red;
    }
}

input {
    border-radius: 0;
}

ul {
    list-style-position: inside;
    &.bullets-list {
        margin: 1em 0;
        padding: 0 0 0 40px;
    }
}

.bloc-principal {
    margin-top: 30px;
    margin-bottom: 30px;
}

.container-full {
    background-color: @main_bg_color;
    padding: 10px 30px 30px 30px;

    h1 {
        margin: 0;
        padding: 0;
    }
}

.profile-menu .container-full {
    padding-top: 0px;
    background-color: transparent;
}

.form-group {
    margin: 10px 0px;
}

.on-hover-dropdown {
    position: relative;

    .dropdown-content {
        display: none;
    }

    .dropdown-content {
        display: none;
        position: absolute;
        padding: 15px;
        z-index: 150;
        right: 0px;
        background-color: #fff;
    }
}

.fixed {
    position: fixed;
}

.inline-block {
    display: inline-block;
    float: none;
}

.middle {
    vertical-align: middle;
}

.scroll {
    overflow: scroll;
}

.text-right {
    text-align: right;
}

.marged {
    margin-top: 10px;
    margin-bottom: 10px;
}

.input-group-addon {
    label {
        margin-bottom: 0;
    }
    input {
        vertical-align: bottom;
        position: relative;
        top: 1px;
    }
}

/*   Bloc annonce   */

.bloc-content {
    margin: 10px 0 20px 0;
}

.bloc-filtre {
    background-color: @main_bg_color;
    height: 700px;
}

.header-annonce,
.header-description {
    background-color: @main_bg_color;
    padding: 15px;
    margin: 0.5rem 0 1rem 0;
    border-radius: 2px;
    .mx-shadow;
}

.header-annonce {
    height: 60px;
}

.button-filtre-responsive {

    button {
        height: 60px;
        width: 100%;
        padding: 0px;
        background-color: @main_third_color;
        .mx-shadow;
    }

}

footer {
    height: 300px;
    background-color: @main_bg_color;
}

/* Ajax */
.ajax-loading-box {
    z-index: 100001;
    position: fixed;
    top: 50%;
    padding: 0px;
    left: 50%;
    width: 52px;
    height: 52px;
    margin-top: -26px;
    margin-left: -26px;
    background: ~"url('../images/ajax_loader.gif') no-repeat 10px 10px #0d0d0d";
    opacity: .8;
    overflow: visible;
    display: none;
}

@media screen and (max-width: @screen-phone) {
    .bloc-content {
        margin-top: 5px;
    }
}

.red {
    color: @red;
}

.smalltext {
    font-size: 11px;
}

.mx-shadow {
    transition: box-shadow .25s;
    box-shadow: 0 1px 0 0 #D0D1D5, 0 0 0 1px rgba(220, 221, 224, 0.7);
}

.one-line-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
