imports:
    - { resource: services/locale.yml }

services:
    # This service is duplicated because it is overridden in some other front bundles
    marketplace.price.formatter:
        class: Wizacha\Marketplace\Price\DetailedPriceFormatter
        public: true
        arguments:
            - '%currency.code%'
    marketplace.backend.price.formatter:
        class: Wizacha\Marketplace\Price\DetailedPriceFormatter
        public: true
        arguments:
            - '%currency.code%'
    # Default autowired service
    Wizacha\Marketplace\Price\DetailedPriceFormatter: '@marketplace.backend.price.formatter'

    marketplace.asset_manager:
        class: Wizacha\Marketplace\Theme\LocalAssetManager
        public: true
        arguments:
            - '%kernel.project_dir%'
            - '%marketplace.version%'
            - '%base_url%'
    app.setting_storage:
        class: Wizacha\AppBundle\Service\SettingStorage
        public: true
        arguments:
            - '@database_connection'
            - '%cscart_table_prefix%'

    app.base_backend_menu_extension:
        class: Wizacha\AppBundle\Backend\Menu\MenuExtension
        public: true
        arguments:
            - '@session'
            - '%feature.multi_vendor_product%'
            - '%feature.pim_sync_product_from_mvp%'
            - '%feature.organisations_enabled%'
            - '@marketplace.payment.stripe'
            - '%feature.available_offers%'
            - '%feature.marketplace_discounts%'
            - '%feature.subscription%'
            - '%feature.currency.advanced%'
            - '@marketplace.payment.mangopay'
            - '@Wizacha\Marketplace\Company\CompanyService'
            - '@Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService'
            - '%feature.user_groups_enabled%'
            - '%feature.enable_yavin%'

    app.backend_menu_extension:
        alias: app.base_backend_menu_extension
        public: true

    app.backend_menu_badge_extension:
        class: Wizacha\AppBundle\Backend\Menu\MenuBadgeExtension
        public: true
        arguments:
            - '@doctrine.dbal.default_connection'
            - '@Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService'

    marketplace.backend_PricesImporter:
        class: Wizacha\Exim\Import\Inventory\Prices

    marketplace.backend_QuantitiesImporter:
        class: Wizacha\Exim\Import\Inventory\Quantities

    marketplace.backend_productimporter:
        class: Wizacha\Exim\Import\Product\SimpleImporter
        public: true
    marketplace.api_productimporter:
        class: Wizacha\Exim\Import\Product\ImportAndRemoveUnspecifiedDeclinations
        public: true

    marketplace.pim.attribute_service:
        class: Wizacha\Marketplace\PIM\Attribute\AttributeService
        public: true
        arguments:
            - '@doctrine.dbal.default_connection'
            - '@event_dispatcher'

    Wizacha\Marketplace\PIM\Option\OptionService:
        class: Wizacha\Marketplace\PIM\Option\OptionService
        autowire: true
        public: true

    marketplace.product.csv_converter:
        class: Wizacha\Marketplace\PIM\Product\Exim\CsvConverter
        public: true
        arguments:
            - []

    marketplace.multi_vendor_product.linker.rule.ean:
        class: Wizacha\Marketplace\PIM\MultiVendorProduct\Linker\Rule\EanMatchingRule
        public: true
        arguments:
            - '@doctrine.orm.entity_manager'

    marketplace.multi_vendor_product.linker.rule.supplier_reference:
        class: Wizacha\Marketplace\PIM\MultiVendorProduct\Linker\Rule\SupplierReferenceMatchingRule
        public: true
        arguments:
            - '@doctrine.orm.entity_manager'

    marketplace.multi_vendor_product.linker.rule.locator:
        class: Symfony\Component\DependencyInjection\ServiceLocator
        public: true
        tags: ['container.service_locator']
        arguments:
            -
                ean: '@marketplace.multi_vendor_product.linker.rule.ean'
                supplier_reference: '@marketplace.multi_vendor_product.linker.rule.supplier_reference'

    marketplace.multi_vendor_product.linker:
        class: Wizacha\Marketplace\PIM\MultiVendorProduct\Linker\MultiVendorProductLinker
        public: true
        arguments:
            - '@marketplace.multi_vendor_product.service'
            - '@marketplace.pim.product.service'
            - '%feature.multi_vendor_product.rules%'
            - '%feature.multi_vendor_product%'
            - '%feature.multi_vendor_product_auto_link%'
            - '%feature.multi_vendor_product_auto_create_if_no_match%'
            - '@doctrine.orm.entity_manager'
            - '@logger'
            - '@marketplace.async_dispatcher'
            - '@marketplace.multi_vendor_product.linker.rule.locator'
        tags:
            - { name: kernel.event_subscriber }

    app.notification_dispatcher:
        class: Wizacha\Component\Notification\NotificationDispatcher
        public: true
        arguments:
            - '@service_container'
            - '@Wizacha\AppBundle\Notification\NotificationConfigService'
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\AppBundle\EventSubscriber\Yavin\User\UserActivatedSubscriber:
        autowire: true
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\AppBundle\EventSubscriber\Yavin\User\UserCreatedSubscriber:
        autowire: true
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\AppBundle\EventSubscriber\Yavin\User\UserUpdatedSubscriber:
        autowire: true
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\AppBundle\EventSubscriber\Yavin\User\UserDeactivatedSubscriber:
        autowire: true
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\AppBundle\EventSubscriber\Yavin\User\UserTypeChangedSubscriber:
        autowire: true
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\AppBundle\EventSubscriber\Yavin\User\UserCompanyChangedSubscriber:
        autowire: true
        tags:
            - { name: kernel.event_subscriber }

    app.mailer:
        class: Wizacha\Bridge\Swiftmailer\SwiftMailer
        public: true
        arguments:
            - '@mailer'
            - '%kernel.project_dir%'
            - '@marketplace.async_dispatcher'

    app.mailer.debug:
        class: Wizacha\Bridge\Swiftmailer\DebugSwiftMailer
        public: true
        arguments:
            - '@swiftmailer.mailer.fake_mailer'

    app.notification.test_notifier:
        class: Wizacha\Component\Notification\TestNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@marketplace.admin_company'

    Wizacha\AppBundle\Notification\UserNotifier:
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'
            - '@app.bfo_service'

    app.notification.user_notifier.debug:
        class: Wizacha\AppBundle\Notification\UserNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'
            - '@app.bfo_service'

    app.notification.option_notifier:
        class: Wizacha\AppBundle\Notification\OptionNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.option_notifier.debug:
        class: Wizacha\AppBundle\Notification\OptionNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    Wizacha\AppBundle\Notification\OrderNotifier:
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'
            - '@marketplace.order.order_service'
        calls:
            - method: setNotifyShipmentCreated
              arguments:
                  - '%feature.notify_shipment_created%'
            - method: setSubscription
              arguments:
                  - '%feature.subscription%'
                  - '@Wizacha\Marketplace\Subscription\SubscriptionService'
            - method: setExternalFrontOfficeUrl
              arguments:
                  - '%external_front_office_url%'
            - method: setPriceFormatter
              arguments:
                  - '@marketplace.backend.price.formatter'

    app.notification.order_notifier.debug:
        class: Wizacha\AppBundle\Notification\OrderNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'
            - '@marketplace.order.order_service'
        calls:
            - method: setNotifyShipmentCreated
              arguments:
                  - '%feature.notify_shipment_created%'
            - method: setSubscription
              arguments:
                  - '%feature.subscription%'
                  - '@Wizacha\Marketplace\Subscription\SubscriptionService'
            - method: setExternalFrontOfficeUrl
              arguments:
                  - '%external_front_office_url%'

    app.notification.rma_notifier:
        class: Wizacha\AppBundle\Notification\RmaNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@marketplace.order.order_service'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.rma_notifier.debug:
        class: Wizacha\AppBundle\Notification\RmaNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@marketplace.order.order_service'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.product_notifier:
        class: Wizacha\AppBundle\Notification\ProductNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@marketplace.pim.product.service'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.product_notifier.debug:
        class: Wizacha\AppBundle\Notification\ProductNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@marketplace.pim.product.service'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.company_notifier:
        class: Wizacha\AppBundle\Notification\CompanyNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.company_notifier.debug:
        class: Wizacha\AppBundle\Notification\CompanyNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    Wizacha\AppBundle\Notification\ModerationNotifier:
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.moderation_notifier.debug:
        class: Wizacha\AppBundle\Notification\ModerationNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.contact_notifier:
        class: Wizacha\AppBundle\Notification\ContactNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.contact_notifier.debug:
        class: Wizacha\AppBundle\Notification\ContactNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.discuss_notifier:
        class: Wizacha\AppBundle\Notification\DiscussNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'
            - '@app.bfo_service'
        calls:
            - [setRouter, ['@router']]

    app.notification.discuss_notifier.debug:
        class: Wizacha\AppBundle\Notification\DiscussNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'
            - '@app.bfo_service'
        calls:
            - [setRouter, ['@router']]

    app.notification.export_notifier:
        class: Wizacha\AppBundle\Notification\ExportNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.export_notifier.debug:
        class: Wizacha\AppBundle\Notification\ExportNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.organisation_notifier:
        class: Wizacha\AppBundle\Notification\OrganisationNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.organisation_notifier.debug:
        class: Wizacha\AppBundle\Notification\OrganisationNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.refund_notifier:
        class: Wizacha\AppBundle\Notification\RefundNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'
            - '@Wizacha\Marketplace\Order\Refund\Service\RefundService'
        calls:
            - [setCurrencySign, ['%currency.sign%']]
            - [setCreditNoteTemplateUrl, ['%marketplace.rma.template_url%']]
            - [setCreditNoteHelper, ['@marketplace.order.credit_note.credit_note_helper']]

    app.notification.refund_notifier.debug:
        class: Wizacha\AppBundle\Notification\RefundNotifier
        public: true
        arguments:
            - '@app.mailer.debug'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'
            - '@Wizacha\Marketplace\Order\Refund\Service\RefundService'
        calls:
            - [setCurrencySign, ['%currency.sign%']]
            - [setCreditNoteTemplateUrl, ['%marketplace.rma.template_url%']]
            - [setCreditNoteHelper, ['@marketplace.order.credit_note.credit_note_helper']]

    app.notification.security_notifier:
        class: Wizacha\AppBundle\Notification\SecurityNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    Wizacha\AppBundle\Service\TranslationService:
        public: true
        autowire: true

    translation.importer:
        public: true
        class: Symfony\Component\Translation\Reader\TranslationReader

    app.listener.language:
        class: Wizacha\AppBundle\EventListener\LanguageListener
        arguments:
            - '@app.locale.detector'
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\AppBundle\Controller\Api\ContactController:
        autowire: true
        public: true
        arguments:
            $mailer: '@app.mailer'

    Wizacha\AppBundle\Controller\Api\SeoController:
        autowire: true
        autoconfigure: true

    app.session_handler:
        class: Wizacha\Bridge\Tygh\SessionHandler
        arguments:
            - "@app.session_backend"
            - "%inactivity_time_out%"

    app.session_backend:
        class: Tygh\Backend\Session\IBackend
        factory: ['Wizacha\Bridge\Tygh\SessionFactory', createSessionBackend]

    app.env_var_processor:
        class: Wizacha\AppBundle\DependencyInjection\EnvVarProcessor
        tags:
            - { name: container.env_var_processor }

    Wizacha\AppBundle\Controller\Api\Catalog\ExportController:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Catalog\ExporterService:
        autowire: true
        arguments:
            $catalogStorageService: '@Wizacha\Storage\CatalogExportStorageService'

    marketplace.asset_theme_manager:
        class: Wizacha\Marketplace\Theme\StorageAssetManager
        arguments:
            - '@Wizacha\Storage\AssetsStorageService'
            - '@marketplace.cache'
            - '%marketplace.version%'

    marketplace.theme_customizer:
        class: Wizacha\AppBundle\Service\ThemeCustomizer
        arguments:
            - '@less_compiler'
            - '@wizacha.registry'
            - '@file_locator'
            - '@marketplace.asset_theme_manager'
            - '%base_url%'

    app.configuration_service:
        class: Wizacha\AppBundle\Service\ConfigurationService
        public: true
        arguments:
            - '@marketplace.theme_customizer'
            - '@marketplace.asset_theme_manager'
            - '@app.setting_storage'

    app.notification.currency_notifier:
        class: Wizacha\AppBundle\Notification\CurrencyNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    app.notification.company_person_notifier:
        class: Wizacha\AppBundle\Notification\CompanyPersonNotifier
        public: true
        arguments:
            - '@app.mailer'
            - '@templating'
            - '@logger'
            - '@marketplace.admin_company'
            - '@marketplace.user.user_repository'
            - '@router'
            - '@marketplace.monolog.level.mailer'

    Wizacha\AppBundle\Controller\Backend\SandboxDiskUsageController:
        public: true
        autowire: true
        arguments:
            $isSandbox: '%feature.sandbox%'
