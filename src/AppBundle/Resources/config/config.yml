parameters:
    use_new_checkout: true
    are_shippings_editable_by_vendors: true
    search_record_product_image_width: 175
    search_record_product_image_height: 175
    feature.allow_files_on_non_edp_products: false
    # Do we require users to accept the terms and conditions in the checkout (CGV)
    feature.checkout.accept_terms_and_conditions: true
    checkout.address.billing_first: true
    feature.enable_company_type_facet: false
    feature.enable_companies_facet: true
    # Par défaut les utilisateurs doivent accepter les CGU pour pouvoir créer un compte
    feature.create_account.must_accept_terms: true
    feature.multi_vendor_product_auto_link: true # Fonctionnement par défaut, souhaité dès que l'on active les MVP
    feature.enable_ecommerce_analytics: false
    # Ne peut pas être activée en même temps que le matching auto (feature.multi_vendor_product_auto_link)
    feature.pim_sync_product_from_mvp: false

    # Activation/désactivation du mode de transport Chrono 13 du transporteur Chronopost
    feature.carrier.chronopost.chrono13: false
    # Activation/désactivation du mode de transport Chrono Relais du transporteur Chronopost
    feature.carrier.chronopost.chronorelais: false

    # Mondial Relay delivery mode
    feature.carrier.mondial_relay: false

    # Désactivation du front office lorsque celui-ci est externalisé
    feature.disable_front_office: false

    # Feature de lead-gen développée pour Autonom-ease et pas utilisée depuis (ne pas oublier de modifier ce commentaire si ça change !)
    # Activer ce feature flag fera apparaitre dans le back-office la page pour consulter les leads créés.
    # Par contre il sera pour le moment impossible d'en créer : les leads sont créés dans le front, et il n'y a pas encore
    # d'API/SDK pour ça.
    feature.lead_gen: false

    feature.attributes_in_groups_can_override_categories: true

    marketplace.transaction_mode.transactional: true
    marketplace.transaction_mode.contact: true


    smarty.template_directories:
        - '%kernel.project_dir%/src/%theme_bundle%/Resources/views/frontend/'
        - '%kernel.project_dir%/src/AppBundle/Resources/views/frontend/'
    smarty.plugin_directories:
        - '%kernel.project_dir%/src/%theme_bundle%/Smarty/'
        - '%kernel.project_dir%/src/AppBundle/Smarty/'

    # Cache max ages for API
    cache_api_catalog_products_id: 300
    cache_api_catalog_product_reviews: 900
    cache_api_search_products: 300
    cache_api_catalog_category_tree: 900
    cache_api_catalog_category_list: 900
    cache_api_cms_menu_list: 900
    cache_api_banners_get: 900
    cache_api_banners_get_category: 900
    cache_api_divisions: 900
    cache_api_declination: 300
