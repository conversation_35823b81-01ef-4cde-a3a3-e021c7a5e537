# Retourne le schema de la documentation de l'API pour Swagger
api_documentation_schema:
    path: /v1/doc/schema.{_format}
    defaults: { _controller: AppBundle:Documentation:schema, applicationTokenRequired: false, _format: yaml }
    requirements:
        _format:  yaml|json
    methods: [GET]

api_documentation_index:
    path: /v1/doc/
    defaults: { _controller: AppBundle:Documentation:index, applicationTokenRequired: false }
    methods: [GET]

#-----
# Ping
#-----
api_ping:
    path: /v1/ping
    defaults: { _controller: AppBundle:Api/Ping:ping, applicationTokenRequired: false }

#-------
# Users
#-------
api_user_authenticate:
    path: /v1/users/authenticate
    defaults: { _controller: marketplace.user.api.usercontroller:authenticateAction }
    methods: [GET]

api_user_oauth_token:
    path: /v1/user/oauth-token
    defaults: { _controller: AppBundle:Api/OAuth:token }
    methods: [POST]

api_user_oauth_authorize_url:
    path: /v1/user/oauth/authorize-url
    defaults: { _controller: AppBundle:Api/OAuth:authorizeUrl }
    methods: [GET]

api_user_oauth_admin_authorize_url:
    path: /v1/user/oauth/admin-authorize-url
    defaults: { _controller: AppBundle:Api/OAuth:adminAuthorizeUrl }
    methods: [GET]

api_user_oauth_logout:
    path: /v1/user/oauth/logout
    defaults: { _controller: AppBundle:Api/OAuth:logout }
    methods: [GET]

api_user_revoke:
    path: /v1/user/revoke
    defaults: { _controller: marketplace.user.api.usercontroller:revokeAction }
    methods: [POST]

api_user_register:
    path: /v1/users
    defaults: { _controller: marketplace.user.api.usercontroller:registerAction }
    methods: [POST]

api_user_list:
    path: /v1/users
    defaults: { _controller: marketplace.user.api.usercontroller:listAction }
    methods: [GET]

api_user:
    path: /v1/users/{userId}
    defaults: { _controller: marketplace.user.api.usercontroller:getAction }
    methods: [GET]
    requirements:
        userId: \d+

api_user_update:
    path: /v1/users/{userId}
    defaults: { _controller: marketplace.user.api.usercontroller:updateAction }
    methods: [PUT]
    requirements:
        userId: \d+

api_user_patch:
    path: /v1/users/{userId}
    defaults: { _controller: marketplace.user.api.usercontroller:patchAction }
    methods: [PATCH]
    requirements:
        userId: \d+

api_user_recover_password:
    path: /v1/users/password/recover
    defaults: { _controller: marketplace.user.api.usercontroller:recoverPasswordAction }
    methods: [POST]

api_user_change_password_with_token:
    path: /v1/users/password/change-with-token
    defaults: { _controller: marketplace.user.api.usercontroller:changePasswordWithTokenAction }
    methods: [PUT]

api_user_change_password:
    path: /v1/users/{userId}/password
    defaults: { _controller: marketplace.user.api.usercontroller:changePasswordAction }
    methods: [PUT]
    requirements:
        userId: \d+

api_user_addresses_update:
    path: /v1/users/{userId}/addresses
    defaults: { _controller: marketplace.user.api.usercontroller:updateAddressesAction }
    methods: [PUT]
    requirements:
        userId: \d+

api_user_delete_extra:
    path: /v1/users/{userId}/extra
    defaults: { _controller: marketplace.user.api.usercontroller:deleteExtraAction }
    methods: [DELETE]
    requirements:
        userId: \d+

api_user_get_basket:
    path: /v1/users/{userId}/basket
    defaults: { _controller: marketplace.user.api.usercontroller:getBasketAction }
    methods: [GET]
    requirements:
        userId: \d+

api_user_post_basket:
    path: /v1/users/{userId}/basket
    defaults: { _controller: marketplace.user.api.usercontroller:postBasketAction }
    methods: [POST]
    requirements:
        userId: \d+

api_user_delete_basket:
    path: /v1/users/{userId}/basket
    defaults: { _controller: marketplace.user.api.usercontroller:deleteBasketAction }
    methods: [DELETE]
    requirements:
        userId: \d+

api_user_enable:
    path: /v1/users/{userId}/enable
    defaults: { _controller: marketplace.user.api.usercontroller:enableAction }
    methods: [POST]
    requirements:
        userId: \d+

api_user_disable:
    path: /v1/users/{userId}/disable
    defaults: { _controller: marketplace.user.api.usercontroller:disableAction }
    methods: [POST]
    requirements:
        userId: \d+

api_user_get_organisation:
    path: /v1/users/{userId}/organisation
    defaults: { _controller: marketplace.user.api.usercontroller:getOrganisationAction }
    methods: [GET]
    requirements:
        userId: \d+

api_user_get_subscriptions:
    path: /v1/users/{userId}/subscriptions
    defaults: { _controller: marketplace.user.api.usercontroller:getSubscriptionsAction }
    methods: [GET]
    requirements:
        userId: \d+

api_add_address_to_address_book:
    path: /v1/users/{userId}/address-book/addresses
    defaults: { _controller: marketplace.user.api.usercontroller:postAddressInAddressBookAction }
    methods: [POST]
    requirements:
        userId: \d+

api_addressBook_list:
    path: /v1/users/{userId}/address-book/addresses
    defaults: { _controller: marketplace.user.api.usercontroller:ListAddressBooksAction }
    methods: [GET]
    requirements:
        userId: \d+

api_addressBook_put:
    path: /v1/users/{userId}/address-book/addresses/{addressId}
    defaults: { _controller: marketplace.user.api.usercontroller:ReplaceAddressBooksAction }
    methods: [PUT]
    requirements:
        userId: \d+

api_remove_addressBook:
    path: /v1/users/{userId}/address-book/addresses/{addressId}
    defaults: { _controller: marketplace.user.api.usercontroller:removeAddressBookAction }
    methods: [DELETE]
    requirements:
        userId: \d+

api_affiliate_user_to_company:
    path: /v1/users/set-vendor
    defaults: { _controller: marketplace.user.api.usercontroller:affiliateUserToCompanyAction }
    methods: [POST]

#------------
# Newsletter
#------------
api_mailinglist_get:
    path:   /v1/mailinglists
    defaults:  { _controller: AppBundle:Api\MailingList:list }
    methods: [GET]

api_mailinglist_post_subscriptions:
    path:   /v1/mailinglists/{id}/subscriptions/{email}
    defaults:  { _controller: AppBundle:Api\MailingList:postSubscription }
    methods: [POST]
    requirements:
        id: \d+

api_mailinglist_delete_subscriptions:
    path:   /v1/mailinglists/{id}/subscriptions/{email}
    defaults:  { _controller: AppBundle:Api\MailingList:deleteSubscription }
    methods: [DELETE]
    requirements:
        id: \d+

api_mailinglist_get_my_subscription:
    path:   /v1/mailinglists/{id}/subscription
    defaults:  { _controller: AppBundle:Api\MailingList:getSubscription }
    methods: [GET]
    requirements:
        id: \d+
#-----------
# Recherche
#-----------
api_search_products:
    path: /v1/catalog/search/products
    defaults:
        _controller: AppBundle:Api/Catalog/ProductSearch:search
        applicationTokenRequired: '%feature.application_token_for_public_routes%'

api_search_products_autocomplete:
    path: /v1/catalog/search/products/autocomplete
    defaults:
        _controller: AppBundle:Api/Catalog/ProductSearch:autocomplete
        applicationTokenRequired: '%feature.application_token_for_public_routes%'

#-----------
# Catalogue
#-----------
api_catalog_products_id:
    path:   /v1/catalog/products/{id}
    defaults:  { _controller: AppBundle:Api/Catalog/Product:getId }
    requirements:
        id: "(\\d+|%regexp_guid%)"

api_catalog_products:
    path:   /v1/catalog/products
    defaults:  { _controller: AppBundle:Api/Catalog/Product:get }

api_catalog_product_declination:
    path:   /v1/catalog/declinations/{declinationId}
    defaults: { _controller: AppBundle:Api/Catalog/Product:getDeclination }

api_catalog_product_attachments:
    path:   /v1/catalog/products/attachments/{id}
    defaults:  { _controller: AppBundle:Api/Catalog/Product:getAttachment, applicationTokenRequired: false }

api_catalog_product_reviews:
    path:   /v1/catalog/products/{productId}/reviews
    defaults:  { _controller: AppBundle:Api/Catalog/CustomerReview:getReviews }
    methods: [GET]

api_catalog_product_review_add_authorization:
    path: /v1/catalog/products/{productId}/reviews/authorized
    defaults: { _controller: AppBundle:Api/Catalog/CustomerReview:canAddReview }
    methods: [GET]

api_catalog_product_review_add:
    path:   /v1/catalog/products/{productId}/reviews
    defaults:  { _controller: AppBundle:Api/Catalog/CustomerReview:addReview }
    methods: [POST]

api_catalog_product_report:
    path:   /v1/catalog/products/{id}/report
    defaults:  { _controller: AppBundle:Api/Catalog/Product:report }
    methods: [POST]

api_catalog_company_list:
    path:   /v1/catalog/companies
    defaults:  { _controller: AppBundle:Api/Catalog/Company:list }

api_catalog_company:
    path:   /v1/catalog/companies/{id}
    defaults:  { _controller: AppBundle:Api/Catalog/Company:get }
    requirements:
        id: \d+

api_catalog_category_list:
    path:   /v1/catalog/categories
    defaults:  { _controller: AppBundle:Api/Catalog/Category:list }

api_catalog_category_tree:
    path:   /v1/catalog/categories/tree
    defaults:  { _controller: AppBundle:Api/Catalog/Category:tree }

api_catalog_category:
    path:   /v1/catalog/categories/{id}
    defaults:  { _controller: AppBundle:Api/Catalog/Category:get }
    requirements:
        id: \d+

api_catalog_attributes_list:
    path:   /v1/catalog/attributes
    defaults:  { _controller: AppBundle:Api/Catalog/Attribute:list }
api_catalog_attribute:
    path:   /v1/catalog/attributes/{id}
    defaults:  { _controller: AppBundle:Api/Catalog/Attribute:get }
    requirements:
        id: \d+
api_catalog_attribute_variants:
    path:   /v1/catalog/attributes/{id}/variants
    defaults:  { _controller: AppBundle:Api/Catalog/Attribute:getVariants }
    requirements:
        id: \d+
api_catalog_attribute_variant:
    path:   /v1/catalog/attributes/variants/{id}
    defaults:  { _controller: AppBundle:Api/Catalog/Attribute:getVariant }
    requirements:
        id: \d+

api_catalog_export:
    path:   /v1/catalog/export/{page}
    defaults: { _controller: AppBundle:Api/Catalog/Export:getFile, page: 1 }

#-----
# CMS
#-----
api_cms_menu_list:
    path: /v1/cms/menus
    defaults: { _controller: AppBundle:Api/Cms/Menu:list }

#--------------
# Products
#--------------
api_product_get_name:
    path:   /v1/products/name
    defaults:  { _controller: marketplace.product.api.productcontroller:productNameAction }

api_get_product_divisions:
    path: /v1/products/{productId}/divisions
    defaults: { _controller: marketplace.product.api.productcontroller:getDivisionsAction }
    methods: [GET]
    requirements:
        productId: \d+

api_get_product_divisions_tree:
    path: /v1/products/{productId}/divisions-tree
    defaults: { _controller: marketplace.product.api.productcontroller:getDivisionsTreeAction }
    methods: [GET]
    requirements:
        productId: \d+

api_get_product_divisions_by_country:
    path: /v1/products/{productId}/divisions/{countryCode}
    defaults: { _controller: marketplace.product.api.productcontroller:getDivisionsAction }
    methods: [GET]
    requirements:
        companyId: \d+

api_put_product_divisions:
    path: /v1/products/{productId}/divisions
    defaults: { _controller: marketplace.product.api.productcontroller:putDivisionsAction }
    methods: [PUT]
    requirements:
        productId: \d+

api_product_attachment_add:
    path: /v1/pim/products/{productId}/attachments
    defaults: { _controller: marketplace.product.api.productcontroller:addAttachmentsAction }
    methods: [POST]

api_product_attachment_delete:
    path:   /v1/pim/products/{productId}/attachments/{attachmentId}
    defaults:  { _controller: marketplace.product.api.productcontroller:deleteAttachmentAction }
    methods: [DELETE]

api_product_video_post:
    path: /v1/products/{productId}/video
    defaults: { _controller: marketplace.product.api.productcontroller:addVideoAction}
    methods: [POST]

api_product_video_delete:
    path: /v1/products/{productId}/video
    defaults: { _controller: marketplace.product.api.productcontroller:removeVideoAction}
    methods: [DELETE]

api_product_stock_put:
    path: /v1/pim/products/{ean}/stocks
    defaults: { _controller: marketplace.product.api.productcontroller:updateStockAction }
    methods: [PUT]

#--------
# Basket
#--------
api_basket_create:
    path: /v1/basket
    defaults: { _controller: AppBundle:Api/Basket:create }
    methods: [POST]

api_basket_get:
    path: /v1/basket/{id}
    defaults: { _controller: AppBundle:Api/Basket:get }
    methods: [GET]

api_basket_items:
    path: /v1/basket/{id}/items
    defaults: { _controller: AppBundle:Api/Basket:items}
    methods: [GET]

api_add_product_to_basket:
    path: /v1/basket/{id}/add
    defaults: { _controller: AppBundle:Api/Basket:add }
    methods: [POST]

api_bulk_add_product_to_basket:
    path: /v1/basket/{id}/bulk-add
    defaults: { _controller: AppBundle:Api/Basket:bulkAdd }
    methods: [POST]

api_remove_product_to_basket:
    path: /v1/basket/{id}/remove
    defaults: { _controller: AppBundle:Api/Basket:remove }
    methods: [POST]

api_bulk_remove_products_to_basket:
    path: /v1/basket/{id}/bulk-remove
    defaults: { _controller: AppBundle:Api/Basket:bulkRemove }
    methods: [POST]

api_change_product_in_basket:
    path: /v1/basket/{id}/modify
    defaults: { _controller: AppBundle:Api/Basket:changeQuantity }
    methods: [POST]

api_merge_basket:
    path: /v1/basket/{id}/merge
    defaults: { _controller: AppBundle:Api/Basket:merge }
    methods: [POST]

api_basket_payments:
    path: /v1/basket/{id}/payments
    defaults: { _controller: AppBundle:Api/Basket:getPayments }

api_basket_coupons_add:
    path: /v1/basket/{id}/coupons/{coupon}
    defaults: { _controller: AppBundle:Api/Basket:addCoupon }
    methods: [POST]

api_basket_coupons_remove:
    path: /v1/basket/{id}/coupons/{coupon}
    defaults: { _controller: AppBundle:Api/Basket:removeCoupon }
    methods: [DELETE]

api_add_comment_to_basket:
    path: /v1/basket/{id}/comments
    defaults: { _controller: AppBundle:Api/Basket:setComments }
    methods: [POST]

api_basket_set_shippings:
    path: /v1/basket/{basketId}/shippings
    defaults: { _controller: AppBundle:Api/Basket:setShippings }
    requirements:
        basketId: "%regexp_guid%"

api_basket_set_chronorelais_pickup_point:
    path: /v1/basket/{basketId}/chronorelais-pickup-point
    defaults: { _controller: AppBundle:Api/Basket:setChronoRelaisPickupPoint }
    methods: [POST]
    requirements:
        basketId: "%regexp_guid%"

api_basket_shippings-price_update:
    path: /v1/basket/{basketId}/shipping-price
    defaults: { _controller: AppBundle:Api/Basket:customShippingsPriceUpdate }
    methods: [POST]
    requirements:
        basketId: "%regexp_guid%"

api_basket_shippings-price_reset:
    path: /v1/basket/{basketId}/shipping-price
    defaults: { _controller: AppBundle:Api/Basket:customShippingsPriceReset }
    methods: [DELETE]
    requirements:
        basketId: "%regexp_guid%"

api_basket_set_mondialrelay_pickup_point:
    path: /v1/basket/{basketId}/mondialrelay-pickup-point
    defaults: { _controller: AppBundle:Api/Basket:setMondialRelayPickupPoint }
    methods: [POST]
    requirements:
        basketId: "%regexp_guid%"

api_basket_order:
    path: /v1/basket/{id}/order
    defaults: { _controller: AppBundle:Api/Basket:order }
    methods: [POST]

api_Choose_shipping_address_for_basket:
    path: /v1/basket/{basketId}/choose-shipping-address
    defaults: { _controller: AppBundle:Api/Basket:chooseShippingAddress }
    methods: [POST]
    requirements:
        basketId: "%regexp_guid%"

api_Choose_billing_address_for_basket:
    path: /v1/basket/{basketId}/choose-billing-address
    defaults: { _controller: AppBundle:Api/Basket:chooseBillingAddress }
    methods: [POST]
    requirements:
        basketId: "%regexp_guid%"

#-----------------
# Mark as delivered a shipment
#-----------------
api_put_mark_a_shipment_as_delivered:
    path: /v1/shipments/{shipmentId}/mark-as-delivered
    defaults: { _controller: AppBundle:Api\Shipments:putMarkShipmentAsDelivered}
    methods: [PUT]
    requirements:
        orderId: \d+

#-------------
# User orders
#-------------
api_orders_list:
    path: /v1/user/orders
    defaults: { _controller: AppBundle:Api/Order:list }
    methods: [GET]

api_orders_get:
    path: /v1/user/orders/{id}
    defaults: { _controller: AppBundle:Api/Order:get }
    methods: [GET]
    requirements:
        id: \d+

api_orders_get_shipments:
    path: /v1/user/orders/{id}/shipments
    defaults: { _controller: AppBundle:Api/Order:getShipments }
    methods: [GET]
    requirements:
        id: \d+

api_get_order_shipments:
    path: /v1/orders/{id}/shipments
    defaults: { _controller: AppBundle:Api/Order:getShipmentsByOrderId }
    methods: [GET]
    requirements:
        id: \d+

api_user_orders_get_pdf_invoice:
    path: /v1/user/orders/{id}/pdf-invoice
    defaults: { _controller: AppBundle:Api/Order:getUserPDFInvoice }
    methods: [GET]
    requirements:
        id: \d+

api_orders_commit_to:
    path: /v1/user/orders/{id}/commitment
    defaults: { _controller: AppBundle:Api/Order:commitTo }
    methods: [POST]
    requirements:
        id: \d+

api_orders_get_progress:
    path: /v1/user/orders/{id}/progress
    defaults: { _controller: AppBundle:Api/Order:getProgress }
    methods: [GET]
    requirements:
        id: \d+

api_orders_get_subscriptions:
    path: /v1/user/orders/{id}/subscriptions
    defaults: { _controller: AppBundle:Api/Order:getUserSubscriptions }
    methods: [GET]
    requirements:
        id: \d+

#---------
# Banners
#---------
api_banners_get:
    path: /v1/cms/banners
    defaults: { _controller: AppBundle:Api/Banner:get }

api_banners_get_category:
    path: /v1/cms/banners/category/{id}
    defaults: { _controller: AppBundle:Api/Banner:getCategory }
    requirements:
        id: '\d+'

#-----------
# Favorites
#-----------
api_favorite_declination:
    path: /v1/user/favorites/declinations
    defaults: { _controller: AppBundle:Api/Favorite/Declination:listAll }
    methods: [GET]

api_favorite_declination_ids:
    path: /v1/user/favorites/declinations/ids
    defaults: { _controller: AppBundle:Api/Favorite/Declination:listAllIds }
    methods: [GET]

api_favorite_declination_add:
    path: /v1/user/favorites/declinations/{id}
    defaults: { _controller: AppBundle:Api/Favorite/Declination:addToFavorite }
    methods: [POST]

api_favorite_declination_remove:
    path: /v1/user/favorites/declinations/{id}
    defaults: { _controller: AppBundle:Api/Favorite/Declination:removeFromFavorite }
    methods: [DELETE]

#------------
# Catalog Promotions
#------------
api_catalog_promotions_create:
    path: /v1/promotions/catalog
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\CatalogPromotionsController::upsertAction }
    methods: [POST]
api_catalog_promotions_update:
    path: /v1/promotions/catalog/{promotionId}
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\CatalogPromotionsController::upsertAction }
    methods: [PUT]
api_catalog_promotions_delete:
    path: /v1/promotions/catalog/{promotionId}
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\CatalogPromotionsController::deleteAction }
    methods: [DELETE]
api_catalog_promotions_get:
    path: /v1/promotions/catalog/{promotionId}
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\CatalogPromotionsController::getAction }
    methods: [GET]
api_catalog_promotions_list:
    path: /v1/promotions/catalog
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\CatalogPromotionsController::listForCompanyAction }
    methods: [GET]

#------------
# Marketplace Promotions
#------------
api_marketplace_promotions_get:
    path: /v1/promotions/marketplace/{promotionId}
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\MarketplacePromotionsController::getAction }
    methods: [GET]

api_marketplace_promotions_create:
    path: /v1/promotions/marketplace
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\MarketplacePromotionsController::upsertAction }
    methods: [POST]

api_marketplace_promotions_update:
    path: /v1/promotions/marketplace/{promotionId}
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\MarketplacePromotionsController::upsertAction }
    methods: [PATCH]

api_marketplace_promotions_list:
    path: /v1/promotions/marketplace
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\MarketplacePromotionsController::listAction }
    methods: [GET]

#------------
# Basket Promotions
#------------
api_basket_promotions_create:
    path: /v1/promotions/basket
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\BasketPromotionsController::upsertAction }
    methods: [POST]
api_basket_promotions_update:
    path: /v1/promotions/basket/{promotionId}
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\BasketPromotionsController::upsertAction }
    methods: [PUT]
api_basket_promotions_delete:
    path: /v1/promotions/basket/{promotionId}
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\BasketPromotionsController::deleteAction }
    methods: [DELETE]
api_basket_promotions_get:
    path: /v1/promotions/basket/{promotionId}
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\BasketPromotionsController::getAction }
    methods: [GET]
api_basket_promotions_list:
    path: /v1/promotions/basket
    defaults: { _controller: Wizacha\AppBundle\Controller\Api\Promotion\BasketPromotionsController::listForCompanyAction }
    methods: [GET]

#------------
# Legacy Promotions
#------------
api_promotions_list:
    path: /v1/promotions
    defaults: { _controller: marketplace.promotion.api.promotioncontroller:getPromotionsAction }
    methods: [GET]

api_promotions_get:
    path: /v1/promotions/{id}
    defaults: { _controller: marketplace.promotion.api.promotioncontroller:getPromotionAction }
    methods: [GET]
    requirements:
        id: "%regexp_guid%"

api_promotions_post:
    path: /v1/promotions
    defaults: { _controller: marketplace.promotion.api.promotioncontroller:postPromotionAction }
    methods: [POST]

api_promotions_put:
    path: /v1/promotions/{id}
    defaults: { _controller: marketplace.promotion.api.promotioncontroller:putPromotionAction }
    methods: [PUT]
    requirements:
        id: "%regexp_guid%"

api_promotions_delete:
    path: /v1/promotions/{id}
    defaults: { _controller: marketplace.promotion.api.promotioncontroller:deletePromotionAction }
    methods: [DELETE]
    requirements:
        id: "%regexp_guid%"

#----------
# Prediggo
#----------
api_prediggo_products:
    path:   /v1/prediggo/products.xml
    defaults:  { _controller: AppBundle:Api/Prediggo/Prediggo:products }

api_prediggo_users:
    path:   /v1/prediggo/users.xml
    defaults:  { _controller: AppBundle:Api/Prediggo/Prediggo:users }

api_prediggo_transactions:
    path:   /v1/prediggo/transactions.xml
    defaults:  { _controller: AppBundle:Api/Prediggo/Prediggo:transactions }

api_prediggo_cmsitems:
    path:   /v1/prediggo/CMSItems.xml
    defaults:  { _controller: AppBundle:Api/Prediggo/Prediggo:cmsItems }

api_prediggo_hierarchy:
    path:   /v1/prediggo/Hierarchy.csv
    defaults:  { _controller: AppBundle:Api/Prediggo/Prediggo:hierarchy }

api_prediggo_attributes_translations:
    path:   /v1/prediggo/AttributesTranslations.csv
    defaults:  { _controller: AppBundle:Api/Prediggo/Prediggo:attributesTranslations }

api_prediggo_values_translations:
    path:   /v1/prediggo/ValuesTranslations.csv
    defaults:  { _controller: AppBundle:Api/Prediggo/Prediggo:valuesTranslations }

api_prediggo_integrity:
    path:   /v1/prediggo/integrity.txt
    defaults:  { _controller: AppBundle:Api/Prediggo/Prediggo:integrity }

#--------------------------
# Multi vendor products API
#--------------------------
api_multi_vendor_product_create:
    path: /v1/pim/multi-vendor-products
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:createAction }
    methods: [POST]

api_multi_vendor_product_read:
    path: /v1/pim/multi-vendor-products/{id}
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:readAction }
    methods: [GET]
    requirements:
        id: "%regexp_guid%"

api_multi_vendor_product_update:
    path: /v1/pim/multi-vendor-products/{id}
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:updateAction }
    methods: [PUT]
    requirements:
        id: "%regexp_guid%"

api_multi_vendor_product_patch:
    path: /v1/pim/multi-vendor-products/{id}
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:patchAction }
    methods: [PATCH]
    requirements:
        id: "%regexp_guid%"

api_multi_vendor_product_delete:
    path: /v1/pim/multi-vendor-products/{id}
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:deleteAction }
    methods: [DELETE]
    requirements:
        id: "%regexp_guid%"

api_multi_vendor_products_list:
    path: /v1/pim/multi-vendor-products
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:listAction }
    methods: [GET]

api_multi_vendor_product_attach_product:
    path: /v1/pim/multi-vendor-products/{id}/links
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:attachProductAction }
    methods: [POST]
    requirements:
        id: "%regexp_guid%"

api_multi_vendor_product_detach_product:
    path: /v1/pim/multi-vendor-products/{id}/links/{productId}
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:detachProductAction }
    methods: [DELETE]
    requirements:
        id: "%regexp_guid%"
        productId: '\d+'

api_multi_vendor_product_list_links:
    path: /v1/pim/multi-vendor-products/{id}/links
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:listLinksAction }
    methods: [GET]
    requirements:
        id: "%regexp_guid%"

api_multi_vendor_product_add_image:
    path: /v1/pim/multi-vendor-products/{id}/images
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:addImageAction }
    methods: [POST]
    requirements:
        id: "%regexp_guid%"

api_multi_vendor_product_delete_image:
    path: /v1/pim/multi-vendor-products/{id}/images/{imageId}
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:deleteImageAction }
    methods: [DELETE]
    requirements:
        id: "%regexp_guid%"
        imageId: '\d+'

api_multi_vendor_product_add_video:
    path: /v1/pim/multi-vendor-products/{id}/video
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:addVideoAction }
    methods: [POST]
    requirements:
        id: "%regexp_guid%"

api_multi_vendor_product_delete_video:
    path: /v1/pim/multi-vendor-products/{id}/video
    defaults: { _controller: marketplace.multi_vendor_product.api.controller:deleteVideoAction }
    methods: [DELETE]
    requirements:
        id: "%regexp_guid%"

#-----------
# Images API
#-----------
api_image_get:
    path: /v1/image/{id}
    defaults: { _controller: AppBundle:Api/Image:get, applicationTokenRequired: false }
    methods: [GET]
    requirements:
        id: '\d+'
    options:
        expose: true

#------------
# Reviews API
#------------
api_review_create:
    path: /v1/catalog/companies/{companyId}/reviews
    defaults: { _controller: marketplace.review.api.controller:createFromCompanyAction }
    methods: [POST]
    requirements:
        companyId: '\d+'

api_reviews_list:
    path: /v1/catalog/companies/{companyId}/reviews
    defaults: { _controller: marketplace.review.api.controller:listFromCompanyAction }
    methods: [GET]
    requirements:
        companyId: '\d+'

api_reviews_get_user_company_review_authorization:
    path: /v1/catalog/companies/{companyId}/reviews/authorized
    defaults: { _controller: marketplace.review.api.controller:getUserAuthorizationToReviewCompany }
    methods: [GET]
    requirements:
        companyId: '\d+'
        userId: '\d+'

api_seo_get_slugs:
    path: /v1/seo/slugs
    defaults: { _controller: AppBundle:Api/Seo:resolve }
    methods: [GET]

api_seo_get_slugs_catalog:
    path: /v1/seo/slugs/catalog
    defaults: { _controller: AppBundle:Api/Seo:catalog }
    methods: [GET]

api_seo_get_slugs_list:
    path: /v1/seo/slugs/list
    defaults: { _controller: AppBundle:Api/Seo:catalogList }
    methods: [GET]

#------------
# Statistics
#------------
api_statistics:
    path: /v1/statistics/%route_statistics_token%
    defaults: { _controller: AppBundle:Api/Statistics:get }
    methods: [GET]

#----------------
# OrderReturn API
#----------------
api_return_list:
    path: /v1/user/orders/returns
    defaults: { _controller: AppBundle:Api/OrderReturn:getByUser }
    methods: [GET]
api_return_get:
    path: /v1/user/orders/returns/{returnId}
    defaults: { _controller: AppBundle:Api/OrderReturn:get }
    methods: [GET]
    requirements:
        id: \d+
api_create_return:
    path: /v1/user/orders/{orderId}/returns
    defaults: { _controller: AppBundle:Api/OrderReturn:create }
    methods: [POST]
    requirements:
        id: \d+
api_returns_get:
    path: /v1/orders/returns/reasons
    defaults: { _controller: AppBundle:Api/OrderReturn:getReasons }
    methods: [GET]

#----------------
# SAV / Litige API
#----------------
api_after_sales_create:
    path: /v1/user/orders/{orderId}/after-sales
    defaults: { _controller: AppBundle:Api\OrderAfterSales:create }
    methods: [POST]
    requirements:
        orderId: \d+

#--------------
# Chrono Relais
#--------------
api_chrono_relais:
    path: /v1/chronopost/points-relais
    defaults: { _controller: AppBundle:Api/Chronopost:listPointsRelais, applicationTokenRequired: false }
    methods: [GET]

#--------------
# Mondial Relay
#--------------
api_mondial_relay_point_list:
    path: /v1/mondial-relay/points-relais
    defaults: { _controller: AppBundle:Api/MondialRelay:listPickupPoints, applicationTokenRequired: false }
    methods: [GET]

api_mondial_relay_point_get:
    path: /v1/mondial-relay/points-relais/{pickupPointId}
    defaults: { _controller: AppBundle:Api/MondialRelay:getPickupPoint }
    methods: [GET]

api_mondial_relay_brand_code_get:
    path: /v1/mondial-relay/brand-code
    defaults: { _controller: AppBundle:Api/MondialRelay:getBrandCode }
    methods: [GET]

#----------------
# CmsPage API
#----------------
api_cms_page_get:
    path: /v1/cms/page/{pageId}
    defaults: { _controller: AppBundle:Api/Cms/Page:view }
    methods: [GET]
    requirements:
        id: \d+

#----------------
# Translation API
#----------------
api_set_front_translations:
    path: /v1/translations/front/{locale}
    defaults: { _controller: AppBundle:Api/Translation:setFrontTranslations }
    methods: [POST]

api_get_front_translations:
    path: /v1/translations/front/{locale}
    defaults: { _controller: AppBundle:Api/Translation:getFrontTranslations }
    methods: [GET]

#---------------
# Moderation API
#---------------
api_moderation_product_update:
    path: /v1/pim/moderation/products/{productId}/{moderationAction}
    defaults: { _controller: marketplace.moderation.api.controller:updateProductAction }
    methods: [PUT]
    requirements:
        productId: \d+
        moderationAction: "(approve|disapprove|standby|pending)"

api_moderation_pending_products_from_company_update:
    path: /v1/pim/moderation/companies/{companyId}/products/{moderationAction}
    defaults: { _controller: marketplace.moderation.api.controller:updatePendingProductsFromCompany }
    methods: [PUT]
    requirements:
        productId: \d+
        moderationAction: "(approve|disapprove|standby)"

#---------------
# Attributes API
#---------------
api_attribute_create:
    path: /v1/pim/attributes
    defaults: { _controller: marketplace.attribute.api.controller:createAction }
    methods: [POST]

api_attribute_read:
    path: /v1/pim/attributes/{id}
    defaults: { _controller: marketplace.attribute.api.controller:readAction }
    methods: [GET]
    requirements:
        id: \d+

api_attribute_update:
    path: /v1/pim/attributes/{id}
    defaults: { _controller: marketplace.attribute.api.controller:updateAction }
    methods: [PUT]
    requirements:
        id: \d+

api_attribute_delete:
    path: /v1/pim/attributes/{id}
    defaults: { _controller: marketplace.attribute.api.controller:deleteAction }
    methods: [DELETE]
    requirements:
        id: \d+

api_attribute_list:
    path: /v1/pim/attributes
    defaults: { _controller: marketplace.attribute.api.controller:listAction }
    methods: [GET]

api_attributes_variants_create:
    path: /v1/pim/attributes/{attributeId}/variants
    defaults: { _controller: marketplace.attribute.api.controller:createVariantAction }
    methods: [POST]
    requirements:
        attributeId: \d+

api_attributes_variants_update:
    path: /v1/pim/attributes/{attributeId}/variants/{id}
    defaults: { _controller: marketplace.attribute.api.controller:updateVariantAction }
    methods: [PUT]
    requirements:
        attributeId: \d+
        id: \d+

api_attributes_variants_delete:
    path: /v1/pim/attributes/{attributeId}/variants/{id}
    defaults: { _controller: marketplace.attribute.api.controller:deleteVariantAction }
    methods: [DELETE]
    requirements:
        attributeId: \d+
        id: \d+

#---------------
# Discussion API
#---------------
api_discussion_list:
    path: /v1/discussions
    defaults: { _controller: AppBundle:Api/Discussion:list }
    methods: [GET]

api_discussion_create:
    path: /v1/discussions
    defaults: { _controller: AppBundle:Api/Discussion:create }
    methods: [POST]

api_discussion_get:
    path: /v1/discussions/{id}
    defaults: { _controller: AppBundle:Api/Discussion:get }
    methods: [GET]
    requirements:
        id: \d+

api_discussion_get_messages:
    path: /v1/discussions/{id}/messages
    defaults: { _controller: AppBundle:Api/Discussion:getMessages }
    methods: [GET]
    requirements:
        id: \d+

api_discussion_post_message:
    path: /v1/discussions/{id}/messages
    defaults: { _controller: AppBundle:Api/Discussion:postMessage }
    methods: [POST]
    requirements:
        id: \d+

api_message_attachment_download:
    path: /v1/discussions/attachments/{attachmentId}
    defaults: {_controller: AppBundle:Api/Discussion:downloadMessageAttachment}
    methods: [GET]
    requirements:
        attachmentId: "%regexp_guid%"

api_contact_request:
    path: /v1/contact-request
    defaults: { _controller: AppBundle:Api/Contact:postContactRequest }
    methods: [POST]

#---------------
# Company API
#---------------
api_register_company:
    path: /v1/companies
    defaults: { _controller: AppBundle:Api/Company:register }
    methods: [POST]

api_register_c2c_company:
    path: /v1/companies/c2c
    defaults: { _controller: AppBundle:Api/Company:registerC2C }
    methods: [POST]

api_update_company:
    path: /v1/companies/{companyId}
    defaults: { _controller: AppBundle:Api/Company:update }
    methods: [PUT]
    requirements:
        companyId: \d+

api_patch_company:
    path: /v1/companies/{companyId}
    defaults: { _controller: AppBundle:Api/Company:patch }
    methods: [PATCH]
    requirements:
        companyId: \d+

api_upload_company_registration_files:
    path: /v1/companies/{companyId}/files
    defaults: { _controller: AppBundle:Api/Company:uploadRegistrationFiles }
    methods: [POST]
    requirements:
        companyId: \d+

api_get_company_registration_files_list:
    path: /v1/companies/{companyId}/files
    defaults: { _controller: AppBundle:Api/Company:getRegistrationFilesList }
    methods: [GET]
    requirements:
        companyId: \d+

api_get_company_registration_file:
    path: /v1/companies/{companyId}/files/{filename}
    defaults: { _controller: AppBundle:Api/Company:getRegistrationFile }
    methods: [GET]
    requirements:
        companyId: \d+
        filename: \w+

api_update_company_registration_file:
    path: /v1/companies/{companyId}/files/{filename}
    defaults: { _controller: AppBundle:Api/Company:updateRegistrationFile }
    methods: [POST]
    requirements:
        companyId: \d+
        filename: \w+

api_delete_company_registration_file:
    path: /v1/companies/{companyId}/files/{filename}
    defaults: { _controller: AppBundle:Api/Company:deleteRegistrationFile }
    methods: [DELETE]
    requirements:
        companyId: \d+
        filename: \w+

api_get_company:
    path: /v1/companies/{companyId}
    defaults: { _controller: AppBundle:Api/Company:get }
    methods: [GET]
    requirements:
        companyId: \d+

api_get_company_divisions:
    path: /v1/companies/{companyId}/divisions
    defaults: { _controller: AppBundle:Api/Company:getDivisions }
    methods: [GET]
    requirements:
        companyId: \d+

api_get_company_divisions_tree:
    path: /v1/companies/{companyId}/divisions-tree
    defaults: { _controller: AppBundle:Api/Company:getDivisionsTree }
    methods: [GET]
    requirements:
        companyId: \d+

api_get_company_divisions_by_country:
    path: /v1/companies/{companyId}/divisions/{countryCode}
    defaults: { _controller: AppBundle:Api/Company:getDivisions }
    methods: [GET]
    requirements:
        companyId: \d+

api_put_company_divisions:
    path: /v1/companies/{companyId}/divisions
    defaults: { _controller: AppBundle:Api/Company:putDivisions }
    methods: [PUT]
    requirements:
        companyId: \d+

api_get_company_image:
    path: /v1/companies/{companyId}/image
    defaults: { _controller: AppBundle:Api/Company:getCompanyImage }
    methods: [GET]

api_add_company_image:
    path: /v1/companies/{companyId}/image
    defaults: { _controller: AppBundle:Api/Company:addCompanyImage }
    methods: [POST]

api_delete_company_image:
    path: /v1/companies/{companyId}/image/{imageId}
    defaults: { _controller: AppBundle:Api/Company:deleteCompanyImage }
    methods: [DELETE]

api_company_get_subscriptions:
    path: /v1/companies/{companyId}/subscriptions
    defaults: { _controller: AppBundle:Api/Company:getSubscriptions }
    methods: [GET]
    requirements:
        companyId: \d+

api_company_add_company_person:
    path: /v1/companies/{companyId}/persons
    defaults: {_controller: AppBundle:Api/CompanyPerson/CompanyPerson:addCompanyPerson}
    methods: [ POST ]
    requirements:
        companyId: \d+

api_company_update_company_person:
    path: /v1/companies/{companyId}/persons/{personId}
    defaults: {_controller: AppBundle:Api/CompanyPerson/CompanyPerson:updateCompanyPerson}
    methods: [ PUT ]
    requirements:
        companyId: \d+
        personId: \d+

api_company_get_company_person_list:
    path: /v1/companies/{companyId}/persons
    defaults: {_controller: AppBundle:Api/CompanyPerson/CompanyPerson:getCompanyPersonList}
    methods: [ GET ]
    requirements:
        companyId: \d+

api_company_delete_company_person:
    path: /v1/companies/{companyId}/persons/{personId}
    defaults: {_controller: AppBundle:Api/CompanyPerson/CompanyPerson:deleteCompanyPerson}
    methods: [ DELETE ]
    requirements:
        companyId: \d+
        personId: \d+

api_company_validate_ubo:
    path: /v1/companies/{companyId}/validate-ubo
    defaults: {_controller: AppBundle:Api/CompanyPerson/CompanyPerson:submitUBO}
    methods: [ POST ]
    requirements:
        companyId: \d+

api_company_get_balance:
    path: /v1/companies/{companyId}/balance
    defaults: { _controller: AppBundle:Api/Company:getCompanyBalance }
    methods: [GET]
    requirements:
        companyId: \d+

api_company_payout:
    path: /v1/companies/{companyId}/payout
    defaults: { _controller: AppBundle:Api/Company:companyPayout }
    methods: [POST]
    requirements:
        companyId: \d+

#--------------
# Organisations
#--------------
api_organisation_registration:
    path: /v1/organisations/registrations
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:registration }
    methods: [POST]

api_organisation_get:
    path: /v1/organisations/{organisationId}
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:get }
    methods: [GET]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_list:
    path: /v1/organisations
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:list }
    methods: [GET]

api_organisation_update:
    path: /v1/organisations/{organisationId}
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:update }
    methods: [PUT]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_add_new_user:
    path: /v1/organisations/{organisationId}/users
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:addNewUser }
    methods: [POST]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_get_users:
    path: /v1/organisations/{organisationId}/users
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:listUsers }
    methods: [GET]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_update_addresses:
    path: /v1/organisations/{organisationId}/addresses
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:updateAddresses }
    methods: [PUT]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_approval:
    path: /v1/organisations/{organisationId}/approval
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:approve }
    methods: [POST]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_disapproval:
    path: /v1/organisations/{organisationId}/disapproval
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:disapprove }
    methods: [POST]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_delete:
    path: /v1/organisations/{organisationId}
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:delete }
    methods: [DELETE]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_get_groups:
    path: /v1/organisations/{organisationId}/groups
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:listGroups }
    methods: [GET]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_post_groups:
    path: /v1/organisations/{organisationId}/groups
    defaults: { _controller: AppBundle:Api/Organisation/Organisation:addGroup }
    methods: [POST]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_group_get_users:
    path: /v1/organisations/groups/{groupId}/users
    defaults: { _controller: AppBundle:Api/Organisation/UserGroup:listUsers }
    methods: [GET]
    requirements:
        groupId: "%regexp_guid%"

api_organisation_group_attach_user:
    path: /v1/organisations/groups/{groupId}/users
    defaults: { _controller: AppBundle:Api/Organisation/UserGroup:addUser }
    methods: [POST]
    requirements:
        groupId: "%regexp_guid%"

api_organisation_group_delete_user:
    path: /v1/organisations/groups/{groupId}/users/{userId}
    defaults: { _controller: AppBundle:Api/Organisation/UserGroup:removeUser }
    methods: [DELETE]
    requirements:
        groupId: "%regexp_guid%"

api_organisation_list_baskets:
    path: /v1/organisations/{organisationId}/baskets
    defaults: { _controller: AppBundle:Api/Organisation/OrganisationBasket:list }
    methods: [GET]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_create_basket:
    path: /v1/organisations/{organisationId}/baskets
    defaults: { _controller: AppBundle:Api/Organisation/OrganisationBasket:create }
    methods: [POST]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_lock_basket:
    path: /v1/organisations/{organisationId}/baskets/{basketId}/lock
    defaults: { _controller: AppBundle:Api/Organisation/OrganisationBasket:lock }
    methods: [POST]
    requirements:
        organisationId: "%regexp_guid%"
        basketId: "%regexp_guid%"

api_organisation_validate_basket:
    path: /v1/organisations/{organisationId}/baskets/{basketId}/validation
    defaults: { _controller: AppBundle:Api/Organisation/OrganisationBasket:validate }
    methods: [POST]
    requirements:
        organisationId: "%regexp_guid%"
        basketId: "%regexp_guid%"

api_organisation_checkout_basket:
    path: /v1/organisations/{organisationId}/baskets/{basketId}/order
    defaults: { _controller: AppBundle:Api/Organisation/OrganisationBasket:order }
    methods: [POST]
    requirements:
        organisationId: "%regexp_guid%"
        basketId: "%regexp_guid%"

api_organisation_hide_basket:
    path: /v1/organisations/{organisationId}/baskets/{basketId}/hide
    defaults: { _controller: AppBundle:Api/Organisation/OrganisationBasket:hide }
    methods: [POST]
    requirements:
        organisationId: "%regexp_guid%"
        basketId: "%regexp_guid%"

api_organisation_orders:
    path: /v1/organisations/{organisationId}/orders
    defaults: { _controller: AppBundle:Api/Organisation/OrganisationOrder:list }
    methods: [GET]
    requirements:
        organisationId: "%regexp_guid%"

api_organisation_order:
    path: /v1/organisations/order/{orderId}
    defaults: { _controller: AppBundle:Api/Organisation/OrganisationOrder:get }
    methods: [GET]
    requirements:
        orderId: \d+

#-----------------
# Available offers
#-----------------
api_divisions_get:
    path: /v1/divisions
    defaults: { _controller: AppBundle:Api/Division/Division:get }
    methods: [GET]

api_divisions_get_tree:
    path: /v1/divisions-tree
    defaults: { _controller: AppBundle:Api/Division/Division:getTree }
    methods: [GET]

api_divisions_put:
    path: /v1/divisions
    defaults: { _controller: AppBundle:Api/Division/Division:put }
    methods: [PUT]

#-----------------
# Mark a order as paid
#-----------------
api_admin_put_mark_a_order_as_paid:
    path: /v1/orders/{orderId}/mark-as-paid
    defaults: { _controller: AppBundle:Api/Order:putMarkAsPaid }
    methods: [PUT]
    requirements:
        orderId: \d+

#-----------------
# Mark a order as delivered
#-----------------
api_order_mark_as_delivered:
    path: /v1/orders/{orderId}/mark-as-delivered
    defaults: { _controller: AppBundle:Api/Order:putMarkAsdDelivered }
    methods: [PUT]
    requirements:
        orderId: \d+
#--------------
# Vendor orders
#--------------
api_order_report_hand_delivery:
    path: /v1/orders/{orderId}/handDelivery
    defaults: { _controller: AppBundle:Api/Order/HandDelivery:reportHandDelivery }
    methods: [POST]
    requirements:
        orderId: \d+

api_order_get_hand_delivery_codes:
    path: /v1/orders/{orderId}/handDelivery
    defaults: { _controller: AppBundle:Api/Order/HandDelivery:getHandDeliveryCodeList }
    methods: [GET]
    requirements:
        orderId: \d+

api_order_generate_mondial_relay_label:
    path: /v1/_orders/{orderId}/mondialRelayLabel
    defaults: { _controller: AppBundle:Api/Order/MondialRelay:generateLabel }
    methods: [POST]
    requirements:
        orderId: \d+

api_order_payment_get:
    path: /v1/orders/{id}/payment
    defaults: { _controller: AppBundle:Api/Order:getPayment }
    methods: [GET]
    requirements:
        id: \d+

api_order_details_patch:
    path: /v1/orders/{orderId}/details
    defaults: { _controller: AppBundle:Api/OrderDetails:setDetails }
    methods: [PATCH]
    requirements:
        orderId: \d+

api_orders_adjustments_post:
    path: /v1/orders/{orderId}/adjustments
    defaults: { _controller: AppBundle:Api/OrderAdjustment:adjustPrice }
    methods: [POST]
    requirements:
        id: \d+

api_order_adjustments_get:
    path: /v1/orders/{orderId}/adjustments
    defaults: { _controller: AppBundle:Api/OrderAdjustment:getAdjustments }
    methods: [GET]
    requirements:
        id: \d+

api_admin_orders_get_pdf_invoice:
    path: /v1/orders/{id}/pdf-invoice
    defaults: { _controller: AppBundle:Api/Order:getAdminPDFInvoice }
    methods: [GET]
    requirements:
        id: \d+

api_order_get_subscriptions:
    path: /v1/orders/{id}/subscriptions
    defaults: { _controller: AppBundle:Api/Order:getSubscriptions }
    methods: [GET]
    requirements:
        id: \d+

api_admin_orders_post_cancel:
    path: /v1/orders/{orderId}/cancel
    defaults: { _controller: AppBundle:Api/Order:postCancel }
    methods: [POST]
    requirements:
        id: \d+

api_admin_orders_post_dispatch_funds:
    path: /v1/orders/{orderId}/dispatch
    defaults: { _controller: AppBundle:Api/Order:postDispatchFunds }
    methods: [POST]
    requirements:
        id: \d+

api_orders_get_refunds:
    path: /v1/orders/{orderId}/refunds
    defaults: { _controller: AppBundle:Api/Refund:getOrderRefund }
    methods: [GET]
    requirements:
        id: \d+

api_order_get_refund:
    path: /v1/orders/{orderId}/refunds/{refundId}
    defaults: { _controller: AppBundle:Api/Refund:getOrderRefundById }
    methods: [GET]
    requirements:
        id: \d+

api_post_refunds:
    path: /v1/orders/{orderId}/refunds
    defaults: { _controller: AppBundle:Api/Refund:postOrderRefund }
    methods: [POST]
    requirements:
        id: \d+

api_user_orders_get_refunds:
    path: /v1/user/orders/{orderId}/refunds
    defaults: { _controller: AppBundle:Api/Refund:getOrderRefund }
    methods: [GET]
    requirements:
        id: \d+

api_user_order_get_refund:
    path: /v1/user/orders/{orderId}/refunds/{refundId}
    defaults: { _controller: AppBundle:Api/Refund:getOrderRefundById }
    methods: [GET]
    requirements:
        id: \d+

api_orders_get_credit_notes:
    path: /v1/orders/{orderId}/credit-notes
    defaults: { _controller: AppBundle:Api/CreditNote:getOrderCreditNotes }
    methods: [GET]
    requirements:
        id: \d+

api_user_get_credit_notes:
    path: /v1/user/orders/{orderId}/credit-notes
    defaults: { _controller: AppBundle:Api/CreditNote:getOrderCreditNotes }
    methods: [GET]
    requirements:
        id: \d+

api_orders_get_credit_note:
    path: /v1/orders/{orderId}/credit-notes/{refundId}
    defaults: { _controller: AppBundle:Api/CreditNote:getCreditNote }
    methods: [GET]
    requirements:
        id: \d+

api_user_get_credit_note:
    path: /v1/user/orders/{orderId}/credit-notes/{refundId}
    defaults: { _controller: AppBundle:Api/CreditNote:getCreditNote }
    methods: [GET]
    requirements:
        id: \d+

api_order_set_invoice_number:
    path: /v1/orders/{orderId}/set-invoice-number
    defaults: { _controller: AppBundle:Api/Order:setInvoiceNumber }
    methods: [PUT]
    requirements:
        orderId: \d+

api_order_set_extra_data:
    path: /v1/orders/{orderId}/extra
    defaults: { _controller: AppBundle:Api/Order:patchExtra }
    methods: [PATCH]
    requirements:
        orderId: \d+

api_order_get_child:
    path: /v1/orders/{orderId}/child
    defaults: {_controller: AppBundle:Api\Order:getOrderChild}
    methods: [GET]
    requirements:
        orderId: \d+

#--------------
# Import data
#--------------
api_import_products:
    path: /v1/import/products
    defaults: { _controller: AppBundle:Api\Import:products }
    methods: [POST]

api_import_categories:
    path: /v1/import/categories
    defaults: { _controller: AppBundle:Api\Import:categories }
    methods: [POST]

api_import_attributes:
    path: /v1/import/attributes
    defaults: { _controller: AppBundle:Api\Import:attributes }
    methods: [POST]

api_import_variants:
    path: /v1/import/variants
    defaults: { _controller: AppBundle:Api\Import:variants }
    methods: [POST]

api_import_links_categories:
    path: /v1/import/linksCategories
    defaults: { _controller: AppBundle:Api\Import:linksCategories }
    methods: [POST]

api_import_divisions:
    path: /v1/import/divisions
    defaults: { _controller: AppBundle:Api\Import:divisions }
    methods: [POST]

api_import_multi_vendor_products:
    path: /v1/import/multi-vendor-products
    defaults: { _controller: AppBundle:Api\Import:multiVendorProducts }
    methods: [POST]

api_import_product_prices:
    path: /v1/import/product-prices
    defaults: { _controller: AppBundle:Api\Import:productPrices }
    methods: [POST]

api_import_product_quantities:
    path: /v1/import/product-quantities
    defaults: { _controller: AppBundle:Api\Import:productQuantities }
    methods: [POST]

api_import_related_products:
    path: /v1/import/related-products
    defaults: { _controller: AppBundle:Api\Import:relatedProducts }
    methods: [POST]

api_import_product_attributes:
    path: /v1/import/product-attributes
    defaults: { _controller: AppBundle:Api\Import:productAttributes }
    methods: [POST]

#--------------
# Import Job
#--------------
api_import_jobs_list:
    path: /v1/jobs
    defaults: { _controller: AppBundle:Api\Job:list }
    methods: [GET]

api_import_job:
    path: /v1/jobs/{jobId}
    defaults: { _controller: AppBundle:Api\Job:getJob }
    methods: [GET]
    requirements:
        jobId: "%regexp_guid%"

api_import_job_report:
    path: /v1/jobs/{jobId}/report
    defaults: { _controller: AppBundle:Api\Job:getReport }
    methods: [GET]
    requirements:
        jobId: "%regexp_guid%"

api_import_job_cancel:
    path: /v1/jobs/{jobId}/cancel
    defaults: { _controller: AppBundle:Api\Job:cancel }
    methods: [POST]
    requirements:
        jobId: "%regexp_guid%"

#----------
# Language
#----------
api_language_list:
    path: /v1/languages
    defaults: {_controller: AppBundle:Api\Language:list}
    methods: [GET]

#--------
# AuthLog
#--------
api_auth_log_get:
    path: /v1/security/viewlog/{id}
    defaults: { _controller: AppBundle:Api\AuthLog:get }
    methods: [GET]
    requirements:
        id: \d+
api_auth_log_list:
    path: /v1/security/viewlogs/{page}.{format}
    defaults: { _controller: AppBundle:Api\AuthLog:list }
    methods: [GET]
    requirements:
        page: \d+
        format: (json|csv)

#----------
# Currency
#----------
api_currency_countries_get:
    path: /v1/currencies/{currencyCode}/countries
    defaults: { _controller: AppBundle:Api/Currency:getCurrencyCountries }
    methods: [GET]
    requirements:
        currencyCode: '%regexp_currency_code%'

api_currency_update:
    path: /v1/currencies/{currencyCode}
    defaults: {_controller: AppBundle:Api\Currency:update}
    methods: [PATCH]
    requirements:
        currencyCode: '%regexp_currency_code%'

api_currency_list:
    path: /v1/currencies
    defaults: {_controller: AppBundle:Api\Currency:list}
    methods: [GET]

api_currency_countries_delete:
    path: /v1/currencies/{currencyCode}/countries/{countryCode}
    defaults: { _controller: AppBundle:Api/Currency:deleteCurrencyCountry }
    methods: [DELETE]
    requirements:
        currencyCode: '%regexp_currency_code%'
        countryCode: '%regexp_country_code%'

api_currency_countries_post:
    path: /v1/currencies/{currencyCode}/countries
    defaults: { _controller: AppBundle:Api/Currency:createCurrencyCountry }
    methods: [POST]
    requirements:
        currencyCode: '%regexp_currency_code%'

api_currency_get:
    path: /v1/currencies/{currencyCode}
    defaults: {_controller: AppBundle:Api\Currency:getCurrency}
    methods: [GET]
    requirements:
        currencyCode: '%regexp_currency_code%'

#----------
# Transaction
#----------
api_transaction_list:
    path: /v1/orders/{orderId}/transactions
    defaults: {_controller: AppBundle:Api\OrderTransactions:listByOrder}
    methods: [GET]
    requirements:
        id: \d+

api_report_transactions_list:
    path: /v1/reports/transactions
    defaults:  { _controller: AppBundle:Api/TransactionTransfer:reportTransactionList }

#----------
# Credit cards
#----------
api_credit_cards_list:
    path: /v1/users/{userId}/cards
    defaults: {_controller: AppBundle:Api\CreditCard:list}
    methods: [GET]
    requirements:
        userId: \d+

api_credit_cards_get:
    path: /v1/users/{userId}/cards/{cardId}
    defaults: {_controller: AppBundle:Api\CreditCard:get}
    methods: [GET]
    requirements:
        userId: \d+
        cardId: "%regexp_guid%"

api_user_credit_card_registration:
    path: /v1/users/{userId}/credit-card-registration
    defaults: {_controller: AppBundle:Api\CreditCard:registration}
    methods: [GET]
    requirements:
        userId: \d+

#----------
# Subscriptions
#----------
api_subscriptions_list:
    path: /v1/subscriptions
    defaults: {_controller: AppBundle:Api\Subscription:list}
    methods: [GET]

api_get_a_subscription:
    path: /v1/subscriptions/{subscriptionId}
    defaults: {_controller: AppBundle:Api\Subscription:get}
    methods: [GET]
    requirements:
        subscriptionId: "%regexp_guid%"

api_get_a_subscription_logs:
    path: /v1/subscriptions/{subscriptionId}/logs
    defaults: {_controller: AppBundle:Api\Subscription:logs}
    methods: [GET]
    requirements:
        subscriptionId: "%regexp_guid%"

api_patch_a_subscription:
    path: /v1/subscriptions/{subscriptionId}
    defaults: {_controller: AppBundle:Api\Subscription:patch}
    methods: [PATCH]
    requirements:
        subscriptionId: "%regexp_guid%"

api_subscription_taxes_list:
    path: /v1/subscriptions/{subscriptionId}/taxes
    defaults: {_controller: AppBundle:Api\Subscription:getTaxes}
    methods: [GET]
    requirements:
        subscriptionId: "%regexp_guid%"

api_subscription_orders_list:
    path: /v1/subscriptions/{subscriptionId}/orders
    defaults: {_controller: AppBundle:Api\Subscription:getOrders}
    methods: [GET]
    requirements:
        subscriptionId: "%regexp_guid%"

api_subscription_items_list:
    path: /v1/subscriptions/{subscriptionId}/items
    defaults: {_controller: AppBundle:Api\Subscription:getItems}
    methods: [GET]
    requirements:
        subscriptionId: "%regexp_guid%"

#----------
# Payments
#----------
api_payments_list:
    path: /v1/payments
    defaults: {_controller: AppBundle:Api\Payment:list}
    methods: [GET]

#----------
# Mandates
#----------
api_direct_debit_payment_create_mandate:
    path: /v1/user/mandates
    defaults: {_controller: AppBundle:Api\Payment\DirectDebitPayment:createMandate}
    methods: [POST]

api_mandate_list:
    path: /v1/user/mandates
    defaults: {_controller: AppBundle:Api\Payment\DirectDebitPayment:getMandates}
    methods: [GET]

#----------
# Commissions
#----------
api_commission_create:
    path: /v1/commissions
    defaults: {_controller: AppBundle:Api\Commission:addMarketplaceCommission}
    methods: [POST]

api_commission_category_create:
    path: /v1/categories/{categoryId}/commissions
    defaults: {_controller: AppBundle:Api\Commission:addCategoryCommission}
    methods: [POST]
    requirements:
        categoryId: \d+

api_commission_company_create:
    path: /v1/companies/{companyId}/commissions
    defaults: {_controller: AppBundle:Api\Commission:addCompanyCommission}
    methods: [POST]
    requirements:
        companyId: \d+

api_commission_update:
    path: /v1/commissions/{commissionId}
    defaults: {_controller: AppBundle:Api\Commission:updateMarketplaceCommission}
    methods: [PATCH]
    requirements:
        commissionId: "%regexp_guid%"

api_commission_category_update:
    path: /v1/categories/{categoryId}/commissions/{commissionId}
    defaults: {_controller: AppBundle:Api\Commission:updateCategoryCommission}
    methods: [PATCH]
    requirements:
        categoryId: \d+
        commissionId: "%regexp_guid%"

api_commission_company_update:
    path: /v1/companies/{companyId}/commissions/{commissionId}
    defaults: {_controller: AppBundle:Api\Commission:updateCompanyCommission}
    methods: [PATCH]
    requirements:
        companyId: \d+
        commissionId: "%regexp_guid%"

api_commission_get:
    path: /v1/commissions
    defaults: {_controller: AppBundle:Api\Commission:getCommissions}
    methods: [GET]

api_commission_marketplace_get:
    path: /v1/commissions/default
    defaults: {_controller: AppBundle:Api\Commission:getMarketplaceCommission}
    methods: [GET]

api_commission_get-by-id:
    path: /v1/commissions/{commissionId}
    defaults: {_controller: AppBundle:Api\Commission:getCommission}
    methods: [GET]
    requirements:
        commissionId: "%regexp_guid%"

api_commission_category_get:
    path: /v1/categories/{categoryId}/commissions
    defaults: {_controller: AppBundle:Api\Commission:getCategoryCommissions}
    methods: [GET]
    requirements:
        categoryId: \d+

api_commission_company_get:
    path: /v1/companies/{companyId}/commissions
    defaults: {_controller: AppBundle:Api\Commission:getCompanyCommissions}
    methods: [GET]
    requirements:
        companyId: \d+

api_commission_delete:
    path: /v1/commissions/{commissionId}
    defaults: {_controller: AppBundle:Api\Commission:deleteCommission}
    methods: [DELETE]
    requirements:
        commissionId: "%regexp_guid%"

#----------
# Callbacks
#----------
api_stripe_callback:
    path: /v1/callbacks/stripe
    controller: Wizacha\AppBundle\Controller\Api\StripeCallbackController
    methods: [POST]

#----------
# Internal usage
#----------
api_basic_configuration:
    path: /v1/basicconfiguration
    methods: [GET]
    defaults: { _controller: AppBundle:Api/Configuration:getConfiguration }

api_save_basic_configuration:
    path: /v1/basicconfiguration
    methods: [POST]
    defaults: { _controller: AppBundle:Api/Configuration:postConfiguration }

#-----------------
# OrderAttachments
#-----------------
api_order_attachment_create:
    path: /v1/orders/{orderId}/attachments
    defaults: {_controller: AppBundle:Api\Order\OrderAttachment:postOrderAttachment}
    methods: [POST]
    requirements:
        orderId: \d+

api_order_attachment_get:
    path: /v1/orders/{orderId}/attachments/{attachmentId}
    defaults: {_controller: AppBundle:Api\Order\OrderAttachment:getOrderAttachment}
    methods: [GET]
    requirements:
        orderId: \d+
        attachmentId: "%regexp_guid%"

api_order_attachment_for_user_get:
    path: /v1/user/orders/{orderId}/attachments/{attachmentId}
    defaults: {_controller: AppBundle:Api\Order\OrderAttachment:getOrderAttachmentByUser}
    methods: [GET]
    requirements:
        orderId: \d+
        attachmentId: "%regexp_guid%"

api_order_attachment_download_by_user:
    path: /v1/user/orders/{orderId}/attachments/{attachmentId}/download
    defaults: {_controller: AppBundle:Api\Order\OrderAttachment:downloadOrderAttachmentByUser}
    methods: [GET]
    requirements:
        orderId: \d+
        attachmentId: "%regexp_guid%"

api_order_add_extra_data:
    path: /v1/user/orders/{orderId}/extra
    defaults: { _controller: AppBundle:Api/Order:postExtra }
    methods: [POST]
    requirements:
        orderId: \d+

api_organisations_order_attachment_for_user_get:
    path: /v1/organisations/orders/{orderId}/attachments/{attachmentId}
    defaults: { _controller: AppBundle:Api/Organisation/OrganisationOrder:getOrganisationOrderAttachmentByUser }
    methods: [GET]
    requirements:
        orderId: \d+
        attachmentId: "%regexp_guid%"

api_organisations_order_attachment_download_by_user:
    path: /v1/organisations/orders/{orderId}/attachments/{attachmentId}/download
    defaults: {_controller: AppBundle:Api/Organisation/OrganisationOrder:downloadOrderAttachmentByUser}
    methods: [GET]
    requirements:
        orderId: \d+
        attachmentId: "%regexp_guid%"

api_order_attachment_list:
    path: /v1/orders/{orderId}/attachments
    defaults: {_controller: AppBundle:Api\Order\OrderAttachment:listOrderAttachments}
    methods: [GET]
    requirements:
        orderId: \d+

api_order_attachment_download:
    path: /v1/orders/{orderId}/attachments/{attachmentId}/download
    defaults: {_controller: AppBundle:Api\Order\OrderAttachment:downloadOrderAttachment}
    methods: [GET]
    requirements:
        orderId: \d+
        attachmentId: "%regexp_guid%"

api_order_attachment_delete:
    path: /v1/orders/{orderId}/attachments/{attachmentId}
    defaults: {_controller: AppBundle:Api\Order\OrderAttachment:deleteOrderAttachment}
    methods: [DELETE]
    requirements:
        orderId: \d+
        attachmentId: "%regexp_guid%"

api_order_attachment_update:
    path: /v1/orders/{orderId}/attachments/{attachmentId}
    defaults: {_controller: AppBundle:Api\Order\OrderAttachment:updateOrderAttachment}
    methods: [PATCH]
    requirements:
        orderId: \d+
        attachmentId: "%regexp_guid%"

#-----------------
# OrderActions
#-----------------

api_order_actions_list:
    path: /v1/orders/{orderId}/actions
    defaults: {_controller: AppBundle:Api\Order\OrderAction:listOrderActions}
    methods: [GET]
    requirements:
        orderId: \d+

api_user_groups_list:
    path: /v1/groups
    defaults: {_controller: AppBundle:Api\Group:list}
    methods: [GET]

api_user_groups_create:
    path: /v1/groups
    defaults: {_controller: AppBundle:Api\Group:create}
    methods: [POST]

api_user_groups_update:
    path: /v1/groups/{groupId}
    defaults: {_controller: AppBundle:Api\Group:update}
    methods: [PATCH]

api_list_users_group:
    path: /v1/groups/{groupId}/users
    defaults: {_controller: AppBundle:Api\Group:listUsers}
    methods: [GET]

api_add_users_group:
    path: /v1/groups/{groupId}/users
    defaults: {_controller: AppBundle:Api\Group:addUsers}
    methods: [POST]

api_add_user_group:
    path: /v1/groups/{groupId}/users/{userId}
    defaults: {_controller: AppBundle:Api\Group:addUser}
    methods: [POST]

api_delete_user_group:
    path: /v1/groups/{groupId}/users/{userId}
    defaults: {_controller: AppBundle:Api\Group:deleteUser}
    methods: [DELETE]

api_delete_users_group:
    path: /v1/groups/{groupId}/users
    defaults: {_controller: AppBundle:Api\Group:deleteUsers}
    methods: [DELETE]

#-----------------
# Related Products
#-----------------

api_related_products_create:
    path: /v1/products/{productId}/related
    defaults: { _controller: AppBundle:Api\RelatedProduct\RelatedProduct:createRelatedProduct }
    methods: [POST]
    requirements:
        productId: \d+

api_related_products_delete:
    path: /v1/products/{productId}/related
    defaults: { _controller: AppBundle:Api\RelatedProduct\RelatedProduct:deleteRelatedProduct }
    methods: [DELETE]
    requirements:
        productId: \d+

#-----------------
# Quotes
#-----------------
api_quote_selection_list:
    path: /v1/quote-request-selections
    defaults: { _controller: AppBundle:Api\Quotation\QuoteRequestSelection:list }
    methods: [GET]

api_quote_selection_add:
    path: /v1/quote-request-selections/{quoteRequestSelectionId}/add
    defaults: { _controller: AppBundle:Api\Quotation\QuoteRequestSelection:addDeclinations }
    methods: [PUT]
    requirements:
        quoteRequestSelectionId: \d+

api_quote_selection_add_no_parameter:
    path: /v1/quote-request-selections/add
    defaults: { _controller: AppBundle:Api\Quotation\QuoteRequestSelection:addDeclinations }
    methods: [PUT]

api_quote_selection_update:
    path: /v1/quote-request-selections/{quoteRequestSelectionId}/update
    defaults: { _controller: AppBundle:Api\Quotation\QuoteRequestSelection:updateDeclinations }
    methods: [PUT]
    requirements:
        quoteRequestSelectionId: \d+

api_quote_selection_update_no_parameter:
    path: /v1/quote-request-selections/update
    defaults: { _controller: AppBundle:Api\Quotation\QuoteRequestSelection:updateDeclinations }
    methods: [PUT]

api_quote_selection_remove:
    path: /v1/quote-request-selections/{quoteRequestSelectionId}/remove
    defaults: { _controller: AppBundle:Api\Quotation\QuoteRequestSelection:removeDeclinations }
    methods: [PUT]
    requirements:
        quoteRequestSelectionId: \d+

api_quote_selection_remove_no_parameter:
    path: /v1/quote-request-selections/remove
    defaults: { _controller: AppBundle:Api\Quotation\QuoteRequestSelection:removeDeclinations }
    methods: [PUT]
