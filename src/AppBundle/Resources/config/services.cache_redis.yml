services:
    snc_redis.public.default:
        alias: snc_redis.default
        public: true
    snc_redis.public.handlers:
        alias: snc_redis.handlers
        public: true
    snc_redis.public.locks:
        alias: snc_redis.locks
        public: true
    snc_redis.public.cscart_sessions:
        alias: snc_redis.cscart_sessions
        public: true

    cscart.cache_backend:
        class: Tygh\Backend\Cache\Redis
        public: true
        arguments:
            - '@snc_redis.default'
            - '@snc_redis.handlers'
            - '@snc_redis.locks'
            - '%marketplace.version%'
