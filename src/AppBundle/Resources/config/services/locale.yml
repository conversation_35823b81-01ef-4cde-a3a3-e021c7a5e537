services:
    app.locale.detector:
        class: Wizacha\AppBundle\Locale\Detector
        public: true
        arguments:
            - '@app.locale.detector.interface'
            - '@app.locale.detector.content'
            - '%locale%'
            - '@marketplace.user.user_service'

    app.locale.detector.interface:
        class: Wizacha\Component\Locale\LocaleDetectorDelegate
        public: false
        arguments:
            - '@app.locale.detector.api_only'
            - '@app.locale.detector.interface.allowed'

    app.locale.detector.content:
        class: Wizacha\Component\Locale\LocaleDetectorDelegate
        public: false
        arguments:
            - '@app.locale.detector.api_only'
            - '@app.locale.detector.content.allowed'

    app.locale.detector.api_only:
        class: Wizacha\AppBundle\Locale\ApiOnlyDetector
        public: false
        arguments:
            - '@app.locale.detector.api'

    app.locale.detector.api:
        class: Wizacha\AppBundle\Locale\ApiDetector
        public: false
        arguments:
            - '@app.locale.detector.in_header'
            - '/api/v1/doc/schema.yaml'
            - '/api/v1/doc/schema.json'

    app.locale.detector.interface.allowed:
        class: Wizacha\Component\Locale\AllowedLocalesDetector
        public: false
        arguments:
            - '@app.locale.detector.interface.front'
        factory:
            - Wizacha\AppBundle\Factory\Locale\AllowedLocalesDetectorFactory
            - make

    app.locale.detector.interface.front:
        class: Wizacha\Component\Locale\LocaleDetectorDelegate
        public: false
        arguments:
            - '@app.locale.detector.interface.in_query'
            - '@app.locale.detector.interface.in_session'
            - '@app.locale.detector.interface.user'

    app.locale.detector.interface.in_query:
        class: Wizacha\Component\Locale\LocaleInQuery
        public: false
        arguments:
            - sl

    app.locale.detector.interface.in_session:
        class: Wizacha\Component\Locale\LocaleInSession
        public: false
        factory:
            - Wizacha\AppBundle\Factory\Locale\LocaleInSessionFactory
            - make

    app.locale.detector.interface.user:
        class: Wizacha\Component\Locale\UserLocale
        public: false

    app.locale.detector.in_header:
        class: Wizacha\Component\Locale\LocaleInHeader
        public: false
        factory:
            - Wizacha\AppBundle\Factory\Locale\LocaleInHeaderFactory
            - make

    app.locale.detector.content.allowed:
        class: Wizacha\Component\Locale\AllowedLocalesDetector
        public: false
        arguments:
            - '@app.locale.detector.content.front'
        factory:
            - Wizacha\AppBundle\Factory\Locale\AllowedLocalesDetectorFactory
            - make

    app.locale.detector.content.front:
        class: Wizacha\Component\Locale\LocaleDetectorDelegate
        public: false
        arguments:
            - '@app.locale.detector.content.in_query'
            - '@app.locale.detector.content.in_session'

    app.locale.detector.content.in_query:
        class: Wizacha\Component\Locale\LocaleInQuery
        public: false
        arguments:
            - descr_sl

    app.locale.detector.content.in_session:
        class: Wizacha\Component\Locale\LocaleInSession
        public: false
        arguments:
            - descr_sl
