<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Service;

use Psr\Log\LoggerInterface;

class WhitelistDomainsService
{
    /** @var string[] */
    private $whitelistDomains;

    /** @var LoggerInterface */
    protected $logger;

    public function __construct(string $whitelistDomains, LoggerInterface $logger)
    {
        $this->whitelistDomains = \explode(',', $whitelistDomains);
        $this->logger = $logger;
    }

    public function isWhitedDomainHost(string $url): bool
    {
        if (\in_array("*", $this->whitelistDomains, true) === true) {
            $this->logger->warning(\sprintf('The application is vulnerable and has let the %s domain pass', $this->getHostFromUrl($url)));

            return true;
        }

        if (\in_array($this->getHostFromUrl($url), $this->whitelistDomains, true) === false) {
            $this->logger->error(\sprintf('Application rejected domain %s', $this->getHostFromUrl($url)));

            return false;
        }

        return true;
    }

    public function getHostFromUrl(string $url): string
    {
        return \parse_url(\trim($url), PHP_URL_HOST);
    }
}
