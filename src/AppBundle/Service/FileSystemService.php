<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Service;

use DirectoryIterator;
use IteratorIterator;
use SplFileInfo;
use Tygh\Registry;

final class FileSystemService
{
    /** @var string */
    private $rootPath;

    /** @var string */
    private $scope;

    public function __construct()
    {
        $this->rootPath = Registry::get('config.dir.root');
    }

    public function getScope(): string
    {
        return $this->scope;
    }

    public function setScope(string $directory): self
    {
        $this->scope = $this->rootPath . $directory;

        return $this;
    }

    public function iterateOverDirectoryFiles(string $directoryRelativePath, callable $function): void
    {
        $directoryIterator = new DirectoryIterator($this->scope . $directoryRelativePath);
        $iterator = new IteratorIterator($directoryIterator);

        foreach ($iterator as $splFileInfo) {
            if ($splFileInfo instanceof SplFileInfo && $splFileInfo->isFile()) {
                $function($splFileInfo);
            }
        }
    }

    public function getFileContents(SplFileInfo $splFileInfo): string
    {
        $fileContents = false;

        if ($splFileInfo->isFile()) {
            $fileObject = $splFileInfo->openFile();

            if ($fileObject->isReadable()) {
                $fileContents = $fileObject->fread($fileObject->getSize());
            }
        }

        return $fileContents;
    }
}
