<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Service;

use Symfony\Component\Config\FileLocator;
use Wizacha\Marketplace\Theme\AssetManager;
use Wizacha\Marketplace\Theme\LessCompiler;
use Wizacha\Registry;

class ThemeCustomizer
{
    public const THEME_CUSTOMIZER_HANDLER = 'theme_version';

    private $lessParser;
    private $cache;
    private $assetManager;
    private $fileLocator;
    private $baseUrl;

    public function __construct(LessCompiler $lessParser, Registry $registry, FileLocator $fileLocator, AssetManager $assetManager, $baseUrl)
    {
        $this->lessParser = $lessParser;
        $this->cache = $registry->cache();
        $this->assetManager = $assetManager;
        $this->fileLocator = $fileLocator;
        $this->baseUrl = $baseUrl;
    }

    /**
     * @param array $values
     * @param string $file
     * @throws \Exception
     */
    public function customizeLess(array $values, $file = '@AppBundle/Resources/public/css/main.less')
    {
        $filePath = $this->fileLocator->locate($file);

        $css = $this->lessParser->compile($filePath, $values, $this->baseUrl . 'assets/');

        $this->assetManager->writeAsset('css/main.css', $css, 'text/css');

        $this->cache->regenerateHandlerId(self::THEME_CUSTOMIZER_HANDLER);
    }
}
