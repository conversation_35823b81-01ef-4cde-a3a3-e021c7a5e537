<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Service;

class TreatmentArrayService
{
    /**
     * @var mixed[] $data
     *
     * @return mixed[]
     */
    public static function removingEmptyField(array $data): array
    {
        return \array_filter($data, function ($value, $key) use ($data): bool {
            return $key !== '' && $value !== '';
        }, ARRAY_FILTER_USE_BOTH);
    }
}
