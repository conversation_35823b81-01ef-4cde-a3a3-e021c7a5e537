<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Service;

use Wizacha\Marketplace\Theme\AssetManager;
use Wizacha\Registry;

class ConfigurationService
{
    public const SETTINGS_NAME = "wizacha_basic_configuration";
    public const SOCIAL_NETWORKS = "social";

    private $themeCustomizer;
    private $assetManager;
    private $settingStorage;

    public function __construct(
        ThemeCustomizer $themeCustomizer,
        AssetManager $assetManager,
        SettingStorage $settingStorage
    ) {
        $this->themeCustomizer = $themeCustomizer;
        $this->assetManager = $assetManager;
        $this->settingStorage = $settingStorage;
    }

    public function refreshStyle(array $variables = null)
    {
        if (null === $variables) {
            $variables = $this->readConfiguration();
        }

        $this->preRefreshStyle($variables);
        $this->themeCustomizer->customizeLess($variables);
    }

    public function readConfiguration(): array
    {
        return $this->settingStorage->get(self::SETTINGS_NAME, []);
    }

    public function saveConfiguration(array $configuration, array $files = [], $full = true): array
    {
        $this->preSaveConfiguration($configuration, $files);
        $this->settingStorage->set(self::SETTINGS_NAME, $configuration);
        if ($full) {
            $this->refreshStyle($configuration);
        }

        return $configuration;
    }

    private function preRefreshStyle(array &$variables)
    {
        // Remove social links: as it is an array, we can't use it in less
        unset($variables[static::SOCIAL_NETWORKS]);
    }

    private function preSaveConfiguration(array &$configuration, array &$files)
    {
        // First: extract social configuration
        $this->handleSocialNetworks($configuration, $files);

        // Handle raw data
        $configuration = $this->normalizeColors($configuration);
        $configuration = $this->deployImages($configuration, $files);

        $configuration = array_filter($configuration, function ($value) {
            return "" !== $value && null !== $value;
        });
    }

    private function handleSocialNetworks(array &$configuration, array &$files)
    {
        $socialConfig = [];

        if (isset($configuration[static::SOCIAL_NETWORKS])) {
            $socialConfig = $configuration[static::SOCIAL_NETWORKS];
        }

        // Only files with a related link must be deployed
        $filesToDeploy = array_map(
            function ($key) use (&$files) {
                $key = static::SOCIAL_NETWORKS . "_" . $key;

                if (isset($files[$key])) {
                    $file = $files[$key];
                    unset($files[$key]);

                    return $file;
                }

                return null;
            },
            array_keys($socialConfig)
        );

        $deployedFiles = $this->deployImages([], $filesToDeploy);

        // Add old links that didn't upload their picto too
        $oldConfig = $this->readConfiguration();
        $oldLinks = [];

        if (isset($oldConfig[static::SOCIAL_NETWORKS]) && \is_array($oldConfig[static::SOCIAL_NETWORKS])) {
            $oldLinks = $oldConfig[static::SOCIAL_NETWORKS];
        }

        foreach ($oldLinks as $oldLinkId => $oldLink) {
            if (!isset($deployedFiles[$oldLinkId]) && !empty($oldLink["image"]) && ! empty($oldLink["link"])) {
                $deployedFiles[$oldLinkId] = $oldLink["image"];
            }
        }

        // Build social entry
        if (! empty($deployedFiles)) {
            $configuration[static::SOCIAL_NETWORKS] = array_map(
                function ($id) use ($socialConfig, $deployedFiles) {
                    return [
                        "link" => $socialConfig[$id],
                        "image" => $deployedFiles[$id]
                    ];
                },
                array_keys($deployedFiles)
            );
        }
    }

    private function deployImages(array $configuration, array $files = [])
    {
        foreach ($files as $configurationName => $fileInfo) {
            if (isset($fileInfo["file"], $fileInfo["name"]) && is_file($fileInfo["file"]) && is_readable($fileInfo["file"])) {
                // Get an escaped name for the file destination
                $fileName = sprintf(
                    "uploads/%s_%s",
                    // Add a pseudo random value for preventing 2 files with the same name to have a conflict
                    uniqid(),
                    // Replace all the chars that arent a-z, 0-9, _, or . by a '_' in the original file name
                    preg_replace('/[^a-z\d_\-\.]/i', "_", basename($fileInfo["name"]))
                );

                $this->assetManager->writeAsset($fileName, file_get_contents($fileInfo["file"]), $fileInfo['mimeType']);

                $configuration[$configurationName] = sprintf('"%s"', $this->assetManager->getAssetUrl($fileName));
            }
        }

        return $configuration;
    }

    private function normalizeColors(array $variables)
    {
        return array_map(
            function ($color) {
                if (! \is_string($color)) {
                    return $color;
                }

                if (preg_match('/^[\da-f]{6}$/i', $color)) {
                    return "#" . $color;
                }

                return $color;
            },
            $variables
        );
    }

    public function getJavascriptHeader(): string
    {
        return $this->settingStorage->get(self::SETTINGS_NAME . '_javascript_header', '');
    }

    public function getJavascriptFooter(): string
    {
        return $this->settingStorage->get(self::SETTINGS_NAME . '_javascript_footer', '');
    }

    public function saveJavascriptConfiguration(string $header, string $footer)
    {
        $this->settingStorage->set(self::SETTINGS_NAME . '_javascript_header', $header);
        $this->settingStorage->set(self::SETTINGS_NAME . '_javascript_footer', $footer);
        Registry::defaultInstance()->cache()->regenerateHandlerId(ThemeCustomizer::THEME_CUSTOMIZER_HANDLER);
    }
}
