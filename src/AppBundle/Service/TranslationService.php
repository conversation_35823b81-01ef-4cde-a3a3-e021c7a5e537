<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Service;

use Doctrine\DBAL\Connection;
use Symfony\Component\Intl\Countries;
use Symfony\Component\Translation\Loader\XliffFileLoader;
use Symfony\Component\Translation\MessageCatalogue;
use Symfony\Component\Translation\MessageCatalogueInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tygh\Registry;
use Wizacha\Bridge\Symfony\Translator;
use Wizacha\Component\Locale\Exception\LocaleNotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;

class TranslationService
{
    public const TRANSLATION_LAST_UPDATED_DATE_KEY = 'translation-last-updated-date';
    public const TRANSLATION_LAST_UPDATED_DATE_FORMAT = \DATE_RFC3339;
    public const TRANSLATION_BATCH_SIZE = 250;

    /** @var XliffFileLoader */
    private $xliffLoader;

    /** @var Connection */
    private $db;

    /** @var Translator */
    private $translator;

    /** @var string[] */
    private $localesCache;

    public function __construct(TranslatorInterface $translator, XliffFileLoader $xliffLoader, Connection $db)
    {
        $this->xliffLoader = $xliffLoader;
        $this->db = $db;
        $this->translator = $translator;
    }

    public function loadXliffString(string $xliff, string $locale): MessageCatalogue
    {
        // Preparing a tmp file, because this loader doesn't accept anything else.
        $tmpFile = tmpfile();
        fwrite($tmpFile, $xliff);
        $tmpFilePath = stream_get_meta_data($tmpFile)["uri"];

        $catalog = $this->xliffLoader->load($tmpFilePath, $locale);

        // Cleanup
        @unlink($tmpFilePath);

        return $catalog;
    }

    public function loadFrontTranslations(string $locale): MessageCatalogue
    {
        $qb = $this->db->createQueryBuilder();
        $qb->select('lv.name', 'lv.value')->from('cscart_language_values', 'lv')
            ->where("lv.lang_code = {$qb->createNamedParameter($locale)}");
        $translations = $qb->execute()->fetchAll(\PDO::FETCH_ASSOC);

        $catalog = new MessageCatalogue($locale);
        foreach ($translations as $translation) {
            $catalog->set($translation['name'], $translation['value']);
        }

        return $catalog;
    }

    /**
     * Inserts front-office translations.
     * If the translation already exists, just flag it as a front-office one without overwriting its current value.
     */
    public function setFrontTranslations(MessageCatalogueInterface $catalog, string $locale): void
    {
        $translationsMap = $catalog->all('messages');

        if (empty($translationsMap)) {
            return;
        }

        $affectedRows = 0;
        try {
            $this->db->beginTransaction();

            $translationsMapChunks = array_chunk($translationsMap, static::TRANSLATION_BATCH_SIZE, true);
            foreach ($translationsMapChunks as $translationsMapChunk) {
                $query = "INSERT INTO `cscart_language_values` (`lang_code`, `name`, `value`, `original_value`) VALUES ";
                $queryValues = [];
                $params = [];
                foreach ($translationsMapChunk as $id => $message) {
                    // remove previously created missing keys before inserting
                    $this->db->executeUpdate(
                        "DELETE FROM
                            `cscart_language_values`
                        WHERE
                            `lang_code` = :locale
                            AND `name` = :id
                            AND BINARY `value` = ''
                        ",
                        [
                            'locale' => $locale,
                            'id' => $id,
                        ]
                    );

                    $queryValues[] = "(?, ?, ?, ?)";
                    $params[] = $locale;
                    $params[] = $id;
                    $params[] = $message;
                    $params[] = $message;
                }
                $query .= implode(', ', $queryValues) . " ON DUPLICATE KEY UPDATE `name`=`name`"; // https://stackoverflow.com/a/4920619/1685538

                $affectedRows += $this->db->executeUpdate($query, $params);
            }

            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }

        $affectedRows += \count($this->createMissingKeys());

        if ($affectedRows > 0) {
            array_map(
                function (string $locale): void {
                    $this->setDbTranslationLastUpdatedDate($locale);
                    $this->bustCache($locale);
                },
                $this->getActivatedLocales()
            );
        }
    }

    public function searchTranslations(string $locale, int $page = 1, int $itemsPerPage = null, string $query = null)
    {
        $catalogue = $this->translator->getCatalogue($locale);
        $messages = $catalogue->all('messages');
        $itemsPerPage = $itemsPerPage ?: (int) Registry::get('settings.Appearance.admin_elements_per_page');
        $itemsPerPage = min($itemsPerPage, 100);

        $search = [
            'items_per_page' => $itemsPerPage,
            'page' => $page,
        ];

        // On transforme le catalogue de : id => trans en [name => id, value => trans]
        array_walk($messages, function (&$message, $key) {
            $message = [
                'name' => $key,
                'value' => $message,
            ];
        });

        if ($fallbackCatalogue = $catalogue->getFallbackCatalogue()) {
            $messagesFallback = $this->translator->getCatalogue($fallbackCatalogue->getLocale())->all('messages');
            $fallback = array_diff_key($messagesFallback, $messages);
            $search['fallback'] = $fallbackCatalogue->getLocale();

            // On ajoute les fallback
            array_walk($fallback, function (&$message, $key) {
                $message = [
                    'name' => $key,
                    'value' => '',
                    'fallback' => $message,
                ];
            });

            $messages = array_values(array_merge($fallback, $messages));

            array_walk($messages, function (&$message) use ($messagesFallback, $search) {
                if (\strlen($message['value']) === 0
                    && \strlen($messagesFallback[$message['name']]) > 0
                    && \strlen($message['fallback']) === 0
                ) {
                    $message['fallback'] = $messagesFallback[$message['name']];
                    $message['fallback_lang'] = $search['fallback'];
                }
            });

            $missing = \count($fallback);
        } else {
            $messages = array_values($messages);
            $missing = 0;
        }

        $localInterface = (string) GlobalState::interfaceLocale();
        if ($locale !== $localInterface) {
            $messagesFallback = $this->translator->getCatalogue($localInterface)->all('messages');
            array_walk($messages, function (&$message) use ($messagesFallback, $localInterface) {
                if (\strlen($message['value']) === 0
                    && \strlen($messagesFallback[$message['name']]) > 0
                    && \strlen($message['fallback']) === 0
                ) {
                    $message['fallback'] = $messagesFallback[$message['name']];
                    $message['fallback_lang'] = $localInterface;
                }
            });
        }

        $search['completion'] =  \count($messages) ? (100 - round($missing * 100 / \count($messages))) : 0;

        // On filtre le catalogue
        if (!empty($query)) {
            $search['q'] = $query;
            $pattern = sprintf('/%s/i', preg_quote($query, '/'));
            $messages = array_values(array_filter($messages, function ($message) use ($pattern) {
                return (preg_match($pattern, $message['name']) || preg_match($pattern, $message['value']));
            }));
        }

        $search['total_items'] = \count($messages);

        usort($messages, function ($a, $b) {
            return strcmp($a['name'], $b['name']);
        });

        $offset = ($search['page'] - 1) * $search['items_per_page'];
        // On pagine le catalogue
        $slice = \array_slice($messages, $offset, $search['items_per_page']);

        return [$slice, $search];
    }

    public function deleteTranslations(array $translationIds, string $locale): void
    {
        if (empty($translationIds)) {
            return;
        }

        $deleted = $this->db->executeUpdate(
            'DELETE FROM `cscart_language_values` WHERE `name` IN (?) AND `lang_code` = ?',
            [ $translationIds, $locale ],
            [ Connection::PARAM_STR_ARRAY, \PDO::PARAM_STR ]
        );

        if ($deleted > 0) {
            $this->setDbTranslationLastUpdatedDate($locale);
            $this->bustCache($locale);
        }
    }

    public function updateCountries(string $locale)
    {
        $countries = Countries::getNames($locale);
        $values = [];
        $params = [];

        foreach ($countries as $code => $name) {
            $values[] = '(?, ?, ?)';
            $params[] = $code;
            $params[] = $locale;
            $params[] = $name;
        }

        $query = 'REPLACE INTO `cscart_country_descriptions` (`code`, `lang_code`, `country`) VALUES ';
        $query .= implode(', ', $values);

        $this->db->executeUpdate($query, $params);
    }

    /**
     * @param bool $updateDuplicates If set to true, existing translations will be overwritten. Else they will be left as they are.
     * @throws \Exception
     */
    public function setTranslations(array $translationsMap, string $locale, $updateDuplicates = false): void
    {
        if (empty($translationsMap)) {
            return;
        }

        if (false === \in_array($locale, $this->getActivatedLocales())) {
            throw new LocaleNotFound('Locale not found ' . $locale);
        }

        $affectedRows = 0;
        try {
            $this->db->beginTransaction();

            $translationsMapChunks = array_chunk($translationsMap, 250, true);
            foreach ($translationsMapChunks as $translationsMapChunk) {
                $query = "INSERT ";
                if (!$updateDuplicates) {
                    $query .= "IGNORE ";
                }
                $query .= "INTO `cscart_language_values` (`lang_code`, `name`, `value`) VALUES ";

                $valuesQueries = [];
                $params = [];
                foreach ($translationsMapChunk as $name => $value) {
                    $valuesQueries[] = "(?, ?, ?)";
                    $params[] = $locale;
                    $params[] = $name;
                    $params[] = $value;
                }
                $query .= implode(", ", $valuesQueries);

                if ($updateDuplicates) {
                    $query .= " ON DUPLICATE KEY UPDATE `value`=VALUES(`value`)";
                }

                $affectedRows += $this->db->executeUpdate($query, $params);
            }

            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }

        $affectedRows += \count($this->createMissingKeys());

        if ($affectedRows > 0) {
            $this->setDbTranslationLastUpdatedDate($locale);
            $this->bustCache($locale);
        }
    }

    /**
     * @return \DateTime the last time translations were updated (useful for caching)
     */
    public function getLastUpdatedAt(string $locale): \DateTime
    {
        $dbTranslationLastUpdatedDate = \DateTime::createFromFormat(
            static::TRANSLATION_LAST_UPDATED_DATE_FORMAT,
            $this->db->fetchColumn(
                'SELECT
                    `value`
                FROM
                    `cscart_language_values`
                WHERE
                    `lang_code` = :langCode
                    AND `name` = :name
                ',
                [
                    'langCode' => $locale,
                    'name' => static::TRANSLATION_LAST_UPDATED_DATE_KEY
                ]
            )
        );

        return
            $dbTranslationLastUpdatedDate instanceof \DateTime
            ? $dbTranslationLastUpdatedDate
            : $this->setDbTranslationLastUpdatedDate($locale)
        ;
    }

    /**
     * @TODO: make it private as soon as all languages write are in this service
     */
    public function bustCache(string $locale): void
    {
        $this->translator->removeCatalogueCache($locale);
        Registry::setChangedTables('language_values');
    }

    /** @return string[] */
    public function createMissingKeys(): array
    {
        try {
            $this->db->beginTransaction();
            $missingKeys = $this->buildMissingKeysSqlValues();
            $missingKeysChunks = array_chunk($missingKeys, 128, true);

            foreach ($missingKeysChunks as $missingKeysChunk) {
                $query = (
                    'INSERT
                    INTO `cscart_language_values`(
                        `name`,
                        `lang_code`,
                        `value`,
                        `original_value`,
                        `frontoffice_enabled`
                    )
                    VALUES'
                    . implode(',', $missingKeysChunk)
                );
                $this->db->executeUpdate($query);
            }

            $this->db->commit();

            return $missingKeys;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /** @return string[] */
    private function buildMissingKeysSqlValues(): array
    {
        $locales = $this->getActivatedLocales();
        $keysSqlValues = [];

        foreach ($this->getIncompleteKeys() as $key) {
            $existingLocales = explode(',', $key['codes']);
            $missingLocales = array_diff($locales, $existingLocales);
            foreach ($missingLocales as $locale) {
                $values = [
                    $this->db->quote($key['name']),
                    $this->db->quote($locale),
                    $this->db->quote(''),
                    $this->db->quote(''),
                    $this->db->quote($key['frontoffice_enabled']),
                ];
                $keysSqlValues[] = '(' . implode(',', $values) . ')';
            }
        }

        return $keysSqlValues;
    }

    /** @return string[] */
    public function getActivatedLocales(): array
    {
        if (false === isset($this->localesCache)) {
            $this->localesCache = \array_column(
                $this->db->fetchAll(
                    "SELECT
                    lang_code
                FROM
                    cscart_languages
                WHERE
                    status = 'A'
                ORDER BY
                    lang_code ASC
                "
                ),
                'lang_code'
            );
        }

        return $this->localesCache;
    }

    /**
     * Clefs avec traductions manquantes
     *
     * @return array[]
     */
    public function getIncompleteKeys(): \Generator
    {
        $preparedStatement = $this->db->prepare(
            'SELECT
                `name`,
                GROUP_CONCAT(LOWER(`lang_code`) ORDER BY `lang_code`) as `codes`,
                `frontoffice_enabled`
            FROM
                `cscart_language_values`
            GROUP BY
                `name`
            HAVING
                `codes` != :activatedLocales
            '
        );

        $preparedStatement->execute(
            [
                'activatedLocales' => implode(',', $this->getActivatedLocales())
            ]
        );

        while ($row = $preparedStatement->fetch()) {
            yield $row;
        }
    }

    /**
     * @return bool True if the cache has been refreshed, false if it didn't need to be
     */
    public function refreshCache(): bool
    {
        $refreshed = false;

        foreach ($this->getActivatedLocales() as $locale) {
            if ($this->refreshLocaleCache($locale)) {
                $refreshed = true;
            }
        }

        return $refreshed;
    }

    /**
     * @param string $locale
     * @return bool True if the cache has been refreshed, false if it didn't need to be
     */
    private function refreshLocaleCache(string $locale): bool
    {
        $localTranslationLastUpdatedDate = \DateTime::createFromFormat(
            static::TRANSLATION_LAST_UPDATED_DATE_FORMAT,
            $this->translator->trans(
                static::TRANSLATION_LAST_UPDATED_DATE_KEY,
                [],
                null,
                $locale
            )
        );

        if ($this->getLastUpdatedAt($locale) == $localTranslationLastUpdatedDate) {
            return false;
        }

        $this->bustCache($locale);
        return true;
    }

    private function setDbTranslationLastUpdatedDate(string $locale): \DateTime
    {
        $query = 'INSERT IGNORE INTO
                `cscart_language_values`
            SET
                `lang_code` = :langCode,
                `name` = :name,
                `value` = :value
            ON DUPLICATE KEY UPDATE
                `value` = :value
            '
        ;

        $lastUpdatedDate = (new \DateTime())
            ->format(static::TRANSLATION_LAST_UPDATED_DATE_FORMAT);

        $this->db->executeUpdate(
            $query,
            [
                'langCode' => $locale,
                'name' => static::TRANSLATION_LAST_UPDATED_DATE_KEY,
                'value' => $lastUpdatedDate,
            ]
        );

        return \DateTime::createFromFormat(
            static::TRANSLATION_LAST_UPDATED_DATE_FORMAT,
            $lastUpdatedDate
        );
    }
}
