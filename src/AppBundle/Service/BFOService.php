<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Service;

class BFOService
{
    private string $bfoUrl;
    private bool $isBFOEnabled;

    public function __construct(string $bfoUrl, bool $bfoEnabled)
    {
        $this->bfoUrl = $bfoUrl;
        $this->isBFOEnabled = $bfoEnabled;
    }

    public function getHomeUrl(): string
    {
        return $this->bfoUrl . '/';
    }

    public function getContactPageUrl(): string
    {
        return $this->bfoUrl . '/contact';
    }

    public function getProfileUrl(): string
    {
        return $this->bfoUrl . '/account';
    }

    public function getDiscussUrl(int $discussionId = null): string
    {
        $url = $this->bfoUrl . '/account/messages';
        if (\is_null($discussionId) === false) {
            $url .= '/' . $discussionId;
        }
        return $url;
    }

    public function isBFOEnabled(): bool
    {
        return $this->isBFOEnabled;
    }

    public function setBFOUrl(string $url): void
    {
        $this->bfoUrl = $url;
    }

    public function setBFOEnabled(bool $isBFOEnabled): void
    {
        $this->isBFOEnabled = $isBFOEnabled;
    }
}
