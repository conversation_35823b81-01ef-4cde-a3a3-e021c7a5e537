<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Service;

use Doctrine\DBAL\Connection;

class SettingStorage
{
    /**
     * @var Connection
     */
    private $connection;

    /**
     * @var string
     */
    private $tablePrefix;

    public function __construct(Connection $connection, string $tablePrefix)
    {
        $this->connection = $connection;
        $this->tablePrefix = $tablePrefix;
    }

    public function get(string $key, $default = null)
    {
        $query = $this->connection->executeQuery('SELECT `value` FROM ' . $this->tablePrefix . 'settings_objects WHERE `name` = :name', [
            'name' => $key
        ]);

        $values = $query->fetchColumn();

        if ($values === false) {
            return $default;
        }

        return json_decode($values, true);
    }

    public function set(string $key, $value)
    {
        $isNew = $this->connection->executeQuery('SELECT 1 FROM ' . $this->tablePrefix . 'settings_objects WHERE `name` = :name', [
            'name' => $key
        ])->rowCount() == 0;

        if ($isNew) {
            $this->connection->insert($this->tablePrefix . 'settings_objects', [
                'name' => $key,
                'section_id' => 0,
                'section_tab_id' => 0,
                'type' => 'I',
                'is_global' => 'Y',
                'value' => json_encode($value)
            ]);
        } else {
            $this->connection->update(
                $this->tablePrefix . 'settings_objects',
                [
                    'value' => json_encode($value)
                ],
                [
                    'name' => $key
                ]
            );
        }
    }
}
