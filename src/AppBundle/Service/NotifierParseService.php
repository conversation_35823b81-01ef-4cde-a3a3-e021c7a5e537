<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Service;

use SplFileInfo;

final class NotifierParseService
{
    /** @var FileSystemService */
    private $fileSystemService;

    /** @var array */
    private $list = [];

    public function __construct(FileSystemService $fileSystemService)
    {
        $this->fileSystemService = $fileSystemService;
        $this->fileSystemService->setScope('/src/AppBundle');
    }

    /**
     * Parse every Wizacha\AppBundle\Notification\*Notifier file
     * And extract translations and injected variables
     */
    public function parseNotifierClasses()
    {
        $this->fileSystemService->iterateOverDirectoryFiles(
            '/Notification',
            function (SplFileInfo $splFileInfo) {
                $className = explode('Notifier', $splFileInfo->getFilename());
                $fileContents = $this->fileSystemService->getFileContents($splFileInfo);

                if (false === $fileContents) {
                    return;
                }

                $this
                    ->parseEmailSubjects(
                        $fileContents,
                        function (array $keyVars) use ($className): void {
                            $this->list[mb_strtolower(reset($className))][] = [
                                'key_vars' => $keyVars,
                            ];
                        }
                    )
                    ->parseEmailContents(
                        $fileContents,
                        function (
                            string $category,
                            string $file,
                            array $keyVars
                        ): void {
                            $this->list[$category][] = [
                                'file' => $file,
                                'key_vars' => $keyVars,
                            ];
                        }
                    )
                ;
            }
        );

        ksort($this->list);

        return $this->list;
    }

    /** Find template inclusions and parse them looking for translations */
    private function parseEmailContents(string $fileContents, callable $then): self
    {
        $scope = $this->fileSystemService->getScope();
        preg_match_all("/@App\/mail\/(?<templates>[\w\/\-]+)\.html\.twig/", $fileContents, $matches);

        foreach ($matches['templates'] as $file) {
            $splFileInfo = new SplFileInfo($scope . '/Resources/views/mail/' . $file . '.html.twig');
            $fileContents = $this->fileSystemService->getFileContents($splFileInfo);

            if (false === $fileContents) {
                continue;
            }

            // Find $key|trans($params) calls in template
            preg_match_all(
                "/{{\s*\(*['\"](?<keys>\w+)['\"].*\|trans(\({(?<params>[^}]+)}(.*?)\))*\s*/s",
                $fileContents,
                $keyParams
            );

            if (\count($keyParams['keys']) > 0) {
                $keyVars = [];
                $i = 0;

                preg_match_all(
                    "/{{\s*\(*['\"](?<keys>\w+)['\"].*\|trans.*\|merge(\((?<params>\w+)\))*\s*/s",
                    $fileContents,
                    $keyParams2
                );

                while (\array_key_exists($i, $keyParams['keys'])) {
                    // Get translation key
                    $key = $keyParams['keys'][$i];

                    // Get translation variables
                    preg_match_all("/['\"](?<vars>[^,]+)['\"]:/", $keyParams['params'][$i], $vars);
                    $keyVars[$key] = [
                        'key' => $key,
                        'vars' => $vars['vars'],
                    ];

                    if (\count($keyParams2['keys']) > 0) {
                        for ($j = 0; $j < \count($keyParams2['keys']); $j++) {
                            if ($key === $keyParams2['keys'][$j]) {
                                if ($keyParams2['params'][$j] === 'order_extra') {
                                    $keyVars[$key]['vars'][] = "[" . $keyParams2['params'][$j] . "_%key%]";
                                } else {
                                    $keyVars[$key]['vars'][] = "[" . $keyParams2['params'][$j] . "]";
                                }
                            }
                        }
                    }
                    $i++;
                }

                $category = reset(explode('/', $file));

                ksort($keyVars);
                $then($category, $file, $keyVars);
            }
        }

        return $this;
    }

    /** Find email subjects using translations */
    private function parseEmailSubjects(string $fileContents, callable $then): self
    {
        $keyVars = [];
        preg_match_all("/__\(\s*(?<translations>[^@;]+)(?<=\))/s", $fileContents, $matches);

        foreach ($matches['translations'] as $match) {
            // Get translation key
            preg_match("/['\"](?<key>\w+)['\"]/", $match, $keys);
            $key = $keys['key'];

            // Get translation variables
            preg_match_all("/['\"](?<params>[^'\"]+)['\"] =>/", $match, $params);

            $keyVars[$key] = [
                'key' => $key,
                'vars' => $params['params'],
            ];
        }

        if (\count($keyVars) > 0) {
            ksort($keyVars);
            $then($keyVars);
        }

        return $this;
    }
}
