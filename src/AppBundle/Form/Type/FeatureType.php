<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Form\Type;

use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FeatureType extends CollectionType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('product_features', null);
        $builder->add('add_new_variant', null);
    }

    public function getBlockPrefix()
    {
        return 'feature';
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'allow_add' => true,
        ));
    }
}
