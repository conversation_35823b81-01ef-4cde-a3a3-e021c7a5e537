<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\UrlType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Count as ConstraintCount;
use Wizacha\Marketplace\Webhooks\Webhook;

class WebhookType extends AbstractType
{
    /** @param mixed[] $options */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $allEventReferences = [];

        foreach ($options['allEventReferences'] as $group => $optionEventReferences) {
            foreach ($optionEventReferences as $optionEventReference) {
                $allEventReferences[$optionEventReference->getName()] = $optionEventReference->getName();
            }
        }

        $builder
            ->add(
                'url',
                UrlType::class,
                [
                    'required' => true,
                    'label' => false,
                ]
            )
            ->add(
                'eventReferences',
                ChoiceType::class,
                [
                    'choices' => $allEventReferences,
                    'label' => false,
                    'attr' => [
                        'class' => 'available-events-select',
                    ],
                    'trim' => true,
                    'placeholder' => 'webhooks_notifications_select_events',
                    'required' => false,
                    'constraints' => [
                        new ConstraintCount(['min' => 1])
                    ],
                ]
            )
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'allEventReferences' => [],
            'data_class' => Webhook::class,
        ]);
    }
}
