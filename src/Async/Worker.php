<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Async;

use Doctrine\Bundle\DoctrineBundle\Registry as Doctrine;
use Doctrine\DBAL\Exception\DeadlockException;
use Doctrine\DBAL\Exception\LockWaitTimeoutException;
use Eventio\BBQ\Job\JobInterface;
use Eventio\BBQ\Queue\QueueInterface;
use Monolog\Logger;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Tygh\DatabaseException;
use Wizacha\Async\Exception\UnknownQueueException;
use Wizacha\Async\Exception\WorkerOutOfMessagesException;
use Wizacha\Async\Exception\WorkerSignalReceivedException;
use Wizacha\Async\Exception\WorkerTimeoutException;
use Wizacha\Async\Exception\WorkerUnavailableDispatcherException;
use W<PERSON>cha\BBQ\Job\SqsJob;
use Wizacha\Misc;
use Wizacha\Registry;

/**
 * Class Worker
 * Manages the asynchronous execution loop of a worker queue.
 * @package Wizacha\Async
 */
class Worker
{
    public const POLLING_SLEEP = 1; // seconds

    private bool $signalReceived = false;
    private int $maxTime;
    /** Current environment, with a valid Dispatcher attached to its container */
    private Registry $registry;
    private LoggerInterface $logger;
    /** @var string[] Filter enabled queues for this worker*/
    private array $enabledQueues;
    private int $remainingMsg;
    private Doctrine $doctrine;

    private bool $enableTick = false;

    /**
     * @param string[] $enabledQueues
     */
    public function __construct(
        Registry $registry,
        Doctrine $doctrine,
        LoggerInterface $logger,
        array $enabledQueues,
        int $maxTime,
        int $remainingMsg
    ) {
        $this->registry = $registry;
        $this->doctrine = $doctrine;
        $this->logger = $logger;
        $this->enabledQueues = $enabledQueues;
        $this->maxTime = $maxTime + \time();
        $this->remainingMsg = $remainingMsg;
    }

    public function signalHandler(int $signal): void
    {
        $this->signalReceived = true;
    }

    /**
     * Fetches specified registered queues and executes tasks while allowed by conditions.
     * @note This loop dispatch process signals, so you can use signal handlers to stop it.
     *
     * @throws WorkerUnavailableDispatcherException
     */
    public function run(bool $enableTick = false): void
    {
        $this->enableTick = $enableTick;

        $jobProvider = $this->getJobProvider();

        $dispatcher = $this->getDispatcher();
        if (false === $dispatcher instanceof Dispatcher) {
            throw new WorkerUnavailableDispatcherException();
        }

        $this->loop($jobProvider, $dispatcher);
    }

    // main job waiting loop
    private function loop(JobProvider $jobProvider, Dispatcher $dispatcher): void
    {
        while (true) { // exit by exception
            // job consuming loop
            foreach ($jobProvider->getJobGenerator() as $job) {
                $dispatcher->nextExecIsSynchronous();
                $this->consumeJob($job);

                // check termination conditions
                $this->checkSignal();
                $this->checkMsg();
                $this->checkTtl();
            }

            // no more jobs in queues
            $this->tick('.');
            \sleep(self::POLLING_SLEEP);

            // check termination conditions
            $this->checkSignal();
            $this->checkTtl();
        }
    }

    /**
     * Consume a Job
     */
    private function consumeJob(JobInterface $job): void
    {
        // get a valid message to consume
        /** @var FunctionPayload */
        $payload = $job->getPayload();
        $queue = $job->getQueue();

        if (false === $payload instanceof FunctionPayload) {
            $this->tick('↺');
            $queue->releaseJob($job);
            $this->log(Logger::ERROR, 'Got an invalid payload');

            return;
        }

        $localRegistry = $payload->registry($this->registry);
        $this->resetDoctrineManager();

        try {
            $this->tick('|');
            // L'export csv est un processus qui peut être très long.
            // On finalize tout de suite le job, et si on a un problème en cours de route, on repousse un nouveau message
            if ($queue->getId() === 'csv_exports') {
                $queue->finalizeJob($job);
                $payload->execFunction($localRegistry);
            } else {
                $payload->execFunction($localRegistry);
                $queue->finalizeJob($job);
            }
        } catch (DatabaseException | DeadlockException | LockWaitTimeoutException $exception) {
            $this->log(
                Logger::ERROR,
                'Deadlock exception, just retry',
                [
                    'exception' => $exception,
                    'payload' => $payload->expose(),
                ]
            );

            /**
             * When a deadlock is encoutered, we put the job back in the queue
             * for later retry.
             */
            if ($queue->getId() === 'csv_exports') {
                $this->tick('↺');
                \sleep(self::POLLING_SLEEP);
                $queue->pushJob($payload);
            } else {
                $queue->releaseJob($job);
            }
        } catch (\Exception $exception) {
            /**
             * If a job fails, don't stop the worker.
             */
            $this->tick('💀');
            $this->log(
                Logger::ERROR,
                $exception->getMessage(),
                [
                    'exception' => $exception,
                    'payload' => $payload->expose(),
                ]
            );
            $queue->releaseJob($job);
        }
        --$this->remainingMsg;

        Misc::removeTempData($localRegistry);
    }

    /**
     * Message limit
     *
     * @throws WorkerOutOfMessagesException
     */
    private function checkMsg(): void
    {
        if (0 >= $this->remainingMsg) {
            throw new WorkerOutOfMessagesException();
        }
    }

    /**
     * Time limit
     *
     * @throws WorkerTimeoutException
     */
    private function checkTtl(): int
    {
        $ttl = $this->maxTime - \time();

        if (0 >= $ttl) {
            throw new WorkerTimeoutException();
        }

        return $ttl;
    }

    /**
     * OS signal
     *
     * @throws WorkerSignalReceivedException
     */
    private function checkSignal(): void
    {
        if ($this->signalReceived) {
            throw new WorkerSignalReceivedException();
        }
    }

    /**
     * Legacy: On reset le manager entre chaque message traité
     * par le worker de façon à en avoir un neuf
     * et ainsi que le traitement de chaque messages soit idempotent
     */
    private function resetDoctrineManager(): void
    {
        $this->doctrine->resetManager();
    }

    /** Get the job provider for the specified queues */
    private function getJobProvider(): JobProvider
    {
        if ([] === $this->enabledQueues) {
            return $this->registry->container->get('worker.job_provider');
        }

        $availableQueues = $this->getAvailableQueues();
        $availableQueueNames = \array_keys($availableQueues);

        $filteredQueues = [];
        foreach ($this->enabledQueues as $queue) {
            if (false === \in_array($queue, $availableQueueNames)) {
                throw new UnknownQueueException($queue);
            }

            $filteredQueues[$queue] = $availableQueues[$queue];
        }

        return new JobProvider(
            $this->registry->container->getParameter('marketplace.queue.config'),
            $filteredQueues
        );
    }

    /** Get the async dispatcher trigger an error if invalid */
    private function getDispatcher(): ?Dispatcher
    {
        return $this->registry->container->get(
            'marketplace.async_dispatcher',
            ContainerInterface::NULL_ON_INVALID_REFERENCE
        );
    }

    /**
     * @return QueueInterface[]
     */
    private function getAvailableQueues()
    {
        return $this->registry->container->get('worker.queue_list');
    }

    /**
     * Log a message with the Worker process pid
     *
     * @param int $level
     * @param string $message
     * @param array $context
     */
    private function log(int $level, string $message, array $context = [])
    {
        $context['pid'] = getmypid();
        $this->logger->log($level, $message, $context);
    }

    private function tick(string $char): void
    {
        if ($this->enableTick) {
            echo $char;
        }
    }
}
