<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Async;

use Aws\Sqs\SqsClient;
use Wizacha\Async\Exception\QueueException;

class SqsQueue extends AbstractAsyncQueue
{
    protected const TYPE = 'sqs';

    private SqsClient $sqsClient;
    private string $queueUrl;

    public function __construct(
        SqsClient $sqsClient,
        string $queueUrl
    ) {
        $this->sqsClient = $sqsClient;
        $this->queueUrl = $queueUrl;
    }

    public function getNbOfQueueMessages(string $queueId): int
    {
        try {
            return (int) $this->sqsClient
                ->getQueueAttributes([
                    'QueueUrl' => $this->queueUrl . $queueId,
                    'AttributeNames' => ['ApproximateNumberOfMessages'],
                ])
                ->get('Attributes')
                ;
        } catch (\Exception $e) {
            throw new QueueException('Queue not available', 0, $e);
        }
    }

    public function getType(): string
    {
        return $this::TYPE;
    }
}
