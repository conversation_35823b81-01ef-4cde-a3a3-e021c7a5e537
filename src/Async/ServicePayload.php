<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Async;

use Doctrine\DBAL\Driver\DriverException;
use Wizacha\Registry;

class ServicePayload extends FunctionPayload
{
    protected const MESSAGE_MAX_SIZE = 4096;

    /**
     * Executes the service
     *
     * @param Registry $registry Environment to use for execution
     *
     * @return bool Return true if function is called, false else ($this->function_name is not callable)
     */
    public function execFunction(Registry $registry): bool
    {
        $logger = $registry->container->get('logger');

        try {
            [$service, $method] = explode('::', $this->function_name);
            \call_user_func_array([$registry->container->get($service), $method], $this->args);

            return true;
        } catch (DriverException $e) {
            // If the error message is greater than MESSAGE_MAX_SIZE
            // we truncate the message proprety from exception
            $reflection = new \ReflectionProperty($e, 'message');
            $reflection->setAccessible(true);
            $reflection->setValue(
                $e,
                mb_substr($e->getMessage(), 0, static::MESSAGE_MAX_SIZE)
            );
            throw $e;
        }

        return false;
    }
}
