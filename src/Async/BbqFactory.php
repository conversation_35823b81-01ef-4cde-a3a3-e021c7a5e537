<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Async;

use Aws\Sqs\SqsClient;
use Eventio\BBQ;
use Eventio\BBQ\Queue\DirectoryQueue;
use Eventio\BBQ\Queue\QueueInterface;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use Wizacha\BBQ\Queue\AmqpQueue;
use Wizacha\BBQ\Queue\SqsQueue;
use Wizacha\Registry;

class BbqFactory
{
    /**
     * @var array
     */
    private $queueConfig;

    /**
     * @var string
     */
    private $queueType;

    /**
     * @var string
     */
    private $queuePath;

    private SqsClient $sqsClient;

    /**
     * @var AMQPStreamConnection
     */
    private $amqpConnection;

    public function __construct(
        array $queueConfig,
        string $queueType,
        string $queuePath,
        SqsClient $sqsClient,
        AMQPStreamConnection $amqpConnection
    ) {
        $this->queueConfig = $queueConfig;
        $this->queueType = $queueType;
        $this->queuePath = $queuePath;
        $this->sqsClient = $sqsClient;
        $this->amqpConnection = $amqpConnection;
    }

    public function createBbq(): BBQ
    {
        $bbq = new BBQ();

        foreach ($this->queueConfig as $queueName => $queueParams) {
            if ($queue = $this->createQueue($queueName)) {
                try {
                    $bbq->registerQueue($queue);
                } catch (\Exception $e) {
                }
            }
        }

        return $bbq;
    }

    protected function createQueue($id): ?QueueInterface
    {
        switch ($this->queueType) {
            case \Wizacha\Async\Config::TYPE_SQS:
                try {
                    return new SqsQueue(
                        $id,
                        $this->sqsClient,
                        $this->queuePath . $id
                    );
                } catch (\Exception $e) {
                }
                return null;
            case \Wizacha\Async\Config::TYPE_AMQP:
                return new AmqpQueue($id, $this->amqpConnection);
            case \Wizacha\Async\Config::TYPE_DIRECTORY:
                return new DirectoryQueue(
                    $id,
                    $this->queuePath . $id
                );
            default:
                return null;
        }
    }
}
