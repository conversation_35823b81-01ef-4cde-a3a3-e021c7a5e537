<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Async;

use Eventio\BBQ;
use Psr\Log\LoggerInterface;
use Wizacha\Async\Debouncer\DebouncerService;
use Wizacha\Registry;

/**
 * Class Dispatcher
 * This class manages the asynchronous execution of a function
 *
 * @package Wizacha\Async
 */
class Dispatcher
{
    public const CFG_QUEUE_NAME = 'queue';
    public const CFG_PAYLOAD_CLASS = 'payload';
    public const CFG_REGISTRY_KEYS = 'env';
    public const CFG_FORCE_ASYNC = 'force_async';

    private const ALLOWED_QUEUE_TYPES = [
        'amqp',
        'sqs',
        'directory',
    ];

    private $_functions_schema = [];
    private $_bbq = null;
    private $_oneSynchronousShot = false;
    private $logger;

    /** @var DebouncerService */
    private $debouncerService;

    private $queueType;

    /**
     * @param array $functions_schema Keys are functions name, and values are associative array of parameters, indexed with CFG_*
     * @param BBQ $bbq Queue manager to use
     */
    public function __construct(
        array $functions_schema,
        BBQ $bbq,
        LoggerInterface $logger,
        DebouncerService $debouncerService,
        string $queueType = ''
    ) {
        $this->_functions_schema = $functions_schema;
        $this->_bbq = $bbq;
        $this->logger = $logger;
        $this->debouncerService = $debouncerService;
        $this->queueType = $queueType;
    }

    /**
     * Tells this dispatcher to execute next asynchronous method synchronously.
     * The standard use case is in the asynchrone worker process, where you
     * want to execute immediately a function, but not necessarily sub-functions.
     */
    public function nextExecIsSynchronous()
    {
        $this->_oneSynchronousShot = true;
    }

    /**
     * If function_name is listed in schema, execution is delayed and returns true.
     * Else, caller has to execute the function normally.
     *
     * @param string|Callable $function_name Function to look for in functions schema
     * @param array $args Arguments for function call
     * @param Registry $registry Global context to use for function call. If not set and needed, default instance will be used.
     *
     * @return bool
     */
    public function delayExec(
        $function_name,
        array $args,
        Registry $registry = null,
        array $progress_id = [],
        string $debounceKey = null
    ) {
        if (true === \defined('SYNC')
            && true === SYNC
        ) {
            return false;
        }

        if (false === \in_array($this->queueType, static::ALLOWED_QUEUE_TYPES, true)
            || false === \is_callable($function_name, true, $callable_name)
            || false === \array_key_exists($callable_name, $this->_functions_schema)
        ) {
            return false;
        }

        if (true === $this->_oneSynchronousShot) {
            $this->_oneSynchronousShot = false;

            return false;
        }

        $function_infos = $this->_functions_schema[$callable_name];

        // only delay readmodel projectors exection when sub-called from the worker
        $force_async = (bool) $function_infos[self::CFG_FORCE_ASYNC];
        if (true === \defined('WORKER')
            && false === $force_async
        ) {
            return false;
        }

        //Create Payload
        $payload_class = $function_infos[self::CFG_PAYLOAD_CLASS];
        if (!class_exists($payload_class)) {
            return false;
        }
        $payload = new $payload_class($function_name, $args);
        if (isset($function_infos[self::CFG_REGISTRY_KEYS])) {
            $registry = $registry ?: Registry::defaultInstance();
            $payload->setEnv($registry, $function_infos[self::CFG_REGISTRY_KEYS]);
        }
        if (!empty($progress_id)) {
            $payload->setProgress($progress_id);
        }

        // hold job in debouncer
        if (null !== $debounceKey) {
            return $this->debouncerService->bounceJob(
                $function_infos[self::CFG_QUEUE_NAME],
                $debounceKey,
                $payload
            );
        }

        //Push job
        $jobArgs = [
            'queue' => $function_infos[self::CFG_QUEUE_NAME],
            'payload' => $payload,
        ];

        try {
            return $this->_bbq->pushJob(
                ...array_values($jobArgs)
            );
        } catch (\Exception $e) {
            $this->logger->error('Invalid job: ' . $e->getMessage(), array_merge($jobArgs, ['exception' => $e]));
        }

        return false;
    }

    /**
     * @return BBQ
     */
    public function bbq()
    {
        return $this->_bbq;
    }

    /**
     * @return array
     */
    public function functionSchema()
    {
        return $this->_functions_schema;
    }
}
