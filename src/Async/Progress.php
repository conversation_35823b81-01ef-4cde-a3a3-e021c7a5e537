<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Async;

use Wizacha\Registry;

/**
 * Class Progress
 * Manages the monitoring of asynchronous tasks progress.
 * Informations are stored in persistent registry memory, so it is
 * safe to use it across several processes.
 * @package Wizacha\Async
 */
class Progress
{

    /**
     * Return code for next() function
     */
    public const EXIT_LAST          = 1;
    public const EXIT_NOT_LAST      = 2;
    public const EXIT_FIRST_UNKNOWN = 3;
    public const EXIT_UNKNOWN       = 4;

    /**
     * Fields returned by taskInfos() function
     */
    public const FIELD_QUEUE      = 'queue_id';
    public const FIELD_GROUP      = 'group_id';
    public const FIELD_TASK       = 'task_id';
    public const FIELD_TOTAL      = 'total';
    public const FIELD_COUNT      = 'count';
    public const FIELD_SUCCESS    = 'success';
    public const FIELD_FAIL       = 'fail';


    /**
     * Internal registry keys
     */
    public const _TASK_INFOS      = '_prg_tsk_infos_';
    public const _TASK_CNT        = '_prg_tsk_cnt_';
    public const _TASK_ERRORS_CNT = '_prg_tsk_err_cnt_';

    /**
     * @var \Wizacha\Registry Registry use to store data
     */
    protected $_registry;

    /**
     * Creates an instance, and initalize registry entries
     * @param Registry $registry
     */
    public function __construct(Registry $registry)
    {
        $this->_registry = $registry;
        $this->_registry->registerPermanentCache(self::_TASK_INFOS);
    }

    /**
     * Starts a new progress for a task, and return the corresponding identifier.
     *
     * @param mixed $queue_id (CSV, Images...)
     * @param mixed $group_id Identifier to group tasks (vendor id, product id... )
     * @param mixed $task_id ('Resizing image 123', 'Importing file a.csv'...)
     * @param integer $total Number of steps to proceed in order to execute the task
     * @param string $callback Callable and must be serializable
     * @return array|null Returns null if the total is invalid
     */
    public function start($queue_id, $group_id, $task_id, $total, $callback = '')
    {
        if (!\is_int($total) || $total <= 0) {
            return null;
        }
        $infos_id = $progress_id = [$queue_id, $group_id, $task_id, uniqid()];
        array_unshift($infos_id, self::_TASK_INFOS);
        $this->_registry->set($infos_id, [$total, $callback]);
        //TODO: reset count and errors (as soon as implemented)
        return $progress_id;
    }

    /**
     * Update the current status of a progress with a successful execution
     * @param array $progress_id
     * @return int
     *          EXIT_LAST           This operation is the last of the task
     *          EXIT_NOT_LAST       Some operations are still waiting
     *          EXIT_FIRST_UNKNOWN  An error occurred, and this this the first time for this task
     *          EXIT_UNKNOWN        An error occurred (at least 2 times)
     */
    public function stepSuccess(array $progress_id)
    {
        return $this->next($progress_id, true);
    }

    /**
     * Update the current status of a progress with a correct call to the function but with a wrong return.
     * @param array $progress_id
     * @return int
     *          EXIT_LAST           This operation is the last of the task
     *          EXIT_NOT_LAST       Some operations are still waiting
     *          EXIT_FIRST_UNKNOWN  An error occurred, and this this the first time for this task
     *          EXIT_UNKNOWN        An error occurred (at least 2 times)
     */
    public function stepFailure(array $progress_id)
    {
        return $this->next($progress_id, false);
    }

    /**
     * Update the current status of a progress.
     * @param array $progress_id
     * @param bool $success
     * @return int
     *          EXIT_LAST           This operation is the last of the task
     *          EXIT_NOT_LAST       Some operations are still waiting
     *          EXIT_FIRST_UNKNOWN  An error occurred, and this this the first time for this task
     *          EXIT_UNKNOWN        An error occurred (at least 2 times)
     */
    protected function next(array $progress_id, $success = true)
    {
        $error_id = $count_id = $infos_id = $progress_id;
        array_unshift($infos_id, self::_TASK_INFOS);
        list($total, $callback) = $this->_registry->get($infos_id);
        $exit = null;
        if (!\is_null($total)) {
            array_unshift($count_id, self::_TASK_CNT);
            $count = $this->_registry->cntIncr($count_id);
            if ($success) {
                $this->_registry->cntIncr(array_merge($count_id, [self::FIELD_SUCCESS]));
            }

            switch (true) {
                case $count < $total:
                    $exit = self::EXIT_NOT_LAST;
                    break;
                case $count == $total:
                    $exit = self::EXIT_LAST;
                    break;
                default:
                    $exit = null;
            }
        }
        //Handle errors
        if (!$exit) {
            array_unshift($error_id, self::_TASK_ERRORS_CNT);
            $error_cnt = $this->_registry->cntIncr($error_id);
            $exit =  (($error_cnt == 1) ? self::EXIT_FIRST_UNKNOWN : self::EXIT_UNKNOWN);
        }
        if (\is_callable($callback)) {
            \call_user_func_array($callback, [$exit, $this->taskInfos($progress_id)]);
        }
        return $exit;
    }

    /**
     * Lists tasks, grouped by queue id, and by group id (if any)
     * @param mixed $queue_id see start() function
     * @param mixed $group_id see start() function
     * @return array List of tasks informations, see taskInfos() function
     */
    public function tasks($queue_id, $group_id = null)
    {
        //Collect corresponding tasks
        if ($group_id) {
            $reg_tasks = [
                $group_id => $this->_registry->get([self::_TASK_INFOS, $queue_id, $group_id]),
            ];
        } else {
            $reg_tasks = $this->_registry->get([self::_TASK_INFOS, $queue_id]);
        }

        $tasks = [];
        foreach ($reg_tasks ? : [] as $group_id => $group_tasks) {
            foreach ($group_tasks ? : [] as $task_id => $uniq_tasks) {
                foreach ($uniq_tasks as $unique_id => $infos) {
                    $progress_id = [$queue_id, $group_id, $task_id, $unique_id];
                    $tasks[]     = $this->taskInfos($progress_id);
                }
            }
        }
        return $tasks;
    }

    /**
     * Gives current informations about a task progress
     * @param array $progress_id
     * @return array|null If the progress id is valid, returns an array with corresponding fields
     *      FIELD_QUEUE     queue id (see start() function)
     *      FIELD_GROUP     group_id (see start() function)
     *      FIELD_TASK      task_id (see start() function)
     *      FIELD_TOTAL     total (see start() function)
     *      FIELD_COUNT     current number of steps processed (see next() function)
     */
    public function taskInfos(array $progress_id)
    {
        list($queue_id, $group_id, $task_id,) = $count_id = $infos_id = $progress_id;
        array_unshift($infos_id, self::_TASK_INFOS);
        list($total, $callback) = $this->_registry->get($infos_id);
        if (\is_null($total)) {
            return null;
        }
        array_unshift($count_id, self::_TASK_CNT);
        $count = $this->_registry->cntGet($count_id) ? : 0;
        $count_success = $this->_registry->cntGet(array_merge($count_id, [self::FIELD_SUCCESS])) ? : 0;
        return [
            self::FIELD_QUEUE => $queue_id,
            self::FIELD_GROUP => $group_id,
            self::FIELD_TASK  => $task_id,
            self::FIELD_TOTAL => $total,
            self::FIELD_COUNT => $count,
            self::FIELD_SUCCESS => $count_success,
            self::FIELD_FAIL    => $count - $count_success,
        ];
    }

    /**
     * Delete registry's data about task
     * TODO: delete count (as soon as implemented)
     * @param array $progress_id
     */
    public function deleteTask(array $progress_id)
    {
        array_unshift($progress_id, self::_TASK_INFOS);
        $this->_registry->del($progress_id);
    }
}
