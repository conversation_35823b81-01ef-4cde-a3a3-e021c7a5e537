<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Async\Debouncer;

use Symfony\Component\Console\ConsoleEvents;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\KernelEvents;

class DebouncerEventSubscriber implements EventSubscriberInterface
{
    private DebouncedJobRepository $debouncedJobRepository;

    public function __construct(
        DebouncedJobRepository $debouncedJobRepository
    ) {
        $this->debouncedJobRepository = $debouncedJobRepository;
    }

    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::TERMINATE => 'onTerminate',
            ConsoleEvents::TERMINATE => 'onTerminate',
        ];
    }

    public function onTerminate()
    {
        $this->debouncedJobRepository->commit();
    }
}
