<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Async\Debouncer;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Statement;
use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Core\DoctrineBulkUpsert;

class DebouncedJobRepository
{
    /** @var EntityManagerInterface */
    private $entityManager;

    /** @var Connection */
    private $connection;

    /** @var ?Statement */
    private $expiredStatement = null;

    /** @var ?Statement */
    private $purgeStatement = null;

    private DoctrineBulkUpsert $doctrineBulkUpsert;

    private bool $hasCommited = false;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;

        $this->connection = $this->entityManager->getConnection();

        /**
         * a Worker should not bulk commit
         */
        if (true === \defined('WORKER')) {
            $this->hasCommited = true;
        }
    }

    private function getDoctrineBulkUpsert(): DoctrineBulkUpsert
    {
        return $this->doctrineBulkUpsert ??= new DoctrineBulkUpsert(
            $this->connection,
            $this->getBulkUpsertQuery()
        );
    }

    private function getBulkUpsertQuery(): string
    {
        $tableName = $this->getTableName();

        return <<<SQL
            INSERT INTO
                `$tableName`(
                    `queue`,
                    `debounce_key`,
                    `payload`,
                    `created_at`,
                    `updated_at`,
                    `ttl`,
                    `expires_at`
                )
            VALUES(
                :queue,
                :debounceKey,
                :payload,
                :createdAt,
                :updatedAt,
                :ttl,
                :expiresAt
            )
            ON DUPLICATE KEY
                UPDATE
                    `payload` = :payload,
                    `updated_at` = :updatedAt,
                    `ttl` = :ttl,
                    `expires_at` = :expiresAt,
                    `bounce_counter` = `bounce_counter` + 1
            SQL
        ;
    }

    public function commit()
    {
        /**
         * a bulk commit should only happen once in an API or BO request
         */
        $this->hasCommited = true;

        $this->getDoctrineBulkUpsert()->commit();
    }

    public function upsert(
        string $queue,
        string $debounceKey,
        string $payload,
        int $ttl = DebouncedJob::DEFAULT_TTL
    ): void {
        $createdAt = new \DateTimeImmutable();
        $updatedAt = $createdAt;
        $expiresAt = $updatedAt->add(
            new \DateInterval("PT{$ttl}S")
        );

        $this->getDoctrineBulkUpsert()->add(
            [
                'queue' => $queue,
                'debounceKey' => $debounceKey,
                'payload' => $payload,
                'createdAt' => $createdAt->format(\DateTimeImmutable::ATOM),
                'updatedAt' => $updatedAt->format(\DateTimeImmutable::ATOM),
                'ttl' => $ttl,
                'expiresAt' => $expiresAt->format(\DateTimeImmutable::ATOM),
                'bouncerCounter' => 1,
            ]
        );

        /**
         * worker or upsert happening after first commit should direct commit
         */
        if (true === $this->hasCommited) {
            $this->commit();
        }
    }

    /**
     * release expired bounced jobs.
     *
     * @return \Generator|DebouncedJob[]
     */
    public function getExpired(): \Generator
    {
        $tableName = $this->getTableName();

        $now = new \DateTimeImmutable();

        $this->expiredStatement ??= $this->connection
            ->prepare(
                "SELECT
                    `queue`,
                    `debounce_key`,
                    `payload`
                FROM
                    `$tableName`
                WHERE
                    `expires_at` < :now
                "
            )
        ;

        $this->expiredStatement->execute(
            ['now' => $now->format(\DateTimeImmutable::ATOM)]
        );

        $stayingAlive = 0;
        while ($row = $this->expiredStatement->fetch()) {
            $stayingAlive++;
            if ($stayingAlive >= 128) {
                /**
                 * Azure context: pushing job to AWS sqs takes ages
                 * and MySQL kick out the connection...
                 */
                $this->connection->ping();
                $stayingAlive = 0;
            }

            yield new DebouncedJob(
                $row['queue'],
                $row['debounce_key'],
                $row['payload']
            );
        }

        $this->purgeStatement ??= $this->connection
            ->prepare(
                "DELETE FROM
                    `$tableName`
                WHERE
                    `expires_at` < :now
                "
            );

        $this->purgeStatement->execute(
            ['now' => $now->format(\DateTimeImmutable::ATOM)]
        );
    }

    public function getStats(): array
    {
        $tableName = $this->getTableName();

        $statement = $this->connection
            ->prepare(
                "SELECT
                    COUNT(*) as jobs,
                    SUM(`bounce_counter`) as bounce_total
                FROM
                    `$tableName`
                    "
            )
        ;
        $statement->execute();

        return \array_map(
            'intval',
            $statement->fetch()
        );
    }

    public function getFirsts(int $limit = 64): array
    {
        $query = $this
            ->entityManager
            ->createQuery(
                \sprintf(
                    "SELECT
                        dj
                    FROM
                    %s dj
                    ",
                    DebouncedJob::class
                )
            )
        ;
        $query->setMaxResults($limit);

        return \array_map(
            function (DebouncedJob $job) {
                return $job->expose();
            },
            $query->getResult()
        );
    }

    private function getTableName()
    {
        return $this->entityManager
            ->getClassMetadata(DebouncedJob::class)
            ->getTableName()
        ;
    }
}
