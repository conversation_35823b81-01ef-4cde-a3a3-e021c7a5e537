<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Async\Debouncer;

use Wizacha\Async\FunctionPayload;

class DebouncedJob
{
    public const DEFAULT_TTL = 60; # default TTL in sec.

    /** @var string */
    private $queue;

    /** @var string */
    private $debounceKey;

    /** @var string */
    private $payload;

    /** @var \DateTimeImmutable */
    private $createdAt;

    /** @var \DateTimeImmutable */
    private $updatedAt;

    /** @var int */
    private $ttl;

    /** @var \DateTimeImmutable */
    private $expiresAt;

    /** @var int */
    private $bounceCounter;

    /** @var FunctionPayload */
    private $jobPayload = null;

    public function __construct(
        string $queue,
        string $debounceKey,
        string $payload
    ) {
        $this->queue = $queue;
        $this->debounceKey = $debounceKey;
        $this->payload = $payload;
    }

    public function getQueue(): string
    {
        return $this->queue;
    }

    public function getDebounceKey(): string
    {
        return $this->debounceKey;
    }

    public function getPayload(): string
    {
        return $this->payload;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): \DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function getTtl(): int
    {
        return $this->ttl;
    }

    public function getBounceCounter(): int
    {
        return $this->bounceCounter;
    }

    public function getExpiresAt(): \DateTimeInterface
    {
        return $this->expiresAt;
    }

    public function getJobPayload(): FunctionPayload
    {
        $this->jobPayload = $this->jobPayload
            ?? \unserialize($this->payload);

        return $this->jobPayload;
    }

    public function expose(): array
    {
        return [
            'queue' => $this->getQueue(),
            'debounceKey' => $this->getDebounceKey(),
            'payload' => $this->getJobPayload()->expose(),
            'bounceCounter' => $this->getBounceCounter(),
            'createdAt' => $this->getCreatedAt()->format(\DateTimeInterface::ATOM),
            'expiresAt' => $this->getExpiresAt()->format(\DateTimeInterface::ATOM),
        ];
    }
}
