<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Async\Debouncer;

use Eventio\BBQ;
use Eventio\BBQ\BBQException;
use Eventio\BBQ\Job\Payload\JobPayloadInterface;
use Psr\Log\LoggerInterface;

class DebouncerService
{
    /** @var DebouncedJobRepository */
    public $debouncedJobRepository;

    /** @var BBQ */
    private $bbq;

    /** @var LoggerInterface */
    private $logger;

    public function __construct(
        DebouncedJobRepository $debouncedJobRepository,
        BBQ $bbq,
        LoggerInterface $logger
    ) {
        $this->debouncedJobRepository = $debouncedJobRepository;
        $this->bbq = $bbq;
        $this->logger = $logger;
    }

    public function bounceJob(
        string $queue,
        string $debounceKey,
        JobPayloadInterface $payload,
        int $ttl = DebouncedJob::DEFAULT_TTL
    ): bool {
        $this->debouncedJobRepository->upsert(
            $queue,
            $debounceKey,
            \serialize($payload),
            $ttl
        );

        return true;
    }

    /**
     * @return array<int,int> int processed job, number of errors encountered while processing debounced jobs
     */
    public function processExpired(): array
    {
        $processed = 0;
        $nbErrors = 0;

        /** @var DebouncedJob $debouncedJob */
        foreach ($this->debouncedJobRepository->getExpired() as $debouncedJob) {
            try {
                $this->bbq->pushJob(
                    $debouncedJob->getQueue(),
                    $debouncedJob->getJobPayload()
                );
                ++$processed;
            } catch (\TypeError | BBQException $exception) {
                $this->logger->error(
                    'DebouncerService could not push async job',
                    [
                        'queue' => $debouncedJob->getQueue(),
                        'debounceKey' => $debouncedJob->getDebounceKey(),
                        'payload' => $debouncedJob->getPayload(),
                        'exception' => $exception,
                    ]
                );
                ++$nbErrors;
            }
        }

        return [$processed, $nbErrors];
    }

    /** @return int[] */
    public function getStats()
    {
        return $this->debouncedJobRepository->getStats();
    }
}
