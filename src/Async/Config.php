<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Async;

class Config
{
    public const CFG_SQS = 'sqs';
    public const SQS_CONFIG = 'config';

    public const CFG_QUEUES = 'queues';
    public const QUEUE_PATH = 'path';
    public const QUEUE_CONFIG = 'config';
    public const QUEUE_TYPE = 'type';

    public const TYPE_SQS = 'sqs';
    public const TYPE_DIRECTORY = 'directory';
    public const TYPE_AMQP = 'amqp';

    public const Q_MAIL_ID      = 'mailsend';
    public const Q_CSV_ID       = 'CSV';
    public const Q_THUMBNAIL_ID = 'thumbnail';
    public const Q_CACHE_ID     = 'cache';
    public const Q_PRODUCTS_ID  = 'products';
    public const Q_MVP_ID       = 'multi_vendor_products';
    public const Q_VIDEOS_ID    = 'videos';
    public const Q_SEARCH_ENGINE_ID    = 'search_engine';

    public const FIELD_PROGRESS_ID = '#progress_id';
}
