<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Async;

class QueueServiceFactory
{
    /** @var AbstractAsyncQueue[] */
    private $queues;
    private $queueType;

    public function __construct(
        iterable $queues,
        string $queueType = ''
    ) {
        $this->queues = $queues;
        $this->queueType = $queueType;
    }

    public function get(): ?AbstractAsyncQueue
    {
        foreach ($this->queues as $queue) {
            if ($this->queueType === $queue->getType()) {
                return $queue;
            }
        }

        return null;
    }
}
