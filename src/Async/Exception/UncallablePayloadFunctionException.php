<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Async\Exception;

class UncallablePayloadFunctionException extends \RuntimeException
{
    public function __construct(string $functionName)
    {
        parent::__construct("Function [$functionName] is not callable in Async message payload.");
    }
}
