<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Async;

use Wizacha\Async\Exception\UncallablePayloadFunctionException;

class FunctionPayload extends \Eventio\BBQ\Job\Payload\JsonSerializedPayload
{
    public const TYPE_LOG        = 'async';
    public const ACTION_ON_ERROR = 'progress_fail';
    public const ACTION_ON_LAST  = 'progress_finalize';
    /**
     * Must be callable
     * @var string
     */
    protected $function_name = '';

    /**
     * Arguments passed to function
     * @var array
     */
    protected $args = [];

    /**
     * Registry keys for function execution
     * @var array
     */
    protected $env = [];

    /**
     * Progress id for job
     * @var array
     */
    protected $progress_id = [];

    /**
     * Class for logging
     * @var string
     */
    protected $logger_class = '\Wizacha\Logger';

    /**
     * @param callable $function
     * @param array $args
     */
    public function __construct($function, array $args)
    {
        $this->function_name = $function;
        $this->args = $args;
    }

    /**
     * Copy key->values from registry
     * @param \Wizacha\Registry $registry
     * @param array $keys
     */
    public function setEnv(\Wizacha\Registry $registry, array $keys)
    {
        if (!\in_array('area', $keys)) {
            $keys[] = ['area'];
        }

        $this->env = array_reduce(
            $keys,
            function (&$return, $key) use ($registry) {
                $return[] = ['key' => $key, 'value' => $registry->get($key)];
                return $return;
            }
        );
    }

    /**
     * @param array $progress_id
     */
    public function setProgress(array $progress_id)
    {
        $this->progress_id = $progress_id;
    }

    /**
     * @return array
     */
    public function progress()
    {
        return $this->progress_id;
    }

    /**
     * @param string $class
     */
    public function setLogger($class)
    {
        if (class_exists($class)) {
            $this->logger_class = $class;
        }
    }

    /**
     * @return string
     */
    public function logger()
    {
        return $this->logger_class;
    }



    /**
     * @return string
     */
    public function functionName()
    {
        return $this->function_name;
    }

    public function setFunctionName($name)
    {
        $this->function_name = $name;
    }

    /**
     * @return array
     */
    public function args()
    {
        return $this->args;
    }

    /**
     * Create a new registry with key-value from $this->env
     * @return \Wizacha\Registry
     */
    public function registry(\Wizacha\Registry $current_registry = null)
    {
        if (!$current_registry) {
            $current_registry = new \Wizacha\Registry();
        }

        $registry = clone $current_registry;

        array_map(
            function ($local_cache) use ($registry) {
                $registry->set($local_cache['key'], $local_cache['value']);
            },
            $this->env
        );

        //Create a new view if view exist
        if ($registry->get(['view'])) {
            $old_registry = \Wizacha\Registry::defaultInstance();
            \Wizacha\Registry::setDefaultRegistry($registry);
            fn_init_templater($registry->get([\Wizacha\Config::REG_AREA]));
            \Wizacha\Registry::setDefaultRegistry($old_registry);
        }


        return $registry;
    }

    /**
     * Executes the function
     * @param \Wizacha\Registry $registry Environment to use for execution
     * @return void
     */
    public function execFunction(\Wizacha\Registry $registry)
    {
        if (false === \is_callable($this->function_name)) {
            throw new UncallablePayloadFunctionException($this->function_name);
        }

        $old_registry = \Wizacha\Registry::defaultInstance();
        \Wizacha\Registry::setDefaultRegistry($registry);

        $return = \call_user_func_array($this->function_name, $this->args);
        if (!empty($this->progress_id)) {
            $progress = new \Wizacha\Async\Progress($registry);
            if ($progress) {
                $exit_code = ($return ?
                    $progress->stepSuccess($this->progress_id) :
                    $progress->stepFailure($this->progress_id)
                );
                $this->callback($exit_code, $progress);
            }
        }

        \Wizacha\Registry::setDefaultRegistry($old_registry);
    }

    protected function callback($exit_code, Progress $progress)
    {
        $task_infos = $progress->taskInfos($this->progress_id);
        switch ($exit_code) {
            case Progress::EXIT_NOT_LAST:
            case Progress::EXIT_UNKNOWN:
                break;
            case Progress::EXIT_FIRST_UNKNOWN:
                $logger = new $this->logger_class($task_infos[Progress::FIELD_GROUP], 0, self::TYPE_LOG, self::ACTION_ON_ERROR);
                $logger->addMessage(self::ACTION_ON_ERROR, __('w_fail_progress', ['[progress_id]' => implode('/', $this->progress_id)]));
                $progress->deleteTask($this->progress_id);
                break;
            case Progress::EXIT_LAST:
                $logger = new $this->logger_class($task_infos[Progress::FIELD_GROUP], 0, self::TYPE_LOG, self::ACTION_ON_LAST);
                $logger->addMessage(
                    self::ACTION_ON_LAST,
                    __(
                        'w_success_progress',
                        [
                            '[task_id]'     => $task_infos[Progress::FIELD_TASK],
                            '[success_step]' => $task_infos[Progress::FIELD_SUCCESS],
                            '[fail_step]'   => $task_infos[Progress::FIELD_FAIL],
                        ]
                    )
                );
                $progress->deleteTask($this->progress_id);
                break;
        }
    }

    public function expose()
    {
        return [
            'function' => $this->function_name,
            'args' => $this->args,
        ];
    }
}
