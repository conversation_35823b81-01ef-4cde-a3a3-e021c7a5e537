<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Async;

use Eventio\BBQ;
use Eventio\BBQ\Queue\AbstractQueue;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use Wizacha\Async\Exception\QueueException;

class AmqpQueue extends AbstractAsyncQueue
{
    protected const TYPE = 'amqp';

    private BBQ $bbq;
    private AMQPStreamConnection $amqpConnection;

    public function __construct(
        BBQ $bbq,
        AMQPStreamConnection $amqpConnection
    ) {
        $this->bbq = $bbq;
        $this->amqpConnection = $amqpConnection;
    }

    public function getNbOfQueueMessages(string $queueId): int
    {
        try {
            /** @var AbstractQueue $queue */
            $queue = $this->bbq->getQueue($queueId);

            $channel = $this->amqpConnection->channel();

            return (int) $channel
                ->queue_declare(
                    $queue->getId(),
                    true
                )[1]
            ;
        } catch (\Throwable $e) {
            throw new QueueException('Queue not available', 0, $e);
        }
    }

    public function getType(): string
    {
        return $this::TYPE;
    }
}
