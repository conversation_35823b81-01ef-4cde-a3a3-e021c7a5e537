<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Async;

use Eventio\BBQ\BBQException;
use Eventio\BBQ\Job\JobInterface;
use Wizacha\Registry;

class JobProvider
{
    /**
     * Queue ids grouped by priority
     * @var array
     */
    protected $queue_priority = [];

    /**
     * @var \Eventio\BBQ\Queue\QueueInterface[]
     */
    protected $queues;

    public function __construct(array $queues_config, array $queues)
    {
        $this->queues = $queues;
        foreach ($queues_config as $queue_id => $queue_data) {
            if ($queues[$queue_id]) {
                $priority = $queue_data['priority'] ?: 0;
                $this->queue_priority[$priority][] = $queue_id;
            }
        }
        krsort($this->queue_priority);
    }


    /**
     * @return JobInterface[]|\Generator
     */
    public function getJobGenerator(): \Generator
    {
        $called = false;
        do {
            foreach ($this->queue_priority as $grouped_queues) {
                $called = false;

                foreach ($grouped_queues as $queue_id) {
                    $queue = $this->queues[$queue_id];
                    try {
                        if ($message = $queue->fetchJob()) {
                            $called = true;
                            yield $message;
                        }
                    } catch (BBQException $exception) {
                        Registry::defaultInstance()->container->get('logger')->warning(
                            'Error while retrieving job.',
                            [
                                'exception' => $exception,
                            ]
                        );
                    }
                }

                //restart from first group when a message is found in a group
                if ($called) {
                    break;
                }
            }
        } while ($called);
    }
}
