<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Wizacha\Marketplace\GlobalState\GlobalState;

/**
 * @deprecated That should be moved into the PIM module.
 */
class Option
{
    public const EVENT_UPDATE = 'option.update';
    public const OPTION_POSITION = 65535;
    public const OPTION_NAME     = "Déclinaison";

    /*
     * Update generic default declination for vendors
     */
    public static function updateOptions()
    {
        //Retrieve root categories
        $root_categories = fn_get_subcategories(0);
        $root_categories = array_column($root_categories, 'category_id');
        $root_categories = implode(',', $root_categories);

        //Update existing options
        \Tygh\Database::query(
            "UPDATE ?:product_options SET ?u
             WHERE `company_id`!=0 AND `status`='A' AND `position`=?i",
            ['w_categories_path' => $root_categories],
            Option::OPTION_POSITION
        );
        \Tygh\Database::query(
            "UPDATE ?:product_options_descriptions AS pod
             JOIN  `?:product_options` AS po ON po.option_id = pod.option_id
             SET ?u
             WHERE lang_code=?s AND `company_id`!=0 AND `status`='A' AND `position`=?i",
            ['option_name' => self::OPTION_NAME],
            (string) GlobalState::contentLocale(),
            self::OPTION_POSITION
        );
    }

    /**
     * Filter options of a product according to a set of exceptions
     * @param array $product_options see 'options' format of fn_gather_additional_product_data
     * @param array $exceptions see format of \Wizacha\Product::getExceptions
     * @return array
     */
    public static function filterExceptions(array $product_options, array $exceptions)
    {
        foreach ($exceptions as $option_id => $variants) {
            if (isset($product_options[$option_id]) && \is_array($product_options[$option_id]['variants'])) {
                $product_options[$option_id]['variants'] = array_intersect_key(
                    $product_options[$option_id]['variants'],
                    $variants
                );
            }
        }
        return $product_options;
    }

    /**
     * Check if a combination of options values is valid according to a set of exceptions
     * @param array $combination [$option_id1 => $variant_id1, ... ]
     * @param array $exceptions see format of \Wizacha\Product::getExceptions
     * @return bool
     */
    public static function isCombinationValid(array $combination, array $exceptions)
    {
        if (empty($exceptions)) {
            return true;
        }
        foreach ($combination as $option_id => $variant_id) {
            if (!isset($exceptions[$option_id][$variant_id])) {
                return false;
            }
        }
        return true;
    }
}
