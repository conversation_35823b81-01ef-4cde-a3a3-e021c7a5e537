<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha;

/**
 * This class is the right way to get a Wizacha\Product instance.
 * It tries to alleviate the lack of Doctrine\Common\Persistence\ObjectManager for these entities.
 */
class ProductManager
{
    /**
     * Holds Wizacha\Product instances indexed by their id.
     *
     * @var Product[]
     */
    private $cache = [];

    public function find(int $productId): Product
    {
        if (false === \array_key_exists($productId, $this->cache)
            || false ===  $this->cache[$productId] instanceof Product
        ) {
            $this->cache[$productId] = new Product($productId);
        }

        return $this->cache[$productId];
    }

    public function getCache(): array
    {
        return $this->cache;
    }

    public function clear(): void
    {
        $this->cache = [];
    }

    /**
     * Cache invalidation from legacy methods
     */
    public function invalidate(int $productId): void
    {
        unset($this->cache[$productId]);
    }
}
