<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

/**
 * @deprecated Legacy class. Should be moved to a proper service in a Tax module.
 */
class Tax
{
    /**
     * @return integer
     */
    public static function getFullRateId()
    {
        static $tva_id;
        if (!isset($tva_id)) {
            $reg_number = \Tygh\Settings::instance()->getSettingDataByName('w_full_tva_register_number')['value'];
            $tva_id = \Tygh\Database::getField("SELECT tax_id FROM ?:taxes WHERE regnumber=?s", $reg_number);
        }
        return $tva_id;
    }

    /**
     * @return integer
     */
    public static function getLowestRateId()
    {
        return \Tygh\Database::getField("SELECT ?:taxes.tax_id FROM ?:taxes LEFT JOIN ?:tax_rates ON ?:tax_rates.tax_id = ?:taxes.tax_id ORDER BY rate_value ASC LIMIT 0,1;");
    }

    /**
     * @return float
     */
    public static function getFullRateValue()
    {
        static $rate_value;
        if (!isset($rate_value)) {
            $default_destination = fn_get_destinations()[0]['destination_id'];
            $tax = fn_get_tax_rates(\Wizacha\Tax::getFullRateId(), $default_destination);
            $rate_value = \floatval($tax[0]['rate_value'] / 100);
        }

        return $rate_value;
    }

    /**
     * @param string $tax as in CSV ('TVA 20%')
     * @param string $lang_code
     * @return int tax_id
     */
    public static function getIdFromTax(string $tax, string $lang_code)
    {
        static $taxes;
        if (!isset($taxes)) {
            $taxes = \Tygh\Database::getHash(
                'SELECT * from ?:tax_descriptions WHERE lang_code = ?s',
                'tax',
                $lang_code
            );
        }

        if (isset($taxes[$tax])) {
            return $taxes[$tax]['tax_id'];
        }

        return 0;
    }
}
