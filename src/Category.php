<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Tygh\Database;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Marketplace\SeoData;

/**
 * @deprecated
 * - For PIM: use Wizacha\Marketplace\PIM\Category\Category
 * - For catalog: use Wizacha\Marketplace\Catalog\Category\Category
 * @see \Wizacha\Marketplace\PIM\Category\Category
 * @see \Wizacha\Marketplace\Catalog\Category\Category
 */
class Category
{
    public const GARBAGE_CATEGORY_NAME     = 'Echec import CSV';
    public const GARBAGE_CATEGORY_POSITION = 9999;

    public const EVENT_UPDATE = 'category.update';
    public const EVENT_DELETE = 'category.delete';

    /**
     * @var integer
     */
    protected $id = 0;

    /**
     * @var array
     */
    private $_data = [];

    /**
     * @var Category[]
     */
    private $_children_data = [];

    /**
     * @param integer $category_id
     * @throws \InvalidArgumentException
     */
    public function __construct($category_id)
    {
        if (!is_numeric($category_id)) {
            throw new \InvalidArgumentException("Expected integer, got " . \gettype($category_id));
        }
        $this->id = \intval($category_id);
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Load data from database if not already done
     * @return array|bool
     */
    private function _getData()
    {
        if (!$this->_data) {
            $this->_data = fn_get_category_data($this->id);
        }
        return $this->_data;
    }

    /**
     * Load data from database if not already done
     * @return array
     */
    private function _getChildrenData()
    {
        if (!$this->_children_data) {
            $this->_children_data = array_map(
                function ($cateogryId) {
                    return new self($cateogryId);
                },
                Database::getColumn(
                    'SELECT category_id FROM ?:categories WHERE parent_id = ?i',
                    $this->id
                )
            );
        }
        return $this->_children_data;
    }

    /**
     * Save data
     * @return void
     */
    public function save()
    {
        if (!$this->_data) {
            return;
        }
        $this->id = fn_update_category($this->_data, $this->id);
    }

    /**
     * Parent categories, ordered from root to direct parent
     * @return Category[]
     */
    public function getCategoriesPath(): array
    {
        $path = [];
        foreach ($this->getPathIds() as $category_id) {
            $path[] = new self($category_id);
        }
        return $path;
    }

    /**
     * Return IDs of the category and its parents, ordered from root to direct parent
     * @return int[]
     */
    public function getPathIds(): array
    {
        $path = (string) $this->_getData()['id_path'];
        return $path ? explode('/', $path) : [];
    }

    /**
     * Use in product_update.
     * @param array $categories
     * @return array
     */
    public static function removeGarbageCategories($categories)
    {
        //only if Garbage is not the only one category
        if (\count($categories) > 1) {
            $garbage_id = \Wizacha\Category::getGarbageCategoryId();
            $categories = array_values(
                array_diff($categories, [$garbage_id])
            );
            if (empty($categories)) {
                $categories = [$garbage_id];
            }
        }
        return $categories;
    }

    /**
     * If garbage category doesn't exist, create it.
     * @return int garbage category id
     */
    public static function getGarbageCategoryId($clearStaticCache = false)
    {
        static $category_id = 0;
        if (!$category_id || $clearStaticCache) {
            $category_id = \Tygh\Database::getField(
                "SELECT ?:categories.category_id
                FROM ?:categories
                WHERE parent_id = 0 AND position = ?i",
                self::GARBAGE_CATEGORY_POSITION
            );
            if (!$category_id) {
                $category_data = array(
                    'parent_id' => 0,
                    'category' =>  self::GARBAGE_CATEGORY_NAME,
                    'status' => \Wizacha\Status::HIDDEN,
                    'position' => self::GARBAGE_CATEGORY_POSITION
                );

                $category_id = (int) fn_update_category($category_data, 0, (string) GlobalState::contentLocale());
            }
        }
        return $category_id;
    }

    /**
     * Return if category is one of the mess categories
     *
     * @param int $id category id
     * @return bool
     */
    public static function isMessCategory($id)
    {
        $position = \Tygh\Database::getField(
            'SELECT position FROM ?:categories WHERE category_id = ?i',
            $id
        );
        return $position == self::GARBAGE_CATEGORY_POSITION;
    }

    /** Category are updated with default meta SEO values when fields are empty.
     * @param array $category_data
     * @param string $lang_code
     * @return array
     */
    public static function setDefaultValues($category_data, $lang_code)
    {
        $default_meta = function ($field) use (&$category_data) {
            if (isset($category_data[$field]) && empty($category_data[$field])) {
                $category_data[$field] = __(
                    "w_default_category_$field",
                    ['[category_name]' => $category_data['category']]
                );
            }
        };

        $default_meta('page_title');
        $default_meta('meta_description');

        $default_keywords = array();
        if (isset($category_data['meta_keywords']) && !$category_data['meta_keywords']) {
            $default_keywords[] = $category_data['category'];
            if (isset($category_data['parent_id']) && $category_data['parent_id'] > 0) {
                $default_keywords[] = fn_get_category_name($category_data['parent_id'], $lang_code);
            }
            $category_data['meta_keywords'] = __(
                'w_default_category_meta_keywords',
                ['[values]' => implode(', ', $default_keywords)]
            );
        }
        return $category_data;
    }

    /**
     * Return true if category or sub-categories has products
     * @param integer $category_id
     * @return bool
     */
    public static function hasProducts($category_id)
    {
        if (!$category_id) {
            return false;
        }
        //More readable with two request.
        $path = \Tygh\Database::getField("SELECT id_path FROM ?:categories WHERE category_id = ?i", $category_id);
        $has_product = \Tygh\Database::getField(
            'SELECT 1
             FROM ?:products_categories as pc
             WHERE pc.category_id IN
                (SELECT c.category_id
                 FROM ?:categories as c
                 WHERE id_path LIKE "' . $path . '/%" or id_path="' . $path . '"
                 )
             LIMIT 1;'
        );

        // Check MVP only if we don't have products to prevent useless queries
        if (!$has_product) {
            $has_product = \Tygh\Database::getField(
                'SELECT 1
             FROM doctrine_multi_vendor_product as mvp
             WHERE mvp.category_id IN
                (SELECT c.category_id
                 FROM ?:categories as c
                 WHERE id_path LIKE "' . $path . '/%" or id_path="' . $path . '"
                 )
             LIMIT 1;'
            );
        }

        return \boolval($has_product);
    }

    public static function search(string $looking_for, string $lang_code, array $search_params = []): array
    {
        $params = [
            'limit'       => \Tygh\Registry::get('settings.Appearance.admin_elements_per_page'),
            'page'        => 1,
            'category_id' => '0',
        ];
        $params = array_merge($params, $search_params);

        $params['w_search']['looking_for'] = $looking_for;
        $params['group_by_level'] = false;
        $params['simple'] = false;
        $params['plain'] = true;

        return fn_get_categories($params, $lang_code, true);
    }

    /**
     * Returns the front-end url of a category
     * @param integer $category_id
     * @return string
     */
    public static function frontUrl($category_id, $protocol = 'https')
    {
        return fn_url(
            'categories.view?category_id=' . $category_id,
            'C',
            $protocol
        );
    }

    /**
     * Returns a PdoColumnIterator to iterate on all ids
     * @return \Wizacha\Core\Iterator\PdoColumnIterator
     */
    public static function allIds()
    {
        return new \Wizacha\Core\Iterator\PdoColumnIterator(
            \Tygh\Database::prepare('SELECT category_id FROM ?:categories')
        );
    }

    /**
     * Return data for block category
     * @return array ['category_data' => array, 'subcategories' => array]
     */
    public static function getContent($value, $block, $block_scheme)
    {
        $c_id = $block['object_id'];
        $sub_cat = fn_get_subcategories($c_id);
        foreach ($sub_cat as $k => $v) {
            $sub_cat[$k]['main_pair'] = \Wizacha\Category::getImage($v);
        }

        return [
            'category_data' => fn_get_category_data($c_id),
            'subcategories' => $sub_cat,
        ];
    }

    public function getMainImage(): ?Image
    {
        $imageData = self::getImage([
            'category_id' => $this->id,
            'id_path' => $this->_getData()['id_path'],
        ]);
        if (!$imageData) {
            return null;
        }
        return Image::fromCsCartData($imageData);
    }

    /**
     * Return the main category image (and try to set one if the category hasn't)
     * @param array $category_data
     * @return array|null image_pair
     * @deprecated Use getMainImage() instead.
     * @see \Wizacha\Category::getMainImage()
     */
    public static function getImage(array $category_data)
    {
        if ($category_pair = fn_get_image_pairs($category_data['category_id'], 'category', 'M')) {
            return $category_pair;
        }

        return [];
    }

    /**
     * Recount all products in category
     */
    public static function synchronizeCount()
    {
        db_query("UPDATE ?:categories SET product_count=0, visible_product_count=0");

        // key category_id value parent_id
        $allCategoriesIds = db_get_hash_single_array("SELECT category_id, parent_id FROM ?:categories", ['category_id', 'parent_id']);

        // On ne veut que les feuilles = les catégories qui n'ont jamais leur ID dans parent_id
        $leafIds = array_diff(array_keys($allCategoriesIds), $allCategoriesIds);

        // On récupere les catégories qui ont pour id_path leur ID (feuille de la racine) ou un id_path qui se termine par leur
        container()->get('marketplace.pim.category_service')->updateProductCount($leafIds);
    }

    /**
     * Get the transactional mode according to its parents
     */
    public function getTransactionMode(): TransactionMode
    {
        $currentMode = new TransactionMode($this->_getData()['transaction_mode']);
        if ($currentMode->equals(TransactionMode::INHERITED())) {
            if (!empty($this->_getData()['parent_id'])) { // only if this category has a parent
                $parentIds = array_reverse($this->getPathIds());
                $dbal = container()->get('doctrine.dbal.default_connection');
                $stmt = $dbal->executeQuery(
                    'SELECT category_id, transaction_mode FROM cscart_categories WHERE transaction_mode != ? AND category_id IN (?)',
                    [
                        TransactionMode::INHERITED,
                        $parentIds,
                    ],
                    [
                        \PDO::PARAM_STR,
                        \Doctrine\DBAL\Connection::PARAM_INT_ARRAY
                    ]
                );

                $modesByCategoryIds = $stmt->fetchAll(\PDO::FETCH_KEY_PAIR);
                foreach ($parentIds as $parentId) {
                    // Go through parents one by one, and take the first mode which is not INHERITED
                    if (isset($modesByCategoryIds[$parentId])) {
                        $currentMode = new TransactionMode($modesByCategoryIds[$parentId]);
                        break;
                    }
                }
            }

            if ($currentMode->equals(TransactionMode::INHERITED())) {
                // All the parents categories have INHERITED as mode, so we return the default mode
                return container()->get('marketplace.transaction_mode.service')->getDefaultMode();
            }
        }

        return $currentMode;
    }

    public function setTransactionMode(TransactionMode $mode): Category
    {
        $this->_getData();
        $this->_data['transaction_mode'] = $mode->getValue();
        return $this;
    }

    /**
     * Check if this category is transactional according to its parents
     */
    public function isTransactional(): bool
    {
        return $this->getTransactionMode() == TransactionMode::TRANSACTIONAL;
    }

    public function isContactOnly(): bool
    {
        return $this->getTransactionMode() == TransactionMode::CONTACT_ONLY;
    }
    /**
     * Whether the transaction mode can be overridden by each product in the category.
     */
    public function isTransactionModeOverridable(): bool
    {
        return $this->_getData()['transaction_mode_overridable'] === 'Y';
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->_getData()['category'];
    }

    /**
     * @return Status
     */
    public function getStatus()
    {
        return new Status($this->_getData()['status']);
    }

    /**
     * @return string
     */
    public function getMetaKeywords()
    {
        return $this->_getData()['meta_keywords'];
    }

    /**
     * Categories visible for vendors
     * @return array of Category
     */
    public function getSubCategoriesVendor()
    {
        return array_filter(
            $this->_getChildrenData(),
            [new \ReflectionMethod($this, 'isVisibleForVendor'), 'invoke']
        );
    }

    public function getParentCategory(): Category
    {
        return new Category($this->_getData()['parent_id']);
    }

    public function getAgeLimit(): int
    {
        return $this->_getData()['age_limit'];
    }

    /**
     * Returns the category level, 1 being the first level (root category).
     */
    public function getLevel(): int
    {
        $path = (string) $this->_getData()['id_path'];
        return substr_count($path, '/') + 1;
    }

    public function isStored(): bool
    {
        return \boolval($this->_getData());
    }

    public function isLeaf(): bool
    {
        return !$this->id || !\Tygh\Database::getField("SELECT category_id FROM ?:categories WHERE parent_id=?i LIMIT 1", $this->id);
    }

    public function isApiFillable(): bool
    {
        return ($this->_getData() !== false) && $this->isLeaf() && ($this->isTransactional() || $this->isContactOnly());
    }

    /**
     * @return bool True if the category is viewable with direct url. This is not the same as 'searchable'
     */
    public function isVisibleForPublic(): bool
    {
        return $this->_getData()['status'] != \Wizacha\Status::DISABLED;
    }

    public function isVisibleForVendor(): bool
    {
        return $this->_getData()['status'] != \Wizacha\Status::DISABLED;
    }

    /**
     * @param array $categories
     * @return string
     */
    public static function generateJsonFromFnGetCategories(array $categories)
    {
        return json_encode(
            array_combine(
                array_column($categories, 'category'),
                array_map('self::filterForJson', $categories)
            ),
            JSON_HEX_APOS
        );
    }


    /**
     * @param array $category
     * @return array
     */
    protected static function filterForJson(array $category)
    {
        $return = [];
        if ($category['subcategories']) {
            $return['children'] = array_combine(array_column($category['subcategories'], 'category'), array_map(__METHOD__, $category['subcategories']));
        }
        $return['name'] = $category['category'];
        return $return;
    }

    public function getDescription()
    {
        return $this->_getData()['description'];
    }
    /**
     * @return array
     */
    public static function getTree($getHiddenCategories = false)
    {
        $csvImportCategoryId = Category::getGarbageCategoryId();

        // return categories without 'échec import csv'
        return array_filter(fn_get_categories_tree('0', true, (string) GlobalState::interfaceLocale(), $getHiddenCategories), function ($i) use ($csvImportCategoryId) {
            return $i['category_id'] != $csvImportCategoryId;
        });
    }

    public function exists(): bool
    {
        return \Tygh\Database::getField(
            "SELECT 1 FROM ?:categories WHERE ?:categories.category_id = ?i",
            $this->id
        ) !== null;
    }

    public function getSeoData(): SeoData
    {
        return new SeoData(
            (string) $this->_getData()['page_title'],
            (string) $this->_getData()['meta_description'],
            (string) $this->_getData()['meta_keywords'],
            (string) $this->_getData()['seo_name']
        );
    }
}
