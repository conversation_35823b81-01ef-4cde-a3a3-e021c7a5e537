# CategoryService updateProductCount Fix

## Problem Description

The `updateProductCount` method in `src/Marketplace/PIM/Category/CategoryService.php` had a design flaw that caused issues with product counting in categories. The method was called by a cron job in `src/AppBundle/Command/RecountProductInCategoriesCommand.php`.

### Original Issue
- The method skipped non-leaf categories (categories that have child categories)
- In the specific use case, products are assigned to parent categories but should be accessible through child categories
- This resulted in child categories having `product_count` and `visible_product_count` values of 0 in the database
- Products would not display on the frontend because child categories showed zero counts

### Root Cause
The original logic at line 91-94:
```php
if ($this->connection->fetchColumn('SELECT COUNT(*) FROM cscart_categories WHERE parent_id = ?', [$categoryId])) {
    // On saute les catégories non feuille. Vaut mieux pas mettre a jour le compteur plutot que de mal le mettre a jour
    continue;
}
```

This logic completely skipped parent categories, preventing proper count propagation to child categories.

## Solution Implemented

### Key Changes Made

1. **Removed the blanket skip of parent categories**: The method now processes both parent and child categories appropriately.

2. **Added inheritance logic for child categories**: Child categories with no direct products now inherit counts from their parent categories.

3. **Added propagation logic for parent categories**: When a parent category has products, it propagates those counts to child categories that have no direct products.

4. **Maintained backward compatibility**: The existing logic for updating parent category counts through the hierarchy is preserved.

### New Logic Flow

1. **For each category being updated**:
   - Check if it has children (is a parent category)
   - Count direct products assigned to the category
   - If it's a child category with no direct products, check for inherited counts from parents
   - Update the category's counts in the database
   - If it's a parent with products, propagate counts to children with no direct products
   - Update parent categories in the hierarchy with the difference

2. **Inheritance mechanism**: 
   - Child categories without direct products inherit from the nearest parent that has products
   - This ensures products assigned to parent categories are accessible through child categories

3. **Propagation mechanism**:
   - When a parent category is updated and has products, it automatically updates child categories that have no direct products
   - This is done recursively for multi-level hierarchies

### New Methods Added

1. **`getInheritedProductCounts(int $categoryId): array`**
   - Finds the nearest parent category with products
   - Returns the counts that should be inherited

2. **`propagateCountsToChildCategories(int $parentCategoryId, int $productCount, int $visibleProductCount): void`**
   - Propagates counts from parent to child categories that have no direct products
   - Works recursively for multi-level hierarchies

## Testing

### Test Scenarios Covered

1. **Basic inheritance**: Products assigned to parent category are accessible through child categories
2. **Direct child products**: Child categories with their own products maintain their own counts
3. **Mixed scenarios**: Parent with products + child with products = correct aggregation
4. **Recursive propagation**: Multi-level category hierarchies work correctly
5. **Backward compatibility**: Existing `synchronizeCount()` method works correctly

### Test Results
- ✅ Parent categories correctly count their direct products
- ✅ Child categories inherit parent's product count when they have no direct products  
- ✅ Child categories with direct products maintain their own counts
- ✅ The `synchronizeCount()` method works correctly with the new logic
- ✅ Multi-level hierarchies are handled properly

## Impact

### Positive Changes
- Child categories now show correct product counts when products are assigned to parent categories
- Products assigned to parent categories are now accessible through child category pages
- The frontend will display products correctly in child categories
- Maintains all existing functionality for leaf categories

### Backward Compatibility
- All existing functionality is preserved
- The change is additive - it handles the new use case without breaking existing behavior
- Existing tests should continue to pass
- The `synchronizeCount()` method continues to work as expected

## Files Modified

1. **`src/Marketplace/PIM/Category/CategoryService.php`**
   - Modified `updateProductCount()` method
   - Added `getInheritedProductCounts()` method  
   - Added `propagateCountsToChildCategories()` method

## Deployment Notes

- This is a backward-compatible change
- No database schema changes required
- No configuration changes required
- The fix will take effect immediately when deployed
- Consider running the recount command after deployment to ensure all categories have correct counts:
  ```bash
  php bin/console categories:recount-products
  ```

## Future Considerations

- Monitor performance impact on large category hierarchies
- Consider adding caching if the inheritance lookups become expensive
- The recursive propagation could be optimized for very deep hierarchies if needed
