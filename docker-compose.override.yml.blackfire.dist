version: '3.5'

services:
    php:
        environment:
            BLACKFIRE_CLIENT_ID: "${BLACKFIRE_CLIENT_ID:-}"
            BLACKFIRE_CLIENT_TOKEN: "${BLACKFIRE_CLIENT_TOKEN:-}"
            BLACKFIRE_LOG_LEVEL: "${BLACKFIRE_LOG_LEVEL:-}"
            BLACKFIRE_AGENT_SOCKET: "tcp://blackfire:${BLACKFIRE_AGENT_SOCKET_PORT:-8707}"

    # Blackfire - Sensiolabs profiling service
    blackfire:
        image: blackfire/blackfire
        ports: ["${BLACKFIRE_AGENT_SOCKET_PORT:-8707}"]
        environment:
            BLAC<PERSON>FIRE_SERVER_ID: "${BLACKFIRE_SERVER_ID:-}"
            BLACKFIRE_SERVER_TOKEN: "${BLACKFIRE_SERVER_TOKEN:-}"
            BLAC<PERSON><PERSON>RE_LOG_LEVEL: "${BLA<PERSON><PERSON>FIRE_LOG_LEVEL:-}"

volumes:
    blackfire:
