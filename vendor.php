<?php

use Symfony\Component\HttpFoundation\Request;

define('AREA', 'A');
define('ACCOUNT_TYPE', 'vendor');

$kernel = require __DIR__ . '/init.php';
$request = Request::createFromGlobals();
$trustedProxies = explode(',', $kernel->getContainer()->getParameter('config.security.trusted_proxies'));
// Uniquement quand on est dans le cluster k8s
if (false !== getenv('KUBERNETES_SERVICE_HOST')) {
    // On fait confiance à toutes les requêtes qui arrivent,  dans le cas ou on est
    // en direct, REMOTE_ADDR correspond à l'adresse réelle du client, dans le cas
    // de k8s, elle correspond à l'adresse de l'ingress
    $trustedProxies = \array_merge($trustedProxies, ['127.0.0.1', 'REMOTE_ADDR']);
}
Request::setTrustedProxies($trustedProxies, Request::HEADER_X_FORWARDED_ALL);

$response = $kernel->handle($request);
$response->send();
$kernel->terminate($request, $response);
