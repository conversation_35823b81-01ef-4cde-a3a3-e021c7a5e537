parameters:
    level: 2
    paths:
        - src/
    excludes_analyse:
        - src/AppBundle/
    bootstrap: '%rootDir%/../../../tests/bootstrap_phpstan.php'
    ignoreErrors:
        # Pour l'instant on ne peut pas ignorer des messages dans un fichier précis, il faut attendre la nouvelle version
        # https://github.com/phpstan/phpstan/issues/1683

        # Services available by theme so they can't be included for phpstan
        - '#Service "app.loyalty_service" is not registered in the container.#'
        - '#Service "app.store_service" is not registered in the container.#'

        # Gérer les "constant not found" qui sont définies par thème, je ne sais pas encore comment
        - '#Constant [A-Z_]{1,} not found.#'

        # A supprimer quand on aura mergé la refacto Algolia
        - '#Call to an undefined method Wizacha\\Search\\Engine\\SearchEngine::searchDisjunctiveFacets().#'
        - '#Call to an undefined method Wizacha\\Search\\Engine\\SearchEngine::getNumericFilterStats().#'

        # Méthodes magiques
        - '#Call to an undefined method SoapClient::SendMessage().#'
        - '#Call to an undefined method SoapClient::GetAuthenticationToken().#'
        - '#Call to an undefined method SoapClient::recherchePointChronopost().#'
        - '#Call to an undefined method SoapClient::rechercheDetailPointChronopostInter().#'
        - '#Call to an undefined method SoapClient::shippingWithReservationAndESDWithRefClient().#'
        - '#Call to an undefined method SoapClient::cancelSkybill().#'
        - '#Call to an undefined method SoapClient::WSI4_PointRelais_Recherche().#'
        - '#Call to an undefined method SoapClient::WSI2_CreationEtiquette().#'
        - '#Call to an undefined method [a-zA-Z0-9\\_]+::findBy[A-Z][a-zA-Z]*\(\)#'
        - '#Access to an undefined property Stripe\\Checkout\\Session::#'

        # Les implémentations de TransactionProcessorInterface ne sont pas cohérentes
        - '#Call to an undefined method Wizacha\\Marketplace\\Payment\\Processor\\TransactionProcessorInterface.#'

        # Chaud de changer ça, trop de risques, même si effectivement c'est très bizarre comme code
        - '#Binary operation "\+" between array|Wizacha\\Money\\Money and array|Wizacha\\Money\\Money|null results in an error.#'

        # C'est géré par un __call dans le SDK d'Algolia
        - '#Call to an undefined method AlgoliaSearch\\Index::browse\(\).#'

#        - '~^Method .+::.+\(\) should return .+ but returns .+\|null.$~'
#        # phpstan fails to resolve logic with optional parameter
#        - '~Parameter #2 $occupation of method Wizacha\\Marketplace\\Organisation\\Organisation::addAdministrator() expects string, string|null given.~'

        # ignore symfony 4 BC break
        - '#Method Symfony\\Contracts\\EventDispatcher\\EventDispatcherInterface::dispatch\(\) invoked with 2 parameters, 1 required.#'
        # Flysystem storage class created with a factory :
        - '#Call to method [a-zA-Z]{1,}\(\) on an unknown class Wizacha\\Storage\\[a-zA-Z]*StorageService.#'
        - '#Call to an undefined method League\\Flysystem\\FilesystemInterface::getAdapter().#'
    symfony:
        container_xml_path: %rootDir%/../../../var/cache/test/appAppKernelTestDebugContainer.xml

    doctrine:
        repositoryClass: Wizacha\Marketplace\Commission\CommissionRepository
includes:
    - vendor/phpstan/phpstan-symfony/extension.neon
    - vendor/phpstan/phpstan-doctrine/extension.neon
