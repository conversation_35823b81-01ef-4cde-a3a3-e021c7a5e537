{"name": "w<PERSON><PERSON>", "version": "1.0.0", "description": "assets task runner", "main": "gulpfile.js", "directories": {"doc": "docs", "test": "tests"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/wizaplace/wizaplace.git"}, "author": "Wizacha DevTeam <<EMAIL>>", "license": "proprietary", "bugs": {"url": "https://github.com/wizaplace/wizaplace/issues"}, "homepage": "https://github.com/wizaplace/wizaplace#readme", "private": true, "dependencies": {"babel-core": "6.26.3", "babel-preset-env": "1.7.0", "babelify": "8.0.0", "browserify": "16.2.3", "dotenv": "5.0.1", "gulp": "^4.0.2", "gulp-cli": "^2.2.0", "gulp-concat": "2.6.1", "gulp-less": "^4.0.1", "gulp-sourcemaps": "2.6.5", "hosted-git-info": "^2.8.9", "redoc": "^2.0.0-rc.4", "safe-json-stringify": "^1.2.0", "swagger-cli": "^4.0.4", "vinyl-buffer": "1.0.1", "vinyl-source-stream": "2.0.0", "vue": "2.6.10"}}