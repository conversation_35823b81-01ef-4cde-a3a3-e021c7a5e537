#!/usr/bin/env php
<?php

require_once __DIR__ . '/../vendor/autoload.php';

use PhpAmqpLib\Connection\AMQPStreamConnection;

$checkAMQP = function (): bool {
    try {
        new AMQPStreamConnection(
            getenv('AMQP_HOST'),
            getenv('AMQP_PORT'),
            getenv('AMQP_USER'),
            getenv('AMQP_PASS')
        );
        return true;
    } catch (\Throwable $throwable) {
        return false;
    }
};

$checkRedis = function (): bool {
    try {
        $redis = new \Redis();
        return @$redis->connect(getenv('REDIS_HOST'));
    } catch (\Throwable $throwable) {
        return false;
    }
};

$checkMysql = function (): bool {
    try {
        $dsn = sprintf(
            'mysql:host=%s;port=%s;dbname=%s',
            getenv('DATABASE_HOST'),
            getenv('DATABASE_PORT'),
            getenv('DATABASE_NAME')
        );
        new \PDO($dsn, getenv('DATABASE_USER'), getenv('DATABASE_PASSWORD'));
        return true;
    } catch (\Throwable $throwable) {
        return false;
    }
};

$checkMysqlDiscuss = function (): bool {
    try {
        $dsn = sprintf(
            'mysql:host=%s;port=%s;dbname=%s',
            getenv('DISCUSS_HOST'),
            getenv('DISCUSS_PORT'),
            getenv('DISCUSS_DBNAME')
        );
        new \PDO($dsn, getenv('DISCUSS_USER'), getenv('DISCUSS_PASSWORD'));
        return true;
    } catch (\Throwable $throwable) {
        return false;
    }
};

$checks = [
    'AMQP' => $checkAMQP,
    'redis' => $checkRedis,
    'MySQL Marketplace' => $checkMysql,
    'MySQL Discuss' => $checkMysqlDiscuss
];

$ok = false;
while (!$ok) {
    $ok = true;
    foreach ($checks as $name => $closure) {
        if (($closure instanceof \Closure) && !$closure()) {
            $ok = false;
            echo sprintf("%s Waiting for %s connection.\n", date('c'), $name);
        }
    }

    sleep(2);
}

echo "check ok\n";
