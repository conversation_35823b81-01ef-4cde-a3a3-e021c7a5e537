#!/bin/bash
# prints the current version of the app

if [ "${MARKETPLACE_VERSION}" != "" ]; then
    echo ${MARKETPLACE_VERSION}
    exit 0
fi

tag=$(git describe --tags 2>/dev/null)
return=$?

if [ $return = 0 ]; then
    echo $tag
elif [ ! -z $DRONE_TAG ]; then
    echo $DRONE_TAG
elif [ ! -z $DRONE_COMMIT_SHA ]; then
    echo $DRONE_COMMIT_SHA
elif [ ! -z $PLATFORM_TREE_ID ]; then
    echo $PLATFORM_TREE_ID
else
    echo "undefined"
    exit 1
fi
