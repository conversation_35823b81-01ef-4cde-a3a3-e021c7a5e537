#!/usr/bin/env php
<?php

use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Input\ArgvInput;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\ErrorHandler\Debug;
use Wizacha\Registry;

set_time_limit(0);

require __DIR__ . '/../vendor/autoload.php';

// The check is to ensure we don't use .env in production
if (getenv('APP_ENV') === false) {
    require_once __DIR__.'/../env.php';
}

$input = new ArgvInput();
$env = $input->getParameterOption(['--env', '-e'], getenv('APP_ENV') ?: 'dev');
$bypassLegacyInit = $input->hasParameterOption(['--bypass-legacy-init'], false);
$bypassLegacyInit = getenv('BYPASS_LEGACY_INIT') ?: $bypassLegacyInit;
$debug = getenv('APP_DEBUG') !== '0' && !$input->hasParameterOption(['--no-debug', '']) && $env !== 'prod';

if (!empty($debug)) {
    Debug::enable();
}

// Now the shell verbosity is defined to debug which is quite annoying
// Track progress of the issue https://github.com/symfony/symfony/issues/25362
if ($debug && !isset($_ENV['SHELL_VERBOSITY']) && !isset($_SERVER['SHELL_VERBOSITY'])) {
    putenv('SHELL_VERBOSITY=1');
    $_ENV['SHELL_VERBOSITY'] = 1;
    $_SERVER['SHELL_VERBOSITY'] = 1;
}

define('DEVELOPMENT', $env !== 'prod');
define('APP_ENV', $env);
define('AREA', 'A');
define('NO_SESSION', true);

if ($input->hasParameterOption(['--no-redis'])) {
    define('CACHE_BACKEND', 'file');
}

if ($bypassLegacyInit) {
    $kernel = new AppKernel($env, DEVELOPMENT);
    $kernel->boot();
    $registry = Registry::defaultInstance();
    $registry->container = $kernel->getContainer();
    define('BOOTSTRAP', true);
    define('DIR_ROOT', str_replace('\\', '/', __DIR__));
    $config = require(__DIR__ . '/../config.php');
    Tygh\Registry::set('config', $config);
} else {
    try {
        require __DIR__ . '/../init.php';
    } catch (\Exception $exception) {
        $polling = 10;
        \fwrite(
            STDERR,
            \sprintf(
                "💀 init failed: %s\n💤 waiting %ss.\n",
                $exception->getMessage(),
                $polling
            )
        );
        \sleep($polling);

        exit(0);
    }
}

$application = new Application($kernel);
$application->getDefinition()->addOptions([new InputOption('--bypass-legacy-init', null, InputOption::VALUE_NONE, 'Do not require init.php')]);
$application->getDefinition()->addOptions([new InputOption('--no-redis', null, InputOption::VALUE_NONE, 'Do not use Redis')]);
$application->run($input);
