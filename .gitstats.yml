sql:
    table: code
tasks:
    'Commit message': "git log -1 --pretty=%B | head -n 1"
    'Commit author': "git log -1 --pretty=%an"
    'Number of files': "find . -type f | wc -l"
    'Number of directories': "find . -type d | wc -l"
    'Number of Smarty files': "find . -type f -name '*.tpl' -not -name './vendor/*' | wc -l"
    'Number of Twig files': "find . -type f -name '*.twig' -not -name './vendor/*' | wc -l"
    'Lines of code in addons': "find app/addons -type f -print0 | xargs -0 cat | wc -l"
    'Usage of CS Cart hooks': "find . -type f -name '*.php' -not -name './vendor/*' -print0 | xargs -0 cat | grep fn_set_hook | wc -l"
    'Usage of Smarty hooks': "find . -type f -name '*.tpl' -not -name './vendor/*' -print0 | xargs -0 cat | grep '{hook' | wc -l"
    'Lines of code in init': "wc -l init.php config.php config.local.php app/functions/fn.init.php 2>/dev/null | grep total | awk '{print $1}'"
    'Number of items in src': "ls -l src/ | wc -l"
    'Number of tests': "phploc tests --count-tests | grep 'Methods' | tail -n 1 | awk '{print $2}'"
    'Number of CS Cart PHP files': "grep -lr \"Kalynyak\" . | wc -l"
    'Number of CS Cart functions': "phploc app | grep 'Named Functions' | awk '{print $3}'"
