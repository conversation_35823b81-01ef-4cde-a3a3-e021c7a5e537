{"name": "wizacha/marketplace", "description": "<PERSON><PERSON><PERSON>", "license": "proprietary", "require": {"php": "^7.4", "ext-redis": "*", "ext-pcntl": "*", "ext-intl": "*", "ext-gd": "*", "ext-mbstring": "*", "ext-pdo": "*", "ext-xml": "*", "ext-soap": "*", "ext-json": "*", "ext-curl": "*", "ext-fileinfo": "*", "ext-amqp": "*", "ext-posix": "*", "symfony/symfony": "4.4.*", "google/recaptcha": "~1.1", "myclabs/php-enum": "~1.2", "algolia/algoliasearch-client-php": "^1.20", "tedivm/jshrink": "~1.0", "monolog/monolog": "~1.12", "phpseclib/phpseclib": "~2.0", "smarty/smarty": "~3.1", "broadway/broadway": "~0.9.0", "mustache/mustache": "~2.8", "doctrine/orm": "~2.5", "doctrine/doctrine-bundle": "~1.5", "matthiasnoback/broadway-serialization": "^1.0", "symfony/monolog-bundle": "^3.1", "snc/redis-bundle": "^2.0", "kphoen/rulerz": "~0.21.1", "acelaya/doctrine-enum-type": "^1.0", "mangopay/php-sdk-v2": "^3.1.1", "aws/aws-sdk-php": "^3.36", "knplabs/knp-snappy-bundle": "^1.5", "h4cc/wkhtmltopdf-amd64": "^0.12.3", "twig/extensions": "^1.3", "friendsofsymfony/jsrouting-bundle": "^2.6", "robmorgan/phinx": "^0.10", "nikic/iter": "^2.1", "symfony/swiftmailer-bundle": "^3.1", "giggsey/libphonenumber-for-php": "^7.7", "google/apiclient": "^2.2", "hipay/hipay-fullservice-sdk-php": "^2.3.1", "nelmio/cors-bundle": "^1.5.6", "willdurand/negotiation": "1.5.0", "influxdb/influxdb-php": "^1.14", "studio-42/elfinder": "^2.1.61", "violet/streaming-json-encoder": "^1.1", "barryvdh/elfinder-flysystem-driver": "^0.2.1", "league/flysystem-aws-s3-v3": "^1.0", "microsoft/azure-storage-blob": "^1.1", "guzzlehttp/guzzle": "^6.3", "psr/http-message": "^1.0", "google/apiclient-services": "^0.56.0", "league/flysystem-azure-blob-storage": "^1.0", "lcobucci/jwt": "^3.2", "ocramius/doctrine-batch-utils": "^1.1", "cocur/slugify": "^3.1", "steevanb/composer-overload-class": "1.3.*", "wizaplace/semantic-versioning": "^1.0", "stripe/stripe-php": "^6.43.0", "twig/twig": "^2.11", "gedmo/doctrine-extensions": "^2.4", "exercise/htmlpurifier-bundle": "^3.0", "wikimedia/less.php": "^2.0", "symfony/messenger": "4.4.*", "symfony/proxy-manager-bridge": "4.4.*", "eventio/bbq": "^1.0.2", "php-amqplib/php-amqplib": "^2.6", "ifsnop/mysqldump-php": "^2.9"}, "require-dev": {"phpunit/phpunit": "^7.0", "nategood/httpful": "0.2.*", "m6web/redis-mock": "^3.0", "atoum/atoum": "^3.2", "atoum/stubs": "~2.1", "atoum/ruler-extension": "^1.0", "beberlei/assert": "^2.4", "phing/phing": "^2.14", "mockery/mockery": "@dev", "fzaninotto/faker": "^1.9", "phpstan/phpstan": "^0.11", "wizaplace/phpunit-slicer": "^0.1.0", "wizaplace/atoum-slicer": "^0.10.0", "wizaplace/phpcs": "^1.2", "symfony/maker-bundle": "^1.23", "league/flysystem-memory": "^1.0", "brainmaestro/composer-git-hooks": "^2.8", "phpstan/phpstan-doctrine": "^0.11", "phpstan/phpstan-symfony": "^0.11"}, "repositories": [{"type": "git", "url": "https://github.com/wizacha/bbq.git"}, {"type": "git", "url": "https://github.com/wizaplace/discuss.git"}, {"type": "git", "url": "https://github.com/wizaplace/atoum-slicer"}], "autoload": {"psr-4": {"Wizacha\\": "src/", "Tygh\\": "app/Tygh/"}, "files": ["app/AppKernel.php", "app/functions/fn.database.php", "app/functions/fn.users.php", "app/functions/fn.catalog.php", "app/functions/fn.cms.php", "app/functions/fn.cart.php", "app/functions/fn.locations.php", "app/functions/fn.common.php", "app/functions/fn.fs.php", "app/functions/fn.images.php", "app/functions/fn.init.php", "app/functions/fn.control.php", "app/functions/fn.search.php", "app/functions/fn.log.php", "app/functions/fn.companies.php", "app/functions/fn.multivendor.php", "src/Marketplace/Order/functions.php", "src/functions.php"]}, "autoload-dev": {"psr-4": {"Wizacha\\Test\\": "tests/"}, "files": ["vendor/phpunit/phpunit/src/Framework/Assert/Functions.php"]}, "scripts": {"post-install-cmd": ["[ $COMPOSER_DEV_MODE -eq 0 ] || vendor/bin/cghooks update"], "post-update-cmd": ["[ $COMPOSER_DEV_MODE -eq 0 ] || vendor/bin/cghooks update"]}, "extra": {"symfony-app-dir": "app", "symfony-bin-dir": "bin", "symfony-var-dir": "var", "symfony-web-dir": "web", "symfony": {"require": "4.4.*"}, "hooks": {"post-checkout": "# This hook has been reseted by Wizaplace app.", "post-merge": "# This hook has been reseted by Wizaplace app.", "pre-push": ["# Before Git invokes a hook, it changes its working directory to either the root of the working tree in a non-bare repository, or to the $GIT_DIR in a bare repository.", "bin/validateBranchName"]}}}