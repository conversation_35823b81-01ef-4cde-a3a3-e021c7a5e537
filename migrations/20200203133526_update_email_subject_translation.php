<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateEmailSubjectTranslation extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("
            UPDATE cscart_language_values
            SET value= 'Order fully shipped'
            WHERE name = 'order_email_subject_c'
            AND value = ''
        ");
    }

    public function down(): void
    {
        $this->execute("
            UPDATE cscart_language_values
            SET value= ''
            WHERE name = 'order_email_subject_c'
            AND value = 'Order fully shipped'
        ");
    }
}
