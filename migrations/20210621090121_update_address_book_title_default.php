<?php

/**
 * @copyright Copyright (c) Wizacha
 * @license Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateAddressBookTitleDefault extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE doctrine_address_book CHANGE COLUMN `title` `title` VARCHAR(10) DEFAULT NULL;");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE doctrine_address_book CHANGE COLUMN `title` `title` varchar(10) NOT NULL DEFAULT '';");
    }
}
