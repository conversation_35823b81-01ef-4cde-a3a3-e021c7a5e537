<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class CreateTableDoctrineUserMandate extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<'SQL'
            CREATE TABLE IF NOT EXISTS `doctrine_user_mandate` (
                    `id` int PRIMARY KEY NOT NULL AUTO_INCREMENT,
                    `user_id` int NOT NULL,
                    `created_at` DATETIME DEFAULT NULL,
                    `iban` VARCHAR(255),
                    `bic` VARCHAR(255),
                    `bank_name` VARCHAR(255),
                    `gender` VARCHAR(1),
                    `first_name` VA<PERSON>HA<PERSON>(255),
                    `last_name` VARCHAR(255),
                    `status` VARCHAR(255) DEFAULT 'D',
                    `agreement_id` int DEFAULT NULL,
                    `processor_id` mediumint(8) unsigned NOT NULL
                )
                ENGINE = InnoDB
                DEFAULT CHARACTER SET = utf8mb4
                COLLATE = utf8mb4_unicode_ci;
SQL
        );
        $this->execute("
            ALTER TABLE doctrine_user_mandate
            ADD CONSTRAINT `FK_ProcessorId` FOREIGN KEY (`processor_id`) REFERENCES `cscart_payment_processors` (`processor_id`);");
    }

    public function down(): void
    {
        $this->execute("DROP TABLE IF EXISTS doctrine_user_mandate;");
    }
}
