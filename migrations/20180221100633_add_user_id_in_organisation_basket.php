<?php

use Phinx\Migration\AbstractMigration;

class AddUserIdInOrganisationBasket extends AbstractMigration
{
    public function up()
    {
        $this->execute('ALTER TABLE doctrine_organisation_basket ADD user_id mediumint(8) unsigned NOT NULL AFTER organisation_id;');
        $this->execute('ALTER TABLE doctrine_organisation_basket ADD CONSTRAINT FK_organisation_basket_user FOREIGN KEY (user_id) REFERENCES cscart_users (user_id)');
    }
}
