<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class SynchronizeSubscriptionIdBetweenTables extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            "ALTER TABLE `cscart_orders`
                MODIFY `subscription_id` CHAR(36) COLLATE 'utf8mb4_unicode_ci';"
        );
        $this->execute("CREATE INDEX `cscart_orders_subscription_id` ON `cscart_orders` (subscription_id);");
    }

    public function down(): void
    {
        $this->execute(
            "ALTER TABLE `cscart_orders`
                MODIFY `subscription_id` VARCHAR(36) COLLATE 'utf8mb4_unicode_ci';"
        );
        $this->execute("ALTER TABLE `cscart_orders` DROP INDEX `cscart_orders_subscription_id`;");
    }
}
