<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddCommentToUserProfieField extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<SQL
INSERT INTO cscart_profile_fields VALUES (48, 'comment', 'Y', 'N', 'N', 'N', 'N', 'N', 'T', 70, 'N', 'F', 0, '', '', 'N', 'N', 'N', 'N', 'N');

INSERT INTO cscart_profile_field_descriptions VALUES (48, 'Commentaire', 'F', 'fr');
INSERT INTO cscart_profile_field_descriptions VALUES (48, 'Comment', 'F', 'en');

ALTER TABLE cscart_user_profiles ADD comment TEXT DEFAULT '' NOT NULL;
SQL
        );
    }

    public function down()
    {
        $this->execute(<<<SQL
DELETE FROM cscart_profile_fields WHERE `field_id` = 48;

DELETE FROM cscart_profile_field_descriptions WHERE `object_id` = 48;

ALTER TABLE cscart_user_profiles DROP COLUMN comment;
SQL
        );
    }
}
