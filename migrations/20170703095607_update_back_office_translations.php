<?php

use Phinx\Migration\AbstractMigration;

class UpdateBackOfficeTranslations extends AbstractMigration
{
    public function up()
    {
        $this->execute('UPDATE cscart_language_values SET value = "Disponible pour" WHERE name = "w_type_delivery" AND lang_code = "fr"');
        $this->execute('UPDATE cscart_language_values SET value = "Les vendeurs pro" WHERE name = "w_type_standard" AND lang_code = "fr"');
        $this->execute('UPDATE cscart_language_values SET value = "Les vendeurs particuliers" WHERE name = "w_type_c2c" AND lang_code = "fr"');
        $this->execute('UPDATE cscart_language_values SET value = "pour les deux" WHERE name = "w_type_all" AND lang_code = "fr"');
        $this->execute('UPDATE cscart_language_values SET value = "Envoi standard" WHERE name = "w_standard_delivery" AND lang_code = "fr"');
        $this->execute('UPDATE cscart_language_values SET value = "Description (optionnelle)" WHERE name = "w_shipping_description" AND lang_code = "fr"');
        $this->execute('UPDATE cscart_language_values SET value = "Informer l\'admin" WHERE name = "notify_orders_department" AND lang_code = "fr"');
        $this->execute('UPDATE cscart_language_values SET value = "Informer le vendeur" WHERE name = "notify_vendor" AND lang_code = "fr"');
        $this->execute('UPDATE cscart_language_values SET value = "Objet du mail" WHERE name = "email_subject" AND lang_code = "fr"');
        $this->execute('UPDATE cscart_language_values SET value = "Options" WHERE name = "feature_flags" AND lang_code = "fr"');
    }
}
