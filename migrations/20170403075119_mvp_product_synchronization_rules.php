<?php

use Phinx\Migration\AbstractMigration;

class MvpProductSynchronizationRules extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<'SQL'
CREATE TABLE doctrine_product_sync_from_mvp_rules (
    company_id INT(11) UNSIGNED NOT NULL,
    included_categories LONGTEXT DEFAULT NULL COMMENT '(DC2Type:json_array)',
    excluded_categories LONGTEXT DEFAULT NULL COMMENT '(DC2Type:json_array)',
    included_brands LONGTEXT DEFAULT NULL COMMENT '(DC2Type:json_array)',
    excluded_brands LONGTEXT DEFAULT NULL COMMENT '(DC2Type:json_array)',
    included_products LONGTEXT DEFAULT NULL COMMENT '(DC2Type:json_array)',
    excluded_products LONGTEXT DEFAULT NULL COMMENT '(DC2Type:json_array)',
    PRIMARY KEY (company_id),
    FOREIGN KEY (company_id) REFERENCES cscart_companies (company_id)
)  DEFAULT CHARACTER SET UTF8MB4 COLLATE UTF8MB4_UNICODE_CI ENGINE=INNODB;
SQL
        );
    }
}
