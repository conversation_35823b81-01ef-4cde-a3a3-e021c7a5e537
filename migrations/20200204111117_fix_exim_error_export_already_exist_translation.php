<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class FixEximErrorExportAlreadyExistTranslation extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'An export already exists. Please wait for its completion to launch another EMAIL mode export.'
            WHERE
                `lang_code` = 'en'
                AND name = 'exim_error_export_already_exist';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'Un export est déjà en cours. Veuillez attendre sa complétion pour déclencher un nouvel export en mode EMAIL.'
            WHERE
                `lang_code` = 'fr'
                AND name = 'exim_error_export_already_exist';
            "
        );
    }
}
