<?php

use Phinx\Migration\AbstractMigration;

class AddOrderConfirmedByMarketplace extends AbstractMigration
{
    public function up()
    {
        $this->execute('ALTER TABLE cscart_orders ADD confirmed TINYINT(1) DEFAULT 0');
        $this->execute('UPDATE cscart_orders SET confirmed = 1 WHERE is_paid = 1 AND is_parent_order = "N"');
        $this->execute('UPDATE cscart_orders SET confirmed = 1 WHERE status != "N" AND is_parent_order = "N"');
        $this->execute('UPDATE cscart_orders SET confirmed = 1 WHERE payment_deferment_authorized = 1 AND is_parent_order = "N"');
    }
}
