<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateTranslationLabelsPaymentMethod extends AbstractMigration
{
    protected const TRANSLATION_LABEL = [
        'transaction_bank_wire' => [
            'fr' => [
                'new' => 'Virement',
                'old' => 'Virement bancaire'
            ],
            'en' => [
                'new' => 'Bank wire',
                'old' => 'Bank transfer'
            ]
        ],
        'transaction_direct_debit' => [
            'fr' => [
                'new' => 'Prélèvement',
                'old' => 'Prélèvement SEPA'
            ],
            'en' => [
                'new' => 'Direct debit',
                'old' => 'SEPA direct debit'
            ]
        ],
        'transaction_transfer' => [
            'fr' => [
                'new' => 'Promotion',
                'old' => 'Transfert bancaire'
            ],
            'en' => [
                'new' => 'Discount',
                'old' => 'Transfer'
            ]
        ],
        'transaction_transfer_coupon' => [
            'fr' => [
                'new' => 'Promotion',
                'old' => 'Coupon %s'
            ],
            'en' => [
                'new' => 'Discount',
                'old' => 'Discount %s'
            ]
        ],
        'transaction_refund_transfer' => [
            'fr' => [
                'new' => 'Remboursement Promotion',
                'old' => 'Remboursement de transfert'
            ],
            'en' => [
                'new' => 'Refund Discount',
                'old' => 'Transfer refund'
            ]
        ],
        'transaction_creditcard' => [
            'en' => [
                'new' => 'Credit Card Payment',
                'old' => 'Credit card payment'
            ]
        ]
    ];

    public function up(): void
    {
        $this->updateTranslation('new');
    }

    public function down(): void
    {
        $this->updateTranslation('old');
    }

    protected function updateTranslation(string $version): void
    {
        foreach (static::TRANSLATION_LABEL as $translationKey => $translationValue) {
            foreach ($translationValue as $langCode => $value) {
                $this->execute("UPDATE `cscart_language_values` SET value = '$value[$version]' WHERE lang_code = '$langCode' AND name = '$translationKey'");
            }
        }
    }
}
