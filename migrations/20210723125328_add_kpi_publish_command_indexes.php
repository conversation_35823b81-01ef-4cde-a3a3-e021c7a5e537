<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddKpiPublishCommandIndexes extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('ALTER TABLE `cscart_orders` ADD INDEX `accepted_by_vendor` (`accepted_by_vendor`)');
        $this->execute('ALTER TABLE `cscart_products` ADD INDEX `approved_status` (`approved`, `status`)');
    }

    public function down(): void
    {
        $this->execute('ALTER TABLE `cscart_orders` DROP INDEX `accepted_by_vendor`');
        $this->execute('ALTER TABLE `cscart_products` DROP INDEX `approved_status`');
    }
}
