<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddLemonwaySepaMandateAgreement extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE `doctrine_user_payment_info` ADD `lemonway_sepa_agreement` int unsigned NULL;");
        $this->execute("ALTER TABLE `doctrine_user_payment_info` ADD `lemonway_electronic_signature` VARCHAR(255) NULL;");
    }

    public function down(): void
    {
        $this->execute(
            "ALTER TABLE `doctrine_user_payment_info` DROP `lemonway_sepa_agreement`;"
        );
        $this->execute(
            "ALTER TABLE `doctrine_user_payment_info` DROP `lemonway_electronic_signature`;"
        );
    }
}
