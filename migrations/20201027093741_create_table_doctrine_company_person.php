<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class CreateTableDoctrineCompanyPerson extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("CREATE TABLE IF NOT EXISTS doctrine_company_person(
                id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                company_id INT(11) UNSIGNED NOT NULL,
                firstname varchar(255) NOT NULL,
                lastname varchar(255) NOT NULL,
                title varchar(19) NOT NULL,
                address varchar(255) NOT NULL,
                address_2 varchar(255),
                city varchar(255) NOT NULL,
                state varchar(255) NOT NULL,
                zipcode varchar(255) NOT NULL,
                country varchar(255) NOT NULL,
                birthdate DATETIME NOT NULL,
                birthplace_city varchar(255) NOT NULL,
                birthplace_country varchar(255) NOT NULL,
                ownership_percentage DECIMAL(12, 2) NULL,
                type ENUM('owner') NOT NULL,
                created_at DATETIME NOT NULL,
                PRIMARY KEY (id),
                CONSTRAINT doctrine_companies_company_id_FK FOREIGN KEY (company_id) REFERENCES cscart_companies (company_id)
            ) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4");
    }

    public function down(): void
    {
        $this->execute("DROP TABLE IF EXISTS doctrine_company_person");
    }
}
