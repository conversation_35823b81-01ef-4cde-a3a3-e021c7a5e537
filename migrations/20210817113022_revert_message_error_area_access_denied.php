<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

use Phinx\Migration\AbstractMigration;

class RevertMessageErrorAreaAccessDenied extends AbstractMigration
{
    public function up()
    {

        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'You are not allowed to access this area.'
            WHERE
                `lang_code` = 'en'
                AND name = 'error_area_access_denied';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'Vous n’êtes pas autorisé à accéder à cette zone.'
            WHERE
                `lang_code` = 'fr'
                AND name = 'error_area_access_denied';
            "
        );
    }
}
