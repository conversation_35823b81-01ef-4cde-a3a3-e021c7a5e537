<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class SetNullableHipayCreditCardInfos extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("
        ALTER TABLE doctrine_credit_card
        CHANGE payment_product_code payment_product_code VARCHAR(255) DEFAULT NULL COMMENT '(DC2Type:string)';
        ");
    }

    public function down(): void
    {
        $this->execute("
        ALTER TABLE doctrine_credit_card
        CHANGE payment_product_code payment_product_code VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)';
        ");
    }
}
