<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddIntraEuropeanCommunityVat extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<SQL
INSERT INTO cscart_profile_fields VALUES (46, 'intra_european_community_vat', 'Y', 'N', 'N', 'N', 'N', 'N', 'I', 50, 'N', 'F', 0, '', '', 'N', 'N', 'N', 'N', 'N');

INSERT INTO cscart_profile_field_descriptions VALUES (46, 'Numéro de TVA intracommunautaire', 'F', 'fr');
INSERT INTO cscart_profile_field_descriptions VALUES (46, 'Intra European Community VAT number', 'F', 'en');

ALTER TABLE cscart_user_profiles ADD intra_european_community_vat VARCHAR(255) DEFAULT '' NOT NULL;
SQL
        );
    }

    public function down()
    {
        $this->execute(<<<SQL
DELETE FROM cscart_profile_fields WHERE `field_id` = 46;

DELETE FROM cscart_profile_field_descriptions WHERE `object_id` = 46;

ALTER TABLE cscart_user_profiles DROP COLUMN intra_european_community_vat;
SQL
        );
    }
}
