<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddSupplierReferenceToCombinationAndOrderDetails extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE `cscart_product_options_inventory`
                            ADD COLUMN `supplier_reference` varchar(255) NULL AFTER `id`");

        $this->execute("ALTER TABLE `cscart_order_details`
                            ADD COLUMN `supplier_reference` varchar(255) NULL AFTER `product_code`");
    }
    public function down(): void
    {
        $this->execute("ALTER TABLE `cscart_product_options_inventory`
                            DROP `supplier_reference`");

        $this->execute("ALTER TABLE `cscart_order_details`
                            DROP `supplier_reference`");
    }
}
