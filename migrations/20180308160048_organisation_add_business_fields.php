<?php

use Phinx\Migration\AbstractMigration;

class OrganisationAddBusinessFields extends AbstractMigration
{
    public function up()
    {
        $this->execute("ALTER TABLE doctrine_organisation ADD legal_information_business_name VARCHAR(255) DEFAULT NULL COMMENT '(DC2Type:string)';");
        $this->execute("ALTER TABLE doctrine_organisation ADD business_unit_code VARCHAR(255) DEFAULT NULL COMMENT '(DC2Type:string)';");
        $this->execute("ALTER TABLE doctrine_organisation ADD business_unit_name VARCHAR(255) DEFAULT NULL COMMENT '(DC2Type:string)';");
    }
}
