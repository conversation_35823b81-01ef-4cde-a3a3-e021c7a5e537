<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddEximLayoutProductAttributes extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<SQL
INSERT INTO cscart_exim_layouts 
VALUES (null, 'general', 'Language,Product code,Company id,Free,Id,Name,Value', 'product_attributes', 'Y');
SQL
        );
    }

    public function down(): void
    {
        $this->execute(<<<SQL
DELETE FROM cscart_exim_layouts WHERE `pattern_id` = 'product_attributes';
SQL
        );
    }
}
