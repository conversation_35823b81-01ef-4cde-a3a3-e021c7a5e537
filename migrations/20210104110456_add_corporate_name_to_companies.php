<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddCorporateNameToCompanies extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE cscart_companies ADD corporate_name VARCHAR(255) NOT NULL AFTER company;");
        $this->execute("UPDATE cscart_companies SET corporate_name = company WHERE corporate_name = '';");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE cscart_companies DROP corporate_name;");
    }
}
