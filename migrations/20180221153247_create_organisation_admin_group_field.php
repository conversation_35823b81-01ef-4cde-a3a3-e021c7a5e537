<?php

use Phinx\Migration\AbstractMigration;

class CreateOrganisationAdminGroupField extends AbstractMigration
{
    public function up()
    {
        $this->execute("ALTER TABLE doctrine_organisation ADD admin_group_id CHAR(36) DEFAULT NULL COMMENT '(DC2Type:guid)';");
        $this->execute("ALTER TABLE doctrine_organisation ADD CONSTRAINT FK_291A015E6AF4DE41 FOREIGN KEY (admin_group_id) REFERENCES doctrine_user_group (id);");
        $this->execute("CREATE UNIQUE INDEX UNIQ_291A015E6AF4DE41 ON doctrine_organisation (admin_group_id);");

        $this->execute("UPDATE doctrine_organisation SET admin_group_id = head_group_id;");
    }
}
