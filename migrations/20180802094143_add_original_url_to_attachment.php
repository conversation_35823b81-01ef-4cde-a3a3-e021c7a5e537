<?php

use Phinx\Migration\AbstractMigration;

class AddOriginalUrlToAttachment extends AbstractMigration
{
    public function up()
    {
        $this->execute("ALTER TABLE doctrine_product_attachments ADD original_url VARCHAR(255) DEFAULT NULL COMMENT '(DC2Type:string)'");
        $this->execute("CREATE INDEX attachment_import_idx ON doctrine_product_attachments (product_id, original_url)");
    }
}
