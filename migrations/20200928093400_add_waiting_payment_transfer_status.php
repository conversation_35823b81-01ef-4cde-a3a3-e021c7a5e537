<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddWaitingPaymentTransferStatus extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("INSERT INTO cscart_statuses (status, type, is_default) VALUES ('J', 'O', 'N')");

        $this->execute("INSERT INTO cscart_status_descriptions
            (status, type, description, email_subj, email_header, lang_code, vendor_email_subj,
            vendor_email_header)
            VALUES ('J', 'O', 'Paiement en cours (Virement)', 'Paiement en cours (Virement)',
            '', 'fr', 'Paiement en cours (Virement)', '')");

        $this->execute("INSERT INTO cscart_status_data (status, type, param, value) VALUES
            ('J', 'O', 'allow_return', 'N'),
            ('J', 'O', 'calculate_for_payouts', 'N'),
            ('J', 'O', 'color', '#4a86e8'),
            ('J', 'O', 'inventory', 'I'),
            ('J', 'O', 'notify', 'N'),
            ('J', 'O', 'notify_department', 'N'),
            ('J', 'O', 'notify_vendor', 'N'),
            ('J', 'O', 'remove_cc_info', 'Y'),
            ('J', 'O', 'repay',	'N')");
    }

    public function down(): void
    {
        $this->execute("DELETE FROM cscart_statuses WHERE status = 'J'");
        $this->execute("DELETE FROM cscart_status_descriptions WHERE status = 'J'");
        $this->execute("DELETE FROM cscart_status_data WHERE status = 'J'");
    }
}
