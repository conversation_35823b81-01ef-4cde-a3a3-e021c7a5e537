<?php

use Phinx\Migration\AbstractMigration;

class AddMissingParentOrderIdIndex extends AbstractMigration
{
    private const TABLE_NAME = 'cscart_orders';
    private const KEY = 'parent_order_id';

    public function change()
    {
        $this
            ->table(static::TABLE_NAME)
            ->addIndex(
                [static::KEY],
                [
                    'name' => \sprintf('idx_%s', static::KEY),
                ]
            )
            ->save()
        ;
    }
}
