<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateTranslationOfErrorLoginNotExists extends AbstractMigration
{
    public function up()
    {
        $this->execute("UPDATE cscart_language_values SET value = REPLACE(value, 'svp ','') WHERE name = 'error_login_not_exists' AND lang_code = 'fr';");
    }

    public function down()
    {
        $this->execute("UPDATE cscart_language_values SET value = REPLACE(value, 'Assurez-vous d','Assurez-vous svp d') WHERE name = 'error_login_not_exists' AND lang_code = 'fr';");
    }
}
