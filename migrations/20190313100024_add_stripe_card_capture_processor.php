<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddStripeCardCaptureProcessor extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("
          INSERT INTO `cscart_payment_processors` 
          (processor_id, processor, processor_script, admin_template, callback, type) 
          VALUES (1011, 'Stripe capture', 'stripe_card_capture.php', 'stripe_card.tpl', 'N', 'P');");
    }
}
