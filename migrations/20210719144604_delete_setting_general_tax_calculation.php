<?php

use Phinx\Migration\AbstractMigration;

class DeleteSettingGeneralTaxCalculation extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("DELETE FROM cscart_settings_objects WHERE object_id='179'");
        $this->execute("DELETE FROM cscart_settings_descriptions WHERE object_id='179' AND object_type='O'");
    }

    public function down(): void
    {
        $this->execute("INSERT INTO cscart_settings_descriptions VALUES ('179', 'O', 'en', '', '', '')");
        $this->execute("INSERT INTO cscart_settings_objects VALUES ('179', 'ROOT', 'tax_calculation', 2, 0, 'S', 'unit_price', 55, 'Y', NULL)");
        $this->execute("INSERT INTO cscart_settings_descriptions VALUES ('179', 'O', 'fr', 'Mode de calcul des taxes basé sur', 'Tax calculation method based on', '')");
    }
}
