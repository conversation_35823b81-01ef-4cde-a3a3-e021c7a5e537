<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class FixDuplicatedPrimaryKeyCscartImages extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE `cscart_images` CHANGE `image_id` `image_id` int unsigned NOT NULL AUTO_INCREMENT FIRST");
        $this->execute("ALTER TABLE `cscart_images_links` CHANGE `image_id` `image_id` int unsigned NOT NULL DEFAULT '0'");
        $this->execute("ALTER TABLE `cscart_images_links` CHANGE `detailed_id` `detailed_id` int unsigned NOT NULL DEFAULT '0'");
        $this->execute("ALTER TABLE `cscart_common_descriptions` CHANGE `object_id` `object_id` int unsigned NOT NULL DEFAULT '0'");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE `cscart_images` CHANGE `image_id` `image_id` mediumint NOT NULL AUTO_INCREMENT FIRST");
        $this->execute("ALTER TABLE `cscart_images_links` CHANGE `image_id` `image_id` mediumint unsigned NOT NULL DEFAULT '0'");
        $this->execute("ALTER TABLE `cscart_images_links` CHANGE `detailed_id` `detailed_id` mediumint unsigned NOT NULL DEFAULT '0'");
        $this->execute("ALTER TABLE `cscart_common_descriptions` CHANGE `object_id` `object_id` mediumint unsigned NOT NULL DEFAULT '0'");
    }
}
