<?php

use Phinx\Migration\AbstractMigration;

class ChangePageDescriptionDefaultValue extends AbstractMigration
{
    public function up()
    {
        // Changement de la valeur par défaut qui était auparavant à "0"
        // et faisait donc apparaitre des descriptions contenant "0" lors de la création des traductions.
        $this->execute("ALTER TABLE `cscart_page_descriptions` CHANGE COLUMN `page` `page` VARCHAR(255) NOT NULL DEFAULT ''");
    }
}
