<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddWithdrawalPeriodToSubscriptionWorkflow extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('
            UPDATE cscart_orders
            SET workflow_name = "credit-card-payment-order-confirmation-withdrawal-period-funds-dispatch"
            WHERE workflow_name = "credit-card-payment-order-confirmation-funds-dispatch";
        ');
    }

    public function down(): void
    {
        $this->execute('
            UPDATE cscart_orders
            SET workflow_name = "credit-card-payment-order-confirmation-funds-dispatch"
            WHERE workflow_name = "credit-card-payment-order-confirmation-withdrawal-period-funds-dispatch";
        ');
    }
}
