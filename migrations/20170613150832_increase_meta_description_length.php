<?php

use Phinx\Migration\AbstractMigration;

class IncreaseMetaDescriptionLength extends AbstractMigration
{
    public function up()
    {
        $this->execute("ALTER TABLE cscart_category_descriptions CHANGE COLUMN `meta_description` `meta_description` VARCHAR(600) NOT NULL DEFAULT '';");
        $this->execute("ALTER TABLE cscart_page_descriptions CHANGE COLUMN `meta_description` `meta_description` VARCHAR(600) NOT NULL DEFAULT '';");
        $this->execute("ALTER TABLE cscart_product_descriptions CHANGE COLUMN `meta_description` `meta_description` VARCHAR(600) NOT NULL DEFAULT '';");
        $this->execute("ALTER TABLE cscart_product_feature_variant_descriptions CHANGE COLUMN `meta_description` `meta_description` VARCHAR(600) NOT NULL DEFAULT '';");
    }
}
