<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddDisplayOnFacetingToOptions extends AbstractMigration
{
    public function up(): void
    {
        // Add column display_on_faceting
        $this->execute("ALTER TABLE cscart_product_options ADD display_on_faceting BOOLEAN DEFAULT FALSE NOT NULL;");
    }

    public function down(): void
    {
        // Remove column display_on_faceting
        $this->execute("ALTER TABLE cscart_product_options DROP COLUMN display_on_faceting;");
    }
}
