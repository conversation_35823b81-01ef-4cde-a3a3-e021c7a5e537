<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddNewTranslationForDiscussNewMessageContentCustomer extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<'SQL'
        INSERT IGNORE INTO cscart_language_values (lang_code, name, value)
        VALUES ('fr', 'wizacha_discuss_new_message_content_customer',
        '\n            <p>Vous avez reçu un nouveau message sur <a href=\"[wizacha_url]\">notre marketplace</a></p>\n            <p>Vous pouvez le consulter en vous rendant dans votre espace personnel</p>\n\n        ');
        INSERT IGNORE INTO cscart_language_values (lang_code, name, value)
        VALUES ('en', 'wizacha_discuss_new_message_content_customer',
        '\n            <p>Vous avez reçu un nouveau message sur <a href=\"[wizacha_url]\">notre marketplace</a></p>\n            <p>Vous pouvez le consulter en vous rendant dans votre espace personnel</p>\n\n        ');
SQL
        );
    }
}
