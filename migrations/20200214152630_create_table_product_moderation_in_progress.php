<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class CreateTableProductModerationInProgress extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<SQL
CREATE TABLE doctrine_product_moderation_in_progress (
    product_id INT NOT NULL,
    created_at DATETIME NOT NULL,
    PRIMARY KEY(product_id)
)
DEFAULT CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
SQL
        );
    }

    public function down(): void
    {
        $this->execute("DROP TABLE IF EXISTS doctrine_product_moderation_in_progress;");
    }
}
