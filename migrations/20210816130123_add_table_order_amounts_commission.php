<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddTableOrderAmountsCommission extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            "CREATE TABLE IF NOT EXISTS doctrine_order_amounts_commission (
            id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)',
            order_id INT NOT NULL,
            company_id INT DEFAULT NULL,
            category_id INT DEFAULT NULL,
            percent_amount NUMERIC(10, 2) NOT NULL,
            fix_amount NUMERIC(10, 2) NOT NULL,
            maximum_amount NUMERIC(10, 2) DEFAULT NULL,
            commission_type VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)',
            PRIMARY KEY(id))
            DEFAULT CHARACTER SET UTF8MB4 COLLATE UTF8MB4_UNICODE_CI ENGINE=INNODB;"
        );

        $this->execute(
            "ALTER TABLE doctrine_order_amounts ADD order_amounts_commission_id CHAR(36) DEFAULT NULL COMMENT '(DC2Type:guid)'"
        );

        $this->execute(
            "ALTER TABLE doctrine_order_amounts ADD CONSTRAINT FK_353817D964598AF5
                FOREIGN KEY (order_amounts_commission_id) REFERENCES doctrine_order_amounts_commission (id) ON DELETE CASCADE"
        );
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE doctrine_order_amounts DROP FOREIGN KEY FK_353817D964598AF5");

        $this->execute('ALTER TABLE doctrine_order_amounts DROP order_amounts_commission_id');

        $this->execute("DROP TABLE IF EXISTS doctrine_order_amounts_commission");
    }
}
