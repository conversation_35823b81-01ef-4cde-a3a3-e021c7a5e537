<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddFullCategoryPathToItemData extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE doctrine_order_item_data
                              ADD full_category_path VARCHAR(255) NULL COMMENT '(DC2Type:string)' AFTER product_id;");
        $this->execute("UPDATE doctrine_order_item_data oid
                               SET full_category_path =
                                    (SELECT id_path
                                       FROM cscart_categories c
                                       JOIN cscart_products_categories pc ON pc.category_id = c.category_id
                                      WHERE pc.product_id = oid.product_id);");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE doctrine_order_item_data DROP full_category_path");
    }
}
