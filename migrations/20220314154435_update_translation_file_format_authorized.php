<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateTranslationFileFormatAuthorized extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Accepted formats: jpg, jpeg, png, pdf"
            WHERE name = "file_format_authorized"
            AND value = "Accepted formats: jpg, png, pdf"
            AND lang_code = "en";
        ');

        $this->execute('
            UPDATE cscart_language_values
            SET value = "Format acceptés : jpg, jpeg, png, pdf"
            WHERE name = "file_format_authorized"
            AND value = "Format acceptés : jpg, png, pdf"
            AND lang_code = "fr";
        ');
    }

    public function down(): void
    {
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Accepted formats: jpg, png, pdf"
            WHERE name = "file_format_authorized"
            AND value = "Accepted formats: jpg, jpeg, png, pdf"
            AND lang_code = "en";
        ');

        $this->execute('
            UPDATE cscart_language_values
            SET value = "Format acceptés : jpg, png, pdf"
            WHERE name = "file_format_authorized"
            AND value = "Format acceptés : jpg, jpeg, png, pdf"
            AND lang_code = "fr";
        ');
    }
}
