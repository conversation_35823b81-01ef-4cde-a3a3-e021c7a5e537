<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateIbanBicForMarchandDeTest extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            "UPDATE cscart_companies
            SET
                iban = '***************************',
                bic = 'BPFIFRP1'
            WHERE
                company_id = 1
                AND company = 'Marchand de test'
                AND iban IS NULL
                AND bic IS NULL
            "
        );
    }

    public function down(): void
    {
        $this->execute(
            "UPDATE cscart_companies
            SET
                iban = NULL,
                bic = NULL
            WHERE
                company_id = 1
                AND company = 'Marchand de test'
                AND iban = '***************************',
                AND bic = 'BPFIFRP1'
            "
        );
    }
}
