<?php

use Phinx\Migration\AbstractMigration;

class UpdateMissingTaxConfigurationTranslation extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            'UPDATE cscart_language_values
            SET value =
        "<p>
La commande %orderId% est expédiée depuis un pays dans lequel vous n’avez pas configuré le taux de taxe pour les frais de port. Dans ce cas, Wizaplace utilise par défaut la taxe de la marketplace.
Vous pouvez le configurer sur la page Taxes applicable pour les frais de port de ce pays de votre back office (Menu Roues crantées / Taxes / Onglet frais de port) .
Vous pouvez consulter le détail de la commande dans le back-office et consulter la taxe qui s’est appliquée sur les frais de port. <br/>
Cordialement,<br/>
Le support client
</p>"
            WHERE md5(value) = "88415681e50f30d97c548d6eca4bbcd0"
            AND lang_code = "fr";'
        );

        $this->execute(<<<SQL
            UPDATE cscart_language_values
            SET value =
        "<p>
        Order %orderId% is shipped from a country where you have not configured the tax rate for shipping. In this case, <PERSON><PERSON><PERSON> uses the marketplace tax by default. You can configure the tax rate applicable to the shipping costs of this country on the Taxes page of your back office (Menu Gears / Taxes / Shipping costs tab).
        You can view the details of the order in the back office and see the tax that has been applied to the shipping costs.<br/>
        Sincerely,<br/>
        Customer support
        </p>"
            WHERE md5(value) = "3daa4fbe60e202b5114cbc5eb55a1f70"
            AND lang_code = "en"
        SQL);
    }

    public function down(): void
    {
        $this->execute(
            'UPDATE cscart_language_values
            SET value = "|
<p>
Bonjour,<br/>
La commande %orderId% est expédiée dans un pays dans lequel vous n’avez pas configurer le taux de taxe pour les frais de port.
Vous pouvez la configurer sur la page Taxes de votre back office.
Si vous ne souhaitez pas maintenir cette commande, rendez-vous dans le back-office pour l’annuler. <br/>
Cordialement,<br/>
Le support client
</p>"
            WHERE md5(value) = "253568e1a2e07914697153cd59a95826"
            AND lang_code = "fr"'
        );

        $this->execute(<<<SQL
            UPDATE cscart_language_values
            SET value ="|
        <p>
        Hello,<br/>
        The %orderId% order is shipped to a country where you have not configured the tax rate for shipping.
        You can configure it on the Taxes page of your back office.
        If you don't want to keep this order, go to the back office to cancel it.<br/>
        Sincerely,<br/>
        Customer support
        </p>"
            WHERE md5(value) = "adec50926df860253b207796653a0571"
            AND lang_code = "en"
        SQL);
    }
}
