<?php

use Phinx\Migration\AbstractMigration;

class ProfileFieldsNoIntegerCompany extends AbstractMigration
{
    public function up()
    {
        /** @var PDO $conn */
        $conn = $this->getAdapter()->getConnection();

        $this->execute("UPDATE cscart_profile_fields SET field_name='b_company' WHERE field_id='39';");
        $this->execute("UPDATE cscart_profile_fields SET field_name='s_company' WHERE field_id='40';");


        try {
            $this->execute("ALTER TABLE `cscart_user_profiles` ADD COLUMN `b_company` VARCHAR(128) NOT NULL AFTER b_lastname;");
        } catch (PDOException $e) {
            if ($e->getCode() != '42S21') { // duplicate column
                throw $e;
            }
        };
        try {
            $this->execute("ALTER TABLE `cscart_user_profiles` ADD COLUMN `s_company` VARCHAR(128) NOT NULL AFTER s_lastname;");
        } catch (PDOException $e) {
            if ($e->getCode() != '42S21') { // duplicate column
                throw $e;
            }
        };

        // s_company already exists
        try {
            $this->execute("ALTER TABLE cscart_orders ADD b_company VARCHAR(128) NOT NULL AFTER b_lastname;");
        } catch (PDOException $e) {
            if ($e->getCode() != '42S21') { // duplicate column
                throw $e;
            }
        };

        // Billing field
        $result = $this->query('SELECT object_id AS user_id, value FROM cscart_profile_fields_data WHERE field_id = 39 AND object_type = "P"');

        while ($row = $result->fetch()) {
            $userId = $row['user_id'];
            $value = $conn->quote($row['value']);
            $this->execute("UPDATE cscart_user_profiles SET b_company=$value WHERE user_id=$userId;");
        }

        // Shipping field
        $result = $this->query('SELECT object_id AS user_id, value FROM cscart_profile_fields_data WHERE field_id = 40 AND object_type = "P"');

        while ($row = $result->fetch()) {
            $userId = $row['user_id'];
            $value = $conn->quote($row['value']);
            $this->execute("UPDATE cscart_user_profiles SET s_company=$value WHERE user_id=$userId;");
        }

        // Orders
        // Billing field
        $result = $this->query('SELECT object_id AS order_id, value FROM cscart_profile_fields_data WHERE field_id = 39 AND object_type = "O"');

        while ($row = $result->fetch()) {
            $value = $conn->quote($row['value']);
            $orderId = $row['order_id'];
            $this->execute("UPDATE cscart_orders SET b_company=$value WHERE order_id=$orderId;");
        }

        // Shipping field ( the field already exists, we migrate only if it's empty )
        $result = $this->query('SELECT object_id AS order_id, value FROM cscart_profile_fields_data WHERE field_id = 40 AND object_type = "O"');

        while ($row = $result->fetch()) {
            $value = $conn->quote($row['value']);
            $orderId = $row['order_id'];
            $this->execute("UPDATE cscart_orders SET s_company=$value WHERE s_company='' AND order_id=$orderId;");
        }
    }
}
