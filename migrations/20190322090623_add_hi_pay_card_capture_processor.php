<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddHiPayCardCaptureProcessor extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("
          INSERT INTO `cscart_payment_processors` 
          (processor_id, processor, processor_script, admin_template, callback, type) 
          VALUES (1012, 'HiPay capture', 'hipay_card_capture.php', 'hipay_card.tpl', 'N', 'P');");
    }
}
