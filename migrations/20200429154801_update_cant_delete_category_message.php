<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateCantDeleteCategoryMessage extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('UPDATE cscart_language_values SET value = "Cette catégorie contient des produits et/ou des fiches produits unifiées, et ne peut donc pas être supprimée." WHERE name = "w_cant_delete_category" and lang_code = "fn";');
        $this->execute('UPDATE cscart_language_values SET value = "This category contains products and, or unified products, and therefore cannot be deleted." WHERE name = "w_cant_delete_category" and lang_code = "en";');
    }

    public function down(): void
    {
        $this->execute('UPDATE cscart_language_values SET value = "La catégorie ou une de ses sous-catégories contient des produits et ne peut donc pas être supprimée" WHERE name = "w_cant_delete_category" and lang_code = "fn";');
        $this->execute('UPDATE cscart_language_values SET value = "The category or one of its subcategories contains products and therefore can not be deleted" WHERE name = "w_cant_delete_category" and lang_code = "en";');
    }
}
