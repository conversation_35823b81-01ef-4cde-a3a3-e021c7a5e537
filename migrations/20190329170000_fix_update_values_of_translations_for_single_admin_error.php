<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class FixUpdateValuesOfTranslationsForSingleAdminError extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<'SQL'
UPDATE `cscart_language_values`
SET value = 'Le statut n''a pas été modifié. Il doit y avoir au moins un administrateur actif sur la boutique.'
WHERE lang_code = 'fr' 
AND name = 'single_admin_cannot_be_deleted' 
AND value = 'Erreur pendant la mise à jour de votre statut. Le statut n''a pas été changé.'
SQL
        );

        $this->execute(<<<'SQL'
UPDATE `cscart_language_values`
SET value = 'Erreur pendant la mise à jour de votre statut. Le statut n''a pas été changé.'
WHERE lang_code = 'fr' 
AND name = 'error_status_not_changed' 
AND value = 'Le statut n''a pas été modifié. Il doit y avoir au moins un administrateur actif sur la boutique.'
SQL
        );
    }
}
