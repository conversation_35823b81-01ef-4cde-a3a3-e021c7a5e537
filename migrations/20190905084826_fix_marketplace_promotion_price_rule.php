<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class FixMarketplacePromotionPriceRule extends AbstractMigration
{
    public function up(): void
    {
        $promotions = $this->query('SELECT id, rules FROM doctrine_promotion WHERE type = "marketplace"');

        foreach ($promotions as $promotion) {
            if (false === \is_string($promotion['rules']) || 0 === mb_strlen($promotion['rules'])) {
                continue;
            }

            if (false !== preg_match('/([0-9]+)/', $promotion['rules'], $matches)) {
                $this->updateRules(
                    $promotion['id'],
                    preg_replace('/([0-9]+)/', $matches[0] * 100, $promotion['rules'])
                );
            }
        }
    }

    public function down(): void
    {
        $promotions = $this->query('SELECT id, rules FROM doctrine_promotion WHERE type = "marketplace"');

        foreach ($promotions as $promotion) {
            if (false === \is_string($promotion['rules']) || 0 === mb_strlen($promotion['rules'])) {
                continue;
            }

            if (false !== preg_match('/([0-9]+)/', $promotion['rules'], $matches)) {
                $this->updateRules(
                    $promotion['id'],
                    preg_replace('/([0-9]+)/', $matches[0] / 100, $promotion['rules'])
                );
            }
        }
    }

    private function updateRules(string $id, string $rules): int
    {
        return  $this->execute('UPDATE doctrine_promotion SET rules = "' . $rules . '" WHERE id="' . $id . '"');
    }
}
