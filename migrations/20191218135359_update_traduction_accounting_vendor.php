<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateTraductionAccountingVendor extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('UPDATE cscart_language_values SET value = "Commission including tax" WHERE name = "accounting_commissions_ttc" and lang_code = "en";');
        $this->execute('UPDATE cscart_language_values SET value = "Shipping cost including tax" WHERE name = "shipping_cost_ttc" and lang_code = "en";');
        $this->execute('UPDATE cscart_language_values SET value = "Total including tax" WHERE name = "total_ttc" and lang_code = "en";');
        $this->execute('UPDATE cscart_language_values SET value = "Total amount including tax" WHERE name = "total_amount_ttc" and lang_code = "en";');
        $this->execute('UPDATE cscart_language_values SET value = "Vendor accounting including tax" WHERE name = "vendor_account_balance_ttc" and lang_code = "en";');
        $this->execute('UPDATE cscart_language_values SET value = "Vendor share including tax" WHERE name = "w_donate_ttc" and lang_code = "en";');
    }

    public function down(): void
    {
        $this->execute('UPDATE cscart_language_values SET value = "Commission included taxes" WHERE name = "accounting_commissions_ttc" and lang_code = "en";');
        $this->execute('UPDATE cscart_language_values SET value = "Shipping cost included taxes" WHERE name = "shipping_cost_ttc" and lang_code = "en";');
        $this->execute('UPDATE cscart_language_values SET value = "Total included taxes" WHERE name = "total_ttc" and lang_code = "en";');
        $this->execute('UPDATE cscart_language_values SET value = "Total amount included taxes" WHERE name = "total_amount_ttc" and lang_code = "en";');
        $this->execute('UPDATE cscart_language_values SET value = "Vendor accounting included taxes" WHERE name = "vendor_account_balance_ttc" and lang_code = "en";');
        $this->execute('UPDATE cscart_language_values SET value = "Vendor share included taxes" WHERE name = "w_donate_ttc" and lang_code = "en";');
    }
}
