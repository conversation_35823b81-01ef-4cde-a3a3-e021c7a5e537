<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class TranslationNotificationFailed extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Virement #%transactionId% reçu de %amount%%currency% non reconnu."
            WHERE name = "bankwire_notification_failed_to_retrieve_orders_subject"
            AND value = "Notification de virement: impossible de récupérer les commandes concernées."
            AND lang_code = "fr"
        ');
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Bank Transfer #%transactionId% received of %amount%%currency% not recognized."
            WHERE name = "bankwire_notification_failed_to_retrieve_orders_subject"
            AND value = "Bankwire notification: could not retrieve orders."
            AND lang_code = "en"
        ');

        $this->execute('
            UPDATE cscart_language_values
            SET value = "<p>
    Nous avons reçu un virement qui n’a pas pu être identifié. Il peut s’agir:<br>
    - D’une référence non renseignée ou ne correspondant à aucune commande en cours.<br>
    - D’un montant à payer différent de celle de la commande.
</p>
<p>
    ID transaction: %transactionId%<br/>
    Référence: %token%<br/>
    Montant: %amount%%currency%
</p>"
            WHERE name = "bankwire_notification_failed_to_retrieve_orders_body"
            AND value = "<p>
    Une erreur est survenue à la réception d\'une notification de virement bancaire.<br/>
    Impossible de récupérer les commandes concernées.
</p>
<p>
    ID transaction : %transactionId%<br/>
    Token : %token%<br/>
    Montant : %amount%<br/>
</p>"
            AND lang_code = "fr"
        ');

        $this->execute('
            UPDATE cscart_language_values
            SET value = "<p>
    We received a bank transfer which couldn’t be identified. It could be for one of the following reasons:<br>
    - The reference was not submitted or doesn’t match any order awaiting payment.<br>
    - The amount doesn’t match exactly the referenced order.
</p>
<p>
    Transaction ID: %transactionId%<br/>
    Reference: %token%<br/>
    Amount: %amount%%currency%
</p>"
            WHERE name = "bankwire_notification_failed_to_retrieve_orders_body"
            AND value = "<p>
    An error occurred during a bankwire notification treatment.<br/>
    We couldn\'t retrieve orders related to this notification.
</p>
<p>
    Transaction ID: %transactionId%<br/>
    Token: %token%<br/>
    Amount: %amount%<br/>
</p>"
            AND lang_code = "en"
        ');
    }

    public function down(): void
    {
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Notification de virement: impossible de récupérer les commandes concernées."
            WHERE name = "bankwire_notification_failed_to_retrieve_orders_subject"
            AND value = "Virement #%transactionId% reçu de %amount%%currency% non reconnu."
            AND lang_code = "fr"
        ');
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Bankwire notification: could not retrieve orders."
            WHERE name = "bankwire_notification_failed_to_retrieve_orders_subject"
            AND value = "Bank Transfer #%transactionId% received of %amount%%currency% not recognized."
            AND lang_code = "en"
        ');

        $this->execute('
            UPDATE cscart_language_values
            SET value = "<p>
    Une erreur est survenue à la réception d\'une notification de virement bancaire.<br/>
    Impossible de récupérer les commandes concernées.
</p>
<p>
    ID transaction : %transactionId%<br/>
    Token : %token%<br/>
    Montant : %amount%<br/>
</p>"
            WHERE name = "bankwire_notification_failed_to_retrieve_orders_body"
            AND value = "<p>
    Nous avons reçu un virement qui n’a pas pu être identifié. Il peut s’agir:<br>
    - D’une référence non renseignée ou ne correspondant à aucune commande en cours.<br>
    - D’un montant à payer différent de celle de la commande.
</p>
<p>
    ID transaction: %transactionId%<br/>
    Référence: %token%<br/>
    Montant: %amount%%currency%
</p>"
            AND lang_code = "fr"
        ');
        $this->execute('
            UPDATE cscart_language_values
            SET value = "<p>
    An error occurred during a bankwire notification treatment.<br/>
    We couldn\'t retrieve orders related to this notification.
</p>
<p>
    Transaction ID: %transactionId%<br/>
    Token: %token%<br/>
    Amount: %amount%<br/>
</p>"
            WHERE name = "bankwire_notification_failed_to_retrieve_orders_body"
            AND value = "<p>
    We received a bank transfer which couldn’t be identified. It could be for one of the following reasons:<br>
    - The reference was not submitted or doesn’t match any order awaiting payment.<br>
    - The amount doesn’t match exactly the referenced order.
</p>
<p>
    Transaction ID: %transactionId%<br/>
    Reference: %token%<br/>
    Amount: %amount%%currency%
</p>"
            AND lang_code = "en"
        ');
    }
}
