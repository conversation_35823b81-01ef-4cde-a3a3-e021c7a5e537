<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class FixCreditCardUniqueConstraint extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('
            CREATE UNIQUE INDEX uniq_card_per_user_idx ON doctrine_credit_card (user_id, pan, card_expiry_month, card_expiry_year);
        ');
    }

    public function down()
    {
        $this->execute('
            DROP index `uniq_card_per_user_idx` ON doctrine_credit_card;
        ');
    }
}
