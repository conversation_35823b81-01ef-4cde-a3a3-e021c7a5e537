<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class MigrateOrderNewTotalFields extends AbstractMigration
{
    public function up(): void
    {
        $this->query('
            UPDATE cscart_orders
            SET customer_total = total
            WHERE marketplace_discount_id IS NULL
        ');
    }

    public function down(): void
    {
        $this->query('
            UPDATE cscart_orders
            SET customer_total = 0
            WHERE marketplace_discount_id IS NULL
        ');
    }
}
