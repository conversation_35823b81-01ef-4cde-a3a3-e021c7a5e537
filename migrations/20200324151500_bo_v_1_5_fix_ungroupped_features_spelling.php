<?php

use Phinx\Migration\AbstractMigration;

class BoV15FixUngrouppedFeaturesSpelling extends AbstractMigration
{
    public function up(): void
    {
        $trad = [
            'ungroupped_features' => [
                'fr' => [
                    'old' => 'Attributs non groupées',
                    'new' => 'Attributs non groupés',
                ]
            ]
        ];
        foreach ($trad as $key => $data) {
            foreach ($data as $lang => $values) {
                $this->query($sql = sprintf("UPDATE cscart_language_values SET value = '%s' WHERE lang_code = '%s' AND name = '%s' AND value = '%s';", $values['new'], $lang, $key, $values['old']));
            }
        }
    }

    public function down()
    {
    }
}
