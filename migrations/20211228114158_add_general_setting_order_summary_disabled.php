<?php

use Phinx\Migration\AbstractMigration;

class AddGeneralSettingOrderSummaryDisabled extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<SQL
INSERT INTO `cscart_settings_objects`
(`edition_type`, `name`, `section_id`, `section_tab_id`, `type`, `value`, `position`, `is_global`, `handler`)
VALUES ('ROOT', 'display_order_summary_in_emails', '2', '0', 'C', 'Y', '56', 'Y', NULL);
SQL
        );

        $result = $this->fetchRow(<<<SQL
SELECT `object_id` FROM cscart_settings_objects 
WHERE `name` = 'display_order_summary_in_emails';
SQL
        );

        $this->execute("INSERT INTO `cscart_settings_descriptions`
(`object_id`, `object_type`, `lang_code`, `value`, `original_value`, `tooltip`)
VALUES (" . $result['object_id'] . ", 'O', 'en', '', '', '');");

        $this->execute("INSERT INTO `cscart_settings_descriptions`
(`object_id`, `object_type`, `lang_code`, `value`, `original_value`, `tooltip`)
VALUES (" . $result['object_id'] . ", 'O', 'fr', 'Afficher les récapitulatifs de commande et d''avoir dans les mails transactionnels', 'Display order and credit note summaries in transactional emails', '');");
    }

    public function down(): void
    {
        $result = $this->fetchRow(<<<SQL
SELECT `object_id` FROM cscart_settings_objects 
WHERE `name` = 'display_order_summary_in_emails';
SQL
        );

        if (\count($result) === 1) {
            $this->execute("DELETE FROM `cscart_settings_descriptions` WHERE `object_id` = " . $result['object_id'] . ";");
            $this->execute("DELETE FROM `cscart_settings_objects` WHERE `object_id` = " . $result['object_id'] . ";");
        }
    }
}
