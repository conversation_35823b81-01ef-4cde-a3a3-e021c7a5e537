<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;
use Wizacha\Marketplace\Commission\CommissionType;

class TransferCommissionToDoctrineCommission extends AbstractMigration
{
    /** @var array[]|null */
    private $commissions = null;

    /** @var float */
    private $marketplaceCommissionPercent = 0;

    /** @var float */
    private $marketplaceCommissionFixed = 0;

    /** @var PDO */
    private $pdo;

    public function up(): void
    {
        $this->pdo = $this->getAdapter()->getConnection();

        $this->execute(
            '
                ALTER TABLE `doctrine_commission`
                    ADD COLUMN `maximum_amount` DECIMAL(12,2) DEFAULT NULL,
                    ADD COLUMN `commission_type` VARCHAR(25) NOT NULL,
                    DROP INDEX `company_category_index`,
                    MODIFY `category_id` INT(11) NULL,
                    MODIFY `company_id` INT(11) NULL;
            '
        );
        $this->execute('CREATE INDEX commission_company_id_idx ON doctrine_commission (company_id)');
        $this->execute('CREATE INDEX commission_category_id_idx ON doctrine_commission (category_id)');
        $this->execute('CREATE INDEX commission_type_idx ON doctrine_commission (commission_type)');

        $this->updateCommissionType();

        $this->marketplaceCommissionPercent = $this->getMarketplaceCommissionPercent();
        $this->marketplaceCommissionFixed = $this->getMarketplaceCommissionFixed();
        $stmt = $this->pdo->prepare('SELECT company_id, commission_fixed, commission_percent FROM `cscart_companies`');
        $stmt->execute();

        while ($commission = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->commissions[] = $commission;
        }

        if (\is_float($this->marketplaceCommissionPercent) && \is_float($this->marketplaceCommissionFixed)) {
            $this->addMarketplaceCommission();
        }

        if (\is_array($this->commissions)) {
            $this->addCommissions();
        }
    }

    public function down(): void
    {
        $this->execute('DELETE FROM `doctrine_commission` WHERE `category_id` IS NULL;');
        $this->execute(
            '
                ALTER TABLE `doctrine_commission`
                    DROP COLUMN `maximum_amount`,
                    DROP COLUMN `commission_type`,
                    MODIFY `category_id` INT(11) NOT NULL,
                    MODIFY `company_id` INT(11) NOT NULL,
                    DROP INDEX commission_company_id_idx,
                    DROP INDEX commission_category_id_idx,
                    DROP INDEX commission_type_idx;
            '
        );
        $this->execute('CREATE UNIQUE INDEX `company_category_index` ON `doctrine_commission` (`category_id`, `company_id`);');
    }

    private function getMarketplaceCommissionPercent(): float
    {
        $dbReturn = $this->fetchRow("SELECT `value` FROM `cscart_settings_objects` WHERE `name` = 'default_commission_percent'");
        if (\is_array($dbReturn) && \array_key_exists('value', $dbReturn)) {
            return (float) $dbReturn['value'];
        }

        return $this->marketplaceCommissionPercent;
    }

    private function getMarketplaceCommissionFixed(): float
    {
        $dbReturn = $this->fetchRow("SELECT `value` FROM `cscart_settings_objects` WHERE `name` = 'default_commission_fixed'");
        if (\is_array($dbReturn) && \array_key_exists('value', $dbReturn)) {
            return (float) $dbReturn['value'];
        }

        return $this->marketplaceCommissionFixed;
    }

    private function addMarketplaceCommission(): void
    {
        $this->execute(
            "
                INSERT INTO `doctrine_commission`(id, percent_amount, fix_amount, commission_type)
                VALUES (UUID(), " . $this->marketplaceCommissionPercent . ", " . $this->marketplaceCommissionFixed . ", 'marketplace')
            "
        );
    }

    private function addCommissions(): void
    {
        foreach ($this->commissions as $companyCommission) {
            if ($this->marketplaceCommissionPercent !== (float) $companyCommission['commission_percent']
                || $this->marketplaceCommissionFixed !== (float) $companyCommission['commission_fixed']
            ) {
                $this->execute(
                    "
                        INSERT INTO `doctrine_commission`(id, company_id, percent_amount, fix_amount, commission_type)
                        VALUES (
                            UUID(),
                            " . $companyCommission['company_id'] . ",
                            " . $companyCommission['commission_percent'] . ",
                            " . $companyCommission['commission_fixed'] . ",
                            'company'
                        )
                    "
                );
            }
        }
    }

    private function updateCommissionType(): void
    {
        $commissions = $this->pdo->prepare('SELECT id, company_id, category_id FROM `doctrine_commission`');
        $commissions->execute();

        while ($commission = $commissions->fetch(PDO::FETCH_ASSOC)) {
            if (\intval($commission['company_id']) === 0 && \intval($commission['category_id']) === 0) {
                $this->execute(
                    "
                        UPDATE `doctrine_commission`
                        SET commission_type = '" . CommissionType::COMMISSION_MARKETPLACE() . "',
                        company_id = null,
                        category_id = null
                        WHERE id = '" . $commission['id'] . "'
                    "
                );
            } elseif (\intval($commission['company_id']) === 0 && \intval($commission['category_id']) !== 0) {
                $this->execute(
                    "
                        UPDATE `doctrine_commission`
                        SET commission_type = '" . CommissionType::COMMISSION_CATEGORY() . "',
                        company_id = null
                        WHERE id = '" . $commission['id'] . "'
                    "
                );
            } elseif (\intval($commission['company_id']) !== 0 && \intval($commission['category_id']) === 0) {
                $this->execute(
                    "
                        UPDATE `doctrine_commission`
                        SET commission_type = '" . CommissionType::COMMISSION_COMPANY() . "',
                        category_id = null
                        WHERE id = '" . $commission['id'] . "'
                    "
                );
            } elseif (\intval($commission['company_id']) !== 0 && \intval($commission['category_id']) !== 0) {
                $this->execute(
                    "
                        UPDATE `doctrine_commission`
                        SET commission_type = '" . CommissionType::COMMISSION_CATEGORY_COMPANY() . "'
                        WHERE id = '" . $commission['id'] . "'
                    "
                );
            }
        }
    }
}
