<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class RenameDotNotCreateInvoiceNote extends AbstractMigration
{
    protected const TRANSLATIONS = [
        [
            'key' => 'do_not_create_invoice',
            'lang' => 'fr',
            'original' => 'Ne pas créer de facture',
            'new' => 'Ne pas générer de numéro de facture'
        ],
        [
            'key' => 'do_not_create_invoice',
            'lang' => 'en',
            'original' => 'Ne pas créer de facture',
            'new' => 'Do not generate invoice number'
        ],
        [
            'key' => 'order_proceed_without_invoice_confirm',
            'lang' => 'fr',
            'original' => 'Etes-vous sûr de vouloir ne pas créer de Facture pour cette commande ?',
            'new' => 'Etes-vous sûr de vouloir ne pas générer de numéro de facture pour cette commande ?'
        ],
        [
            'key' => 'order_proceed_without_invoice_confirm',
            'lang' => 'en',
            'original' => 'Are you sure you do not want to create an invoice for this order?',
            'new' => 'Are you sure you do not want to generate an invoice number for this order?'
        ],
        [
            'key' => 'do_not_create_credit',
            'lang' => 'fr',
            'original' => "Ne pas créer d'avoir",
            'new' => "Ne pas générer de numéro d'avoir",
        ],
        [
            'key' => 'do_not_create_credit',
            'lang' => 'en',
            'original' => 'Do not create credit note',
            'new' => 'Do not generate credit note number',
        ],
    ];

    public function up(): void
    {
        foreach (static::TRANSLATIONS as $translation) {
            $this->execute('
                UPDATE cscart_language_values
                SET value="' . $translation['new'] . '"
                WHERE name="' . $translation['key'] . '"
                AND lang_code = "' . $translation['lang'] . '"
                AND value="' . $translation['original'] . '";
            ');
        }
    }

    public function down(): void
    {
        foreach (static::TRANSLATIONS as $translation) {
            $this->execute('
                UPDATE cscart_language_values
                SET value="' . $translation['original'] . '"
                WHERE name="' . $translation['key'] . '"
                AND lang_code = "' . $translation['lang'] . '"
                AND value="' . $translation['new'] . '";
            ');
        }
    }
}
