<?php

use Phinx\Migration\AbstractMigration;

class BoV3UndoRenameProductsAndOptions extends AbstractMigration
{
    public function up(): void
    {
        $trad = [
            'options' => [
                'fr' => [
                    'old' => 'Modération options',
                    'new' => 'Options',
                ],
                'en' => [
                    'old' => 'Moderation options',
                    'new' => 'Options',
                ],
            ],
            'products' => [
                'fr' => [
                    'old' => 'Modération',
                    'new' => 'Produits',
                ],
                'en' => [
                    'old' => 'Moderation',
                    'new' => 'Products',
                ]
            ]
        ];
        foreach ($trad as $key => $data) {
            foreach ($data as $lang => $values) {
                $this->query($sql = sprintf("UPDATE cscart_language_values SET value = '%s' WHERE lang_code = '%s' AND name = '%s' AND value = '%s';", $values['new'], $lang, $key, $values['old']));
            }
        }
    }

    public function down()
    {
    }
}
