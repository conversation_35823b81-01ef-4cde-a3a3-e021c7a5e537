<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddColumnSubscriptionIdCscartOrders extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE `cscart_orders` ADD COLUMN `subscription_id` VARCHAR(36) DEFAULT NULL");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE `cscart_orders` DROP COLUMN `subscription_id`");
    }
}
