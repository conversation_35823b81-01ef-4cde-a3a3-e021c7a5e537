<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddEximLayoutRelatedProducts extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<SQL
INSERT INTO cscart_exim_layouts 
VALUES (null, 'general', 'Company id,Product code,Type,Related product,Related company,Description,Extra', 'related_products', 'Y');
SQL
        );
    }

    public function down(): void
    {
        $this->execute(<<<SQL
DELETE FROM cscart_exim_layouts WHERE `pattern_id` = 'related_products';
SQL
        );
    }
}
