<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class FixSpellingMistakesInDefaultVariableOnEmails extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = '<p>Votre commande a été complètement expédiée. </p><p>Merci de nous avoir choisis.</p>'
            WHERE
                `lang_code` = 'fr'
                AND name = 'order_email_header_c';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = '<p>Le marchand n''a pas validé votre commande. Merci de contacter l''administration</p>'
            WHERE
                `lang_code` = 'fr'
                AND name = 'order_email_header_d';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'Merci d''avoir soumis votre organisation, malheureusement cette dernière n''a pas pu être acceptée par l''administration du site. Pour plus d''informations, merci de contacter les administrateurs de la marketplace.'
            WHERE
                `lang_code` = 'fr'
                AND name = 'organisation_disapproved_mail_content';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'Un problème est survenu lors du paiement de votre abonnement <strong>\"[subscriptionName]\"</strong>.<br>Connectez-vous à votre <a href=\"[customerAccountLink]\" target=\"_blank\">espace client</a> ou contactez léquipe commerciale pour régulariser.<br><br>À défaut de régularisation, les services associés à cet abonnement pourront être suspendus.<br>'
            WHERE
                `lang_code` = 'fr'
                AND name = 'subscription_notification_mail_customer_fail';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = '\n        <p>Une de vos commandes fait l''objet d''une demande de retour de la part du client.</p>\n        <p>Le client va procéder à l''expédition du retour à l''adresse suivante :\n            <div>\n                <span>[address]</span>\n            </div>\n            <div>\n                <span>[zipcode] [city] [country] </span>\n            </div>\n        </p>\n        <p>Merci de nous informer dès la réception de la pièce</p>\n        [returned_products]\n        '
            WHERE
                `lang_code` = 'fr'
                AND name = 'w_rma_request_vendor';
            "
        );
    }

    public function down(): void
    {
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = '<p>Votre commande a été completement expédiée. </p><p>Merci de nous avoir choisi.</p>'
            WHERE
                `lang_code` = 'fr'
                AND name = 'order_email_header_c';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = '<p>Le marchand n''a pas validée votre commande. Merci de contacter l''administration</p>'
            WHERE
                `lang_code` = 'fr'
                AND name = 'order_email_header_d';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'Merci d''avoir soumis votre organisation, malheureusement mais cette dernière n''a pas pu être acceptée par l''administration du site. Pour plus d''informations, merci de contacter les administrateurs de la marketplace.'
            WHERE
                `lang_code` = 'fr'
                AND name = 'organisation_disapproved_mail_content';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'Un problème est survenu lors du paiement de votre abonnement <strong>\"[subscriptionName]\"</strong>.<br>Connectez vous à votre <a href=\"[customerAccountLink]\" target=\"_blank\">espace client</a> ou contactez léquipe commerciale pour régulariser.<br><br>À défaut de régularisation, les services associés à cet abonnement pourront être suspendus.<br>'
            WHERE
                `lang_code` = 'fr'
                AND name = 'subscription_notification_mail_customer_fail';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = '\n        <p>Une de vos commande fait l''objet d''une demande de retour de la part du client.</p>\n        <p>Le client va procéder à l''expédition du retour à l''adresse suivante :\n            <div>\n                <span>[address]</span>\n            </div>\n            <div>\n                <span>[zipcode] [city] [country] </span>\n            </div>\n        </p>\n        <p>Merci de nous informer dès la réception de la pièce</p>\n        [returned_products]\n        '
            WHERE
                `lang_code` = 'fr'
                AND name = 'w_rma_request_vendor';
            "
        );
    }
}
