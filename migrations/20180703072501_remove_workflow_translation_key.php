<?php

use Phinx\Migration\AbstractMigration;

class RemoveWorkflowTransla<PERSON><PERSON><PERSON> extends AbstractMigration
{
    public function up()
    {
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_bank_transfer_payment_pending_bank_validation"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_bank_transfer_payment_pending_redirection_to_payment_processor"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_credit_card_payment_pending_bank_validation"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_credit_card_payment_pending_redirection_to_payment_processor"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_delivery_pending_delivery"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_funds_dispatch_pending_funds_dispatch"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_manual_payment_pending_manual_payment"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_order_commitment_pending_order_commitment"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_order_preparation_pending_vendor_preparation_end"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_order_validation_pending_vendor_validation"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_payment_deferment_authorization_pending_authorization"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_wait_payment_deferment_pending_bank_validation"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_wait_payment_deferment_pending_redirection_to_payment_processor"');
        $this->execute('DELETE FROM cscart_language_values WHERE name="workflow_withdrawal_period_pending_withdrawal_period_end"');
    }
}
