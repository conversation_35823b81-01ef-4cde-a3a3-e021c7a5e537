<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class TranslationPaymentTransferNoRib extends AbstractMigration
{
    public function up(): void
    {
        $trad = "        Please send your bank transfer with the following information:<ul><li>Amount: %amount%</li><li>IBAN: %iban%</li><li>BIC: %bic%</li><li>Name: %label%</li><li>Name / Company name: %social_name%</li><li>Address: %address%</li></ul>\r\n        ";

        $this->execute("UPDATE cscart_language_values SET value = '$trad' WHERE name = 'payment_transfer_instructions_no_rib'");
    }
}
