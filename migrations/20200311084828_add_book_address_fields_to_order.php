<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

use Phinx\Migration\AbstractMigration;

class AddBookAddressFieldsToOrder extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<SQL
ALTER TABLE cscart_orders ADD COLUMN b_label VARCHAR(128) NULL AFTER company;
ALTER TABLE cscart_orders ADD COLUMN b_comment  varchar(255) NULL AFTER b_phone;
ALTER TABLE cscart_orders ADD COLUMN s_label VARCHAR(128) NULL AFTER b_comment;
ALTER TABLE cscart_orders ADD COLUMN s_comment  varchar(255) NULL AFTER s_phone;
SQL
        );
    }

    public function down(): void
    {
        $this->execute(<<<SQL
ALTER TABLE cscart_orders DROP COLUMN b_label;
ALTER TABLE cscart_orders DROP COLUMN b_comment;
ALTER TABLE cscart_orders DROP COLUMN s_label;
ALTER TABLE cscart_orders DROP COLUMN s_comment;
SQL
        );
    }
}
