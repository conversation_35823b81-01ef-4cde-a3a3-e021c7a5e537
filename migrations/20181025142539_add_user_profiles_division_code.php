<?php

use Phinx\Migration\AbstractMigration;

class AddUserProfilesDivisionCode extends AbstractMigration
{
    public function up()
    {
        $this->execute('
            ALTER TABLE cscart_user_profiles ADD s_division_code VARCHAR(25) NULL;
            ALTER TABLE cscart_user_profiles
            MODIFY COLUMN profile_name varchar(32) NOT NULL DEFAULT \'\' AFTER s_division_code;
        ');

        $this->execute('
            INSERT INTO cscart_profile_fields (field_id, field_name, profile_show, profile_required, checkout_show, checkout_required, partner_show, partner_required, field_type, POSITION, is_default, SECTION, matching_id, CLASS, autocomplete_type, w_card_show, w_card_required, w_new_line, w_invoice_show, w_invoice_required)
            VALUES (42,
                    \'s_division_code\',
                    \'Y\',
                    \'N\',
                    \'Y\',
                    \'N\',
                    \'N\',
                    \'N\',
                    \'I\',
                    190,
                    \'N\',
                    \'S\',
                    1,
                    \'\',
                    \'\',
                    \'N\',
                    \'N\',
                    \'N\',
                    \'N\',
                    \'N\');      
        ');
    }
}
