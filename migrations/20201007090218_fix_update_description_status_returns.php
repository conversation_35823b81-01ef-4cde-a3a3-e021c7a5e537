<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

/**
 * The purpose of this migration file is to solve a problem with a migration that did not respect the correct format
 * for the file name, which had the consequence of an execution order for the said migration.
 * Deleted migration : 20200615529090990_update_description_status_returns
 */
class FixUpdateDescriptionStatusReturns extends AbstractMigration
{
    public function up(): void
    {
        $results = $this->query(
            "SELECT `version`
                 FROM `migration`
                 WHERE `version` = 20200615529090990;"
        );

        if ($results->rowCount() === 1) {
            return;
        }

        $this->execute('
            UPDATE cscart_status_descriptions
            SET description = "Received"
            WHERE status = "A"
            AND type = "R"
            AND lang_code = "en";
        ');

        $this->execute('
            UPDATE cscart_status_descriptions
            SET description = "Declined"
            WHERE status = "D"
            AND type = "R"
            AND lang_code = "en";
        ');

        $this->execute('
            UPDATE cscart_status_descriptions
            SET description = "Completed"
            WHERE status = "C"
            AND type = "R"
            AND lang_code = "en";
        ');

        $this->execute('
            UPDATE cscart_status_descriptions
            SET description = "Request for return"
            WHERE status = "R"
            AND type = "R"
            AND lang_code = "en";
        ');
    }

    public function down(): void
    {
        $this->execute(
            "DELETE FROM migration
            WHERE version = 20200615529090990;"
        );

        $this->execute('
            UPDATE cscart_status_descriptions
            SET description = null
            WHERE description = "Received"
            AND status = "A"
            AND type = "R"
            AND lang_code = "en";
        ');

        $this->execute('
            UPDATE cscart_status_descriptions
            SET description = null
            WHERE description = "Declined"
            AND status = "D"
            AND type = "R"
            AND lang_code = "en";
        ');

        $this->execute('
            UPDATE cscart_status_descriptions
            SET description = null
            WHERE description = "Completed"
            AND status = "C"
            AND type = "R"
            AND lang_code = "en";
        ');

        $this->execute('
            UPDATE cscart_status_descriptions
            SET description = null
            WHERE description = "Request for return"
            AND status = "R"
            AND type = "R"
            AND lang_code = "en";
        ');
    }
}
