<?php

use Phinx\Migration\AbstractMigration;

class CreateDefaultOrganisationUserGroups extends AbstractMigration
{
    public function up()
    {
        // création du groupe "Administrateur" pour les organisations n'ayant pas le groupe créé
        $this->execute(<<<'SQL'
INSERT INTO doctrine_user_group (`id`, `organisation_id`, `name`, `type`)
SELECT UUID(), id, 'organisation_group_admin_label', 'admin'
FROM doctrine_organisation
WHERE id NOT IN (
    SELECT organisation_id
    FROM doctrine_user_group
    WHERE `type` = "admin"
);
SQL
        );

        // mise à jour de la liaison Organisation <-> Groupe "Administrateur"
        $this->execute(<<<'SQL'
UPDATE doctrine_organisation o
JOIN doctrine_user_group u ON o.id = u.organisation_id AND u.type = "admin"
SET o.admin_group_id = u.id;
SQL
        );

        // création du groupe "Acheteur" pour les organisations n'ayant pas le groupe créé
        $this->execute(<<<'SQL'
INSERT INTO doctrine_user_group (`id`, `organisation_id`, `name`, `type`)
SELECT UUID(), id, 'organisation.group.buyer.label', 'buyer'
FROM doctrine_organisation
WHERE id NOT IN (
    SELECT organisation_id
    FROM doctrine_user_group
    WHERE `type` = "buyer"
);
SQL
        );

        // création du groupe "Créateur de projet" pour les organisations n'ayant pas le groupe créé
        $this->execute(<<<'SQL'
INSERT INTO doctrine_user_group (`id`, `organisation_id`, `name`, `type`)
SELECT UUID(), id, 'organisation.group.project_creator.label', 'project-creator'
FROM doctrine_organisation
WHERE id NOT IN (
    SELECT organisation_id
    FROM doctrine_user_group
    WHERE `type` = "project-creator"
);
SQL
        );
    }
}
