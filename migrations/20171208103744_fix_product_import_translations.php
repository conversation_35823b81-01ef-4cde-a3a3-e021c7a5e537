<?php

use Phinx\Migration\AbstractMigration;

class FixProductImportTranslations extends AbstractMigration
{
    public function up()
    {
        $template = "UPDATE `cscart_language_values` SET `value` = '%s' WHERE `name` = '%s' AND `lang_code` = 'en';";

        $this->execute(sprintf($template, '<h4>Product Attribute Import Format</h4><code>{$ldelim}%Feature ID%{$rdelim} (%Group name%) %Feature name%: %Feature type%[%Feature value%]</code>where,<dl class="dl-horizontal"><dt>%Feature ID%</dt><dd>the id of feature</dd><dt>%Group name%</dt><dd>the name of the feasture group</dd><dt>%Feature name%</dt><dd>the name of the feature</dd><dt>%Feature type%</dt><dd>Feature type (C - Checkbox, M - Multiple checkbox, S - Text select, N - Numver select, E - Extended select, T - simple text, O - Number, D - date)</dd><dt>%Feature value%</dt><dd>the value of the feature (many values can be delimited by a coma)</dd></dl><p>many values can be delimited by a semicolon.</p><h5>Example:</h5><code>ISBN: T[1233423423]; Published at: D[05/05/07]; Color: S[Red]</code>', 'text_exim_import_features_note'));
        $this->execute(sprintf($template, '<h4>Import file format</h4><code>%File location%</code> where,<dl class="dl-horizontal"><dt>%File location%<dt><dd> can be the relative or absolute path on the server or an external URL</dd></dl><p>Many files can be delimited by a coma.</p><h5>Example 1:</h5><code>file1.jpg</code><h5>Example 2:</h5><code>backup/file1.zip, images/file2.jpg</code>', 'text_exim_import_files_note'));
        $this->execute(sprintf($template, '<h4>Image Import Format</h4><code>%Image location%#%Alternative text%</code> où,<br><dl class="dl-horizontal"><dt>%Image location%</dt><dd>can be the relative or absolute path on the server or an external URL,</dd><dt>%Alternative text%<dt><dd>image alternative text</dd></dl><h5>Example 1:</h5><code>images/product_images/apples.jpg#Apples</code><h5>Example 2:</h5><code>http://www.site.com/apples.jpg#Apples</code>', 'text_exim_import_images_note'));
        $this->execute(sprintf($template, '<h4>Product option import format</h4><code>{$ldelim}%Option ID[_L]%{$rdelim}%Option name%: %Option type%[{$ldelim}%Variant 1 ID%{$rdelim}%Variant 1%,{$ldelim}%Variant 2 ID%{$rdelim}%Variant 2%,{$ldelim}%Variant N ID%{$rdelim}%Variant N%]</code> where,<dl class="dl-horizontal"><dt>%Option ID%</dt><dd>option id</dd><dt>L</dt><dd>Option, means that it is a global option linked to the product.</dd><dt>%Option name%</dt><dd>option name,</dd><dt>%Variant N ID%</dt><dd>variant id,</dd><dt>%Variant N%</dt><dd>the name of the variant. Variants must be defined if the option is of the selectbox or radiogroup type.</dd><dt>%Option type%</dt><dd>option type, can be:<br><ul><li><b>S</b> - selectbox,</li><li><b>R</b> - radiogroup,</li><li><b>C</b> - Checkbox,</li><li><b>I</b> - simple input,</li><li><b>T</b> - text box (textarea).</li></ul></dd></dl><p>Many options can be delimited by a semicolon.</p><h5>Examples:</h5>Simple text options:<code>Your age: I; Date of birth: I; Notes: T</code><br>Options with variants:<code>Color: S[Red, Green, Blue]; Size: R[X, XL, XXL]</code>', 'text_exim_import_options_note'));
    }
}
