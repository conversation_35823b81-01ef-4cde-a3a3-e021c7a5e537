<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateBirthdayDatetimeToDate extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            "ALTER TABLE `cscart_users`
                MODIFY `birthday` DATE;"
        );
    }

    public function down(): void
    {
        $this->execute(
            "ALTER TABLE `cscart_users`
                MODIFY `birthday` DATETIME;"
        );
    }
}
