<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateCompanyFieldInProfile extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<SQL
UPDATE cscart_profile_fields SET profile_show = 'Y', position = 20, section = 'F' WHERE field_name = 'company';

SQL
        );
    }

    public function down()
    {
        $this->execute(<<<SQL
UPDATE cscart_profile_fields SET profile_show = 'N', position = 40, section = 'C' WHERE field_name = 'company';
SQL
        );
    }
}
