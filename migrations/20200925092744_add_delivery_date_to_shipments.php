<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddDeliveryDateToShipments extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('ALTER TABLE cscart_shipments ADD delivery_date DATETIME DEFAULT NULL;');

        /**
         * Update shipment delivery_date for order already has been delivered
         * Shipment delivery date = order delivery date
         */
        $this->execute('
            UPDATE cscart_shipments cs
            INNER JOIN  cscart_orders co ON cs.shipping_id = co.shipping_ids
            SET cs.delivery_date = co.delivery_date
            WHERE co.delivered = 1;
        ');
    }

    public function down(): void
    {
        $this->execute('ALTER TABLE cscart_shipments DROP delivery_date;');
    }
}
