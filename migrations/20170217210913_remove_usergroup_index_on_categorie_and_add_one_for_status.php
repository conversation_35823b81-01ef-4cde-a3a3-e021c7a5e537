<?php

use Phinx\Migration\AbstractMigration;

class RemoveUsergroupIndexOnCategorieAndAddOneForStatus extends AbstractMigration
{
    //L'index c_status faisait réference aux usergroup_ids qui ne sont plus utilisés dans les requetes.
    public function up()
    {
        $this->execute("DROP INDEX `c_status` ON `cscart_categories`");
        $this->execute("CREATE INDEX `status_categoryid`  ON `cscart_categories` (status, parent_id) ");
    }
}
