<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddBookAddressFieldsToProfile extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<SQL
INSERT INTO cscart_profile_fields VALUES (50, 'b_address_id', 'N', 'N', 'N', 'N', 'N', 'N', 'I', 75, 'N', 'B', 0, '', '', 'N', 'N', 'N', 'N', 'N');
ALTER TABLE cscart_user_profiles ADD COLUMN b_address_id  char(36) COLLATE 'utf8mb4_unicode_ci' NULL AFTER profile_type;
ALTER TABLE cscart_user_profiles ADD CONSTRAINT fk_b_address_id FOREIGN KEY (b_address_id) REFERENCES doctrine_address_book(id);

INSERT INTO cscart_profile_fields VALUES (51, 'b_label', 'Y', 'N', 'Y', 'N', 'N', 'N', 'I', 70, 'N', 'B', 0, '', '', 'N', 'N', 'N', 'N', 'Y');
INSERT INTO cscart_profile_field_descriptions VALUES (51, 'Titre de l''adresse', 'F', 'fr');
INSERT INTO cscart_profile_field_descriptions VALUES (51, 'Address title', 'F', 'en');
ALTER TABLE cscart_user_profiles ADD COLUMN b_label VARCHAR(128) NULL AFTER b_address_id;

INSERT INTO cscart_profile_fields VALUES (52, 'b_comment', 'Y', 'N', 'Y', 'N', 'N', 'N', 'I', 200, 'N', 'B', 0, '', '', 'N', 'N', 'N', 'N', 'Y');
INSERT INTO cscart_profile_field_descriptions VALUES (52, 'Commentaire', 'F', 'fr');
INSERT INTO cscart_profile_field_descriptions VALUES (52, 'Comment', 'F', 'en');
ALTER TABLE cscart_user_profiles ADD COLUMN b_comment  varchar(255) NULL AFTER b_division_code;

INSERT INTO cscart_profile_fields VALUES (53, 's_address_id', 'N', 'N', 'N', 'N', 'N', 'N', 'I', 75, 'N', 'S', 0, '', '', 'N', 'N', 'N', 'N', 'N');
ALTER TABLE cscart_user_profiles ADD COLUMN s_address_id  char(36) COLLATE 'utf8mb4_unicode_ci' NULL AFTER b_comment;
ALTER TABLE cscart_user_profiles ADD CONSTRAINT fk_s_address_id FOREIGN KEY (s_address_id) REFERENCES doctrine_address_book(id);

INSERT INTO cscart_profile_fields VALUES (54, 's_label', 'Y', 'N', 'Y', 'N', 'N', 'N', 'I', 70, 'N', 'S', 0, '', '', 'N', 'N', 'N', 'N', 'Y');
INSERT INTO cscart_profile_field_descriptions VALUES (54, 'Titre de l''adresse', 'F', 'fr');
INSERT INTO cscart_profile_field_descriptions VALUES (54, 'Address title', 'F', 'en');
ALTER TABLE cscart_user_profiles ADD COLUMN s_label VARCHAR(128) NULL AFTER s_address_id;

INSERT INTO cscart_profile_fields VALUES (55, 's_comment', 'Y', 'N', 'Y', 'N', 'N', 'N', 'I', 200, 'N', 'S', 0, '', '', 'N', 'N', 'N', 'N', 'Y');
INSERT INTO cscart_profile_field_descriptions VALUES (55, 'Commentaire', 'F', 'fr');
INSERT INTO cscart_profile_field_descriptions VALUES (55, 'Comment', 'F', 'en');
ALTER TABLE cscart_user_profiles ADD COLUMN s_comment  varchar(255) NULL AFTER s_division_code;
SQL
        );
    }

    public function down(): void
    {
        $this->execute(<<<SQL
DELETE FROM cscart_profile_fields WHERE `field_id` = 50;
ALTER TABLE cscart_user_profiles DROP FOREIGN KEY fk_b_address_id;
ALTER TABLE cscart_user_profiles DROP INDEX fk_b_address_id;
ALTER TABLE cscart_user_profiles DROP COLUMN b_address_id;

DELETE FROM cscart_profile_fields WHERE `field_id` = 51;
DELETE FROM cscart_profile_field_descriptions WHERE `object_id` = 51;
ALTER TABLE cscart_user_profiles DROP COLUMN b_label;

DELETE FROM cscart_profile_fields WHERE `field_id` = 52;
DELETE FROM cscart_profile_field_descriptions WHERE `object_id` = 52;
ALTER TABLE cscart_user_profiles DROP COLUMN b_comment;

DELETE FROM cscart_profile_fields WHERE `field_id` = 53;
ALTER TABLE cscart_user_profiles DROP FOREIGN KEY fk_s_address_id;
ALTER TABLE cscart_user_profiles DROP INDEX fk_s_address_id;
ALTER TABLE cscart_user_profiles DROP COLUMN s_address_id;

DELETE FROM cscart_profile_fields WHERE `field_id` = 54;
DELETE FROM cscart_profile_field_descriptions WHERE `object_id` = 54;
ALTER TABLE cscart_user_profiles DROP COLUMN s_label;

DELETE FROM cscart_profile_fields WHERE `field_id` = 55;
DELETE FROM cscart_profile_field_descriptions WHERE `object_id` = 55;
ALTER TABLE cscart_user_profiles DROP COLUMN s_comment;
SQL
        );
    }
}
