<?php

use Phinx\Migration\AbstractMigration;

class AddDolistTemplateOrderRefunded extends AbstractMigration
{
    protected const TEMPLATES = [
        'order_refunded_admin' => 40,
        'order_refunded_vendor' => 41,
        'order_refunded_customer' => 42,
    ];

    public function up()
    {
        foreach (static::TEMPLATES as $templateType => $templateId) {
            $this->execute("
                INSERT INTO doctrine_dolist_template (template_type, template_id)
                VALUES ('{$templateType}', '{$templateId}');
            ");
        }
    }

    public function down()
    {
        $this->execute('
            DELETE FROM doctrine_dolist_template
            WHERE template_id IN (' . implode(',', array_values(static::TEMPLATES)) . ')
        ');
    }
}
