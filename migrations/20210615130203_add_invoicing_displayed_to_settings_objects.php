<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddInvoicingDisplayedToSettingsObjects extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<'SQL'
INSERT INTO `cscart_settings_objects`
(`edition_type`, `name`, `section_id`, `section_tab_id`, `type`, `value`, `position`, `is_global`, `handler`)
VALUES (295, 'invoicing_disabled', '0', '0', 'C', 'N', '0', 'Y', NULL);
SQL
        );
    }

    public function down()
    {
        $this->execute(<<<SQL
DELETE FROM `cscart_settings_objects` WHERE `name` = 'invoicing_disabled';
SQL
        );
    }
}
