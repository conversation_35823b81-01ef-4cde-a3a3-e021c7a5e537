<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdatePaymentProcessorsScript extends AbstractMigration
{
    public function up(): void
    {
        $query = "UPDATE cscart_payment_processors SET processor_script = 'manualpayment.php' WHERE processor_id = 1010";
        $this->execute($query);
    }

    public function down(): void
    {
        $query = "UPDATE cscart_payment_processors SET processor_script = 'nopayment.php' WHERE processor_id = 1010";
        $this->execute($query);
    }
}
