<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddLemonwaySepaDirectDebitPaymentMethod extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<SQL
            INSERT INTO `cscart_payment_processors` (`processor_id`, `processor`, `processor_script`, `admin_template`, `callback`, `type`) VALUES
            (1015, 'Lemonway SEPA direct', 'lemonway_sepa_direct.php', 'lemonway_sepa.tpl', 'N', 'P'),
            (1016, 'Lemonway SEPA échéance', 'lemonway_sepa_deferment.php', 'lemonway_sepa.tpl', 'N', 'P');
            SQL
        );
    }

    public function down(): void
    {
        $this->execute("DELETE FROM `cscart_payment_processors` WHERE `processor_id` = 1015 OR `processor_id` = 1016");
    }
}
