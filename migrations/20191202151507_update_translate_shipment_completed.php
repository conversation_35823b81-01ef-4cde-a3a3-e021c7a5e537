<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateTranslateShipmentCompleted extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("UPDATE cscart_language_values SET value = 'La commande a été intégralement remise au %shipment_name%.' WHERE name = 'shipment_completed' AND lang_code = 'fr';");

        $this->execute("UPDATE cscart_language_values SET value = 'The order has been delivered to %shipment_name% in full.' WHERE name = 'shipment_completed' AND lang_code = 'en';");
    }

    public function down(): void
    {
        $this->execute("UPDATE cscart_language_values SET value = 'La commande a été intégralement remise au transporteur.' WHERE name = 'shipment_completed' AND lang_code = 'fr';");

        $this->execute("UPDATE cscart_language_values SET value = 'The order has been delivered to the carrier in full.' WHERE name = 'shipment_completed' AND lang_code = 'en';");
    }
}
