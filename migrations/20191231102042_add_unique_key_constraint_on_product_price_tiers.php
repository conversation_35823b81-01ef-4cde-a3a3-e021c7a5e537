<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddUniqueKeyConstraintOnProductPriceTiers extends AbstractMigration
{

    public function up(): void
    {
        $this->execute('ALTER TABLE doctrine_product_price_tiers ADD product_id_lower_limit_option_id VARCHAR(255) DEFAULT \'\' NOT NULL');
        $this->execute('UPDATE doctrine_product_price_tiers SET product_id_lower_limit_option_id = CONCAT_WS(\'_\', product_id, lower_limit, product_option_inventory_id)');
        $this->execute('ALTER TABLE doctrine_product_price_tiers ADD CONSTRAINT UNIQUE (product_id_lower_limit_option_id)');
    }

    public function down(): void
    {
        $this->execute('ALTER TABLE doctrine_product_price_tiers DROP COLUMN product_id_lower_limit_option_id');
    }
}
