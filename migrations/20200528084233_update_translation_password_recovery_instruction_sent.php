<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

use Phinx\Migration\AbstractMigration;

class UpdateTranslationPasswordRecoveryInstructionSent extends AbstractMigration
{
    public function up()
    {
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Si votre email existe, nous venons de vous envoyer les instructions de récupération de mot de passe."
            WHERE name = "text_password_recovery_instructions_sent"
            AND value = "Les instructions de récupération de mot de passe ont été envoyées à votre email."
            AND lang_code = "fr";
        ');
        $this->execute('
            UPDATE cscart_language_values
            SET value = "If your email exists, we have just sent you the password recovery instructions."
            WHERE name = "text_password_recovery_instructions_sent"
            AND value = "Password recovery"
            AND lang_code = "en";
        ');
    }

    public function down()
    {
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Les instructions de récupération de mot de passe ont été envoyées à votre email."
            WHERE name = "text_password_recovery_instructions_sent"
            AND value = "Si votre email existe, nous venons de vous envoyer les instructions de récupération de mot de passe."
            AND lang_code = "fr";
        ');
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Password recovery"
            WHERE name = "text_password_recovery_instructions_sent"
            AND value = "If your email exists, we have just sent you the password recovery instructions."
            AND lang_code = "en";
        ');
    }
}
