<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateUserBirthdayTypeFromIntToDate extends AbstractMigration
{
    public function up(): void
    {
        // Rename column from birthday to old_birthday
        $this->execute("ALTER TABLE cscart_users Change birthday old_birthday INT NULL;");
        // Add column birthday type date, can be NULL
        $this->execute("ALTER TABLE cscart_users ADD birthday DATETIME NULL AFTER old_birthday;");

        // Select Users where they has birthday data
        $connection = $this->getAdapter()->getConnection();
        $statement = $connection->prepare('SELECT user_id, old_birthday FROM cscart_users WHERE old_birthday != :birthday');
        $statement->execute([
            'birthday' => 0
        ]);

        while ($user = $statement->fetch()) {
            // UPDATE User birthday to put birthday to DATETIME
            $update = $connection->prepare("UPDATE cscart_users set birthday = :birthday WHERE user_id = :user_id ");
            $update->execute([
                'birthday' => DateTime::createFromFormat('U', (string) $user['old_birthday'])->format("Y-m-d"),
                'user_id' => $user['user_id']
            ]);
        };

        // Remove column old_birthday
        $this->execute("ALTER TABLE cscart_users DROP COLUMN old_birthday;");
    }

    public function down(): void
    {
        // Rename column from birthday to old_birthday
        $this->execute("ALTER TABLE cscart_users Change birthday old_birthday DATETIME;");
        // Add column birthday type INT, default be 0
        $this->execute("ALTER TABLE cscart_users ADD birthday INT DEFAULT 0 AFTER old_birthday;");

        // Select Users where they has birthday data
        $connection = $this->getAdapter()->getConnection();
        $statement = $connection->prepare('SELECT user_id, old_birthday FROM cscart_users WHERE old_birthday IS NOT NULL');
        $statement->execute();

        while ($user = $statement->fetch()) {
            // UPDATE User birthday to put birthday to DATETIME
            $old_birthday = new DateTime($user['old_birthday']);
            $update = $connection->prepare("UPDATE cscart_users set birthday = :birthday WHERE user_id = :user_id ");
            $update->execute([
                'birthday' => (int) $old_birthday->getTimestamp(),
                'user_id' => (int) $user['user_id']
            ]);
        };
        // Remove column old_birthday
        $this->execute("ALTER TABLE cscart_users DROP COLUMN old_birthday;");
    }
}
