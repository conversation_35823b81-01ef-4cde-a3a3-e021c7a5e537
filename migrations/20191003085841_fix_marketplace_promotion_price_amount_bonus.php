<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class FixMarketplacePromotionPriceAmountBonus extends AbstractMigration
{
    public function up(): void
    {
        foreach ($this->getPromotions() as $promotion) {
            $this->updateRules($promotion['id'], (int) $promotion['reduction_fixed'] / 100);
        }
    }

    public function down(): void
    {
        foreach ($this->getPromotions() as $promotion) {
            $this->updateRules($promotion['id'], (int) $promotion['reduction_fixed'] * 100);
        }
    }

    private function getPromotions()
    {
        return $this->query('
            SELECT b.id, b.reduction_fixed, b.promotion_id
            FROM doctrine_promotion AS p
            LEFT JOIN doctrine_bonus AS b ON p.id = b.promotion_id
            WHERE p.type = "marketplace"
            AND b.bonus_type = "fixed"
            AND b.reduction_fixed > 0
        ');
    }

    private function updateRules(string $id, float $amount): int
    {
        return  $this->execute('UPDATE doctrine_bonus SET reduction_fixed = "' . $amount . '" WHERE id="' . $id . '"');
    }
}
