<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateImportDivisionsTranslations extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Divisions imported with success"
            WHERE name = "exim_available_offers_division_import_success"
            AND value = "Division imported with success"
            AND lang_code = "en"
        ');
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Divisions importées avec succès"
            WHERE name = "exim_available_offers_division_import_success"
            AND value = "Division importée avec succès"
            AND lang_code = "fr"
        ');
    }

    public function down(): void
    {
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Division imported with success"
            WHERE name = "exim_available_offers_division_import_success"
            AND value = "Divisions imported with success"
            AND lang_code = "en"
        ');
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Division importée avec succès"
            WHERE name = "exim_available_offers_division_import_success"
            AND value = "Divisions importées avec succès"
            AND lang_code = "fr"
        ');
    }
}
