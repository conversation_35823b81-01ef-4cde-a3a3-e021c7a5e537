<?php

use Phinx\Db\Adapter\MysqlAdapter;
use Phinx\Migration\AbstractMigration;

class ProperOptionVariantIdsInCscartProductOptionsExceptions extends AbstractMigration
{
    private const TABLE_NAME_EXCEPTIONS = 'cscart_product_options_exceptions';
    private const TABLE_NAME_OPTIONS = 'cscart_product_options';
    private const TABLE_NAME_VARIANTS = 'cscart_product_option_variants';

    private const COMBINATION = 'combination';
    private const OPTION_ID = 'option_id';
    private const VARIANT_ID = 'variant_id';

    public function up(): void
    {
        $this->createColumns();
        $this->migrateData();
        $this->purgeOrphans();
        $this->createIndexes();
    }

    public function down(): void
    {
        $table = $this->table(static::TABLE_NAME_EXCEPTIONS);

        $table
            ->dropForeignKey([static::OPTION_ID])
            ->dropForeignKey([static::VARIANT_ID])
            ->save()
        ;
        $table
            ->removeColumn(static::OPTION_ID)
            ->removeColumn(static::VARIANT_ID)
            ->save()
        ;
    }

    private function createColumns(): void
    {
        try {
            $this
                ->table(static::TABLE_NAME_EXCEPTIONS)
                ->addColumn(
                    static::OPTION_ID,
                    'integer',
                    [
                        'signed' => false,
                        'limit' => MysqlAdapter::INT_MEDIUM,
                        'after' => static::COMBINATION,
                    ]
                )
                ->addColumn(
                    static::VARIANT_ID,
                    'integer',
                    [
                        'signed' => false,
                        'limit' => MysqlAdapter::INT_MEDIUM,
                        'after' => static::OPTION_ID,
                    ]
                )
                ->update()
            ;
        } catch (\PDOException $e) {
            // already created
        }
    }

    private function createIndexes(): void
    {
        $this
            ->table(static::TABLE_NAME_EXCEPTIONS)
            ->addForeignKeyWithName(
                'fk_option_id',
                [static::OPTION_ID],
                static::TABLE_NAME_OPTIONS,
                [static::OPTION_ID],
                [
                    'delete' => 'RESTRICT',
                ]
            )
            ->addForeignKeyWithName(
                'fk_variant_id',
                [static::VARIANT_ID],
                static::TABLE_NAME_VARIANTS,
                [static::VARIANT_ID],
                [
                    'delete' => 'RESTRICT',
                ]
            )
            ->save()
        ;
    }

    private function migrateData(): void
    {
        $readStatement = $this->adapter->getConnection()->prepare(
            \sprintf(
                <<<SQL
                SELECT *
                FROM %s
                SQL,
                static::TABLE_NAME_EXCEPTIONS
            )
        );

        $this->adapter->beginTransaction();
        $writeStatement = $this->adapter->getConnection()->prepare(
            \sprintf(
                <<<SQL
                UPDATE
                    %s e
                SET
                    e.option_id = :option_id,
                    e.variant_id = :variant_id
                WHERE
                    e.product_id = :product_id
                    AND e.combination = :combination
                SQL,
                static::TABLE_NAME_EXCEPTIONS
            )
        );

        $readStatement->execute();

        while ($row = $readStatement->fetch()) {
            $data = \unserialize($row['combination']);

            $updateRow = [
                'product_id' => $row['product_id'],
                'combination' => $row['combination'],
                'option_id' => \key($data),
                'variant_id' => \current($data),
            ];

            $writeStatement->execute($updateRow);
        }

        $this->adapter->commitTransaction();
    }

    private function purgeOrphans()
    {
        $connection = $this->adapter->getConnection();
        $connection->beginTransaction();

        $deleteStatement = $this->adapter->getConnection()->prepare(
            \sprintf(
                <<<SQL
                DELETE e
                FROM %s e
                    LEFT JOIN %s o ON
                        o.option_id = e.option_id
                    LEFT JOIN %s v ON
                        v.variant_id = e.variant_id
                WHERE
                    (
                        o.option_id IS NULL
                        OR v.variant_id IS NULL
                    )
                SQL,
                static::TABLE_NAME_EXCEPTIONS,
                static::TABLE_NAME_OPTIONS,
                static::TABLE_NAME_VARIANTS
            )
        );

        $deleteStatement->execute();

        $connection->commit();
    }
}
