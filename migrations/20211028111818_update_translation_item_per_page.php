<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

use Phinx\Migration\AbstractMigration;

class UpdateTranslationItemPerPage extends AbstractMigration
{
    public function up()
    {
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Items per page"
            WHERE name = "view_by"
            AND value = "Afficher par"
            AND lang_code = "en";
        ');
    }

    public function down()
    {
        $this->execute('
            UPDATE cscart_language_values
            SET value = "Afficher par"
            WHERE name = "view_by"
            AND value = "Items per page"
            AND lang_code = "en";
        ');
    }
}
