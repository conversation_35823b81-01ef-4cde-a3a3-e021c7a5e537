<?php

use Phinx\Migration\AbstractMigration;

class ProfileFieldsTitleNoIntegerId extends AbstractMigration
{
    public function up()
    {
        $this->execute("UPDATE cscart_profile_fields SET field_name='title' WHERE field_id='36';");
        $this->execute("UPDATE cscart_profile_fields SET field_name='b_title' WHERE field_id='37';");
        $this->execute("UPDATE cscart_profile_fields SET field_name='s_title' WHERE field_id='38';");


        $this->execute("ALTER TABLE `cscart_user_profiles` ADD COLUMN `b_title` VARCHAR(10) NOT NULL AFTER profile_type;");
        $this->execute("ALTER TABLE `cscart_user_profiles` ADD COLUMN `s_title` VARCHAR(10) NOT NULL AFTER b_phone;");

        $this->execute("ALTER TABLE `cscart_orders` ADD COLUMN `b_title` VARCHAR(10) NOT NULL AFTER company;");
        $this->execute("ALTER TABLE `cscart_orders` ADD COLUMN `s_title` VARCHAR(10) NOT NULL AFTER b_phone;");


        $this->execute("ALTER TABLE `cscart_users` ADD COLUMN `title` VARCHAR(10) NOT NULL AFTER salt;");

        // Data migration in new fields
        // User field
        $result = $this->query('SELECT object_id AS user_id, value FROM cscart_profile_fields_data WHERE field_id = 36 AND object_type = "U"');

        while ($row = $result->fetch()) {
            if ($row['value'] == 1) {
                $title  = 'mr';
            } elseif ($row['value'] == 2) {
                $title  = 'mrs';
            } else {
                continue;
            }

            $userId = $row['user_id'];
            $this->execute("UPDATE cscart_users SET title='$title' WHERE user_id=$userId;");
        }

        // Billing field
        $result = $this->query('SELECT object_id AS user_id, value FROM cscart_profile_fields_data WHERE field_id = 37 AND object_type = "P"');

        while ($row = $result->fetch()) {
            if ($row['value'] == 4) {
                $title  = 'mr';
            } elseif ($row['value'] == 3) {
                $title  = 'mrs';
            } else {
                continue;
            }

            $userId = $row['user_id'];
            $this->execute("UPDATE cscart_user_profiles SET b_title='$title' WHERE user_id=$userId;");
        }

        // Shipping field
        $result = $this->query('SELECT object_id AS user_id, value FROM cscart_profile_fields_data WHERE field_id = 38 AND object_type = "P"');

        while ($row = $result->fetch()) {
            if ($row['value'] == 4) {
                $title  = 'mr';
            } elseif ($row['value'] == 3) {
                $title  = 'mrs';
            } else {
                continue;
            }

            $userId = $row['user_id'];
            $this->execute("UPDATE cscart_user_profiles SET s_title='$title' WHERE user_id=$userId;");
        }

        // Orders
        // Billing field
        $result = $this->query('SELECT object_id AS order_id, value FROM cscart_profile_fields_data WHERE field_id = 37 AND object_type = "O"');

        while ($row = $result->fetch()) {
            if ($row['value'] == 4) {
                $title  = 'mr';
            } elseif ($row['value'] == 3) {
                $title  = 'mrs';
            } else {
                continue;
            }

            $orderId = $row['order_id'];
            $this->execute("UPDATE cscart_orders SET b_title='$title' WHERE order_id=$orderId;");
        }

        // Shipping field
        $result = $this->query('SELECT object_id AS order_id, value FROM cscart_profile_fields_data WHERE field_id = 38 AND object_type = "O"');

        while ($row = $result->fetch()) {
            if ($row['value'] == 4) {
                $title  = 'mr';
            } elseif ($row['value'] == 3) {
                $title  = 'mrs';
            } else {
                continue;
            }

            $orderId = $row['order_id'];
            $this->execute("UPDATE cscart_orders SET s_title='$title' WHERE order_id=$orderId;");
        }
    }
}
