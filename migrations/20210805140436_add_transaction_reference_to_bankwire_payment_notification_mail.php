<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddTransactionReferenceToBankwirePaymentNotificationMail extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<'SQL'
            UPDATE cscart_language_values
            SET value = "Instructions de virement de référence %$transactionReference% pour la commande %orderId% de %amount%"
            WHERE name = "bankwire_payment_notification_subject"
            AND lang_code = "fr"
            AND value = "Instructions de virement pour la commande %orderId% de %amount%";
        SQL);

        $this->execute(<<<'SQL'
            UPDATE cscart_language_values
            SET value = "Bank transfer instructions with reference %$transactionReference% for order %orderId% of %amount%"
            WHERE name = "bankwire_payment_notification_subject"
            AND lang_code = "en"
            AND value = "Bank transfer instructions for order %orderId% of %amount%";
        SQL);

        $this->execute(<<<'SQL'
            UPDATE cscart_language_values
            SET value = "<p>Pour finaliser votre commande %orderId% du %orderDate%, veuillez envoyer votre virement bancaire avec les informations suivantes :</p>
                <p>
                    Montant : %amount% <br>
                    IBAN : %rib% <br>
                    BIC : %bic% <br>
                    Référence : %label% <br>
                    Nom / Raison sociale : %social_name% <br>
                    Adresse : %address% <br>
                    Référence virement bancaire  : %$transactionReference% <br>
                </p>
                <p>
                    Vous recevrez une confirmation par email dès réception du virement habituellement sous 3 jours ouvrables.<br>
                    Pour respecter ce délai de traitement, veuillez bien renseigner la bonne référence ci-dessus dans le commentaire lors de l’envoi du virement.
                </p>"
            WHERE name = "bankwire_payment_notification_body"
            AND lang_code = "fr"
            AND value = "<p>Pour finaliser votre commande %orderId% du %orderDate%, veuillez envoyer votre virement bancaire avec les informations suivantes :</p>
                <p>
                    Montant : %amount% <br>
                    IBAN : %rib% <br>
                    BIC : %bic% <br>
                    Référence : %label% <br>
                    Nom / Raison sociale : %social_name% <br>
                    Adresse : %address% <br>
                </p>
                <p>
                    Vous recevrez une confirmation par email dès réception du virement habituellement sous 3 jours ouvrables.<br>
                    Pour respecter ce délai de traitement, veuillez bien renseigner la bonne référence ci-dessus dans le commentaire lors de l’envoi du virement.
                </p>";
        SQL);

        $this->execute(<<<'SQL'
            UPDATE cscart_language_values
            SET value = "<p>To confirm your order %orderId% on %orderDate%, please send us a bank transfer with the following:</p>
            <p>
                Amount: %amount% <br>
                IBAN: %rib% <br>
                BIC: %bic% <br>
                Reference: %label% <br>
                Name: %social_name% <br>
                Address: %address% <br>
                Bankwire reference : %$transactionReference% <br>
            </p>
            <p>
                You will receive an email once your bank transfer is confirmed usually under 3 working days. <br>
                To meet those deadlines, please ensure you fill the correct reference in the comment of your bank transfer.
            </p>"
            WHERE name = "bankwire_payment_notification_body"
            AND lang_code = "en"
            AND value = "<p>To confirm your order %orderId% on %orderDate%, please send us a bank transfer with the following:</p>
                <p>
                    Amount: %amount% <br>
                    IBAN: %rib% <br>
                    BIC: %bic% <br>
                    Reference: %label% <br>
                    Name: %social_name% <br>
                    Address: %address% <br>
                </p>
                <p>
                    You will receive an email once your bank transfer is confirmed usually under 3 working days. <br>
                    To meet those deadlines, please ensure you fill the correct reference in the comment of your bank transfer.
                </p>";
        SQL);
    }

    public function down(): void
    {
        $this->execute(<<<'SQL'
            UPDATE cscart_language_values
            SET value = "Instructions de virement pour la commande %orderId% de %amount%"
            WHERE name = "bankwire_payment_notification_subject"
            AND lang_code = "fr"
            AND value ="Instructions de virement de référence %$transactionReference% pour la commande %orderId% de %amount%";
        SQL);

        $this->execute(<<<'SQL'
            UPDATE cscart_language_values
            SET value = "Bank transfer instructions for order %orderId% of %amount%"
            WHERE name = "bankwire_payment_notification_subject"
            AND lang_code = "en"
            AND value = "Bank transfer instructions with reference %$transactionReference% for order %orderId% of %amount%";
        SQL);

        $this->execute(<<<'SQL'
            UPDATE cscart_language_values
            SET value = "<p>Pour finaliser votre commande %orderId% du %orderDate%, veuillez envoyer votre virement bancaire avec les informations suivantes :</p>
                <p>
                    Montant : %amount% <br>
                    IBAN : %rib% <br>
                    BIC : %bic% <br>
                    Référence : %label% <br>
                    Nom / Raison sociale : %social_name% <br>
                    Adresse : %address% <br>
                </p>
                <p>
                    Vous recevrez une confirmation par email dès réception du virement habituellement sous 3 jours ouvrables.<br>
                    Pour respecter ce délai de traitement, veuillez bien renseigner la bonne référence ci-dessus dans le commentaire lors de l’envoi du virement.
                </p>"
            WHERE name = "bankwire_payment_notification_body"
            AND lang_code = "fr"
            AND value = "<p>Pour finaliser votre commande %orderId% du %orderDate%, veuillez envoyer votre virement bancaire avec les informations suivantes :</p>
                <p>
                    Montant : %amount% <br>
                    IBAN : %rib% <br>
                    BIC : %bic% <br>
                    Référence : %label% <br>
                    Nom / Raison sociale : %social_name% <br>
                    Adresse : %address% <br>
                    Référence virement bancaire  : %$transactionReference% <br>
                </p>
                <p>
                    Vous recevrez une confirmation par email dès réception du virement habituellement sous 3 jours ouvrables.<br>
                    Pour respecter ce délai de traitement, veuillez bien renseigner la bonne référence ci-dessus dans le commentaire lors de l’envoi du virement.
                </p>";
        SQL);

        $this->execute(<<<'SQL'
            UPDATE cscart_language_values
            SET value = "<p>To confirm your order %orderId% on %orderDate%, please send us a bank transfer with the following:</p>
                <p>
                    Amount: %amount% <br>
                    IBAN: %rib% <br>
                    BIC: %bic% <br>
                    Reference: %label% <br>
                    Name: %social_name% <br>
                    Address: %address% <br>
                </p>
                <p>
                    You will receive an email once your bank transfer is confirmed usually under 3 working days. <br>
                    To meet those deadlines, please ensure you fill the correct reference in the comment of your bank transfer.
                </p>"
            WHERE name = "bankwire_payment_notification_body"
            AND lang_code = "en"
            AND value = "<p>To confirm your order %orderId% on %orderDate%, please send us a bank transfer with the following:</p>
                <p>
                    Amount: %amount% <br>
                    IBAN: %rib% <br>
                    BIC: %bic% <br>
                    Reference: %label% <br>
                    Name: %social_name% <br>
                    Address: %address% <br>
                    Bankwire reference : %$transactionReference% <br>
                </p>
                <p>
                    You will receive an email once your bank transfer is confirmed usually under 3 working days. <br>
                    To meet those deadlines, please ensure you fill the correct reference in the comment of your bank transfer.
                </p>";
        SQL);
    }
}
