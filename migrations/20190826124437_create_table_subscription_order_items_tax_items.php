<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class CreateTableSubscriptionOrderItemsTaxItems extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE doctrine_credit_card DROP PRIMARY KEY");
        $this->execute("ALTER TABLE doctrine_credit_card ADD PRIMARY KEY (id);");
        $this->execute("ALTER TABLE doctrine_credit_card MODIFY user_id MEDIUMINT(8) UNSIGNED NOT NULL;");
        $this->execute("CREATE INDEX IDX_507054D8A76ED395 ON doctrine_credit_card (user_id);");
        $this->execute("ALTER TABLE doctrine_credit_card ADD CONSTRAINT FK_507054D8A76ED395 FOREIGN KEY (user_id) REFERENCES cscart_users (user_id);");

        $this->execute(<<<SQL
CREATE TABLE doctrine_order_item (
    id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)',
    category_id INT NOT NULL,
    product_id INT NOT NULL,
    product_code VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)',
    product_name VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)',
    product_is_renewable TINYINT(1) NOT NULL,
    product_option_inventory_code VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)',
    quantity INT UNSIGNED NOT NULL,
    unit_price BIGINT UNSIGNED NOT NULL COMMENT '(DC2Type:money)',
    total_price BIGINT UNSIGNED NOT NULL COMMENT '(DC2Type:money)',
    price_include_taxes TINYINT(1) NOT NULL,
    INDEX category_id_index (category_id),
    INDEX product_id_index (product_id),
    PRIMARY KEY(id)
)
DEFAULT CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
SQL
        );

        $this->execute(<<<SQL
CREATE TABLE doctrine_subscription (
    id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)',
    first_order_id INT UNSIGNED NOT NULL,
    company_id INT UNSIGNED NOT NULL,
    credit_card_id CHAR(36) DEFAULT NULL COMMENT '(DC2Type:guid)',
    user_id MEDIUMINT(8) UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)',
    status VARCHAR(15) NOT NULL COMMENT '(DC2Type:string)',
    price BIGINT UNSIGNED NOT NULL COMMENT '(DC2Type:money)',
    is_auto_renew TINYINT(1) NOT NULL,
    created_at DATETIME NOT NULL COMMENT '(DC2Type:datetimetz_immutable)',
    commitment_period INT UNSIGNED NOT NULL,
    payment_frequency INT UNSIGNED NOT NULL,
    next_payment_at DATETIME NOT NULL COMMENT '(DC2Type:datetimetz_immutable)',
    commitment_end_at DATETIME NOT NULL COMMENT '(DC2Type:datetimetz_immutable)',
    INDEX first_order_id_company_id_index (first_order_id, company_id),
    INDEX IDX_6C3D57397048FD0F (credit_card_id),
    INDEX IDX_6C3D5739A76ED395 (user_id),
    PRIMARY KEY(id)
)
DEFAULT CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
SQL
        );

        $this->execute(<<<SQL
CREATE TABLE doctrine_subscriptions_has_order_items (
    subscription_id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)',
    order_items_id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)',
    INDEX IDX_9FA407D69A1887DC (subscription_id),
    INDEX IDX_9FA407D68A484C35 (order_items_id),
    PRIMARY KEY(subscription_id, order_items_id)
)
DEFAULT CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
SQL
        );

        $this->execute(<<<SQL
CREATE TABLE doctrine_tax_item (
    id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)',
    order_item_id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)',
    tax_id MEDIUMINT(8) UNSIGNED NOT NULL,
    rate DOUBLE PRECISION NOT NULL,
    name VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)',
    amount BIGINT UNSIGNED NOT NULL COMMENT '(DC2Type:money)',
    INDEX tax_id_index (tax_id),
    INDEX IDX_4F4BEE71E415FB15 (order_item_id),
    PRIMARY KEY(id)
)
DEFAULT CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
SQL
        );

        $this->execute("ALTER TABLE doctrine_subscription
            ADD CONSTRAINT FK_6C3D57397048FD0F FOREIGN KEY (credit_card_id) REFERENCES doctrine_credit_card (id),
            ADD CONSTRAINT FK_6C3D5739A76ED395 FOREIGN KEY (user_id) REFERENCES cscart_users (user_id);
        ");
        $this->execute("ALTER TABLE doctrine_subscriptions_has_order_items
            ADD CONSTRAINT FK_9FA407D69A1887DC39875F63979B1AD6 FOREIGN KEY (subscription_id) REFERENCES doctrine_subscription (id),
            ADD CONSTRAINT FK_9FA407D68A484C35 FOREIGN KEY (order_items_id) REFERENCES doctrine_order_item (id);
        ");
        $this->execute("ALTER TABLE doctrine_tax_item ADD CONSTRAINT FK_3C43C54F8A484C35 FOREIGN KEY (order_item_id) REFERENCES doctrine_order_item (id);");
    }

    public function down(): void
    {
        $this->execute("DROP TABLE IF EXISTS doctrine_tax_item;");
        $this->execute("DROP TABLE IF EXISTS doctrine_subscriptions_has_order_items;");
        $this->execute("DROP TABLE IF EXISTS doctrine_subscription;");
        $this->execute("DROP TABLE IF EXISTS doctrine_order_item;");
        $this->execute("ALTER TABLE doctrine_credit_card DROP FOREIGN KEY FK_507054D8A76ED395;");
        $this->execute("DROP INDEX user_id_index ON doctrine_credit_card;");
        $this->execute("ALTER TABLE doctrine_credit_card DROP PRIMARY KEY");
        $this->execute("ALTER TABLE doctrine_credit_card ADD PRIMARY KEY (id, user_id);");
    }
}
