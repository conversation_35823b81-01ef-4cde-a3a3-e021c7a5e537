<?php

use Phinx\Migration\AbstractMigration;

class DeleteAllDuplicatedImagesPairedToFeatureVariant extends AbstractMigration
{
    public function up(): void
    {
        $featureVariant = $this->fetchAll(
            'SELECT variant_id 
            FROM cscart_product_feature_variants'
        );

        foreach ($featureVariant as $variant) {
            $maxImage = $this->fetchRow(
                "SELECT MAX(cscart_images_links.image_id) as image_id
                FROM cscart_images_links
                WHERE cscart_images_links.object_id = " . $variant['variant_id'] . "
                    AND cscart_images_links.object_type = 'feature_variant'
                    AND cscart_images_links.type = 'V'"
            );

            if ($maxImage['image_id'] > 0) {
                $this->query(
                    "DELETE cscart_images_links
                    FROM cscart_images_links
                    WHERE cscart_images_links.object_type = 'feature_variant'
                        AND cscart_images_links.object_id = " . $variant['variant_id'] . " 
                        AND cscart_images_links.image_id != " . $maxImage['image_id']
                );
            }
        }
    }

    // Empty because we delete data in images_links table and we do not update its structure
    public function down(): void
    {
    }
}
