<?php

use Phinx\Migration\AbstractMigration;

class AddTranslationForProfileFields extends AbstractMigration
{
    public function up()
    {
        $translations = [
            1 => "Mister",
            2 => "Madam",
            3 => "Madam",
            4 => "Mister",
            6 => "Firstname",
            7 => "Lastname",
            8 => "Company name",
            9 => "Phone",
            10 => "Fax",
            11 => "Web site",
            14 => "Firstname",
            15 => "Firstname",
            16 => "Lastname",
            17 => "Lastname",
            18 => "Address",
            19 => "Address",
            20 => "Additional address",
            21 => "Additional address",
            22 => "City",
            23 => "City",
            24 => "State/province",
            25 => "State/province",
            26 => "Country",
            27 => "Country",
            28 => "Zip code",
            29 => "Zip code",
            30 => "Phone",
            31 => "Phone",
            32 => "E-mail",
            33 => "E-mail",
            35 => "Address name",
            36 => "Civility",
            37 => "Civility",
            38 => "Civility",
            39 => "Company name",
            40 => "Company name",
            42 => "Division",
        ];

        foreach ($translations as $id => $translation) {
            $this->execute("UPDATE cscart_profile_field_descriptions SET description = '{$translation}' WHERE object_id = {$id} AND lang_code = 'en'");
        }

        $this->execute("
        INSERT IGNORE INTO cscart_profile_field_descriptions (object_id, description, object_type, lang_code) VALUES (41, 'Birthday', 'F', 'en');
        INSERT IGNORE INTO cscart_profile_field_descriptions (object_id, description, object_type, lang_code) VALUES (41, 'Anniversaire', 'F', 'fr');
        
        UPDATE cscart_profile_field_descriptions SET description = 'Birthday' WHERE object_id = 41 AND lang_code = 'en';
        UPDATE cscart_profile_field_descriptions SET description = 'Anniversaire' WHERE object_id = 41 AND lang_code = 'en';
        ");
    }
}
