<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class EditDispatchFundsTransferVendorAndCommissionLabel extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('UPDATE `cscart_language_values` SET value = "Vendor transfer" WHERE lang_code = "en" AND name = "transaction_dispatch_funds_transfer_vendor"');
        $this->execute('UPDATE `cscart_language_values` SET value = "Commission transfer" WHERE lang_code = "en" AND name = "transaction_dispatch_funds_transfer_commission"');
    }

    public function down(): void
    {
        $this->execute('UPDATE `cscart_language_values` SET value = "Merchant Transfer" WHERE lang_code = "en" AND name = "transaction_dispatch_funds_transfer_vendor"');
        $this->execute('UPDATE `cscart_language_values` SET value = "Commission Transfer" WHERE lang_code = "en" AND name = "transaction_dispatch_funds_transfer_commission"');
    }
}
