<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateValueOfCompanyForVendor extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("
            UPDATE cscart_users u
            INNER JOIN  cscart_companies c ON u.company_id = c.company_id
            SET u.company = c.company
            WHERE u.user_type like 'V'
            AND u.company = '';
        ");
    }
}
