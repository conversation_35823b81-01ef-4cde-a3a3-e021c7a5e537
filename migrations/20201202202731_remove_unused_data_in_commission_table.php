<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class RemoveUnusedDataInCommissionTable extends AbstractMigration
{
    public function up(): void
    {
        /**
         * Supprimer les commissions liées à une company dont l’id n’est plus présent dans
         * cscart_companies
         */
        $this->execute('
            DELETE
            FROM doctrine_commission
            WHERE company_id NOT IN
            (SELECT company_id FROM cscart_companies) ;
        ');

        /**
         * supprimer l’un des deux commissions “en double“ tel que défini par
         * 1. le même company_id,
         * 2. category_id à NULL
         * 3. le commission_type à 'company'
         * 4. ont exactement le même montant de commission
         */
        $this->execute('
            DELETE dc1 FROM doctrine_commission dc1
            INNER JOIN doctrine_commission dc2
            WHERE
            dc1.category_id IS NULL
            AND dc1.commission_type =\'company\'

            AND dc1.company_id = dc2.company_id
            AND dc1.commission_type = dc2.commission_type
            -- category_id can be null
            AND dc1.category_id <=> dc2.category_id
            -- remove commissions that have exactly the same commission amount
            AND dc1.percent_amount = dc2.percent_amount
            AND dc1.fix_amount = dc2.fix_amount
            -- maximum_amount can be null
            AND (dc1.maximum_amount <=> dc2.maximum_amount
                OR (dc1.maximum_amount IS NULL AND dc2.maximum_amount = 0)
                OR (dc2.maximum_amount IS NULL AND dc1.maximum_amount = 0)
            )
            -- condition to delete 1 row
            AND dc1.id < dc2.id    ;
        ');
    }
}
