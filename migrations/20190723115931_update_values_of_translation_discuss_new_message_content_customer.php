<?php

use Phinx\Migration\AbstractMigration;

class UpdateValuesOfTranslationDiscussNewMessageContentCustomer extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<'SQL'
        UPDATE `cscart_language_values`
        SET value = '\n            <p>You have received a new message on our marketplace</a></p>\n            <p> You can view it by visiting your profile</p>\n\n        '
        WHERE lang_code = 'en' 
        AND name = 'wizacha_discuss_new_message_content_customer';

        UPDATE `cscart_language_values`
        SET value = '\n            <p>Vous avez reçu un nouveau message sur notre marketplace</a></p>\n            <p>Vous pouvez le consulter en vous rendant dans votre espace personnel</p>\n\n        '
        WHERE lang_code = 'fr' 
        AND name = 'wizacha_discuss_new_message_content_customer';
SQL
        );
    }
}
