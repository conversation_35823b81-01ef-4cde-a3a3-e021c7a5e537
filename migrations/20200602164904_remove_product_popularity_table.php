<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class RemoveProductPopularityTable extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("DROP TABLE IF EXISTS doctrine_product_popularity;");
    }

    public function down(): void
    {
        $this->execute(<<<SQL
        CREATE TABLE IF NOT EXISTS doctrine_product_popularity (
            product_id VARCHAR(255) NOT NULL,
            viewed INT NOT NULL,
            basket_added INT NOT NULL,
            basket_removed INT NOT NULL,
            ordered INT NOT NULL,
            PRIMARY KEY(product_id)
        )
        DEFAULT CHARACTER SET utf8mb4
        COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
SQL
        );
    }
}
