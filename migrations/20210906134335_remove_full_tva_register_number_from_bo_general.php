<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class RemoveFullTvaRegisterNumberFromBoGeneral extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("
            UPDATE cscart_settings_objects
            SET section_id = 0
            WHERE name = 'w_full_tva_register_number'
        ");
    }

    public function down(): void
    {
        $this->execute("
            UPDATE cscart_settings_objects
            SET section_id = 2
            WHERE name = 'w_full_tva_register_number'
        ");
    }
}
