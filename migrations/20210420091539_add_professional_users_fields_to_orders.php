<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddProfessionalUsersFieldsToOrders extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE cscart_orders ADD is_customer_professional TINYINT(2) DEFAULT 0 NOT NULL AFTER s_address_type;");
        $this->execute("ALTER TABLE cscart_orders ADD customer_company VARCHAR(255) DEFAULT '' NOT NULL AFTER is_customer_professional;");
        $this->execute("ALTER TABLE cscart_orders ADD customer_legal_identifier VARCHAR(255) DEFAULT '' NOT NULL AFTER customer_company;");
        $this->execute("ALTER TABLE cscart_orders ADD customer_intra_european_community_vat VARCHAR(255) DEFAULT '' NOT NULL AFTER customer_legal_identifier;");
        $this->execute("ALTER TABLE cscart_orders ADD customer_job_title VARCHAR(255) DEFAULT '' NOT NULL AFTER customer_intra_european_community_vat;");
        $this->execute("ALTER TABLE cscart_orders ADD customer_account_comment TEXT DEFAULT '' NOT NULL AFTER customer_job_title;");
        $this->execute("ALTER TABLE cscart_orders ADD customer_external_identifier VARCHAR(255) DEFAULT '' NOT NULL AFTER customer_account_comment;");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE cscart_orders DROP COLUMN is_customer_professional;");
        $this->execute("ALTER TABLE cscart_orders DROP COLUMN customer_company;");
        $this->execute("ALTER TABLE cscart_orders DROP COLUMN customer_legal_identifier;");
        $this->execute("ALTER TABLE cscart_orders DROP COLUMN customer_intra_european_community_vat;");
        $this->execute("ALTER TABLE cscart_orders DROP COLUMN customer_job_title;");
        $this->execute("ALTER TABLE cscart_orders DROP COLUMN customer_account_comment;");
        $this->execute("ALTER TABLE cscart_orders DROP COLUMN customer_external_identifier;");
    }
}
