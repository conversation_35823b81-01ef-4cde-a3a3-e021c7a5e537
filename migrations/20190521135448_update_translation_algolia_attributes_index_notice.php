<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateTranslationAlgoliaAttributesIndexNotice extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("
            UPDATE cscart_language_values
            SET value = 'Vous ne pouvez pas utiliser plus de 50 attributs dans la recherche (case cochable \"%feature_is_searchable%\") (%total_features% activés).'
            WHERE name = 'too_many_attribute_index_notice'
              AND lang_code = 'fr'");

        $this->execute("
            UPDATE cscart_language_values
            SET value = 'You can not use more than 50 attributes in the search (see checkbox \"%feature_is_searchable%\") (%total_features% enabled).'
            WHERE name = 'too_many_attribute_index_notice'
              AND lang_code = 'en'");
    }
}
