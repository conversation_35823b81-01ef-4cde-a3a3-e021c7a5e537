<?php

use Phinx\Migration\AbstractMigration;

class FixIncorrectShipmentDeliveryDateUpdate extends AbstractMigration
{
    public function up(): void
    {
        $statement = $this->getPdo()->prepare(
            'UPDATE
                cscart_shipments cs
                JOIN cscart_shipment_items csi
                    USING(shipment_id)
                JOIN cscart_orders co
                    USING(order_id)
            SET
                cs.delivery_date = co.delivery_date
            WHERE
                co.delivered = 1
                AND co.timestamp <= :timestamp
            '
        );
        $statement->execute(
            ['timestamp' => $this->getAccidentTimestamp()]
        );
    }

    public function down(): void
    {
        $statement = $this->getPdo()->prepare(
            'UPDATE
                cscart_shipments cs
                INNER JOIN cscart_orders co
                    ON cs.shipping_id = co.shipping_ids
            SET
                cs.delivery_date = co.delivery_date
            WHERE
                co.delivered = 1
                AND co.timestamp <= :timestamp
            '
        );

        $statement->execute(
            ['timestamp' => $this->getAccidentTimestamp()]
        );
    }

    /** trying to not update legit cscart_shipments.delivery_date */
    public function getAccidentTimestamp(): int
    {
        $accidentDate = new DateTime(
            $this->getPdo()->query(
                'SELECT
                    end_time
                FROM
                    migration
                WHERE
                    version = "20200925092744"
                '
            )->fetch()['end_time']
        );

        return $accidentDate->getTimestamp();
    }

    /** the good old vanilla PDO */
    public function getPdo(): \PDO
    {
        return $this->getAdapter()->getConnection();
    }
}
