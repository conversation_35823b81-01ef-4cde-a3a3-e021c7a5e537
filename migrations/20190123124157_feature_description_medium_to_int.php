<?php

use Phinx\Migration\AbstractMigration;

class FeatureDescriptionMediumToInt extends AbstractMigration
{
    public function up()
    {
        $this->execute('ALTER TABLE cscart_product_features_descriptions modify feature_id INT(11) unsigned DEFAULT 0 NOT NULL;');
        $this->execute('ALTER TABLE cscart_product_feature_variants modify feature_id INT(11) unsigned DEFAULT 0 NOT NULL;');
        $this->execute('ALTER TABLE cscart_product_features_values modify feature_id INT(11) unsigned DEFAULT 0 NOT NULL;');
        $this->execute('ALTER TABLE cscart_product_filters modify feature_id INT(11) unsigned DEFAULT 0 NOT NULL;');
        $this->execute('ALTER TABLE cscart_product_filter_ranges modify feature_id INT(11) unsigned DEFAULT 0 NOT NULL;');
    }
}
