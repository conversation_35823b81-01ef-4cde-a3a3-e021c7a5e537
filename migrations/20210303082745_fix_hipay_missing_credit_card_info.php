<?php

use Phinx\Migration\AbstractMigration;

class FixHiPayMissingCreditCardInfo extends AbstractMigration
{
    private const TABLE_CREDIT_CARD = 'doctrine_credit_card';
    private const KEY_PAYMENT_PRODUCT_CODE = 'payment_product_code';
    private const KEY_ID = 'id';
    private const KEY_BRAND = 'brand';

    private array $brandPaymentProductMapping;

    public function up()
    {
        $this
            ->table(static::TABLE_CREDIT_CARD)
            ->addColumn(
                static::KEY_PAYMENT_PRODUCT_CODE,
                'string',
                ['length' => 255]
            )
            ->save()
        ;

        $updateQuery = \sprintf(
            'UPDATE `%s` SET `%s` = :payment_product_code WHERE `%s` = :id',
            static::TABLE_CREDIT_CARD,
            static::KEY_PAYMENT_PRODUCT_CODE,
            static::KEY_ID
        );
        $updateStatement = $this
            ->getPdo()
            ->prepare($updateQuery)
        ;

        $this->getPdo()->beginTransaction();

        $mapping = $this->getBrandPaymentProductMapping();

        foreach ($this->getCreditCardInfo() as [$id, $brand]) {
            $updateStatement->execute(
                [
                    'payment_product_code' => $mapping[$brand],
                    'id' => $id
                ]
            );
        }

        $this->getPdo()->commit();
    }

    public function down()
    {
        $this
            ->table(static::TABLE_CREDIT_CARD)
            ->removeColumn(static::KEY_PAYMENT_PRODUCT_CODE)
            ->save()
        ;
    }

    private function getCreditCardInfo(): \Generator
    {
        $selectQuery = \sprintf(
            'SELECT `%s`, `%s` FROM `%s`',
            static::KEY_ID,
            static::KEY_BRAND,
            static::TABLE_CREDIT_CARD
        );

        $selectStatment = $this
            ->getPdo()
            ->prepare($selectQuery)
        ;
        $selectStatment->execute();

        while ($row = $selectStatment->fetch()) {
            yield $row;
        }
    }

    private function getBrandPaymentProductMapping()
    {
        return
            $this->brandPaymentProductMapping
            ??= \iterator_to_array($this->loadPaymentProduct())
        ;
    }

    /** @return \Generator<string> */
    private function loadPaymentProduct(): \Generator
    {
        $data = \json_decode(
            \file_get_contents(
                __DIR__ . '/Hipay/payment_product.json'
            ),
            true
        );

        foreach ($data as $row) {
            yield \strtoupper($row['description']) => $row['code'];
        }
    }

    /** the good old vanilla PDO */
    public function getPdo(): \PDO
    {
        return $this->getAdapter()->getConnection();
    }
}
