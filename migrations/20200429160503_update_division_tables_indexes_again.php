<?php

use Phinx\Migration\AbstractMigration;

class UpdateDivisionTablesIndexesAgain extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            '
            CREATE UNIQUE INDEX doctrine_division_blacklists_company_division_code
                ON doctrine_division_blacklists (company_id, division_code);
        '
        );
    }

    public function down(): void
    {
        $this->execute(
            '
            DROP INDEX doctrine_division_blacklists_company_division_code
                ON doctrine_division_blacklists;
        '
        );
    }
}
