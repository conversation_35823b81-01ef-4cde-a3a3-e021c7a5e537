<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddHipaySepaDirectDebitPaymentMethod extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            "INSERT INTO `cscart_payment_processors` (`processor_id`, `processor`, `processor_script`, `admin_template`, `callback`, `type`)
            VALUES (1013, 'HiPay SEPA direct','hipay_sepa_direct.php','hipay_sepa.tpl','N','P'),
            (1014, 'HiPay SEPA échéance','hipay_sepa_deferment.php','hipay_sepa.tpl','N','P');"
        );
    }

    public function down(): void
    {
        $this->execute("DELETE FROM `cscart_payment_processors` WHERE `processor_id` = 1013 OR `processor_id` = 1014");
    }
}
