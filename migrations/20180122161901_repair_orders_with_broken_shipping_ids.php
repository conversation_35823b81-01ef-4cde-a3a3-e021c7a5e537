<?php

use Phinx\Migration\AbstractMigration;

class RepairOrdersWithBrokenShippingIds extends AbstractMigration
{
    public function up()
    {
        $data = $this->fetchAll("SELECT o.order_id, od.data FROM cscart_orders o INNER JOIN cscart_order_data od ON o.order_id = od.order_id AND od.type = 'L' WHERE o.shipping_ids = '0';");

        foreach ($data as $line) {
            $data = @unserialize($line['data']);

            if (\is_array($data)) {
                $shippingIds = array_column($data, 'shipping_id');
                if (!empty($shippingIds)) {
                    $shippingIds = implode(',', $shippingIds);
                    $orderId = $line['order_id'];
                    $this->execute("UPDATE cscart_orders SET shipping_ids = '$shippingIds' WHERE order_id = $orderId");
                }
            }
        }
    }
}
