<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddRefundAfterDrawalPeriodToOrderRefunds extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('ALTER TABLE `doctrine_order_refunds` ADD `refunded_after_withdrawal_period` BOOLEAN NOT NULL DEFAULT 0 AFTER `message`;');
    }

    public function down(): void
    {
        $this->execute('ALTER TABLE `doctrine_order_refunds` DROP `refunded_after_withdrawal_period`');
    }
}
