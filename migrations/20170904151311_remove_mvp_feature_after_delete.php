<?php

use Phinx\Migration\AbstractMigration;

class RemoveMvpFeatureAfterDelete extends AbstractMigration
{
    public function up()
    {
        // Delete features with produt_id in GUID format + non existant in MVP table (cast nécéssaire car les encodages doctrine / cscart different)
        $this->execute('DELETE FROM cscart_product_features_values WHERE LENGTH(product_id) = 36 AND product_id COLLATE utf8mb4_unicode_ci NOT IN (SELECT id FROM doctrine_multi_vendor_product)');
    }
}
