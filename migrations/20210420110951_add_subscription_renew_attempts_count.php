<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddSubscriptionRenewAttemptsCount extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE doctrine_subscription ADD renew_attempts_count INT UNSIGNED NOT NULL DEFAULT 0;");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE doctrine_subscription DROP renew_attempts_count;");
    }
}
