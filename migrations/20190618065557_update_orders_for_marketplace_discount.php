<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateOrdersForMarketplaceDiscount extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("
            ALTER TABLE cscart_orders
            ADD COLUMN `marketplace_discount_id` VARCHAR (36) DEFAULT NULL AFTER subtotal_discount;
        ");

        $this->execute("
            ALTER TABLE cscart_orders
            ADD COLUMN `marketplace_discount_total` DECIMAL (12, 2) DEFAULT NULL AFTER marketplace_discount_id;
        ");

        $this->execute("
            ALTER TABLE cscart_orders
            ADD COLUMN `customer_total` DECIMAL (12, 2) DEFAULT NULL AFTER marketplace_discount_total;
        ");
    }
}
