<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddFreeShippingThresholdFields extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            'ALTER TABLE `cscart_w_company_shipping_rates` ADD `carriage_paid_threshold` DECIMAL(12,2) DEFAULT NULL;'
        );
        $this->execute(
            'ALTER TABLE `cscart_orders` ADD `carriage_paid` TINYINT(1) DEFAULT NULL;'
        );
    }

    public function down(): void
    {
        $this->execute(
            'ALTER TABLE `cscart_w_company_shipping_rates` DROP `carriage_paid_threshold`'
        );
        $this->execute(
            'ALTER TABLE `cscart_orders` DROP `carriage_paid`'
        );
    }
}
