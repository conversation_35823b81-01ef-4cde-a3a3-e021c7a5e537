<?php

use Phinx\Migration\AbstractMigration;

class AvailableOffersConfigFields extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<SQL
INSERT INTO `cscart_settings_objects` (`edition_type`, `name`, `section_id`, `section_tab_id`, `type`, `value`, `position`, `is_global`, `handler`)
VALUES
    (2, 'divisions_included', '0', '0', 'I', '', '0', 'N', NULL),
    (2, 'divisions_excluded', '0', '0', 'I', '', '0', 'N', NULL);

ALTER TABLE `cscart_companies`
ADD `divisions_included` text NULL,
ADD `divisions_excluded` text NULL AFTER `divisions_included`;

ALTER TABLE `cscart_products`
ADD `divisions_included` text NULL,
ADD `divisions_excluded` text NULL AFTER `divisions_included`;

SQL
        );
    }

    public function down()
    {
        $this->execute(<<<SQL
DELETE FROM `cscart_settings_objects` WHERE `name` = 'divisions_included';
DELETE FROM `cscart_settings_objects` WHERE `name` = 'divisions_excluded';

ALTER TABLE `cscart_companies`
DROP `divisions_included`,
DROP `divisions_excluded`;

ALTER TABLE `cscart_products`
DROP `divisions_included`,
DROP `divisions_excluded`;
SQL
        );
    }
}
