<?php

use Phinx\Migration\AbstractMigration;

class UpdateDivisionTablesIndexes extends AbstractMigration
{
    public function up(): void
    {
        $this->execute('
            CREATE INDEX doctrine_divisions_lft
                ON doctrine_divisions (lft);
            
            CREATE INDEX doctrine_divisions_code_enabled
                ON doctrine_divisions (code, is_enabled);
            
            CREATE INDEX doctrine_division_blacklists_company
                ON doctrine_division_blacklists (company_id);
            
            CREATE INDEX doctrine_division_products_product
                ON doctrine_division_products (product_id);
            
            CREATE UNIQUE INDEX doctrine_division_products_product_division
                ON doctrine_division_products (product_id, division_code);
        ');
    }

    public function down(): void
    {
        $this->execute('
            DROP INDEX doctrine_divisions_lft 
                ON doctrine_divisions;
            
            DROP INDEX doctrine_divisions_code_enabled 
                ON doctrine_divisions;
            
            DROP INDEX doctrine_division_blacklists_company 
                ON doctrine_division_blacklists;
            
            DROP INDEX doctrine_division_products_product 
                ON doctrine_division_products;
            
            DROP INDEX doctrine_division_products_product_division 
                ON doctrine_division_products;
        ');
    }
}
