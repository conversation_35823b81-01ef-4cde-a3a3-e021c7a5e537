<?php

/**
 * @copyright Copyright (c) Wizacha
 * @license Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddUserDeclinationDisplayType extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE `cscart_users` ADD COLUMN `declination_display_type` char(5) NOT NULL DEFAULT 'board'");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE `cscart_users` DROP COLUMN `declination_display_type`");
    }
}
