<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class EditCreditNoteTranslationTotalInvoiceValues extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("
            UPDATE cscart_language_values
            SET value= 'Total'
            WHERE name = 'total_cost'
            AND value= 'Coût total'
            AND lang_code = 'fr'
        ");
        $this->execute("
            UPDATE cscart_language_values
            SET value= 'Sur la facture'
            WHERE name = 'invoice_copy'
            AND value= 'Duplicata de facture'
            AND lang_code = 'fr'
        ");
    }

    public function down(): void
    {
        $this->execute("
            UPDATE cscart_language_values
            SET value= 'Coût total'
            WHERE name = 'total_cost'
            AND value= 'Total'
            AND lang_code = 'fr'
        ");
        $this->execute("
            UPDATE cscart_language_values
            SET value= 'Duplicata de facture'
            WHERE name = 'invoice_copy'
            AND value= 'Sur la facture'
            AND lang_code = 'fr'
        ");
    }
}
