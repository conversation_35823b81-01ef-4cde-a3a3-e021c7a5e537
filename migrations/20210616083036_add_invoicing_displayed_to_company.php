<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddInvoicingDisplayedToCompany extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE cscart_companies ADD invoicing_disabled  BOOLEAN DEFAULT FALSE NOT NULL;");
        $this->execute("ALTER TABLE cscart_companies ADD invoicing_disabled_by_admin  BOOLEAN DEFAULT FALSE NOT NULL;");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE cscart_companies DROP invoicing_disabled;");
        $this->execute("ALTER TABLE cscart_companies DROP invoicing_disabled_by_admin;");
    }
}
