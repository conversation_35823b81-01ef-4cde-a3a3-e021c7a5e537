<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class CreateTableMessageAttachments extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<'SQL'
            CREATE TABLE IF NOT EXISTS `doctrine_message_attachments` (
                    `id` VARCHAR(36) NOT NULL COMMENT '(DC2Type:guid)',
                    `message_id` int NOT NULL,
                    `name` VARCHAR(255) NOT NULL,
                    `public_url` VARCHAR(255),
                    PRIMARY KEY (`id`)
                )
                ENGINE = InnoDB
                DEFAULT CHARACTER SET = utf8mb4
                COLLATE = utf8mb4_unicode_ci;
SQL
        );
    }

    public function down(): void
    {
        $this->execute("DROP TABLE IF EXISTS doctrine_message_attachments;");
    }
}
