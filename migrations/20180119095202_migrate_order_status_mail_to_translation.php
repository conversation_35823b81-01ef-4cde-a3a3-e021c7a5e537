<?php

use Phinx\Migration\AbstractMigration;

class MigrateOrderStatusMailToTranslation extends AbstractMigration
{
    public function up()
    {
        foreach ($this->fetchAll('SELECT * FROM cscart_status_descriptions WHERE type = "O"') as $line) {
            $statusLetter = strtolower($line['status']);

            // Status name
            $this->table('cscart_language_values')->insert([
                'lang_code' => $line['lang_code'],
                'name' => 'order_status_' . $statusLetter,
                'value' => $line['description'],
            ])->save();

            // Customer email subject
            $this->table('cscart_language_values')->insert([
                'lang_code' => $line['lang_code'],
                'name' => 'order_email_subject_' . $statusLetter,
                'value' => $line['email_subj'],
            ])->save();

            // Customer email header
            $this->table('cscart_language_values')->insert([
                'lang_code' => $line['lang_code'],
                'name' => 'order_email_header_' . $statusLetter,
                'value' => $line['email_header'],
            ])->save();

            // Vendor email subject
            $this->table('cscart_language_values')->insert([
                'lang_code' => $line['lang_code'],
                'name' => 'order_vendor_email_subject_' . $statusLetter,
                'value' => $line['vendor_email_subj'],
            ])->save();

            // Vendor email header
            $this->table('cscart_language_values')->insert([
                'lang_code' => $line['lang_code'],
                'name' => 'order_vendor_email_header_' . $statusLetter,
                'value' => $line['vendor_email_header'],
            ])->save();
        }
    }
}
