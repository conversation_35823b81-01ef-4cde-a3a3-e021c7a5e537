<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddSubscriptionProductOptions extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE `cscart_product_options` ADD COLUMN `code` VARCHAR(255) DEFAULT NULL");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE `cscart_product_options` DROP COLUMN `code`");
    }
}
