<?php

use Phinx\Migration\AbstractMigration;

class UpdateOrganisationFieldsConstraints extends AbstractMigration
{
    public function up()
    {
        $this->execute("ALTER TABLE doctrine_organisation CHANGE legal_information_siret legal_information_siret VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)';");
        $this->execute("ALTER TABLE doctrine_organisation CHANGE legal_information_vat_number legal_information_vat_number VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)';");
        $this->execute("ALTER TABLE doctrine_organisation CHANGE legal_information_business_name legal_information_business_name VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)';");
        $this->execute("ALTER TABLE doctrine_organisation CHANGE business_unit_code business_unit_code VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)';");
        $this->execute("ALTER TABLE doctrine_organisation CHANGE business_unit_name business_unit_name VA<PERSON>HAR(255) NOT NULL COMMENT '(DC2Type:string)';");
    }
}
