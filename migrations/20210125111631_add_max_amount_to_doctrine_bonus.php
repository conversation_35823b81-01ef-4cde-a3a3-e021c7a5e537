<?php

/**
 * @copyright Copyright (c) Wizacha
 * @license Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddMaxAmountToDoctrineBonus extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE `doctrine_bonus` ADD COLUMN `max_amount` BIGINT UNSIGNED DEFAULT NULL COMMENT '(DC2Type:money)'");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE `doctrine_bonus` DROP COLUMN `max_amount`");
    }
}
