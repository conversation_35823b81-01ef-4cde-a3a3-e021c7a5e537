<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class CreateTableDoctrineUboMangopay extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("CREATE TABLE IF NOT EXISTS doctrine_ubo_mangopay(
                id INT(11) UNSIGNED AUTO_INCREMENT,
                submitted_date DATETIME NULL,
                declaration_id varchar(255) NULL,
                ubo_status varchar(128) NULL,
                person INT(11) UNSIGNED NULL,
                PRIMARY KEY (id),
                CONSTRAINT doctrine_person_ubo_mangopay_FK FOREIGN KEY (person) REFERENCES doctrine_company_person (id)
            ) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4");
    }

    public function down(): void
    {
        $this->execute("DROP TABLE IF EXISTS doctrine_ubo_mangopay");
    }
}
