<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddAddressBookToEximLayouts extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<SQL
INSERT INTO cscart_exim_layouts VALUES (null, 'general', 'E-mail', 'user_addresses_book', 'Y');
SQL
        );
    }

    public function down(): void
    {
        $this->execute(<<<SQL
DELETE FROM cscart_exim_layouts WHERE `pattern_id` = 'user_addresses_book';
SQL
        );
    }
}
