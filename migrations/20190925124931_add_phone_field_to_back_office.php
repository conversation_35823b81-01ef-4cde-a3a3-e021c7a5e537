<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddPhoneFieldToBackOffice extends AbstractMigration
{
    public function up(): void
    {
        $this->query("UPDATE cscart_profile_fields SET profile_show='Y' WHERE field_id=9;");
    }

    public function down(): void
    {
        $this->query("UPDATE cscart_profile_fields SET profile_show='N' WHERE field_id=9;");
    }
}
