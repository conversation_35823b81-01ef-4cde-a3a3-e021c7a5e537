<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class CreateTableDoctrineCompanyPersonHasNationalities extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            "CREATE TABLE IF NOT EXISTS doctrine_company_person_has_nationalities (
                    company_person_id INT(11) UNSIGNED NOT NULL,
                    country_code CHAR(2) NOT NULL,
                    PRIMARY KEY(company_person_id, country_code)
                ) ENGINE = InnoDB DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
                ALTER TABLE doctrine_company_person_has_nationalities ADD CONSTRAINT doctrine_company_person_id_FK FOREIGN KEY (company_person_id) REFERENCES doctrine_company_person (id) ON DELETE CASCADE;
                ALTER TABLE doctrine_company_person_has_nationalities ADD CONSTRAINT cscart_countries_code_FK FOREIGN KEY (country_code) REFERENCES cscart_countries (code);"
        );
    }

    public function down(): void
    {
        $this->execute("DROP TABLE IF EXISTS doctrine_company_person_has_nationalities");
    }
}
