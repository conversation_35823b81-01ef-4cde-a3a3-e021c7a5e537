<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class InconsisntPopUpMessageAfterPasswordReset extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'You are now logged into your account. Please change your password directly in the account information. Remember to save the changes.'
            WHERE
                `lang_code` = 'en'
                AND name = 'text_change_password';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'Vous êtes maintenant connecté à votre compte. Veuillez modifier votre mot de passe directement dans les informations du compte. N’oubliez pas de sauvegarder les changements.'
            WHERE
                `lang_code` = 'fr'
                AND name = 'text_change_password';
            "
        );
    }

    public function down(): void
    {
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'You are now logged in to your account. Please change the password and click the \"Save\" button.'
            WHERE
                `lang_code` = 'en'
                AND name = 'text_change_password';
            "
        );
        $this->execute(
            "UPDATE
                `cscart_language_values`
            SET
                `value` = 'Vous êtes maintenant connecté à votre compte. Veuillez changer votre mot de passe en cliquant sur le bouton \"Modifier le mot de passe\".'
            WHERE
                `lang_code` = 'fr'
                AND name = 'text_change_password';
            "
        );
    }
}
