<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateTranslationTabPayment extends AbstractMigration
{
    public function up(): void
    {
        //Translation Fr
        $this->execute('UPDATE `cscart_language_values` SET value = "Transactions" WHERE lang_code = "fr" AND name = "payments"');
    }

    public function down(): void
    {
        //Translation Fr
        $this->execute('UPDATE `cscart_language_values` SET value = "Paiements" WHERE lang_code = "fr" AND name = "payments"');
    }
}
