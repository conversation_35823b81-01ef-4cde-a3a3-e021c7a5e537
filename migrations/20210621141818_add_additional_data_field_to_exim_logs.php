<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddAdditionalDataFieldToEximLogs extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE doctrine_exim_job_logs ADD additional_data LONGTEXT DEFAULT NULL COMMENT '(DC2Type:json_array)'");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE doctrine_exim_job_logs DROP COLUMN additional_data;");
    }
}
