<?php

use Phinx\Migration\AbstractMigration;

class AddOrganisationAdministrator extends AbstractMigration
{
    public function up()
    {
        $this->execute("CREATE TABLE doctrine_organisation_administrator (id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)', user_id MEDIUMINT(8) UNSIGNED DEFAULT NULL, occupation VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)', identity_card VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)', proof_of_appointment VARCHAR(255) NOT NULL COMMENT '(DC2Type:string)', UNIQUE INDEX UNIQ_DD77B528A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;");
        $this->execute("ALTER TABLE doctrine_organisation_administrator ADD CONSTRAINT FK_DD77B528A76ED395 FOREIGN KEY (user_id) REFERENCES cscart_users (user_id);");
        $this->execute("ALTER TABLE doctrine_organisation_administrator ADD organisation_id CHAR(36) NOT NULL COMMENT '(DC2Type:guid)';");
        $this->execute("ALTER TABLE doctrine_organisation_administrator ADD CONSTRAINT FK_DD77B5289E6B1585 FOREIGN KEY (organisation_id) REFERENCES doctrine_organisation (id);");
        $this->execute("CREATE INDEX IDX_DD77B5289E6B1585 ON doctrine_organisation_administrator (organisation_id);");
    }
}
