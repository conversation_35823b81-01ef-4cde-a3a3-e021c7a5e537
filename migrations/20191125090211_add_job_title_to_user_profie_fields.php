<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddJobTitleToUserProfieFields extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<SQL
INSERT INTO cscart_profile_fields VALUES (47, 'job_title', 'Y', 'N', 'N', 'N', 'N', 'N', 'I', 30, 'N', 'F', 0, '', '', 'N', 'N', 'N', 'N', 'N');

INSERT INTO cscart_profile_field_descriptions VALUES (47, 'Fonction/rôle', 'F', 'fr');
INSERT INTO cscart_profile_field_descriptions VALUES (47, 'Job title', 'F', 'en');

ALTER TABLE cscart_user_profiles ADD job_title VARCHAR(255) DEFAULT '' NOT NULL;
SQL
        );
    }

    public function down()
    {
        $this->execute(<<<SQL
DELETE FROM cscart_profile_fields WHERE `field_id` = 47;

DELETE FROM cscart_profile_field_descriptions WHERE `object_id` = 47;

ALTER TABLE cscart_user_profiles DROP COLUMN job_title;
SQL
        );
    }
}
