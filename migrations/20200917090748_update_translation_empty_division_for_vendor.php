<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateTranslationEmptyDivisionForVendor extends AbstractMigration
{
    public function up(): void
    {
        //Translation Fr
        $this->execute('UPDATE `cscart_language_values` SET value = "Ce marchand n’a aucune division active" WHERE lang_code = "fr" AND name = "no_division_for_vendor"');
    }

    public function down(): void
    {
        //Translation EN
        $this->execute('UPDATE `cscart_language_values` SET value = "This company has no active division" WHERE lang_code = "fr" AND name = "no_division_for_vendor"');
    }
}
