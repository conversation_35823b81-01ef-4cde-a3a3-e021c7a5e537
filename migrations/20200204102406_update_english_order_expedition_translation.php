<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateEnglishOrderExpeditionTranslation extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("
            UPDATE cscart_language_values
            SET value= 'Shipment of your order'
            WHERE name = 'order_email_subject_e'
            AND value = ''
            AND lang_code = 'en'
        ");
    }

    public function down(): void
    {
        $this->execute("
            UPDATE cscart_language_values
            SET value= ''
            WHERE name = 'order_email_subject_e'
            AND value = 'Shipment of your order'
            AND lang_code = 'en'
        ");
    }
}
