<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddBillingNumberAndCreditNoteNumberToCscartCompanies extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE cscart_companies ADD prefix_billing_number VARCHAR(255) NOT NULL DEFAULT '';");
        $this->execute("ALTER TABLE cscart_companies ADD prefix_credit_note_number VARCHAR(255) NOT NULL DEFAULT '';");
    }

    public function down(): void
    {
        $this->execute("ALTER TABLE cscart_companies DROP prefix_billing_number;");
        $this->execute("ALTER TABLE cscart_companies DROP prefix_credit_note_number;");
    }
}
