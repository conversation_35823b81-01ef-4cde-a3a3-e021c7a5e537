<?php

use Phinx\Migration\AbstractMigration;

class UseOneToOneBetweenLinkAndProduct extends AbstractMigration
{
    public function up()
    {
        $this->execute("
            ALTER TABLE doctrine_multi_vendor_product_link
                DROP FOREIGN KEY FK_137DF914584665A,
                DROP INDEX fk_137df914584665a,
                DROP FOREIGN KEY FK_137DF915A3B2CFC,
                DROP PRIMARY KEY;
        ");

        $this->execute("
            ALTER TABLE doctrine_multi_vendor_product_link
                ADD PRIMARY KEY (product_id, multi_vendor_product_id),
                ADD CONSTRAINT FK_137DF914584665A FOREIGN KEY (product_id) REFERENCES cscart_products (product_id),
                ADD CONSTRAINT FK_137DF915A3B2CFC FOREIGN KEY (multi_vendor_product_id) REFERENCES doctrine_multi_vendor_product (id),
                ADD UNIQUE INDEX unique_product_index (product_id),
                ADD INDEX IDX_137DF915A3B2CFC (multi_vendor_product_id);
        ");
    }
}
