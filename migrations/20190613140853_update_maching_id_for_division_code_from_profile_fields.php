<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class UpdateMachingIdForDivisionCodeFromProfileFields extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("UPDATE cscart_profile_fields SET matching_id = 43 WHERE field_id = 42");
        $this->execute("UPDATE cscart_profile_fields SET matching_id = 42 WHERE field_id = 43");
    }
}
