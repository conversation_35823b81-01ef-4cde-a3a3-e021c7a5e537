<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class OrderAcceptationDelayOnPayment extends AbstractMigration
{
    public function up(): void
    {
        $this->execute("ALTER TABLE `cscart_payments` ADD COLUMN `has_acceptation_delay` TINYINT(1) DEFAULT 0 NOT NULL AFTER `a_w_payment_commission`");
        $this->execute("ALTER TABLE `cscart_payments` ADD COLUMN `order_acceptation_delay` INT(11) DEFAULT 0 NOT NULL AFTER `has_acceptation_delay`");
    }
}
