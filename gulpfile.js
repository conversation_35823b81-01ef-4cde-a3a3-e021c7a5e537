'use strict';

// dependencies
const gulp = require('gulp');
const less = require('gulp-less');
const concat = require('gulp-concat');
const sourcemaps = require('gulp-sourcemaps');
const babelify = require('babelify');
const browserify = require('browserify');
const buffer = require('vinyl-buffer');
const source = require('vinyl-source-stream');
require('dotenv').config();

// helpers
const nodeModulePath = "./node_modules";

// config
let themeName = process.env.FRONTEND_THEME;
let theme = themeName.charAt(0).toUpperCase() + themeName.slice(1);

// Front Office tasks
// ==================

// move fonts
gulp.task('front:fonts', () => {
    return gulp.src(`./src/${theme}Bundle/Resources/public/fonts/**/*.*`)
        .pipe(gulp.dest('assets/fonts'));
});

// move images
gulp.task('front:images', () => {
    return gulp.src(`./src/${theme}Bundle/Resources/public/images/**/*.*`)
        .pipe(gulp.dest('assets/images'));
});

// Front Office style
gulp.task('front:style', gulp.series('front:images', 'front:fonts', () => {
    return gulp.src(`./src/${theme}Bundle/Resources/public/css/main.less`)
        .pipe(less('main.css')) // destination filename
        .on('error', function (e) {
            console.error(e);
            this.emit('end');
        })
        .pipe(gulp.dest('assets/css')); // destination folder
}));

// Front Office watchers
gulp.task('front:watch', gulp.series('front:style', () => {
    // watch for style changes
    gulp.watch(`./src/${theme}Bundle/Resources/public/css/**/*.*`)
        .on('change', gulp.series('front:style'));
}));

// Back Office tasks
// =================

// move media (images, fonts)
gulp.task('back:media', gulp.series((done) => {
    return gulp.src('./design/backend/media/**/*.*')
        .pipe(gulp.dest('assets/backoffice/media'));
}));

// Back Office style (move media before)
gulp.task('back:style', gulp.series('back:media', () => {
    return gulp.src('./design/backend/css/backoffice.less')
        .pipe(sourcemaps.init())
        .pipe(less('backoffice.css')) // destination filename
        .on('error', function (e) {
            console.error(e);
            this.emit('end');
        })
        .pipe(sourcemaps.write())
        .pipe(gulp.dest('./assets/backoffice/css')); // destination folder
}));

// Back Office script
gulp.task('back:babelify', gulp.series(() => {
    let bundler = browserify('./js/backend/backoffice.js', {debug: true}).transform(babelify);

    return bundler.bundle()
        .on('error', function (err) {
            console.error(err);
            this.emit('end');
        })
        .pipe(source('backoffice.js')) // destination filename
        .pipe(buffer())
        .pipe(sourcemaps.init({loadMaps: true}))
        .pipe(sourcemaps.write('./'))
        .pipe(gulp.dest('./assets/backoffice/tmp')); // destination folder
}));

// add libraries
gulp.task('back:script:dev', gulp.series('back:babelify', () => {
    return gulp.src([
            `${nodeModulePath}/vue/dist/vue.js`,
            `./assets/backoffice/tmp/backoffice.js`,
        ])
        .pipe(sourcemaps.init())
        .pipe(concat('backoffice.js'))
        .pipe(sourcemaps.write('./'))
        .pipe(gulp.dest('./assets/backoffice/js'));
}));

gulp.task('back:script:prod', gulp.series('back:babelify', () => {
    return gulp.src(`${nodeModulePath}/vue/dist/vue.min.js`)
    .pipe(sourcemaps.init())
    .pipe(concat('backoffice.js'))
    .pipe(sourcemaps.write('./'))
    .pipe(gulp.dest('./assets/backoffice/js'));
}));

// Back Office watchers
gulp.task('back:watch', gulp.series(() => {

    // watch for style changes
    gulp.watch('./design/**/*.less')
        .on('change', gulp.series('back:style'));

    // watch for script changes
    gulp.watch('./js/backend/backoffice.js')
        .on('change', gulp.series('back:script:dev'));
}));


// Front Office tasks
gulp.task('front', gulp.series('front:style', 'front:watch'));

// Back Office tasks
gulp.task('back', gulp.series('back:style', 'back:script:dev', 'back:watch'));

// Run default task (both Front and Back Office tasks)
gulp.task('default', gulp.series('front', 'back'));
