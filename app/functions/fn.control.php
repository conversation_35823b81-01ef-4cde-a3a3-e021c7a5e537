<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya M<PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Tygh\Navigation\LastView;
use Tygh\Registry;
use Tygh\Session;
use Wizacha\AppBundle\Backend\Exception\RunControllerException;
use Wizacha\Cscart\CsCartEvents;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;

define('GET_CONTROLLERS', 1);
define('GET_PRE_CONTROLLERS', 2);
define('GET_POST_CONTROLLERS', 3);

/**
 * Load addon
 *
 * @param string $addon_name addon name
 * @return boolean true if addon loaded, false otherwise
 */
function fn_load_addon($addon_name)
{
    static $cache = array();

    if (!isset($cache[$addon_name])) {
        $_addon = Registry::get("addons.$addon_name");
        if ($_addon === null || Registry::get("addons.$addon_name.status") === 'D') {
            $cache[$addon_name] = false;

            return false;
        }

        $cache[$addon_name] = true;
    }

    return $cache[$addon_name];
}

/**
 * Front controller.
 *
 * Dispatches the request to controllers.
 */
function fn_dispatch(Request $request, $controller = '', $mode = '', $action = '', $dispatch_extra = '', $area = AREA): Response
{
    // Je pense que ça peut rester là vu que c'est utilisé uniquement dans CsCart
    $exim_dir = Registry::get('config.dir.exim') . Registry::get('runtime.company_id').'/';
    Registry::set('config.dir.exim', $exim_dir);

    $controller = empty($controller) ? Registry::get('runtime.controller') : $controller;
    $mode = empty($mode) ? Registry::get('runtime.mode') : $mode;
    $action = empty($action) ? Registry::get('runtime.action') : $action;
    $dispatch_extra = empty($dispatch_extra) ? Registry::get('runtime.dispatch_extra') : $dispatch_extra;

    $regexp = "/^[a-zA-Z0-9_\+]+$/";
    if (!preg_match($regexp, $controller) || !preg_match($regexp, $mode)) {
        throw new AccessDeniedHttpException('Access denied');
    }

    $run_controllers = true;
    $external = false;
    $status = CONTROLLER_STATUS_NO_PAGE;

    LastView::instance()->prepare($_REQUEST);

    $controllers_cascade = array();
    $controllers_list = array('init');
    if ($run_controllers == true) {
        $controllers_list[] = $controller;
        $controllers_list = array_unique($controllers_list);
    }
    $controllers_list[] = 'exit';
    foreach ($controllers_list as $ctrl) {
        $core_controllers = fn_init_core_controllers($ctrl);
        list($addon_controllers) = fn_init_addon_controllers($ctrl);

        if (empty($core_controllers) && empty($addon_controllers)) {
            //$controllers_cascade = array(); // FIXME: controllers_cascade contains INIT. We should not clear initiation code.
            $status = CONTROLLER_STATUS_NO_PAGE;
            $run_controllers = false;
            break;
        }

        $core_pre_controllers = fn_init_core_controllers($ctrl, GET_PRE_CONTROLLERS);
        $core_post_controllers = fn_init_core_controllers($ctrl, GET_POST_CONTROLLERS);

        list($addon_pre_controllers) = fn_init_addon_controllers($ctrl, GET_PRE_CONTROLLERS);
        list($addon_post_controllers, $addons) = fn_init_addon_controllers($ctrl, GET_POST_CONTROLLERS);

        // we put addon post-controller to the top of post-controller cascade if current addon serves this request
        if (count($addon_controllers)) {
            $addon_post_controllers = fn_reorder_post_controllers($addon_post_controllers, $addon_controllers[0]);
        }

        $controllers_cascade = array_merge($controllers_cascade, $addon_pre_controllers, $core_pre_controllers, $core_controllers, $addon_controllers, $core_post_controllers, $addon_post_controllers);

        if (empty($controllers_cascade)) {
            throw new \InvalidArgumentException("No controllers for: $ctrl");
        }
    }

    if ($mode == 'add') {
        $tpl = 'update.tpl';
    } elseif (strpos($mode, 'add_') === 0) {
        $tpl = str_replace('add_', 'update_', $mode) . '.tpl';
    } else {
        $tpl = $mode . '.tpl';
    }

    // Always choose 'layout.tpl' for 'bundle' controller
    if ($controller == 'bundle') {
        $tpl = 'layout.tpl';
    }

    $view = Registry::get('view');
    if ($view->templateExists('views/' . $controller . '/' . $tpl)) { // try to find template in base views
        $view->assign('content_tpl', 'views/' . $controller . '/' . $tpl);
    } elseif (defined('LOADED_ADDON_PATH') && $view->templateExists('addons/' . LOADED_ADDON_PATH . '/views/' . $controller . '/' . $tpl)) { // try to find template in addon views
        $view->assign('content_tpl', 'addons/' . LOADED_ADDON_PATH . '/views/' . $controller . '/' . $tpl);
    } elseif (!empty($addons)) { // try to find template in addon views that extend base views
        foreach ($addons as $addon => $_v) {
            if ($view->templateExists('addons/' . $addon . '/views/' . $controller . '/' . $tpl)) {
                $view->assign('content_tpl', 'addons/' . $addon . '/views/' . $controller . '/' . $tpl);
                break;
            }
        }
    }

    foreach ($controllers_cascade as $item) {
        try {
            $_res = fn_run_controller($request, $item, $controller, $mode, $action, $dispatch_extra); // 0 - status, 1 - url, 2 - external, 3 - response content
        } catch (\Throwable $throwable) {
            throw new RunControllerException($throwable->getMessage(), $throwable->getCode(), $throwable);
        }

        // case when fn_redirect is used inside controllers
        if ($_res instanceof Response) {
            return $_res;
        }

        $external = !empty($_res[2]) ? $_res[2] : false;
        $url = !empty($_res[1]) ? $_res[1] : '';

        // Allows returning a response from controllers to bypass CS Cart's template system
        $responseContent = !empty($_res[3]) ? $_res[3] : null;
        if ($responseContent instanceof Response) {
            return $responseContent;
        }

        // Status could be changed only if we allow to run controllers despite of init controller
        if ($run_controllers == true) {
            $status = !empty($_res[0]) ? $_res[0] : CONTROLLER_STATUS_OK;
        }

        if ($status == CONTROLLER_STATUS_OK && !empty($url)) {
            $redirect_url = $url;
        } elseif ($status == CONTROLLER_STATUS_REDIRECT && !empty($url)) {
            $redirect_url = $url;
            break;
        } elseif ($status == CONTROLLER_STATUS_DENIED || $status == CONTROLLER_STATUS_NO_PAGE) {
            break;
        }
    }

    LastView::instance()->init($_REQUEST);

    // In console mode, just stop here
    if (defined('CONSOLE')) {
        exit;
    }

    if (!empty($_SESSION['auth']['this_login']) && Registry::ifGet($_SESSION['auth']['this_login'], 'N') === 'Y') {
        fn_set_notification('E', __('error'), __(ACCOUNT_TYPE . LOGIN_STATUS_USER_DISABLED));
        $status = CONTROLLER_STATUS_DENIED;
    }

    // Redirect if controller returned successful/redirect status only
    if (in_array($status, array(CONTROLLER_STATUS_OK, CONTROLLER_STATUS_REDIRECT)) && !empty($_REQUEST['redirect_url']) && !$external) {
        $redirect_url = $_REQUEST['redirect_url'];
    }

    // If controller returns "Redirect" status, check if redirect url exists
    if ($status == CONTROLLER_STATUS_REDIRECT && empty($redirect_url)) {
        $status = CONTROLLER_STATUS_NO_PAGE;
    }

    // In backend show "changes saved" notification
    // If I understand correctly in admin every POST without error will show "changes saved"
    // That sucks, thanks Obama!
    if ($area == 'A' && $_SERVER['REQUEST_METHOD'] == 'POST' && in_array($status, array(CONTROLLER_STATUS_OK, CONTROLLER_STATUS_REDIRECT))) {
        if (strpos($mode, 'update') !== false && !fn_notification_exists('type', 'E')) {
            fn_set_notification('N', __('notice'), __('text_changes_saved'), 'I', 'changes_saved');
        }
    }

    // Attach params and redirect if needed
    if (in_array($status, array(CONTROLLER_STATUS_OK, CONTROLLER_STATUS_REDIRECT)) && !empty($redirect_url)) {
        $params = array (
            'page',
            'selected_section',
            'active_tab'
        );

        $url_params = array();
        foreach ($params as $param) {
            if (!empty($_REQUEST[$param])) {
                $url_params[$param] = $_REQUEST[$param];
            }
        }

        if (!empty($url_params)) {
            $redirect_url = fn_link_attach($redirect_url, http_build_query($url_params));
        }

        if (!isset($external)) {
            $external = false;
        }

        return fn_redirect($redirect_url, $external);
    }

    if (!$view->getTemplateVars('content_tpl') && $status == CONTROLLER_STATUS_OK) { // FIXME
        $status = CONTROLLER_STATUS_NO_PAGE;
    }

    $statusCode = Response::HTTP_OK;

    if ($status != CONTROLLER_STATUS_OK) {

        if ($status == CONTROLLER_STATUS_NO_PAGE) {
            if ($area === 'C') {
                throw new NotFoundHttpException('Controller "'.$controller.'" returned: Not Found');
            } else if ($area == 'A' && empty($_SESSION['auth']['user_id'])) {
                // If admin is not logged in redirect to login page from not found page
                fn_set_notification('W', __('page_not_found'), __('page_not_found_text'));
                return fn_redirect("auth.login_form");
            }

            $statusCode = Response::HTTP_NOT_FOUND;
        }

        $view->assign('exception_status', $status);
        if ($area == 'A') {
            $view->assign('content_tpl', 'exception.tpl'); // for backend only
        }
        if ($status == CONTROLLER_STATUS_DENIED) {
            $statusCode = Response::HTTP_FORBIDDEN;
            $view->assign('page_title', __('access_denied'));
        } elseif ($status == CONTROLLER_STATUS_NO_PAGE) {
            $view->assign('page_title', __('page_not_found'));
        }
    }

    if ($controller === 'categories' && $mode === 'update' && $area === 'A'){
        $bfoUrl = container()->getParameter('config.bfo_url');
        if (container()->getParameter('feature.enable_bfo')
            && \is_null($bfoUrl) === false
            && $bfoUrl !== '') {
            // if the feature.bfo_url is found, category preview happens in BFO
            $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
            $categoryId = $request->query->get('category_id');
            $slug = fn_seo_get_name('c', $categoryId, '', null, $lang_code);
            $view->assign('bfo_url', $bfoUrl.'/c'.$categoryId.'-'.$slug);
        }
    }

    $content = Registry::get('view')->fetch('index.tpl');

    // Pass current URL to ajax response only if we render whole page
    if (defined('AJAX_REQUEST')) {
        Registry::get('ajax')->assign('current_url', fn_url(Registry::get('config.current_url'), $area, 'current'));

        return Registry::get('ajax')->render($content);
    } elseif ($area == 'C') {
        return fn_symfony_fallback($request, $status, $content);
    }

    return new Response($content, $statusCode);
}

/**
 * Fallback to display legacy controllers in the Symfony application
 *
 * @deprecated needs a refacto
 */
function fn_symfony_fallback(Request $request, $status, $content): Response
{
    $request->attributes->add($_REQUEST);
    $request->attributes->add([
        'content' => $content,
        'scripts' => Registry::get('view')->fetch('common/scripts.tpl'),
        'styles' => Registry::get('view')->fetch('common/styles.tpl'),
        'status' => $status,
    ]);
    $controller = container()->get('fallback_controller');
    $response = $controller->displayAction($request);

    return $response;
}

/**
 * Puts the addon post-controller to the top of post-controllers cascade if current addon serves this request
 *
 * @param array $addon_post_controllers post controllers from addons
 * @param array $current_controller current controllers list
 * @return array controllers list
 */
function fn_reorder_post_controllers($addon_post_controllers, $current_controller)
{
    if (empty($addon_post_controllers) || empty($current_controller)) {
        return $addon_post_controllers;
    }

    // get addon name from the path like /var/www/html/cart/app/addons/[addon]/controllers/backend/[controller].php
    $part = substr($current_controller, strlen(Registry::get('config.dir.addons')));
    // we have [addon]/controllers/backend/[controller].php in the $part
    $addon_name = substr($part, 0, strpos($part, '/'));

    // we search post-controller of the addon that owns active controller of current request
    // and if we find it we put this post-controller to the top of the cascade
    foreach ($addon_post_controllers as $k => $post_controller) {
        if (strpos($post_controller, Registry::get('config.dir.addons') . $post_controller) !== false) {
            // delete in current place..
            unset($addon_post_controllers[$k]);
            // and put at the beginning
            array_unshift($addon_post_controllers, $post_controller);
            break; // only one post controller can be here
        }
    }

    return $addon_post_controllers;
}

/**
 * Runs specified controller by including its file
 *
 * @param string $path path to controller
 * @return array|Response controller return status
 */
function fn_run_controller(Request $request, $path, $controller, $mode, $action, $dispatch_extra)
{
    $stopwatch = container()->getParameter('kernel.debug')
        ? container()->get('debug.stopwatch', ContainerInterface::NULL_ON_INVALID_REFERENCE)
        : null
    ;

    $userService = container()->get(UserService::class);
    $user = $_SESSION['auth']['user_id'] > 0 ? $userService->get($_SESSION['auth']['user_id']) : null;

    if ($mode !== 'logout'
        && \array_key_exists('password_change_timestamp', $_SESSION['auth']) === true
        && $user instanceOf User === true
        && $userService->isUserPasswordExpired($user) === true
    ) {
        $user = $userService->get($_SESSION['auth']['user_id']);
        $userService->recoverPassword($user->getEmail());

        return fn_redirect('auth.logout?passwordExpiryTimeLeft=' . $userService->getPasswordRenewalTimeLimit());
    }

    // On démarre un chrono pour chaque controller exécuté
    if (null !== $stopwatch) {
        $globalStopwatchEvent = $stopwatch->start($path, 'section');
    }

    // on évalue le controller demandé en contrôlant les paramètres exposés
    $return = fn_evaluate_controller($path, [
        'auth' => & $_SESSION['auth'],
        'request' => $request,
        'path' => $path,
        'controller' => $controller,
        'mode' => $mode,
        'action' => $action,
        'dispatch_extra' => $dispatch_extra,
    ]);

    // On arrête le chrono
    if (null !== $stopwatch && $globalStopwatchEvent->isStarted()) {
        $globalStopwatchEvent->stop();
    }

    return $return;
}

/**
 * Safely evaluate controller, require $path if first execution, rename function
 * and eval code either
 *
 * @return array|Response
 */
function fn_evaluate_controller(string $path, array $parameters)
{
    static $check_included = array();

    // expose les paramètres du controller dans le scope courant
    extract($parameters);
    unset($parameters);

    // le controller a déjà été inclus une première fois pendant la requête
    if (isset($check_included[$path])) {
        return eval(fn_rewrite_function_name($path));
    }

    $check_included[$path] = true;

    return require $path;
}

/**
 * Rewrite function name in cscart controllers to allow multiple calls of the
 * same controller, might be usefull when there is a redirect in AJAX context,
 * which is done in the same request
 */
function fn_rewrite_function_name(string $path): string
{
    $code = fn_get_contents($path);

    return str_replace(array('function fn', '<?php', '?>'), array('function '.str_replace('.', '', uniqid('fn', true)), '', ''), $code);
}

/**
 * Generates list of core (pre/post)controllers
 *
 * @param string $controller controller name
 * @param string $type controller type (pre/post)
 * @return array controllers list
 */
function fn_init_core_controllers($controller, $type = GET_CONTROLLERS, $area = AREA)
{
    $controllers = array();

    $prefix = '';
    $area_name = fn_get_area_name($area);

    if ($type == GET_POST_CONTROLLERS) {
        $prefix = '.post';
    } elseif ($type == GET_PRE_CONTROLLERS) {
        $prefix = '.pre';
    }

    // try to find area-specific controller
    if (is_readable(Registry::get('config.dir.root') . '/src/AppBundle/Controller/CsCart/' . $area_name . '/' . $controller . $prefix . '.php')) {
        $controllers[] = Registry::get('config.dir.root') . '/src/AppBundle/Controller/CsCart/' . $area_name . '/' . $controller . $prefix . '.php';
    }

    // try to find common controller
    if (is_readable(Registry::get('config.dir.root') . '/src/AppBundle/Controller/CsCart/common/' . $controller . $prefix . '.php')) {
        $controllers[] = Registry::get('config.dir.root') . '/src/AppBundle/Controller/CsCart/common/' . $controller . $prefix . '.php';
    }

    return $controllers;
}

/**
 * Generates list of (pre/post)controllers from active addons
 *
 * @param string $controller controller name
 * @param string $type controller type (pre/post)
 * @return array controllers list and active addons
 */
function fn_init_addon_controllers($controller, $type = GET_CONTROLLERS, $area = AREA)
{
    $controllers = array();
    static $addons = array();
    $prefix = '';
    $area_name = fn_get_area_name($area);

    if ($type == GET_POST_CONTROLLERS) {
        $prefix = '.post';
    } elseif ($type == GET_PRE_CONTROLLERS) {
        $prefix = '.pre';
    }

    foreach ((array) Registry::get('addons') as $addon_name => $data) {
        if (fn_load_addon($addon_name) == true) {
            // try to find area-specific controller
            $dir = Registry::get('config.dir.addons') . $addon_name . '/controllers/' . $area_name . '/';
            if (is_readable($dir . $controller . $prefix . '.php')) {
                $controllers[] = $dir . $controller . $prefix . '.php';
                $addons[$addon_name] = true;
                if (empty($prefix)) {
                    fn_define('LOADED_ADDON_PATH', $addon_name);
                }
            }

            // try to find common controller
            $dir = Registry::get('config.dir.addons') . $addon_name . '/controllers/common/';
            if (is_readable($dir . $controller . $prefix . '.php')) {
                $controllers[] = $dir . $controller . $prefix . '.php';
                $addons[$addon_name] = true;
                if (empty($prefix)) {
                    fn_define('LOADED_ADDON_PATH', $addon_name);
                }
            }
        }
    }

    return array($controllers, $addons);
}

/**
 * Looks for "dispatch" parameter in REQUEST array and extracts controller, mode, action and extra parameters.
 *
 * @param array $req Request parameters
 * @param string $area Area
 * @return boolean always true
 */
function fn_get_route(&$req, $area = AREA)
{
    $is_allowed_url = fn_check_requested_url();

    fn_seo_get_route($req, $result, $area, $is_allowed_url);
    if (!$is_allowed_url) {
        $req = array(
            'dispatch' => '_no_page'
        );
    }

    if (!empty($req['dispatch'])) {
        $dispatch = is_array($req['dispatch']) ? key($req['dispatch']) : $req['dispatch'];
        $match = true;
    } else {
        $dispatch = 'index.index';
        $match = false;
    }

    rtrim($dispatch, '/.');
    $dispatch = str_replace('/', '.', $dispatch);

    $dispatchParts = explode('.', $dispatch);

    Registry::set('runtime.controller', empty($dispatchParts[0]) ? 'index' : $dispatchParts[0]);
    Registry::set('runtime.mode', empty($dispatchParts[1]) ? 'index' : $dispatchParts[1]);
    Registry::set('runtime.action', $dispatchParts[2] ?? null);
    Registry::set('runtime.dispatch_extra', $dispatchParts[3] ?? null);
    Registry::set('runtime.checkout', false);
    Registry::set('runtime.price_tier_management_flag', container()->getParameter('feature.tier_pricing'));

    $req['dispatch'] = $dispatch;

    return $match;
}

/**
 * Puts data to storage
 * @param string $key key
 * @param string $data data
 * @return integer data ID
 */
function fn_set_storage_data($key, $data = '')
{
    $data_id = 0;
    if (!empty($data)) {
        $data_id = db_query('REPLACE ?:storage_data (`data_key`, `data`) VALUES(?s, ?s)', $key, $data);
        Registry::set('storage_data.' . $key, $data);
    } else {
        db_query('DELETE FROM ?:storage_data WHERE `data_key` = ?s', $key);
        Registry::del('storage_data.' . $key);
    }

    return $data_id;
}

/**
 * Gets data from storage
 * @param string $key key
 * @return mixed key value
 */
function fn_get_storage_data($key)
{
    if (!Registry::isExist('storage_data.' . $key)) {
        Registry::set('storage_data.' . $key, db_get_field('SELECT `data` FROM ?:storage_data WHERE `data_key` = ?s', $key));
    }

    return Registry::get('storage_data.' . $key);
}

/**
 * Checks is some key is expired (value of given key should be timestamp).
 *
 * @param string $key Key name
 * @param int $time_period Time period (in seconds), that should be added to the current timestamp for the future check.
 * @return boolean True, if saved timestamp is less than current timestamp, false otherwise.
 */
function fn_is_expired_storage_data($key, $time_period = null)
{
    $time = fn_get_storage_data($key);
    if ($time < TIME && $time_period) {
        fn_set_storage_data($key, TIME + $time_period);
    }

    return $time < TIME;
}


/**
 * Removes service parameters from URL
 * @param string $url URL
 * @return string clean URL
 */
function fn_url_remove_service_params($url)
{
    $params = array(
        'is_ajax',
        'callback',
        'full_render',
        'result_ids',
        'init_context',
        'skip_result_ids_check'
    );

    array_unshift($params, $url);

    return call_user_func_array('fn_query_remove', $params);
}
