<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, Ilya M<PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Rhumsaa\Uuid\Uuid;
use Tygh\Navigation\LastView;
use Tygh\Registry;
use Wizacha\AppBundle\Security\Constants\SecurityConstants;
use Wizacha\Component\AuthLog\DestinationType;
use Wizacha\Component\AuthLog\SourceType;
use Wizacha\Component\AuthLog\StatusType;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Quotation\QuoteRequestService;
use Wizacha\Marketplace\User\Event\UserPasswordChanged;
use Wizacha\Marketplace\User\Event\UserProfileActivated;
use Wizacha\Marketplace\User\Event\UserProfileActivationRequested;
use Wizacha\Marketplace\User\Event\UserProfileCreated;
use Wizacha\Marketplace\User\Event\UserProfileUpdated;
use Wizacha\Marketplace\User\Event\Yavin\UserCompanyChanged;
use Wizacha\Marketplace\User\Event\Yavin\UserDeactivated;
use Wizacha\Marketplace\User\Event\Yavin\UserTypeChanged;
use Wizacha\Marketplace\User\Event\Yavin\UserCreated;
use Wizacha\Marketplace\User\Event\Yavin\UserActivated;
use Wizacha\Marketplace\User\Event\Yavin\UserUpdated;
use Wizacha\Marketplace\User\Password;
use Wizacha\Marketplace\User\UserPasswordHistoryService;
use Wizacha\Marketplace\User\UserSerializer;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Marketplace\User\UserTitle;
use Wizacha\Status;
use Symfony\Component\HttpFoundation\Request;

/**
 * Gets user info
 *
 * @param string $user_id User identifier
 * @param int $get_profile Gets profile with user or not
 * @param int $profile_id Prodile identifier to get
 * @param bool $bypassCompanyCondition If true, a vendor can get info of any user
 * @return array User data
 */
function fn_get_user_info($user_id, $get_profile = true, &$profile_id = NULL, bool $bypassCompanyCondition = false)
{
    $user_fields = array (
        'user_uuid',
        'user_id',
        'user_type',
        'status',
        'user_login',
        'is_root',
        'company_id',
        'firstname',
        'lastname',
        'birthday',
        'company',
        'email',
        'phone',
        'fax',
        'url',
        'tax_exempt',
        'lang_code',
        'password_change_timestamp',
        'api_key',
        'basket_id',
        'title',
        'organisation_id',
        'currency_code',
        'loyalty_identifier',
        'is_locked',
        'timestamp',
        'extra',
        'api_key_updated_at',
        'declination_display_type',
    );

    $condition = (!$bypassCompanyCondition && $user_id != $_SESSION['auth']['user_id']) ? fn_get_company_condition('?:users.company_id') : '';

    if (trim($condition)) {
        if (Registry::get('runtime.company_id')) {
            $condition = "(user_type = 'V' $condition)";
        }
        $company_customers = db_get_fields("SELECT user_id FROM ?:orders WHERE company_id = ?i", Registry::get('runtime.company_id'));
        if ($company_customers) {
            $condition = db_quote("(user_id IN (?n) OR $condition)", $company_customers);
        }
        $condition = " AND $condition ";
    }

    $user_fields[] = 'birthday';

    $user_fields = implode(',', $user_fields);
    $user_data = db_get_row("SELECT $user_fields FROM ?:users WHERE user_id = ?i $condition", $user_id);

    if (empty($user_data)) {
        return array();
    }

    if (\is_string($user_data['birthday']) === true && $user_data['birthday'] !== "") {
        $user_data['birthday'] = \DateTime::createFromFormat(UserSerializer::BIRTHDAY_FORMAT, $user_data['birthday'])->setTime(0, 0);
    }

    if (\is_null($user_data['user_id']) === false) {
        $userService = container()->get('marketplace.user.user_service');
        $user_data['nationalities'] = $userService->getNationalitiesCode($user_data['user_id']);
    }

    if ($get_profile == true) {
        if (!empty($profile_id)) {
            $profile_data = db_get_row("SELECT * FROM ?:user_profiles WHERE user_id = ?i AND profile_id = ?i", $user_data['user_id'], $profile_id);
        }

        if (empty($profile_data)) {
            $profile_data = db_get_row("SELECT * FROM ?:user_profiles WHERE user_id = ?i AND profile_type = 'P'", $user_data['user_id']);
            $profile_id = $profile_data['profile_id'];
        }

        $user_data = fn_array_merge($user_data, $profile_data);
    }

    // Get additional fields
    $prof_cond = ($get_profile && !empty($profile_data['profile_id'])) ? db_quote("OR (object_id = ?i AND object_type = 'P')", $profile_data['profile_id']) : '';
    $additional_fields = db_get_hash_single_array("SELECT field_id, value FROM ?:profile_fields_data WHERE (object_id = ?i AND object_type = 'U') $prof_cond", array('field_id', 'value'), $user_id);

    $user_data['fields'] = $additional_fields;

    fn_add_user_data_descriptions($user_data);

    // Quote data
    if (true === container()->getParameter('feature.quote_requests')) {
        $user_data['quote_request_selection_ids'] =
            container()->get(QuoteRequestService::class)->getUserSelectionIds($user_id);
    }

    return $user_data;
}

//
// Get user short info
//
function fn_get_user_short_info($user_id)
{
    return db_get_row("SELECT user_id, user_login, company_id, firstname, lastname, email, user_type FROM ?:users WHERE user_id = ?i", $user_id);
}

//
// Get user name
//
function fn_get_user_name($user_id)
{
    if (!empty($user_id)) {
        $user_data = db_get_row("SELECT firstname, lastname FROM ?:users WHERE user_id = ?i", $user_id);
        if (!empty($user_data)) {
            return $user_data['firstname'] . ' ' . $user_data['lastname'];
        }
    }

    return false;
}

/**
 * @param int $user_id
 *
 * @return string|null
 */
function fn_get_user_company_name(int $user_id): ?string
{
    return db_get_field("SELECT company FROM ?:users WHERE user_id = ?i", $user_id);
}

/**
 * Get user data for API
 */
function fn_get_api_user(string $apiKey) : array
{
    return db_get_row('SELECT * FROM ?:users WHERE api_key = ?s', $apiKey);
}

//
// Get all user profiles
//
function fn_get_user_profiles($user_id)
{
    $profiles = array();
    if (!empty($user_id)) {
        $profiles = db_get_array("SELECT profile_id, profile_type, profile_name FROM ?:user_profiles WHERE user_id = ?i", $user_id);
    }

    return $profiles;
}

/** @return mixed[] */
function fn_get_all_user_profiles(int $userId): array
{
    if ($userId > 0) {
        $profiles = db_get_array("SELECT * FROM ?:user_profiles WHERE user_id = ?i", $userId)[0];
    }

    return $profiles ?? [];
}

/**
 * Checks if shipping and billing addresses are different
 *
 * @param array $profile_fields profile fields
 * @return bool true if different, false - otherwise
 */
function fn_check_shipping_billing($user_data, $profile_fields) : bool
{
    if (empty($user_data)) {
        return false;
    }

    if (container()->getParameter('checkout.address.billing_first')) {
        $first = 'S';
        $second = 'B';
    } else {
        $first = 'B';
        $second = 'S';
    }

    if (!empty($profile_fields[$second])) {
        foreach ($profile_fields[$second] as $v) {
            // Workaround for email field
            if ($v['field_name'] == 'email') {
                continue;
            }

            $id = !empty($v['field_name']) ? $v['field_name'] : $v['field_id'];
            $matching_id = !empty($profile_fields[$first][$v['matching_id']]) ? (!empty($v['field_name']) ? ($profile_fields[$first][$v['matching_id']]['field_name']) : $v['matching_id']) : 0;
            $udata = !empty($v['field_name']) ? $user_data : (!empty($user_data['fields']) ? $user_data['fields'] : array());

            // If field is set in shipping section and disabled in billing, so - different
            if ((!empty($udata[$id]) || (empty($udata[$id]) && !empty($v['required']) && $v['required'] == 'Y')) && empty($matching_id)) {
                return true;
            }

            // If field set in both sections and fields are different, so -
            if (isset($udata[$id]) && isset($udata[$matching_id]) && $udata[$id] != $udata[$matching_id]) {
                return true;
            }
        }
    }

    return false;
}

/**
 * Compare fields in shipping and billing sections
 *
 * @param array $profile_fields profile fields
 * @return bool true if billing section contains all fields from shipping section, false - otherwise
 */
function fn_compare_shipping_billing($profile_fields)
{

    if (container()->getParameter('checkout.address.billing_first')) {
        $from_section = 'B';
        $to_section = 'S';
    } else {
        $from_section = 'S';
        $to_section = 'B';
    }

    if (empty($profile_fields[$from_section]) || empty($profile_fields[$to_section])) {
        return false;
    }

    foreach ($profile_fields[$to_section] as $v) {
        // If field is set in shipping section and disabled in billing, so - different
        if (empty($profile_fields[$from_section][$v['matching_id']]) && $v['required'] == 'Y') {
            return false;

        } elseif (!empty($profile_fields[$from_section][$v['matching_id']]) && $v['required'] == 'Y' && $profile_fields[$from_section][$v['matching_id']]['required'] != 'Y') {
            return false;
        }
    }

    return true;
}


function fn_add_user_data_descriptions(&$user_data, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    fn_fill_user_fields($user_data);

    // Replace country and state values with their descriptions
    if (!empty($user_data['b_country'])) {
        $user_data['b_country_descr'] = fn_get_country_name($user_data['b_country'], $lang_code);
    }
    if (!empty($user_data['s_country'])) {
        $user_data['s_country_descr'] = fn_get_country_name($user_data['s_country'], $lang_code);
    }
    if (!empty($user_data['b_state'])) {
        $user_data['b_state_descr'] = fn_get_state_name($user_data['b_state'], $user_data['b_country'], $lang_code);
        if (empty($user_data['b_state_descr'])) {
            $user_data['b_state_descr'] = $user_data['b_state'];
        }
    }
    if (!empty($user_data['s_state'])) {
        $user_data['s_state_descr'] = fn_get_state_name($user_data['s_state'], $user_data['s_country'], $lang_code);
        if (empty($user_data['s_state_descr'])) {
            $user_data['s_state_descr'] = $user_data['s_state'];
        }
    }
}

function fn_fill_address(&$user_data, &$profile_fields, $use_default = false)
{
    if (container()->getParameter('checkout.address.billing_first') || $use_default) {
        $from = 'B';
        $to = 'S';
    } else {
        $from = 'S';
        $to = 'B';
    }

    if (!empty($profile_fields[$to])) {
        // Clean shipping/billing data
        foreach ($profile_fields[$to] as $field_id => $v) {
            if (!empty($v['matching_id']) && isset($profile_fields[$from][$v['matching_id']]['field_name'])) {
                if ($profile_fields[$from][$v['matching_id']]['field_name'] == $v['field_name']) {
                    continue;
                }
            }

            if (!empty($v['field_name'])) {
                if (empty($v['matching_id']) || (!empty($v['matching_id']) && isset($profile_fields[$from][$v['matching_id']]))) {
                    $user_data[$v['field_name']] = '';
                }
            } else {
                if (empty($v['matching_id']) || (!empty($v['matching_id']) && isset($profile_fields[$from][$v['matching_id']]))) {
                    $user_data['fields'][$v['field_id']] = '';
                }
            }
        }

        // Fill shipping/billing data with billing/shipping
        foreach ($profile_fields[$to] as $v) {
            if (isset($profile_fields[$from][$v['matching_id']])) {
                if (!empty($v['field_name']) && !empty($user_data[$profile_fields[$from][$v['matching_id']]['field_name']])) {
                    $user_data[$v['field_name']] = $user_data[$profile_fields[$from][$v['matching_id']]['field_name']];
                } elseif (isset($user_data['fields'][$profile_fields[$from][$v['matching_id']]['field_id']])) {
                    $user_data['fields'][$v['field_id']] = $user_data['fields'][$profile_fields[$from][$v['matching_id']]['field_id']];
                }
            }
        }
    }
}

function fn_fill_user_fields(&$user_data)
{
    $exclude = array(
        'user_login',
        'password',
        'user_type',
        'status',
        'cart_content',
        'timestamp',
        'referer',
        'last_login',
        'lang_code',
        'user_id',
        'profile_id',
        'profile_type',
        'profile_name',
        'tax_exempt',
        'salt',
        'company_id'
    );

    $profile_fields = fn_get_table_fields('user_profiles', $exclude);
    $fields = fn_array_merge($profile_fields, fn_get_table_fields('users', $exclude), false);

    $fill = array(
        'b_firstname' => array('firstname', 's_firstname'),
        'b_lastname' => array('lastname', 's_lastname'),
        'b_title' => array('title', 's_title'),
        'b_company' => array('s_company'),
        's_firstname' => array('b_firstname'),
        's_lastname' => array('b_lastname'),
        's_title' => array('b_title'),
        's_company' => array('b_company'),
        'firstname' => array('b_firstname', 's_firstname'),
        'lastname' => array('b_lastname', 's_lastname'),
        'title' => array('b_title', 's_title'), // if title is empty put b_title value, or s_title if b_title is empty
    );

    foreach ($fill as $k => $v) {
        if (!isset($user_data[$k])) {
            @[$f, $s] = $v;
            $user_data[$k] = !empty($user_data[$f]) ? $user_data[$f] : (!empty($s) && !empty($user_data[$s]) ? $user_data[$s] : '');
        }
    }

    // Fill empty fields to avoid php notices
    foreach ($fields as $field) {
        if (empty($user_data[$field])) {
            $user_data[$field] = '';
        }
    }

    // Fill address with default data
    if (!fn_is_empty($user_data)) {
        $default = array(
            's_country' => 'default_country',
            'b_country' => 'default_country',
        );

        foreach ($default as $k => $v) {
            if (empty($user_data[$k])) {
                $user_data[$k] = Registry::get('settings.General.' . $v);
            }
        }
    }

    return true;
}

/**
 * @return array
 */
function fn_get_profile_fields($location = 'C', $_auth = array(), $lang_code = null, $params = array())
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $auth = & $_SESSION['auth'];

    if (empty($_auth)) {
        $_auth = $auth;
    }

    if (!empty($params['get_custom'])) {
        $condition = "WHERE ?:profile_fields.is_default = 'N' ";
    } else {
        $condition = "WHERE 1 ";
    }

    if (!empty($params['get_profile_required'])) {
        $condition .= "AND ?:profile_fields.profile_required = 'Y' ";
    }

    if ($location == 'A' || $location == 'V' || $location == 'C') {
        $select = ", ?:profile_fields.profile_required as required";
        $condition .= " AND ?:profile_fields.profile_show = 'Y'";
    } elseif ($location == 'O' || $location == 'I') {
        $select = ", ?:profile_fields.checkout_required as required";
        $condition .= " AND ?:profile_fields.checkout_show = 'Y'";
    }

    if (!empty($params['field_id'])) {
        $condition .= db_quote(' AND ?:profile_fields.field_id = ?i', $params['field_id']);
    }

    if ($location == 'wCard') {
        $select = ", ?:profile_fields.w_card_required as required";
        $condition .= " AND ?:profile_fields.w_card_show = 'Y' AND ?:profile_fields.field_type != 'E'";
    }elseif ($location == 'wInvoice') {
        $select = ", ?:profile_fields.w_invoice_required as required";
        $condition .= " AND ?:profile_fields.w_invoice_show = 'Y' AND ?:profile_fields.field_type != 'E'";
    }


    // Determine whether to retrieve or not email field
    $skip_email_field = false;

    if ($location != 'I') {
        if (Registry::get('settings.General.use_email_as_login') == 'Y') {
            if ($location == 'O' && Registry::get('settings.General.disable_anonymous_checkout') == 'Y' && empty($_auth['user_id'])) {
                $skip_email_field = true;
            } elseif (strpos('APVC', $location) !== false) {
                $skip_email_field = true;
            }
        }
    }

    if ($skip_email_field) {
        $condition .= " AND ?:profile_fields.field_type != 'E'";
    }

    $profile_fields = db_get_hash_multi_array("SELECT ?:profile_fields.*, ?:profile_field_descriptions.description $select FROM ?:profile_fields LEFT JOIN ?:profile_field_descriptions ON ?:profile_field_descriptions.object_id = ?:profile_fields.field_id AND ?:profile_field_descriptions.object_type = 'F' AND lang_code = ?s $condition ORDER BY ?:profile_fields.position", array('section', 'field_id'), $lang_code);

    $matches = array();

    // Collect matching IDs
    if (!empty($profile_fields['S'])) {
        foreach ($profile_fields['S'] as $v) {
            $matches[$v['matching_id']] = $v['field_id'];
        }
    }

    $profile_fields['E'][] = array(
        'section' => 'E',
        'field_type' => 'I',
        'field_name' => 'email',
        'description' => __('email'),
        'required' => 'Y',
    );

    foreach ($profile_fields as $section => $fields) {
        foreach ($fields as $k => $v) {
            if ($v['field_type'] == 'S' || $v['field_type'] == 'R') {
                if ($v['field_name'] === 'title' || $v['field_name'] === 'b_title' || $v['field_name'] === 's_title') {
                    $profile_fields[$section][$k]['values'] = [
                        UserTitle::MR => __(UserTitle::MR()->getTranslationKey()),
                        UserTitle::MRS => __(UserTitle::MRS()->getTranslationKey()),
                    ];
                    break;
                }

                $_id = $v['field_id'];
                if ($section == 'B' && empty($v['field_name'])) {
                    // If this field is enabled in billing section
                    if (!empty($matches[$v['field_id']])) {
                        $_id = $matches[$v['field_id']];
                    // Otherwise, get it from database
                    } else {
                        $_id = db_get_field("SELECT field_id FROM ?:profile_fields WHERE matching_id = ?i", $v['field_id']);
                    }
                }
                $profile_fields[$section][$k]['values'] = db_get_hash_single_array("SELECT ?:profile_field_values.value_id, ?:profile_field_descriptions.description FROM ?:profile_field_values LEFT JOIN ?:profile_field_descriptions ON ?:profile_field_descriptions.object_id = ?:profile_field_values.value_id AND ?:profile_field_descriptions.object_type = 'V' AND ?:profile_field_descriptions.lang_code = ?s WHERE ?:profile_field_values.field_id = ?i ORDER BY ?:profile_field_values.position", array('value_id', 'description'), $lang_code, $_id);
            }
        }
    }

    if (!empty($params['field_id'])) {
        $result = reset($profile_fields);
        if (!empty($result[$params['field_id']])) {
            return $result[$params['field_id']];
        } else {
            return array();
        }
    }

    $sections = array(
        'C' => true,
        'B' => true,
        'S' => true,
        'E' => true
    );

    $sections = array_intersect_key($sections, $profile_fields);
    $profile_fields = array_merge($sections, $profile_fields);

    //Delete adresses profile's section when FF is disabled
    if ($profile_fields['B'][43]['profile_show'] === 'Y'
        && (container()->getParameter('feature.enable_divisions') === false
            || container()->getParameter('feature.available_offers') === false))
    {
        unset($profile_fields['B'][43]);
    }

    if ($profile_fields['S'][42]['profile_show'] === 'Y'
        && (container()->getParameter('feature.enable_divisions') === false
            || container()->getParameter('feature.available_offers') === false))
    {
        unset($profile_fields['S'][42]);
    }

    return $profile_fields;
}

function fn_store_profile_fields($user_data, $object_id, $object_type)
{
    // Delete existing fields
    if ($object_type == 'UP') {
        db_query("DELETE FROM ?:profile_fields_data WHERE (object_id = ?i AND object_type = ?s) OR (object_id = ?i AND object_type = ?s)", $object_id['U'], 'U', $object_id['P'], 'P');
    } else {
        db_query("DELETE FROM ?:profile_fields_data WHERE object_id = ?i AND object_type = ?s", $object_id, $object_type);
    }

    if (!empty($user_data['fields'])) {
        $fields_info = db_get_hash_array("SELECT field_id, field_type, section FROM ?:profile_fields WHERE field_id IN (?n)", 'field_id', array_keys($user_data['fields']));

        $_data = array ();
        foreach ($user_data['fields'] as $field_id => $value) {
            if ($object_type == 'UP') {
                $_data['object_type'] = ($fields_info[$field_id]['section'] == 'C') ? 'U' : 'P';
                $_data['object_id'] = ($fields_info[$field_id]['section'] == 'C') ? $object_id['U'] : $object_id['P'];
            } else {
                $_data['object_type'] = $object_type;
                $_data['object_id'] = $object_id;
            }
            $_data['field_id'] = $field_id;
            $_data['value'] = ($fields_info[$field_id]['field_type'] == 'D') ? fn_parse_date($value) : $value;

            db_query("REPLACE INTO ?:profile_fields_data ?e", $_data);
        }
    }

    return true;
}

//
// Fill auth array
//
function fn_fill_auth($user_data = array(), $order_ids = array(), $act_as_user = false, $area = AREA)
{
    $_auth = array (
        'area' => !fn_check_user_type_admin_area($user_data) ? 'C' : 'A',
        'user_id' => empty($user_data['user_id']) ? 0 : $user_data['user_id'],
        'user_type' => !empty($user_data['user_type']) ? $user_data['user_type'] : 'C',
        'tax_exempt' => empty($user_data['tax_exempt']) ? 'N' : $user_data['tax_exempt'],
        'last_login' => empty($user_data['last_login']) ? 0 : $user_data['last_login'],
        'order_ids' => $order_ids,
        'act_as_user' => $act_as_user,
        'this_login' => TIME,
        'password_change_timestamp' => empty($user_data['password_change_timestamp']) ? 0 : $user_data['password_change_timestamp'],
        'company_id' => empty($user_data['company_id']) ? 0 : $user_data['company_id'],
        'is_root' => empty($user_data['is_root']) ? 'N' : $user_data['is_root'],
        'referer' => !empty($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '',
        'locale' => $user_data['lang_code'] ?? null,
    );

    if (Registry::get('runtime.simple_ultimate')) {
        unset($_auth['company_id']);
    }

    return $_auth;
}

//
// The function saves information into user_data table.
//
function fn_save_user_additional_data($type, $data, $user_id = 0)
{
    $auth = & $_SESSION['auth'];

    if (empty($user_id) && !empty($auth['user_id'])) {
        $user_id = $auth['user_id'];
    }

    if (empty($user_id)) {
        return false;
    }

    return db_query('REPLACE INTO ?:user_data ?e', array('user_id' => $user_id, 'type' => $type, 'data' => serialize($data)));
}

//
// The function returns information from user_data table.
//
function fn_get_user_additional_data($type, $user_id = 0)
{
    $auth = & $_SESSION['auth'];

    if (empty($user_id) && !empty($auth['user_id'])) {
        $user_id = $auth['user_id'];
    }

    if (empty($user_id)) {
        return false;
    }

    $data = db_get_field("SELECT data FROM ?:user_data WHERE user_id = ?i AND type = ?s", $user_id, $type);
    if (!empty($data)) {
        $data = unserialize($data);
    }

    return $data;
}

//
// The function returns description of user type.
//
function fn_get_user_type_description($type, $plural = false, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $type_descr = array(
        'S' => array(
            'C' => 'customer',
            'A' => 'administrator',
        ),
        'P' => array(
            'C' => 'customers',
            'A' => 'administrators',
        ),
    );

    $type_descr['S']['V'] = 'vendor_administrator';
    $type_descr['P']['V'] = 'vendor_administrators';

    $s = ($plural == true) ? 'P' : 'S';

    return __($type_descr[$s][$type], '', $lang_code);
}

/**
 * Getting users list
 *
 * @param  array  $params          Params list
 * @param  array  $auth            Auth
 * @param  int    $items_per_page  Items per page
 * @param  str    $custom_view     Custom view
 * @return array
 */
function fn_get_users($params, &$auth, $items_per_page = 0, $custom_view = '')
{
    // Init filter
    $_view = !empty($custom_view) ? $custom_view : 'users';
    $params = LastView::instance()->update($_view, $params);

    // Set default values to input params
    $default_params = array (
        'page' => 1,
        'items_per_page' => $items_per_page
    );

    $params = array_merge($default_params, $params);

    // Define fields that should be retrieved
    $fields = array(
        "?:users.user_id",
        "?:users.user_login",
        "?:users.is_root",
        "?:users.is_locked",
        "?:users.timestamp",
        "?:users.user_type",
        "?:users.status",
        "?:users.firstname",
        "?:users.lastname",
        "?:users.email",
        "?:users.company",
        "?:users.company_id",
        "?:companies.company as company_name",
    );

    // Define sort fields
    $sortings = array(
        'id' => "?:users.user_id",
        'username' => "?:users.user_login",
        'email' => "?:users.email",
        'name' => array("?:users.lastname", "?:users.firstname"),
        'date' => "?:users.timestamp",
        'type' => "?:users.user_type",
        'status' => "?:users.status",
        'company' => "company_name",
    );

    if (isset($params['compact']) && $params['compact'] == 'Y') {
        $union_condition = ' OR ';
    } else {
        $union_condition = ' AND ';
    }

    $condition = array();
    $join = $group = '';

    $group .= " GROUP BY ?:users.user_id";

    if (isset($params['name']) && fn_string_not_empty($params['name'])) {
        $arr = fn_explode(' ', $params['name']);
        foreach ($arr as $k => $v) {
            if (!fn_string_not_empty($v)) {
                unset($arr[$k]);
            }
        }
        if (sizeof($arr) == 2) {
            $condition['name'] = db_quote(" AND (?:users.firstname LIKE ?l AND ?:users.lastname LIKE ?l)",  "%".array_shift($arr)."%", "%".array_shift($arr)."%");
        } else {
            $condition['name'] = db_quote(" AND (?:users.firstname LIKE ?l OR ?:users.lastname LIKE ?l)", "%".trim($params['name'])."%", "%".trim($params['name'])."%");
        }
    }

    if (isset($params['user_login']) && fn_string_not_empty($params['user_login'])) {
        $condition['user_login'] = db_quote(" $union_condition ?:users.user_login LIKE ?l", "%".trim($params['user_login'])."%");
    }

    if (!empty($params['tax_exempt'])) {
        $condition['tax_exempt'] = db_quote(" AND ?:users.tax_exempt = ?s", $params['tax_exempt']);
    }

    if (!empty($params['status'])) {
        $condition['status'] = db_quote(" AND ?:users.status = ?s", $params['status']);
    }

    if (isset($params['email']) && fn_string_not_empty($params['email'])) {
        $condition['email'] = db_quote(" $union_condition ?:users.email LIKE ?l", "%".trim($params['email'])."%");
    }

    if (isset($params['address']) && fn_string_not_empty($params['address'])) {
        $condition['address'] = db_quote(" AND (?:user_profiles.b_address LIKE ?l OR ?:user_profiles.s_address LIKE ?l)", "%".trim($params['address'])."%", "%".trim($params['address'])."%");
    }

    if (isset($params['zipcode']) && fn_string_not_empty($params['zipcode'])) {
        $condition['zipcode'] = db_quote(" AND (?:user_profiles.b_zipcode LIKE ?l OR ?:user_profiles.s_zipcode LIKE ?l)", "%".trim($params['zipcode'])."%", "%".trim($params['zipcode'])."%");
    }

    if (!empty($params['country'])) {
        $condition['country'] = db_quote(" AND (?:user_profiles.b_country LIKE ?l OR ?:user_profiles.s_country LIKE ?l)", "%$params[country]%", "%$params[country]%");
    }

    if (isset($params['state']) && fn_string_not_empty($params['state'])) {
        $condition['state'] = db_quote(" AND (?:user_profiles.b_state LIKE ?l OR ?:user_profiles.s_state LIKE ?l)", "%".trim($params['state'])."%", "%".trim($params['state'])."%");
    }

    if (isset($params['city']) && fn_string_not_empty($params['city'])) {
        $condition['city'] = db_quote(" AND (?:user_profiles.b_city LIKE ?l OR ?:user_profiles.s_city LIKE ?l)", "%".trim($params['city'])."%", "%".trim($params['city'])."%");
    }

    if (!empty($params['user_id'])) {
        $condition['user_id'] = db_quote(' AND ?:users.user_id IN (?n)', $params['user_id']);
    }

    if (!empty($params['p_ids']) || !empty($params['product_view_id'])) {
        $arr = (strpos($params['p_ids'], ',') !== false || !is_array($params['p_ids'])) ? explode(',', $params['p_ids']) : $params['p_ids'];
        if (empty($params['product_view_id'])) {
            $condition['order_product_id'] = db_quote(" AND ?:order_details.product_id IN (?n)", $arr);
        } else {
            $condition['order_product_id'] = db_quote(" AND ?:order_details.product_id IN (?n)", db_get_fields(fn_get_products(array('view_id' => $params['product_view_id'], 'get_query' => true))));
        }

        $join .= db_quote(" LEFT JOIN ?:orders ON ?:orders.user_id = ?:users.user_id AND ?:orders.is_parent_order != 'Y' LEFT JOIN ?:order_details ON ?:order_details.order_id = ?:orders.order_id");
    }

    if (defined('RESTRICTED_ADMIN')) { // FIXME: NOT GOOD
        $condition['restricted_admin'] = db_quote(" AND ((?:users.user_type != 'A' AND ?:users.user_type != 'V') OR (?:users.user_type = 'A' AND ?:users.user_id = ?i))", $auth['user_id']);
    }

    // sometimes other vendor's admins could buy products from other vendors.
    if (!empty($params['user_type']) && (!($params['user_type'] == 'C' && Registry::get('runtime.company_id')))) {
        $condition['user_type'] = db_quote(' AND ?:users.user_type = ?s', $params['user_type']);
    } else {

        // Get active user types
        $user_types = array_keys(fn_get_user_types());

        // Select only necessary groups frm all available
        if (!empty($params['user_types'])) {
            $user_types = array_intersect($user_types, $params['user_types']);
        }

        if (!empty($params['exclude_user_types'])) {
            $user_types = array_diff($user_types, $params['exclude_user_types']);
        }

        $condition['user_type'] = db_quote(" AND ?:users.user_type IN(?a)", $user_types);
    }

    $join .= db_quote(" LEFT JOIN ?:user_profiles ON ?:user_profiles.user_id = ?:users.user_id");

    $join .= db_quote(" LEFT JOIN ?:companies ON ?:companies.company_id = ?:users.company_id");

    if (isset($params['company']) && fn_string_not_empty($params['company'])) {
        $condition['company'] = db_quote(" AND (?:users.company LIKE ?l OR ?:user_profiles.b_company LIKE ?l OR ?:user_profiles.s_company LIKE ?l OR ?:companies.company LIKE ?l)",
            "%".trim($params['company'])."%",
            "%".trim($params['company'])."%",
            "%".trim($params['company'])."%",
            "%" . trim($params['company']) . "%"
         );
    }

    if (isset($params['company_id']) && $params['company_id'] != '') {
        $condition['company_id'] = db_quote(' AND ?:users.company_id = ?i ', $params['company_id']);
    }

    if (Registry::get('runtime.company_id')) {
        if (empty($params['user_type'])) {
            $condition['users_company_id'] = db_quote(" AND (?:users.user_id IN (?n) OR (?:users.user_type != ?s AND" . fn_get_company_condition('?:users.company_id', false) . ")) ", fn_get_company_customers_ids(Registry::get('runtime.company_id')), 'C');
        } elseif (fn_check_user_type_admin_area ($params['user_type'])) {
            $condition['users_company_id'] = fn_get_company_condition('?:users.company_id');
        } elseif ($params['user_type'] == 'C') {
            $condition['users_company_id'] = db_quote(" AND ?:users.user_id IN (?n) ", fn_get_company_customers_ids(Registry::get('runtime.company_id')));
        }
    }

    $sorting = db_sort($params, $sortings, 'name', 'asc');

    // Used for Extended search
    if (!empty($params['get_conditions'])) {
        return array($fields, $join, $condition);
    }

    // Paginate search results
    $limit = '';
    if (!empty($params['items_per_page'])) {
        $params['total_items'] = db_get_field("SELECT COUNT(DISTINCT(?:users.user_id)) FROM ?:users $join WHERE 1 ". implode(' ', $condition));
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    $users = db_get_array("SELECT " . implode(', ', $fields) . " FROM ?:users $join WHERE 1" . implode('', $condition) . " $group $sorting $limit");

    LastView::instance()->processResults('users', $users, $params);

    return array($users, $params);
}

function fn_get_user_types()
{
    $types = array (
        'C' => 'add_customer',
        'A' => 'add_administrator',
    );

    $company_id = Registry::get('runtime.company_id');
    if ($company_id) {
        unset($types['A']);
    }

    $types['V'] = 'add_vendor_administrator';

    return $types;
}

function fn_get_user_edp($params, $items_per_page = 0)
{
    $default_params = array (
        'page' => 1,
        'items_per_page' => $items_per_page
    );

    $params = array_merge($default_params, $params);

    $fields = array (
        '?:order_details.product_id',
        '?:order_details.order_id',
        '?:order_details.extra',
        '?:orders.status',
        '?:products.unlimited_download',
        '?:product_descriptions.product'
    );

    $where = db_quote(' AND ?:orders.status != ?s', STATUS_INCOMPLETED_ORDER);
    $orders_company_condition = '';
    $limit = '';

    if (!empty($params['order_ids'])) {
        $where = db_quote(" AND ?:orders.order_id IN (?n)", $params['order_ids']);
    } elseif (!empty($params['items_per_page'])) {
        $params['total_items'] = count(db_get_fields(
            "SELECT COUNT(*)"
            . " FROM ?:order_details"
            . " INNER JOIN ?:orders ON ?:orders.order_id = ?:order_details.order_id AND ?:orders.is_parent_order != 'Y' $orders_company_condition"
            . " INNER JOIN ?:product_file_ekeys ON ?:product_file_ekeys.product_id = ?:order_details.product_id AND ?:product_file_ekeys.order_id = ?:order_details.order_id"
            . " INNER JOIN ?:product_files ON ?:product_files.product_id = ?:order_details.product_id"
            . " WHERE ?:orders.user_id = ?i AND ?:product_files.status = 'A'" . $where
            . " GROUP BY ?:order_details.product_id, ?:order_details.order_id",
            $params['user_id']
        ));
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    $products = db_get_array(
        "SELECT " . implode(', ', $fields)
        . " FROM ?:order_details"
        . " INNER JOIN ?:product_files ON ?:product_files.product_id = ?:order_details.product_id"
        . " INNER JOIN ?:orders ON ?:orders.order_id = ?:order_details.order_id AND ?:orders.is_parent_order != 'Y' $orders_company_condition"
        . " INNER JOIN ?:product_file_ekeys ON ?:product_file_ekeys.product_id = ?:order_details.product_id AND ?:product_file_ekeys.order_id = ?:order_details.order_id"
        . " LEFT JOIN ?:products ON ?:products.product_id = ?:order_details.product_id"
        . " LEFT JOIN ?:product_descriptions ON ?:product_descriptions.product_id = ?:order_details.product_id AND ?:product_descriptions.lang_code = ?s"
        . " LEFT JOIN ?:product_file_folders ON ?:product_file_folders.product_id = ?:order_details.product_id"
        . " WHERE ?:orders.user_id = ?i AND ?:orders.is_parent_order != 'Y' AND ?:product_files.status = 'A' AND (?:product_file_folders.status = 'A' OR ?:product_files.folder_id IS NULL)" . $where
        . " GROUP BY ?:order_details.order_id, ?:order_details.product_id"
        . " ORDER BY ?:orders.order_id DESC $limit",
        (string) GlobalState::interfaceLocale(), $params['user_id']
    );

    if (!empty($products)) {
        foreach ($products as $k => $v) {
            $params = array (
                'product_id' => $v['product_id'],
                'order_id' => $v['order_id']
            );
            [$product_file_folders] = fn_get_product_file_folders($params);
            [$product_files] = fn_get_product_files($params);
            $products[$k]['files_tree'] = fn_build_files_tree($product_file_folders, $product_files);
        }
    }

    return array($products, $params);
}

function fn_is_restricted_admin($params)
{
    if (!defined('RESTRICTED_ADMIN')) {
        return false;
    }

    $auth = & $_SESSION['auth'];

    $not_own_profile = false;
    $is_restricted = false;

    if ($is_restricted) {
        return true;
    }

    if (!empty($params['user_id']) && $params['user_id'] != $auth['user_id']) {
        $requested_utype = db_get_field("SELECT user_type FROM ?:users WHERE user_id = ?i", $params['user_id']);
        if (in_array($requested_utype, array('A', 'V'))) {
            return true;
        }
        $not_own_profile = true;
    } elseif (empty($params['user_id'])) {
        $not_own_profile = true;
    }

    $user_type = isset($params['user_data']['user_type']) ? $params['user_data']['user_type'] : (isset($params['user_type']) ? $params['user_type'] : '');
    if ($not_own_profile && fn_check_user_type_admin_area($user_type)) {
        return true;
    }

    return false;
}

/**
 * This function initializes the session data of a selected user (cart, wishlist etc...)
 *
 * @param array $sess_data
 * @param int $user_id
 * @return true
 */

function fn_init_user_session_data(&$sess_data, $user_id)
{
    // Restore cart content
    $sess_data['cart'] = empty($sess_data['cart']) ? array() : $sess_data['cart'];

    // Cleanup cached shipping rates
    unset($sess_data['shipping_rates']);

    fn_extract_cart_content($sess_data['cart'], $user_id, 'C');
    $sess_data['cart']['user_data'] = fn_get_user_info($user_id);

    fn_save_cart_content($sess_data['cart'], $user_id);

    return true;
}

/**
 * Restores password, password1 and password2 with clear POST data.
 * Example: password1 = "Some<secret>password"
 *     Processed before $_REQUEST['user_data']['password1'] = 'Somepassword'
 *     Returned value $_REQUEST['user_data']['password1'] = 'Some<secret>password'
 *
 * @param array &$destination $_REQUEST
 * @param array &$source $_POST
 */
function fn_restore_processed_user_password(&$destination, &$source)
{
    $fields = array(
        'password', 'password1', 'password2', 'old_password'
    );

    foreach ($fields as $field) {
        if (isset($source[$field])) {
            $destination[$field] = $source[$field];
        }
    }
}

/**
 * Add/update user
 *
 * @param int $user_id - user ID to update (empty for new user)
 * @param array $user_data - user data
 * @param array $auth - authentication information
 * @param bool $ship_to_another - flag indicates that shipping and billing fields are different
 * @param bool $notify_user - flag indicates that user should be notified
 * @param bool $send_password - TRUE if the password should be included into the e-mail
 * @param bool $magical_merge - TRUE si les champs des adresses de  livraison/facturation doivent impacter les information du profil utilisateur
 * @return array with user ID and profile ID if success, false otherwise
 */
function fn_update_user($user_id, $user_data, &$auth, $ship_to_another, $notify_user, $send_password = false, $magical_merge = true)
{
    $companyIdChanged = false;
    $passwordChanged = false;

    if (isset($user_data['organisation_id']) && $user_data['organisation_id'] === '') {
        //si on ne fait pas ça, ça va causer une erreur à cause de la clé étrangère
        unset($user_data['organisation_id']);
    }

    if (!$user_id && empty($user_data['api_key'])) {
        $user_data['api_key'] = \Tygh\Api::generateKey();
    }
    if (isset($user_data['password1']) && !isset($user_data['password2'])) {
        $user_data['password2'] = $user_data['password1'];
    }

    // Check email
    if (
        (
            empty($user_id) || // If I am registering
            (!empty($user_id) && isset($user_data['email'])) // or updating, with email field in data
        )
        && !filter_var(($user_data['email'] ?? ''), FILTER_VALIDATE_EMAIL) // and the given email is wrong
    ) {
        if ($auth === null || (\is_array($auth) === true && \count($auth) === 0)) {
            fn_set_notification('E', __('error'), __('error_validator_email'));
        }

        return false;
    }

    \Wizacha\Profile::correlateProfileDatas($user_data, $magical_merge);

    if (array_key_exists('birthday', $user_data) === true && $user_data['birthday'] !== null) {
        if (is_string($user_data['birthday']) === true) {
            $user_data['birthday'] = \DateTime::createFromFormat(UserSerializer::BIRTHDAY_FORMAT, $user_data['birthday']);
        }
        $user_data['birthday'] = $user_data['birthday'] ? $user_data['birthday']->format(UserSerializer::BIRTHDAY_FORMAT) : null;
    }

    if (!empty($user_data['c2c_vendor_pseudo'])) {
        $c2c_vendor_pseudo = $user_data['c2c_vendor_pseudo'];
    }

    $register_at_checkout = isset($user_data['register_at_checkout']) && $user_data['register_at_checkout'] == 'Y' ? true : false;

    if (!empty($user_id)) {
        $current_user_data = db_get_row(
            "SELECT user_id, company_id, is_root, status, user_type, user_login, lang_code, password, salt, last_passwords, is_locked
            FROM ?:users WHERE user_id = ?i",
            $user_id
        );

        if (empty($current_user_data)) {
            fn_set_notification('E', __('error'), __('object_not_found', array('[object]' => __('user'))),'','404');

            return false;
        }

        if (!empty($user_data['profile_id']) && AREA != 'A') {
            $profile_ids = db_get_fields("SELECT profile_id FROM ?:user_profiles WHERE user_id = ?i", $user_id);

            if (!in_array($user_data['profile_id'], $profile_ids)) {
                fn_set_notification('W', __('warning'), __('access_denied'));

                return false;
            }
        }

        // If the user company changed, we check if the previous one still has one associated administrator
        if (true === array_key_exists('company_id', $user_data) && $user_data['company_id'] !== $current_user_data['company_id']) {
            $companyIdChanged = true;
            // Vérifier l'existance de vendeur courant
            $currentCompanyData = fn_get_company_data($current_user_data['company_id']);

            if (\is_array($currentCompanyData) ===  true && \array_key_exists('company_id', $currentCompanyData) === true) {
                list($same_company_users,) = fn_get_users(['company_id' => $current_user_data['company_id']], $auth);

                if (\count($same_company_users) === 1) {
                    fn_set_notification('E', __('error'), __('error_no_administrator_name'));

                    return false;
                }
            }
        }

        if ($user_data['company_id'] === null ||  (AREA !== 'A' && container()->get('security.authorization_checker')->isGranted('ROLE_ADMIN') === false)) {
            //we should set company_id for the frontend
            $user_data['company_id'] = $current_user_data['company_id'];
        }

        $action = 'update';
    } else {
        $current_user_data = array(
            'status' => (AREA != 'A' && container()->getParameter('feature.approve_user_by_admin')) ? \Wizacha\Status::PENDING : (!empty($user_data['status']) ? $user_data['status'] : 'A'),
            'user_type' => 'C', // FIXME?
        );

        $action = 'add';

        $user_data['lang_code'] = !empty($user_data['lang_code']) ? $user_data['lang_code'] : (string) GlobalState::interfaceLocale();
        $user_data['timestamp'] = TIME;
    }

    $current_user_data['password'] = !empty($current_user_data['password']) ? $current_user_data['password'] : '';
    $current_user_data['salt'] = !empty($current_user_data['salt']) ? $current_user_data['salt'] : '';

    // Set the user type
    $user_data['user_type'] = fn_check_user_type($user_data, $current_user_data);

    // Remove company_id if user_type is changed from V to C and company name from 'company' field
    if ($current_user_data['user_type'] === 'V' && $user_data['user_type'] === 'C') {
        $companyIdChanged = true;

        $user_data['company_id'] = 0;
        $user_data['company'] = "";
    }

    if (
        Registry::get('runtime.company_id')
        && (
            !fn_check_user_type_admin_area($user_data['user_type'])
            || (
                isset($current_user_data['company_id'])
                && $current_user_data['company_id'] != Registry::get('runtime.company_id')
            )
        )
    ) {
        fn_set_notification('W', __('warning'), __('access_denied'));

        return false;
    }

    // Check if this user needs login/password
    if (fn_user_need_login($user_data['user_type'])) {
        // Check if user_login already exists
        // FIXME
        if (!isset($user_data['email'])) {
            $user_data['email'] = db_get_field("SELECT email FROM ?:users WHERE user_id = ?i", $user_id);
        }

        $is_exist = fn_is_user_exists($user_id, $user_data);

        if ($is_exist) {
            fn_set_notification('E', __('error'), __('error_user_exists'), '', 'user_exist');

            return false;
        }

        // Check the passwords
        if (!empty($user_data['password1']) || !empty($user_data['password2'])) {
            $user_data['password1'] = !empty($user_data['password1']) ? trim($user_data['password1']) : '';
            $user_data['password2'] = !empty($user_data['password2']) ? trim($user_data['password2']) : '';
        }

        // if the passwords are not set and this is not a forced password check
        // we will not update password, otherwise let's check password
        if (!empty($_SESSION['auth']['forced_password_change']) || !empty($user_data['password1']) || !empty($user_data['password2'])) {

            $valid_passwords = true;
            $featureStrictPassword = container()->getParameter('feature.security.enable_strict_password');

            if ($user_data['password1'] != $user_data['password2']) {
                $valid_passwords = false;
                fn_set_notification('E', __('error'), __('error_passwords_dont_match'));
            }

            // We not validate password when we deactivate user (anonymization for exemple)
            if (
                (array_key_exists('is_locked', $current_user_data) === false || (bool) $current_user_data['is_locked'] === false)
                && (array_key_exists('is_locked', $user_data) === false || (bool) $user_data['is_locked'] === false)
                && (
                    \filter_var($featureStrictPassword, FILTER_VALIDATE_BOOLEAN) === true
                    && Password::isPasswordFormatValid($user_data['password1']) === false
                )) {
                $valid_passwords = false;
                fn_set_notification(
                    'E',
                    __('error'),
                    __('error_password_format_not_valid'),
                    '',
                    'password_format_invalid'
                );
            }

            if (!$valid_passwords) {
                return false;
            }

            $user_data['password'] = fn_encode_password($user_data['password1']);
            if ($user_data['password'] != $current_user_data['password'] && !empty($user_id)) {
                // if user set current password - there is no necessity to update password_change_timestamp
                $user_data['password_change_timestamp'] = $_SESSION['auth']['password_change_timestamp'] = TIME;

                // check if password is different from x previous passwords
                $userService = container()->get('marketplace.user.user_service');
                $isPasswordExist = container()->get(UserPasswordHistoryService::class)->isPasswordExist($userService->get($user_id), $user_data['password1']);
                if ($isPasswordExist === true) {
                    fn_set_notification(
                        'E',
                        __('error'),
                        __('error_password_difference_limit', ['%limit%' => container()->getParameter('previous_passwords_difference_limit')]),
                        '',
                        'error_password_difference_limit'

                    );

                    return false;
                }
                $passwordChanged = true;
            }
            unset($_SESSION['auth']['forced_password_change']);
            fn_delete_notification('password_expire');

        }
    }

    $user_data['status'] = (AREA != 'A' || empty($user_data['status'])) ? $current_user_data['status'] : $user_data['status']; // only administrator can change user status

    // Fill the firstname, lastname and phone from the billing address if the profile was created or updated through the admin area.
    if (AREA != 'A') {
        container()->getParameter('checkout.address.billing_first') ? $address_zone = 'b' : $address_zone = 's';
    } else {
        $address_zone = 'b';
    }
    if ($magical_merge && empty($user_data['firstname']) && !empty($user_data[$address_zone . '_firstname'])) {
        $user_data['firstname'] = $user_data[$address_zone . '_firstname'];
    }
    if ($magical_merge &&  empty($user_data['lastname']) && !empty($user_data[$address_zone . '_lastname'])) {
        $user_data['lastname'] = $user_data[$address_zone . '_lastname'];
    }
    if ($magical_merge && empty($user_data['phone']) && !empty($user_data[$address_zone . '_phone'])) {
        $user_data['phone'] = $user_data[$address_zone . '_phone'];
    }

    //for ult company_id was set before
    fn_set_company_id($user_data);

    if (!empty($current_user_data['is_root']) && $current_user_data['is_root'] == 'Y') {
        $user_data['is_root'] = 'Y';
    } else {
        $user_data['is_root'] = 'N';
    }

    // check if it is a root admin
    $is_root_admin_exists = db_get_field(
        "SELECT user_id FROM ?:users WHERE company_id = ?i AND is_root = 'Y' AND user_id != ?i",
        $user_data['company_id'], !empty($user_id) ? $user_id : 0
    );
    $user_data['is_root'] = empty($is_root_admin_exists) && $user_data['user_type'] !== 'C' ? 'Y' : 'N';

    unset($user_data['user_id']);

    if (!empty($user_id)) {
        file_put_contents("/var/www/html/change_user_data.log", "user_data => " . print_r($user_data,true) . "\nuser_id =>" . print_r($user_id,true) . "\n", FILE_APPEND);
        db_query("UPDATE ?:users SET ?u WHERE user_id = ?i", $user_data, $user_id);

        fn_log_event('users', 'update', array(
            'user_id' => $user_id,
            'company_id' => fn_get_company_id('users', 'user_id', $user_id),
        ));
    } else {
        if (!isset($user_data['password_change_timestamp'])) {
            $user_data['password_change_timestamp'] = TIME;
        }

        // Generate Uuid V4
        $user_data['user_uuid'] = Uuid::uuid4();

        try {
            $user_id = db_query("INSERT INTO ?:users ?e", $user_data);
        } catch (\Exception $exception) {
            if ($exception->getCode() === 1062) { // Duplicate entry
                fn_set_notification('E', __('error'), __('error_user_exists'), '', 'user_exist');

                return false;
            }

            throw $exception;
        }

        fn_log_event('users', 'create', array(
            'user_id' => $user_id,
            'company_id' => fn_get_company_id('users', 'user_id', $user_id),
        ));
    }
    $user_data['user_id'] = $user_id;

    // Set/delete insecure password notification
    if (AREA == 'A' && !empty($user_data['password1'])) {
        if (!fn_compare_login_password($user_data, $user_data['password1'])) {
            fn_delete_notification('insecure_password');
        } else {

            $lang_var = 'warning_insecure_password';
            if (Registry::get('settings.General.use_email_as_login') == 'Y') {
                $lang_var = 'warning_insecure_password_email';
            }

            fn_set_notification('E', __('warning'), __($lang_var, array(
                '[link]' => fn_url("profiles.update?user_id=" . $user_id)
            )), 'K', 'insecure_password');
        }
    }

    if (empty($user_data['user_login'])) { // if we're using email as login or user type does not require login, fill login field
        db_query("UPDATE ?:users SET user_login = 'user_?i' WHERE user_id = ?i AND user_login = ''", $user_id, $user_id);
    }

    // Fill shipping info with billing if needed
    if (empty($ship_to_another)) {
        $profile_fields = fn_get_profile_fields($user_data['user_type']);
        $use_default = (AREA == 'A') ? true : false;
        fn_fill_address($user_data, $profile_fields, $use_default);
    }

    $user_data['profile_id'] = fn_update_user_profile($user_id, $user_data, $action);

    if (\array_key_exists('nationalities', $user_data) === true && \is_array($user_data['nationalities']) === true) {
        $userService = container()->get('marketplace.user.user_service');
        $userService->updateNationalities($user_id, $user_data['nationalities']);
    }


    $user_data = fn_get_user_info($user_id, true, $user_data['profile_id']);

    if ($register_at_checkout) {
        $user_data['register_at_checkout'] = 'Y';
    }
    $lang_code = (AREA == 'A' && !empty($user_data['lang_code'])) ? $user_data['lang_code'] : (string) GlobalState::interfaceLocale();

    // Send notifications to customer
    $user = container()->get('marketplace.user.user_repository')->findOneByEmail($user_data['email'], true);
    if (!empty($notify_user)) {

        // Notify customer about profile activation (when update profile only)
        if ($action == 'update' && in_array($current_user_data['status'], [\Wizacha\Status::DISABLED, \Wizacha\Status::PENDING]) && $user_data['status'] === \Wizacha\Status::ENABLED) {
            container()->get('event_dispatcher')
                ->dispatch(new UserProfileActivated($user), UserProfileActivated::class);
        }

        if ($action === 'add') {
            container()->get('event_dispatcher')
                ->dispatch(new UserProfileCreated($user), UserProfileCreated::class);
        } else {
            container()->get('event_dispatcher')
                ->dispatch(new UserProfileUpdated($user), UserProfileUpdated::class);
        }
    }

    if ($action == 'add') {
        $skip_auth = false;
        if (AREA != 'A') {
            if (container()->getParameter('feature.approve_user_by_admin')) {
                fn_set_notification('W', __('important'), __('text_profile_should_be_approved'));

                // Notify administrator about new profile
                container()->get('event_dispatcher')
                    ->dispatch(new UserProfileActivationRequested($user), UserProfileActivationRequested::class);

                $skip_auth = true;
            } else {
                fn_set_notification('N', __('information'), __('text_profile_is_created'));
            }
        }

        if (!is_null($auth)) {
            if (!empty($auth['order_ids'])) {
                db_query("UPDATE ?:orders SET user_id = ?i WHERE order_id IN (?n)", $user_id, $auth['order_ids']);
            }

            if (empty($skip_auth)) {
                $auth = fn_fill_auth($user_data);
                if ($c2c_vendor_pseudo) {
                    $_SESSION['auth']['company_id'] = \Wizacha\Company::newC2C(
                        $user_id,
                        $c2c_vendor_pseudo
                    );
                }

                if ($session = container()->get('session')) {
                    \Wizacha\Product::createFromSession($session);
                }
            }
        }
    } else {
        if (AREA == 'C') {
            fn_set_notification('N', __('information'), __('text_profile_is_updated'));
        }
    }


    if ($company_id = $user_data['company_id']) {
        \Wizacha\Company::synchronizeDataC2C($company_id);
        if (array_key_exists('company_id', $current_user_data)
            && $current_user_data['company_id']
            && $current_user_data['company_id'] != $company_id
        ) {
            \Wizacha\Company::synchronizeDataC2C($current_user_data['company_id']);
        }
    }

    if ($action === 'update') {
        // if status is updated
        if ($current_user_data['status'] !== $user_data['status']) {
            // status is updated to enabled
            if ($user_data['status'] === Status::ENABLED()->getValue()) {
                container()->get('event_dispatcher')
                    ->dispatch(new UserActivated($user));
            }
            // status is updated to disabled
            if ($user_data['status'] === Status::DISABLED()->getValue()) {
                container()->get('event_dispatcher')
                    ->dispatch(new UserDeactivated($user));
            }
        }
        // if user type is updated
        if ($current_user_data['user_type'] !== $user_data['user_type']) {
            container()->get('event_dispatcher')
                ->dispatch(new UserTypeChanged($user));
        }
        // user is updated -> dispatch event
        container()->get('event_dispatcher')
            ->dispatch(new UserUpdated($user));
    }

    // if user is created we send an event to the dispatcher
    if ($action === 'add') {
        $passwordChanged = true;
        container()->get('event_dispatcher')
            ->dispatch(new UserCreated($user));
    }

    // if company_id is updated we send an event to the dispatcher
    if (true === $companyIdChanged) {
        container()->get('event_dispatcher')
            ->dispatch(new UserCompanyChanged($user));
    }

    // dispatch event password changed
    if ($passwordChanged === true) {
        container()->get('event_dispatcher')->dispatch(
            new UserPasswordChanged(
                container()->get(UserService::class)->get($user_id)
            ),
            UserPasswordChanged::class
        );
    }

    return array($user_id, !empty($user_data['profile_id']) ? $user_data['profile_id'] : false);
}

/**
 * Updates profile data of registered user
 *
 * @param int $user_id User identifier
 * @param array $user_data Profile information
 * @param string $action Current action (Example: 'add')
 * @return int profile ID
 */
function fn_update_user_profile($user_id, $user_data, $action = '')
{
    // Add new profile or update existing
    if ((isset($user_data['profile_id']) && empty($user_data['profile_id'])) || $action == 'add') {
        if ($action == 'add') {
            unset($user_data['profile_id']);

            $user_data['profile_type'] = 'P';
            $user_data['profile_name'] = empty($user_data['profile_name']) ? __('main') : $user_data['profile_name'];
        } else {
            $user_data['profile_type'] = 'S';
        }

        $user_data['profile_id'] = db_query("INSERT INTO ?:user_profiles ?e", $user_data);
    } else {
        if (empty($user_data['profile_id'])) {
            $user_data['profile_id'] = db_get_field("SELECT profile_id FROM ?:user_profiles WHERE user_id = ?i AND profile_type = 'P'", $user_id);
        }
        $is_exists = db_get_field('SELECT COUNT(*) FROM ?:user_profiles WHERE user_id = ?i AND profile_id = ?i', $user_id, $user_data['profile_id']);

        if ($user_data['b_address_id'] === '') {
            $user_data['b_address_id'] = null;
        }
        if ($user_data['s_address_id'] === '') {
            $user_data['s_address_id'] = null;
        }

        if ($is_exists) {
            db_query("UPDATE ?:user_profiles SET ?u WHERE profile_id = ?i", $user_data, $user_data['profile_id']);
        } else {
            $user_data['profile_id'] = db_query("INSERT INTO ?:user_profiles ?e", $user_data);
        }
    }

    // Add/Update additional fields
    fn_store_profile_fields($user_data, array('U' => $user_id, 'P' => $user_data['profile_id']), 'UP');

    // Change email in orders table when the profile is updated.
    db_query("UPDATE ?:orders SET email = ?s WHERE user_id = ?i", $user_data['email'], $user_id);

    return $user_data['profile_id'];
}

/**
 * Deletes secondary user profile
 *
 * @param int $user_id User identifier
 * @param int $profile_id User profile identifier
 * @return bool True on success, false otherwise
 */
function fn_delete_user_profile($user_id, $profile_id)
{
    $can_delete = db_get_field(
        "SELECT profile_id FROM ?:user_profiles WHERE user_id = ?i AND profile_id = ?i AND profile_type = 'S'",
        $user_id, $profile_id
    );

    if (!empty($can_delete)) {
        db_query("DELETE FROM ?:user_profiles WHERE profile_id = ?i", $profile_id);
    }

    return !empty($can_delete);
}

/**
 * Check if user login/email equal to customer password
 *
 * @param array $user_data user data
 * @param string $password user password
 * @return boolean
 */
function fn_compare_login_password($user_data, $password)
{
    if (Registry::get('settings.General.use_email_as_login') == 'Y') {
        $account = !empty($user_data['email']) ? $user_data['email'] : '';
    } else {
        $account = !empty($user_data['user_login']) ? $user_data['user_login'] : '';
    }

    $result = $password == $account ? true : false;
    return $result;
}

/**
 * Check if specified account needs login
 *
 * @param string $user_type - user account type
 * @return bool true if needs login, false otherwise
 */
function fn_user_need_login($user_type)
{
    $types = array(
        'A',
        'C',
        'V'
    );

    return in_array($user_type, $types);
}

/**
 * Check compatible user types
 *
 * @param array $user_data - new user data
 * @param array $current_user_data - current user data
 * @return char user type
 */
function fn_check_user_type($user_data, $current_user_data)
{
    $currentType = $current_user_data['user_type'];
    $requestedType = !empty($user_data['user_type']) ? $user_data['user_type'] : $currentType;

    if (AREA === 'A'
        || (AREA === null && container()->get('security.authorization_checker')->isGranted('ROLE_ADMIN'))
    ) {
        return $requestedType;
    }

    return $currentType;
}

function fn_check_user_type_admin_area($user_type = '')
{
    if (is_array($user_type)) {
        $user_type = !empty($user_type['user_type']) ? $user_type['user_type'] : '';
    }

    return ($user_type == 'A' || $user_type == 'V') ? true : false;
}

/**
 * Filter hidden fields, which were hidden to checkout
 *
 * @param array $user_data - user data
 * @param char $location - flag for including data
 * @return array filtered fields
 */
function fn_filter_hidden_profile_fields(&$user_data, $location)
{
    $condition = "WHERE 1 ";

    if ($location == 'O') {
        $condition .= " AND ?:profile_fields.checkout_show = 'N'";
    }

    $filtered = db_get_array("SELECT ?:profile_fields.field_name FROM ?:profile_fields $condition");
    foreach ($filtered as $field) {
        if (!empty($field['field_name'])) {
            /* Workaround for 'email' field */
            if ($field['field_name'] == 'email') {
                continue;
            }

            unset($user_data[$field['field_name']]);
        }
    }

    return $filtered;
}

function fn_check_profile_fields_population($user_data, $section, $profile_fields)
{
    // If this section does not have fields, assume it's filled
    // or if we're checking shipping/billing section and shipping/billing address does not differ from billing/shipping, assume that fields filled correctly
    if ($section == 'B') {
        $check_section = 'S';
    } else {
        $check_section = 'B';
    }

    if (empty($profile_fields[$section]) || ($section == $check_section && fn_check_shipping_billing($user_data, $profile_fields) == false)) {
        return true;
    }

    foreach ($profile_fields[$section] as $field) {
        if ($field['required'] == 'Y' && ((!empty($field['field_name']) && empty($user_data[$field['field_name']])) || (empty($field['field_name']) && empty($user_data['fields'][$field['field_id']])))) {
            return false;
        }
    }

    return true;
}

/**
 * Checks is it possible or not to delete user
 *
 * @param array $user_data Array with user data (should contain at least user_id, is_root and company_id fields)
 * @param array $auth Array with authorization data
 * @return bool True if user can be deleted, false otherwise.
 */
function fn_check_rights_delete_user($user_data, $auth)
{
    $result = true;

    if (
        ($user_data['is_root'] == 'Y' && !$user_data['company_id']) // root admin
        || fn_is_restricted_admin($user_data) // have no rights to delete user
        || (!empty($auth['user_id']) && $auth['user_id'] == $user_data['user_id']) // trying to delete himself
        || (Registry::get('runtime.company_id') && $user_data['is_root'] == 'Y') // vendor root admin
    ) {
        $result = false;
    }

    return $result;
}

/**
 * Deletes user and all related data
 *
 * @param int $user_id User identification
 *
 * @return boolean False, if user can not be deleted, true if user was successfully deleted
 */
function fn_delete_user(int $user_id): bool
{
    $auth = $_SESSION['auth'];

    $user = container()->get('marketplace.user.user_service')->get($user_id);
    $userLogged = container()->get('marketplace.user.user_service')->get($auth['user_id']);
    if ($user->isMarketplaceAdministrator() === true && $userLogged->isMarketplaceSupportAdministrator() === false) {
        fn_set_notification('W', __('warning'), __('admin_cannot_be_deleted', array(
            '[user_id]' => $user_id
        )));

        return false;
    }
    //Get orders list associate to this user
    $params = ['user_id' => $user_id];
    [$orders] = fn_get_orders($params, Registry::get('settings.Appearance.admin_orders_per_page'), true);

    //User can't be deleted if orders have been created
    if (\count($orders) > 0) {
        fn_set_notification('W', __('warning'), __('user_cannot_be_deleted', array(
            '[user_id]' => $user_id
        )));

        return false;
    }

    if ($user->belongsToAnOrganisation() === true) {
        fn_set_notification('W', __('warning'), __('organisation_user_cannot_be_deleted', [
            '[user_id]' => $user_id
        ]));

        return false;
    }

    $condition = fn_get_company_condition('?:users.company_id');
    $user_data = db_get_row("SELECT user_id, is_root, company_id FROM ?:users WHERE user_id = ?i $condition", $user_id);

    //User can not be deleted if he is the single admin of a vendor enabled
    $company = new Company($user_data['company_id']);
    $admins = $company->getAdmins();
    //Check if admin is not single and if all other admins are not disabled
    $adminCount = count($admins);
    $disabledAdminCount = count($company->getDisabledAdmins());
    if (($adminCount - $disabledAdminCount) <= 1) {
        fn_set_notification('E', __('error'), __('single_admin_cannot_be_deleted', array(
            '[user_id]' => $user_id
        )));

        return false;
    }

    if (empty($user_data) || !fn_check_rights_delete_user($user_data, $auth)) {
        fn_set_notification('W', __('warning'), __('user_cannot_be_deleted', array(
            '[user_id]' => $user_id
        )));

        return false;
    }

    // Log user deletion
    fn_log_event('users', 'delete', array (
        'user_id' => $user_id,
        'company_id' => fn_get_company_id('users', 'user_id', $user_id),
    ));

    if ($user_data['is_root'] == 'Y') {
        db_query("UPDATE ?:users SET is_root = 'Y' WHERE company_id = ?i LIMIT 1", $user_data['company_id']);
    }

    try {
        db_query("DELETE FROM ?:users WHERE user_id = ?i", $user_id);
    } catch (\Tygh\DatabaseForeignKeyConstraintException $e) {
        fn_set_notification(
            'W',
            __('warning'),
            __(
                'user_cannot_be_deleted',
                ['[user_id]' => $user_id]
            )
        );

        return false;
    }

    db_query("DELETE FROM ?:user_profiles WHERE user_id = ?i", $user_id);
    db_query("DELETE FROM ?:user_session_products WHERE user_id = ?i", $user_id);
    db_query("DELETE FROM ?:user_data WHERE user_id = ?i", $user_id);
    db_query("UPDATE ?:orders SET user_id = 0 WHERE user_id = ?i", $user_id);

    return true;
}

/**
 * Log in user using only user id
 * return 0 - we can't find user with provided user_id
 * return 1 - user was successfully loggined
 * return 2 - user disabled
 *
 */

function fn_login_user($user_id = '')
{
    $udata = array();
    $auth = & $_SESSION['auth'];
    $condition = '';
    if (!empty($user_id)) {
        $udata = db_get_row("SELECT * FROM ?:users WHERE user_id = ?i AND status = 'A'" . $condition, $user_id);
        if (empty($udata)) {
            $udata = db_get_row("SELECT * FROM ?:users WHERE user_id = ?i AND user_type IN ('A', 'V', 'P')", $user_id);
        }

        unset($_SESSION['status']);

        $auth = fn_fill_auth($udata, isset($auth['order_ids']) ? $auth['order_ids'] : array());
        if (!empty($udata)) {
            if (AREA == 'C') {
                if ($cu_id = fn_get_session_data('cu_id')) {
                    fn_clear_cart($cart);
                    // If a cart exist when user log, remove old cart saved in database
                    if (db_get_field("SELECT product_id FROM ?:user_session_products WHERE user_id = ?i LIMIT 1", $cu_id)) {
                        if ($user_id) {
                            db_query("DELETE FROM ?:user_session_products WHERE user_id = ?i", $user_id);
                        }
                    }
                    fn_save_cart_content($cart, $cu_id, 'C', 'U');
                    fn_delete_session_data('cu_id');
                }
                fn_init_user_session_data($_SESSION, $udata['user_id']);
            }

            if ($session = container()->get('session')) {
                \Wizacha\Product::createFromSession($session);
            }

            return LOGIN_STATUS_OK;
        } else {
            return LOGIN_STATUS_USER_DISABLED;
        }
    } else {
        $auth = fn_fill_auth($udata, isset($auth['order_ids']) ? $auth['order_ids'] : array());

        return LOGIN_STATUS_USER_NOT_FOUND;
    }
}

function fn_check_permission_act_as_user()
{
    return !empty($_SESSION['auth']['user_id']) && !empty($_REQUEST['user_id']) && $_SESSION['auth']['user_id'] == $_REQUEST['user_id'] ? true : false;
}

/**
 * Checks if administrator can mange profiles
 *
 * @param char $user_type Type of profiles
 * @return boolean Flag: indicates if administrator has the permission to manage profiles
 */
function fn_check_permission_manage_profiles($user_type)
{
    $result = true;

    $params = array (
        'user_type' => $user_type
    );
    $result = $result && !fn_is_restricted_admin($params);

    if (Registry::get('runtime.company_id') && $result) {
        $result = ($user_type == 'V' && Registry::get('runtime.company_id'));
    }
    return $result;
}

/**
 * Nouvelle génération de mot de passe utilisant l'API native de PHP,
 * utilisée pour toutes les créations à partir de maintenant,
 * car il n'y a aucune raison de générer un mot de passe à l'ancienne.
 */
function fn_encode_password(string $clearPassword) : string
{
    return password_hash($clearPassword, PASSWORD_DEFAULT);
}

/**
 * Nouvelle fonction de vérification de mot de passe,
 * devant faire la différence entre les 2 systèmes de mot de passe.
 */
function fn_verify_password(string $clearPassword, string $salt, string $hashedPassword): bool
{
    // Si nouveau système
    if (fn_is_new_password($hashedPassword)) {
        return password_verify($clearPassword, $hashedPassword);
    }

    // Si ancien système
    if (strlen($salt) === 0) {
        $authenticated = $hashedPassword === md5($clearPassword);
    } else {
        // The first combination is the legacy one (with salt), the second one is used with Magento hash migration.
        $authenticated = $hashedPassword === md5(md5($clearPassword) . md5($salt))
            || $hashedPassword === md5($salt . $clearPassword)
        ;
    }

    // If the password is right, we temporarily store the password in the request to migrate it to a proper algorithm.
    if ($authenticated) {
        container()->get('request_stack')->getCurrentRequest()->request->set(
            SecurityConstants::LEGACY_PASSWORD,
            $clearPassword
        );
    }

    return $authenticated;
}

/**
 * Détecte si le password encodé apparait dans la colonne gérant les nouveaux passwords
 */
function fn_is_new_password(string $hashedPassword) : bool
{
    $passwordInfo = password_get_info($hashedPassword);

    if (
        (
            0 === $passwordInfo['algo']
            || null === $passwordInfo['algo']
        )
        && 'unknown' === $passwordInfo['algoName']
    ) {
        return false;
    }

    return true;
}

function fn_get_my_account_title_class()
{
    return !empty($_SESSION['auth']['user_id']) ? 'logged' : 'unlogged';
}

/**
 * La question à laquelle répond cette méthode est : "Doit-on empecher l'utilisateur dont l'id est $user_id (0 si on est en création)
 *  de prendre l'email renseigné dans $user_data."
 * On doit l'en empecher si un autre utilisateur ou une company autre que la sienne posséde déjà l'email.
 *
 * @param int $user_id User identifier to ignore
 * @param array $user_data User data
 * @return bool true si l'adresse est déjà utilisé par un autre compte
 */
function fn_is_user_exists($user_id, $user_data): bool
{
    $condition = db_quote(" (?p ?p) ", (!empty($user_data['email']) ? db_quote('email = ?s', $user_data['email']) : '0'), (empty($user_data['user_login']) ? '' : db_quote(" OR user_login = ?s", $user_data['user_login'])));
    $condition .= db_quote(" AND user_id != ?i", $user_id);

    // La query qui check si un autre utilisateur utilise l'email
    $query = "SELECT 1 FROM ?:users WHERE $condition";

    if (!empty($user_data['email'])) {
        //Check si une company utilise l'email
        $query .= db_quote(" UNION SELECT 1 FROM ?:companies");
        $join='';
        $condition = db_quote(" WHERE ?:companies.email = ?s", $user_data['email']);

        //Si on teste un utilisateur existant il peut utiliser l'email si c'est celui de sa company
        if($user_id) {
            $join .= db_quote(" LEFT JOIN ?:users ON ?:users.company_id = ?:companies.company_id");
            $condition .= db_quote(" AND ?:users.user_id != ?i", $user_id);
        }

        // C'est laissé au cas où ce serait utilisé dans le backoffice. Cas où on crée directement un user avec une company
        if (!empty($user_data['company_id'])) {
            $condition .= db_quote(" AND ?:companies.company_id != ?i", $user_data['company_id']);
        }
        $query .= $join.$condition;
    }

    return (bool) db_get_field("SELECT EXISTS($query)");
}

/**
 * Get profile field value for printing
 *
 * @param array $profile Profile data
 * @param array $field Field data
 * @return string Field value
 */
function fn_get_profile_field_value($profile, $field)
{
    $data_id = $field['field_name'];
    $value = !empty($profile[$data_id]) ? $profile[$data_id] : '';

    if (!empty($value)) {
        if (strpos("AO", $field['field_type']) !== false) {
            // States/Countries
            $title = $data_id . '_descr';
            $value = !empty($profile[$title]) ? $profile[$title] : '-';
        } elseif ($field['field_type'] == "C") {
            // Checkbox
            $value = ($value == "Y") ? __('yes') : __('no');
        } elseif ($field['field_type'] == "D") {
            // Date
            $value = fn_date_format($value, Registry::get('settings.Appearance.date_format'));
        } elseif (strpos("RS", $field['field_type']) !== false) {
            // Selectbox/Radio
            $value = $field['values'][$value];
        }
    }

    return $value;
}

/**
 * Gets user type from request parameters
 *
 * @param array $params Request parameters
 * @param string $area current application area
 * @return char User type
 */
function fn_get_request_user_type($params, $area = AREA)
{
    $user_type = '';

    if (!empty($params['user_type'])) {
        $user_type =  $params['user_type'];
    } elseif (!empty($params['user_id'])) {
        $user_type = db_get_field("SELECT user_type FROM ?:users WHERE user_id = ?i", $params['user_id']);
    }

    // Use customer user type by default
    $user_type = !empty($user_type) ? $user_type : 'C';

    if ($area == 'A' && empty($params['user_type']) && empty($params['user_id']) && Registry::get('runtime.company_id')) {
        $user_type = 'V';
    }
    return $user_type;
}

function fn_auth_routines(Request $request, string $userLogin)
{
    // authLog
    $authLogRepository = container()->get('marketplace.authlog.repository');
    $authenticatorThrottling = container()->get('app.authenticator_throttling');

    $status = true;

    $user_login = (!empty($userLogin)) ? $userLogin : '';
    $password = (!empty($_POST['password'])) ? $_POST['password']: '';
    $field = (Registry::get('settings.General.use_email_as_login') == 'Y') ? 'email' : 'user_login';

    $condition = '';

    // Check throttling
    $timeToWait = $authenticatorThrottling->checkThrottling($userLogin);
    if ($timeToWait > 0) {
        fn_set_notification('E', __('error'), __('authenticator_throttling', [
            '%waitingTime%' => $timeToWait
        ]));

        return [false, [], [], [], ''];
    }

    $user_data = db_get_row("SELECT * FROM ?:users WHERE $field = ?s" . $condition, $user_login);

    if (empty($user_data)) {
        $user_data = db_get_row("SELECT * FROM ?:users WHERE $field = ?s AND user_type IN ('A', 'V', 'P')", $user_login);
    }

    if (!empty($user_data) && (!fn_check_user_type_admin_area($user_data) && AREA == 'A' || !fn_check_user_type_access_rules($user_data))) {
        fn_set_notification('E', __('error'), __('error_area_access_denied'));

        // AuthLog
        $authLogRepository->save(
            $user_login,
            StatusType::ACCESS_DENIED(),
            SourceType::BASIC(),
            DestinationType::mapLegacy(ACCOUNT_TYPE)
        );

        $status = false;
    }

    if (!empty($user_data['status']) && $user_data['status'] == 'D') {
        fn_set_notification('E', __('error'), __('error_account_disabled'));

        // AuthLog
        $authLogRepository->save(
            $user_login,
            StatusType::DESACTIVATED_ACCOUNT(),
            SourceType::BASIC(),
            DestinationType::mapLegacy(ACCOUNT_TYPE)
        );

        $status = false;
    }

    if (!empty($user_data['status']) && $user_data['status'] == \Wizacha\Status::PENDING) {
        fn_set_notification('E', __('error'), __('error_account_pending'));

        // AuthLog
        $authLogRepository->save(
            $user_login,
            StatusType::PENDING_ACCOUNT(),
            SourceType::BASIC(),
            DestinationType::mapLegacy(ACCOUNT_TYPE)
        );

        $status = false;
    }

    $salt = isset($user_data['salt']) ? $user_data['salt'] : '';

    return array($status, $user_data, $user_login, $password, $salt);
}

function fn_fill_contact_info_from_address($data)
{
    $profile_fields = fn_get_profile_fields('O');

    container()->getParameter('checkout.address.billing_first') && !empty($profile_fields['B']) ? $address_zone = 'b' : $address_zone = 's';

    if (!empty($data['firstname']) || !empty($data[$address_zone . '_firstname'])) {
        $data['firstname'] = empty($data['firstname']) && !empty($data[$address_zone . '_firstname']) ? $data[$address_zone . '_firstname'] : $data['firstname'];
    }

    if (!empty($data['lastname']) || !empty($data[$address_zone . '_lastname'])) {
        $data['lastname'] = empty($data['lastname']) && !empty($data[$address_zone . '_lastname']) ? $data[$address_zone . '_lastname'] : $data['lastname'];
    }

    if (!empty($data['phone']) || !empty($data[$address_zone . '_phone'])) {
        $data['phone'] = empty($data['phone']) && !empty($data[$address_zone . '_phone']) ? $data[$address_zone . '_phone'] : $data['phone'];
    }

    return $data;
}

/**
 * Checks if user data are complete
 *
 * @param array $user_data Array of user data @see fn_get_user_info
 * @param string $location Location identifier
 * @param array $auth - authentication information
 * @return bool True on success, false otherwise
 */
function fn_check_profile_fields($user_data, $location = 'C', $auth = array())
{
    $result = true;
    $profile_fields = fn_get_profile_fields($location, $auth);
    if ($location == 'O') {
        unset($profile_fields['E']);
    }

    foreach ($profile_fields as $section => $fields) {
        if (!fn_check_profile_fields_population($user_data, $section, $profile_fields)) {
            $result = false;
            break;
        }
    }

    return $result;
}

/**
 * Update or create profile field. Also can create fields matching.
 *
 * @param array $field_data Array of profile field data
 * @param int $field_id Profile field id to be updated. If empty - new field will be created
 * @param strging $lang_code 2-letters language code
 *
 * @return int $field_id New or updated field id
 */
function fn_update_profile_field($field_data, $field_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    if (empty($field_id)) {

        $add_match = false;

        $field_name = $field_data['field_name'];
        if ($field_data['section'] == 'BS') {
            $field_data['section'] = 'B';
            $field_data['field_name'] = !empty($field_name) ? ('b_' . $field_name) : '';
            $add_match = true;
        }

        // Insert main data
        $field_id = db_query("INSERT INTO ?:profile_fields ?e", $field_data);

        // Insert descriptions
        $_data = array (
            'object_id' => $field_id,
            'object_type' => 'F',
            'description' => $field_data['description'],
        );

        \Tygh\Languages\Helper::insertTranslations('profile_field_descriptions', $lang_code, $_data);

        if (substr_count('SR', $field_data['field_type']) && is_array($field_data['add_values']) && $add_match == false) {
            fn_add_field_values($field_data['add_values'], $field_id);
        }

        if ($add_match == true) {
            $field_data['section'] = 'S';
            $field_data['field_name'] = !empty($field_name) ? ('s_' . $field_name) : '';
            $field_data['matching_id'] = $field_id;

            // Update match for the billing field
            $s_field_id = fn_update_profile_field($field_data, 0, $lang_code);
            if (!empty($s_field_id)) {
                db_query('UPDATE ?:profile_fields SET matching_id = ?i WHERE field_id = ?i', $s_field_id, $field_id);
            }
        }

    } else {
        db_query("UPDATE ?:profile_fields SET ?u WHERE field_id = ?i", $field_data, $field_id);

        if (!empty($field_data['matching_id']) && $field_data['section'] == 'S') {
            db_query('UPDATE ?:profile_fields SET field_type = ?s WHERE field_id = ?i', $field_data['field_type'], $field_data['matching_id']);
        }

        db_query("UPDATE ?:profile_field_descriptions SET ?u WHERE object_id = ?i AND object_type = 'F' AND lang_code = ?s", $field_data, $field_id, $lang_code);

        if (!empty($field_data['field_type'])) {
            if (strpos('SR', $field_data['field_type']) !== false) {
                if (!empty($field_data['values'])) {
                    foreach ($field_data['values'] as $value_id => $vdata) {
                        db_query("UPDATE ?:profile_field_values SET ?u WHERE value_id = ?i", $vdata, $value_id);
                        db_query("UPDATE ?:profile_field_descriptions SET ?u WHERE object_id = ?i AND object_type = 'V' AND lang_code = ?s", $vdata, $value_id, $lang_code);
                    }

                    // Completely delete removed values
                    $existing_ids = db_get_fields("SELECT value_id FROM ?:profile_field_values WHERE field_id = ?i", $field_id);
                    $val_ids = array_diff($existing_ids, array_keys($field_data['values']));

                    if (!empty($val_ids)) {
                        fn_delete_field_values($field_id, $val_ids);
                    }
                } else {
                   if (isset($field_data['add_values'])) {
                        fn_delete_field_values($field_id);
                    }
                }

                if (!empty($field_data['add_values']) && is_array($field_data['add_values'])) {
                    fn_add_field_values($field_data['add_values'], $field_id);
                }
            } else {
                fn_delete_field_values($field_id);
            }
        }
    }

    return $field_id;
}

function fn_update_user_status_and_notify(string $statusTo, ?string $statusFrom, int $userId, string $userEmail, array $request): void
{
    if ($userId !== 1) {
        /** @var \PDOStatement $result */
        $result = db_query("UPDATE ?:users SET status = ?s WHERE user_id = ?i", $statusTo, $userId);

        if ($result->rowCount() > 0) {
            fn_set_notification('N', __('notice'), __('status_changed'));

            $force_notification = fn_get_notification_rules($request);
            if ($force_notification['C']
                && $statusTo === Status::ENABLED
                && in_array($statusFrom, [Status::DISABLED, Status::PENDING])
            ) {
                container()->get('event_dispatcher')->dispatch(
                    new UserProfileActivated(
                        container()->get('marketplace.user.user_repository')->findOneByEmail($userEmail)
                    ),
                    UserProfileActivated::class
                );
            }
        }
    }
}

function fn_can_update_user_status(Company $company, ?string $statusFrom, ?string $statusTo): bool
{
    // If companyId === 0, it's a user, we can update the status. If status is not changed, we can update
    if ($company->getId() === 0 || $statusFrom === $statusTo) {
        return true;
    }

    $adminsCount = count($company->getAdmins());
    $disabledAdminsCount = count($company->getDisabledAdmins());

    //Check that admin is not the only one
    //(or at least one in the case which he is disabled) and if all others admins are not disabled

    return ((($adminsCount - $disabledAdminsCount) > 1) && $statusFrom === Status::ENABLED)
        // If we want to enable, we allow to update
        || ($statusTo === Status::ENABLED);
}
