<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Languages\Languages;
use Tygh\Registry;
use Tygh\Settings;
use Wizacha\CloudImageManager;
use Wizacha\Marketplace\GlobalState\GlobalState;

//
// Get image
//
function fn_get_image($image_id, $object_type, $lang_code = null, $get_all_alts = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();

    if (!empty($image_id) && !empty($object_type)) {
        $image_data = db_get_row("SELECT ?:images.image_id, ?:images.image_path, ?:common_descriptions.description as alt, ?:images.image_x, ?:images.image_y FROM ?:images LEFT JOIN ?:common_descriptions ON ?:common_descriptions.object_id = ?:images.image_id AND ?:common_descriptions.object_holder = 'images' AND ?:common_descriptions.lang_code = ?s  WHERE ?:images.image_id = ?i", $lang_code, $image_id);
        if ($get_all_alts && count(Languages::getAll()) > 1) {
            $image_data['alt'] = db_get_hash_single_array('SELECT description, lang_code FROM ?:common_descriptions WHERE object_id = ?i AND object_holder = ?s', array('lang_code', 'description'), $image_data['image_id'], 'images');
        }
    }

    fn_attach_absolute_image_paths($image_data, $object_type);

    return (!empty($image_data) ? $image_data : false);
}

//
// Attach image paths
//
function fn_attach_absolute_image_paths(&$image_data, $object_type)
{
    $image_id = !empty($image_data['images_image_id'])? $image_data['images_image_id'] : $image_data['image_id'];
    $path = $object_type . '/' . floor($image_id / MAX_FILES_IN_DIR);

    if (!empty($image_data['image_path'])) {
        $image_name = $image_data['image_path'];
        $image_data['relative_path'] = $path . '/' . $image_name;
        $imagesStorageService = container()->get('Wizacha\Storage\ImagesStorageService');
        $image_data['http_image_path'] = $imagesStorageService->getUrl($path . '/' . $image_name, 'http');
        $image_data['absolute_path'] =$imagesStorageService->getAbsolutePath($path . '/' . $image_name);
        $image_data['image_path'] = $imagesStorageService->getUrl($path . '/' . $image_name);
    }

    return $image_data;
}

/**
 * Copy image to final storage, create thumbnails, update DB, remove temp file
 * Images are immutable, each time we have a new content, we have a new ID
 * The old $image_id is remove from storage and DB
 *
 * @param mixed $image_data Array with image data
 * @param int $image_id Image ID
 * @param string $image_type Type (object) of image (may be product, category, and so on)
 * @param string $lang_code 2 letters language code
 * @return int Updated or inserted image ID. False on failure.
 * @internal called by fn_update_image_pairs
 */
function fn_update_image($image_data, $image_id = 0, $image_type = 'product', $lang_code = null)
{
    $imagesStorageService = container()->get('Wizacha\Storage\ImagesStorageService');
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $image_data['name'] = \Wizacha\ImageManager::escapeName($image_data['name']);
    $extension = pathinfo($image_data['name'])['extension'];

    $images_path = $image_type . '/';
    $_data = array();

    if (empty($image_id)) {
        $max_id = db_get_next_auto_increment_id('images');
        $img_id_subdir = floor($max_id / MAX_FILES_IN_DIR) . "/";
    } else {
        $img_id_subdir = floor($image_id / MAX_FILES_IN_DIR) . "/";
    }
    $images_path .= $img_id_subdir;

    list($_data['image_x'], $_data['image_y'], $mime_type) = fn_get_image_size($image_data['path']);

    if (!\Wizacha\ImageManager::isSizeCorrect($_data['image_x'], $_data['image_y'])) return;

    // Get the real image type
    $ext = fn_get_image_extension($mime_type);
    if (strpos($image_data['name'], '.') === false) {
        $extension = $ext;
        $image_data['name'] .= '.' . $ext;
    }

    // Check if image path already set
    $image_path = db_get_field("SELECT image_path FROM ?:images WHERE image_id = ?i", $image_id);

    // Delete existing image
    if (!empty($image_path)) {
        $imagesStorageService->delete($images_path . $image_path);
        // Clear all existing thumbnails
        fn_delete_image_thumbnails($images_path . $image_path);
    }

    $params = array(
        'file' => $image_data['path'],
    );

    if (!empty($image_data['params'])) {
        $params = fn_array_merge($params, $image_data['params']);
    }

    // Rotate the original jpg file (if the exif data contains orientation flag)
    if ($image_type == 'detailed' && $ext == 'jpg') {
        $tmpImage = fn_imagecreatefromjpeg($image_data['path'], $isRotated);
        // If the image has been rotated of 90 or -90 deg, swap the width and height vars
        if (imagesx($tmpImage) != $_data['image_x']) {
            list($_data['image_x'], $_data['image_y']) = [$_data['image_y'], $_data['image_x']];
        }
        if ($isRotated) {
            imagejpeg($tmpImage, $image_data['path'], 90);
            imagedestroy($tmpImage);
        }
    }

    //Prepare migration of images : duplicate images in originals/{id/1000}/id.jpg
    $newData = $_data;

    $params['keep_origins'] = true; //Else, the file is deleted after the put and can't be duplicate in new location

    list($_data['image_size'], $_data['image_path']) = $imagesStorageService->put($images_path . $image_data['name'], $params);

    $_data['image_path'] = fn_basename($_data['image_path']); // we need to store file name only

    $old_id = $image_id;

    try {
        db_query('BEGIN');

        // On supprime l'ancienne image en cas de mise à jour
        if (!empty($old_id)) {
            db_query("DELETE FROM ?:images_seo WHERE image_id = ?i", $old_id);
            db_query('DELETE FROM ?:images WHERE image_id = ?i', $old_id);
        }

        $_data['image_id'] = $image_id;
        $image_id = db_query("REPLACE INTO ?:images ?e", $_data);

        db_query('COMMIT');
        fn_w_invalidate_cloudimage_cache($image_id);
    } catch (\Exception $ex) {
        db_query('ROLLBACK');
        throw $ex;
    }

    //Put image on new path
    $new_location = 'originals/'.$img_id_subdir. $image_id.'.'.$extension;
    $params['overwrite'] = true;
    $params['keep_origins'] = false; //Delete the temporary file like in old functionnement
    fn_delete_image_thumbnails($images_path . $image_path);

    $imagesStorageService->put($new_location, $params);

    return $image_id;
}

//CsCart 4.1.5
function fn_get_count_image_link($image_id)
{
    return db_get_field("SELECT COUNT(*) FROM ?:images_links WHERE image_id = ?i OR detailed_id = ?i", $image_id, $image_id);
}

//
// Delete image
//
function fn_delete_image($image_id, $pair_id, $object_type = 'product')
{
    if (!\Wizacha\ImageManager::isPairDeletable($pair_id, AREA, \Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance()))) {
        return false;
    }

    $_image_file = db_get_field("SELECT image_path FROM ?:images WHERE image_id = ?i", $image_id);
    $extension = pathinfo($_image_file)['extension'];
    if (empty($_image_file)) {
        return false;
    }

    db_query("UPDATE ?:images_links SET " . ($object_type == 'detailed' ? 'detailed_id' : 'image_id') . " = '0' WHERE pair_id = ?i", $pair_id);
    $_ids = db_get_row("SELECT image_id, detailed_id FROM ?:images_links WHERE pair_id = ?i", $pair_id);

    if (empty($_ids['image_id']) && empty($_ids['detailed_id'])) {
        db_query("DELETE FROM ?:images_links WHERE pair_id = ?i", $pair_id);
    }

    if (fn_get_count_image_link($image_id) == 0) {

        $img_id_subdir = floor($image_id / MAX_FILES_IN_DIR);
        $_image_file = $object_type . '/' . $img_id_subdir . '/' . $_image_file;

        $imagesStorageService = container()->get('Wizacha\Storage\ImagesStorageService');

        $imagesStorageService->delete($_image_file);
        $imagesStorageService->delete('originals/'.$img_id_subdir."/{$image_id}.{$extension}");

        db_query("DELETE FROM ?:images_seo WHERE image_id = ?i", $image_id);
        db_query("DELETE FROM ?:images WHERE image_id = ?i", $image_id);
        db_query("DELETE FROM ?:common_descriptions WHERE object_id = ?i AND object_holder = 'images'", $image_id);

        // Clear all existing thumbnails
        fn_delete_image_thumbnails($_image_file);

        fn_w_invalidate_cloudimage_cache($image_id);
    }

    return true;
}

/**
 * Deletes all thumbnails of specified file
 *
 * @param string $filename file name
 * @param string $prefix path prefix
 * @return boolean always true
 */
function fn_delete_image_thumbnails($filename, $prefix = '')
{
    if (!empty($filename)) {
        $imagesStorageService = container()->get('Wizacha\Storage\ImagesStorageService');
        $imagesStorageService->deleteDir($prefix . \Wizacha\ImageManager::thumbnailsRootPath($filename));
    }

    return true;
}

//
// Get image pair(s)
//
function fn_get_image_pairs($object_ids, $object_type, $pair_type, $get_icon = true, $get_detailed = true, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $icon_pairs = $detailed_pairs = $pairs_data = array();
    if (is_array($object_ids)) {
        if (empty($object_ids)) {
            return [];
        } else {
            $arrayCond = implode(',', array_map(function ($id) {return db_quote('?s', $id);}, $object_ids));
            $cond = db_quote("AND ?:images_links.object_id IN ($arrayCond)");
        }
    } else {
        $cond = db_quote("AND ?:images_links.object_id = ?s", $object_ids);
    }

    if ($get_icon == true || $get_detailed == true) {
        if ($get_icon == true) {
            $join_cond = "?:images_links.image_id = ?:images.image_id";
            $icon_pairs = db_get_memoized_array(
                    "SELECT ?:images_links.*, ?:images.image_path, ?:common_descriptions.description AS alt, ?:images.image_x, ?:images.image_y, ?:images.image_id as images_image_id"
                    . " FROM ?:images_links"
                    . " LEFT JOIN ?:images ON $join_cond"
                    . " LEFT JOIN ?:common_descriptions ON ?:common_descriptions.object_id = ?:images.image_id AND ?:common_descriptions.object_holder = 'images' AND ?:common_descriptions.lang_code = ?s"
                    . " WHERE ?:images_links.object_type = ?s AND ?:images_links.type = ?s $cond"
                    . " ORDER BY ?:images_links.position, ?:images_links.detailed_id",
                    $lang_code, $object_type, $pair_type
                );
        }

        if ($get_detailed == true) {
            $join_cond = db_quote("?:images_links.detailed_id = ?:images.image_id");
            $detailed_pairs = db_get_memoized_array(
                    "SELECT ?:images_links.*, ?:images.image_path, ?:common_descriptions.description AS alt, ?:images.image_x, ?:images.image_y, ?:images.image_id as images_image_id"
                    . " FROM ?:images_links"
                    . " LEFT JOIN ?:images ON $join_cond"
                    . " LEFT JOIN ?:common_descriptions ON ?:common_descriptions.object_id = ?:images.image_id AND ?:common_descriptions.object_holder = 'images' AND ?:common_descriptions.lang_code = ?s"
                    . " WHERE ?:images_links.object_type = ?s AND ?:images_links.type = ?s $cond"
                    . " ORDER BY ?:images_links.position, ?:images_links.pair_id",
                    $lang_code, $object_type, $pair_type
                );
        }

        foreach ((array) $object_ids as $object_id) {
            $pairs_data[$object_id] = array();
        }

        // Convert the received data to the standard format in order to keep the backward compatibility
        foreach ($icon_pairs as $pair) {
            $_pair = array(
                'pair_id' => $pair['pair_id'],
                'image_id' => $pair['image_id'],
                'detailed_id' => $pair['detailed_id'],
                'position' => $pair['position'],
            );

            if (!empty($pair['images_image_id'])) { //get icon data if exist
                $icon = fn_attach_absolute_image_paths($pair, $object_type);

                $_pair['icon'] = array(
                    'image_path' => $icon['image_path'],
                    'alt' => $icon['alt'],
                    'image_x' => $icon['image_x'],
                    'image_y' => $icon['image_y'],
                    'http_image_path' => $icon['http_image_path'],
                    'absolute_path' => $icon['absolute_path'],
                    'relative_path' => $icon['relative_path'],
                    'position' => (int) $icon['position'],
                );
            }

            $pairs_data[$pair['object_id']][$pair['pair_id']] = $_pair;
        }// -foreach icon_pairs

        foreach ($detailed_pairs as $pair) {
            $pair_id = $pair['pair_id'];
            $object_id = $pair['object_id'];

            if (!empty($pairs_data[$object_id][$pair_id]['detailed_id'])) {
                $detailed = fn_attach_absolute_image_paths($pair, 'detailed');
                $pairs_data[$object_id][$pair_id]['detailed'] = array(
                    'image_path' => $detailed['image_path'],
                    'alt' => $detailed['alt'],
                    'image_x' => $detailed['image_x'],
                    'image_y' => $detailed['image_y'],
                    'http_image_path' => $detailed['http_image_path'],
                    'absolute_path' => $detailed['absolute_path'],
                    'relative_path' => $detailed['relative_path'],
                    'position' => (int) $detailed['position'],
                    'altText' => getImagesSeoAltText($pair['detailed_id'], $lang_code),
                );
            } elseif (empty($pairs_data[$object_id][$pair_id]['pair_id'])) {
                $pairs_data[$object_id][$pair_id] = array(
                    'pair_id' => $pair['pair_id'],
                    'image_id' => $pair['image_id'],
                    'detailed_id' => $pair['detailed_id'],
                    'position' => $pair['position'],
                );

                if (!empty($pair['images_image_id'])) { //get detailed data if exist
                    $detailed = fn_attach_absolute_image_paths($pair, 'detailed');
                    $pairs_data[$object_id][$pair_id]['detailed'] = array(
                        'image_path' => $detailed['image_path'],
                        'alt' => $detailed['alt'],
                        'image_x' => $detailed['image_x'],
                        'image_y' => $detailed['image_y'],
                        'http_image_path' => $detailed['http_image_path'],
                        'absolute_path' => $detailed['absolute_path'],
                        'relative_path' => $detailed['relative_path'],
                        'position' => (int) $detailed['position'],
                        'altText' => getImagesSeoAltText($pair['detailed_id'], $lang_code),
                    );
                }
            }
        }// -foreach detailed_pairs

    } else {
        $pairs_data = db_get_memoized_hash_multi_array("SELECT pair_id, image_id, detailed_id, object_id FROM ?:images_links WHERE object_type = ?s AND type = ?s $cond", array('object_id', 'pair_id'), $object_type, $pair_type);
    }

    if (is_array($object_ids)) {
        return $pairs_data;
    } else {
        if ($pair_type == 'A') {
            return $pairs_data[$object_ids];
        } else {
            return !empty($pairs_data[$object_ids])? reset($pairs_data[$object_ids]) : array();
        }
    }
}

/**
 * Create/Update image pairs (icon -> detailed image)
 *
 * @internal  called by fn_clone_image_pairs OR fn_attach_image_pairs
 */
function fn_update_image_pairs($icons, $detailed, $pairs_data, $object_id = 0, $object_type = 'product_lists', $object_ids = array (), $update_alt_desc = true, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $pair_ids = array();

    if (!empty($pairs_data)) {
        foreach ($pairs_data as $k => $p_data) {
            $data = array();
            $pair_id = !empty($p_data['pair_id']) ? $p_data['pair_id'] : 0;
            $o_id = !empty($object_id) ? $object_id : ((!empty($p_data['object_id'])) ? $p_data['object_id'] : 0);

            if ($o_id == 0 && !empty($object_ids[$k])) {
                $o_id = $object_ids[$k];
            } elseif (!empty($object_ids) && empty($object_ids[$k])) {
                continue;
            }

            // Check if main pair is exists
            if (empty($pair_id) && !empty($p_data['type']) && $p_data['type'] == 'M') {
                $pair_data = db_get_row("SELECT pair_id, image_id, detailed_id FROM ?:images_links WHERE object_id = ?s AND object_type = ?s AND type = ?s", $o_id, $object_type, $p_data['type']);
                $pair_id = !empty($pair_data['pair_id']) ? $pair_data['pair_id'] : 0;
            } else {
                $pair_data = db_get_row("SELECT image_id, detailed_id FROM ?:images_links WHERE pair_id = ?i", $pair_id);
                if (empty($pair_data)) {
                    $pair_id = 0;
                }
            }

            // Update detailed image
            if (!empty($detailed[$k]) && !empty($detailed[$k]['size']) ) {
                if (fn_get_image_size($detailed[$k]['path'])) {
                    $detailed_id = 0;
                    //Update current detailed image only if it's link only at one object
                    if (!empty($pair_data['detailed_id']) && fn_get_count_image_link($pair_data['detailed_id'])<2) {
                        $detailed_id = $pair_data['detailed_id'];
                    }

                    // $data['detailed_id'] ne doit être setté que si valide (= non NULL),
                    // car la DB contient une valeur par défaut qui va bien.
                    if ($detailedId = fn_update_image($detailed[$k],  $detailed_id, 'detailed')) {
                        $data['detailed_id'] = $detailedId;
                    }
                }
            }

            // Update icon
            if (!empty($icons[$k]) && !empty($icons[$k]['size'])) {
                if (fn_get_image_size($icons[$k]['path'])) {
                    $data['image_id'] = fn_update_image($icons[$k], !empty($pair_data['image_id']) ? $pair_data['image_id'] : 0, $object_type);
                }
            }

            // Update alt descriptions
            if (((empty($data) && !empty($pair_id)) || !empty($data)) && $update_alt_desc == true) {
                $image_ids = array();
                if (!empty($pair_id)) {
                    $image_ids = db_get_row("SELECT image_id, detailed_id FROM ?:images_links WHERE pair_id = ?i", $pair_id);
                }

                $image_ids = fn_array_merge($image_ids, $data);

                $fields = array('detailed', 'image');
                foreach ($fields as $field) {
                    if (!empty($image_ids[$field . '_id']) && isset($p_data[$field . '_alt'])) {
                        if (!is_array($p_data[$field . '_alt'])) {
                            $_data = array (
                                'description' => empty($p_data[$field . '_alt']) ? '' : trim($p_data[$field . '_alt']),
                                'object_holder' => 'images'
                            );

                            // check, if this is new record, create new descriptions for all languages
                            $is_exists = db_get_field('SELECT object_id FROM ?:common_descriptions WHERE object_id = ?i AND lang_code = ?s AND object_holder = ?s', $image_ids[$field . '_id'], $lang_code, 'images');
                            if (!$is_exists) {
                                fn_create_description('common_descriptions', 'object_id', $image_ids[$field . '_id'], $_data);
                            } else {
                                db_query('UPDATE ?:common_descriptions SET ?u WHERE object_id = ?i AND lang_code = ?s AND object_holder = ?s', $_data, $image_ids[$field . '_id'], $lang_code, 'images');
                            }
                        } else {
                            foreach ($p_data[$field . '_alt'] as $lc => $_v) {
                                $_data = array (
                                    'object_id' => $image_ids[$field . '_id'],
                                    'description' => empty($_v) ? '' : trim($_v),
                                    'lang_code' => $lc,
                                    'object_holder' => 'images'
                                );
                                db_query("REPLACE INTO ?:common_descriptions ?e", $_data);
                            }
                        }
                    }
                }
            }

            // update image seo alt
            $imageSeoId = $detailedId ?? $pair_data['detailed_id'];
            if ($imageSeoId !== null && $imageSeoId !== '0') {
                $seoImagesData = [
                    'altText' => $p_data['altText'],
                    'lang_code' => $lang_code,
                ];

                // check, if this is new record, create new alt for all languages
                $imagesNumber = db_get_field('SELECT COUNT(*) FROM ?:images_seo WHERE image_id = ?i AND lang_code = ?s', $imageSeoId, $lang_code);
                if ((int) $imagesNumber > 0) {
                    db_query('UPDATE ?:images_seo SET ?u WHERE image_id = ?i AND lang_code = ?s', $seoImagesData, $imageSeoId, $lang_code);
                } else {
                    $seoImagesData['image_id'] = $imageSeoId;
                    db_query("INSERT INTO ?:images_seo ?e", $seoImagesData);
                }
            }

            if (empty($data)) {
                continue;
            }

            // Pair is exists
            $data['position'] = !empty($p_data['position']) ? $p_data['position'] : 0; // set data position

            if (!empty($pair_id)) {
                db_query("UPDATE ?:images_links SET ?u WHERE pair_id = ?i", $data, $pair_id);
            } else {
                $data['type'] = ($p_data['type'] === "") ? 'A' : $p_data['type']; // set link type
                $data['object_id'] = $o_id; // assign pair to object
                $data['object_type'] = $object_type;
                $pair_id = db_query("INSERT INTO ?:images_links ?e", $data);
            }

            $pairs_data[$k]['pair_id'] = $pair_id;

            $pair_ids[] = $pair_id;
        }
    }

    return $pair_ids;
}

function fn_delete_image_pairs($object_id, $object_type, $pair_type = '')
{
    $cond = '';

    if ($pair_type  === 'A') {
        $cond .= db_quote("AND type = 'A'");
    } elseif ($pair_type === 'M') {
        $cond .= db_quote("AND type = 'M'");
    } elseif ($pair_type === 'V') {
        $cond .= db_quote("AND type = 'V'");
    }

    $pair_ids = db_get_fields("SELECT pair_id FROM ?:images_links WHERE object_id = ?s AND object_type = ?s ?p", $object_id, $object_type, $cond);

    foreach ($pair_ids as $pair_id) {
        fn_delete_image_pair($pair_id, $object_type);
    }

    return true;
}

//
// Delete image pair
//
function fn_delete_image_pair($pair_id, $object_type = 'product')
{
    if (!empty($pair_id)) {
        $images = db_get_row("SELECT image_id, detailed_id FROM ?:images_links WHERE pair_id = ?i", $pair_id);
        if (!empty($images)) {
            fn_delete_image($images['image_id'], $pair_id, $object_type);
            fn_delete_image($images['detailed_id'], $pair_id, 'detailed');
        }

        return true;
    }

    return false;
}

//
// Clone image pairs
//
function fn_clone_image_pairs($target_object_id, $object_id, $object_type, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    // Get all pairs
    $pair_data = db_get_hash_array("SELECT pair_id, image_id, detailed_id, type FROM ?:images_links WHERE object_id = ?s AND object_type = ?s", 'pair_id', $object_id, $object_type);

    if (empty($pair_data)) {
        return false;
    }

    $icons = $detailed = $pairs_data = array();

    $imagesStorageService = container()->get('Wizacha\Storage\ImagesStorageService');
    foreach ($pair_data as $pair_id => $p_data) {
        if (!empty($p_data['image_id'])) {
            $icons[$pair_id] = fn_get_image($p_data['image_id'], $object_type, $lang_code, true);

            if (!empty($icons[$pair_id])) {
                $p_data['image_alt'] = empty($icons[$pair_id]['alt']) ? '' : $icons[$pair_id]['alt'];

                $tmp_name = fn_create_temp_file();
                $imagesStorageService->export($icons[$pair_id]['relative_path'], $tmp_name);
                $name = fn_basename($icons[$pair_id]['image_path']);

                $icons[$pair_id] = array(
                    'path' => $tmp_name,
                    'size' => filesize($tmp_name),
                    'error' => 0,
                    'name' => $name,
                );
            }
        }
        if (!empty($p_data['detailed_id'])) {
            $detailed[$pair_id] = fn_get_image($p_data['detailed_id'], 'detailed', $lang_code, true);
            if (!empty($detailed[$pair_id])) {
                $p_data['detailed_alt'] = empty($detailed[$pair_id]['alt']) ? '' : $detailed[$pair_id]['alt'];

                $tmp_name = fn_create_temp_file();

                $imagesStorageService->export($detailed[$pair_id]['relative_path'], $tmp_name);

                $name = fn_basename($detailed[$pair_id]['image_path']);

                $detailed[$pair_id] = array(
                    'path' => $tmp_name,
                    'size' => filesize($tmp_name),
                    'error' => 0,
                    'name' => $name,
                );
            }
        }

        $pairs_data = array(
            $pair_id => array(
                'type' => $p_data['type'],
                'image_alt' => (!empty($p_data['image_alt'])) ? $p_data['image_alt'] : '',
                'detailed_alt' => (!empty($p_data['detailed_alt'])) ? $p_data['detailed_alt'] : '',
            )
        );

        fn_update_image_pairs($icons, $detailed, $pairs_data, $target_object_id, $object_type, array(), true, $lang_code);
    }
}

// ----------- Utility functions -----------------

//
// Resize image
//
function fn_resize_image($src, $new_width = 0, $new_height = 0, $bg_color = '#ffffff')
{
    static $notification_set = false;
    static $gd_settings = array();

    $registry = \Wizacha\Registry::defaultInstance();
    $logger = $registry->container->get('logger');
    $logger->info(
        "[Images] > Start image resizing to $new_width x $new_height",
        [
            'company_id' => $registry->get(['runtime', 'company_id']),
            'image_path' => $src,
            'new_width' => $new_width,
            'new_height' => $new_height,
            'bg_color' => $bg_color
        ]
    );

    if (file_exists($src) && (!empty($new_width) || !empty($new_height)) && extension_loaded('gd')) {
        $img_functions = array(
            'png' => function_exists('imagepng'),
            'jpg' => function_exists('imagejpeg'),
            'gif' => function_exists('imagegif'),
            'bmp' => function_exists('imagebmp'),
            'webp' => function_exists('imagewebp'),
        );

        if (empty($gd_settings)) {
            $gd_settings = Settings::instance()->getValues('Thumbnails');
        }

        list($width, $height, $mime_type) = fn_get_image_size($src);
        if (
            empty($width)
            || empty($height)
            || !\Wizacha\ImageManager::isSizeCorrect($width, $height)
        ) {
            return false;
        }

        $ext = fn_get_image_extension($mime_type);
        if (empty($img_functions[$ext])) {
            if ($notification_set == false) {
                fn_set_notification('E', __('error'), __('error_image_format_not_supported', array(
                    '[format]' => $ext
                )));
                $notification_set = true;
            }

            return false;
        }

        if ($ext == 'gif') {
            $new = imagecreatefromgif($src);
        } elseif ($ext == 'jpg') {
            $new = fn_imagecreatefromjpeg($src);
            // If the image has been rotated of 90 or -90 deg, swap the width and height vars
            if (imagesx($new) != $width) {
                list($width, $height) = [$height, $width];
                list($new_width, $new_height) = [$new_height, $new_width];
            }
        } elseif ($ext == 'png') {
            $new = imagecreatefrompng($src);
        } elseif ($ext === 'bmp') {
            $new = imageCreateFromBmp($src);
        } elseif ( $ext === 'webp') {
            $new = imagecreatefromwebp($src);
        }

        if (empty($new_width) || empty($new_height)) {
            if ($width < $new_width) {
                $new_width = $width;
            }
            if ($height < $new_height) {
                $new_height = $height;
            }
        }

        $dst_width = $new_width;
        $dst_height = $new_height;

        if (empty($new_height)) { // if we passed width only, calculate height
            $dst_height = $new_height = ($height / $width) * $new_width;

        } elseif (empty($new_width)) { // if we passed height only, calculate width
            $dst_width = $new_width = ($width / $height) * $new_height;

        } else { // we passed width and height, we need to fit image in this sizes
            if ($new_width * $height / $width > $dst_height) {
                $new_width = $width * $dst_height / $height;
            }
            $new_height = ($height / $width) * $new_width;
            if ($new_height * $width / $height > $dst_width) {
                $new_height = $height * $dst_width / $width;
            }
            $new_width = ($width / $height) * $new_height;

            $make_box = true;
        }

        $new_width = intval($new_width);
        $new_height = intval($new_height);

        $dst = imagecreatetruecolor($dst_width, $dst_height);

        if (function_exists('imageantialias')) {
            imageantialias($dst, true);
        }

        list($r, $g, $b) = (empty($bg_color)) ? fn_parse_rgb('#ffffff') : fn_parse_rgb($bg_color);
        $c = imagecolorallocate($dst, $r, $g, $b);

        if (empty($bg_color) && ($ext == 'png' || $ext == 'gif')) {
            if (function_exists('imagecolorallocatealpha') && function_exists('imagecolortransparent') && function_exists('imagesavealpha') && function_exists('imagealphablending')) {
                $c = imagecolorallocatealpha($dst, 255, 255, 255, 127);
                imagecolortransparent($dst, $c);
                imagesavealpha($dst, true);
                imagealphablending($dst, false);
            }
        }

        imagefilledrectangle($dst, 0, 0, $dst_width, $dst_height, $c);

        if (!empty($make_box)) {
            $x = intval(($dst_width - $new_width) / 2);
            $y = intval(($dst_height - $new_height) / 2);
        } else {
            $x = 0;
            $y = 0;
        }

        imagecopyresampled($dst, $new, $x, $y, 0, 0, $new_width, $new_height, $width, $height);

        // Free memory from image
        imagedestroy($new);

        if ($gd_settings['convert_to'] == 'original') {
            $convert_to = $ext;
        } elseif (!empty($img_functions[$gd_settings['convert_to']])) {
            $convert_to = $gd_settings['convert_to'];
        } else {
            $convert_to = key($img_functions);
        }

        ob_start();
        if ($convert_to == 'gif') {
            imagegif($dst);
        } elseif ($convert_to == 'jpg') {
            imagejpeg($dst, null, $gd_settings['jpeg_quality']);
        } elseif ($convert_to == 'png') {
            imagepng($dst);
        } elseif ($convert_to === 'bmp') {
            imagebmp($dst);
        } elseif ($convert_to === 'webp') {
            imagewebp($dst);
        }
        $content = ob_get_clean();

        $logger->addInfo(
            "[Images] > End of image resizing to $new_width x $new_height",
            [
                'company_id' => $registry->get(['runtime', 'company_id']),
                'image_path' => $src,
                'width' => $width,
                'height' => $height,
                'new_width' => $new_width,
                'new_height' => $new_height,
                'bg_color' => $bg_color,
                'convert_to' => $convert_to
            ]
        );

        return array($content, $convert_to);
    }

    return false;
}

/**
 * @param string $file The JPEG image path
 * @param bool $isRotated Output parameter, true if the image has been rotated
 * @return resource A GD image
 */
function fn_imagecreatefromjpeg($file, &$isRotated = false)
{
    $image = imagecreatefromjpeg($file);
    $exif = @exif_read_data($file);
    if (!empty($exif['Orientation'])) {
        $isRotated = true;
        switch($exif['Orientation']) {
            /*
              1        2       3      4         5            6           7          8

            888888  888888      88  88      8888888888  88                  88  8888888888
            88          88      88  88      88  88      88  88          88  88      88  88
            8888      8888    8888  8888    88          8888888888  8888888888          88
            88          88      88  88
            88          88  888888  888888
            */
            case 2:
                imageflip($image, IMG_FLIP_HORIZONTAL);
                break;
            case 3:
                $image = imagerotate($image, 180, 0);
                break;
            case 4:
                imageflip($image, IMG_FLIP_VERTICAL);
                break;
            case 5:
                imageflip($image, IMG_FLIP_VERTICAL);
                $image = imagerotate($image, -90, 0);
                break;
            case 6:
                $image = imagerotate($image, -90, 0);
                break;
            case 7:
                imageflip($image, IMG_FLIP_HORIZONTAL);
                $image = imagerotate($image, -90, 0);
                break;
            case 8:
                $image = imagerotate($image, 90, 0);
                break;
            default:
                $isRotated = false;
                break;
        }
    }
    return $image;
}

//
// Check supported GDlib formats
//
function fn_check_gd_formats()
{
    $avail_formats = array(
        'original' => __('same_as_source'),
    );

    if (function_exists('imagegif')) {
        $avail_formats['gif'] = 'GIF';
    }
    if (function_exists('imagejpeg')) {
        $avail_formats['jpg'] = 'JPEG';
    }
    if (function_exists('imagepng')) {
        $avail_formats['png'] = 'PNG';
    }

    return $avail_formats;
}

//
// Get image extension by MIME type
//
function fn_get_image_extension($image_type)
{
    static $image_types = array (
        'image/gif' => 'gif',
        'image/pjpeg' => 'jpg',
        'image/jpeg' => 'jpg',
        'image/png' => 'png',
        'application/x-shockwave-flash' => 'swf',
        'image/psd' => 'psd',
        'image/bmp' => 'bmp',
        'image/x-icon' => 'ico',
        'image/x-ms-bmp' => 'bmp',
        'image/webp' => 'webp',
    );

    return isset($image_types[$image_type]) ? $image_types[$image_type] : false;
}

/**
 * Returns image width, height, mime type and local path to image
 *
 * @param string $file path to image
 * @return array array with width, height, mime type and path
 */
function fn_get_image_size($file)
{
    // File is url, get it and store in temporary directory
    if (strpos($file, '://') !== false) {
        $tmp = fn_create_temp_file();

        if (fn_put_contents($tmp, fn_get_contents($file)) == 0) {
            return false;
        }

        $file = $tmp;
    }

    list($w, $h, $t, $a) = @getimagesize($file);

    if (empty($w)) {
        return false;
    }

    $t = image_type_to_mime_type($t);

    return array($w, $h, $t, $file);
}

function fn_attach_image_pairs($name, $object_type, $object_id = 0, $lang_code = null, $object_ids = array ())
{
    $extensions = ['png', 'gif', 'jpg', 'jpeg', 'ico', 'bmp', 'webp'];

    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $icons = fn_filter_uploaded_data($name . '_image_icon', $extensions);
    $detailed = fn_filter_uploaded_data($name . '_image_detailed', $extensions);
    $pairs_data = !empty($_REQUEST[$name . '_image_data']) ? $_REQUEST[$name . '_image_data'] : array();

    return fn_update_image_pairs($icons, $detailed, $pairs_data, $object_id, $object_type, $object_ids, true, $lang_code);
}

/**
 * Generate thumbnail with given size from image
 *
 * @param string $image_path Path to image
 * @param int $width Thumbnail width
 * @param int $height Thumbnail height
 * @param bool $lazy lazy generation - returns script URL that generates thumbnail
 * @return array array with width, height, mime type and path
 */
function fn_generate_thumbnail($image_path, $width, $height = 0, $lazy = false, $protocol = '')
{
    $imagesStorageService = container()->get('Wizacha\Storage\ImagesStorageService');
    if (empty($image_path)) {
        return '';
    }

    // This function can take remote url, like video thumbnails which are hosted on S3
    $isRemote = false;
    if (strpos($image_path, '://') !== false) {
        $isRemote = true;
        $remotePath = $image_path;
        $image_path = substr(parse_url($image_path, PHP_URL_PATH), 1); // keep the path and remove the first slash
    }

    $filename = \Wizacha\ImageManager::thumbnailPath($image_path, $width ?? 0, $height);

    $image_url = $imagesStorageService->getUrl($image_path, 'http');

    $convertToFormat = Registry::get('settings.Thumbnails.convert_to');
    if (
        null !== $convertToFormat
        && ('original' !== $convertToFormat)
    ) {
        $filename = preg_replace("/\.[^.]*?$/", '.'.$convertToFormat, $filename);
    }

    $th_filename = '';


    if ($imagesStorageService->isExist($filename) === false) {
        if ($lazy === false) {
            $tmp_path = fn_create_temp_file();
            $exported_to_local_tmp_dir = $imagesStorageService->export($image_path, $tmp_path);

            if ($exported_to_local_tmp_dir === true && empty($tmp_path) === false) {
                list($cont, $format) = fn_resize_image($tmp_path, $width, $height, Registry::get('settings.Thumbnails.thumbnail_background_color'));

                if (!empty($cont)) {
                    list(, $th_filename) = $imagesStorageService->put($filename, array(
                        'contents' => $cont,
                        'caching' => true
                    ));
                }
            }
        } else {
            $th_filename = fn_url('image.thumbnail?w=' . $width . '&h=' . $height . '&image_path=' . urlencode($image_url), AREA, 'current');
        }
    } else {
        $th_filename = $filename;
    }

    if (empty($th_filename)) {
        return container()->getParameter('image.serve_fake_images')
            ? "https://unsplash.it/$width/$height?image=" . (crc32($image_path) % 1084)
            : ''
        ;
    }

    return $imagesStorageService->getUrl($th_filename, $protocol);
}

function fn_parse_rgb($color)
{
    $r = hexdec(substr($color, 1, 2));
    $g = hexdec(substr($color, 3, 2));
    $b = hexdec(substr($color, 5, 2));
    return array($r, $g, $b);
}

function fn_image_to_display($images, $image_width = 0, $image_height = 0)
{
    if (empty($images)) {
        return array();
    }

    $image_data = array();

    // image pair passed
    if (!empty($images['icon']) || !empty($images['detailed'])) {
        if (!empty($images['icon'])) {
            $original_width = $images['icon']['image_x'];
            $original_height = $images['icon']['image_y'];
            $image_path = $images['icon']['image_path'];
            $absolute_path = $images['icon']['absolute_path'];
            $relative_path = $images['icon']['relative_path'];
        } else {
            $original_width = $images['detailed']['image_x'];
            $original_height = $images['detailed']['image_y'];
            $image_path = $images['detailed']['image_path'];
            $absolute_path = $images['detailed']['absolute_path'];
            $relative_path = $images['detailed']['relative_path'];
        }

        $detailed_image_path = !empty($images['detailed']['image_path']) ? $images['detailed']['image_path'] : '';
        $alt = !empty($images['icon']['alt']) ? $images['icon']['alt'] : $images['detailed']['alt'];

    // single image passed only
    } else {
        $original_width = $images['image_x'];
        $original_height = $images['image_y'];
        $image_path = $images['image_path'];
        $alt = $images['alt'];
        $detailed_image_path = '';
        $absolute_path = $images['absolute_path'];
        $relative_path = $images['relative_path'];
    }

    if (!empty($image_height) && empty($image_width)) {
        $image_width = $original_height ? intval($image_height * $original_width / $original_height) : 0;
    }

    if (!empty($image_width) && empty($image_height)) {
        $image_height = $original_width ? intval($image_width * $original_height / $original_width) : 0;
    }

    if (!empty($image_width)) {
        $image_path = fn_generate_thumbnail($relative_path, $image_width, $image_height, false);
    } else {
        $image_width = $original_width;
        $image_height = $original_height;
    }

    if (!empty($image_path)) {
        $image_data = array(
            'image_path' => $image_path,
            'detailed_image_path' => $detailed_image_path,
            'alt' => $alt,
            'width' => $image_width,
            'height' => $image_height,
            'absolute_path' => $absolute_path,
            'generate_image' => strpos($image_path, '&image_path=') !== false // FIXME: dirty checking
        );
    }

    return $image_data;
}

/**
 * If CloudImage configured, invalidate cache
 */
function fn_w_invalidate_cloudimage_cache(int $imageId): void
{
    $featureflag = container()->getParameter('feature.cloudimage');

    if (0 < $imageId
        && true === \filter_var($featureflag, FILTER_VALIDATE_BOOLEAN)
    ) {
        /** @var CloudImageManager */
        $cloudImageManager = container()->get(CloudImageManager::class);
        $cloudImageManager->invalidateCloudImageCache($imageId);
    }
}

function  getImagesSeoAltText(int $imageId, string $langCode): ?string
{
    return db_get_field("SELECT altText FROM ?:images_seo WHERE image_id = ?i AND lang_code = ?s", $imageId, $langCode);
}
