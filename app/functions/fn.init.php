<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Tygh\Ajax;
use Tygh\Registry;
use Tygh\Settings;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Smarty\LegacySmartyFactory;

/**
 * Init template engine
 *
 * @return boolean always true
 */
function fn_init_templater($area = AREA)
{
    $view = LegacySmartyFactory::create($area);

    Registry::set('view', $view);

    return array(INIT_STATUS_OK);
}

/**
 * Init ajax engine
 *
 * @return boolean true if current request is ajax, false - otherwise
 */
function fn_init_ajax()
{
    if (defined('AJAX_REQUEST')) {
        return array(INIT_STATUS_OK);
    }

    if (empty($_REQUEST['ajax_custom']) && ((!empty($_REQUEST['callback']) && !empty($_REQUEST['result_ids'])) || !empty($_REQUEST['is_ajax']) || (!empty($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false))) {
        $ajax = new Ajax($_REQUEST);
        Registry::set('ajax', $ajax);
        fn_define('AJAX_REQUEST', true);
    }

    return array(INIT_STATUS_OK);
}

/**
 * Init languages
 *
 * @param array $params request parameters
 * @return boolean always true
 */
function fn_init_language($params, $area = AREA)
{
    container()
        ->get('app.locale.detector')
        ->detect(Request::createFromGlobals());

    $showHiddenLanguages = $area != 'C' ? true : false;
    $availLanguages = fn_get_avail_languages($area, $showHiddenLanguages);

    Registry::set('languages', $availLanguages);

    return array(INIT_STATUS_OK);
}

/**
 * Init company data
 * Company data array will be saved in the registry runtime.company_data
 *
 * @param array $params request parameters
 * @return array with init data (init status, redirect url in case of redirect)
 */
function fn_init_company_data($params)
{
    $company_data = array(
        'company' => __('all_vendors'),
    );

    $company_id = Registry::get('runtime.company_id');
    if ($company_id) {
        $company_data = fn_get_company_data($company_id);
    }

    Registry::set('runtime.company_data', $company_data);

    return array(INIT_STATUS_OK);
}

/**
 * Init selected company
 * Selected company id will be saved in the registry runtime.company_id
 *
 * @param array $params request parameters
 * @return array with init data (init status, redirect url in case of redirect)
 */
function fn_init_company_id(&$params)
{
    $company_id = 0;
    $result = array(INIT_STATUS_OK);

    if (isset($params['switch_company_id'])) {
        $switch_company_id = intval($params['switch_company_id']);
    } else {
        $switch_company_id = false;
    }

    if (defined('API')) {
        $api = Registry::get('api');

        if ($api instanceof \Tygh\Api && $user_data = $api->getUserData()) {
            $company_id = 0;

            if ($user_data['company_id']) {
                $company_id = $user_data['company_id'];
            }

            $store = array();
            if (preg_match('/(stores|vendors)\/(\d+)\/.+/', $api->getRequest()->getResource(), $store)) {

                if ($company_id && $company_id != $store[2]) {
                    $response = new \Tygh\Api\Response(\Tygh\Api\Response::STATUS_FORBIDDEN);
                    /**
                     * Here is exit.
                     */
                    $response->send();
                }

                $company_id = intval($store[2]);
                if (!fn_get_available_company_ids($company_id)) {
                    $company_id = 0;
                }
            }
        } else {
            $response = new \Tygh\Api\Response(\Tygh\Api\Response::STATUS_UNAUTHORIZED);
            $response->send();
        }
    }

    // set company_id for vendor's admin
    if (AREA == 'A' && !empty($_SESSION['auth']['company_id'])) {
        $company_id = intval($_SESSION['auth']['company_id']);
        if (!fn_get_available_company_ids($company_id)) {
            return fn_init_company_id_redirect($params, 'access_denied');
        }
    }

    // admin switching company_id
    if (!$company_id) {
        if ($switch_company_id !== false) { // request not empty
            if ($switch_company_id) {
                if (fn_get_available_company_ids($switch_company_id)) {
                    $company_id = $switch_company_id;
                } else {
                    return fn_init_company_id_redirect($params, 'company_not_found');
                }
            }
            fn_set_session_data('company_id', $company_id, COOKIE_ALIVE_TIME);
        } else {
            $company_id = fn_init_company_id_find_in_session();
        }
    }

    Registry::set('runtime.company_id', $company_id);

    unset($params['switch_company_id']);

    return $result;
}

/**
 * Form error notice and make redirect. Used in fn_init_company_id
 *
 * @param array $params request parameters
 * @param string $message language variable name for message
 * @param int $redirect_company_id New company id for redirecting, if null, company id saved in session will be used
 * @return array with init data (init status, redirect url in case of redirect)
 */
function fn_init_company_id_redirect(&$params, $message, $redirect_company_id = null)
{
    if ('access_denied' == $message) {

        $_SESSION['auth'] = array();
        $redirect_url = 'auth.login_form' . (!empty($params['return_url']) ? '?return_url=' . urldecode($params['return_url']) : '');

    } elseif ('company_not_found' == $message) {

        $dispatch = !empty($params['dispatch']) ? $params['dispatch'] : 'auth.login_form';
        unset($params['dispatch']);
        $params['switch_company_id'] = (null === $redirect_company_id) ? fn_init_company_id_find_in_session() : $redirect_company_id;

        $redirect_url = $dispatch . '?' . http_build_query($params);
    }

    fn_set_notification('E', __('error'), __($message));

    return array(INIT_STATUS_REDIRECT, $redirect_url);
}

/**
 * Tryes to find company id in session
 *
 * @return int Company id if stored in session, 0 otherwise
 */
function fn_init_company_id_find_in_session()
{
    $session_company_id = intval(fn_get_session_data('company_id'));
    if ($session_company_id && !fn_get_available_company_ids($session_company_id)) {
        fn_delete_session_data('company_id');
        $session_company_id = 0;
    }

    return $session_company_id;
}

/**
 * Init currencies
 *
 * @param array $params request parameters
 * @param  string $area Area ('A' for admin or 'C' for customer)
 * @return boolean always true
 */
function fn_init_currency($params, $area = AREA)
{
    $avail_statuses = ($area == 'C') ? array('A') : array('A', 'H');
    $cond = db_quote(' AND status IN (?a)', $avail_statuses);

    $currencies = db_get_hash_array("SELECT a.*, b.description FROM ?:currencies as a LEFT JOIN ?:currency_descriptions as b ON a.currency_code = b.currency_code AND lang_code = ?s WHERE 1 ?p ORDER BY a.position", 'currency_code', (string) GlobalState::interfaceLocale(), $cond);

    if (!empty($params['currency']) && !empty($currencies[$params['currency']])) {
        $secondary_currency = $params['currency'];
    } elseif (($c = fn_get_session_data('secondary_currency' . $area)) && !empty($currencies[$c])) {
        $secondary_currency = $c;
    } else {
        foreach ($currencies as $v) {
            if ($v['is_primary'] == 'Y') {
                $secondary_currency = $v['currency_code'];
                break;
            }
        }
    }

    if (empty($secondary_currency)) {
        reset($currencies);
        $secondary_currency = key($currencies);
    }

    if ($secondary_currency != fn_get_session_data('secondary_currency' . $area)) {
        fn_set_session_data('secondary_currency'.$area, $secondary_currency, COOKIE_ALIVE_TIME);
    }

    $primary_currency = '';

    foreach ($currencies as $v) {
        if ($v['is_primary'] == 'Y') {
            $primary_currency = $v['currency_code'];
            break;
        }
    }

    if (empty($primary_currency)) {
        reset($currencies);
        $first_currency = current($currencies);
        $primary_currency = $first_currency['currency_code'];
    }

    define('CART_PRIMARY_CURRENCY', $primary_currency);
    define('CART_SECONDARY_CURRENCY', $secondary_currency);

    Registry::set('currencies', $currencies);

    return array(INIT_STATUS_OK);
}

/**
 * Init user
 *
 * @return boolean always true
 */
function fn_init_user($area = AREA)
{
    $user_info = array();
    if (!empty($_SESSION['auth']['user_id'])) {
        $user_info = fn_get_user_short_info($_SESSION['auth']['user_id']);
        if (empty($user_info)) { // user does not exist in the database, but exists in session
            $_SESSION['auth'] = array();
        }
    }

    if (empty($_SESSION['auth'])) {

        $udata = array();
        $user_id = fn_get_session_data($area . '_user_id');

        if ($area == 'A' && defined('CONSOLE')) {
            $user_id = 1;
        }

        if ($user_id) {
            fn_define('LOGGED_VIA_COOKIE', true);
        }

        fn_login_user($user_id);

        if (!defined('NO_SESSION')) {
            $_SESSION['cart'] = isset($_SESSION['cart']) ? $_SESSION['cart'] : array();
        }

        if ((defined('LOGGED_VIA_COOKIE') && !empty($_SESSION['auth']['user_id'])) || ($cu_id = fn_get_session_data('cu_id'))) {
            if (!empty($cu_id)) {
                fn_define('COOKIE_CART' , true);
            }

            // Cleanup cached shipping rates

            unset($_SESSION['shipping_rates']);

            if (!empty($_SESSION['auth']['user_id'])) {
                $_SESSION['cart']['user_data'] = fn_get_user_info($_SESSION['auth']['user_id']);
                $user_info = fn_get_user_short_info($_SESSION['auth']['user_id']);
            }
        }
    }

    if (fn_is_expired_storage_data('cart_products_next_check', SECONDS_IN_HOUR * 12)) {
        db_query("DELETE FROM ?:user_session_products WHERE user_type = 'U' AND timestamp < ?i", (TIME - SECONDS_IN_DAY * 30));
    }

    Registry::set('user_info', $user_info);

    return array(INIT_STATUS_OK);
}

function fn_init_settings()
{
    Registry::registerCache('settings', array('settings_objects', 'settings_vendor_values', 'settings_descriptions', 'settings_sections', 'settings_variants'), Registry::cacheLevel('static'));
    if (Registry::isExist('settings') == false) {
        Registry::set('settings', Settings::instance()->getValues());
    }

    // Set timezone
    date_default_timezone_set(Registry::get('settings.Appearance.timezone'));

    fn_define('DEFAULT_LANGUAGE', Registry::get('settings.Appearance.backend_default_language'));

    return array(INIT_STATUS_OK);
}

/**
 * Initialize all enabled addons
 *
 * @return boolean always true
 */
function fn_init_addons()
{
    Registry::registerCache('addons', array('addons', 'settings_objects', 'settings_vendor_values', 'settings_descriptions', 'settings_sections', 'settings_variants'), Registry::cacheLevel('static'));

    if (Registry::isExist('addons') == false) {

        $_addons = db_get_hash_array("SELECT addon, priority, status FROM ?:addons ORDER BY priority", 'addon');

        foreach ($_addons as $k => $v) {
            $_addons[$k] = Settings::instance()->getValues($v['addon'], Settings::ADDON_SECTION, false);
            $_addons[$k]['status'] = $v['status'];
            $_addons[$k]['priority'] = $v['priority'];
        }

        Registry::set('addons', $_addons);
    }
    foreach ((array) Registry::get('addons') as $addon_name => $data) {
        if (empty($data['status'])) {
            log_unused_code(__FILE__, __LINE__);
            // FIX ME: Remove me
            error_log("ERROR: Addons initialization: Bad '$addon_name' addon data:" . serialize($data) . " Addons Registry:" . serialize(Registry::get('addons')));
        }
        if (!empty($data['status']) && $data['status'] == 'A') {
            if (is_file(Registry::get('config.dir.addons') . $addon_name . '/init.php')) {
                include_once(Registry::get('config.dir.addons') . $addon_name . '/init.php');
            }
            if (file_exists(Registry::get('config.dir.addons') . $addon_name . '/func.php')) {
                include_once(Registry::get('config.dir.addons') . $addon_name . '/func.php');
            }
            if (file_exists(Registry::get('config.dir.addons') . $addon_name . '/config.php')) {
                include_once(Registry::get('config.dir.addons') . $addon_name . '/config.php');
            }

            Registry::get('class_loader')->add('', Registry::get('config.dir.addons') . $addon_name);
        }
    }

    Registry::set('addons_initiated', true);

    return array(INIT_STATUS_OK);
}

function fn_init_stack()
{
    $stack = Registry::get('init_stack');
    if (empty($stack)) {
        $stack = array();
    }

    $stack_data = func_get_args();

    foreach ($stack_data as $data) {
        $stack[] = $data;
    }

    Registry::set('init_stack', $stack);

    return true;
}

/**
 * Run init functions
 *
 * @param array $request $_REQUEST global variable
 * @return bool always true
 */
function fn_init(&$request)
{
    $stack = Registry::get('init_stack');

    // Cleanup stack
    Registry::set('init_stack', array());

    foreach ($stack as $function_data) {
        $function = array_shift($function_data);

        if (!is_callable($function)) {
            continue;
        }

        $result = call_user_func_array($function, $function_data);

        $status = !empty($result[0]) ? $result[0] : INIT_STATUS_OK;
        $url = !empty($result[1]) ? $result[1] : '';
        $message = !empty($result[2]) ? $result[2] : '';

        if ($status == INIT_STATUS_OK && !empty($url)) {
            $redirect_url = $url;

        } elseif ($status == INIT_STATUS_REDIRECT && !empty($url)) {
            // Code utilisé, item rollbar 1023
            // log_unused_code(__FILE__, __LINE__);
            $redirect_url = $url;
            break;

        } elseif ($status == INIT_STATUS_FAIL) {
            log_unused_code(__FILE__, __LINE__);
            if (empty($message)) {
                $message = 'Initiation failed in <b>' . (is_array($function) ? implode('::', $function) : $function) . '</b> function';
            }
            die($message);
        }
    }

    if (!empty($redirect_url)) {
        // Code utilisé
        // log_unused_code(__FILE__, __LINE__);
        // TODO: hack here to redirect, will disappear when symfony PR gets merged
        $response = fn_redirect($redirect_url);
        $response->send();
        exit;
    }

    $stack = Registry::get('init_stack');
    if (!empty($stack)) {
        // New init functions were added to stack. Execute them
        fn_init($request);
    }

    return true;
}

/**
 * Init api object and put it to Registry.
 */
function fn_init_api($request)
{
    Registry::set('api', new \Wizacha\Bridge\CsCart\Api(), true);

    return array(INIT_STATUS_OK);
}

function fn_init_upload_size()
{
    if (isset($_SERVER['CONTENT_LENGTH']) && ($_SERVER['CONTENT_LENGTH'] > fn_return_bytes(ini_get('upload_max_filesize')) || $_SERVER['CONTENT_LENGTH'] > fn_return_bytes(ini_get('post_max_size')))) {
        $max_size = fn_return_bytes(ini_get('upload_max_filesize')) < fn_return_bytes(ini_get('post_max_size')) ? ini_get('upload_max_filesize') : ini_get('post_max_size');

        fn_set_notification('E', __('error'), __('text_forbidden_uploaded_file_size', array(
            '[size]' => $max_size
        )));
        return [INIT_STATUS_REDIRECT, $_SERVER['HTTP_REFERER']];
    }
}

function fn_init_stack_bypass(): bool
{
    // is Image API
    if (0 === strpos($_SERVER['REQUEST_URI'], '/api/v1/image/')) {
        return true;
    }

    // is legacy Catalog API
    if (
        ('GET' === $_SERVER['REQUEST_METHOD'])
        && (0 === strpos($_SERVER['REQUEST_URI'], '/api/v1/catalog/'))
        && (false === strpos($_SERVER['REQUEST_URI'], '/api/v1/catalog/search/products'))
    ) {
        return true;
    }

    return false;
}
