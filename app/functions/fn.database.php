<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Symfony\Component\HttpFoundation\RequestStack;
use Tygh\Database;
use Tygh\Registry;
use Wizacha\Marketplace\Exception\NotFound;

function db_memoized_key(array $args): string{
    /** @var ?RequestStack */
    static $requestStack = null;

    $requestStack = $requestStack ?? container()->get('request_stack');

    $request = $requestStack
        ->getCurrentRequest();

    if (null === $request) {
        throw new NotFound('No request for this call');
    }

    $requestId = $request
        ->headers
        ->get('x-request-id')
    ;

    return \json_encode(
        \array_merge(
            ['x-request-id' => $requestId],
            $args
        )
    );
}

function db_get_memoized_data(array &$data, string $method, array $args){
    try {
        $key = db_memoized_key($args);
    } catch (NotFound $exception) {
        return  \call_user_func_array(array('\\Tygh\\Database', $method), $args);
    }

    if (false === \array_key_exists($key, $data)) {
        $data[$key] = \call_user_func_array(array('\\Tygh\\Database', $method), $args);
    }

    return $data[$key];
}

/**
 * Execute query and format result as associative array with column names as keys
 *
 * @param string $query unparsed query
 * @param mixed ... unlimited number of variables for placeholders
 * @return array structured data
 */
function db_get_array()
{
    return call_user_func_array(array('\\Tygh\\Database', 'getArray'), func_get_args());
}
function db_get_memoized_array()
{
    static $data = [];

    return db_get_memoized_data(
        $data,
        'getArray',
        \func_get_args()
    );
}

/**
 * Execute query and format result as associative array with column names as keys and index as defined field
 *
 * @param string $query unparsed query
 * @param string $field field for array index
 * @param mixed ... unlimited number of variables for placeholders
 * @return array structured data
 */
function db_get_hash_array()
{
    return call_user_func_array(array('\\Tygh\\Database', 'getHash'), func_get_args());
}
function db_get_memoized_hash_array()
{
    static $data = [];

    return db_get_memoized_data(
        $data,
        'getHash',
        \func_get_args()
    );
}

/**
 * Execute query and format result as associative array with column names as keys and then return first element of this array
 *
 * @param string $query unparsed query
 * @param mixed ... unlimited number of variables for placeholders
 * @return array structured data
 */
function db_get_row()
{
    return call_user_func_array(array('\\Tygh\\Database', 'getRow'), func_get_args());
}
function db_get_memoized_row()
{
    static $data = [];

    return db_get_memoized_data(
        $data,
        'getRow',
        \func_get_args()
    );
}

/**
 * Execute query and returns first field from the result
 *
 * @param string $query unparsed query
 * @param mixed ... unlimited number of variables for placeholders
 * @return mixed structured data
 */
function db_get_field()
{
    return call_user_func_array(array('\\Tygh\\Database', 'getField'), func_get_args());
}
function db_get_memoized_field()
{
    static $data = [];

    return db_get_memoized_data(
        $data,
        'getField',
        \func_get_args()
    );
}


/**
 * Execute query and format result as set of first column from all rows
 *
 * @param string $query unparsed query
 * @param mixed ... unlimited number of variables for placeholders
 * @return array structured data
 */
function db_get_fields()
{
    return call_user_func_array(array('\\Tygh\\Database', 'getColumn'), func_get_args());
}
function db_get_memoized_fields()
{
    static $data = [];

    return db_get_memoized_data(
        $data,
        'getColumn',
        \func_get_args()
    );
}

/**
 * Execute query and format result as one of: field => array(field_2 => value), field => array(field_2 => row_data), field => array([n] => row_data)
 *
 * @param string $query unparsed query
 * @param array $params array with 3 elements (field, field_2, value)
 * @param mixed ... unlimited number of variables for placeholders
 * @return array structured data
 */
function db_get_hash_multi_array()
{
    return call_user_func_array(array('\\Tygh\\Database', 'getMultiHash'), func_get_args());
}
function db_get_memoized_hash_multi_array()
{
    static $data = [];

    return db_get_memoized_data(
        $data,
        'getMultiHash',
        \func_get_args()
    );
}


/**
 * Execute query and format result as key => value array
 *
 * @param string $query unparsed query
 * @param array $params array with 2 elements (key, value)
 * @param mixed ... unlimited number of variables for placeholders
 * @return array structured data
 */
function db_get_hash_single_array()
{
    return call_user_func_array(array('\\Tygh\\Database', 'getSingleHash'), func_get_args());
}
function db_get_memoized_hash_single_array()
{
    static $data = [];

    return db_get_memoized_data(
        $data,
        'getSingleHash',
        \func_get_args()
    );
}

/**
 *
 * Prepare data and execute REPLACE INTO query to DB
 * If one of $data element is null function unsets it before querry
 *
 * @param string $table  Name of table that condition generated. Must be in SQL notation without placeholder.
 * @param array  $data   Array of key=>value data of fields need to insert/update
 * @return db_result
 */
function db_replace_into($table, $data)
{
    return call_user_func_array(array('\\Tygh\\Database', 'replaceInto'), func_get_args());
}

/**
 * Execute query
 *
 * @param string $query unparsed query
 * @param mixed ... unlimited number of variables for placeholders
 * @return mixed result set or the ID generated for an AUTO_INCREMENT field for insert statement
 */
function db_query()
{
    return call_user_func_array(array('\\Tygh\\Database', 'query'), func_get_args());
}

/**
 * Parse query and replace placeholders with data
 *
 * @param string $query unparsed query
 * @param mixed ... unlimited number of variables for placeholders
 * @return string parsed query
 */
function db_quote()
{
    return call_user_func_array(array('\\Tygh\\Database', 'quote'), func_get_args());
}

/**
 * Get column names from table
 *
 * @param string $table_name table name
 * @param array $exclude optional array with fields to exclude from result
 * @param bool $wrap_quote optional parameter, if true, the fields will be enclosed in quotation marks
 * @return array columns array
 */
function fn_get_table_fields()
{
    return call_user_func_array(array('\\Tygh\\Database', 'getTableFields'), func_get_args());
}

/**
 * Check if passed data corresponds columns in table and remove unnecessary data
 *
 * @param array $data data for compare
 * @param array $table_name table name
 * @return mixed array with filtered data or false if fails
 */
function fn_check_table_fields()
{
    return call_user_func_array(array('\\Tygh\\Database', 'checkTableFields'), func_get_args());
}

/**
 * Remove value from set (e.g. remove 2 from "1,2,3" results in "1,3")
 *
 * @param string $field table field with set
 * @param string $value value to remove
 * @return string database construction for removing value from set
 */
function fn_remove_from_set($field, $value)
{
    return Database::quote("TRIM(BOTH ',' FROM REPLACE(CONCAT(',', $field, ','), CONCAT(',', ?s, ','), ','))", $value);
}

/**
 * Add value to set (e.g. add 2 from "1,3" results in "1,3,2")
 *
 * @param string $field table field with set
 * @param string $value value to add
 * @return string database construction for add value to set
 */
function fn_add_to_set($field, $value)
{
    return Database::quote("TRIM(BOTH ',' FROM CONCAT_WS(',', ?p, ?s))", fn_remove_from_set($field, $value), $value);
}

/**
 * Create set from php array
 *
 * @param array $set_data values array
 * @return string database construction for creating set
 */
function fn_create_set($set_data = array())
{
    return empty($set_data) ? '' : implode(',', $set_data);
}

/**
 * Connect to database server and select database
 */
function db_initiate(string $host, string $user, string $password, string $name, int $port = 3306): bool
{
    return Database::connect($user, $password, $host, $name, $port);
}

/**
 * Get the number of found rows from the last query
 *
 */
function db_get_found_rows()
{
    return Database::getField("SELECT FOUND_ROWS()");
}

/**
 * Get auto increment value for table
 *
 * @param string $table - database table
 * @return integer - auto increment value
 */
function db_get_next_auto_increment_id($table)
{
    $table_status = Database::getRow("SHOW TABLE STATUS LIKE '?:$table'");

    return !empty($table_status['Auto_increment'])? $table_status['Auto_increment'] : $table_status['AUTO_INCREMENT'];
}

/**
 * Sort query results
 *
 * @param array $params sort params
 * @param array $sortings available sortings
 * @param string $default_by default sort field
 * @param string $default_by default order
 * @return string SQL substring
 */
function db_sort(&$params, $sortings, $default_by = '', $default_order = '')
{
    $directions = array (
        'asc' => 'desc',
        'desc' => 'asc',
        'descasc' => 'ascdesc', // when sorting by 2 fields
        'ascdesc' => 'descasc' // when sorting by 2 fields
    );

    if (empty($params['sort_order']) || empty($directions[$params['sort_order']])) {
        $params['sort_order'] = $default_order;
    }

    if (empty($params['sort_by']) || empty($sortings[$params['sort_by']])) {
        $params['sort_by'] = $default_by;
    }

    $params['sort_order_rev'] = $directions[$params['sort_order']];

    if (is_array($sortings[$params['sort_by']])) {
        if ($params['sort_order'] == 'descasc') {
            $order = implode(' desc, ', $sortings[$params['sort_by']]) . ' asc';
        } elseif ($params['sort_order'] == 'ascdesc') {
            $order = implode(' asc, ', $sortings[$params['sort_by']]) . ' desc';
        } else {
            $order = implode(' ' . $params['sort_order'] . ', ', $sortings[$params['sort_by']]) . ' ' . $params['sort_order'];
        }
    } else {
        $order = $sortings[$params['sort_by']] . ' ' . $params['sort_order'];
    }

    return ' ORDER BY ' . $order;
}

/**
 * Paginate query results
 *
 * @param int $page page number
 * @param int $items_per_page items per page
 * @return string SQL substring
 */
function db_paginate($page, $items_per_page)
{
    $page = intval($page);
    $page = $page > 0 ? $page : 1;
    $items_per_page = intval($items_per_page);

    return ' LIMIT ' . (($page - 1) * $items_per_page) . ', ' . $items_per_page;
}
