<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use GuzzleHttp\Psr7\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Tygh\Bootstrap;
use Tygh\Registry;
use Wizacha\Cscart\Http;

/**
 * Normalize path: remove "../", "./" and duplicated slashes
 *
 * @param string $path
 * @param string $separator
 * @return string normilized path
 */
function fn_normalize_path($path, $separator = '/')
{

    $result = array();
    $path = preg_replace("/[\\\\\/]+/S", $separator, $path);
    $path_array = explode($separator, $path);
    if (!$path_array[0]) {
        $result[] = '';
    }

    foreach ($path_array as $key => $dir) {
        if ($dir == '..') {
            if (end($result) == '..') {
               $result[] = '..';
            } elseif (!array_pop($result)) {
               $result[] = '..';
            }
        } elseif ($dir != '' && $dir != '.') {
            $result[] = $dir;
        }
    }

    if (!end($path_array)) {
        $result[] = '';
    }

    return fn_is_empty($result) ? '' : implode($separator, $result);
}

/**
 * Create directory wrapper. Allows to create included directories
 *
 * @param string $dir
 * @param int $perms permission for new directory
 * @return array List of directories
 */
function fn_mkdir($dir, $perms = DEFAULT_DIR_PERMISSIONS)
{
    return \Wizacha\Cscart\Fs::fn_mkdir($dir, $perms);
}

/**
 * Get MIME type by the file name
 *
 * @param string $filename
 * @param string $not_available_result MIME type that will be returned in case all checks fail
 * @return string $file_type MIME type of the given file.
 */
function fn_get_file_type($filename, $not_available_result = 'application/octet-stream')
{
    $file_type = $not_available_result;

    static $types = array (
        'zip' => 'application/zip',
        'tgz' => 'application/tgz',
        'rar' => 'application/rar',
        'gz'  => 'application/gzip',

        'exe' => 'application/exe',
        'com' => 'application/com',
        'bat' => 'application/bat',

        'png' => 'image/png',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'gif' => 'image/gif',
        'bmp' => 'image/bmp',
        'ico' => 'image/x-icon',
        'swf' => 'application/x-shockwave-flash',
        'webp' => 'image/webp',

        'csv' => 'text/csv',
        'txt' => 'text/plain',
        'doc' => 'application/msword',
        'xls' => 'application/vnd.ms-excel',
        'ppt' => 'application/vnd.ms-powerpoint',
        'pdf' => 'application/pdf',

        'xml' => 'text/xml',

        'css' => 'text/css',
        'js' => 'text/javascript'
    );

    $ext = strtolower(fn_get_file_ext($filename));

    if (!empty($types[$ext])) {
        $file_type = $types[$ext];
    }

    return $file_type;
}

/**
 * Function tries to get MIME type by different ways.
 *
 * @param string $filename Full path with name to file
 * @param boolean $check_by_extension Try to get MIME type by extension of the file
 * @param string $not_available_result MIME type that will be returned in case all checks fail
 * @return string MIME type of the given file.
 */
function fn_get_mime_content_type($filename, $check_by_extension = true, $not_available_result = 'application/octet-stream')
{

    $guesser = new \Symfony\Component\Mime\FileBinaryMimeTypeGuesser();
    $type = $guesser->guessMimeType($filename);
    if (empty($type) && class_exists('finfo')) {
        $finfo_handler = @finfo_open(FILEINFO_MIME);
        if ($finfo_handler !== false) {
            $type = @finfo_file($finfo_handler, $filename);
            list($type) = explode(';', $type);
            @finfo_close($finfo_handler);
        }
    }

    if (empty($type) && function_exists('mime_content_type')) {
        $type = @mime_content_type($filename);
    }

    if (empty($type) && $check_by_extension && strpos(fn_basename($filename), '.') !== false) {
        $type = fn_get_file_type(fn_basename($filename), $not_available_result);
    }

    return !empty($type) ? $type : $not_available_result;
}

/**
 * Create temporary file for uploaded file
 *
 * @param $val file path
 * @return array $val
 */
function fn_get_server_data($val)
{
    if (strpos($val, Registry::get('config.dir.root')) === 0) {
        $val = substr_replace($val, '', 0, strlen(Registry::get('config.dir.root')));
    }

    setlocale(LC_ALL, 'en_US.UTF8');
    $path = fn_normalize_path(Registry::get('config.dir.root') . '/' . $val);
    $result = false;
    if (file_exists($path)) {

        $result = array(
            'name' => fn_basename($val),
            'path' => $path
        );

        $tempfile = fn_create_temp_file();
        fn_copy($result['path'], $tempfile);
        $result['path'] = $tempfile;
        $result['size'] = filesize($result['path']);

        $cache = Registry::get('temp_fs_data');

        if (!isset($cache[$result['path']])) { // cache file to allow multiple usage
            $cache[$result['path']] = $tempfile;
            Registry::set('temp_fs_data', $cache);
        }
    }

    return $result;
}

/**
 * Rebuilds $_FILES array to more user-friendly look
 *
 * @param string $name Name of file parameter
 * @return array Rebuilt file array
 */
function fn_rebuild_files($name)
{
    $rebuilt = array();

    if (!is_array(@$_FILES[$name])) {
        return $rebuilt;
    }

    if (isset($_FILES[$name]['error'])) {
        if (!is_array($_FILES[$name]['error'])) {
            return $_FILES[$name];
        }
    } elseif (fn_is_empty($_FILES[$name]['size'])) {
        return $_FILES[$name];
    }

    foreach ($_FILES[$name] as $k => $v) {
        if ($k == 'tmp_name') {
            $k = 'path';
        }
        $rebuilt = fn_array_multimerge($rebuilt, $v, $k);
    }

    return $rebuilt;
}

/**
 * Recursively copy directory (or just a file)
 *
 * @param string $source
 * @param string $dest
 * @param bool $silent
 * @param array $exclude_files
 * @return bool True on success, false otherwise
 */
function fn_copy($source, $dest, $silent = true, $exclude_files = array())
{
    // Simple copy for a file
    if (is_file($source)) {
        $source_file_name = fn_basename($source);
        if (in_array($source_file_name, $exclude_files)) {
            return true;
        }
        if (@is_dir($dest)) {
            $dest .= '/' . $source_file_name;
        }
        if (filesize($source) == 0) {
            $fd = fopen($dest, 'w');
            fclose($fd);
            $res = true;
        } else {
            $res = @copy($source, $dest);
        }
        @chmod($dest, DEFAULT_FILE_PERMISSIONS);
        clearstatcache(true, $dest);

        return $res;
    }

    // Make destination directory
    if ($silent == false) {
        $_dir = strpos($dest, Registry::get('config.dir.root')) === 0 ? str_replace(Registry::get('config.dir.root') . '/', '', $dest) : $dest;
        fn_set_progress('echo', $_dir);
    }

    if (!fn_mkdir($dest)) {
        return false;
    }

    // Loop through the folder
    if (@is_dir($source)) {
        $dir = dir($source);
        while (false !== $entry = $dir->read()) {
            // Skip pointers
            if ($entry == '.' || $entry == '..') {
                continue;
            }

            // Deep copy directories
            if ($dest !== $source . '/' . $entry) {
                if (fn_copy($source . '/' . $entry, $dest . '/' . $entry, $silent, $exclude_files) == false) {
                    return false;
                }
            }
        }

        // Clean up
        $dir->close();

        return true;
    } else {
        return false;
    }
}

/**
 * Recursively remove directory (or just a file)
 *
 * @param string $source
 * @param bool $delete_root
 * @param string $pattern
 * @return bool
 */
function fn_rm($source, $delete_root = true, $pattern = '')
{
    // Simple copy for a file
    if (is_file($source)) {
        $res = true;
        if (empty($pattern) || (!empty($pattern) && preg_match('/' . $pattern . '/', fn_basename($source)))) {
            $res = @unlink($source);
        }

        return $res;
    }

    // Loop through the folder
    if (is_dir($source)) {
        $dir = dir($source);
        while (false !== $entry = $dir->read()) {
            // Skip pointers
            if ($entry == '.' || $entry == '..') {
                continue;
            }
             if (fn_rm($source . '/' . $entry, true, $pattern) == false) {
                return false;
            }
        }
        // Clean up
        $dir->close();

        return ($delete_root == true && empty($pattern)) ? @rmdir($source) : true;
    } else {
        return false;
    }
}

/**
 * Get file extension
 *
 * @param string $filename
 * @return string File extension
 */
function fn_get_file_ext($filename)
{
    return pathinfo($filename, PATHINFO_EXTENSION);
}

/**
 * Get directory contents
 *
 * @param string $dir directory path
 * @param bool $get_dirs get sub directories
 * @param bool $get_files
 * @param mixed $extension allowed file extensions
 * @param string $prefix file/dir path prefix
 * @return array $contents directory contents
 */
function fn_get_dir_contents($dir, $get_dirs = true, $get_files = false, $extension = '', $prefix = '', $recursive = false)
{

    $contents = array();
    if (is_dir($dir)) {
        if ($dh = opendir($dir)) {

            // $extention - can be string or array. Transform to array.
            $extension = is_array($extension) ? $extension : array($extension);

            while (($file = readdir($dh)) !== false) {
                if ($file == '.' || $file == '..') {
                    continue;
                }

                if ($recursive == true && is_dir($dir . '/' . $file)) {
                    $contents = fn_array_merge($contents, fn_get_dir_contents($dir . '/' . $file, $get_dirs, $get_files, $extension, $prefix . $file . '/', $recursive), false);
                }

                if ((is_dir($dir . '/' . $file) && $get_dirs == true) || (is_file($dir . '/' . $file) && $get_files == true)) {
                    if ($get_files == true && !fn_is_empty($extension)) {
                        // Check all extentions for file
                        foreach ($extension as $_ext) {
                             if (substr($file, -strlen($_ext)) == $_ext) {
                                $contents[] = $prefix . $file;
                                break;
                             }
                        }
                    } else {
                        $contents[] = $prefix . $file;
                    }
                }
            }
            closedir($dh);
        }
    }

    asort($contents, SORT_STRING);

    return $contents;
}

/**
 * Get file contents from local or remote filesystem
 *
 * @param string $location file location
 * @param string $base_dir
 * @return string $result
 */
function fn_get_contents($location, $base_dir = '')
{
    $result = '';
    $path = $base_dir . $location;

    if (!empty($base_dir) && !fn_check_path($path)) {
        return $result;
    }

    // Location is regular file
    if (is_file($path)) {
        $result = @file_get_contents($path);

    // Location is url
    } elseif (strpos($path, '://') !== false) {
        $response = Http::get($path);

        if ($response instanceof Response) {
            $result = (string) $response->getBody();
        }
    }

    return $result;
}


/**
 * Write a string to a file
 *
 * @param string $location file location
 * @param string $content
 * @param string $base_dir
 * @param int $file_perm File access permissions for setting after writing into the file. For example 0666.
 * @param boolean $append append content if set to true
 * @return string $result
 */
function fn_put_contents($location, $content, $base_dir = '', $file_perm = DEFAULT_FILE_PERMISSIONS, $append = false)
{
    $result = '';
    $path = $base_dir . $location;

    if (!empty($base_dir) && !fn_check_path($path)) {
        return false;
    }

    fn_mkdir(dirname($path));

    $flags = 0;
    if ($append == true) {
        $flags = FILE_APPEND;
    }

    // Location is regular file
    $result = @file_put_contents($path, $content, $flags);
    if ($result !== false) {
        @chmod($path, $file_perm);
    }

    return $result;
}

/**
 * Add http scheme if missing from url
 * //foo/bar becomes http://foo/bar
 *
 * @param string $val
 * @return string
 */
function fn_get_real_url($val)
{
    if (!preg_match('/:\/\//', $val)) {
        if (substr($val, 0, 2) == '//') {
            $val = 'http:' . $val;
        } else {
            $val = 'http://' . $val;
        }
    }

    return $val;
}

/**
 * Get data from url
 *
 * @param string $val
 * @return array|false $val
 */
function fn_get_url_data($val, $try_to_gzdecode = false)
{
    $val = fn_get_real_url($val);

    $result = false;

    $response = Http::get($val, $try_to_gzdecode);

    if ($response instanceof Response) {
        $_data = (string) $response->getBody();

        $result = array(
            'name' => fn_basename($val)
        );

        // Check if the file is dynamically generated
        if (strpos($result['name'], '&') !== false || strpos($result['name'], '?') !== false) {
            $filename = null;

            if (\preg_match(
                    '/filename="?(?<filename>[^"]+)"?/',
                    \current(
                        $response->getHeader('Content-Disposition')
                    ),
                    $matches
                )
            ) {
                $filename = $matches['filename'];
            }

            // No value given by Content-Disposition
            if ($filename === null) {
                $filenameWithoutQueryString = substr($result['name'], 0, strpos($result['name'], '?'));
                if (strlen(pathinfo($filenameWithoutQueryString, PATHINFO_EXTENSION)) === 3) {
                    // Seems to be a real filename, use it
                    $filename = $filenameWithoutQueryString;
                } else {
                    // Use a fallback :(
                    $filename = 'url_uploaded_file_' . uniqid(TIME);
                }
            }

            $result['name'] = $filename;
        }
        $result['path'] = fn_create_temp_file();
        $result['size'] = strlen($_data);

        $fd = fopen($result['path'], 'wb');
        fwrite($fd, $_data, $result['size']);
        fclose($fd);
        @chmod($result['path'], DEFAULT_FILE_PERMISSIONS);

        $cache = Registry::get('temp_fs_data');

        if (!isset($cache[$result['path']])) { // cache file to allow multiple usage
            $cache[$result['path']] = $result['path'];
            Registry::set('temp_fs_data', $cache);
        }
    }

    return $result;
}

/**
 * Function get local uploaded
 *
 * @param array $val
 * @staticvar array $cache
 * @return array
 */
function fn_get_local_data($val)
{
    $cache = Registry::get('temp_fs_data');

    if (!isset($cache[$val['path']])) { // cache file to allow multiple usage
        $tempfile = fn_create_temp_file();
        if (move_uploaded_file($val['path'], $tempfile) == true) {
            @chmod($tempfile, DEFAULT_FILE_PERMISSIONS);
            $cache[$val['path']] = $tempfile;
        } else {
            $cache[$val['path']] = '';
        }

        Registry::set('temp_fs_data', $cache);
    }

    if (defined('KEEP_UPLOADED_FILES')) {
        $tempfile = fn_create_temp_file();
        fn_copy($cache[$val['path']], $tempfile);
        $val['path'] = $tempfile;
    } else {
        $val['path'] = $cache[$val['path']];
    }

    return !empty($val['size']) ? $val : false;
}

/**
 * Filter data from file uploader
 * @param string $name
 * @param array $filter_by_ext
 * @param bool $check_files
 * @return array
 * @throws Exception
 */
function fn_filter_uploaded_data($name, $filter_by_ext = array(), $check_files = true)
{
    $imagesStorage = container()->get('Wizacha\Storage\CustomFilesStorageService');
    $image_manager = new \Wizacha\ImageManager($imagesStorage);
    $udata_local = fn_rebuild_files('file_' . $name);
    $udata_other = !empty($_REQUEST['file_' . $name]) ? $_REQUEST['file_' . $name] : array();
    $utype = !empty($_REQUEST['type_' . $name]) ? $_REQUEST['type_' . $name] : array();

    if (empty($utype)) {
        return array();
    }

    $filtered = array();

    foreach ($utype as $id => $type) {
        if ($type == 'local' && !fn_is_empty(@$udata_local[$id])) {
            $filtered[$id] = fn_get_local_data(Bootstrap::stripSlashes($udata_local[$id]));
        } elseif ($type == 'server' && !fn_is_empty(@$udata_other[$id]) && AREA == 'A') {
            $filtered[$id] = fn_get_server_data($udata_other[$id]);
        } elseif ($type == 'url' && !fn_is_empty(@$udata_other[$id])) {
            $filtered[$id] = $image_manager->getUrlData($udata_other[$id]);
            if ($filtered[$id] === false) {
                fn_set_notification('E', __('error'), __('cant_upload_file_not_fqdn_domain'));
            }
        }

        if (isset($filtered[$id]) && $filtered[$id] === false) {
            unset($filtered[$id]);
            fn_set_notification('E', __('error'), __('cant_upload_file'));
        }

        if (!empty($filtered[$id]) && is_array($filtered[$id]) && !empty($filtered[$id]['name'])) {
            $filtered[$id]['name'] = str_replace(' ', '_', urldecode($filtered[$id]['name'])); // replace spaces with underscores
            $ext = fn_get_file_ext($filtered[$id]['name']);

            if ($check_files && !empty($filter_by_ext) && !in_array(fn_strtolower($ext), $filter_by_ext)) {
                unset($filtered[$id]);
                fn_set_notification('E', __('error'), __('text_not_allowed_to_upload_file_extension', array(
                    '[ext]' => $ext
                )));

            } elseif ($check_files && in_array(fn_strtolower($ext), Registry::get('config.forbidden_file_extensions'))) {
                unset($filtered[$id]);

                fn_set_notification('E', __('error'), __('text_forbidden_file_extension', array(
                    '[ext]' => $ext
                )));
            }

        }

        if ($check_files && !empty($filtered[$id]['path']) && in_array(fn_get_mime_content_type($filtered[$id]['path'], true, 'text/plain'), Registry::get('config.forbidden_mime_types'))) {
            fn_set_notification('E', __('error'), __('text_forbidden_file_mime', array(
                '[mime]' => fn_get_mime_content_type($filtered[$id]['path'], true, 'text/plain')
            )));
            unset($filtered[$id]);
        }
    }

    return $filtered;
}

/**
 * Remove temporary files
 */
function fn_remove_temp_data()
{
    \Wizacha\Misc::removeTempData(\Wizacha\Registry::defaultInstance());
}

/**
 * Create temporary file
 *
 * @param string $ext
 * @return string temporary file
 */
function fn_create_temp_file($ext = '')
{
    $config_dir_cache_misc = Registry::get('config.dir.cache_misc');
    fn_mkdir($config_dir_cache_misc . 'tmp');
    $finalName = $tmpnam = tempnam($config_dir_cache_misc . 'tmp/', 'tmp_');
    if (strlen($ext) > 0) {
        $finalName = $tmpnam . '.' . $ext;
        rename($tmpnam, $finalName);
    }
    // On supprime le fichier temporaire en fin de requête
    \Wizacha\Misc::addFileToDeleteInCleanup($finalName, \Wizacha\Registry::defaultInstance());

    return $finalName;
}

/**
 * Check path to file
 *
 * @param string $path
 * @return bool
 */
function fn_check_path($path)
{
    $real_path = realpath($path);

    return str_replace('\\', '/', $real_path) == $path ? true : false;
}

/**
 * Wrapper for rename with chmod
 *
 * @param string $oldname The old name. The wrapper used in oldname must match the wrapper used in newname.
 * @param string $newname The new name.
 * @param resource $context Note: Context support was added with PHP 5.0.0. For a description of contexts, refer to Stream Functions.
 *
 * @return boolean Returns TRUE on success or FALSE on failure.
 */
function fn_rename($oldname, $newname, $context = null)
{
    $result = ($context === null) ? rename($oldname, $newname) : rename($oldname, $newname, $context);
    if ($result !== false) {
        @chmod($newname, is_dir($newname) ? DEFAULT_DIR_PERMISSIONS : DEFAULT_FILE_PERMISSIONS);
    }

    return $result;
}

/*
 * Returns pathinfo with using UTF characters.
 *
 * @param string $path
 * @param string $encoding
 * @return array
 */
function fn_pathinfo($path, $encoding = 'UTF-8')
{
    $basename = explode("/", $path);
    $basename = end($basename);

    if (strpos($path, '/') === false) {
        $path = './' . $path;
    }

    $dirname = rtrim(fn_substr($path, 0, fn_strlen($path, $encoding) - fn_strlen($basename, $encoding) - 1, $encoding), '/');
    $dirname .= empty($dirname) ? '/' : '';

    if (strpos($basename, '.') !== false) {
        $_name_components = explode('.', $basename);
        $extension = array_pop($_name_components);
        $filename = implode('.', $_name_components);
    } else {
        $extension = '';
        $filename = $basename;
    }

    return array (
        'dirname' => $dirname,
        'basename' => $basename,
        'extension' => $extension,
        'filename' => $filename
    );
}

/*
 * Returns basename with using UTF characters.
 *
 * @param string $path
 * @param string $suffix
 * @param string $encoding
 * @return string
 */
function fn_basename($path, $suffix = '', $encoding = 'UTF-8')
{
    $basename = explode("/", $path);
    $basename = end($basename);

    if (!empty($suffix) && fn_substr($basename, (0 - fn_strlen($suffix, $encoding)), fn_strlen($basename, $encoding), $encoding) == $suffix) {
        $basename = fn_substr($basename, 0, (0 - fn_strlen($suffix, $encoding)), $encoding);
    }

    return $basename;
}
