<?php
/**
 * Smarty plugin
 * @package Smarty
 * @subpackage plugins
 */

/**
 * Smarty plugin
 * -------------------------------------------------------------
 * Type:     modifier<br>
 * Name:     sort_by<br>
 * Purpose:  allows arrays of named arrays to be sorted by a given field
 * Example:  {$fields|@sort_by:"text"}
 * ------
 *
 *  -------------------------------------------------------
 */

//
// Modifier: sortby -
//
function smarty_modifier_sort_by($arrData, $sortfields)
{
    array_sort_by_fields($arrData ,$sortfields);

    return $arrData;
}

function array_sort_by_fields(&$data, $sortby)
{
    uasort($data, function($a, $b) use ($sortby) {
        return strcasecmp($a[$sortby], $b[$sortby]);
    });
}
