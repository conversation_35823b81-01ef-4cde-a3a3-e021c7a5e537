<?php

use Tygh\Registry;

/**
 * Smarty plugin
 * @package Smarty
 * @subpackage plugins
 */
function smarty_block_scripts($params, $content, &$smarty, &$repeat)
{
    $version = container()->getParameter('marketplace.version');

    if ($repeat == true) {
        return;
    }

    $scripts = array();
    $return = '';

    if (preg_match_all('/\<script(.*?)\>(.*?)\<\/script\>/s', $content, $m)) {
        $contents = '';

        foreach ($m[1] as $src) {
            if (!empty($src) && preg_match('/src ?= ?"([^"]+)"/', $src, $_m)) {
                $scripts[] = str_replace(Registry::get('config.current_location'), '', preg_replace('/\?.*?$/', '', $_m[1]));
            }
        }

        $area = AREA;
        $filename = "js/tygh/scripts-{$version}-{$area}.js";
        $staticsStorage = container()->get('Wizacha\Storage\StaticsStorageService');
        if (!$staticsStorage->isExist($filename)) {

            foreach ($scripts as $src) {
                $contents .= fn_get_contents(Registry::get('config.dir.root') . $src);
            }
            $contents = JShrink\Minifier::minify($contents, ['flaggedComments' => false]);

            $staticsStorage->put($filename, array(
                'contents' => $contents,
                'caching' => true
            ));
        }

        $return = '<script type="text/javascript" src="' . $staticsStorage->getUrl($filename) . '?ver=' . $version . '"></script>';

        foreach ($m[2] as $sc) {
            if (!empty($sc)) {
                $return .= '<script type="text/javascript">' . $sc . '</script>' . "\n";
            }
        }
    }

    return $return;
}
