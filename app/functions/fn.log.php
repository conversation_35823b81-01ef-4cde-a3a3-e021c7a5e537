<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Psr\Log\LoggerInterface;
use Tygh\Registry;

fn_define('LOG_MAX_DATA_LENGTH', 10000);

/**
 * Comment utiliser :
 *
 *     log_unused_code(__FILE__, __LINE__);
 */
function log_unused_code($file, $line)
{
    /** @var LoggerInterface $logger */
    $logger = container()->get('logger');
    $logger->warning('Code that we thought was unused is actually used: ' . $file . ' line ' . $line);

    assert(false, 'Code is used. Remove log_unused_code'); // In tests: die. We have to fix this call before merge the PR
}

function fn_log_event($type, $action, $data = array(), \Psr\Log\LoggerInterface $logger = null)
{
    $logger = $logger ?: container()->get('logger');
    $content = array();

    $actions = Registry::get('settings.Logging.log_type_' . $type);

    $cut_log = Registry::ifGet('log_cut', false);
    Registry::del('log_cut');

    $cut_data = Registry::ifGet('log_cut_data', false);
    Registry::del('log_cut_data');

    if (empty($actions) || ($action && !empty($actions) && empty($actions[$action])) || !empty($cut_log)) {
        return false;
    }

    if (!empty($_SESSION['auth']['user_id'])) {
        $user_id = $_SESSION['auth']['user_id'];
    } else {
        $user_id = 0;
    }

    if ($type == 'users' && $action == 'logout' && !empty($data['user_id'])) {
        $user_id = $data['user_id'];
    }

    $event_type = 'N'; // notice

    if (!empty($data['backtrace'])) {
        $_btrace = array();
        $func = '';
        foreach (array_reverse($data['backtrace']) as $v) {
            if (empty($v['file'])) {
                $func = $v['function'];
                continue;
            } elseif (!empty($func)) {
                $v['function'] = $func;
                $func = '';
            }

            $_btrace[] = array(
                'file' => !empty($v['file']) ? $v['file'] : '',
                'line' => !empty($v['line']) ? $v['line'] : '',
                'function' => $v['function'],
            );
        }

        $data['backtrace'] = serialize($_btrace);
    } else {
        $data['backtrace'] = '';
    }

    if ($type == 'general') {
        if ($action == 'deprecated') {
            $content['deprecated_function'] = $data['function'];
        }
        $content['message'] = $data['message'];
    } elseif ($type == 'orders') {

        $order_status_descr = fn_get_simple_statuses(STATUSES_ORDER, true, true);

        $content = array (
            'order' => '# ' . $data['order_id'],
            'id' => $data['order_id'],
        );

        if ($action == 'status') {
            $content['status'] = $order_status_descr[$data['status_from']] . ' -> ' . $order_status_descr[$data['status_to']];
        }

    } elseif ($type == 'products') {

        $product = db_get_field("SELECT product FROM ?:product_descriptions WHERE product_id = ?i AND lang_code = ?s", $data['product_id'], Registry::get('settings.Appearance.backend_default_language'));
        $content = array (
            'product' => $product . ' (#' . $data['product_id'] . ')',
            'id' => $data['product_id'],
        );

        if ($action == 'low_stock') { // log stock - warning
            $event_type = 'W';
        }

    } elseif ($type == 'categories') {

        $category = db_get_field("SELECT category FROM ?:category_descriptions WHERE category_id = ?i AND lang_code = ?s", $data['category_id'], Registry::get('settings.Appearance.backend_default_language'));
        $content = array (
            'category' => $category . ' (#' . $data['category_id'] . ')',
            'id' => $data['category_id'],
        );

    } elseif ($type == 'database') {
        if ($action == 'error') {
            $content = array (
                'error' => $data['error']['message'],
                'query' => $data['error']['query'],
            );
            $event_type = 'E';
        }

    } elseif ($type == 'requests') {

        if (!empty($cut_data)) {
            $data['data'] = preg_replace("/\<(" . implode('|', $cut_data) . ")\>(.*?)\<\/(" . implode('|', $cut_data) . ")\>/s", '<${1}>******</${1}>', $data['data']);
            $data['data'] = preg_replace("/(" . implode('|', $cut_data) . ")=(.*?)(&)/s", '${1}=******${3}', $data['data']);
        }

        $content = array (
            'url' => $data['url'],
            'request' => (fn_strlen($data['data']) < LOG_MAX_DATA_LENGTH && preg_match('//u',$data['data'])) ? $data['data'] : '',
            'response' => (fn_strlen($data['response']) < LOG_MAX_DATA_LENGTH && preg_match('//u',$data['response'])) ? $data['response'] : '',
        );

    } elseif ($type == 'users') {

        if (!empty($data['user_id'])) {
            $info = db_get_row("SELECT firstname, lastname, email FROM ?:users WHERE user_id = ?i", $data['user_id']);
            $content = array (
                'user' => $info['firstname'] . ($info['firstname'] && $info['lastname'] ? ' ' : '' ) . $info['lastname'] . ($info['firstname'] || $info['lastname'] ? '; ' : '' ) . $info['email'] . ' (#' . $data['user_id'] . ')',
            );
            $content['id'] = $data['user_id'];

        } elseif (!empty($data['user'])) {
            $content = array (
                'user' => $data['user'],
            );
        }

        if (in_array($action, array ('session', 'failed_login'))) {
            $ip = fn_get_ip();
            $content['ip_address'] = empty($data['ip']) ? $ip['host'] : $data['ip'];
        }

        if ($action == 'failed_login') { // failed login - warning
            $event_type = 'W';
        }
    }

    if (Registry::get('runtime.company_id')) {
        $company_id = Registry::get('runtime.company_id');

    } elseif (!empty($data['company_id'])) {
        $company_id = $data['company_id'];

    } else {
        $company_id = 0;
    }

    $context = array (
        'user_id' => $user_id,
        'timestamp' => TIME,
        'content' => $content,
        'backtrace' => $data['backtrace'],
        'company_id' => $company_id,
    );
    $msg = "$type.$action";

    switch($event_type) {
        case 'E':
            $logger->error($msg, $context);
            break;
        case 'W':
            $logger->warning($msg, $context);
            break;
        case 'N':
            $logger->notice("NOTICE_DEBUG::" . $msg . print_r(debug_backtrace(2),true), $context);
            break;
        default:
            $logger->critical($msg, $context);
    }

    return true;
}

/**
 * Add a record to the log if the user session is expired
 *
 * @param array $entry - session record
 * @return bool Always true
 */
function fn_log_user_logout($entry, $data)
{
    if (!empty($data['auth']) && $data['auth']['user_id']) {
        $this_login = empty($data['auth']['this_login']) ? 0 : $data['auth']['this_login'];

        // Log user logout
        fn_log_event('users', 'session', array (
            'user_id' => $data['auth']['user_id'],
            'ip' => empty($data['auth']['ip']) ? '' : $data['auth']['ip'],
            'time' => ($entry['expiry'] - $this_login),
            'timeout' => true,
            'expiry' => $entry['expiry'],
            'company_id' => fn_get_company_id('users', 'user_id', $data['auth']['user_id']),
        ));
    }

    return true;
}
