<?php
/***************************************************************************
 *                                                                          *
 *   (c) 2004 <PERSON>, <PERSON>ey <PERSON>, Ilya M<PERSON> Shalnev    *
 *                                                                          *
 * This  is  commercial  software,  only  users  who have purchased a valid *
 * license  and  accept  to the terms of the  License Agreement can install *
 * and use this program.                                                    *
 *                                                                          *
 ****************************************************************************
 * PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
 * "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
 ****************************************************************************/

use Tygh\BlockManager\Block;
use Tygh\DatabaseForeignKeyConstraintException;
use Tygh\Languages\Languages;
use Tygh\Navigation\LastView;
use Tygh\Registry;
use Wizacha\AppBundle\Controller\Api\Division\Dto\ApiDivisionSettingsDto;
use Wizacha\Component\Locale\Locale;
use Wizacha\Core\Concurrent\MutexService;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Division\ProductDivisionSettings;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Attribute\AttributeService;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService;
use Wizacha\Marketplace\PIM\Option\CannotDeleteOptionVariantException;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Marketplace\PIM\Product\Template\TemplateService;
use Wizacha\Product;
use Wizacha\Option;
use Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory;
use Wizacha\Marketplace\PriceTier\PriceTier;
use Wizacha\Marketplace\PriceTier\Service\PriceTierService;
use Wizacha\Money\Money;
use Wizacha\ProductManager;
use Wizacha\Shipping;
use Wizacha\Status;
use Wizacha\User;

/**
 * Gets full product data by its id
 *
 * @param int $product_id Product ID
 * @param mixed $auth Array with authorization data
 * @param string $lang_code 2 letters language code
 * @param string $field_list List of fields for retrieving
 * @param boolean $get_add_pairs Get additional images
 * @param boolean $get_main_pair Get main images
 * @param boolean $get_taxes Get taxes
 * @param boolean $get_qty_discounts Get quantity discounts
 * @param boolean $preview Is product previewed by admin
 * @param boolean $features Get product features
 * @param boolean $skip_company_condition Skip company condition and retrieve product data for displayin on other store page. (Works only in ULT)
 * @return array|bool Array with product data
 */
function fn_get_product_data($product_id, &$auth, $lang_code = null, $field_list = '', $get_add_pairs = true, $get_main_pair = true, $get_taxes = true, $get_qty_discounts = false, $preview = false, $features = true, $skip_company_condition = false, $feature_variants_selected_only = false, $sort_variants = true, $skipOptions = true)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    // CSV export has a cache: only the last product is kept, if the $product_id ID is different, the cache is reset (low memory consumption)
    static $productDataCacheExport;

    $product_id = intval($product_id);

    if (!empty($product_id)) {

        if (defined('CSV_EXPORT') && !empty($productDataCacheExport) && $productDataCacheExport['product_id'] == $product_id) {
            return $productDataCacheExport;
        }

        if (empty($field_list)) {
            $descriptions_list = "?:product_descriptions.*";
            $field_list = "?:products.*, $descriptions_list";
        }
        $field_list .= ", MIN(IF(?:product_prices.percentage_discount = 0, ?:product_prices.price, ?:product_prices.price - (?:product_prices.price * ?:product_prices.percentage_discount)/100)) as price";
        $field_list .= ", GROUP_CONCAT(IF(?:products_categories.link_type = 'M', CONCAT(?:products_categories.category_id, 'M'), ?:products_categories.category_id)) as category_ids";

        $_p_statuses = array('A', 'H');
        $_c_statuses = array('A', 'H');

        $condition = $join = $avail_cond = '';

        $avail_cond .= fn_get_company_condition('?:products.company_id');

        $avail_cond .= (AREA == 'C' && empty($preview)) ? db_quote(' AND ?:categories.status IN (?a) AND ?:products.status IN (?a)', $_c_statuses, $_p_statuses) : '';

        $avail_cond .= fn_get_localizations_condition('?:products.localization');

        if (AREA == 'C') {
            $join .= " LEFT JOIN ?:companies as companies ON companies.company_id = ?:products.company_id";
            if (!$preview) {
                $field_list .= ', companies.company as company_name';
                $condition .= " AND (companies.status = 'A' OR ?:products.company_id = 0) ";
            }
        }

        $join .= " INNER JOIN ?:products_categories ON ?:products_categories.product_id = ?:products.product_id INNER JOIN ?:categories ON ?:categories.category_id = ?:products_categories.category_id $avail_cond";

        //bestseller hook
        $product_category = db_get_field("SELECT category_id FROM ?:products_categories WHERE product_id = ?i AND link_type = 'M'", $product_id);
        $field_list .= ", ?:product_sales.amount as sales_amount";
        $join .= db_quote(" LEFT JOIN ?:product_sales ON ?:product_sales.product_id = ?:products.product_id AND ?:product_sales.category_id = ?i", $product_category);

        //discussion hook
        $field_list .= ", ?:discussion.type as discussion_type";
        $join .= " LEFT JOIN ?:discussion ON ?:discussion.object_id = CAST(?:products.product_id AS CHAR) AND ?:discussion.object_type = 'P'";

        //rma hook
        $field_list .= ", ?:products.is_returnable, ?:products.return_period";

        //seo hook
        $field_list .= ', ?:seo_names.name as seo_name';
        $join .= db_quote(
            " LEFT JOIN ?:seo_names ON ?:seo_names.object_id = ?s AND ?:seo_names.type = 'p' "
            . "AND ?:seo_names.dispatch = '' ",
            $product_id
        );

        //vendor data premoderation hook
        if (AREA == 'A') {
            $field_list .= ', companies.pre_moderation as company_pre_moderation';
            $field_list .= ', companies.pre_moderation_edit as company_pre_moderation_edit';
            if (strpos($join, '?:companies') === false) {
                $join .= ' LEFT JOIN ?:companies as companies ON companies.company_id = ?:products.company_id';
            }
        }

        //wizaplace hook
        if (AREA == 'C') {
            $field_list .= ', companies.w_company_type as w_company_type ';
        }

        $product_data = db_get_row("SELECT $field_list FROM ?:products LEFT JOIN ?:product_prices ON ?:product_prices.product_id = ?:products.product_id AND ?:product_prices.lower_limit = 1 LEFT JOIN ?:product_descriptions ON ?:product_descriptions.product_id = ?:products.product_id AND ?:product_descriptions.lang_code = ?s ?p WHERE ?:products.product_id = ?i ?p GROUP BY ?:products.product_id", $lang_code, $join, $product_id, $condition);

        if (empty($product_data)) {
            return false;
        }

        if (\is_null($product_data['price'])) {
            container()->get('logger')->error('null price', [
                'product_data' => $product_data,
                'product_prices' => db_get_array("SELECT * FROM ?:products LEFT JOIN ?:product_prices ON ?:product_prices.product_id = ?:products.product_id WHERE ?:products.product_id = ?i", $product_id)
            ]);
        }

        $product_data['base_price'] = $product_data['price']; // save base price (without discounts, etc...)

        list($product_data['category_ids'], $product_data['main_category']) = fn_convert_categories($product_data['category_ids']);

        // Generate meta description automatically
        if (!empty($product_data['full_description']) && empty($product_data['meta_description']) && defined('AUTO_META_DESCRIPTION') && AREA != 'A') {
            $product_data['meta_description'] = fn_generate_meta_description($product_data['full_description']);
        }

        // If tracking with options is enabled, check if at least one combination has positive amount
        if (!empty($product_data['tracking']) && $product_data['tracking'] == 'O') {
            $product_data['amount'] = db_get_field("SELECT MAX(amount) FROM ?:product_options_inventory WHERE product_id = ?i", $product_id);
        }

        $product_data['product_id'] = $product_id;

        // Get product shipping settings
        if (!empty($product_data['shipping_params'])) {
            $product_data = array_merge(unserialize($product_data['shipping_params']), $product_data);
        }

        // Get main image pair
        if ($get_main_pair == true) {
            $product_data['main_pair'] = fn_get_image_pairs($product_id, 'product', 'M', true, true, $lang_code);
        }

        // Get additional image pairs
        if ($get_add_pairs == true) {
            $product_data['image_pairs'] = fn_get_image_pairs($product_id, 'product', 'A', true, true, $lang_code);
        }

        // Get taxes
        $product_data['tax_ids'] = !empty($product_data['tax_ids']) ? explode(',', $product_data['tax_ids']) : array();

        // Get qty discounts
        if ($get_qty_discounts == true) {
            fn_get_product_prices($product_id, $product_data);
        }

        if ($features) {
            // Get product features

            $path = !empty($product_data['main_category']) ? explode('/', db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i", $product_data['main_category'])) : '';

            /*
             * If `display_on = product`, the function return the attributes for the product view in front page.
             * In the admin page or for the vendor of product, we should have all attributes.
             */
            if (AREA == 'A' || $product_data['company_id'] == $auth['company_id']) {
                $display_on = '';
            } else {
                $display_on = 'product';
            }

            $_params = array(
                'category_ids' => $path,
                'product_id' => $product_id,
                'product_company_id' => !empty($product_data['company_id']) ? $product_data['company_id'] : 0,
                'statuses' => AREA == 'C' ? array('A') : array('A', 'H'),
                'variants' => true,
                'plain' => false,
                'display_on' => $display_on,
                'existent_only' => (AREA != 'A' && !defined('CONSOLE')),
                'variants_selected_only' => $feature_variants_selected_only,
                'sort_variants' => $sort_variants,
            );
            list($product_data['product_features']) = fn_get_product_features($_params, 0, $lang_code);
            $product_data['header_features'] = fn_get_product_features_list($product_data, 'H');
        } else {
            $product_data['product_features'] = fn_get_product_features_list($product_data, 'A');
        }

        if ($skipOptions === false) {
            $product_options = fn_get_product_options([$product_id], (string) GlobalState::interfaceLocale());
            $product_data['options'] = Option::filterExceptions(
                $product_options[$product_id],
                Product::getExceptions($product_id)
            );
        }

        $product_data['video'] = container()->get('marketplace.pim.video_service')->findOneByProductId($product_id);
        $product_data['attachments'] = container()->get('marketplace.pim.product.service')->getAttachments($product_id);
        $product_data['divisions'] = container()->get('marketplace.division.products.service')->getAllDivisionsCode($product_id);

    } else {
        return false;
    }

    $product_data['detailed_params']['info_type'] = 'D';

    if (empty($product_data['seo_name']) && !empty($product_data['product_id'])) {
        $product_data['seo_name'] = fn_seo_get_name('p', $product_data['product_id'], '', null, $lang_code);
    }
    fn_w_set_shippings_product_data($product_data);
    if (AREA == 'C' && !$preview && isset($product_data['approved']) && $product_data['approved'] != 'Y' && $product_data['company_id'] != $auth['company_id']) {
        $product_data = array();
    }

    if (defined('CSV_EXPORT')) {
        $productDataCacheExport = $product_data;
    }

    return (!empty($product_data) ? $product_data : false);
}

function fn_get_product_category_id(int $product_id): ?int
{
    $result = db_get_field("SELECT category_id FROM ?:products_categories WHERE product_id = ?i AND link_type = 'M'", $product_id);

    return is_null($result) ? null : intval($result);
}

function fn_get_product_supplier_ref(int $product_id): ?string
{
    $result = db_get_field("SELECT w_supplier_ref FROM ?:products WHERE product_id = ?i", $product_id);

    return is_null($result) ? null : $result;
}
/**
 * Gets product name by id
 *
 * @param mixed $product_id Integer product id, or array of product ids
 * @param string $lang_code 2-letter language code
 * @param boolean $as_array Flag: if set, result will be returned as array <i>(product_id => product)</i>; otherwise only product name will be returned
 * @return mixed In case 1 <i>product_id</i> is passed and <i>as_array</i> is not set, a product name string is returned;
 * Array <i>(product_id => product)</i> for all given <i>product_ids</i>;
 * <i>False</i> if <i>$product_id</i> is not defined
 */
function fn_get_product_name($product_id, $lang_code = null, $as_array = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $result = false;
    if (!empty($product_id)) {
        if (!is_array($product_id) && strpos($product_id, ',') !== false) {
            $product_id = explode(',', $product_id);
        }

        $field_list = 'pd.product_id as product_id, pd.product as product';
        $join = '';
        if (is_array($product_id) || $as_array == true) {
            $condition = db_quote(' AND pd.product_id IN (?n) AND pd.lang_code = ?s', $product_id, $lang_code);
        } else {
            $condition = db_quote(' AND pd.product_id = ?i AND pd.lang_code = ?s', $product_id, $lang_code);
        }

        $result = db_get_memoized_hash_single_array("SELECT $field_list FROM ?:product_descriptions pd $join WHERE 1 $condition", array('product_id', 'product'));
        if (!(is_array($product_id) || $as_array == true)) {
            if (isset($result[$product_id])) {
                $result = $result[$product_id];
            } else {
                $result = null;
            }
        }
    }

    return $result;
}

/**
 * Gets product price by id
 *
 * @param int $product_id Product id
 * @param int $amount Optional parameter: necessary to calculate quantity discounts
 * @param array $auth Array of authorization data
 * @return float Price
 */
function fn_get_product_price(int $product_id, int $amount)
{
    $price = db_get_field(
        "SELECT MIN(IF(?:product_prices.percentage_discount = 0, ?:product_prices.price, "
        . "?:product_prices.price - (?:product_prices.price * ?:product_prices.percentage_discount)/100)) as price "
        . "FROM ?:product_prices "
        . "WHERE lower_limit <=?i AND ?:product_prices.product_id = ?i "
        . "ORDER BY lower_limit DESC LIMIT 1",
        $amount, $product_id
    );

    return (empty($price)) ? 0 : floatval($price);
}

/**
 * Do not use, too large for memory in production ; use a Generator instead
 * @param int $companyId
 * @return array
 * @deprecated
 */
function fn_find_all_product_ids_by_company_id(int $companyId): array
{
    $products = db_get_array("SELECT product_id FROM cscart_products WHERE company_id = ?i", $companyId);

    $productsIds = [];
    foreach ($products as $product) {
        $productsIds[] = \intval($product['product_id']);
    }

    return $productsIds;
}

/**
 * Do not use, too large for memory in production ; use a Generator instead
 * @return array
 * @deprecated
 */
function fn_find_all_product_ids(): array
{
    $products = db_get_array("SELECT product_id FROM cscart_products");

    $productsIds = [];
    foreach ($products as $product) {
        $productsIds[] = \intval($product['product_id']);
    }

    return $productsIds;
}

/**
 * Gets product descriptions to the given language
 *
 * @param array $products Array of products
 * @param string $fields List of fields to be translated
 * @param string $lang_code 2-letter language code.
 * @param boolean $translate_options Flag: if set, product options are also translated; otherwise not
 */
function fn_translate_products(&$products, $fields = '', $lang_code = '', $translate_options = false)
{
    if (empty($fields)) {
        $fields = 'product, short_description, full_description';
    }

    foreach ($products as $k => $v) {
        if (!empty($v['deleted_product'])) {
            continue;
        }
        $descriptions = db_get_row("SELECT $fields FROM ?:product_descriptions WHERE product_id = ?i AND lang_code = ?s", $v['product_id'], $lang_code);
        foreach ($descriptions as $k1 => $v1) {
            $products[$k][$k1] = $v1;
        }
        if ($translate_options && !empty($v['product_options'])) {
            foreach ($v['product_options'] as $k1 => $v1) {
                $option_descriptions = db_get_memoized_row("SELECT option_name, option_text, description, comment FROM ?:product_options_descriptions WHERE option_id = ?i AND lang_code = ?s", $v1['option_id'], $lang_code);
                foreach ($option_descriptions as $k2 => $v2) {
                    $products[$k]['product_options'][$k1][$k2] = $v2;
                }

                if ($v1['option_type'] == 'C') {
                    $products[$k]['product_options'][$k1]['variant_name'] = (empty($v1['position'])) ? __('no', '', $lang_code) : __('yes', '', $lang_code);
                } elseif ($v1['option_type'] == 'S' || $v1['option_type'] == 'R') {
                    $variant_description = db_get_memoized_field("SELECT variant_name FROM ?:product_option_variants_descriptions WHERE variant_id = ?i AND lang_code = ?s", $v1['value'], $lang_code);
                    $products[$k]['product_options'][$k1]['variant_name'] = $variant_description;
                }
            }
        }
    }
}

/**
 * Gets additional products data
 *
 * @param array $products Array with products
 * @param array $params Array of flags which determines which data should be gathered
 * @return array Array of products with additional information
 */
function fn_gather_additional_products_data(&$products, $params)
{
    if (empty($products)) {
        return;
    }

    // Set default values to input params
    $default_params = array(
        'get_icon' => false,
        'get_detailed' => false,
        'get_additional' => false,
        'get_options' => true,
        'get_discounts' => true,
        'get_features' => false,
        'get_extra' => false,
        'get_taxed_prices' => true,
        'get_for_one_product' => (!is_array(reset($products))) ? true : false,
        'detailed_params' => true,
        'features_display_on' => 'C',
        'tier_pricing' => container()->getParameter('feature.tier_pricing'),
    );

    $params = array_merge($default_params, $params);

    $auth = & $_SESSION['auth'];
    $allow_negative_amount = Registry::get('settings.General.allow_negative_amount');
    $inventory_tracking = Registry::get('settings.General.inventory_tracking');

    if ($params['get_for_one_product']) {
        $products = array($products);
    }

    $product_ids = array();
    foreach ($products as $v) {
        $product_ids[] = $v['product_id'];
    }

    if ($params['get_icon'] || $params['get_detailed']) {
        $products_images = fn_get_image_pairs($product_ids, 'product', 'M', $params['get_icon'], $params['get_detailed'], (string) GlobalState::interfaceLocale());
    }

    if ($params['get_additional']) {
        $additional_images = fn_get_image_pairs($product_ids, 'product', 'A', true, true, (string) GlobalState::interfaceLocale());
    }

    if ($params['get_options']) {
        $product_options = fn_get_product_options($product_ids, (string) GlobalState::interfaceLocale());
    } else {
        $has_product_options = db_get_memoized_hash_array("SELECT a.option_id, a.product_id FROM ?:product_options AS a WHERE a.product_id IN (?n) AND a.status = 'A'", 'product_id', $product_ids);
        $has_product_options_links = db_get_memoized_hash_array("SELECT c.option_id, c.product_id FROM ?:product_global_option_links AS c LEFT JOIN ?:product_options AS a ON a.option_id = c.option_id WHERE a.status = 'A' AND c.product_id IN (?n)", 'product_id', $product_ids);
    }

    // foreach $products
    foreach ($products as &$_product) {
        $product = $_product;
        $product_id = $product['product_id'];

        // Get images
        if ($params['get_icon'] == true || $params['get_detailed'] == true) {
            if (empty($product['main_pair']) && !empty($products_images[$product_id])) {
                $product['main_pair'] = reset($products_images[$product_id]);
            }
        }

        if ($params['get_additional'] == true) {
            if (empty($product['image_pairs']) && !empty($additional_images[$product_id])) {
                $product['image_pairs'] = $additional_images[$product_id];
            }
        }

        if (!isset($product['base_price'])) {
            $product['base_price'] = $product['price']; // save base price (without discounts, etc...)
        }

        // Convert product categories
        if (!empty($product['category_ids']) && !is_array($product['category_ids'])) {
            list($product['category_ids'], $product['main_category']) = fn_convert_categories($product['category_ids']);
        }

        $product['selected_options'] = empty($product['selected_options']) ? array() : $product['selected_options'];

        // Get product options
        if ($params['get_options'] && !empty($product_options[$product['product_id']])) {
            if (!isset($product['options_type']) || !isset($product['exceptions_type'])) {
                $types = db_get_row('SELECT options_type, exceptions_type FROM ?:products WHERE product_id = ?i', $product['product_id']);
                $product['options_type'] = $types['options_type'];
                $product['exceptions_type'] = $types['exceptions_type'];
            }

            if (empty($product['product_options'])) {
                if (!empty($product['combination'])) {
                    $selected_options = fn_get_product_options_by_combination($product['combination']);
                }

                $product['product_options'] = (!empty($selected_options)) ? fn_get_selected_product_options($product['product_id'], $selected_options, (string) GlobalState::interfaceLocale()) : $product_options[$product_id];
            }

            $product = fn_apply_options_rules($product);

            if (!empty($params['get_icon']) || !empty($params['get_detailed'])) {
                // Get product options images
                if (!empty($product['combination_hash']) && !empty($product['product_options'])) {
                    $image = fn_get_image_pairs($product['combination_hash'], 'product_option', 'M', $params['get_icon'], $params['get_detailed'], (string) GlobalState::interfaceLocale());
                    if (!empty($image)) {
                        $product['main_pair'] = $image;
                    }
                }
            }
            $product['has_options'] = !empty($product['product_options']);

            $product['product_options'] = \Wizacha\Option::filterExceptions(
                $product['product_options'],
                Product::getExceptions($product_id)
            );

            // Change price
            $selected_options = isset($product['selected_options']) ? $product['selected_options'] : array();
            foreach ($product['product_options'] as $option) {
                if (!empty($option['disabled'])) {
                    unset($selected_options[$option['option_id']]);
                }
            }

            $product['selected_options'] = $selected_options;

            if (empty($product['modifiers_price'])) {
                $product['base_modifier'] = fn_apply_options_modifiers($selected_options, $product['base_price'], 'P', array(), array('product_data' => $product));
                $old_price = $product['price'];
                $product['price'] = fn_apply_options_modifiers($selected_options, $product['price'], 'P', array(), array('product_data' => $product));

                if (empty($product['original_price'])) {
                    $product['original_price'] = $old_price;
                }

                $product['original_price'] = fn_apply_options_modifiers($selected_options, $product['original_price'], 'P', array(), array('product_data' => $product));
                $product['modifiers_price'] = $product['price'] - $old_price;
            }

            if (!empty($product['list_price'])) {
                $product['list_price'] = fn_apply_options_modifiers($selected_options, $product['list_price'], 'P', array(), array('product_data' => $product));
            }

            if (!empty($product['prices']) && is_array($product['prices'])) {
                foreach ($product['prices'] as $pr_k => $pr_v) {
                    $product['prices'][$pr_k]['price'] = fn_apply_options_modifiers($selected_options, $pr_v['price'], 'P', array(), array('product_data' => $product));
                }
            }
        } else {
            $product['has_options'] = (!empty($has_product_options[$product_id]) || !empty($has_product_options_links[$product_id])) ? true : false;
            $product['product_options'] = empty($product['product_options']) ? array() : $product['product_options'];
        }

        unset($selected_options);

        if(isset($product['original_price'],$product['base_price']) && 0==$product['base_price']){
            $product['base_price'] = $product['original_price'];
            $product['modifiers_price'] = $product['base_modifier'] - $product['base_price'];
        }

        // FIXME: old product options scheme
        $product['discounts'] = array('A' => 0, 'P' => 0);
        if (!empty($product['promotions'])) {
            foreach ($product['promotions'] as $v) {
                foreach ($v['bonuses'] as $a) {
                    if ($a['discount_bonus'] == 'to_fixed') {
                        $product['discounts']['A'] += $a['discount'];
                    } elseif ($a['discount_bonus'] == 'by_fixed') {
                        $product['discounts']['A'] += $a['discount_value'];
                    } elseif ($a['discount_bonus'] == 'to_percentage') {
                        $product['discounts']['P'] += 100 - $a['discount_value'];
                    } elseif ($a['discount_bonus'] == 'by_percentage') {
                        $product['discounts']['P'] += $a['discount_value'];
                    }
                }
            }
        }

        // Add product prices with taxes and without taxes
        if ($params['get_taxed_prices'] && AREA != 'A' && Registry::get('settings.Appearance.show_prices_taxed_clean') == 'Y' && $auth['tax_exempt'] != 'Y') {
            fn_get_taxed_and_clean_prices($product, $auth);
        }

        if ($params['get_features'] && !isset($product['product_features'])) {
            $product['product_features'] = fn_get_product_features_list($product, $params['features_display_on']);
        }

        if ($params['get_extra'] && !empty($product['is_edp']) && $product['is_edp'] == 'Y') {
            $product['agreement'] = array(fn_get_edp_agreements($product['product_id']));
        }

        $product['qty_content'] = fn_get_product_qty_content($product, $allow_negative_amount, $inventory_tracking);

        if ($params['tier_pricing'] === true) {
            $hasPriceTiers = container()->get('marketplace.price_tier.price_tier_service')
                ->hasPriceTiers($product['product_id'], null);

            $product['has_price_tiers'] = $hasPriceTiers;
        }

        if ($params['detailed_params']) {
            $product['detailed_params'] = empty($product['detailed_params']) ? $params : array_merge($product['detailed_params'], $params);
        }

        $_product = $product;
    }
    // \foreach $products

    if ($params['get_for_one_product'] == true) {
        $products = array_shift($products);
    }
}

/**
 * Forms a drop-down list of possible product quantity values with the given quantity step
 *
 * @param array $product Product data
 * @param char $allow_negative_amount Flag: allow or disallow negative product quantity(Y - allow, N - disallow)
 * @param char $inventory_tracking Flag: track product qiantity or not (Y - track, N - do not track)
 * @return array qty_content List of available quantity values with the given step
 */
function fn_get_product_qty_content($product, $allow_negative_amount, $inventory_tracking)
{
    $qty_content = array();

    if (!empty($product['qty_step'])) {

        $default_list_qty_count = 100;

        if (empty($product['min_qty'])) {
            $min_qty = $product['qty_step'];
        } else {
            $min_qty = fn_ceil_to_step($product['min_qty'], $product['qty_step']);
        }

        if (!empty($product['list_qty_count'])) {
            $max_list_qty = $product['list_qty_count'] * $product['qty_step'] + $min_qty - $product['qty_step'];
        } else {
            $max_list_qty = $default_list_qty_count * $product['qty_step'] + $min_qty - $product['qty_step'];
        }

        // max amount
        if ($product['tracking'] != 'D' && $allow_negative_amount != 'Y' && $inventory_tracking == 'Y') {
            if (isset($product['in_stock'])) {
                $max_qty = fn_floor_to_step($product['in_stock'], $product['qty_step']);

            } elseif (isset($product['inventory_amount'])) {
                $max_qty = fn_floor_to_step($product['inventory_amount'], $product['qty_step']);

            } elseif ($product['amount'] < $product['qty_step']) {
                $max_qty = $product['qty_step'];

            } else {
                $max_qty = fn_floor_to_step($product['amount'], $product['qty_step']);
            }

            if (!empty($product['list_qty_count'])) {
                $max_qty = min($max_qty, $max_list_qty);
            }

        } else {
            $max_qty = $max_list_qty;
        }

        if (!empty($product['max_qty'])) {
            $max_qty = min($max_qty, fn_floor_to_step($product['max_qty'], $product['qty_step']));
        }

        for ($qty = $min_qty; $qty <= $max_qty; $qty += $product['qty_step']) {
            $qty_content[] = $qty;
        }
    }

    return $qty_content;
}

/**
 * Gets additional data for a single product
 *
 * @param array $product Product data
 * @param boolean $get_icon Flag that define if product icon should be gathered
 * @param boolean $get_detailed Flag determines if detailed image should be gathered
 * @param boolean $get_options Flag that define if product options should be gathered
 * @param boolean $get_discounts Flag that define if product discounts should be gathered
 * @param boolean $get_features Flag that define if product features should be gathered
 * @return array Product data with the additional information
 */
function fn_gather_additional_product_data(&$product, $get_icon = false, $get_detailed = false, $get_options = true, $get_discounts = true, $get_features = false)
{
    // Get specific settings
    $params = array(
        'get_icon' => $get_icon,
        'get_detailed' => $get_detailed,
        'get_options' => $get_options,
        'get_discounts' => $get_discounts,
        'get_features' => $get_features,
    );

    fn_gather_additional_products_data($product, $params);
}

/**
 * Returns product folders
 *
 * @param array $params
 *        int product_id     - ID of product
 *        string folder_ids  - get folders by ids
 *        string order_by
 * @return array folders, params
 */
function fn_get_product_file_folders($params, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $params['product_id'] = !empty($params['product_id']) ? $params['product_id'] : 0;
    $fields = array(
        'SUM(?:product_files.file_size) as folder_size',
        '?:product_file_folders.*',
        '?:product_file_folder_descriptions.folder_name'
    );
    $default_params = array(
        'product_id' => 0,
        'folder_ids' => '',
        'order_by' => 'position, folder_name',
    );
    $params = array_merge($default_params, $params);

    $join = db_quote(" LEFT JOIN ?:product_files ON ?:product_file_folders.folder_id = ?:product_files.folder_id LEFT JOIN ?:product_file_folder_descriptions ON ?:product_file_folder_descriptions.folder_id = ?:product_file_folders.folder_id AND ?:product_file_folder_descriptions.lang_code = ?s", $lang_code);
    $order = $params['order_by'];

    if (!empty($params['folder_ids'])) {
        $condition = db_quote("WHERE ?:product_file_folders.folder_id IN (?n)", $params['folder_ids']);
    } else {
        $condition = db_quote("WHERE ?:product_file_folders.product_id = ?i", $params['product_id']);
    }

    if (AREA == 'C') {
        $condition .= " AND ?:product_file_folders.status = 'A'";
    }

    $folders = db_get_array("SELECT " . implode(', ', $fields) . " FROM ?:product_file_folders ?p ?p GROUP BY folder_id ORDER BY ?p", $join, $condition, $order);

    return array($folders, $params);
}

/**
 * Returns product files
 * @param array $params
 *        int product_id     - ID of product
 *        bool preview_check - get files only with preview
 *        int order_id       - get order ekeys for the files
 *        string file_ids    - get files by ids
 * @return array files, params
 */
function fn_get_product_files($params, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $default_params = array(
        'product_id' => 0,
        'preview_check' => false,
        'order_id' => 0,
        'file_ids' => '',
    );
    $params = array_merge($default_params, $params);

    $fields = array(
        '?:product_files.*',
        '?:product_file_descriptions.file_name',
        '?:product_file_descriptions.license',
        '?:product_file_descriptions.readme'
    );

    $join = db_quote(" LEFT JOIN ?:product_file_descriptions ON ?:product_file_descriptions.file_id = ?:product_files.file_id AND ?:product_file_descriptions.lang_code = ?s", $lang_code);

    if (!empty($params['order_id'])) {
        $fields[] = '?:product_file_ekeys.active';
        $fields[] = '?:product_file_ekeys.downloads';
        $fields[] = '?:product_file_ekeys.ekey';

        $join .= db_quote(" LEFT JOIN ?:product_file_ekeys ON ?:product_file_ekeys.file_id = ?:product_files.file_id AND ?:product_file_ekeys.order_id = ?i", $params['order_id']);
        $join .= (AREA == 'C') ? " AND ?:product_file_ekeys.active = 'Y'" : '';
    }

    if (!empty($params['file_ids'])) {
        $condition = db_quote("WHERE ?:product_files.file_id IN (?n)", $params['file_ids']);
    } else {
        $condition = db_quote("WHERE ?:product_files.product_id = ?i", $params['product_id']);
    }

    if ($params['preview_check'] == true) {
        $condition .= " AND preview_path != ''";
    }

    if (AREA == 'C') {
        $condition .= " AND ?:product_files.status = 'A'";
    }

    $files = db_get_array("SELECT " . implode(', ', $fields) . " FROM ?:product_files ?p ?p ORDER BY position, file_name", $join, $condition);

    if (!empty($files)) {
        foreach ($files as $k => $file) {
            if (!empty($file['license']) && $file['agreement'] == 'Y') {
                $files[$k]['agreements'] = array($file);
            }
            if (!empty($file['product_id']) && !empty($file['ekey'])) {
                $files[$k]['edp_info'] = fn_get_product_edp_info($file['product_id'], $file['ekey']);
            }
        }
    }

    return array($files, $params);
}

/**
 * Returns product folders and files merged and presented as a tree
 *
 * @param array $folders Product folders
 * @param array $files Product files
 * @return array tree
 */
function fn_build_files_tree($folders, $files)
{
    $tree = array();
    $folders = !empty($folders) ? $folders : array();
    $files = !empty($files) ? $files : array();

    if (is_array($folders) && is_array($files)) {

        foreach ($folders as $v_folder) {
            $subfiles = array();
            foreach ($files as $v_file) {
                if ($v_file['folder_id'] == $v_folder['folder_id']) {
                    $subfiles[] = $v_file;
                }
            }

            $v_folder['files'] = $subfiles;
            $tree['folders'][] = $v_folder;
        }

        foreach ($files as $v_file) {
            if (empty($v_file['folder_id'])) {
                $tree['files'][] = $v_file;
            }
        }

    }

    return $tree;
}

/**
 * Returns EDP ekey info
 *
 * @param int $product_id Product identifier
 * @param string $ekey Download key
 * @return array Download key info
 */
function fn_get_product_edp_info($product_id, $ekey)
{
    $unlimited = db_get_field("SELECT unlimited_download FROM ?:products WHERE product_id = ?i", $product_id);
    $ttl_condition = ($unlimited == 'Y') ? '' : db_quote(" AND ttl > ?i", TIME);

    $edp_info = db_get_row(
        "SELECT product_id, order_id, file_id "
        . "FROM ?:product_file_ekeys "
        . "WHERE product_id = ?i AND active = 'Y' AND ekey = ?s ?p",
        $product_id, $ekey, $ttl_condition
    );

    return $edp_info;
}

/**
 * Gets EDP agreements
 *
 * @param int $product_id Product identifier
 * @param bool $file_name If true get file name in info, false otherwise
 * @return array EDP agreements data
 */
function fn_get_edp_agreements($product_id, $file_name = false)
{
    $join = '';
    $fields = array(
        '?:product_files.file_id',
        '?:product_files.agreement',
        '?:product_file_descriptions.license'
    );

    if ($file_name == true) {
        $join .= db_quote(" LEFT JOIN ?:product_file_descriptions ON ?:product_file_descriptions.file_id = ?:product_files.file_id AND product_file_descriptions.lang_code = ?s", (string) GlobalState::interfaceLocale());
        $fields[] = '?:product_file_descriptions.file_name';
    }

    $edp_agreements = db_get_array("SELECT " . implode(', ', $fields) . " FROM ?:product_files INNER JOIN ?:product_file_descriptions ON ?:product_file_descriptions.file_id = ?:product_files.file_id AND ?:product_file_descriptions.lang_code = ?s WHERE ?:product_files.product_id = ?i AND ?:product_file_descriptions.license != '' AND ?:product_files.agreement = 'Y'", (string) GlobalState::interfaceLocale(), $product_id);

    return $edp_agreements;
}

//-------------------------------------- 'Categories' object functions -----------------------------

/**
 * Gets subcategories list for current category (first-level categories only)
 *
 * @param int $category_id Category identifier
 * @param string $lang_code 2-letters language code
 * @return array Subcategories
 */
function fn_get_subcategories($category_id = '0', $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $params = array(
        'category_id' => $category_id,
        'visible' => true
    );

    list($categories,) = fn_get_categories($params, $lang_code);

    return $categories;
}

/**
 * Gets categories tree (multidimensional) from the current category
 *
 * @param int $category_id Category identifier
 * @param boolean $simple Flag that defines if category names path and product count should not be gathered
 * @param string $lang_code 2-letters language code
 * @return array Array of subcategories as a hierarchical tree
 */
function fn_get_categories_tree($category_id = '0', $simple = true, $lang_code = null, $getHiddenCategories = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $params = array(
        'category_id' => $category_id,
        'simple' => $simple,
        'get_hidden_categories' => $getHiddenCategories,
        'generate_category_path' => true,
    );

    list($categories,) = fn_get_categories($params, $lang_code);

    return $categories;
}

/**
 * Gets categories tree (plain) from the current category
 *
 * @param int $category_id Category identifier
 * @param boolean $simple Flag that defines if category names path and product count should not be gathered
 * @param string $lang_code 2-letters language code
 * @param array $company_ids Identifiers of companies for that categories should be gathered
 * @return array Array of subategories as a simple list
 */
function fn_get_plain_categories_tree($category_id = '0', $simple = true, $lang_code = null, $company_ids = '')
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $params = array(
        'category_id' => $category_id,
        'simple' => $simple,
        'visible' => false,
        'plain' => true,
        'company_ids' => $company_ids,
    );

    list($categories,) = fn_get_categories($params, $lang_code);

    return $categories;
}

/**
 * Checks if objects should be displayed in a picker
 *
 * @param string $table Name of SQL table with objects
 * @param int $threshold Value of the threshold after which the picker should be displayed
 * @return boolean Flag that defines if picker should be displayed
 */
function fn_show_picker($table, $threshold)
{
    return db_get_field("SELECT COUNT(*) FROM ?:$table") > $threshold;
}

/**
 * Gets categories tree beginning from category identifier defined in params or root category
 * @param array $params Categories search params
 *      category_id - Root category identifier
 *      visible - Flag that defines if only visible categories should be included
 *      current_category_id - Identifier of current node for visible categories
 *      simple - Flag that defines if category path should be getted as set of category IDs
 *      plain - Flag that defines if continues list of categories should be returned
 *      --------------------------------------
 *      Examples:
 *      Gets whole categories tree:
 *      fn_get_categories()
 *      --------------------------------------
 *      Gets subcategories tree of the category:
 *      fn_get_categories(123)
 *      --------------------------------------
 *      Gets all first-level nodes of the category
 *      fn_get_categories(123, true)
 *      --------------------------------------
 *      Gets all visible nodes of the category, start from the root
 *      fn_get_categories(0, true, 234)
 * @param string $lang_code 2-letters language code
 * @return array Categories tree
 */
function fn_get_categories($params = array(), $lang_code = null, $showDeactivatedCategories = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $default_params = array(
        'category_id' => 0,
        'visible' => false,
        'current_category_id' => 0,
        'simple' => true,
        'plain' => false,
        'limit' => 0,
        'item_ids' => '',
        'group_by_level' => true,
        'get_images' => false,
        'category_delimiter' => '/',
        'get_hidden_categories' => false,
        'generate_category_path' => false,
    );

    $params = array_merge($default_params, $params);

    $sortings = array(
        'timestamp' => '?:categories.timestamp',
        'name' => '?:category_descriptions.category',
        'position' => array(
            '?:categories.position',
            '?:category_descriptions.category'
        )
    );

    $auth = & $_SESSION['auth'];

    $fields = array(
        '?:categories.category_id',
        '?:categories.parent_id',
        '?:categories.id_path',
        '?:category_descriptions.category',
        '?:categories.position',
        '?:categories.status'
    );

    if ($params['simple'] == false) {
        $fields[] = '?:categories.product_count';
        $fields[] = '?:categories.visible_product_count';
    }

    if (empty($params['current_category_id']) && !empty($params['product_category_id'])) {
        $params['current_category_id'] = $params['product_category_id'];
    }

    $condition = '';

    if (Registry::get('runtime.company_id')) {
        $company_id = Registry::get('runtime.company_id');
    } elseif (!empty($params['company_ids'])) {
        $company_id = (int)$params['company_ids'];
    }
    if (!empty($company_id)) {
        $company_data = fn_get_company_data($company_id);
        if (!empty($company_data['category_ids'])) {
            $company_condition = db_quote(' AND ?:categories.category_id IN (?n)', $company_data['category_ids']);
            $condition .= $company_condition;
        }
    }

    if (AREA == 'C') {
        if($params['get_hidden_categories']) {
            // active and hidden categories
            $params['status'] = $params['status']?: array('A', 'H');
        } else {
            // only active categories
            $params['status'] = $params['status']?: array('A');
        }
    }

    if (false === $showDeactivatedCategories && strlen($params['status'][0]) > 0) {
        $condition .= db_quote(" AND ?:categories.status IN (?a)", $params['status']);
    }

    if (isset($params['parent_category_id'])) {
        // set parent id, that was set in block properties
        $params['category_id'] = $params['parent_category_id'];
    }

    if ($params['visible'] == true && empty($params['b_id'])) {
        if (!empty($params['current_category_id'])) {
            $cur_id_path = db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i", $params['current_category_id']);
            if (!empty($cur_id_path)) {
                $parent_categories_ids = explode('/', $cur_id_path);
            }
        }
        if (!empty($params['category_id']) || empty($parent_categories_ids)) {
            $parent_categories_ids[] = $params['category_id'];
        }
        $parents_condition = db_quote(" AND ?:categories.parent_id IN (?n)", $parent_categories_ids);
    }

    // if we have company_condtion, skip $parents_condition, it will be processed later by PHP
    if (!empty($parents_condition) && empty($company_condition)) {
        $condition .= $parents_condition;
    }

    if (!empty($params['category_id'])) {
        $from_id_path = db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i", $params['category_id']);
        $condition .= db_quote(" AND ?:categories.id_path LIKE ?l", "$from_id_path/%");
    }

    if (!empty($params['item_ids'])) {
        $condition .= db_quote(' AND ?:categories.category_id IN (?n)', explode(',', $params['item_ids']));
    }

    if (!empty($params['except_id']) && (empty($params['item_ids']) || !empty($params['item_ids']) && !in_array($params['except_id'], explode(',', $params['item_ids'])))) {
        $condition .= db_quote(' AND ?:categories.category_id != ?i AND ?:categories.parent_id != ?i', $params['except_id'], $params['except_id']);
    }

    if (!empty($params['period']) && $params['period'] != 'A') {
        list($params['time_from'], $params['time_to']) = fn_create_periods($params);
        $condition .= db_quote(" AND (?:categories.timestamp >= ?i AND ?:categories.timestamp <= ?i)", $params['time_from'], $params['time_to']);
    }

    $limit = $join = $group_by = '';

    fn_wizacha_backend_design_get_categories($params, $join, $condition, $fields, $group_by, $sortings, $lang_code);

    $fields[] = '?:seo_names.name as seo_name';
    $join .= db_quote(
        " LEFT JOIN ?:seo_names ON ?:seo_names.object_id = CAST(?:categories.category_id AS CHAR) ?p", fn_get_seo_join_condition('c')
    );

    if (!empty($params['rating'])) {
        $fields[] = 'avg(?:discussion_rating.rating_value) AS rating';
        $join .= db_quote(" INNER JOIN ?:discussion ON ?:discussion.object_id = CAST(?:categories.category_id AS CHAR) AND ?:discussion.object_type = 'C'");
        $join .= db_quote(" INNER JOIN ?:discussion_rating ON ?:discussion.thread_id=?:discussion_rating.thread_id");
        $join .= db_quote(" INNER JOIN ?:discussion_posts ON ?:discussion_posts.post_id=?:discussion_rating.post_id AND ?:discussion_posts.status = 'A'");
        $group_by = 'GROUP BY ?:discussion_rating.thread_id';
        $sortings['rating'] = 'rating';
        $params['sort_by'] = 'rating';
        $params['sort_order'] = 'asc';
    }

    if (!empty($params['limit'])) {
        $page = empty($params['page']) ? 1 : $params['page'];
            $limit = db_paginate($page, $params['limit']);
        }

    $sorting = db_sort($params, $sortings, 'position', 'asc');

    if (!empty($params['get_conditions'])) {
        return array($fields, $join, $condition, $group_by, $sorting, $limit);
    }

    $categories = db_get_hash_array('SELECT ' . implode(',', $fields) . " FROM ?:categories LEFT JOIN ?:category_descriptions ON ?:categories.category_id = ?:category_descriptions.category_id AND ?:category_descriptions.lang_code = ?s $join WHERE 1 ?p $group_by $sorting ?p", 'category_id', $lang_code, $condition, $limit);

    if (empty($categories)) {
        return array(array());
    }

    if (!empty($params['active_category_id']) && !empty($categories[$params['active_category_id']])) {
        $categories[$params['active_category_id']]['active'] = true;
        Registry::set('runtime.active_category_ids', explode('/', $categories[$params['active_category_id']]['id_path']));
    }

    $categories_list = array();
    if ($params['simple'] == true || $params['group_by_level'] == true) {
        $child_for = array_keys($categories);
        $where_condition = !empty($params['except_id']) ? db_quote(' AND category_id != ?i', $params['except_id']) : '';
        $has_children = db_get_hash_array("SELECT category_id, parent_id FROM ?:categories WHERE parent_id IN(?n) ?p", 'parent_id', $child_for, $where_condition);
    }

    if ($params['generate_category_path']) {
        foreach ($categories as &$category) {
            if (!empty($category['parent_id'])) {
                $seoPathFragments = array_map(function ($parentId) use ($categories) {
                    return $categories[$parentId]['seo_name'];
                }, explode('/', $category['id_path']));
                $category['category_path'] = implode('/', $seoPathFragments);
            } else {
                $category['category_path'] = $category['seo_name'];
            }
        }
    }

    // Group categories by the level (simple)
    if ($params['simple'] == true) {
        foreach ($categories as $k => $v) {
            $v['level'] = substr_count($v['id_path'], '/');
            if ((!empty($params['current_category_id']) || $v['level'] == 0) && isset($has_children[$k])) {
                $v['has_children'] = $has_children[$k]['category_id'];
            }
            $categories_list[$v['level']][$v['category_id']] = $v;
            if ($params['get_images'] == true) {
                $categories_list[$v['level']][$v['category_id']]['main_pair'] = fn_get_image_pairs($v['category_id'], 'category', 'M', true, true, $lang_code);
            }
        }
    } elseif ($params['group_by_level'] == true) {
        // Group categories by the level (simple) and literalize path
        foreach ($categories as $k => $v) {
            $path = explode('/', $v['id_path']);
            $category_path = array();
            foreach ($path as $__k => $__v) {
                $category_path[$__v] = @$categories[$__v]['category'];
            }
            $v['category_path'] = implode($params['category_delimiter'], $category_path);
            $v['level'] = substr_count($v['id_path'], "/");
            if ((!empty($params['current_category_id']) || $v['level'] == 0) && isset($has_children[$k])) {
                $v['has_children'] = $has_children[$k]['category_id'];
            }
            $categories_list[$v['level']][$v['category_id']] = $v;
            if ($params['get_images'] == true) {
                $categories_list[$v['level']][$v['category_id']]['main_pair'] = fn_get_image_pairs($v['category_id'], 'category', 'M', true, true, $lang_code);
            }
        }
    } else {
        $categories_list = $categories;
        if ($params['get_images'] == true) {
            foreach ($categories_list as $k => $v) {
                if ($params['get_images'] == true) {
                    $categories_list[$k]['main_pair'] = fn_get_image_pairs($v['category_id'], 'category', 'M', true, true, $lang_code);
                }
            }
        }
    }

    ksort($categories_list, SORT_NUMERIC);
    $categories_list = array_reverse($categories_list);

    foreach ($categories_list as $level => $v) {
        foreach ($v as $k => $data) {
            if (isset($data['parent_id']) && isset($categories_list[$level + 1][$data['parent_id']])) {
                $categories_list[$level + 1][$data['parent_id']]['subcategories'][] = $categories_list[$level][$k];
                unset($categories_list[$level][$k]);
            }
        }
    }

    if ($params['group_by_level'] == true) {
        $categories_list = array_pop($categories_list);
    }

    if ($params['plain'] == true) {
        $categories_list = fn_multi_level_to_plain($categories_list, 'subcategories');
    }

    if (!empty($params['item_ids'])) {
        $categories_list = fn_sort_by_ids($categories_list, explode(',', $params['item_ids']), 'category_id');
    }

    if (!empty($params['add_root'])) {
        array_unshift($categories_list, array('category_id' => 0, 'category' => $params['add_root']));
    }

    fn_dropdown_appearance_cut_second_third_levels($categories_list, 'subcategories', $params);

    if (AREA == 'C') {
        foreach ($categories_list as $k => $category) {
            fn_seo_cache_parent_items_path('c', $category['category_id'], $category['id_path']);
            fn_seo_cache_name('c', $category['category_id'], $category['seo_name'], isset($category['company_id']) ? $category['company_id'] : '');
        }
    }

    // process search results
    if (!empty($params['save_view_results'])) {
        $request = $params;
        $request['page'] = 1;
        $categories_res = ($params['plain'] == true) ? $categories_list : fn_multi_level_to_plain($categories_list, 'subcategories');
        foreach ($categories_res as $key => $item) {
            if (empty($item['category_id'])) {
                unset($categories_res[$key]);
            }
        }
        $request['total_items'] = $request['items_per_page'] = count($categories_res);
        LastView::instance()->processResults('categories', $categories_res, $request);
    }

    return array($categories_list, $params);
}

/**
 * Recursively sorts an array using a user-supplied comparison function
 *
 * @param array $array Array for sorting
 * @param string $key Key of subarray for sorting
 * @param callback $function Comparison function
 */
function fn_sort(&$array, $key, $function)
{
    usort($array, $function);
    foreach ($array as $k => $v) {
        if (!empty($v[$key])) {
            fn_sort($array[$k][$key], $key, $function);
        }
    }
}

/**
 * Gets full category data by its id
 *
 * @param int $category_id ID of category
 * @param string $lang_code 2-letters language code
 * @param string $field_list List of categories table' fields. If empty, data from all fields will be returned.
 * @param boolean $get_main_pair Get or not category image
 * @param boolean $skip_company_condition Select data for other stores categories. By default is false. This flag is used in ULT for displaying common categories in picker.
 * @return mixed Array with category data.
 */
function fn_get_category_data($category_id = 0, $lang_code = null, $field_list = '', $get_main_pair = true, $skip_company_condition = false, $preview = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    if (Registry::get('runtime.company_id')) {
        $company_data = Registry::get('runtime.company_data');
        if (!empty($company_data['categories'])) {
            $allowed_categories = explode(',', $company_data['categories']);
            if (!in_array($category_id, $allowed_categories)) {
                return false;
            }
        }
    }

    $conditions = '';

    if (empty($field_list)) {
        $descriptions_list = "?:category_descriptions.*";
        $field_list = "?:categories.*, $descriptions_list";
    }

    $join = '';

    $field_list .= ', ?:seo_names.name as seo_name';
    $join .= db_quote(
        " LEFT JOIN ?:seo_names ON ?:seo_names.object_id = ?s ?p",
        $category_id, fn_get_seo_join_condition('c')
    );

    $category_data = db_get_row("SELECT $field_list FROM ?:categories LEFT JOIN ?:category_descriptions ON ?:category_descriptions.category_id = ?:categories.category_id AND ?:category_descriptions.lang_code = ?s ?p WHERE ?:categories.category_id = ?i ?p", $lang_code, $join, $category_id, $conditions);

    if (!empty($category_data)) {
        $category_data['category_id'] = $category_id;

        // Generate meta description automatically
        if (empty($category_data['meta_description']) && defined('AUTO_META_DESCRIPTION') && AREA != 'A') {
            $category_data['meta_description'] = !empty($category_data['description']) ? fn_generate_meta_description($category_data['description']) : '';
        }

        if ($get_main_pair == true) {
            $category_data['main_pair'] = \Wizacha\Category::getImage($category_data);
        }
    }

    if (AREA == 'C' && !empty($category_data)) {
        fn_seo_cache_parent_items_path('c', $category_data['category_id'], $category_data['id_path']);
        fn_seo_cache_name('c', $category_data['category_id'], $category_data['seo_name'], isset($category['seo_name']) ? $category['seo_name'] : '');
    }

    if (empty($category_data['seo_name']) && !empty($category_data['category_id'])) {
        $category_data['seo_name'] = fn_seo_get_name('c', $category_data['category_id'], '', null, $lang_code);
    }

    return (!empty($category_data) ? $category_data : false);
}

/**
 * Gets category name by category identifier
 *
 * @param int /array $category_id Category identifier or array of category identifiers
 * @param string $lang_code 2-letters language code
 * @param boolean $as_array Flag if false one category name is returned as simple string, if true category names are always returned as array
 * @return string/array Category name or array with category names
 */
function fn_get_category_name($category_id = 0, $lang_code = null, $as_array = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $name = array();

    if (!empty($category_id)) {
        if (!is_array($category_id) && strpos($category_id, ',') !== false) {
            $category_id = explode(',', $category_id);
        }
        if (is_array($category_id) || $as_array == true) {
            $name = db_get_hash_single_array("SELECT category_id, category FROM ?:category_descriptions WHERE category_id IN (?n) AND lang_code = ?s", array('category_id', 'category'), $category_id, $lang_code);
        } else {
            $name = db_get_field("SELECT category FROM ?:category_descriptions WHERE category_id = ?i AND lang_code = ?s", $category_id, $lang_code);
        }
    }

    return $name;
}

/**
 * Gets category path by category identifier
 *
 * @param int $category_id Category identifier
 * @param string $lang_code 2-letters language code
 * @param string $path_separator String character(s) separating the catergories
 * @return string Category path
 */
function fn_get_category_path($category_id = 0, $lang_code = null, $path_separator = '/')
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $category_path = false;

    if (!empty($category_id)) {

        $id_path = db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i", $category_id);

        $category_names = db_get_hash_single_array(
            "SELECT category_id, category FROM ?:category_descriptions WHERE category_id IN (?n) AND lang_code = ?s",
            array('category_id', 'category'), explode('/', $id_path), $lang_code
        );

        $path = explode('/', $id_path);
        $_category_path = '';
        foreach ($path as $v) {
            $_category_path .= $category_names[$v] . $path_separator;
        }
        $_category_path = rtrim($_category_path, $path_separator);

        $category_path = (!empty($_category_path) ? $_category_path : false);
    }

    return $category_path;
}

/**
 * Removes category by identifier
 *
 * @param int $category_id Category identifier
 * @param boolean $recurse Flag that defines if category should be deleted recursively
 * @return int/boolean Identifiers of deleted categories or false if categories were not found
 */
function fn_delete_category($category_id, $recurse = true)
{
    // Prevent removing an category that is not empty
    if (\Wizacha\Category::hasProducts($category_id)) {
        return false;
    }

    if (empty($category_id)) {
        return false;
    }

    //invalid categories menu handler
    \Wizacha\Registry::defaultInstance()->invalidHandler(\Wizacha\Cache\Handler::CATEGORIES_MENU);

    // Log category deletion
    fn_log_event('categories', 'delete', array(
        'category_id' => $category_id,
    ));

    // Delete all subcategories
    if ($recurse == true) {
        $id_path = db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i", $category_id);
        $category_ids = db_get_fields("SELECT category_id FROM ?:categories WHERE category_id = ?i OR id_path LIKE ?l", $category_id, "$id_path/%");
    } else {
        $category_ids[] = $category_id;
    }

    $seoService = container()->get('marketplace.seo.seo_service');

    foreach ($category_ids as $k => $category_id) {
        Block::instance()->removeDynamicObjectdata('categories', $category_id);

        // Deleting category
        db_query("DELETE FROM ?:category_descriptions WHERE category_id = ?i", $category_id);
        db_query("DELETE FROM ?:categories WHERE category_id = ?i", $category_id);

        // Deleting additional product associations without deleting products itself
        db_query("DELETE FROM ?:products_categories WHERE category_id = ?i AND link_type = 'A'", $category_id);

        // Remove this category from features assignments
        db_query("UPDATE ?:product_features SET categories_path = ?p", fn_remove_from_set('categories_path', $category_id));

        // Deleting main products association with deleting products
        $products_to_delete = db_get_fields("SELECT product_id FROM ?:products_categories WHERE category_id = ?i AND link_type = 'M'", $category_id);
        if (!empty($products_to_delete)) {
            foreach ($products_to_delete as $key => $value) {
                fn_delete_product($value, true);
            }
        }

        // Deleting category images
        fn_delete_image_pairs($category_id, 'category');

        db_query("UPDATE ?:companies SET categories = ?p", fn_remove_from_set('categories', $category_id));
        db_query("DELETE FROM ?:product_sales WHERE category_id = ?i", $category_id);
        fn_delete_discussion($category_id, 'C');
        $seoService->removeSlug(\Wizacha\Marketplace\Seo\SlugTargetType::CATEGORY(), $category_id);
    }

    \Wizacha\Events\Config::dispatch(
        \Wizacha\Category::EVENT_DELETE,
        (new \Wizacha\Events\IterableEvent)->setArray($category_ids)
    );

    return $category_ids; // Returns ids of deleted categories
}

/**
 * Removes product by identifier
 *
 * @param int $product_id Product identifier
 * @param bool $asynchronous
 * @return bool Flag that defines if product was deleted
 * @throws Exception
 * @throws Forbidden
 */
function fn_delete_product($product_id, bool $asynchronous = false)
{
    if ($asynchronous && container()
        ->get('marketplace.async_dispatcher')
        ->delayExec(__METHOD__, func_get_args())
    ) {
        return true;
    }

    $status = true;
    $product_deleted = false;

    if (!empty($product_id)) {

        if (!fn_check_company_id('products', 'product_id', $product_id)) {
            throw new Forbidden();
        }

        if ($status == false) {
            return false;
        }

        if (null !== $product_id) {
            try {
                container()->get('marketplace.pim.product.service')->detach($product_id);
            } catch (\Wizacha\Marketplace\Exception\NotFound $ex) {}
        }

        // Delete attachments
        $productService = container()->get('marketplace.pim.product.service');
        $attachments = $productService->getAttachments((int) $product_id);

        foreach ($attachments as $attachment) {
            $productService->removeAttachment($attachment);
        }

        // Delete product files
        fn_delete_product_files(0, $product_id);

        // Delete product folders
        fn_delete_product_file_folders(0, $product_id);

        $category_ids = db_get_fields("SELECT category_id FROM ?:products_categories WHERE product_id = ?i", $product_id);
        db_query("DELETE FROM ?:products_categories WHERE product_id = ?i", $product_id);
        container()->get('marketplace.pim.category_service')->updateProductCount($category_ids);

        db_query("DELETE FROM ?:products WHERE product_id = ?i", $product_id);
        db_query("DELETE FROM ?:product_descriptions WHERE product_id = ?i", $product_id);
        db_query("DELETE FROM ?:product_prices WHERE product_id = ?i", $product_id);
        db_query("DELETE FROM ?:product_features_values WHERE product_id = ?s", $product_id);

        db_query("DELETE FROM ?:product_options_exceptions WHERE product_id = ?i", $product_id);

        $priceTierRepository = container()->get('marketplace.price_tier.price_tier_repository');
        foreach ($priceTierRepository->findByProductId($product_id) as $priceTier) {
            $priceTierRepository->remove($priceTier);
        }

        // If the product was waiting for moderation, we delete it from the moderation in progress table
        $moderationService = container()->get('marketplace.moderation.product_moderation_in_progress_service');
        $moderationService->delete((int) $product_id);

        fn_delete_image_pairs($product_id, 'product');

        // Delete product options and inventory records for this product
        fn_poptions_delete_product($product_id);

        $product_deleted = true;
    }
    db_query("DELETE FROM ?:product_sales WHERE product_id = ?i", $product_id);
    fn_delete_discussion($product_id, 'P');
    container()->get('marketplace.seo.seo_service')->removeSlug(\Wizacha\Marketplace\Seo\SlugTargetType::PRODUCT(), $product_id);

    try {
        container()->get('marketplace.pim.product.service')->removeFromCache((int) $product_id);
    } catch (\Exception $ex) {
        // silent fail because the product may not be in the doctrine identity map
    }

    \Wizacha\Events\Config::dispatch(
        Product::EVENT_DELETE,
        (new \Wizacha\Events\IterableEvent)->setElement($product_id)
    );

    return $product_deleted;
}

/**
 * Adds or updates product
 *
 * @param array $product_data Product data
 * @param int $product_id Product identifier
 * @param string $lang_code Two-letter language code (e.g. 'en', 'ru', etc.)
 * @return bool|int|mixed New or updated product identifier
 * @return bool $dispatch
 * @throws \Wizacha\Marketplace\Exception\NotFound
 * @throws \InvalidArgumentException
 */
function fn_update_product($product_data, $product_id = 0, $lang_code = null, $dispatch = true)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();

    if ($product_data['product_template_type'] === 'product' && empty($product_data['is_edp']) === true) {
        $product_data['is_edp'] = 'N';
    }

    $mainCategoryId = Product::getMainCategoryFromData($product_data);

    if (\is_int($mainCategoryId) === true) {
        $productService = container()->get(ProductService::class);
        $productCategoryId = fn_get_product_category_id($product_id);

        if (false === \is_null($productCategoryId) && $productCategoryId !== $mainCategoryId) {
            if ($productService->updateProductCategoryHasImpactToDeclinations($product_id, $mainCategoryId) === true) {
                if (\array_key_exists('updateProductByApi', $product_data) === true && $product_data['updateProductByApi'] === true) {
                    throw new \InvalidArgumentException('api_warning_updating_category');
                }

                fn_delete_product_option_combinations($product_id);
                $product_data['tracking'] = 'B';
                $productService->deleteProductOptionException($product_id);

                fn_set_notification('E', __('error'), __('error_updating_category'));
            }
        }
    }

    Product::checkShippingsBeforeUpdate($product_data, $product_id, \Tygh\Registry::get('runtime.company_id'));
    if (!empty($product_data['category_ids']) && is_array($product_data['category_ids'])) {
        $product_data['category_ids'] = \Wizacha\Category::removeGarbageCategories(
            array_unique($product_data['category_ids'])
        );
    }

    $product_data['updated_timestamp'] = time();

    $purifierService = container()->get('purifier.default');

    // Sanitize product full description
    if (true === \array_key_exists('full_description', $product_data) && $product_data['full_description'] !== null) {
        $product_data['full_description'] = \trim($purifierService->purify($product_data['full_description']));
    }

    // Sanitize product short description
    if (true === \array_key_exists('short_description', $product_data) && $product_data['short_description'] !== null) {
        $product_data['short_description'] = \trim($purifierService->purify($product_data['short_description']));
    }

    $_data = $product_data;

    $_data['is_subscription'] = false;
    $_data['is_renewable'] = false;

    if (is_bool($product_data['is_subscription'])) {
        $_data['is_subscription'] = $product_data['is_subscription'];
    } elseif (is_string($product_data['is_subscription'])) {
        $_data['is_subscription'] = $product_data['is_subscription'] === "1";
    }

    if (is_bool($product_data['is_renewable'])) {
        $_data['is_renewable'] = $product_data['is_renewable'];
    } elseif (is_string($product_data['is_renewable'])) {
        $_data['is_renewable'] = $product_data['is_renewable'] === "1";
    }

    if (!empty($product_data['timestamp'])) {
        $_data['timestamp'] = fn_parse_date($product_data['timestamp']); // Minimal data for product record
    } elseif (empty($product_id) || isset($product_data['timestamp'])) {
        $_data['timestamp'] = time();
    }

    if (empty($product_id) && \Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance())) {
        $_data['company_id'] = \Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance());
    }

    if (!empty($product_data['avail_since'])) {
        $_data['avail_since'] = fn_parse_date($product_data['avail_since']);
    }

    if (isset($product_data['tax_ids'])) {
        $_data['tax_ids'] = empty($product_data['tax_ids']) ? '' : fn_create_set($product_data['tax_ids']);
    }

    if (isset($product_data['localization'])) {
        $_data['localization'] = empty($product_data['localization']) ? '' : fn_implode_localizations($_data['localization']);
    }

    if (!empty($product_data['list_qty_count']) && $product_data['list_qty_count'] < 0) {
        $_data['list_qty_count'] = 0;
    }

    if (!empty($product_data['qty_step']) && $product_data['qty_step'] < 0) {
        $_data['qty_step'] = 0;
    }

    if (!empty($product_data['min_qty'])) {
        $_data['min_qty'] = fn_ceil_to_step(abs($product_data['min_qty']), $_data['qty_step']);
    }

    if (!empty($product_data['max_qty'])) {
        $_data['max_qty'] = fn_ceil_to_step(abs($product_data['max_qty']), $_data['qty_step']);
    }

    // Allow to give an empty EAN at product creation (replaced by an uniqid). If an empty value is given at update, the old value is kept.
    if (empty($product_data['product_code'])) {
        if ($product_id == 0) {
            $_data['product_code'] = uniqid();
        } else {
            unset($_data['product_code']);
        }
    }

    if (!empty($product_data['max_price_adjustment']) && $product_data['max_price_adjustment'] >= 0 && $product_data['max_price_adjustment'] <=100) {
        $_data['max_price_adjustment'] = (int) $product_data['max_price_adjustment'];
    } else {
        $_data['max_price_adjustment'] = null;
    }

    if (Registry::get('settings.General.inventory_tracking') == "N" && isset($_data['tracking'])) {
        unset($_data['tracking']);
    }

    if (Registry::get('settings.General.allow_negative_amount') == 'N' && isset($_data['amount'])) {
        $_data['amount'] = abs($_data['amount']);
    }

    if (!empty($product_data['transaction_mode'])) {
        $transactionModeService = container()->get('marketplace.transaction_mode.service');
        if (!in_array($product_data['transaction_mode'], $transactionModeService->getAvailablesModesAsArray())) {
            unset($product_data['transaction_mode']);
        }
    }

    $shipping_params = array();
    if (!empty($product_id)) {
        $shipping_params = db_get_field('SELECT shipping_params FROM ?:products WHERE product_id = ?i', $product_id);
        if (!empty($shipping_params)) {
            $shipping_params = unserialize($shipping_params);
        }
    }

    // Save the product shipping params
    $_shipping_params = array(
        'min_items_in_box' => isset($_data['min_items_in_box']) ? intval($_data['min_items_in_box']) : (!empty($shipping_params['min_items_in_box']) ? $shipping_params['min_items_in_box'] : 0),
        'max_items_in_box' => isset($_data['max_items_in_box']) ? intval($_data['max_items_in_box']) : (!empty($shipping_params['max_items_in_box']) ? $shipping_params['max_items_in_box'] : 0),
        'box_length' => isset($_data['box_length']) ? intval($_data['box_length']) : (!empty($shipping_params['box_length']) ? $shipping_params['box_length'] : 0),
        'box_width' => isset($_data['box_width']) ? intval($_data['box_width']) : (!empty($shipping_params['box_width']) ? $shipping_params['box_width'] : 0),
        'box_height' => isset($_data['box_height']) ? intval($_data['box_height']) : (!empty($shipping_params['box_height']) ? $shipping_params['box_height'] : 0),
    );

    $isUuid = '/^[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}$/';
    $_data['shipping_params'] = serialize($_shipping_params);
    unset($_shipping_params);

    // add new product
    if (empty($product_id)) {
        $event = Product::EVENT_CREATE;
        //Products can't be created without category id
        if (! Product::getMainCategoryFromData($product_data)) {
            throw new \InvalidArgumentException('Expect integer');
        }

        if (empty($product_data['w_condition']) || !in_array($product_data['w_condition'], Product::getAllConditions())) {
            if (\Wizacha\Company::isPrivateIndividual(fn_get_company_data($_data['company_id']))) {
                $_data['w_condition'] = Product::CONDITION_USED;
            } else {
                $_data['w_condition'] = Product::CONDITION_NEW;
            }
        }
        $create = true;
        $product_data['create'] = true;
        // product title can't be empty and not set product_id
        if (empty($product_data['product']) || !empty($product_data['product_id'])) {
            fn_set_notification('E', __('error'), __('need_product_name'));

            return false;
        }

        $mvpRules = container()->getParameter('feature.multi_vendor_product.rules');

        if ($mvpRules !== '' && false === fn_check_supplier_ref_mvp_rules($_data, \explode(',', $mvpRules))) {
            fn_set_notification('E', __('error'), __('supplier_reference_is_empty'));

            return false;
        }

        if (array_key_exists("attachments", $_data) === true) {
            foreach ($_data['attachments'] as $dataAttachments) {
                if (array_key_exists("url", $dataAttachments) === true && fn_get_url_data($dataAttachments['url']) === false) {
                    fn_set_notification('E', __('error'), __('url_attachement_not_found'));
                    return false;
                }
            }
        }

        if (array_key_exists("video", $_data) === true && \strlen($_data['video']) > 0) {
            $video = htmlspecialchars($_data['video']);
            try {
                if (preg_match($isUuid, $video) !== 1 && filter_var($video, FILTER_VALIDATE_URL) === false && fopen($video, 'r') === false) {
                    fn_set_notification('E', __('error'), __('video_not_found'));
                    return false;
                }
            } catch (Exception $exception) {
                fn_set_notification('E', __('error'), __('video_not_found'));
                return false;
            }
        }

        if (array_key_exists("infinite_stock", $product_data) === true && $_data['infinite_stock'] === null) {
            $_data['infinite_stock'] = false;
        }

        $product_id = db_query("INSERT INTO ?:products ?e", $_data);

        if (empty($product_id)) {
            $product_id = false;
        }

        //
        // Adding same product descriptions for all cart languages
        //
        $_data = $product_data;
        $_data['product_id'] = $product_id;
        $_data['product'] = trim($_data['product'], " -");

        if (array_key_exists('full_description', $_data) && $_data['full_description'] === null) {
            $_data['full_description'] = '';
        }

        \Tygh\Languages\Helper::insertTranslations('product_descriptions', $lang_code, $_data);

        // Default divisions settings
        if (true === container()->getParameter('feature.available_offers')
            && false === \array_key_exists('divisions', $_data)
        ) {
            $_data['divisions'] = [
                'included' => [ProductDivisionSettings::DEFAULT_DIVISION],
                'excluded' => [],
            ];
        }

        // update product
    } else {
        $event = Product::EVENT_UPDATE;
        if (array_key_exists('w_condition',$product_data) && !in_array($product_data['w_condition'], Product::getAllConditions())) {
            unset($_data['w_condition']);
        }

        $mvpRules = container()->getParameter('feature.multi_vendor_product.rules');

        if ($mvpRules !== '' && false === fn_check_supplier_ref_mvp_rules($_data, \explode(',', $mvpRules), true)) {
            fn_set_notification('E', __('error'), __('supplier_reference_is_empty'));

            return false;
        }

        $create = false;
        if (isset($product_data['product']) && empty($product_data['product'])) {
            unset($product_data['product']);
        }

        //Since C2C, it's possible to modify products from C area
        if( 'C' == AREA && isset($_SESSION['auth'])) {
            $auth = $_SESSION['auth'];
        }

        // Check if status will be modified
        if (\strlen($product_data['status']) > 0) {
            $old_status = db_get_field("SELECT status FROM cscart_products WHERE product_id = ?i AND status <> ?s", $product_id, $product_data['status']);
        } else {
            unset($_data['status']);
        }

        $arow = db_query("UPDATE ?:products SET ?u WHERE product_id = ?i", $_data, $product_id);

        $_data = $product_data;
        if (\strlen($_data['product']) > 0) {
            $_data['product'] = trim($_data['product'], " -");
        }

        if (\array_key_exists('lang_code', $_data) === false ||  \strlen($_data['lang_code'])  === 0) {
            $_data['lang_code'] = $lang_code;
        }

        db_query("UPDATE ?:product_descriptions SET ?u WHERE product_id = ?i AND lang_code = ?s", $_data, $product_id, $lang_code);

        if ($arow === false) {
            fn_set_notification('E', __('error'), __('object_not_found', array('[object]' => __('product'))), '', '404');
            $product_id = false;
        }
    }

    if ($product_id) {
        /** @var ProductManager */
        $productManager = container()->get('cscart.product_manager');
        // invalidate productManager cache
        $productManager->invalidate($product_id);

        // Update product prices
        $product_data = fn_update_product_prices($product_id, $product_data);

        // Update product categories
        $categoriesUpdated = fn_update_product_categories($product_id, $product_data);

        // Recount products in categories if status has been modified
        // Skip recount if the count has been modified by fn_update_product_categories
        if (!$categoriesUpdated && !empty($old_status)) {
            $visibleToHiddenOrDisabled = $old_status == \Wizacha\Status::ENABLED && in_array($product_data['status'], [
                \Wizacha\Status::HIDDEN,
                \Wizacha\Status::DISABLED,
            ]);
            $hiddenOrDisabledToVisible = $product_data['status'] == \Wizacha\Status::ENABLED && in_array($old_status, [
                \Wizacha\Status::HIDDEN,
                \Wizacha\Status::DISABLED,
            ]);

            $categories = $product_data['category_ids'] ?? [
                db_get_field("SELECT category_id FROM cscart_products_categories WHERE product_id = ?i AND link_type = 'M'", $product_id)
            ];

            if ($visibleToHiddenOrDisabled || $hiddenOrDisabledToVisible) {
                container()->get('marketplace.pim.category_service')->updateProductCount($categories);
            }
        }

        // Update product features value
        $product_data['product_features'] = !empty($product_data['product_features']) ? $product_data['product_features'] : array();
        $product_data['add_new_variant'] = !empty($product_data['add_new_variant']) ? $product_data['add_new_variant'] : array();
        fn_update_product_features_value($product_id, $product_data['product_features'], $product_data['add_new_variant'], $lang_code);

        // Update main images pair
        fn_attach_image_pairs('product_main', 'product', $product_id, $lang_code);

        // Update additional images
        fn_attach_image_pairs('product_additional', 'product', $product_id, $lang_code);

        // Adding new additional images
        fn_attach_image_pairs('product_add_additional', 'product', $product_id, $lang_code);

        if (isset($product_data['video'])) {
            $videoService = container()->get('marketplace.pim.video_service');
            $currentVideo = $videoService->findOneByProductId($product_id);

            if ($currentVideo !== null && $product_data['video'] !== $currentVideo->getId()) {
                $videoService->delete($currentVideo->getId());
            }

            if (empty($product_data['video']) === false && preg_match($isUuid, $product_data['video']) === 1) {
                $video = $videoService->get($product_data['video']);
                if ($video !== null && $video->getProductId() === null) {
                    $videoService->addVideoToProduct($video, $product_id);
                }
            }
        }

        // Save divisions settings
        if (true === container()->getParameter('feature.available_offers')
            && true === \is_array($_data['divisions'])
        ) {
            container()
                ->get('marketplace.divisions_settings.service')
                ->updateProductDivisionSettings(
                    $product_id,
                    new ApiDivisionSettingsDto(
                        $_data['divisions']['included'],
                        $_data['divisions']['excluded']
                    )
                )
            ;
        }
    }

    fn_bestsellers_update_product_post($product_data, $product_id, $lang_code, $create);
    fn_discussion_update_product_post($product_data, $product_id, $lang_code, $create);

    if (Registry::get('runtime.company_id')) {
        $product_data['company_id'] = Registry::get('runtime.company_id');
    }
    if (empty($product_data['categories'])) {
        $product_data['categories'] = db_get_fields('SELECT category_id FROM ?:products_categories WHERE product_id = ?i', $product_id);
        $product_data['categories'] = implode(',', $product_data['categories']);
    }
    fn_seo_update_object($product_data, $product_id, 'p', $lang_code);

    try {
        container()->get('marketplace.pim.product.service')->refresh((int) $product_id);
    } catch (\Exception $ex) {
        // silent fail because the product may not be in the doctrine identity map
    }

    if (($dispatch === true || $event === Product::EVENT_CREATE) && Product::hasChanged($product_id, new Locale($lang_code))) {
        \Wizacha\Events\Config::dispatch(
            $event,
            (new \Wizacha\Events\IterableEvent)->setElement($product_id)
        );
    }

    return $product_id;
}

/**
 * @return bool True if something was updated
 */
function fn_update_product_categories(int $product_id, array $product_data) : bool
{
    $cid = Product::getMainCategoryFromData($product_data);
    if (!$cid) {
        return false;
    }

    $existingCategory = db_get_field(
        "SELECT category_id FROM ?:products_categories WHERE product_id = ?i AND link_type = 'M'", $product_id
    );

    $needToUpdateCategory = $existingCategory != $cid;
    if ($needToUpdateCategory) {
        /** @var MutexService */
        $mutexService = container()->get(MutexService::class);
        $mutex = $mutexService->createBlockingMutex(
            'product_categories',
            $product_id
        );

        db_query("DELETE FROM ?:products_categories WHERE product_id = ?i", $product_id);
        db_query("INSERT INTO ?:products_categories ?e", [
            'product_id' => $product_id,
            'category_id' => $cid,
            'position' => 0,
            'link_type' => 'M'
        ]);

        unset($mutex); // Force release mutex now

        $ids = array_filter([
            $existingCategory,
            $cid,
        ]);
        container()->get('marketplace.pim.category_service')->updateProductCount($ids);
    }

    return $needToUpdateCategory;
}

function fn_update_product_features_value($product_id, $product_features, $add_new_variant, $lang_code)
{
    if (empty($product_features)) {
        return false;
    }

    $featuresTypes = db_get_hash_single_array('SELECT feature_id, feature_type FROM cscart_product_features WHERE feature_id IN (?n)', ['feature_id', 'feature_type'], array_keys($product_features));
    foreach ($product_features as $feature_id => $value) {
        if (is_array($value) && ($featuresTypes[$feature_id] ?? '') !== AttributeType::CHECKBOX_MULTIPLE) {
            fn_set_notification('E', __('error'), __('product_feature_cannot_save_array', [
                '[feature_id]' => $feature_id,
            ]), '', 'cannot_save_multiple_values');

            return false;
        }
    }

    $i_data = array(
        'product_id' => $product_id,
        'lang_code' => $lang_code
    );

    $idPathQuery = 'SELECT ?:categories.id_path FROM ?:categories LEFT JOIN ';
    if (Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct::isMultiVendorProductId($product_id)) {
        $idPathQuery .= 'doctrine_multi_vendor_product ON ?:categories.category_id = doctrine_multi_vendor_product.category_id WHERE doctrine_multi_vendor_product.id = ?s';
    } else {
        $idPathQuery .= '?:products_categories ON ?:categories.category_id = ?:products_categories.category_id WHERE ?:products_categories.product_id = ?i';
    }
    $id_paths = db_get_fields($idPathQuery, $product_id);
    foreach ($product_features as $feature_id => $value) {

        // Check if feature is applicable for this product and can be edited by the user
        $_params = array(
            'category_ids' => array_unique(explode('/', implode('/', $id_paths))),
            'exclude_admin_only' => boolval(Registry::get('runtime.company_id')),
            'feature_id' => $feature_id
        );
        list($_feature) = fn_get_product_features($_params);

        if (empty($_feature)) {
            $_feature = db_get_field("SELECT description FROM ?:product_features_descriptions WHERE feature_id = ?i AND lang_code = ?s", $feature_id, $lang_code);
            $_product = db_get_field("SELECT product FROM ?:product_descriptions WHERE product_id = ?i AND lang_code = ?s", $product_id, $lang_code);
            fn_set_notification('E', __('error'), __('product_feature_cannot_assigned', array(
                '[feature_name]' => $_feature,
                '[product_name]' => $_product
            )));

            continue;
        }

        $i_data['feature_id'] = $feature_id;
        unset($i_data['value']);
        unset($i_data['variant_id']);
        unset($i_data['value_int']);
        $feature_type = $featuresTypes[$feature_id];

        // Delete variants in current language
        if ($feature_type == 'T') {
            db_query("DELETE FROM ?:product_features_values WHERE feature_id = ?i AND product_id = ?s AND lang_code = ?s", $feature_id, $product_id, $lang_code);
        } else {
            db_query("DELETE FROM ?:product_features_values WHERE feature_id = ?i AND product_id = ?s", $feature_id, $product_id);
        }

        if ($feature_type == 'D') {
            $i_data['value_int'] = !empty($value) ? fn_parse_date($value) : null;
        } elseif ($feature_type == 'M') {
            if (!empty($add_new_variant[$feature_id]['variant']) || (isset($add_new_variant[$feature_id]['variant']) && $add_new_variant[$feature_id]['variant'] === '0')) {
                $value = empty($value) ? array() : $value;
                $value[] = fn_add_feature_variant($feature_id, $add_new_variant[$feature_id]);
            }
            if (is_array($value)) {
                foreach ($value as $variant_id) {
                    foreach (Languages::getAll() as $i_data['lang_code'] => $_d) { // insert for all languages
                        $i_data['variant_id'] = $variant_id;
                        db_query("REPLACE INTO ?:product_features_values ?e", $i_data);
                    }
                }
            }
            continue;
        } elseif (in_array($feature_type, array('S', 'N', 'E'))) {
            if (!empty($add_new_variant[$feature_id]['variant']) || (isset($add_new_variant[$feature_id]['variant']) && $add_new_variant[$feature_id]['variant'] === '0')) {
                $i_data['variant_id'] = fn_add_feature_variant($feature_id, $add_new_variant[$feature_id], $id_paths);
                $i_data['value_int'] = $add_new_variant[$feature_id]['variant'];
            } elseif (!empty($value) && $value != 'disable_select') {
                if ($feature_type == 'N') {
                    $i_data['value_int'] = db_get_field("SELECT variant FROM ?:product_feature_variant_descriptions WHERE variant_id = ?i AND lang_code = ?s", $value, $lang_code);
                }
                $i_data['variant_id'] = $value;
            } else {
                continue;
            }
        } else {
            if ($value == '') {
                continue;
            }
            if ($feature_type == 'O') {
                $i_data['value_int'] = $value;
            } else {
                $i_data['value'] = $value;
            }
        }

        if ($feature_type != 'T') { // feature values are common for all languages, except text (T)
            foreach (Languages::getAll() as $i_data['lang_code'] => $_d) {
                db_query("REPLACE INTO ?:product_features_values ?e", $i_data);
            }
        } else { // for text feature, update current language only
            $i_data['lang_code'] = $lang_code;
            db_query("INSERT INTO ?:product_features_values ?e", $i_data);
        }
    }

    return true;
}

/**
 * Adds or updates category
 *
 * @param array $category_data Category data
 * @param int $category_id Category identifier
 * @param string $lang_code Two-letter language code (e.g. 'en', 'ru', etc.)
 * @return int New or updated category identifier
 */
function fn_update_category($category_data, $category_id = 0, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $category_data = \Wizacha\Category::setDefaultValues($category_data ,$lang_code);

    if($category_id) {
        \Wizacha\AgeVerification::onCategoryUpdate($category_data, $category_id);
    } else {
        \Wizacha\AgeVerification::onCategoryCreate($category_data);
    }

    // category title required
    if (empty($category_data['category'])) {
        //return false; // FIXME: management page doesn't have category name
    }

    $_data = $category_data;

    if (isset($category_data['timestamp'])) {
        $_data['timestamp'] = fn_parse_date($category_data['timestamp']);
    }

    if (isset($_data['position']) && empty($_data['position']) && $_data['position'] != '0' && isset($_data['parent_id'])) {
        $_data['position'] = db_get_field("SELECT max(position) FROM ?:categories WHERE parent_id = ?i", $_data['parent_id']);
        $_data['position'] = $_data['position'] + 10;
    }

    // Sanitize category description
    if (true === \array_key_exists('description', $category_data) && $category_data['description'] !== null) {
        $purifierService = container()->get('purifier.default');
        $category_data['description'] = \trim($purifierService->purify($category_data['description']));
    }

    if (!empty($_data['transaction_mode'])) {
        $transactionModeService = container()->get('marketplace.transaction_mode.service');
        if (!in_array($_data['transaction_mode'], $transactionModeService->getAvailablesModesAsArray())) {
            unset($_data['transaction_mode']);
        }
    }

    // create new category
    if (empty($category_id)) {
        $create = true;
        $category_id = db_query("INSERT INTO ?:categories ?e", $_data);

        if (empty($category_id)) {
            $category_id = false;
        }

        // now we need to update 'id_path' field, as we know $category_id
        /* Generate id_path for category */
        $parent_id = intval($_data['parent_id']);
        if ($parent_id == 0) {
            $id_path = $category_id;
        } else {
            $id_path = db_get_row("SELECT id_path FROM ?:categories WHERE category_id = ?i", $parent_id);
            if (empty($id_path)) {
                fn_set_notification('E', __('error'), __('object_not_found', array('[object]' => __('parent_category'))));
                $category_id = false;
            } else {
                $id_path = $id_path['id_path'] . '/' . $category_id;
            }
        }

        if ($category_id !== false) {
            db_query('UPDATE ?:categories SET ?u WHERE category_id = ?i', array('id_path' => $id_path), $category_id);

            //
            // Adding same category descriptions for all cart languages
            //
            $_data = $category_data;
            $_data['category_id'] = $category_id;

            \Tygh\Languages\Helper::insertTranslations('category_descriptions', $lang_code, $_data);
        }

        // update existing category
    } else {

        /* regenerate id_path for all child categories of the updated category */
        if (isset($category_data['parent_id'])) {
            fn_change_category_parent($category_id, intval($category_data['parent_id']));
        }

        $arow = db_query("UPDATE ?:categories SET ?u WHERE category_id = ?i", $_data, $category_id);
        $_data = $category_data;
        $arow_description = db_query("UPDATE ?:category_descriptions SET ?u WHERE category_id = ?i AND lang_code = ?s", $_data, $category_id, $lang_code);

        if ($arow === false && $arow_description === false) {
            fn_set_notification('E', __('error'), __('object_not_found', array('[object]' => __('category'))), '', '404');
            $category_id = false;
        }
    }

    if ($category_id) {
        //Invalid categories_menu handler
        \Wizacha\Registry::defaultInstance()->invalidHandler(\Wizacha\Cache\Handler::CATEGORIES_MENU);

        // Log category add/update
        fn_log_event('categories', !empty($create) ? 'create' : 'update', array(
            'category_id' => $category_id,
        ));
    }

    fn_seo_update_object($category_data, $category_id, 'c', $lang_code);
    \Wizacha\Option::updateOptions();
    if (!empty($category_data['discussion_type'])) {
        $discussion = array(
            'object_type' => 'C',
            'object_id' => $category_id,
            'type' => $category_data['discussion_type']
        );
        fn_update_discussion($discussion);
    }

    if ($category_id) {
        \Wizacha\Events\Config::dispatch(
            \Wizacha\Category::EVENT_UPDATE,
            (new \Wizacha\Events\IterableEvent)->setElement($category_id)
        );
    }

    return $category_id;

}

/**
 * Changes category parent
 *
 * @param int $category_id Category identifier
 * @param int $new_parent_id Identifier of new category parent
 * @return bool True on success, false otherwise
 */
function fn_change_category_parent($category_id, $new_parent_id)
{
    if (!empty($category_id)) {
        $new_parent_path = db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i", $new_parent_id);
        $current_path = db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i", $category_id);

        if (!empty($new_parent_path) && !empty($current_path)) {
            db_query("UPDATE ?:categories SET parent_id = ?i, id_path = ?s WHERE category_id = ?i", $new_parent_id, "$new_parent_path/$category_id", $category_id);
            db_query("UPDATE ?:categories SET id_path = CONCAT(?s, SUBSTRING(id_path, ?i)) WHERE id_path LIKE ?l", "$new_parent_path/$category_id/", strlen($current_path . '/') + 1, "$current_path/%");
        } elseif (empty($new_parent_path) && !empty($current_path)) {
            db_query("UPDATE ?:categories SET parent_id = ?i, id_path = ?i WHERE category_id = ?i", $new_parent_id, $category_id, $category_id);
            db_query("UPDATE ?:categories SET id_path = CONCAT(?s, SUBSTRING(id_path, ?i)) WHERE id_path LIKE ?l", "$category_id/", strlen($current_path . '/') + 1, "$current_path/%");
        }

        return true;
    }

    return false;
}

/**
 * Delete product option combination
 *
 * @param string $combination_hash Numeric Hash of options combination. (E.g. '3364473348')
 * @return bool Always true
 */
function fn_delete_product_combination($combination_hash)
{
    fn_delete_image_pairs($combination_hash, 'product_option');

    db_query("DELETE FROM ?:product_options_inventory WHERE combination_hash = ?i", $combination_hash);

    return true;
}

/**
 * Removes options and their variants by option identifier
 *
 * @param int $option_id Option identifier
 * @param int $pid Identifier of the product from which the option should be removed (for global options)
 * @return bool True on success, false otherwise
 */
function fn_delete_product_option($option_id, $pid = 0)
{
    $option_deleted = false;

    if (!empty($option_id)) {
        $condition = fn_get_company_condition('?:product_options.company_id');
        $_otps = db_get_row("SELECT product_id, inventory FROM ?:product_options WHERE option_id = ?i $condition", $option_id);
        if (empty($_otps)) {
            return false;
        }

        $product_id = $_otps['product_id'];
        $option_inventory = $_otps['inventory'];
        $product_link = db_get_fields("SELECT product_id FROM ?:product_global_option_links WHERE option_id = ?i AND product_id = ?i", $option_id, $pid);
        if (empty($product_id) && !empty($product_link)) {
            // Linked option
            $option_description = db_get_field("SELECT option_name FROM ?:product_options_descriptions WHERE option_id = ?i AND lang_code = ?s", $option_id, (string) GlobalState::interfaceLocale());
            db_query("DELETE FROM ?:product_global_option_links WHERE product_id = ?i AND option_id = ?i", $pid, $option_id);
            fn_set_notification('W', __('warning'), __('option_unlinked', array(
                '[option_name]' => $option_description
            )));
        } else {
            // Product option
            db_query("DELETE FROM ?:product_options_descriptions WHERE option_id = ?i", $option_id);
            db_query("DELETE FROM ?:product_options WHERE option_id = ?i", $option_id);
            fn_delete_product_option_variants($option_id);
        }

        if ($option_inventory == "Y" && !empty($product_id)) {
            fn_delete_product_option_combinations($product_id);
        }

        $option_deleted = true;
    }

    return $option_deleted;
}

/**
 * Deletes product option combinations and its data (images). Used when deleting or changing product option.
 *
 * @param int $product_id Product Id.
 */
function fn_delete_product_option_combinations($product_id)
{
    if (!empty($product_id)) {
        $c_ids = db_get_fields("SELECT combination_hash FROM ?:product_options_inventory WHERE product_id = ?i", $product_id);
        db_query("DELETE FROM ?:product_options_inventory WHERE product_id = ?i", $product_id);
        foreach ($c_ids as $c_id) {
            fn_delete_image_pairs($c_id, 'product_option', '');
        }
    }
}

/**
 * Removes option variants
 *
 * @param int $option_id Option identifier: if given, all the option variants are deleted
 * @param int $variant_ids Variants identifiers: used if option_id is empty
 * @return bool Always true
 */
function fn_delete_product_option_variants($option_id = 0, $variant_ids = array())
{
    if (!empty($option_id)) {
        $_vars = db_get_fields("SELECT variant_id FROM ?:product_option_variants WHERE option_id = ?i", $option_id);
    } elseif (!empty($variant_ids)) {
        $_vars = db_get_fields("SELECT variant_id FROM ?:product_option_variants WHERE variant_id IN (?n)", $variant_ids);
    }

    if (!empty($_vars)) {
        foreach ($_vars as $v_id) {
            db_query("DELETE FROM ?:product_option_variants_descriptions WHERE variant_id = ?i", $v_id);
            fn_delete_image_pairs($v_id, 'variant_image');
        }

        db_query("DELETE FROM ?:product_option_variants WHERE variant_id IN (?n)", $_vars);
    }
    return true;
}

/**
 * Gets product options
 *
 * @param array $product_ids Product identifiers
 * @param string $lang_code 2-letters language code
 * @param bool $only_selectable Flag that forces to retreive the options with certain types (default: select, radio or checkbox)
 * @param bool $inventory Get only options with the inventory tracking
 * @param bool $only_avail Get only available options
 * @param bool $skip_global Get only general options, not global options, applied as link
 * @return array List of product options data
 */
function fn_get_product_options($product_ids, $lang_code = null, $only_selectable = false, $inventory = false, $only_avail = false, $skip_global = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $condition = $_status = $join = '';
    $extra_variant_fields = '';
    $option_ids = $variants_ids = $options = array();
    $selectable_option_types = array('S', 'R', 'C');

    if (AREA == 'C' || $only_avail == true) {
        $_status .= " AND status = 'A'";
    }
    if ($only_selectable == true) {
        $condition .= db_quote(" AND a.option_type IN(?a)", $selectable_option_types);
    }
    if ($inventory == true) {
        $condition .= " AND a.inventory = 'Y'";
    }

    $join = db_quote(" LEFT JOIN ?:product_options_descriptions as b ON a.option_id = b.option_id AND b.lang_code = ?s ", $lang_code);
    $fields = "a.*, b.option_name, b.option_text, b.description, b.inner_hint, b.incorrect_message, b.comment";

    // FIXME 2tl show admin
    $condition .= fn_get_company_condition('a.company_id', true, '', true);

    if (!empty($product_ids)) {
        $_options = db_get_memoized_hash_multi_array(
            "SELECT " . $fields
            . " FROM ?:product_options as a "
            . $join
            . " WHERE a.product_id IN (?n)" . $condition . $_status
            . " ORDER BY a.position",
            array('product_id', 'option_id'), $product_ids
        );
        if (!$skip_global) {
            $global_options = db_get_memoized_hash_multi_array(
                "SELECT c.product_id AS cur_product_id, a.option_id, b.option_name, a.*, b.option_text, b.description, b.inner_hint, b.incorrect_message, b.comment"
                . " FROM ?:product_options as a"
                . " LEFT JOIN ?:product_options_descriptions as b ON a.option_id = b.option_id AND b.lang_code = ?s"
                . " LEFT JOIN ?:product_global_option_links as c ON c.option_id = a.option_id"
                . " WHERE c.product_id IN (?n) AND a.product_id = 0" . $condition . $_status
                . " ORDER BY a.position",
                array('cur_product_id', 'option_id'), $lang_code, $product_ids
            );
        }
        foreach ((array)$product_ids as $product_id) {
            $_opts = (empty($_options[$product_id]) ? array() : $_options[$product_id]) + (empty($global_options[$product_id]) ? array() : $global_options[$product_id]);
            $options[$product_id] = fn_sort_array_by_key($_opts, 'position');
        }
    } else {
        //we need a separate query for global options
        $options = db_get_memoized_hash_multi_array(
            "SELECT a.*, b.option_name, b.option_text, b.description, b.inner_hint, b.incorrect_message, b.comment"
            . " FROM ?:product_options as a"
            . $join
            . " WHERE a.product_id = 0" . $condition . $_status
            . " ORDER BY a.position",
            array('product_id', 'option_id')
        );
    }

    foreach ($options as $product_id => $_options) {
        $option_ids = array_merge($option_ids, array_keys($_options));
    }

    if (empty($option_ids)) {
        if (is_array($product_ids)) {
            return $options;
        } else {
            return !empty($options[$product_ids]) ? $options[$product_ids] : array();
        }
    }

    $_status = (AREA == 'A') ? '' : " AND a.status='A'";

    $v_fields = "a.variant_id, a.option_id, a.position, a.modifier, a.modifier_type, a.weight_modifier, a.weight_modifier_type, $extra_variant_fields b.variant_name";
    $v_join = db_quote("LEFT JOIN ?:product_option_variants_descriptions as b ON a.variant_id = b.variant_id AND b.lang_code = ?s", $lang_code);
    $v_condition = db_quote("a.option_id IN (?n) $_status", array_unique($option_ids));
    $v_sorting = "a.position, a.variant_id";

    $variants = db_get_memoized_hash_multi_array("SELECT $v_fields FROM ?:product_option_variants as a $v_join WHERE $v_condition ORDER BY $v_sorting", array('option_id', 'variant_id'));

    foreach ($variants as $option_id => $_variants) {
        $variants_ids = array_merge($variants_ids, array_keys($_variants));
    }

    if (empty($variants_ids)) {
        return is_array($product_ids) ? $options : $options[$product_ids];
    }

    foreach ($options as $product_id => &$_options) {
        foreach ($_options as $option_id => &$_option) {
            // Add variant names manually, if this option is "checkbox"
            if ($_option['option_type'] == 'C' && !empty($variants[$option_id])) {
                foreach ($variants[$option_id] as $variant_id => $variant) {
                    $variants[$option_id][$variant_id]['variant_name'] = $variant['position'] == 0 ? __('no') : __('yes');
                }
            }

            $_option['variants'] = !empty($variants[$option_id]) ? $variants[$option_id] : array();
            $_option['is_system'] = is_string($_option['code']);
        }
    }

    return is_array($product_ids) ? $options : $options[$product_ids];
}

/**
 * Returns a array of product options using some params
 *
 * @param array $params - array of params
 * @param int $items_per_page - items per page
 * @param $lang_code - language code
 * @return array ($product_options, $params, $product_options_count)
 */
function fn_get_product_global_options($params = array(), $items_per_page = 0, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $params = LastView::instance()->update('product_global_options', $params);

    $default_params = array(
        'product_id' => 0,
        'page' => 1,
        'items_per_page' => $items_per_page
    );

    $params = array_merge($default_params, $params);

    $fields = array(
        '?:product_options.*',
        '?:product_options_descriptions.*',
    );

    $condition = $join = '';

    $join .= db_quote("LEFT JOIN ?:product_options_descriptions ON ?:product_options_descriptions.option_id = ?:product_options.option_id AND ?:product_options_descriptions.lang_code = ?s ", $lang_code);

    $order = 'ORDER BY position';

    $params['product_id'] = !empty($params['product_id']) ? $params['product_id'] : 0;
    $condition .= db_quote(" AND ?:product_options.product_id = ?i", $params['product_id']);

    // FIXME 2tl show admin
    $condition .= fn_get_company_condition('company_id', true, '', true);
    if (isset($params['premoderation'])) {
        $condition .= W_SUBIMTTED_OPTION_CONDITION;
    }

    if (isset($params['display_on_faceting']) && \is_bool($params['display_on_faceting']) === true) {
        $condition .= db_quote(" AND display_on_faceting = ?i", $params['display_on_faceting']);
    }

    if (isset($params['status']) && \is_string($params['status']) === true) {
        $condition .= db_quote(" AND status = ?i", $params['status']);
    }

    $limit = '';
    if (!empty($params['items_per_page'])) {
        $params['total_items'] = db_get_field("SELECT COUNT(*) FROM ?:product_options $join WHERE 1 $condition");
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    $data = db_get_array("SELECT " . implode(', ', $fields) . " FROM ?:product_options $join WHERE 1 $condition $order $limit ");

    return array($data, $params);
}

/**
 * Permet de récupérer toutes les options systèmes de la MP
 */
function fn_get_system_options(array $params = [], int $itemsPerPage = 0, string $langCode = null): array
{
    $langCode = $langCode ?? (string) GlobalState::interfaceLocale();
    $params = array_merge(
        [
            'product_id' => 0,
            'page' => 1,
            'items_per_page' => $itemsPerPage,
        ],
        LastView::instance()->update('product_global_options', $params)
    );

    $condition = implode(
        " AND ",
        [
            "po.code IS NOT NULL",
        ]
    );

    $limit = "";

    if (array_key_exists('items_per_page', $params)) {
        $params['total_items'] = db_get_field("SELECT COUNT(po.option_id) FROM ?:product_options as po WHERE $condition");
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    return [
        db_get_memoized_array(
            "SELECT po.*, pod.option_name, pod.option_text, pod.description, pod.inner_hint, pod.incorrect_message, pod.comment "
            . " FROM cscart_product_options as po "
            . db_quote(" LEFT JOIN cscart_product_options_descriptions as pod ON po.option_id = pod.option_id AND pod.lang_code = ?s ", $langCode)
            . " WHERE " . $condition
            . " ORDER BY po.position, pod.option_name "
            . $limit
        ),
        $params,
    ];
}

/** @return null|mixed[] */
function fn_get_system_option(int $id, string $langCode = null): ?array
{
    $langCode = $langCode ?? (string) GlobalState::interfaceLocale();

    $option = db_get_array(
        "SELECT po.*, pod.option_name, pod.option_text, pod.description, pod.inner_hint, pod.incorrect_message, pod.comment "
        . " FROM cscart_product_options as po "
        . db_quote(" LEFT JOIN cscart_product_options_descriptions as pod ON po.option_id = pod.option_id AND pod.lang_code = ?s ", $langCode)
        . db_quote(" WHERE po.option_id = ?i AND po.code IS NOT NULL")
        . " ORDER BY po.position, pod.option_name ",
        $id
    );

    return \array_key_exists(0, $option) ? $option[0] : null;
}

function fn_get_system_option_code(int $optionId): ?string
{
    return db_get_field("SELECT o.code FROM ?:product_options as o WHERE o.option_id = ?i", $optionId);
}

/**
 * Returns an array of product options with values by combination
 * @see fn_get_options_combination if you want to do the reverse operation
 *
 * @param string $combination Options combination code
 * @return array Options decoded from combination
 */

function fn_get_product_options_by_combination($combination) : array
{
    $options = [];
    $_comb = explode('_', $combination);
    if (!empty($_comb) && is_array($_comb)) {
        $iterations = count($_comb);
        for ($i = 0; $i < $iterations; $i += 2) {
            $options[$_comb[$i]] = isset($_comb[$i + 1]) ? $_comb[$i + 1] : '';
        }
    }
    return $options;
}

/**
 * Removes all product options from the product
 * @param int $product_id Product identifier
 */
function fn_poptions_delete_product($product_id)
{
    $option_ids = db_get_fields('SELECT option_id FROM ?:product_options WHERE product_id = ?i', $product_id);
    if (!empty($option_ids)) {
        foreach ($option_ids as $option_id) {
            fn_delete_product_option($option_id, $product_id);
        }
    }

    db_query("DELETE FROM ?:product_options_exceptions WHERE product_id = ?i", $product_id);
    db_query("DELETE FROM ?:product_global_option_links WHERE product_id = ?i", $product_id);

    $option_combinations = db_get_fields('SELECT combination_hash FROM ?:product_options_inventory WHERE product_id = ?i', $product_id);
    if (!empty($option_combinations)) {
        foreach ($option_combinations as $hash) {
            fn_delete_product_combination($hash);
        }
    }
}

/**
 * Gets product options with the selected values data
 *
 * @param int $product_id Product identifier
 * @param array $selected_options Selected opotions values
 * @param string $lang_code 2-letters language code
 * @return array List of product options with selected values
 */
function fn_get_selected_product_options($product_id, $selected_options, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $extra_variant_fields = '';
    $fields = db_quote("a.*, b.option_name, b.option_text, b.description, b.inner_hint, b.incorrect_message, b.comment, a.status");
    $condition = db_quote("c.product_id = ?i AND a.status = 'A'", $product_id);
    $join = db_quote("LEFT JOIN ?:product_options_descriptions as b ON a.option_id = b.option_id AND b.lang_code = ?s LEFT JOIN ?:product_global_option_links as c ON c.option_id = a.option_id", $lang_code);

    $_opts = \Tygh\Database::getHash("SELECT $fields FROM ?:product_options as a $join WHERE $condition ORDER BY a.position", 'option_id');
    if (is_array($_opts)) {
        $_status = (AREA == 'A') ? '' : " AND a.status = 'A'";
        foreach ($_opts as $k => $v) {
            $_vars = db_get_hash_array("SELECT a.variant_id, a.position, a.modifier, a.modifier_type, a.weight_modifier, a.weight_modifier_type, $extra_variant_fields  b.variant_name FROM ?:product_option_variants as a LEFT JOIN ?:product_option_variants_descriptions as b ON a.variant_id = b.variant_id AND b.lang_code = ?s WHERE a.option_id = ?i $_status ORDER BY a.position", 'variant_id', $lang_code, $v['option_id']);

            // Add variant names manually, if this option is "checkbox"
            if ($v['option_type'] == 'C' && !empty($_vars)) {
                foreach ($_vars as $variant_id => $variant) {
                    $_vars[$variant_id]['variant_name'] = $variant['position'] == 0 ? __('no') : __('yes');
                }
            }

            $_opts[$k]['value'] = (!empty($selected_options[$v['option_id']])) ? $selected_options[$v['option_id']] : '';
            $_opts[$k]['variants'] = $_vars;
        }

    }

    return $_opts;
}

/**
 * Calculates product price/weight with options modifiers
 * It can be called to get price depending on its quantity using the $basketQuantity parameter (to be used in a basket context)
 *
 * @param array $product_options Product options
 * @param mixed $base_value Base price or weight value
 * @param string $type Calculation type (price or weight)
 * @param array $orig_options Original options
 * @param array $extra Extra data
 * @param mixed Recalculated value
 *
 * @return mixed New base value after applying modifiers
 */
function fn_apply_options_modifiers($product_options, $base_value, $type, $orig_options = array(), $extra = array(), int $basketQuantity = null)
{
    static $option_types_cache = array();
    static $option_modifiers_cache = array();

    $fields = ($type == 'P') ? "a.modifier, a.modifier_type" : "a.weight_modifier as modifier, a.weight_modifier_type as modifier_type";

    if (isset($extra['product_data']['product_id'])) {
        $combination = fn_get_options_combination($product_options);

        if (is_string($combination) === true && $combination !== '0') {
            if (is_int($basketQuantity) === true) {
                $combination_price = container()->get('marketplace.price_tier.price_tier_repository')
                    ->findByQuantityAndDeclination(
                        $extra['product_data']['product_id'],
                        fn_inventoryId_from_string($extra['product_data']['product_id'], $combination),
                        $basketQuantity
                    )
                    ->getPrice()
                    ->getConvertedAmount()
                ;
            } else {
                $combination_price = db_get_field(
                    'SELECT w_price FROM ?:product_options_inventory WHERE combination = ?s AND product_id = ?i',
                    $combination, $extra['product_data']['product_id']);
            }

            if ($combination_price) {
                $base_value = $combination_price;
            }
        }
    }

    $orig_value = $base_value;
    if (!empty($product_options)) {

        // Check options type. We need to apply only Selectbox, radiogroup and checkbox modifiers
        if (empty($orig_options)) {
            $_key = md5(serialize(array_keys($product_options)));
            if (!isset($option_types_cache[$_key])) {
                $option_types = db_get_hash_single_array("SELECT option_type as type, option_id FROM ?:product_options WHERE option_id IN (?n)", array('option_id', 'type'), array_keys($product_options));
                $option_types_cache[$_key] = $option_types;
            } else {
                $option_types = $option_types_cache[$_key];
            }
        } else {
            $option_types = array();
            foreach ($orig_options as $_opt) {
                $option_types[$_opt['option_id']] = $_opt['option_type'];
            }
        }

        foreach ($product_options as $option_id => $variant_id) {
            if (empty($option_types[$option_id]) || strpos('SRC', $option_types[$option_id]) === false) {
                continue;
            }
            if (empty($orig_options)) {
                $_key = md5($fields . $variant_id);
                if (!isset($option_modifiers_cache[$_key])) {
                    $om_join = "";
                    $om_condition = db_quote("a.variant_id = ?i", $variant_id);
                    $_mod = db_get_row("SELECT $fields FROM ?:product_option_variants a $om_join WHERE 1 AND $om_condition");
                    $option_modifiers_cache[$_key] = $_mod;
                } else {
                    $_mod = $option_modifiers_cache[$_key];
                }
            } else {
                foreach ($orig_options as $_opt) {
                    if ($_opt['value'] == $variant_id && !empty($variant_id)) {
                        $_mod = array();
                        $_mod['modifier'] = $_opt['modifier'];
                        $_mod['modifier_type'] = $_opt['modifier_type'];
                    }
                }
            }

            if (!empty($_mod)) {
                if ($_mod['modifier_type'] == 'A') {
                    // Absolute
                    if ($_mod['modifier'][0] == '-') {
                        $base_value = $base_value - floatval(substr($_mod['modifier'], 1));
                    } else {
                        $base_value = $base_value + floatval($_mod['modifier']);
                    }
                } else {
                    // Percentage
                    if ($_mod['modifier'][0] == '-') {
                        $base_value = $base_value - ((floatval(substr($_mod['modifier'], 1)) * $orig_value) / 100);
                    } else {
                        $base_value = $base_value + ((floatval($_mod['modifier']) * $orig_value) / 100);
                    }
                }
            }
        }
    }

    $base_value = ($base_value > 0) ? $base_value : 0;

    return $base_value;
}

/**
 * Returns selected product options.
 * For options wich type is checkbox function gets translation from langvars 'no' and 'yes' and return it as variant_name.
 *
 * @param array $selected_options Options as option_id => selected_variant_id.
 * @param string $lang_code 2digits language code.
 *
 * @return array Array of associative arrays wich contain options data.
 */
function fn_get_selected_product_options_info($selected_options, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    if (empty($selected_options) || !is_array($selected_options)) {
        return array();
    }
    $result = array();
    foreach ($selected_options as $option_id => $variant_id) {
        $_opts = db_get_row(
            "SELECT a.*, a.position as option_position, b.option_name, b.option_text, b.description, b.inner_hint, b.incorrect_message " .
            "FROM ?:product_options as a LEFT JOIN ?:product_options_descriptions as b ON a.option_id = b.option_id AND b.lang_code = ?s " .
            "WHERE a.option_id = ?i ORDER BY a.position",
            $lang_code, $option_id
        );

        if (empty($_opts)) {
            continue;
        }
        $_vars = array();
        if (strpos('SRC', $_opts['option_type']) !== false) {
            $_vars = db_get_memoized_row(
                "SELECT a.modifier, a.modifier_type, a.position, b.variant_name, img.image_id FROM ?:product_option_variants as a " .
                "LEFT JOIN ?:product_option_variants_descriptions as b ON a.variant_id = b.variant_id AND b.lang_code = ?s " .
                "LEFT JOIN cscart_images_links as img ON CAST(a.variant_id AS CHAR) = img.object_id AND img.object_type = 'variant_image' " .
                "WHERE a.variant_id = ?i ORDER BY a.position",
                $lang_code, $variant_id
            );
        }

        if ($_opts['option_type'] == 'C') {
            $_vars['variant_name'] = (empty($_vars['position'])) ? __('no', '', $lang_code) : __('yes', '', $lang_code);
        } elseif ($_opts['option_type'] == 'I' || $_opts['option_type'] == 'T') {
            $_vars['variant_name'] = $variant_id;
        } elseif (!isset($_vars['variant_name'])) {
            $_vars['variant_name'] = '';
        }

        $_vars['value'] = $variant_id;

        $result[] = fn_array_merge($_opts, $_vars);
    }

    return $result;
}

/**
 * Gets default product options
 *
 * @param integer $product_id Product identifier
 * @param bool $get_all Whether to get all the default options or not
 * @param array $product Product data
 * @return array The resulting array
 */
function fn_get_default_product_options($product_id, $get_all = false, $product = array())
{
    list($inventory) = Product::getInventory(['product_id' => $product_id]);
    $inventory = array_filter(
        $inventory,
        function($declination) {
            return !(empty($declination['amount']) || empty($declination['w_price']));
        }
    );

    if (empty($inventory)) {
        return [];
    }
    return reset($inventory)['combination'];
}

/**
 * Gets all possible options combinations
 *
 * @param array $options Options identifiers
 * @param array $variants Options variants identifiers in the order according to the $options parameter
 * @return array Combinations
 */
function fn_get_options_combinations($options, $variants)
{
    $combinations = array();

    // Take first option
    $options_key = array_keys($options);
    $variant_number = reset($options_key);
    $option_id = $options[$variant_number];

    // Remove current option
    unset($options[$variant_number]);

    // Get combinations for other options
    $sub_combinations = !empty($options) ? fn_get_options_combinations($options, $variants) : array();

    if (!empty($variants[$variant_number])) {
        // run through variants
        foreach ($variants[$variant_number] as $variant) {
            if (!empty($sub_combinations)) {
                // add current variant to each subcombination
                foreach ($sub_combinations as $sub_combination) {
                    $sub_combination[$option_id] = $variant;
                    $combinations[] = $sub_combination;
                }
            } else {
                $combinations[] = array(
                    $option_id => $variant
                );
            }
        }
    } else {
        $combinations = $sub_combinations;
    }

    return $combinations;
}

/**
 * Generates product variants combinations
 *
 * @param int   $product_id              Product identifier
 * @param int   $amount                  Default combination amount
 * @param array $options                 Array of option identifiers
 * @param array $variants                Array of option variant identifier arrays in the order according to the $options parameter
 * @param array $productOptionsInventory Array of old product options inventory
 *
 * @return array Array of combinations
 */
function fn_look_through_variants($product_id, $amount, $options, $variants, array $productOptionsInventory)
{
    //According to this addon, exceptions store allowed variants
    $allowed_variants = db_get_array("SELECT option_id, variant_id FROM ?:product_options_exceptions WHERE product_id = ?i", $product_id);
    $allowed_variants = array_reduce($allowed_variants, function (array &$r, $v) {
        $r[$v['option_id']][$v['variant_id']] = true;

        return $r;
    }, []);
    foreach ($options as $k => $option_id) {
        $option_variants = $allowed_variants[$option_id];
        $variants[$k] = array_filter($variants[$k], function ($variant_id) use ($option_variants) {
            return isset($option_variants[$variant_id]);
        });
    }
    $combinations = fn_get_options_combinations($options, $variants);

    if (!empty($combinations)) {
        /** @var PriceTierService */
        $priceTierService = container()->get('marketplace.price_tier.price_tier_service');
        /** @var ProductService */
        $productService = container()->get('marketplace.pim.product.service');

        foreach ($combinations as $combination) {
            $_data = [];
            $_data['product_id'] = $product_id;

            $_data['combination_hash'] = fn_generate_cart_id($product_id, array('product_options' => $combination));

            if (array_key_exists($_data['combination_hash'], $productOptionsInventory)) {
                // Update des données
                $oldData = $productOptionsInventory[$_data['combination_hash']];

                $_data['amount'] = $oldData['amount'] ?? $amount;
                $_data['crossed_out_price'] = $oldData['crossed_out_price'] ?? null;
                $_data['product_code'] = $oldData['product_code'] ?? '';
                $_data['affiliate_link'] = $oldData['affiliate_link'] ?? null;
                $_data['w_price'] = $oldData['w_price'];

                db_query("UPDATE ?:product_options_inventory SET ?u WHERE id = ?i", $_data, $oldData['id']);

                unset($productOptionsInventory[$_data['combination_hash']]);
            } else {
                // Creation des données
                $_data['combination'] = fn_get_options_combination($combination);
                $_data['amount'] = $amount;
                $_data['crossed_out_price'] = null;
                $_data['product_code'] = '';
                $_data['affiliate_link'] = null;
                $_data['w_price'] = 0;
                $_data['supplier_reference'] = null;

                db_query("INSERT INTO ?:product_options_inventory ?e", $_data);
            }

            // This condition is here to prevent price tiers duplicates when a reload-data is launched
            if ($_REQUEST['dispatch'] === 'product_options.exceptions') {
                $priceTierInventory = [
                    'product_id' => $product_id,
                    'combination' => $priceTierService->combinationToString($combination),
                    'priceTiers' => [
                        [
                            'price' => $_data['w_price'],
                            'lowerLimit' => 0,
                        ],
                    ],
                ];

                $priceTierService->updateCombinationPriceTiers(
                    $productService->get($product_id),
                    $priceTierInventory
                );
            }

            $combinations[] = $combination;
        }

        foreach ($productOptionsInventory as $productOption) {
            db_query("DELETE FROM ?:product_options_inventory WHERE id = ?i", $productOption['id']);
        }
    }

    return $combinations;
}

/**
 * Checks and rebuilds product options inventory if necessary
 *
 * @param int $product_id Product identifier
 * @param int $amount Default combination amount
 * @return boolean Always true
 */
function fn_rebuild_product_options_inventory($product_id, $amount = 50)
{
    //Si on est sur que `$product_id!=0` on pourrait supprimer le `a.product_id = ?i` puisque c'est toujours =0
    $_options = db_get_fields("SELECT a.option_id FROM ?:product_options as a LEFT JOIN ?:product_global_option_links as b ON a.option_id = b.option_id WHERE (a.product_id = ?i OR b.product_id = ?i) AND a.option_type IN ('S','R','C') AND a.inventory = 'Y' ORDER BY position", $product_id, $product_id);
    if (empty($_options)) {
        db_query("DELETE FROM ?:product_options_inventory WHERE product_id = ?i", $product_id);
        return;
    }

    $productOptionsInventory = db_get_array("SELECT id, product_code, combination_hash, amount, w_price, crossed_out_price, affiliate_link FROM ?:product_options_inventory WHERE product_id = ?i", $product_id);
    // On tris les resultats avec comme clé le hash
    foreach ($productOptionsInventory as $key => $d) {
        $productOptionsInventory[$d['combination_hash']] = $d;
        unset($productOptionsInventory[$key]);
    }

    foreach ($_options as $k => $option_id) {
        $variants[$k] = db_get_fields("SELECT variant_id FROM ?:product_option_variants WHERE option_id = ?i ORDER BY position", $option_id);
    }
    fn_look_through_variants($product_id, $amount, $_options, $variants, $productOptionsInventory);

    // Delete image pairs assigned to old combinations
    foreach (array_keys($productOptionsInventory) as $v) {
        fn_delete_image_pairs($v, 'product_option');
    }

    return true;
}

/**
 * Gets array of product features
 *
 * @param array $params Products features search params
 * @param int $items_per_page Items per page
 * @param string $lang_code 2-letters language code
 * @return array Array with 3 params
 *              array $data Products features data
 *              array $params Products features search params
 *              boolean $has_ungroupped Flag determines if there are features without group
 */
function fn_get_product_features($params = array(), $items_per_page = 0, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    // Init filter
    $params = LastView::instance()->update('product_features', $params);

    $default_params = array(
        'product_id' => 0,
        'category_ids' => array(),
        'statuses' => AREA == 'C' ? [Status::ENABLED] : [Status::ENABLED, Status::DISABLED],
        'variants' => false,
        'plain' => false,
        'feature_types' => array(),
        'feature_id' => 0,
        'display_on' => '',
        'is_searchable' => '',
        'used_for_recommendations' => '',
        'exclude_group' => false,
        'exclude_filters' => false,
        'page' => 1,
        'items_per_page' => $items_per_page,
        'exclude_admin_only' => false,
        'variants_selected_only' => false,
        'sort_variants' => true,
    );

    $params = array_merge($default_params, $params);

    // Si on a une company, alors on enlève les attributs désactivés de la recherche
    if (Registry::get('runtime.company_id')) {
        if (false !== $key = array_search('D', $params['statuses'])) {
            unset($params['statuses'][$key]);
        }
    }

    //On ne peut plus avoir de feature cachée. Le fait d'avoir le status H pertube l'utilisation de l'index en BDD.
    $key = array_search(Status::HIDDEN, $params['statuses']);
    if ($key !== false) {
        unset($params['statuses'][$key]);
    }

    $base_fields = $fields = array(
        'pf.feature_id',
        'pf.feature_code',
        'pf.company_id',
        'pf.feature_type',
        'pf.parent_id',
        'pf.display_on_product',
        'pf.display_on_faceting',
        'pf.editable_only_by_admin',
        'pf.is_searchable',
        'pf.used_for_recommendations',
        'pf.is_required',
        '?:product_features_descriptions.description',
        '?:product_features_descriptions.lang_code',
        'pf.categories_path',
        '?:product_features_descriptions.full_description',
        'pf.status',
        'pf.position'
    );

    $condition = $join = $group = '';

    $join .= db_quote(" LEFT JOIN ?:product_features_descriptions ON ?:product_features_descriptions.feature_id = pf.feature_id AND ?:product_features_descriptions.lang_code = ?s", $lang_code);
    $join .= db_quote(" LEFT JOIN ?:product_features AS groups ON pf.parent_id = groups.feature_id");

    $fields[] = 'groups.position AS group_position';

    if (!empty($params['product_id'])) {
        $join .= db_quote(" LEFT JOIN ?:product_features_values ON ?:product_features_values.feature_id = pf.feature_id  AND ?:product_features_values.product_id = ?s AND ?:product_features_values.lang_code = ?s", $params['product_id'], $lang_code);

        if (!empty($params['existent_only'])) {
            $condition .= db_quote(" AND IF(pf.feature_type = 'G' OR pf.feature_type = 'C', 1, ?:product_features_values.feature_id)");
        }

        $fields[] = '?:product_features_values.value';
        $fields[] = '?:product_features_values.variant_id';
        $fields[] = '?:product_features_values.value_int';
    }

    if (!empty($params['exclude_admin_only'])) {
        $condition .= db_quote(" AND pf.editable_only_by_admin = ?s", 'N');
    }

    if (!empty($params['feature_id'])) {
        $condition .= db_quote(" AND pf.feature_id = ?i", $params['feature_id']);
    }

    if (!empty($params['exclude_group'])) {
        $condition .= db_quote(" AND pf.feature_type != 'G'");
    }

    if (isset($params['description']) && fn_string_not_empty($params['description'])) {
        $condition .= db_quote(" AND ?:product_features_descriptions.description LIKE ?l", "%" . trim($params['description']) . "%");
    }

    if (!empty($params['statuses'])) {
        $condition .= db_quote(" AND pf.status IN (?a)", $params['statuses']);
    }

    if (isset($params['parent_id']) && $params['parent_id'] !== '') {
        $condition .= db_quote(" AND pf.parent_id = ?i", $params['parent_id']);
    }

    if (!empty($params['display_on']) && in_array($params['display_on'], array('product', 'catalog', 'header', 'faceting'))) {
        $condition .= " AND pf.display_on_".$params['display_on']." = 'Y'";
    }

    if (isset($params['is_searchable']) && in_array($params['is_searchable'], ['Y', 'N'])) {
        $condition .= db_quote(" AND pf.is_searchable = ?s", $params['is_searchable']);
    }

    if (isset($params['used_for_recommendations']) && in_array($params['used_for_recommendations'], ['Y', 'N'])) {
        $condition .= db_quote(" AND pf.used_for_recommendations = ?s", $params['used_for_recommendations']);
    }

    if (!empty($params['feature_types'])) {
        $condition .= db_quote(" AND pf.feature_type IN (?a)", $params['feature_types']);
    }

    if (!empty($params['category_ids'])) {
        $c_ids = is_array($params['category_ids']) ? $params['category_ids'] : fn_explode(',', $params['category_ids']);
        $find_set = array(
            " pf.categories_path = '' "
        );
        foreach ($c_ids as $k => $v) {
            $find_set[] = db_quote(" FIND_IN_SET(?i, pf.categories_path) ", $v);
        }
        $find_in_set = db_quote(" AND (?p)", implode('OR', $find_set));
        $condition .= $find_in_set;
    }

    if (!empty($params['exclude_filters'])) {
        $exclude_feature_id = db_get_fields("SELECT ?:product_filters.feature_id FROM ?:product_filters GROUP BY ?:product_filters.feature_id");
        if (!empty($exclude_feature_id)) {
            $condition .= db_quote(" AND pf.feature_id NOT IN (?a)", $exclude_feature_id);
            unset($exclude_feature_id);
        }
    }

    $condition .= fn_get_company_condition('pf.company_id',true,'',true);

    $limit = '';
    if (!empty($params['items_per_page'])) {
        $params['total_items'] = db_get_field("SELECT COUNT(*) FROM ?:product_features AS pf $join WHERE 1 $condition $group ORDER BY pf.position, ?:product_features_descriptions.description");
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    $data = db_get_hash_array("SELECT " . implode(', ', $fields) . " FROM ?:product_features AS pf $join WHERE 1 $condition $group ORDER BY group_position, pf.position, ?:product_features_descriptions.description $limit", 'feature_id');

    $has_ungroupped = false;
    if (!empty($data)) {
        if ($params['variants'] == true) {
            foreach ($data as $k => $v) {
                if (in_array($v['feature_type'], array('S', 'M', 'N', 'E'))) {
                    if ($v['feature_type'] != 'M' && empty($params['all_variants'])) {
                        $variants_per_page = PRODUCT_FEATURE_VARIANTS_THRESHOLD;
                    } else {
                        $variants_per_page = 0;
                    }

                    $_params = array(
                        'feature_id' => $v['feature_id'],
                        'product_id' => $params['product_id'],
                        'feature_type' => $v['feature_type'],
                        'get_images' => true,
                        'selected_only' => $params['variants_selected_only'],
                        'sort' => $params['sort_variants'],
                    );

                    list($data[$k]['variants'], $_search) = fn_get_product_feature_variants($_params, $variants_per_page, $lang_code);
                    if (
                        $_params['feature_type'] === \Wizacha\Marketplace\PIM\Attribute\AttributeType::LIST_BRAND
                        || !empty($_search['total_items']) && $_search['total_items'] > PRODUCT_FEATURE_VARIANTS_THRESHOLD
                    ) {
                        $data[$k]['use_variant_picker'] = true;
                    }
                }
            }
        }

        if ($params['plain'] == false) {
            // Get groups
            if (!empty($params['exclude_group'])) {
                $groups = db_get_hash_array("SELECT " . implode(', ', $base_fields) . " FROM ?:product_features AS pf LEFT JOIN ?:product_features_descriptions ON ?:product_features_descriptions.feature_id = pf.feature_id AND ?:product_features_descriptions.lang_code = ?s WHERE pf.feature_type = 'G' ORDER BY pf.position, ?:product_features_descriptions.description", 'feature_id', $lang_code);
                foreach ($data as $k => $v) {
                    if (empty($v['parent_id']) || empty($groups[$v['parent_id']])) {
                        $has_ungroupped = true;
                        break;
                    }
                }
                $data = fn_array_merge($data, $groups);
            }

            $delete_keys = array();
            $attributesInGroupsCanHaveCategories = container()->getParameter('feature.attributes_in_groups_can_override_categories');
            foreach ($data as $k => $v) {
                if (!empty($v['parent_id']) && !empty($data[$v['parent_id']])) {
                    $data[$v['parent_id']]['subfeatures'][$v['feature_id']] = $v;
                    $data[$k] = & $data[$v['parent_id']]['subfeatures'][$v['feature_id']];
                    $delete_keys[] = $k;
                }

                if ($attributesInGroupsCanHaveCategories) {
                    $enableGetDescription = $v['feature_type'] != AttributeType::GROUP; // Attributes + attributes in groups (not groups)
                } else {
                    $enableGetDescription = empty($v['parent_id']); // Attributes + groups (not attributes in groups)
                }

                if (!empty($params['get_descriptions']) && $enableGetDescription) {
                    $d = fn_get_categories_list($v['categories_path']);
                    $data[$k]['feature_description'] = __('display_on') . ': <span>' . implode(', ', $d) . '</span>';
                }
            }

            foreach ($delete_keys as $k) {
                unset($data[$k]);
            }
        }
    }

    return array($data, $params, $has_ungroupped);
}

/**
 * Gets single product feature data
 *
 * @param int $feature_id Feature identifier
 * @param boolean $get_variants Flag determines if product variants should be fetched
 * @param boolean $get_variant_images Flag determines if variant images should be fetched
 * @param string $lang_code 2-letters language code
 * @return array Product feature data
 */
function fn_get_product_feature_data($feature_id, $get_variants = false, $get_variant_images = false, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $fields = array(
        '?:product_features.feature_id',
        '?:product_features.feature_code',
        '?:product_features.company_id',
        '?:product_features.feature_type',
        '?:product_features.parent_id',
        '?:product_features.display_on_product',
        '?:product_features.display_on_faceting',
        '?:product_features.editable_only_by_admin',
        '?:product_features.is_searchable',
        '?:product_features.used_for_recommendations',
        '?:product_features.is_required',
        '?:product_features_descriptions.description',
        '?:product_features_descriptions.lang_code',
        '?:product_features.categories_path',
        '?:product_features_descriptions.full_description',
        '?:product_features.status',
        '?:product_features.feature_type',
        '?:product_features.position'
    );

    $join = db_quote("LEFT JOIN ?:product_features_descriptions ON ?:product_features_descriptions.feature_id = ?:product_features.feature_id AND ?:product_features_descriptions.lang_code = ?s", $lang_code);

    $condition = db_quote("?:product_features.feature_id = ?i", $feature_id);

    $feature_data = db_get_row("SELECT " . implode(",", $fields) . " FROM ?:product_features $join WHERE $condition");

    if ($get_variants == true) {
        list($feature_data['variants']) = fn_get_product_feature_variants(array(
            'feature_id' => $feature_id,
            'feature_type' => $feature_data['feature_type'],
            'get_images' => $get_variant_images
        ), 0, $lang_code);
    }

    return $feature_data;
}

/**
 * Gets product features list
 *
 * @param array $product Array with product data
 * @param string $display_on Code determines zone (product/catalog page) for that features are selected
 * @param string $lang_code 2-letters language code
 * @return array Product features
 */
function fn_get_product_features_list($product, $display_on = 'C', $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $product_id = $product['product_id'];

    $features_list = array();

    if ($display_on == 'A') {
        $condition = '';
    } else {
        $condition = " AND f.display_on_product = 'Y'";
    }

    $path = !empty($product['main_category']) ? explode('/', db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i", $product['main_category'])) : array();

    if (empty($path) === false) {
        $find_set = array(
            " f.categories_path = '' "
        );
        foreach ($path as $k => $v) {
            $find_set[] = db_quote(" FIND_IN_SET(?i, f.categories_path) ", $v);
        }
        $find_in_set = db_quote(" AND (?p)", implode('OR', $find_set));
        $condition .= $find_in_set;
    }

    $fields = db_quote("v.product_id, v.feature_id, v.value, v.value_int, v.variant_id, f.feature_type, fd.description, vd.variant, f.parent_id, ft.filter_id, ft.field_type, f.position, gf.position as gposition");
    $join = db_quote(
        "LEFT JOIN ?:product_features_values as v ON v.feature_id = f.feature_id "
        . " LEFT JOIN ?:product_features_descriptions as fd ON fd.feature_id = v.feature_id AND fd.lang_code = ?s"
        . " LEFT JOIN ?:product_feature_variants fv ON fv.variant_id = v.variant_id"
        . " LEFT JOIN ?:product_filters AS ft ON ft.feature_id = f.feature_id"
        . " LEFT JOIN ?:product_feature_variant_descriptions as vd ON vd.variant_id = fv.variant_id AND vd.lang_code = ?s"
        . " LEFT JOIN ?:product_features as gf ON gf.feature_id = f.parent_id AND gf.feature_type = ?s ",
        $lang_code, $lang_code, 'G');

    if (is_array($product_id)) {
        $condition = db_quote("f.status = 'A' AND v.product_id IN (?a) ?p AND (v.variant_id != 0 OR (f.feature_type != 'C' AND v.value != '') OR (f.feature_type = 'C') OR v.value_int != '') AND v.lang_code = ?s", $product_id, $condition, $lang_code);
    } else {
        $condition = db_quote("f.status = 'A' AND v.product_id = ?s ?p AND (v.variant_id != 0 OR (f.feature_type != 'C' AND v.value != '') OR (f.feature_type = 'C') OR v.value_int != '') AND v.lang_code = ?s", $product_id, $condition, $lang_code);
    }

    $_data = db_get_array("SELECT $fields FROM ?:product_features as f $join WHERE $condition ORDER BY fd.description, fv.position, vd.variant, vd.variant_id");

    if (!empty($_data)) {
        foreach ($_data as $k => $feature) {
            if (is_array($product_id) === false) {
                unset($feature['product_id']);
            }

            if ($feature['feature_type'] == 'C') {
                if ($feature['value'] != 'Y' && $display_on != 'A') {
                    unset($_data[$k]);
                    continue;
                }
            }

            if (empty($features_list[$feature['feature_id']])) {
                $features_list[$feature['feature_id']] = $feature;
            }

            if (!empty($feature['variant_id'])) { // feature has several variants
                if (!empty($feature['filter_id'])) {
                    $range_data = array(
                        'range_id' => $feature['variant_id'],
                        'range_name' => $feature['variant'],
                        'feature_type' => $feature['feature_type'],
                    );
                    $features_list[$feature['feature_id']]['features_hash'] = fn_add_range_to_url_hash('', $range_data, $feature['field_type']);
                }
                $features_list[$feature['feature_id']]['variants'][$feature['variant_id']] = array(
                    'value' => $feature['value'],
                    'value_int' => $feature['value_int'],
                    'variant_id' => $feature['variant_id'],
                    'variant' => $feature['variant']
                );
            }

            unset($features_list[$feature['feature_id']]['filter_id']);
            unset($features_list[$feature['feature_id']]['field_type']);
        }

    }

    $groups = array();
    foreach ($features_list as $f_id => $data) {
        $groups[$data['parent_id']]['features'][$f_id] = $data;
        $groups[$data['parent_id']]['position'] = empty($data['parent_id']) ? $data['position'] : $data['gposition'];
    }

    $features_list = array();
    if (!empty($groups)) {
        $groups = fn_sort_array_by_key($groups, 'position');
        foreach ($groups as $g) {
            $g['features'] = fn_sort_array_by_key($g['features'], 'position');
            $features_list = fn_array_merge($features_list, $g['features']);
        }
    }

    unset($groups);
    foreach ($features_list as $f_id => $data) {
        unset($features_list[$f_id]['position']);
        unset($features_list[$f_id]['gposition']);
    }

    if (is_array($product_id)) {
        foreach ($features_list as $feature) {
            $productId = $feature['product_id'];
            unset($feature['product_id']);
            $features_list[$productId][$feature['feature_id']] = $feature;
        }
    }

    return $features_list;
}

/**
 * Gets product feature variants
 *
 * @param array $params array with search parameters
 * @param int $items_per_page Items per page
 * @param string $lang_code 2-letters language code
 * @return array Product feature variants
 */
function fn_get_product_feature_variants($params, $items_per_page = 0, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    // Set default values to input params
    $default_params = array(
        'page' => 1,
        'product_id' => 0,
        'feature_id' => 0,
        'feature_type' => '',
        'get_images' => false,
        'items_per_page' => $items_per_page,
        'selected_only' => false,
        'all' => false,
        'sort' => true,
    );

    $params = array_merge($default_params, $params);

    if (is_array($params['feature_id'])) {
        $fields = array(
            '?:product_feature_variant_descriptions.variant',
            '?:product_feature_variants.variant_id',
            '?:product_feature_variants.feature_id',
        );
    } else {
        $fields = array(
            '?:product_feature_variant_descriptions.*',
            '?:product_feature_variants.*',
        );
    }

    $condition = $group_by = $sorting = '';
    $feature_id = is_array($params['feature_id']) ? $params['feature_id'] : array($params['feature_id']);

    $join = db_quote(" LEFT JOIN ?:product_feature_variant_descriptions ON ?:product_feature_variant_descriptions.variant_id = ?:product_feature_variants.variant_id AND ?:product_feature_variant_descriptions.lang_code = ?s", $lang_code);
    if ($params['all'] !== true) {
        $condition .= db_quote(" AND ?:product_feature_variants.feature_id IN (?a)", $feature_id);
    }

    if ($params['sort']) {
        $sorting = db_quote("ORDER BY ?:product_feature_variants.position, ?:product_feature_variant_descriptions.variant");
    }

    if (!empty($params['product_id'])) {
        $fields[] = '?:product_features_values.variant_id as selected';
        $fields[] = '?:product_features.feature_type';

        if (!empty($params['selected_only'])) {
            $join .= db_quote(" INNER JOIN ?:product_features_values ON ?:product_features_values.variant_id = ?:product_feature_variants.variant_id AND ?:product_features_values.lang_code = ?s AND ?:product_features_values.product_id = ?s", $lang_code, $params['product_id']);
        } else {
            $join .= db_quote("  LEFT JOIN ?:product_features_values ON ?:product_features_values.variant_id = ?:product_feature_variants.variant_id AND ?:product_features_values.lang_code = ?s AND ?:product_features_values.product_id = ?s", $lang_code, $params['product_id']);
        }

        $join .= db_quote(" LEFT JOIN ?:product_features ON ?:product_features.feature_id = ?:product_feature_variants.feature_id");
        $group_by = db_quote(" GROUP BY ?:product_feature_variants.variant_id");
    }

    $limit = '';
    if (!empty($params['items_per_page'])) {
        $params['total_items'] = db_get_field("SELECT COUNT(*) FROM ?:product_feature_variants WHERE 1 $condition");
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    $fields[] = '?:seo_names.name as seo_name';
    $join .= db_quote(
        " LEFT JOIN ?:seo_names ON ?:seo_names.object_id = CAST(?:product_feature_variants.variant_id AS CHAR) "
        . "AND ?:seo_names.type = 'e' AND ?:seo_names.dispatch = ''"
    );
    $vars = db_get_hash_array('SELECT ' . implode(', ', $fields) . " FROM ?:product_feature_variants $join WHERE 1 $condition $group_by $sorting $limit", 'variant_id');

    if ($params['get_images'] == true && in_array($params['feature_type'], ['E', 'S', 'N', 'M'])) {
        $variant_ids = array();
        foreach ($vars as $variant) {
            $variant_ids[] = $variant['variant_id'];
        }
        $image_pairs = fn_get_image_pairs($variant_ids, 'feature_variant', 'V', true, true, $lang_code);
        foreach ($vars as &$variant) {
            $variant['image_pair'] = array_pop($image_pairs[$variant['variant_id']]);
        }
        unset($variant);
    }

    if (!empty($vars)) {
        foreach ($vars as $k => $variant) {
            if (empty($variant['seo_name']) && !empty($variant['variant_id'])) {
                $vars[$k]['seo_name'] = fn_seo_get_name('e', $variant['variant_id'], '', null, $lang_code);
            }
        }
    }

    return array($vars, $params);
}

function fn_get_option_variants($optionId, $lang_code = null): array
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    $fields = "a.option_id, a.*";

    $condition = db_quote("a.option_id = ?i", $optionId);

    $opt = db_get_row(
        "SELECT " . $fields
        . " FROM ?:product_options as a"
        . " WHERE " . $condition
        . " ORDER BY a.position"
    );

    if (\is_array($opt) === true && \count($opt) > 0) {
        $_cond = ($opt['option_type'] == 'C') ? ' AND a.position = 1' : '';

        $join = '';

        $opt['variants'] = db_get_hash_array(
            "SELECT a.variant_id, a.position, a.modifier, a.modifier_type, a.weight_modifier, a.weight_modifier_type, a.status, b.variant_name FROM ?:product_option_variants as a LEFT JOIN ?:product_option_variants_descriptions as b ON a.variant_id = b.variant_id AND b.lang_code = ?s $join WHERE a.option_id = ?i $_cond ORDER BY a.position",
            'variant_id',
            $lang_code,
            $optionId
        );

        if (\is_array($opt['variants']) === true && \count($opt['variants']) > 0) {
            foreach ($opt['variants'] as $k => $v) {
                $opt['variants'][$k]['image_pair'] = fn_get_image_pairs(
                    $v['variant_id'],
                    'variant_image',
                    'V',
                    true,
                    true,
                    $lang_code
                );
            }
        }
    }

    return $opt['variants'];
}
/**
 * Gets product feature variant data
 *
 * @param int $variant_id Variant identifier
 * @param string $lang_code 2-letters language code
 * @return array Variant data
 */
function fn_get_product_feature_variant($variant_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $fields = "*";
    $join = db_quote("LEFT JOIN ?:product_feature_variant_descriptions ON ?:product_feature_variant_descriptions.variant_id = ?:product_feature_variants.variant_id AND ?:product_feature_variant_descriptions.lang_code = ?s", $lang_code);
    $condition = db_quote("?:product_feature_variants.variant_id = ?i", $variant_id);

    $var = db_get_row("SELECT $fields FROM ?:product_feature_variants $join WHERE $condition");

    if (empty($var)) {
        return false;
    }

    $var['image_pair'] = fn_get_image_pairs($variant_id, 'feature_variant', 'V', true, true, $lang_code);

    if (empty($var['meta_description']) && defined('AUTO_META_DESCRIPTION') && AREA != 'A') {
        $var['meta_description'] = fn_generate_meta_description($var['description']);
    }

    return $var;
}

/**
 * Filters feature group data, leaves only settings that should be upllied to feature
 *
 * @param array $group_data Group data
 * @return array Filtered group data
 */
function fn_filter_feature_group_data($group_data)
{
    $display_settings = [
        'display_on_product',
        'display_on_faceting',
        'is_searchable',
        'used_for_recommendations',
        'is_required',
        'editable_only_by_admin',
    ];
    foreach ($display_settings as $setting) {
        if ($group_data[$setting] != 'Y') {
            unset($group_data[$setting]);
        }
    }

    // Do not put group categories to children if the flag is enabled
    if (container()->getParameter('feature.attributes_in_groups_can_override_categories')) {
        unset($group_data['categories_path']);
    }

    return $group_data;
}

/**
 * Updates product feature
 *
 * @param array $feature_data Feature data
 * @param int $feature_id Feature identifier
 * @param string $lang_code 2-letters language code
 * @param bool $updateVariants For CSV import, we don't want this function handle variants
 * @return int/boolean Feature identifier if product feature was updated, false otherwise
 */
function fn_update_product_feature($feature_data, $feature_id, $lang_code = null, $updateVariants = true)
{
    // Lorsque les données propres à la feature (nom, type, proprieté d'affichage) ne sont pas modifiées, on va eviter
    // de re-projeter tout le catalogue lié à la feature
    $needEventTrigger = true;
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    fn_wizacha_backend_design_update_product_feature_pre($feature_data, $feature_id, $lang_code);

    if (null === $feature_data) {
        return false;
    }

    if (!empty($feature_data['status']) && !in_array($feature_data['status'], [\Wizacha\Status::ENABLED, \Wizacha\Status::DISABLED])) {
        return false;
    }

    if (in_array($feature_id, container()->getParameter('readonly_attributes'))) {
        $feature_data = array_intersect_key(
            $feature_data,
            array_flip([
                'description',
                'feature_code',
                'position',
                'full_description',
                'variants',
                'categories_path',
            ])
        );
    }

    $deleted_variants = array();

    // If this feature belongs to the group, get categories assignment from this group (depends on feature flag, see fn_filter_feature_group_data)
    if (!empty($feature_data['parent_id'])) {
        $gdata = db_get_row("
            SELECT categories_path, display_on_product, display_on_faceting,
                is_searchable, is_required, editable_only_by_admin, used_for_recommendations
            FROM ?:product_features
            WHERE feature_id = ?i",
            $feature_data['parent_id']
        );
        if (!empty($gdata)) {
            $gdata = fn_filter_feature_group_data($gdata);
            $feature_data = fn_array_merge($feature_data, $gdata);
        }
    }

    if (!empty($feature_data['feature_type']) && $feature_data['feature_type'] == AttributeType::GROUP) {
        // If attributes in groups can define their categories, we force the parent categories to be empty
        if (container()->getParameter('feature.attributes_in_groups_can_override_categories')) {
            $feature_data['categories_path'] = '';
        }
    }

    $feature_data['description'] = trim($feature_data['description']);

    // Sanitize product feature description
    if (true === \array_key_exists('full_description', $feature_data) && $feature_data['full_description'] !== null) {
        $purifierService = container()->get('purifier.default');
        $feature_data['full_description'] = \trim($purifierService->purify($feature_data['full_description']));
    }

    if (!intval($feature_id)) { // check for intval as we use "0G" for new group
        $needEventTrigger = false;
        $feature_data['feature_id'] = $feature_id = db_query("INSERT INTO ?:product_features ?e", $feature_data);
        \Tygh\Languages\Helper::insertTranslations('product_features_descriptions', $lang_code, $feature_data);
    } else {
        $updateFeature = true;

        // On cherche à determiner si on doit mettre à jour les données propres au product_feature ou si on doit
        // seulement mettre à jour les product_feature_variant. Si les données propres à l'attribut ne sont pas modifiés,
        // on ne reprojete pas tous les produits liés à l'attribut.
        $oldInfo = fn_get_product_feature_data($feature_id);
        $newData = $feature_data;
        unset($newData['variants']);
        unset($newData['original_var_ids']);
        foreach ($newData as $key=>$value) {
            if ($oldInfo[$key] == $value) {
                unset($newData[$key]);
            }
        }
        if (empty($newData)) {
            $needEventTrigger = false;
            $updateFeature = false;
        }


        if ($updateFeature) {
            $arow = db_query("UPDATE ?:product_features SET ?u WHERE feature_id = ?i", $feature_data, $feature_id);
            db_query('UPDATE ?:product_features_descriptions SET ?u WHERE feature_id = ?i AND lang_code = ?s', $feature_data, $feature_id, $lang_code);

            if ($arow === false) {
                fn_set_notification('E', __('error'), __('object_not_found', array('[object]' => __('feature'))), '', '404');
                $feature_id = false;
            }
        }
    }

    if ($feature_id) {
        // If this feature is group, set its categories to all children (depends on feature flag, see fn_filter_feature_group_data)
        if ($updateFeature && !empty($feature_data['feature_type']) && $feature_data['feature_type'] == AttributeType::GROUP) {
            $u = array(
                'categories_path' => !empty($feature_data['categories_path']) ? $feature_data['categories_path'] : '',
                'display_on_product' => !empty($feature_data['display_on_product']) ? $feature_data['display_on_product'] : '',
                'display_on_faceting' => !empty($feature_data['display_on_faceting']) ? $feature_data['display_on_faceting'] : '',
                'is_searchable' => !empty($feature_data['is_searchable']) ? $feature_data['is_searchable'] : '',
                'is_required' => !empty($feature_data['is_required']) ? $feature_data['is_required'] : '',
                'editable_only_by_admin' => !empty($feature_data['editable_only_by_admin']) ? $feature_data['editable_only_by_admin'] : '',
                'used_for_recommendations' => !empty($feature_data['used_for_recommendations']) ? $feature_data['used_for_recommendations'] : '',
            );
            $u = fn_filter_feature_group_data($u);
            db_query("UPDATE ?:product_features SET ?u WHERE parent_id = ?i", $u, $feature_id);
        }

        if ($updateVariants) {
            if (!empty($feature_data['feature_type']) && strpos('SMNE', $feature_data['feature_type']) === false) {
                if ($oldInfo['feature_type'] != $feature_data['feature_type']) {
                    // Delete variants for simple features
                    fn_delete_product_feature_variants($feature_id);
                }

            } else {
                fn_update_product_feature_variants($feature_id, $feature_data, $lang_code);
            }

            if (\in_array($feature_data['feature_type'], [
                    'E',
                    'M',
                    'S',
                    'N'
                ]) && !empty($feature_data['variants'])) {
                foreach ($feature_data['variants'] as $v) {
                    if (!empty($v['variant_id'])) {
                        fn_seo_update_object($v, $v['variant_id'], 'e', $lang_code);
                    }
                }

                if (!empty($deleted_variants)) {
                    db_query(
                        "DELETE FROM ?:seo_names WHERE object_id IN (?a) AND type = ?s AND dispatch = '' ",
                        $deleted_variants, 'e'
                    );
                }
            } elseif (!empty($feature_data['variants']) && is_array($feature_data['variants'])) {
                $object_ids = array();
                foreach ($feature_data['variants'] as $variant) {
                    if (!empty($variant['variant_id'])) {
                        $object_ids[] = $variant['variant_id'];
                    }
                }

                db_query(
                    "DELETE FROM ?:seo_names WHERE object_id IN (?a) AND type = ?s AND dispatch = '' ",
                    $object_ids, 'e'
                );
            }
        }

        if ($needEventTrigger) {
            container()->get('event_dispatcher')->dispatch(
                (new \Wizacha\Events\IterableEvent)->setElement($feature_id),
                AttributeService::EVENT_UPDATE
            );
        }
    }

    return $feature_id;
}

/**
 * Updates product feature variants
 *
 * @param int $feature_id Feature identifier
 * @param array $feature_data Feature data
 * @param string $lang_code 2-letters language code
 *
 * @return array $variant_ids Feature variants identifier
 */
function fn_update_product_feature_variants($feature_id, &$feature_data, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $variant_ids = array();

    if (!empty($feature_data['variants'])) {

        foreach ($feature_data['variants'] as $key => $variant) {
            $variant_id = fn_update_product_feature_variant($feature_id, $feature_data['feature_type'], $variant, $lang_code);

            $variant_ids[$key] = $variant_id;
            $feature_data['variants'][$key]['variant_id'] = $variant_id; // for addons
        }

        if (!empty($variant_ids)) {
            fn_attach_image_pairs('variant_image', 'feature_variant', 0, $lang_code, $variant_ids);
        }

        if (!empty($feature_data['original_var_ids'])) {
            $original_variant_ids = explode(',', $feature_data['original_var_ids']);
            $deleted_variants = array_diff($original_variant_ids, $variant_ids);

            fn_delete_product_feature_variants(0, $deleted_variants);
        }
    }

}

/**
 * Updates product feature variant
 *
 * @param int $feature_id Feature identifier
 * @param array $feature_type Feature type
 * @param array $variant Feature variant data
 * @param string $lang_code 2-letters language code
 *
 * @return string|false $variant_id Feature variant identifier (return an int as string..)
 */
function fn_update_product_feature_variant($feature_id, $feature_type, $variant, $lang_code = null)
{
    $isVariantUpdated = false;
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    if (empty($variant['variant']) && (!isset($variant['variant']) || $variant['variant'] !== '0')) {
        return false;
    }

    $variant['feature_id'] = $feature_id;

    // Sanitize product feature variant description
    if (true === \array_key_exists('description', $variant) && $variant['description'] !== null) {
        $purifierService = container()->get('purifier.default');
        $variant['description'] = \trim($purifierService->purify($variant['description']));
    }

    if (isset($variant['variant_id'])) {
        $variant_id = db_get_field('SELECT variant_id FROM ?:product_feature_variants WHERE variant_id = ?i', $variant['variant_id']);
        unset($variant['variant_id']);
    }

    if (empty($variant_id)) {
        $join = db_quote('INNER JOIN ?:product_feature_variants fv ON fv.variant_id = fvd.variant_id');
        $variant_id = db_get_field("SELECT fvd.variant_id FROM ?:product_feature_variant_descriptions AS fvd $join WHERE variant = ?s AND feature_id = ?i", $variant['variant'], $feature_id);
    }

    if (empty($variant_id)) {
        $variant_id = $variant['variant_id'] = db_query("INSERT INTO ?:product_feature_variants ?e", $variant);
        \Tygh\Languages\Helper::insertTranslations('product_feature_variant_descriptions', $lang_code, $variant);
    } else {
        $requireDataUpdate  = function ($oldData, $newData): bool {
            foreach($oldData as $key => $value) {
                if (array_key_exists($key, $newData) && $newData[$key] != $oldData[$key]) {
                    return true;
                }
            }
            return false;
        };
        $oldData = db_get_row("SELECT * FROM ?:product_feature_variants WHERE variant_id = ?i", $variant_id);

        if ($requireDataUpdate($oldData, $variant)) {
            $isVariantUpdated = true;
            db_query("UPDATE ?:product_feature_variants SET ?u WHERE variant_id = ?i", $variant, $variant_id);
        }
        $oldData = db_get_row("SELECT * FROM ?:product_feature_variant_descriptions WHERE variant_id = ?i AND lang_code = ?s", $variant_id, $lang_code);

       if ($requireDataUpdate($oldData, $variant)) {
           $isVariantUpdated = true;
            db_query("UPDATE ?:product_feature_variant_descriptions SET ?u WHERE variant_id = ?i AND lang_code = ?s", $variant, $variant_id, $lang_code);        }
    }

    if ($feature_type == 'N') {// number
        $isVariantUpdated = true;
        db_query('UPDATE ?:product_features_values SET ?u WHERE variant_id = ?i AND lang_code = ?s', array('value_int' => $variant['variant']), $variant_id, $lang_code);
    }

    if ($isVariantUpdated) {
        container()->get('event_dispatcher')->dispatch(
            (new \Wizacha\Events\IterableEvent)->setElement($variant_id),
            AttributeService::EVENT_UPDATE_VARIANT
        );
    }

    return $variant_id;
}

/**
 * Removes product feature
 *
 * @param int $feature_id Feature identifier
 * @return boolean Always true
 */
function fn_delete_feature($feature_id)
{
    fn_update_product_feature(['status' => \Wizacha\Status::DISABLED], $feature_id);

    if (in_array($feature_id, container()->getParameter('readonly_attributes'))) {
        return false;
    }

    $feature_deleted = true;

    $feature_type = db_get_field("SELECT feature_type FROM ?:product_features WHERE feature_id = ?i", $feature_id);

    $variant_ids = db_get_fields("SELECT variant_id FROM ?:product_feature_variants WHERE feature_id = ?i", $feature_id);
    if (!empty($variant_ids)) {
        db_query(
            "DELETE FROM ?:seo_names WHERE object_id IN (?a) AND type = ?s AND dispatch = ''",
            $variant_ids, 'e'
        );
    }

    if ($feature_type == 'G') {
        $fids = db_get_fields("SELECT feature_id FROM ?:product_features WHERE parent_id = ?i", $feature_id);
        if (!empty($fids)) {
            foreach ($fids as $fid) {
                fn_delete_feature($fid);
            }
        }
    }

    $affected_rows = db_query("DELETE FROM ?:product_features WHERE feature_id = ?i", $feature_id);
    db_query("DELETE FROM ?:product_features_descriptions WHERE feature_id = ?i", $feature_id);

    if ($affected_rows == 0) {
        fn_set_notification('E', __('error'), __('object_not_found', array('[object]' => __('feature'))), '', '404');
        $feature_deleted = false;
    }

    $variant_ids = fn_delete_product_feature_variants($feature_id);

    $filter_ids = db_get_fields("SELECT filter_id FROM ?:product_filters WHERE feature_id = ?i", $feature_id);
    foreach ($filter_ids as $_filter_id) {
        fn_delete_product_filter($_filter_id);
    }

    if ($feature_deleted) {
        container()->get('event_dispatcher')->dispatch(
            (new \Wizacha\Events\IterableEvent)->setElement($feature_id),
            AttributeService::EVENT_DELETE
        );
    }

    return $feature_deleted;
}

/**
 * Removes feature variants
 *
 * Le $feature_id est présent seulement lorsqu'on est dans le process de suppression de la feature. Par conséquent, on
 * n'a pas besoin de dispatch les events dans la mesure où ils seront déjà lancés dans la suppression de la feature
 *
 * @param int $feature_id Feature identifier
 * @param array $variant_ids Variants identifier
 * @return array $variant_ids Deleted feature variants
 */
function fn_delete_product_feature_variants($feature_id = 0, $variant_ids = array())
{
    $impactedProducts = [];
    if (\count($variant_ids) > 0) {
        $impactedProducts = container()->get('marketplace.pim.attribute_service')->getProductsFromVariantsId($variant_ids);
    }
    if (!empty($feature_id)) {
        $variant_ids = db_get_fields("SELECT variant_id FROM ?:product_feature_variants WHERE feature_id = ?i", $feature_id);
        db_query("DELETE FROM ?:product_features_values WHERE feature_id = ?i", $feature_id);
        db_query("DELETE FROM ?:product_feature_variants WHERE feature_id = ?i", $feature_id);

    } elseif (!empty($variant_ids)) {
        db_query("DELETE FROM ?:product_feature_variants WHERE variant_id IN (?n)", $variant_ids);
        db_query("DELETE FROM ?:product_features_values WHERE variant_id IN (?n)", $variant_ids);
    }

    if (!empty($variant_ids)) {
        db_query("DELETE FROM ?:product_feature_variant_descriptions WHERE variant_id IN (?n)", $variant_ids);
        foreach ($variant_ids as $variant_id) {
            fn_delete_image_pairs($variant_id, 'feature_variant');
        }
    }

    if (is_array($variant_ids) && !empty($variant_ids)) {
        db_query("DELETE FROM ?:seo_names WHERE type='e' AND object_id IN (?a)", $variant_ids);
    }

    $productProjector = container()->get('marketplace.product.projector');
    foreach ($impactedProducts as $productId) {
        if (MultiVendorProduct::isMultiVendorProductId($productId) === false) {
            $productProjector->projectProduct((int) $productId);
        } else {
            $productProjector->projectMultiVendorProduct($productId);
        }
    }

    return $variant_ids;
}

/**
 * Removes range from url (example - delete "R2" from "R2.V2.V11" - result "R2.V11")
 *
 * @param string $url url from wich will delete
 * @param array $range deleted element
 * @param string $field_type type of product field (A - amount, P - price, etc)
 * @return string
 */

function fn_delete_range_from_url($url, $range, $field_type = '')
{
    $prefix = empty($field_type) ? (in_array($range['feature_type'], array('N', 'O', 'D')) ? 'R' : 'V') : $field_type;
    $element = $prefix . $range['range_id'];
    $pattern = '/(' . $element . '[\.]?)|([\.]?' . $element . ')(?![\d]+)/';
    return preg_replace($pattern, '', $url);
}

/**
 * Adds range to hash (example - add "V2" to "R23.V11.R5" - result "R23.V11.R5.V2")
 *
 * @param string $hash hash to which will be added
 * @param array $range added element
 * @param string $field_type element prefix ("R" or "V")
 * @return string new hash
 */
function fn_add_range_to_url_hash($hash, $range, $field_type = '')
{
    if (!is_array($range)) {
        $_range['range_id'] = $range;
    } else {
        $_range = $range;
    }

    if ($field_type == 'P') {
        //remove previous price diapason
        $pattern = '/(P\d+-\d+-\w+\.?)|(\.?P\d+-\d+-\w+)/';
        $hash = preg_replace($pattern, '', $hash);
    }

    if ($field_type == 'A') {
        //remove previous amount diapason
        $pattern = '/(A\d+-\d+\.?)|(\.?A\d+-\d+)/';
        $hash = preg_replace($pattern, '', $hash);
    }

    $prefix = empty($field_type) ? (in_array($_range['feature_type'], array('N', 'O', 'D')) ? 'R' : 'V') : $field_type;
    $result = '';
    if (empty($hash)) {
        $result = $prefix . $_range['range_id'];
    } elseif (strrpos($hash, $prefix . $_range['range_id']) === false) {
        $result = $hash . '.' . $prefix . $_range['range_id'];
    } else {
        $result = $hash;
    }
    return $result;
}

function fn_delete_product_filter($filter_id)
{
    $range_ids = db_get_fields("SELECT range_id FROM ?:product_filter_ranges WHERE filter_id = ?i", $filter_id);

    db_query("DELETE FROM ?:product_filters WHERE filter_id = ?i", $filter_id);
    db_query("DELETE FROM ?:product_filter_descriptions WHERE filter_id = ?i", $filter_id);

    foreach ($range_ids as $range_id) {
        db_query("DELETE FROM ?:product_filter_ranges_descriptions WHERE range_id = ?i", $range_id);
    }

    db_query("DELETE FROM ?:product_filter_ranges WHERE filter_id = ?i", $filter_id);
    return true;
}

function fn_parse_features_hash($features_hash = '', $values = true)
{
    $result = array();

    if (!empty($features_hash)) {
        $variants_ids = $ranges_ids = $fields_ids = $slider_vals = $fields_ids_revert = array();
        preg_match_all('/([A-Z]+)([\d]+-?\d*-?\w*)[,]?/', $features_hash, $vals);

        if ($values !== true) {
            return $vals;
        }

        $fields = fn_get_product_filter_fields();

        if (!empty($vals) && !empty($vals[1]) && !empty($vals[2])) {
            foreach ($vals[1] as $key => $range_type) {
                if ($range_type == 'V') {
                    // Feature variants
                    $variants_ids[] = $vals[2][$key];
                } elseif ($range_type == 'R') {
                    // Feature ranges
                    $ranges_ids[] = $vals[2][$key];
                } elseif (!empty($fields[$range_type]['slider'])) {
                    $slider_vals[$vals[1][$key]] = explode('-', $vals[2][$key]);
                } else {
                    // Product field ranges
                    $fields_ids[$vals[2][$key]] = $vals[1][$key];
                    $fields_ids_revert[$vals[1][$key]][] = $vals[2][$key];
                }
            }
        }

        $variants_ids = array_map('intval', $variants_ids);
        $ranges_ids = array_map('intval', $ranges_ids);

        $result = array($variants_ids, $ranges_ids, $fields_ids, $slider_vals, $fields_ids_revert);
    }

    return $result;
}

/**
 * Generates fields for the product filters
 * Returns array with following structure:
 *
 * code => array (
 *        'db_field' => db_field,
 *        'table' => db_table,
 *        'name' => lang_var_name,
 *        'condition_type' => condition_type
 * );
 *
 * condition_type - contains "C" - char (example, free_shipping == "Y")
 *                             "D" - dinamic (1.23 < price < 3.45)
 *                             "F" - fixed (supplier_id = 3)
 *
 * slider - boolean, if true then the slider will be displaed for choosing the range of values
 * is_range - boolean, show or not ranges
 *
 */
function fn_get_product_filter_fields()
{
    $filters = array(
        // price filter
        'P' => array(
            'db_field' => 'price',
            'table' => 'product_prices',
            'description' => 'price',
            'condition_type' => 'D',
            'slider' => true,
        ),
        // amount filter
        'A' => array(
            'db_field' => 'amount',
            'table' => 'products',
            'description' => 'in_stock',
            'condition_type' => 'D',
            'slider' => true,
            'hidden' => true,
        ),
        // filter by free shipping
        'F' => array(
            'db_field' => 'free_shipping',
            'table' => 'products',
            'description' => 'free_shipping',
            'condition_type' => 'C',
            'variant_descriptions' => array(
                'Y' => __('yes'),
                'N' => __('no')
            )
        )
    );

    $filters['S'] = array (
        'db_field' => 'company_id',
        'table' => 'products',
        'description' => 'vendor',
        'condition_type' => 'F',
        'range_name' => 'company',
        'foreign_table' => 'companies',
        'foreign_index' => 'company_id'
    );
    return $filters;
}

/**
 * Gets all combinations of options stored in exceptions
 * @deprecated
 * @see \Wizacha\Product::getExceptions
 */
function fn_get_product_exceptions($product_id, $short_list = false)
{
    $exceptions = db_get_array("SELECT * FROM ?:product_options_exceptions WHERE product_id = ?i", $product_id);

    foreach ($exceptions as $k => $v) {
        $exceptions[$k]['combination'] = [$v['option_id'] => $v['variant_id']];

        if ($short_list) {
            $exceptions[$k] = $exceptions[$k]['combination'];
        }
    }

    fn_wizacha_declinations_get_product_exceptions_post($product_id, $exceptions, $short_list);

    return $exceptions;
}


//
// Returnns true if such combination already exists
//
function fn_check_combination($combinations, $product_id)
{
    $exceptions = fn_get_product_exceptions($product_id);

    $result = false;

    if (!empty($exceptions)) {
        foreach ($exceptions as $k => $v) {
            $temp = array();
            $temp = $v['combination'];
            foreach ($combinations as $key => $value) {
                if ((in_array($value, $temp)) && ($temp[$key] == $value)) {
                    unset($temp[$key]);
                }
            }
            if (empty($temp)) {
                $result = true;
                break;
            }
        }
    }

    return $result;
}

//
// Updates options exceptions using product_id;
//
function fn_update_exceptions($product_id)
{
    $result = false;

    if ($product_id) {

        $exceptions = fn_get_product_exceptions($product_id);

        if (!empty($exceptions)) {
            db_query("DELETE FROM ?:product_options_exceptions WHERE product_id = ?i", $product_id);
            foreach ($exceptions as $k => $v) {
                $_options_order = db_get_fields("SELECT a.option_id FROM ?:product_options as a LEFT JOIN ?:product_global_option_links as b ON a.option_id = b.option_id WHERE b.product_id = ?i ORDER BY position", $product_id);

                if (empty($_options_order)) {
                    return false;
                }
                $combination = array();

                foreach ($_options_order as $option) {
                    if (!empty($v['combination'][$option])) {
                        $combination[$option] = $v['combination'][$option];
                    } else {
                        $combination[$option] = -1;
                    }
                }

                $_data = array(
                    'product_id' => $product_id,
                    'option_id' => \key($combination),
                    'variant_id' => \current($combination),
                );

                db_query("INSERT INTO ?:product_options_exceptions ?e", $_data);
            }

            $result = true;
        }
    }

    return $result;
}

/**
 * Constructs a string in format option1_variant1_option2_variant2...
 * @see fn_get_product_options_by_combination if you want to do the reverse operation
 *
 * @param array $product_options
 * @return string
 */
function fn_get_options_combination($product_options)
{
    // BugFix: Ensure that order is consistent
    if (is_array($product_options)) {
        ksort($product_options);
    }

    if (empty($product_options) && !is_array($product_options)) {
        return '';
    }

    $combination = '';
    foreach ($product_options as $option => $variant) {
        $combination .= $option . '_' . $variant . '_';
    }
    $combination = trim($combination, '_');

    return $combination;
}

function fn_get_products($params, $items_per_page = 0, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    if (!empty($params['bestsellers'])) {
        $params['extend'][] = 'categories';
    } elseif (empty($params['sort_by']) || empty($sortings[$params['sort_by']])) {
        $default_sorting_params = fn_get_default_products_sorting();
        if ((!empty($params['sort_by']) && $params['sort_by'] == 'bestsellers') || ($default_sorting_params['sort_by'] == 'bestsellers') || isset($params['sales_amount_from']) || isset($params['sales_amount_to'])) {
            $params['extend'][] = 'categories';
            $params['extend'][] = 'sales';
        }
    }
    // Init filter
    $params = LastView::instance()->update('products', $params);

    // Set default values to input params
    $default_params = array(
        'area' => AREA,
        'extend' => (AREA == 'C') ? array('product_name', 'prices', 'categories') : array('product_name', 'prices'),
        'custom_extend' => array(),
        'pname' => '',
        'pshort' => '',
        'pfull' => '',
        'pkeywords' => '',
        'feature' => array(),
        'type' => 'simple',
        'page' => 1,
        'action' => '',
        'variants' => array(),
        'ranges' => array(),
        'custom_range' => array(),
        'field_range' => array(),
        'features_hash' => '',
        'limit' => 0,
        'bid' => 0,
        'match' => '',
        'tracking' => array(),
        'sort_by' => 'null',
        'items_per_page' => $items_per_page
    );
    if (empty($params['custom_extend'])) {
        $params['extend'] = !empty($params['extend']) ? array_merge($default_params['extend'], $params['extend']) : $default_params['extend'];
    } else {
        $params['extend'] = $params['custom_extend'];
    }

    $params = array_merge($default_params, $params);
    //block the max items per page to 500
    $params['items_per_page'] = $params['items_per_page'] > 500 ? 500 : $params['items_per_page'];

    //by default, if no option checked and "generic field" q not empty, the search will be on product name
    if ((empty($params['pname']) || $params['pname'] != 'Y') && (empty($params['pshort']) || $params['pshort'] != 'Y') && (empty($params['pfull']) || $params['pfull'] != 'Y') && (empty($params['pkeywords']) || $params['pkeywords'] != 'Y') && (empty($params['feature']) || $params['feature'] != 'Y') && !empty($params['q'])) {
        $params['pname'] = 'Y';
    }

    $auth = & $_SESSION['auth'];

    // Define fields that should be retrieved
    if (empty($params['only_short_fields'])) {
        $fields = array(
            'products.*',
        );
    } else {
        $fields = array(
            'products.product_id',
            'products.product_code',
            'products.product_type',
            'products.status',
            'products.company_id',
            'products.list_price',
            'products.amount',
            'products.weight',
            'products.tracking',
            'products.is_edp',
            'products.approved',
            'products.w_supplier_ref',
            'products.product_template_type',
        );
    }

    // Define sort fields
    $sortings = array(
        'code' => 'products.product_code',
        'status' => 'products.status',
        'product' => 'descr1.product',
        'position' => 'products_categories_filter.position',
        'price' => 'price',
        'list_price' => 'products.list_price',
        'weight' => 'products.weight',
        'amount' => 'products.amount',
        'timestamp' => 'products.timestamp',
        'updated_timestamp' => 'products.updated_timestamp',
        'company' => 'company_name',
        'null' => 'NULL'
    );

    if (!empty($params['order_ids'])) {
        $sortings['p_qty'] = 'purchased_qty';
        $sortings['p_subtotal'] = 'purchased_subtotal';
        $fields[] = "SUM(?:order_details.amount) as purchased_qty";
        $fields[] = "SUM(?:order_details.price) as purchased_subtotal";
    }

    if (isset($params['compact']) && $params['compact'] == 'Y') {
        $union_condition = ' OR ';
    } else {
        $union_condition = ' AND ';
    }

    $join = $condition = $u_condition = $inventory_condition = $having = '';

    // Search string condition for SQL query
    if (isset($params['q']) && fn_string_not_empty($params['q'])) {

        $params['q'] = trim($params['q']);
        if ($params['match'] == 'any') {
            $pieces = fn_explode(' ', $params['q']);
            $search_type = ' OR ';
        } elseif ($params['match'] == 'all') {
            $pieces = fn_explode(' ', $params['q']);
            $search_type = ' AND ';
        } else {
            $pieces = array($params['q']);
            $search_type = '';
        }

        $_condition = array();
        foreach ($pieces as $piece) {
            if (strlen($piece) == 0) {
                continue;
            }

            $tmp = db_quote("(descr1.search_words LIKE ?l)", '%' . $piece . '%'); // check search words

            if ($params['pname'] == 'Y') {
                $tmp .= db_quote(" OR descr1.product LIKE ?l", '%' . $piece . '%');
            }
            if ($params['pshort'] == 'Y') {
                $tmp .= db_quote(" OR descr1.short_description LIKE ?l", '%' . $piece . '%');
                $tmp .= db_quote(" OR descr1.short_description LIKE ?l", '%' . htmlentities($piece, ENT_QUOTES, 'UTF-8') . '%');
            }
            if ($params['pfull'] == 'Y') {
                $tmp .= db_quote(" OR descr1.full_description LIKE ?l", '%' . $piece . '%');
                $tmp .= db_quote(" OR descr1.full_description LIKE ?l", '%' . htmlentities($piece, ENT_QUOTES, 'UTF-8') . '%');
            }
            if ($params['pkeywords'] == 'Y') {
                $tmp .= db_quote(" OR (descr1.meta_keywords LIKE ?l OR descr1.meta_description LIKE ?l)", '%' . $piece . '%', '%' . $piece . '%');
            }
            if (!empty($params['feature']) && $params['action'] != 'feature_search') {
                $tmp .= db_quote(" OR ?:product_features_values.value LIKE ?l", '%' . $piece . '%');
            }

            $_condition[] = '(' . $tmp . ')';
        }

        $_cond = implode($search_type, $_condition);

        if (!empty($_condition)) {
            $condition .= ' AND (' . $_cond . ') ';
        }

        if (!empty($params['feature']) && $params['action'] != 'feature_search') {
            $join .= " LEFT JOIN ?:product_features_values ON ?:product_features_values.product_id = CAST(products.product_id AS CHAR)";
            $condition .= db_quote(" AND (?:product_features_values.feature_id IN (?n) OR ?:product_features_values.feature_id IS NULL)", array_values($params['feature']));
        }

        //if perform search we also get additional fields
        if ($params['pname'] == 'Y') {
            $params['extend'][] = 'product_name';
        }

        if ($params['pshort'] == 'Y' || $params['pfull'] == 'Y' || $params['pkeywords'] == 'Y') {
            $params['extend'][] = 'description';
        }

        unset($_condition);
    }

    //
    // [Advanced and feature filters]
    //

    if (!empty($params['apply_limit']) && $params['apply_limit'] && !empty($params['pid'])) {
        $pids = array();

        foreach ($params['pid'] as $pid) {
            if ($pid != $params['exclude_pid']) {
                if (count($pids) == $params['limit']) {
                    break;
                } else {
                    $pids[] = $pid;
                }
            }
        }
        $params['pid'] = $pids;
    }

    if (!empty($params['features_hash']) || (!fn_is_empty($params['variants'])) || !empty($params['feature_code'])) {
        $join .= db_quote(" LEFT JOIN ?:product_features_values ON ?:product_features_values.product_id = CAST(products.product_id AS CHAR) AND ?:product_features_values.lang_code = ?s", $lang_code);
    }

    if (!empty($params['variants'])) {
        $params['features_hash'] .= implode('.', $params['variants']);
    }

    // Feature code
    if (!empty($params['feature_code'])) {
        $join .= db_quote(" LEFT JOIN ?:product_features ON ?:product_features_values.feature_id = ?:product_features.feature_id");
        $condition .= db_quote(" AND ?:product_features.feature_code = ?s", $params['feature_code']);
    }

    $advanced_variant_ids = $simple_variant_ids = $ranges_ids = $fields_ids = $fields_ids_revert = $slider_vals = array();

    if (!empty($params['features_hash'])) {
        list($av_ids, $ranges_ids, $fields_ids, $slider_vals, $fields_ids_revert) = fn_parse_features_hash($params['features_hash']);
        $advanced_variant_ids = db_get_hash_multi_array("SELECT feature_id, variant_id FROM ?:product_feature_variants WHERE variant_id IN (?n)", array('feature_id', 'variant_id'), $av_ids);
    }

    if (!empty($params['multiple_variants'])) {
        $simple_variant_ids = $params['multiple_variants'];
    }

    if (!empty($advanced_variant_ids)) {
        $variant_ids = array_reduce($advanced_variant_ids, function (&$result, $variant_ids) {return array_merge($result, array_keys($variant_ids));}, []);
        $join .= db_quote(
            " INNER JOIN
                (SELECT DISTINCT product_id
                FROM ?:product_features_values
                WHERE lang_code = ?s AND ?:product_features_values.variant_id IN (?a)
                ) AS pfv_advanced ON pfv_advanced.product_id = CAST(products.product_id AS CHAR) ",
            $lang_code,
            $variant_ids
        );
    }

    if (!empty($simple_variant_ids)) {
        $join .= db_quote(" LEFT JOIN (SELECT product_id, GROUP_CONCAT(?:product_features_values.variant_id) AS simple_variants FROM ?:product_features_values WHERE lang_code = ?s GROUP BY product_id) AS pfv_simple ON pfv_simple.product_id = CAST(products.product_id AS CHAR)", $lang_code);

        $where_conditions = array();
        foreach ($simple_variant_ids as $k => $variant_id) {
            $where_conditions[] = db_quote(" FIND_IN_SET('?i', simple_variants)", $variant_id);
        }
        $condition .= ' AND ' . implode(' AND ', $where_conditions);
    }

    //
    // Ranges from text inputs
    //

    // Feature ranges
    if (!empty($params['custom_range'])) {
        foreach ($params['custom_range'] as $k => $v) {
            $k = intval($k);
            if (isset($v['from']) && fn_string_not_empty($v['from']) || isset($v['to']) && fn_string_not_empty($v['to'])) {
                if (!empty($v['type'])) {
                    if ($v['type'] == 'D') {
                        $v['from'] = fn_parse_date($v['from']);
                        $v['to'] = fn_parse_date($v['to']);
                    }
                }
                $join .= db_quote(" LEFT JOIN ?:product_features_values as custom_range_$k ON custom_range_$k.product_id = CAST(products.product_id AS CHAR) AND custom_range_$k.lang_code = ?s", $lang_code);
                if (fn_string_not_empty($v['from']) && fn_string_not_empty($v['to'])) {
                    $condition .= db_quote(" AND (custom_range_$k.value_int >= ?i AND custom_range_$k.value_int <= ?i AND custom_range_$k.value = '' AND custom_range_$k.feature_id = ?i) ", $v['from'], $v['to'], $k);
                } else {
                    $condition .= " AND custom_range_$k.value_int" . (fn_string_not_empty($v['from']) ? db_quote(' >= ?i', $v['from']) : db_quote(" <= ?i AND custom_range_$k.value = '' AND custom_range_$k.feature_id = ?i ", $v['to'], $k));
                }
            }
        }
    }
    // Product field ranges
    $filter_fields = fn_get_product_filter_fields();
    if (!empty($params['field_range'])) {
        foreach ($params['field_range'] as $field_type => $v) {
            $structure = $filter_fields[$field_type];
            if (!empty($structure) && (!empty($v['from']) || !empty($v['to']))) {
                if ($field_type == 'P') { // price
                    $v['cur'] = !empty($v['cur']) ? $v['cur'] : CART_SECONDARY_CURRENCY;
                    if (empty($v['orig_cur'])) {
                        // saving the first user-entered values
                        // will be always search by it
                        $v['orig_from'] = $v['from'];
                        $v['orig_to'] = $v['to'];
                        $v['orig_cur'] = $v['cur'];
                        $params['field_range'][$field_type] = $v;
                    }
                    if ($v['orig_cur'] != CART_PRIMARY_CURRENCY) {
                        // calc price in primary currency
                        $cur_prim_coef = Registry::get('currencies.' . $v['orig_cur'] . '.coefficient');
                        $decimals = Registry::get('currencies.' . CART_PRIMARY_CURRENCY . '.decimals');
                        $search_from = round($v['orig_from'] * floatval($cur_prim_coef), $decimals);
                        $search_to = round($v['orig_to'] * floatval($cur_prim_coef), $decimals);
                    } else {
                        $search_from = $v['orig_from'];
                        $search_to = $v['orig_to'];
                    }
                    // if user switch the currency, calc new values for displaying in filter
                    if ($v['cur'] != CART_SECONDARY_CURRENCY) {
                        if (CART_SECONDARY_CURRENCY == $v['orig_cur']) {
                            $v['from'] = $v['orig_from'];
                            $v['to'] = $v['orig_to'];
                        } else {
                            $prev_coef = Registry::get('currencies.' . $v['cur'] . '.coefficient');
                            $cur_coef = Registry::get('currencies.' . CART_SECONDARY_CURRENCY . '.coefficient');
                            $v['from'] = floor(floatval($v['from']) * floatval($prev_coef) / floatval($cur_coef));
                            $v['to'] = ceil(floatval($v['to']) * floatval($prev_coef) / floatval($cur_coef));
                        }
                        $v['cur'] = CART_SECONDARY_CURRENCY;
                        $params['field_range'][$field_type] = $v;
                    }
                }

                $params["$structure[db_field]_from"] = trim(isset($search_from) ? $search_from : $v['from']);
                $params["$structure[db_field]_to"] = trim(isset($search_to) ? $search_to : $v['to']);
            }
        }
    }
    // Ranges from database
    if (!empty($ranges_ids)) {
        $filter_conditions = db_get_hash_multi_array("SELECT `from`, `to`, feature_id, filter_id, range_id FROM ?:product_filter_ranges WHERE range_id IN (?n)", array('filter_id', 'range_id'), $ranges_ids);
        $where_conditions = array();
        foreach ($filter_conditions as $fid => $range_conditions) {
            foreach ($range_conditions as $k => $range_condition) {
                $k = $fid . "_" . $k;
                $join .= db_quote(" LEFT JOIN ?:product_features_values as var_val_$k ON var_val_$k.product_id = CAST(products.product_id AS CHAR) AND var_val_$k.lang_code = ?s", $lang_code);
                $where_conditions[] = db_quote("(var_val_$k.value_int >= ?i AND var_val_$k.value_int <= ?i AND var_val_$k.value = '' AND var_val_$k.feature_id = ?i)", $range_condition['from'], $range_condition['to'], $range_condition['feature_id']);
            }
            $condition .= db_quote(" AND (?p)", implode(" OR ", $where_conditions));
            $where_conditions = array();
        }
    }

    // Field ranges
    //$fields_ids = empty($params['fields_ids']) ? $fields_ids : $params['fields_ids'];
    if (!empty($params['fields_ids'])) {

        foreach ($fields_ids as $rid => $field_type) {
            if (!empty($filter_fields[$field_type])) {
                $structure = $filter_fields[$field_type];
                if ($structure['condition_type'] == 'D' && empty($structure['slider'])) {
                    $range_condition = db_get_row("SELECT `from`, `to`, range_id FROM ?:product_filter_ranges WHERE range_id = ?i", $rid);
                    if (!empty($range_condition)) {
                        $params["$structure[db_field]_from"] = $range_condition['from'];
                        $params["$structure[db_field]_to"] = $range_condition['to'];
                    }
                } elseif ($structure['condition_type'] == 'F') {
                    $params['filter_params'][$structure['db_field']][] = $rid;
                } elseif ($structure['condition_type'] == 'C') {
                    $params['filter_params'][$structure['db_field']][] = ($rid == 1) ? 'Y' : 'N';
                }
            }
        }
    } elseif (!empty($fields_ids_revert)) {
        foreach ($fields_ids_revert as $field_type => $rids) {
            if (!empty($filter_fields[$field_type])) {
                $structure = $filter_fields[$field_type];
                if ($structure['condition_type'] == 'D' && empty($structure['slider'])) {
                    foreach ($rids as $rid) {
                        $range_condition = db_get_row("SELECT `from`, `to`, range_id FROM ?:product_filter_ranges WHERE range_id = ?i", $rid);
                        if (!empty($range_condition)) {
                            $params["$structure[db_field]_from"] = $range_condition['from'];
                            $params["$structure[db_field]_to"] = $range_condition['to'];
                        }
                    }
                } elseif ($structure['condition_type'] == 'F') {
                    $params['filter_params'][$structure['db_field']] = $rids;
                } elseif ($structure['condition_type'] == 'C') {
                    if (count($rids) > 1) {
                        foreach ($rids as $rid) {
                            if ($fields_ids[$rid] == $field_type) {
                                unset($fields_ids[$rid]);
                            }
                            $params['features_hash'] = fn_delete_range_from_url($params['features_hash'], array('range_id' => $rid), $field_type);
                        }
                    } else {
                        $params['filter_params'][$structure['db_field']][] = ($rids[0] == 1) ? 'Y' : 'N';
                    }
                }
            }
        }
    }

    // Slider ranges
    $slider_vals = empty($params['slider_vals']) ? $slider_vals : $params['slider_vals'];
    if (!empty($slider_vals)) {
        foreach ($slider_vals as $field_type => $vals) {
            if (!empty($filter_fields[$field_type])) {
                if ($field_type == 'P') {
                    $currency = !empty($vals[2]) ? $vals[2] : CART_PRIMARY_CURRENCY;
                    if ($currency != CART_PRIMARY_CURRENCY) {
                        $coef = Registry::get('currencies.' . $currency . '.coefficient');
                        $decimals = Registry::get('currencies.' . CART_PRIMARY_CURRENCY . '.decimals');
                        $vals[0] = round(floatval($vals[0]) * floatval($coef), $decimals);
                        $vals[1] = round(floatval($vals[1]) * floatval($coef), $decimals);
                    }
                }

                $structure = $filter_fields[$field_type];
                $params["$structure[db_field]_from"] = $vals[0];
                $params["$structure[db_field]_to"] = $vals[1];
            }
        }
    }

    // Checkbox features
    if (!empty($params['ch_filters']) && !fn_is_empty($params['ch_filters'])) {
        foreach ($params['ch_filters'] as $k => $v) {
            // Product field filter
            if (is_string($k) == true && !empty($v) && $structure = $filter_fields[$k]) {
                $condition .= db_quote(" AND $structure[table].$structure[db_field] IN (?a)", ($v == 'A' ? array('Y', 'N') : $v));
                // Feature filter
            } elseif (!empty($v)) {
                $fid = intval($k);
                $join .= db_quote(" LEFT JOIN ?:product_features_values as ch_features_$fid ON ch_features_$fid.product_id = CAST(products.product_id AS CHAR) AND ch_features_$fid.lang_code = ?s", $lang_code);
                $condition .= db_quote(" AND ch_features_$fid.feature_id = ?i AND ch_features_$fid.value IN (?a)", $fid, ($v == 'A' ? array('Y', 'N') : $v));
            }
        }
    }

    // Text features
    if (!empty($params['tx_features'])) {
        foreach ($params['tx_features'] as $k => $v) {
            if (fn_string_not_empty($v)) {
                $fid = intval($k);
                $join .= " LEFT JOIN ?:product_features_values as tx_features_$fid ON tx_features_$fid.product_id = CAST(products.product_id AS CHAR)";
                $condition .= db_quote(" AND tx_features_$fid.value LIKE ?l AND tx_features_$fid.lang_code = ?s", "%" . trim($v) . "%", $lang_code);
            }
        }
    }

    $total = 0;

    // [/Advanced filters]
    $feature_search_condition = '';
    if (!empty($params['feature'])) {
        // Extended search by product fields
        $_cond = array();
        $total_hits = 0;
        foreach ($params['feature'] as $f_id) {
            if (!empty($f_val)) {
                $total_hits++;
                $_cond[] = db_quote("(?:product_features_values.feature_id = ?i)", $f_id);
            }
        }

        $params['extend'][] = 'categories';
        if (!empty($_cond)) {
            $cache_feature_search = db_get_fields("SELECT product_id, COUNT(product_id) as cnt FROM ?:product_features_values WHERE (" . implode(' OR ', $_cond) . ") GROUP BY product_id HAVING cnt = $total_hits");
            $feature_search_condition .= db_quote(" AND CAST(products_categories.product_id AS CHAR) IN (?n)", $cache_feature_search);
        }
    }

    // Category search condition for SQL query
    $category_filter_where_condition = '';
    if (!empty($params['cid'])) {
        $cids = is_array($params['cid']) ? $params['cid'] : array($params['cid']);

        if (!empty($params['subcats']) && $params['subcats'] == 'Y') {
            $_ids = db_get_fields("SELECT a.category_id FROM ?:categories as a LEFT JOIN ?:categories as b ON b.category_id IN (?n) WHERE a.id_path LIKE CONCAT(b.id_path, '/%')", $cids);

            $cids = fn_array_merge($cids, $_ids, false);
        }

        $params['extend'][] = 'categories_filter';
        $category_filter_where_condition = db_quote(" AND categories_filter.category_id IN (?n)", $cids);
        $condition .= $category_filter_where_condition;
    }

    // If we need to get the products by IDs and no IDs passed, don't search anything
    if (!empty($params['force_get_by_ids']) && empty($params['pid']) && empty($params['product_id'])) {
        return array(array(), $params, 0);
    }

    // Product ID search condition for SQL query
    if (!empty($params['pid'])) {
        $u_condition .= db_quote($union_condition . ' products.product_id IN (?n)', $params['pid']);
    }

    // Exclude products from search results
    if (!empty($params['exclude_pid'])) {
        $condition .= db_quote(' AND products.product_id NOT IN (?n)', $params['exclude_pid']);
    }

    // Only select the selected approved status
    if (
        is_string($params['approved'])
        && (
            $params['approved'] === 'Y'
            || $params['approved'] === 'N'
            || $params['approved'] === 'P'
        )
    ) {
        $condition .= db_quote(' AND products.approved = ?s', strtoupper($params['approved']));
    }
    if (is_string($params['updatedAfter'])) {

        $date = MultiVendorProductService::getDateFromParameters($params['updatedAfter'], 'updatedAfter');

        $condition .= db_quote(' AND products.updated_timestamp >= (?n)', $date->getTimestamp());
    }
    if (is_string($params['updatedBefore'])) {
        $date = MultiVendorProductService::getDateFromParameters($params['updatedBefore'], 'updatedBefore');

        $condition .= db_quote(' AND products.updated_timestamp <= (?n)', $date->getTimestamp());
    }

    // Search products by localization
    $condition .= fn_get_localizations_condition('products.localization', true);

    $company_condition = '';

    if ($params['area'] == 'C') {
        if (!empty($params['c2c_vendors_products']) || !empty($params['b2c_vendors_products'])) {
            $company_condition .= fn_get_company_condition(
                'products.company_id',
                true,
                \Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance()),
                false,
                true
            );
        } else {
            $company_condition .= " AND companies.status = 'A' ";
            $params['extend'][] = 'companies';
        }
    } else {
        $company_condition .= fn_get_company_condition('products.company_id');
    }

    if ($params['companies_status']) {
        $company_condition .= \Tygh\Database::quote(" AND companies.status IN (?a)", $params['companies_status']);
        if (!in_array('companies', $params['extend'])) {
            $params['extend'][] = 'companies';
        }
    }
    $condition .= $company_condition;

    if (Registry::get('runtime.company_id') && isset($params['company_id'])) {
        $params['company_id'] = Registry::get('runtime.company_id');
    }
    if (isset($params['company_id']) && $params['company_id'] != '') {
        $condition .= db_quote(' AND products.company_id = ?i ', $params['company_id']);
    }

    if (!empty($params['company_ids'])) {
        $condition .= db_quote(' AND products.company_id IN (?n)', $params['company_ids']);
    }

    if (!empty($params['filter_params'])) {
        foreach ($params['filter_params'] as $field => $f_vals) {
            $condition .= db_quote(' AND products.' . $field . ' IN (?a) ', $f_vals);
        }
    }

    if (isset($params['price_from']) && fn_is_numeric($params['price_from'])) {
        $condition .= db_quote(' AND prices.price >= ?d', fn_convert_price(trim($params['price_from'])));
        $params['extend'][] = 'prices2';
    }

    if (isset($params['price_to']) && fn_is_numeric($params['price_to'])) {
        $condition .= db_quote(' AND prices.price <= ?d', fn_convert_price(trim($params['price_to'])));
        $params['extend'][] = 'prices2';
    }

    if (isset($params['weight_from']) && fn_is_numeric($params['weight_from'])) {
        $condition .= db_quote(' AND products.weight >= ?d', fn_convert_weight(trim($params['weight_from'])));
    }

    if (isset($params['weight_to']) && fn_is_numeric($params['weight_to'])) {
        $condition .= db_quote(' AND products.weight <= ?d', fn_convert_weight(trim($params['weight_to'])));
    }

    // search specific inventory status
    if (!empty($params['tracking'])) {
        $condition .= db_quote(' AND products.tracking IN(?a)', $params['tracking']);
    }

    if (isset($params['amount_from']) && fn_is_numeric($params['amount_from'])) {
        $condition .= db_quote(" AND IF(products.tracking = 'O', inventory.amount >= ?i, products.amount >= ?i)", $params['amount_from'], $params['amount_from']);
        $inventory_condition .= db_quote(' AND inventory.amount >= ?i', $params['amount_from']);
    }

    if (isset($params['amount_to']) && fn_is_numeric($params['amount_to'])) {
        $condition .= db_quote(" AND IF(products.tracking = 'O', inventory.amount <= ?i, products.amount <= ?i)", $params['amount_to'], $params['amount_to']);
        $inventory_condition .= db_quote(' AND inventory.amount <= ?i', $params['amount_to']);
    }

    if (Registry::get('settings.General.inventory_tracking') == 'Y' && Registry::get('settings.General.show_out_of_stock_products') == 'N' && $params['area'] == 'C' && empty($params['c2c_vendors_products'])) { // FIXME? Registry in model
        $condition .= " AND (products.infinite_stock OR IF(products.tracking = 'O', inventory.amount > 0, products.amount > 0))";
    }

    if (!empty($params['status'])) {
        $condition .= db_quote(' AND products.status IN (?a)', $params['status']);
    }

    if (!empty($params['free_shipping'])) {
        $condition .= db_quote(' AND products.free_shipping = ?s', $params['free_shipping']);
    }

    if (!empty($params['downloadable'])) {
        $condition .= db_quote(' AND products.is_edp = ?s', $params['downloadable']);
    }

    if (isset($params['pcode'])) {
        if (is_array($params['pcode'])) {
            $pcode = array_filter($params['pcode']);
            $fields[] = 'inventory.combination';
            $u_condition .= db_quote(" $union_condition (inventory.product_code IN (?a) OR products.product_code IN (?a))", $pcode, $pcode);
            $inventory_condition .= db_quote(" AND inventory.product_code IN (?a)", $pcode);
        } else if (fn_string_not_empty($params['pcode'])) {
            $pcode = trim($params['pcode']);
            $fields[] = 'inventory.combination';
            $u_condition .= db_quote(" $union_condition (inventory.product_code LIKE ?l OR products.product_code LIKE ?l)", "%$pcode%", "%$pcode%");
            $inventory_condition .= db_quote(" AND inventory.product_code LIKE ?l", "%$pcode%");
        }
    }

    if (isset($params['supplier_ref'])) {
        $cond = ' AND ';
        if (isset($params['supplier_ref_compact']) && $params['supplier_ref_compact'] === 'Y') {
            $cond = ' OR ';
        }

        $supplierRef = is_array($params['supplier_ref']) ? $params['supplier_ref'] : [$params['supplier_ref']];

        //By default the research is based on an IN comparison, with an array of values
        $comparison = 'IN (?a)';
        $comparisonValue = array_filter($supplierRef);

        //With supplier_ref_match = any, the search is changed to a LIKE comparison, with a string value
        if (isset($params['supplier_ref_match']) && $params['supplier_ref_match'] === 'any') {
            $comparison = 'LIKE ?l';
            $comparisonValue = "%$supplierRef[0]%";
        }

        $condition .= db_quote($cond . ' ( products.w_supplier_ref '.$comparison.' OR (inventory.supplier_reference '.$comparison.') )' , $comparisonValue, $comparisonValue);
    }

    if ((isset($params['amount_to']) && fn_is_numeric($params['amount_to'])) || (isset($params['amount_from']) && fn_is_numeric($params['amount_from'])) || \array_key_exists('supplier_ref', $params) === true || !empty($params['pcode']) || (Registry::get('settings.General.inventory_tracking') == 'Y' && $params['area'] == 'C')) {
        $join .= " LEFT JOIN ?:product_options_inventory as inventory ON inventory.product_id = products.product_id $inventory_condition";
    }

    if (!empty($params['period']) && $params['period'] != 'A') {
        list($params['time_from'], $params['time_to']) = fn_create_periods($params);
        $condition .= db_quote(" AND (products.timestamp >= ?i AND products.timestamp <= ?i)", $params['time_from'], $params['time_to']);
    }

    if (!empty($params['item_ids'])) {
        $condition .= db_quote(" AND products.product_id IN (?n)", explode(',', $params['item_ids']));
    }

    if (!empty($params['order_ids'])) {
        $arr = (strpos($params['order_ids'], ',') !== false || !is_array($params['order_ids'])) ? explode(',', $params['order_ids']) : $params['order_ids'];

        $condition .= db_quote(" AND ?:order_details.order_id IN (?n)", $arr);

        $join .= " LEFT JOIN ?:order_details ON ?:order_details.product_id = products.product_id";
    }

    $limit = '';
    $group_by = 'products.product_id';
    // Show enabled products
    $_p_statuses = array('A');
    $condition .= ($params['area'] == 'C' && empty($params['b2c_vendors_products'])) ?
            db_quote(' AND products.status IN (?a)', $_p_statuses) :
            ''
    ;

    // -- JOINS --
    if (in_array('product_name', $params['extend'])) {
        $fields[] = 'descr1.product as product, descr1.page_title, descr1.meta_description, descr1.meta_keywords';
        $join .= db_quote(" LEFT JOIN ?:product_descriptions as descr1 ON descr1.product_id = products.product_id AND descr1.lang_code = ?s ", $lang_code);
    }

    // get prices
    $price_condition = '';
    if (in_array('prices', $params['extend'])) {
        $fields[] = 'MIN(IF(prices.percentage_discount = 0, prices.price, prices.price - (prices.price * prices.percentage_discount)/100)) as price';
        $join .= " LEFT JOIN ?:product_prices as prices ON prices.product_id = products.product_id AND prices.lower_limit = 1";
    }

    // get prices for search by price
    if (in_array('prices2', $params['extend'])) {
        $join .= " LEFT JOIN ?:product_prices as prices_2 ON prices.product_id = prices_2.product_id AND prices_2.lower_limit = 1 AND prices_2.price < prices.price ";
        $condition .= ' AND prices_2.price IS NULL';
        $price_condition .= ' AND prices_2.price IS NULL';
    }

    // get search words
    if (in_array('search_words', $params['extend'])) {
        $fields[] = 'descr1.search_words';
    }

    // get short & full description if short is empty
    // "description" is applicable only if "short_and_full_description" is not set
    if (in_array('description', $params['extend']) && !in_array('short_and_full_description', $params['extend'])) {
        $fields[] = 'descr1.short_description';
        $fields[] = "IF(descr1.short_description = '', descr1.full_description, '') as full_description";
    }

    // get short & full description
    // bypass and replace the "description" extend even if it is set
    if (in_array('short_and_full_description', $params['extend'])) {
        $fields[] = 'descr1.short_description';
        $fields[] = 'descr1.full_description';
    }


    // get companies
    $companies_join = db_quote(" LEFT JOIN ?:companies AS companies ON companies.company_id = products.company_id ");
    if (in_array('companies', $params['extend'])) {
        $fields[] = 'companies.company as company_name';
        $join .= $companies_join;
    }

    // for compatibility
    if (in_array('category_ids', $params['extend'])) {
        $params['extend'][] = 'categories';
    }

    // get categories
    $_c_statuses = array('A', 'H'); // Show enabled categories
     $category_avail_cond = ($params['area'] == 'C') ? db_quote(" AND ?:categories.status IN (?a) ", $_c_statuses) : '';
    $categories_join = " INNER JOIN ?:products_categories as products_categories ON products_categories.product_id = products.product_id INNER JOIN ?:categories ON ?:categories.category_id = products_categories.category_id $category_avail_cond $feature_search_condition";

    $category_filter_avail_cond = str_replace('?:categories', 'categories_filter', $category_avail_cond);
    $categories_filter_join = " INNER JOIN ?:products_categories as products_categories_filter ON products_categories_filter.product_id = products.product_id INNER JOIN ?:categories AS categories_filter ON categories_filter.category_id = products_categories_filter.category_id $category_filter_avail_cond $category_filter_where_condition";

    if (in_array('categories', $params['extend'])) {
        $fields[] = "GROUP_CONCAT(IF(products_categories.link_type = 'M', CONCAT(products_categories.category_id, 'M'), products_categories.category_id)) as category_ids";
        $join .= $categories_join;
    }

    if (in_array('categories_filter', $params['extend'])) {
        $fields[] = 'products_categories_filter.position';
        $join .= $categories_filter_join;
        $condition .= fn_get_localizations_condition('categories_filter.localization', true);
    }

    //  -- \JOINs --

    if (!empty($u_condition)) {
        $condition .= " $union_condition ((" . ($union_condition == ' OR ' ? '0 ' : '1 ') . $u_condition . ')' . $company_condition . $price_condition . ')';
    }

    // code for products filter by company (vendor)
    if (isset($params['company_id']) && $params['company_id'] != '') {
        $params['company_id'] = intval($params['company_id']);
        $condition .= db_quote(' AND products.company_id = ?i ', $params['company_id']);
    }
    $sortings['approval'] = 'products.approved';

    if (AREA == 'A') {
        if (!empty($params['approval_status']) && $params['approval_status'] != 'all') {
            $condition .= db_quote(' AND products.approved = ?s', $params['approval_status']);
        }
    } elseif (empty($params['approval_status']) || $params['approval_status'] != 'all') {

        $products_prior_approval = Registry::get('addons.vendor_data_premoderation.products_prior_approval');
        $products_updates_approval = Registry::get('addons.vendor_data_premoderation.products_updates_approval');

        if ($products_prior_approval == 'all' || $products_updates_approval == 'all') {
            $condition .= db_quote(' AND products.approved = ?s', 'Y');
        } elseif ($products_prior_approval == 'custom' || $products_updates_approval == 'custom') {
            $condition .= " AND IF (companies.pre_moderation = 'Y' || companies.pre_moderation_edit = 'Y', products.approved = 'Y', 1) ";
        }
    }

    if ($params['area'] == 'C' && in_array('prices', $params['extend'])) {
        $fields[] = 'inventory.w_price';
        $condition .= \Tygh\Database::quote(" AND IF(products.tracking = 'O', inventory.w_price > ?i, prices.price > ?i) ", 0, 0);
    }
    $condition .= \Wizacha\Company::getTypeCondition($params);
    $condition .= Product::getProductCondition($params);

    fn_bestsellers_get_products($params, $fields, $sortings, $condition, $join, $sorting, $group_by);
    fn_discussion_get_products($params, $fields, $sortings, $condition, $join, $sorting, $group_by);
    fn_seo_get_products($params, $fields, $sortings, $condition, $join, $sorting, $group_by);
    // -- SORTINGS --
    if (empty($params['sort_by']) || empty($sortings[$params['sort_by']])) {
        $params = array_merge($params, fn_get_default_products_sorting());
        if (empty($sortings[$params['sort_by']])) {
            $_products_sortings = fn_get_products_sorting(false);
            $params['sort_by'] = key($_products_sortings);
        }
    }

    $default_sorting = fn_get_products_sorting(false);

    if ($params['sort_by'] == 'position' && !in_array('categories_filter', $params['extend'])) {
        $join .= $categories_filter_join;
    }

    if ($params['sort_by'] == 'company' && !in_array('companies', $params['extend'])) {
        $join .= $companies_join;
    }

    if (empty($params['sort_order'])) {
        if (!empty($default_sorting[$params['sort_by']]['default_order'])) {
            $params['sort_order'] = $default_sorting[$params['sort_by']]['default_order'];
        } else {
            $params['sort_order'] = 'asc';
        }
    }

    $sorting = db_sort($params, $sortings);

    // Parfois, le tri peut ne pas être prédictif (notamment le tri par list_price). Rajouter en tri secondaire le
    // product_id permet de garantir l'ordre de tri dans tous les cas.
    $sorting .= ', products.product_id ASC';

    /*
     * image_criteria = Y : Only products with at least one image will be returned
     * image_criteria = N : Only products without image will be returned
     * image_criteria = "whatever you want" : The default behavior
     */
    if (!empty($params['image_criteria'])) {
        if ($params['image_criteria'] == 'Y') {
            $join .= db_quote(" INNER JOIN ?:images_links AS images_links ON images_links.object_id = CAST(products.product_id AS CHAR) AND images_links.object_type = 'product'");
        }
        if ($params['image_criteria'] == 'N') {
            $join .= db_quote(" LEFT JOIN ?:images_links AS images_links ON images_links.object_id = CAST(products.product_id AS CHAR) AND images_links.object_type = 'product'");
            $having .= ' AND COUNT(images_links.pair_id) = 0';
        }
    }

    // Add product moderation status
    if (true === array_key_exists('moderation', $params)) {
        $fields[] = 'product_moderation.created_at as moderation_date';
        $join .= db_quote(" LEFT JOIN doctrine_product_moderation_in_progress AS product_moderation ON product_moderation.product_id = products.product_id");

        // If we don't want products in the moderation process
        if('exclude' === $params['moderation']) {
            $condition .= db_quote(' AND product_moderation.product_id IS NULL');
        }
    }


    // Available offers
    if (container()->getParameter('feature.available_offers')) {
        if (isset($params['products_without_divisions']) && $params['products_without_divisions'] === "Y") {
            $join .= db_quote(" LEFT JOIN doctrine_division_products AS ddp ON ddp.product_id = products.product_id");
            $condition .= db_quote(" AND ddp.product_id is NULL ");

            if ($params['status'] !== "D") {
                $condition .= db_quote(" AND products.status != 'D' ");
            }
        } else if (isset($params['division-enabled']) && !empty($params['division-enabled'])) {
            $join .= db_quote(" LEFT JOIN doctrine_division_products AS ddp ON ddp.product_id = products.product_id");
            $condition .= db_quote(" AND ddp.product_id is NOT NULL AND ddp.division_code IN (?a)", $params['division-enabled']);
        }
    }

    // -- \SORTINGS --

    // Security fix, ensure the query is wrapped and there is a condition with company_id
    // To prevent a vendor to see other vendor products
    // $condition and $company_condition begin with 'AND'

    if ($company_condition) {
        $condition = "$company_condition AND ( 1 $condition )";
    }

    // Used for View cascading
    if (!empty($params['get_query'])) {
        return "SELECT products.product_id FROM ?:products as products $join WHERE 1 $condition GROUP BY products.product_id";
    }

    // Used for Extended search
    if (!empty($params['get_conditions'])) {
        return array($fields, $join, $condition);
    }

    if (!empty($params['limit'])) {
        $limit = db_quote(" LIMIT 0, ?i", $params['limit']);

    } elseif (!empty($params['items_per_page'])) {
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    $calc_found_rows = '';
    if (empty($total)) {
        $calc_found_rows = 'SQL_CALC_FOUND_ROWS';
    }

    $products = db_get_array("SELECT $calc_found_rows " . implode(', ', $fields) . " FROM ?:products as products $join WHERE 1 $condition GROUP BY $group_by HAVING 1 $having $sorting $limit");

    if (!empty($params['items_per_page'])) {
        $params['total_items'] = !empty($total) ? $total : db_get_found_rows();
    } else {
        $params['total_items'] = count($products);
    }

    // Post processing
    if (in_array('categories', $params['extend'])) {
        foreach ($products as $k => $v) {
            list($products[$k]['category_ids'], $products[$k]['main_category']) = fn_convert_categories($v['category_ids']);
        }
    }

    if (!empty($params['item_ids'])) {
        $products = fn_sort_by_ids($products, explode(',', $params['item_ids']));
    }
    if (!empty($params['pid']) && !empty($params['apply_limit']) && $params['apply_limit']) {
        $products = fn_sort_by_ids($products, $params['pid']);
    }

    fn_seo_get_products_post($products, $params);
    $divisionProductsService = container()->get('marketplace.division.products.service');
    $videoService = container()->get('marketplace.pim.video_service');
    foreach ($products as &$product) {
        $product['header_features'] = fn_get_product_features_list($product, 'H');
        $product['available_offers'] = $divisionProductsService->getAllDivisionsCode($product['product_id']);

        if (in_array('video', $params['extend'])) {
            $product['video'] = $videoService->findOneByProductId($product['product_id']);
        }
    }

    LastView::instance()->processResults('products', $products, $params);

    return array($products, $params);
}

function fn_sort_by_ids($items, $ids, $field = 'product_id')
{
    $tmp = array();

    foreach ($items as $k => $item) {
        foreach ($ids as $key => $item_id) {
            if ($item_id == $item[$field]) {
                $tmp[$key] = $item;
                break;
            }
        }
    }

    ksort($tmp);

    return $tmp;
}

function fn_convert_categories($category_ids)
{
    $c_ids = explode(',', $category_ids);
    $categories = array();
    $main_category = 0;
    foreach ($c_ids as $v) {
        if (strpos($v, 'M') !== false) {
            $main_category = intval($v);
        }
        if (!in_array(intval($v), $categories)) {
            $categories[] = intval($v);
        }
    }

    if (empty($main_category)) {
        $main_category = reset($categories);
    }

    return array($categories, $main_category);
}

/**
 * Updates product option
 *
 * @param array $option_data option data array
 * @param int $option_id option ID (empty if we're adding the option)
 * @param string $lang_code language code to add/update option for
 * @return int ID of the added/updated option
 */
function fn_update_product_option($option_data, $option_id = 0, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    //Set company ownership, and hide features during submission
    $company_id = Tygh\Registry::get('runtime.company_id');

    if ((int) $company_id > 0) {
        $option_data['company_id'] = $company_id;
        $isEnableAutoValidateProduct = container()->getParameter('feature.config_enable_auto_validate_product');
        $option_data['status'] = ($isEnableAutoValidateProduct === true) ? 'A' : 'D';
    }
    //Helper: if a w_product_id is set, the option category will be its main category
    if (isset($option_data['w_product_id'])) {
        $option_data['inventory']   = 'Y';
        $option_data['option_type'] = 'S';
        $option_data['required']    = 'Y';
        $category_id                = fn_w_get_product_main_category($option_data['w_product_id']);
        if ($category_id) {
            $option_data['w_categories_path'] =
                (empty($option_data['w_categories_path']) ? '' : ($option_data['w_categories_path'] . ','))
                . $category_id;
        }
    }

    //set bool value for display_on_faceting
   if ($option_data['display_on_faceting'] === 'on') {
       $option_data['display_on_faceting'] = true;
   } else {
       $option_data['display_on_faceting'] = false;
   }

    // Sanitize category description
    if (true === \array_key_exists('description', $option_data) && $option_data['description'] !== null) {
        $purifierService = container()->get('purifier.default');
        $option_data['description'] = \trim($purifierService->purify($option_data['description']));
    }

    // Add option
    if (empty($option_id)) {

        if (empty($option_data['product_id'])) {
            $option_data['product_id'] = 0;
        }

        $option_data['option_id'] = $option_id = db_query('INSERT INTO ?:product_options ?e', $option_data);

        \Tygh\Languages\Helper::insertTranslations('product_options_descriptions', $lang_code, $option_data);

        $create = true;
        // Update option
    } else {

        // if option inventory changed from Y to N, we should clear option combinations
        if (!empty($option_data['product_id']) && !empty($option_data['inventory']) && $option_data['inventory'] == 'N') {
            $condition = fn_get_company_condition('?:product_options.company_id');
            $old_option_inventory = db_get_field("SELECT inventory FROM ?:product_options WHERE option_id = ?i $condition", $option_id);
            if ($old_option_inventory == 'Y') {
                $inventory_filled = db_get_field('SELECT COUNT(*) FROM ?:product_options_inventory WHERE product_id = ?i', $option_data['product_id']);
                if ($inventory_filled) {
                    fn_delete_product_option_combinations($option_data['product_id']);
                }
            }
        }

        db_query("UPDATE ?:product_options SET ?u WHERE option_id = ?i", $option_data, $option_id);
        db_query("UPDATE ?:product_options_descriptions SET ?u WHERE option_id = ?i AND lang_code = ?s", $option_data, $option_id, $lang_code);
    }

    if (!empty($option_data['variants'])) {
        $var_ids = array();

        // Generate special variants structure for checkbox (2 variants, 1 hidden)
        if ($option_data['option_type'] == 'C') {
            $option_data['variants'] = array_slice($option_data['variants'], 0, 1); // only 1 variant should be here
            reset($option_data['variants']);
            $_k = key($option_data['variants']);
            $option_data['variants'][$_k]['position'] = 1; // checked variant
            $v_id = db_get_field("SELECT variant_id FROM ?:product_option_variants WHERE option_id = ?i AND position = 0", $option_id);
            $option_data['variants'][] = array( // unchecked variant
                'position' => 0,
                'variant_id' => $v_id
            );
        }

        $variant_images = array();
        foreach ($option_data['variants'] as $k => $v) {
            if ((!isset($v['variant_name']) || $v['variant_name'] == '') && $option_data['option_type'] != 'C') {
                continue;
            }

            // Update product options variants
            if (isset($v['modifier'])) {
                $v['modifier'] = floatval($v['modifier']);
                if (floatval($v['modifier']) > 0) {
                    $v['modifier'] = '+' . $v['modifier'];
                }
            }

            if (isset($v['weight_modifier'])) {
                $v['weight_modifier'] = floatval($v['weight_modifier']);
                if (floatval($v['weight_modifier']) > 0) {
                    $v['weight_modifier'] = '+' . $v['weight_modifier'];
                }
            }

            $v['option_id'] = $option_id;

            if (empty($v['variant_id']) || (!empty($v['variant_id']) && !db_get_field("SELECT variant_id FROM ?:product_option_variants WHERE variant_id = ?i", $v['variant_id']))) {
                $v['variant_id'] = db_query("INSERT INTO ?:product_option_variants ?e", $v);
                \Tygh\Languages\Helper::insertTranslations('product_option_variants_descriptions', $lang_code, $v);
            } else {
                db_query("UPDATE ?:product_option_variants SET ?u WHERE variant_id = ?i", $v, $v['variant_id']);
                db_query("UPDATE ?:product_option_variants_descriptions SET ?u WHERE variant_id = ?i AND lang_code = ?s", $v, $v['variant_id'], $lang_code);
            }

            $var_ids[] = $v['variant_id'];

            if ($option_data['option_type'] == 'C') {
                fn_delete_image_pairs($v['variant_id'], 'variant_image'); // force deletion of variant image for "checkbox" option
            } else {
                $variant_images[$k] = $v['variant_id'];
            }
        }

        if ($option_data['option_type'] != 'C' && !empty($variant_images)) {
            fn_attach_image_pairs('variant_image', 'variant_image', 0, $lang_code, $variant_images);
        }

        // Delete obsolete variants
        $condition = !empty($var_ids) ? db_quote('AND variant_id NOT IN (?n)', $var_ids) : '';
        $deleted_variants = db_get_fields("SELECT variant_id FROM ?:product_option_variants WHERE option_id = ?i $condition", $option_id, $var_ids);

        if (!empty($deleted_variants)) {
            try {
                db_query("DELETE FROM ?:product_option_variants WHERE variant_id IN (?n)", $deleted_variants);
                db_query("DELETE FROM ?:product_option_variants_descriptions WHERE variant_id IN (?n)", $deleted_variants);
            } catch (DatabaseForeignKeyConstraintException $e) {
                throw new CannotDeleteOptionVariantException($deleted_variants);
            }
            foreach ($deleted_variants as $v_id) {
                fn_delete_image_pairs($v_id, 'variant_image');
            }
        }
    }
    // Rebuild exceptions
    if (!empty($create) && !empty($option_data['product_id'])) {
        fn_update_exceptions($option_data['product_id']);
    }

    \Wizacha\Events\Config::dispatch(
        \Wizacha\Option::EVENT_UPDATE,
        (new \Wizacha\Events\IterableEvent)->setElement($option_id)
    );
    return $option_id;
}

function fn_convert_weight($weight)
{
    if (Registry::get('config.localization.weight_unit')) {
        $g = Registry::get('settings.General.weight_symbol_grams');
        $weight = $weight * Registry::get('config.localization.weight_unit') / $g;
    }
    return sprintf('%01.2f', $weight);
}

function fn_convert_price($price)
{
    $currencies = Registry::get('currencies');
    return $price * $currencies[CART_PRIMARY_CURRENCY]['coefficient'];
}

function fn_get_products_sorting($simple_mode = true)
{
    $sorting = array(
        'null' => array('description' => __('none'), 'default_order' => 'asc', 'desc' => false),
        'timestamp' => array('description' => __('date'), 'default_order' => 'desc'),
        'position' => array('description' => __('default'), 'default_order' => 'asc'),
        'product' => array('description' => __('name'), 'default_order' => 'asc'),
        'price' => array('description' => __('price'), 'default_order' => 'asc'),
    );

    $sorting['bestsellers'] = array('description' => __('bestselling'), 'default_order' => 'desc');

    if ($simple_mode) {
        foreach ($sorting as &$sort_item) {
            $sort_item = $sort_item['description'];
        }
    }

    return $sorting;
}

function fn_get_products_sorting_orders()
{
    return array('asc', 'desc');
}

function fn_get_categories_list($category_ids, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    static $max_categories = 10;
    $c_names = array();
    if (!empty($category_ids)) {
        $c_ids = fn_explode(',', $category_ids);
        $tr_c_ids = array_slice($c_ids, 0, $max_categories);
        $c_names = fn_get_category_name($tr_c_ids, $lang_code);
        if (sizeof($tr_c_ids) < sizeof($c_ids)) {
            $c_names[] = '... (' . sizeof($c_ids) . ')';
        }
    } else {
        $c_names[] = __('all_categories');
    }
    return $c_names;
}

function fn_get_allowed_options_combination($options, $variants, $string, $iteration, $exceptions, $inventory_combinations)
{
    static $result = array();
    $combinations = array();
    foreach ($variants[$iteration] as $variant_id) {
        if (count($options) - 1 > $iteration) {
            $string[$iteration][$options[$iteration]] = $variant_id;
            list($_c, $is_result) = fn_get_allowed_options_combination($options, $variants, $string, $iteration + 1, $exceptions, $inventory_combinations);
            if ($is_result) {
                return array($_c, $is_result);
            }

            $combinations = array_merge($combinations, $_c);
            unset($string[$iteration]);
        } else {
            $_combination = array();
            if (!empty($string)) {
                foreach ($string as $val) {
                    foreach ($val as $opt => $var) {
                        $_combination[$opt] = $var;
                    }
                }
            }
            $_combination[$options[$iteration]] = $variant_id;
            $combinations[] = $_combination;

            foreach ($combinations as $combination) {
                $allowed = true;
                foreach ($exceptions as $exception) {
                    $res = array_diff($exception, $combination);

                    if (empty($res)) {
                        $allowed = false;
                        break;

                    } else {
                        foreach ($res as $option_id => $variant_id) {
                            if ($variant_id == -1) {
                                unset($res[$option_id]);
                            }
                        }

                        if (empty($res)) {
                            $allowed = false;
                            break;
                        }
                    }
                }

                if ($allowed) {
                    $result = $combination;

                    if (empty($inventory_combinations)) {
                        return array($result, true);
                    } else {
                        foreach ($inventory_combinations as $_icombination) {
                            $_res = array_diff($_icombination, $combination);
                            if (empty($_res)) {
                                return array($result, true);
                            }
                        }
                    }
                }
            }

            $combinations = array();
        }
    }

    if ($iteration == 0) {
        return array($result, true);
    } else {
        return array($combinations, false);
    }
}

function fn_apply_options_rules($product)
{
    /*	Options type:
            P - simultaneous/parallel
            S - sequential
    */
    // Check for the options and exceptions types
    if (!isset($product['options_type']) || !isset($product['exceptions_type'])) {
        $product = array_merge($product, db_get_row('SELECT options_type, exceptions_type FROM ?:products WHERE product_id = ?i', $product['product_id']));
    }

    // Get the selected options or get the default options
    $product['selected_options'] = empty($product['selected_options']) ? array() : $product['selected_options'];
    $product['options_update'] = ($product['options_type'] == 'S') ? true : false;

    // Conver the selected options text to the utf8 format
    if (!empty($product['product_options'])) {
        foreach ($product['product_options'] as $id => $option) {
            if (!empty($option['value'])) {
                $product['product_options'][$id]['value'] = fn_unicode_to_utf8($option['value']);
            }
            if (!empty($product['selected_options'][$option['option_id']])) {
                $product['selected_options'][$option['option_id']] = fn_unicode_to_utf8($product['selected_options'][$option['option_id']]);
            }
        }
    }

    $selected_options = & $product['selected_options'];
    $changed_option = empty($product['changed_option']) ? true : false;

    $simultaneous = array();
    $next = 0;

    foreach ($product['product_options'] as $_id => $option) {
        if (!in_array($option['option_type'], array('I', 'T', 'F'))) {
            $simultaneous[$next] = $option['option_id'];
            $next = $option['option_id'];
        }

        if (!empty($option['value'])) {
            $selected_options[$option['option_id']] = $option['value'];
        }

        if (!$changed_option && $product['changed_option'] == $option['option_id']) {
            $changed_option = true;
        }

        if (!empty($selected_options[$option['option_id']]) && ($selected_options[$option['option_id']] == 'checked' || $selected_options[$option['option_id']] == 'unchecked') && $option['option_type'] == 'C') {
            foreach ($option['variants'] as $variant) {
                if (($variant['position'] == 0 && $selected_options[$option['option_id']] == 'unchecked') || ($variant['position'] == 1 && $selected_options[$option['option_id']] == 'checked')) {
                    $selected_options[$option['option_id']] = $variant['variant_id'];
                    if ($changed_option) {
                        $product['changed_option'] = $option['option_id'];
                    }
                }
            }
        }

        // Check, if the product has any options modifiers
        if (!empty($product['product_options'][$_id]['variants'])) {
            foreach ($product['product_options'][$_id]['variants'] as $variant) {
                if (!empty($variant['modifier']) && floatval($variant['modifier'])) {
                    $product['options_update'] = true;
                }
            }
        }
    }

    if (!empty($product['changed_option']) && empty($selected_options[$product['changed_option']]) && $product['options_type'] == 'S') {
        $product['changed_option'] = array_search($product['changed_option'], $simultaneous);
        if ($product['changed_option'] == 0) {
            unset($product['changed_option']);
            $reset = true;
            if (!empty($selected_options)) {
                foreach ($selected_options as $option_id => $variant_id) {
                    if (!isset($product['product_options'][$option_id]) || !in_array($product['product_options'][$option_id]['option_type'], array('I', 'T', 'F'))) {
                        unset($selected_options[$option_id]);
                    }
                }
            }
        }
    }

    if (empty($selected_options) && $product['options_type'] == 'P') {
        $selected_options = fn_get_default_product_options($product['product_id'], true, $product);
    }

    if (empty($product['changed_option']) && isset($reset)) {
        $product['changed_option'] = '';

    } elseif (empty($product['changed_option'])) {
        end($selected_options);
        $product['changed_option'] = key($selected_options);
    }

    if ($product['options_type'] == 'S') {
        empty($product['changed_option']) ? $allow = 1 : $allow = 0;

        foreach ($product['product_options'] as $_id => $option) {
            $product['product_options'][$_id]['disabled'] = false;

            if (in_array($option['option_type'], array('I', 'T', 'F'))) {
                continue;
            }

            $option_id = $option['option_id'];

            if ($allow >= 1) {
                unset($selected_options[$option_id]);
                $product['product_options'][$_id]['value'] = '';
            }

            if ($allow >= 2) {
                $product['product_options'][$_id]['disabled'] = true;
                continue;
            }

            if (empty($product['changed_option']) || (!empty($product['changed_option']) && $product['changed_option'] == $option_id) || $allow > 0) {
                $allow++;
            }
        }

        $product['simultaneous'] = $simultaneous;
    }

    // Restore selected values
    if (!empty($selected_options)) {
        foreach ($product['product_options'] as $_id => $option) {
            if (isset($selected_options[$option['option_id']])) {
                $product['product_options'][$_id]['value'] = $selected_options[$option['option_id']];
            }
        }
    }

    // Generate combination hash to get images. (Also, if the tracking with options, get amount and product code)
    $combination_hash = fn_generate_cart_id($product['product_id'], array('product_options' => $selected_options), true);
    $product['combination_hash'] = $combination_hash;

    // Change product code and amount
    if (!empty($product['tracking']) && $product['tracking'] == 'O') {
        $product['hide_stock_info'] = false;
        if ($product['options_type'] == 'S') {
            foreach ($product['product_options'] as $option) {
                $option_id = $option['option_id'];
                if ($option['inventory'] == 'Y' && empty($product['selected_options'][$option_id])) {
                    $product['hide_stock_info'] = true;

                    break;
                }
            }
        }

        if (!$product['hide_stock_info']) {
            $combination = db_get_row(
                "SELECT product_code, amount FROM ?:product_options_inventory WHERE combination_hash = ?i AND product_id = ?i",
                $combination_hash,
                $product['product_id']
            );

            if (!empty($combination['product_code'])) {
                $product['product_code'] = $combination['product_code'];
            }

            if (Registry::get('settings.General.inventory_tracking') == 'Y') {
                if (isset($combination['amount'])) {
                    $product['inventory_amount'] = $combination['amount'];
                } else {
                    $product['inventory_amount'] = $product['amount'] = 0;
                }
            }
        }
    }

    if (!$product['options_update']) {
        $product['options_update'] = db_get_field('SELECT COUNT(*) FROM ?:product_options_inventory WHERE product_id = ?i', $product['product_id']);
    }

    return $product;
}

function fn_clone_product($product_id)
{
    // Clone main data
    $productData = db_get_row("SELECT * FROM ?:products WHERE product_id = ?i", $product_id);
    unset($productData['product_id']);

    // get the time of creation for the duplicated product and set it
    $currentDate = (new \DateTimeImmutable())->getTimestamp();
    $productData['timestamp'] = $currentDate;
    $productData['updated_timestamp'] = $currentDate;

    Product::setDataBeforeClonning($productData);

    $pid = db_query("INSERT INTO ?:products ?e", $productData);

    // Clone descriptions
    $data = db_get_array("SELECT * FROM ?:product_descriptions WHERE product_id = ?i", $product_id);
    foreach ($data as $v) {
        $v['product_id'] = $pid;
        if ($v['lang_code'] == (string) GlobalState::interfaceLocale()) {
            $orig_name = $v['product'];
            $new_name = $v['product'] . ' [CLONE]';
        }

        $v['product'] .= ' [CLONE]';
        db_query("INSERT INTO ?:product_descriptions ?e", $v);
    }

    // Clone prices
    $data = db_get_array("SELECT * FROM ?:product_prices WHERE product_id = ?i", $product_id);
    foreach ($data as $v) {
        $v['product_id'] = $pid;
        unset($v['price_id']);
        db_query("INSERT INTO ?:product_prices ?e", $v);
    }

    if ($productData['tracking'] == Product::TRACKING_TYPE_INVENTORY) {
        // Clone options links
        $data = db_get_array("SELECT * FROM ?:product_global_option_links WHERE product_id = ?i", $product_id);
        foreach ($data as $newData) {
            $newData['product_id'] = $pid;
            db_query("INSERT INTO ?:product_global_option_links ?e", $newData);
        }

        // Clone options exceptions
        $data = db_get_array("SELECT * FROM ?:product_options_exceptions WHERE product_id = ?i", $product_id);
        foreach ($data as $newData) {
            $newData['product_id'] = $pid;
            db_query("INSERT INTO ?:product_options_exceptions ?e", $newData);
        }

        // Clone inventory
        $data = db_get_array("SELECT product_id, product_code, combination_hash, combination, amount, temp, position, w_price, crossed_out_price, affiliate_link, declination_id, infinite_stock, supplier_reference FROM ?:product_options_inventory WHERE product_id = ?i", $product_id);
        foreach ($data as $newData) {
            $newData['product_id'] = $pid;
            $newData['combination_hash'] = fn_generate_cart_id(
                $pid,
                ['product_options' => fn_get_product_options_by_combination($newData['combination'])]
            );

            db_query("INSERT INTO ?:product_options_inventory ?e", $newData);

            // Clone declinations images
            fn_clone_image_pairs($pid.'_'.$newData['combination'], $product_id.'_'.$newData['combination'], 'declinations');
            fn_clone_price_tiers($product_id, $pid, $newData['combination']);
        }
    }

    // Clone categories links
    $data = db_get_array("SELECT * FROM ?:products_categories WHERE product_id = ?i", $product_id);
    $_cids = array();
    foreach ($data as $v) {
        $v['product_id'] = $pid;
        db_query("INSERT INTO ?:products_categories ?e", $v);
        $_cids[] = $v['category_id'];
    }
    container()->get('marketplace.pim.category_service')->updateProductCount($_cids);

    // Clone product features
    $data = db_get_array("SELECT * FROM ?:product_features_values WHERE product_id = ?s", $product_id);
    foreach ($data as $v) {
        $v['product_id'] = $pid;
        db_query("INSERT INTO ?:product_features_values ?e", $v);
    }

    // Clone blocks
    Block::instance()->cloneDynamicObjectData('products', $product_id, $pid);

    fn_clone_discussion($product_id, $pid, 'P');
    Product::cloneShippings($product_id, $pid);

    // Clone images
    fn_clone_image_pairs($pid, $product_id, 'product');

    // Clone product files
    fn_clone_product_files($product_id, $pid);

    if (!empty($pid) && Registry::get('runtime.company_id')) {
        fn_change_approval_status($pid, 'P');
    }

    fn_clone_price_tiers($product_id, $pid);
    // Clone product metadata
    $data = db_get_array("SELECT * FROM ?:product_metadata WHERE product_id=?s", $product_id);
    foreach ($data as $v) {
        $v['product_id'] = $pid;
        db_query("INSERT INTO ?:product_metadata ?e", $v);
    }

    if (Product::hasChanged($pid)) {
        \Wizacha\Events\Config::dispatch(
            Product::EVENT_CREATE,
            \Wizacha\Events\IterableEvent::fromElement($pid)
        );
    }
    return array('product_id' => $pid, 'orig_name' => $orig_name, 'product' => $new_name);
}

/**
 * Clone the Prices Tiers linked to a product or an inventory
 *
 * @param int $oldProductId
 * @param int $newProductId
 * @param string|null $combination
 * @throws \Wizacha\Marketplace\Exception\ProductNotFound
 */
function fn_clone_price_tiers(int $oldProductId, int $newProductId, string $combination = null): void
{
    $productOptionInventoryRepository = container()->get('marketplace.price_tier.product_option_inventory_repository');
    $newCombination = null;

    if (is_string($combination)) {
        $newCombination = $productOptionInventoryRepository->findOneByCombination($combination, $newProductId);
        $combination = $productOptionInventoryRepository->findOneByCombination($combination, $oldProductId);
    }

    $priceTierRepository = container()->get('marketplace.price_tier.price_tier_repository');
    $collectionProductTiers = $priceTierRepository->findByProductAndProductOptionInventory($oldProductId, $combination);

    foreach ($collectionProductTiers as $priceTier) {
        $priceTier = $priceTier->clonePriceTier(container()->get('marketplace.pim.product.service')->get($newProductId), $newCombination);

        $priceTierRepository->save($priceTier);
    }
}

/**
 * Updates product prices.
 * On a retiré toutes les notions de `lower_limit`  et de `percentage_discount`
 * à l'update des prix
 * TODO: Retirer les colonnes, voir la table cscart_product_prices (/!\ à la rétro compatibilité)
 * @param int $product_id Product identifier.
 * @param array $product_data Array of product data.
 * @param int $company_id Company identifier.
 *
 * @return array Modified <i>$product_data</i> array.
 */
function fn_update_product_prices($product_id, $product_data, $company_id = 0)
{
    $_product_data = $product_data;

    // Update product prices
    if (isset($_product_data['price'])) {
        $_price = array(
            'price' => abs($_product_data['price']),
            'lower_limit' => 1,
        );

        if (!isset($_product_data['prices'])) {
            $_product_data['prices'][0] = $_price;
        } else {
            unset($_product_data['prices'][0]);
            array_unshift($_product_data['prices'], $_price);
        }
    }

    if (!empty($_product_data['prices'])) {
        $data = [
            'product_id' => $product_id,
            'price' => $_product_data['prices'][0]['price'],
            'percentage_discount' => 0,
            'lower_limit' => 1,
            'usergroup_id'=> 0,
        ];

        db_query("REPLACE INTO ?:product_prices ?e", $data);

        $priceTierRepository = container()->get('marketplace.price_tier.price_tier_repository');
        $priceTierLowerLimit0 = $priceTierRepository->findOneByProductIdAndLowerLimit($product_id, 0);
        $priceTiersNotLowerLimit0 = $priceTierRepository->findByProductIdAndLowerLimitNotZero($product_id);

        if ($priceTierLowerLimit0 instanceof PriceTier) {
            // We don't want to delete other priceTiers if we are editing the product price from BO
            $updatedFromBo = \in_array(
                $_REQUEST['dispatch'],
                [
                    'products.update',
                    'products.m_update'
                ]
            );

            if (false === $updatedFromBo) {
                foreach ($priceTiersNotLowerLimit0 as &$priceTierToDelete) {
                    $priceTierRepository->getEntityManager()->remove($priceTierToDelete);
                    unset($priceTierToDelete);
                }
            }

            $priceTierLowerLimit0->setPrice((Money::fromVariable($_product_data['prices'][0]['price'])));
            $priceTierRepository->getEntityManager()->persist($priceTierLowerLimit0);
            $priceTierRepository->getEntityManager()->flush();
        }
    }

    return $_product_data;
}

function fn_update_product_quantity(int $product_id, int $amount): void
{
    if ($amount >= 0) {
        db_query('UPDATE ?:products SET ?u WHERE product_id = ?i', ['amount' => $amount], $product_id);
    }
}

function fn_update_product_crossed_out_prices(int $product_id, float $crossedOutPrice): void
{
    if ($crossedOutPrice >= 0) {
        db_query('UPDATE ?:products SET ?u WHERE product_id = ?i', ['crossed_out_price' => $crossedOutPrice], $product_id);
    }
}

/**
 * Gets product prices.
 *
 * @param int $product_id Product identifier
 * @param array $product_data Array of product data. Result data will be saved in this variable.
 */
function fn_get_product_prices($product_id, &$product_data)
{
    $table_name = '?:product_prices';
    $condition = '';

    // For customer
    if (AREA == 'C') {
        $_prices = db_get_array("SELECT prices.product_id, prices.lower_limit, prices.percentage_discount, IF(prices.percentage_discount = 0, prices.price, prices.price - (prices.price * prices.percentage_discount)/100) as price FROM $table_name prices WHERE prices.product_id = ?i $condition AND lower_limit > 1  ORDER BY lower_limit", $product_id);

        if (empty($product_data['prices']) && !empty($_prices) && sizeof($_prices) > 0) {
            $product_data['prices'] = $_prices;
        }
        // Other - get all
    } else {
        $product_data['prices'] = db_get_array("SELECT prices.product_id, prices.lower_limit, prices.percentage_discount, IF(prices.percentage_discount = 0, prices.price, prices.price - (prices.price * prices.percentage_discount)/100) as price FROM $table_name prices WHERE product_id = ?i $condition ORDER BY lower_limit", $product_id);
    }
}

//
// Copy product files
//
function fn_copy_product_files($file_id, $file, $product_id, $var_prefix = 'file')
{
    $filename = $product_id . '/' . $file['name'];

    $_data = array();

    $downloadsStorageService = container()->get('Wizacha\Storage\DownloadsStorageService');
    list($_data[$var_prefix . '_size'], $_data[$var_prefix . '_path']) = $downloadsStorageService->put($filename, array(
        'file' => $file['path'],
        'overwrite' => true
    ));

    $_data[$var_prefix . '_path'] = fn_basename($_data[$var_prefix . '_path']);
    db_query('UPDATE ?:product_files SET ?u WHERE file_id = ?i', $_data, $file_id);

    return true;
}

/**
 * Add feature variants
 *
 * @param int[]|null $categoryIds
 */
function fn_add_feature_variant(int $feature_id, array $variant, array $categoryIds = null): int
{
    $feature = fn_get_product_feature_data($feature_id , true);

    foreach($feature['variants'] as $f_variant){
        if($f_variant['variant'] === $variant['variant']){
            $variant['variant_id'] = $f_variant['variant_id'];
            break;
        }
    }

    //If the variant is a brand, update meta description and keywords
    if ($feature_id == fn_w_get_brand_id($categoryIds)) {
        $variant['meta_description'] = __('w_meta_description_for_brand',['[variant]' => $variant['variant']]);
        $variant['meta_keywords'] = __('w_meta_keywords_for_brand', ['[variant]' => $variant['variant']]);
    }


    if(isset($variant['variant_id'])){
        return $variant['variant_id'];
    }

    if (empty($variant['variant']) && (!isset($variant['variant']) || $variant['variant'] !== '0')) {
        return false;
    }

    $variant['feature_id'] = $feature_id;
    $variant['variant_id'] = db_query("INSERT INTO ?:product_feature_variants ?e", $variant);

    \Tygh\Languages\Helper::insertTranslations('product_feature_variant_descriptions', null, $variant);

    return $variant['variant_id'];
}

/**
 * Gets default products sorting params
 *
 * @return array Sorting params
 */
function fn_get_default_products_sorting()
{
    $params = explode('-', Registry::get('settings.Appearance.default_products_sorting'));
    if (is_array($params) && count($params) == 2) {
        $sorting = array(
            'sort_by' => array_shift($params),
            'sort_order' => array_shift($params),
        );
    } else {
        $default_sorting = fn_get_products_sorting(false);
        $sort_by = current(array_keys($default_sorting));
        $sorting = array(
            'sort_by' => $sort_by,
            'sort_order' => $default_sorting[$sort_by]['default_order'],
        );
    }

    return $sorting;
}

/**
 * Physically deletes product files on disk
 *
 * @param int $file_id file ID to delete
 * @return boolean true on success, false - otherwise
 */
function fn_delete_product_files_path($file_ids)
{
    if (!empty($file_ids) && is_array($file_ids)) {
        $files_data = db_get_array("SELECT file_path, preview_path, product_id FROM ?:product_files WHERE file_id IN (?n)", $file_ids);

        $downloadsStorageService = container()->get('Wizacha\Storage\DownloadsStorageService');
        foreach ($files_data as $file_data) {
            if (!empty($file_data['file_path'])) {
                $downloadsStorageService->delete($file_data['product_id'] . '/' . $file_data['file_path']);
            }
            if (!empty($file_data['preview_path'])) {
                $downloadsStorageService->delete($file_data['product_id'] . '/' . $file_data['preview_path']);
            }

            // delete empty directory
            $files = $downloadsStorageService->getList($file_data['product_id']);
            if (empty($files)) {
                $downloadsStorageService->deleteDir($file_data['product_id']);
            }

        }

        return true;
    }

    return false;
}

/**
 * Delete product files in folder
 *
 * @param int $folder_id folder ID to delete
 * @param int $product_id product ID to delete all files from it. Ignored if $folder_id is passed
 * @return boolean true on success, false - otherwise
 */
function fn_delete_product_file_folders($folder_id, $product_id = 0)
{
    if (empty($product_id) && !empty($folder_id)) {
        $product_id = db_get_field("SELECT product_id FROM ?:product_file_folders WHERE folder_id = ?i", $folder_id);
    } elseif (empty($folder_id) && empty($product_id)) {
        return false;
    }

    if (!fn_company_products_check($product_id, true)) {
        return false;
    }

    if (!empty($folder_id)) {
        $folder_ids = array($folder_id);
        $file_ids = db_get_fields("SELECT file_id FROM ?:product_files WHERE product_id = ?i AND folder_id = ?i", $product_id, $folder_id);
    } else {
        $folder_ids = db_get_fields("SELECT folder_id FROM ?:product_file_folders WHERE product_id = ?i", $product_id);
        $file_ids = db_get_fields("SELECT file_id FROM ?:product_files WHERE product_id = ?i AND folder_id IN (?n)", $product_id, $folder_ids);
    }

    if (!empty($file_ids) && fn_delete_product_files_path($file_ids) == false) {
        return false;
    }

    db_query("DELETE FROM ?:product_file_folders WHERE folder_id IN (?n)", $folder_ids);
    db_query("DELETE FROM ?:product_file_folder_descriptions WHERE folder_id IN (?n)", $folder_ids);

    db_query("DELETE FROM ?:product_files WHERE file_id IN (?n)", $file_ids);
    db_query("DELETE FROM ?:product_file_descriptions WHERE file_id IN (?n)", $file_ids);

    return true;
}

/**
 * Delete product files
 *
 * @param int $file_id file ID to delete
 * @param int $product_id product ID to delete all files from it. Ignored if $file_id is passed
 * @return boolean true on success, false - otherwise
 */
function fn_delete_product_files($file_id, $product_id = 0)
{
    if (empty($product_id) && !empty($file_id)) {
        $product_id = db_get_field("SELECT product_id FROM ?:product_files WHERE file_id = ?i", $file_id);
    } elseif (empty($folder_id) && empty($product_id)) {
        return false;
    }

    if (!fn_company_products_check($product_id, true)) {
        return false;
    }

    if (!empty($file_id)) {
        $file_ids = array($file_id);
    } else {
        $file_ids = db_get_fields("SELECT file_id FROM ?:product_files WHERE product_id = ?i", $product_id);
    }

    if (fn_delete_product_files_path($file_ids) == false) {
        return false;
    }

    db_query("DELETE FROM ?:product_files WHERE file_id IN (?n)", $file_ids);
    db_query("DELETE FROM ?:product_file_descriptions WHERE file_id IN (?n)", $file_ids);

    return true;
}

/**
 * Update product folder
 *
 * @param array $product_file_fodler folder data
 * @param int $folder_id folder ID for update, if empty - new folder will be created
 * @param string $lang_code language code to update folder description
 * @return int folder ID
 */

function fn_update_product_file_folder($product_file_folder, $folder_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    if (!fn_company_products_check($product_file_folder['product_id'], true)) {
        return false;
    }

    if (empty($folder_id)) {

        $product_file_folder['folder_id'] = $folder_id = db_query('INSERT INTO ?:product_file_folders ?e', $product_file_folder);

        \Tygh\Languages\Helper::insertTranslations('product_file_folder_descriptions', $lang_code, $product_file_folder);
    } else {
        db_query('UPDATE ?:product_file_folders SET ?u WHERE folder_id = ?i', $product_file_folder, $folder_id);
        db_query('UPDATE ?:product_file_folder_descriptions SET ?u WHERE folder_id = ?i AND lang_code = ?s', $product_file_folder, $folder_id, $lang_code);
    }

    return $folder_id;
}

/**
 * Update product file
 *
 * @param array $product_file file data
 * @param int $file_id file ID for update, if empty - new file will be created
 * @param string $lang_code language code to update file description
 * @return boolean true on success, false - otherwise
 */
function fn_update_product_file($product_file, $file_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    if (!fn_company_products_check($product_file['product_id'], true)) {
        return false;
    }

    $uploaded_data = fn_filter_uploaded_data('base_file');
    $uploaded_preview_data = fn_filter_uploaded_data('file_preview');

    if (!empty($file_id) || !empty($uploaded_data[$file_id])) {

        if (!empty($uploaded_data[$file_id])) {
            $product_file['file_name'] = empty($product_file['file_name']) ? $uploaded_data[$file_id]['name'] : $product_file['file_name'];
        }

        // Remove old file before uploading a new one
        if (!empty($file_id)) {
            $dir = $product_file['product_id'];
            $old_file = db_get_row('SELECT file_path, preview_path FROM ?:product_files WHERE product_id = ?i AND file_id = ?i', $product_file['product_id'], $file_id);

            $downloadsStorageService = container()->get('Wizacha\Storage\DownloadsStorageService');
            if (!empty($uploaded_data) && !empty($old_file['file_path'])) {
                $downloadsStorageService->delete($dir . '/' . $old_file['file_path']);
            }

            if (!empty($uploaded_preview_data) && !empty($old_file['preview_path'])) {
                $downloadsStorageService->delete($dir . '/' . $old_file['preview_path']);
            }
        }

        // Update file data
        if (empty($file_id)) {
            $product_file['file_id'] = $file_id = db_query('INSERT INTO ?:product_files ?e', $product_file);

            \Tygh\Languages\Helper::insertTranslations('product_file_descriptions', $lang_code, $product_file);

            $uploaded_id = 0;
        } else {

            db_query('UPDATE ?:product_files SET ?u WHERE file_id = ?i', $product_file, $file_id);
            db_query('UPDATE ?:product_file_descriptions SET ?u WHERE file_id = ?i AND lang_code = ?s', $product_file, $file_id, $lang_code);

            $uploaded_id = $file_id;
        }

        // Copy base file
        if (!empty($uploaded_data[$uploaded_id])) {
            fn_copy_product_files($file_id, $uploaded_data[$uploaded_id], $product_file['product_id']);
        }

        // Copy preview file
        if (!empty($uploaded_preview_data[$uploaded_id])) {
            fn_copy_product_files($file_id, $uploaded_preview_data[$uploaded_id], $product_file['product_id'], 'preview');
        }
    }

    return $file_id;
}

/**
 * Clone product folders
 *
 * @param int $source_id source product ID
 * @param int $target_id target product ID
 * @return associative array with the old folder IDs as keys and the new folder IDs as values
 */
function fn_clone_product_file_folders($source_id, $target_id)
{
    $data = db_get_array("SELECT * FROM ?:product_file_folders WHERE product_id = ?i", $source_id);
    $new_folder_ids = array();
    if (!empty($data)) {
        foreach ($data as $v) {
            $folder_descr = db_get_array("SELECT * FROM ?:product_file_folder_descriptions WHERE folder_id = ?i", $v['folder_id']);

            $v['product_id'] = $target_id;
            $old_folder_id = $v['folder_id'];
            unset($v['folder_id']);

            $new_folder_ids[$old_folder_id] = $new_folder_id = db_query("INSERT INTO ?:product_file_folders ?e", $v);

            foreach ($folder_descr as $key => $descr) {
                $descr['folder_id'] = $new_folder_id;
                db_query("INSERT INTO ?:product_file_folder_descriptions ?e", $descr);
            }
        }
    }

    return $new_folder_ids;
}

/**
 * Clone product files
 *
 * @param int $source_id source product ID
 * @param int $target_id target product ID
 * @return boolean true on success, false - otherwise
 */
function fn_clone_product_files($source_id, $target_id)
{
    $data = db_get_array("SELECT * FROM ?:product_files WHERE product_id = ?i", $source_id);

    $new_folder_ids = fn_clone_product_file_folders($source_id, $target_id);

    if (!empty($data)) {
        foreach ($data as $v) {
            $file_descr = db_get_array("SELECT * FROM ?:product_file_descriptions WHERE file_id = ?i", $v['file_id']);

            $v['product_id'] = $target_id;
            unset($v['file_id']);

            // set new folder id
            if (!empty($v['folder_id'])) {
                $v['folder_id'] = $new_folder_ids[$v['folder_id']];
            }

            $new_file_id = db_query("INSERT INTO ?:product_files ?e", $v);

            foreach ($file_descr as $key => $descr) {
                $descr['file_id'] = $new_file_id;
                db_query("INSERT INTO ?:product_file_descriptions ?e", $descr);
            }

        }

        $downloadsStorageService = container()->get('Wizacha\Storage\DownloadsStorageService');
        $downloadsStorageService->copy($source_id, $target_id);

        return true;
    }

    return false;
}

/**
 * Download product file
 *
 * @param int $file_id file ID
 * @param boolean $is_preview flag indicates that we download file itself or just preview
 * @param string $ekey temporary key to download file from customer area
 * @param string $area current working area
 * @return file starts to download on success, boolean false in case of fail
 */
function fn_get_product_file($file_id, $is_preview = false, $ekey = '', $area = AREA)
{
    if (!empty($file_id)) {
        $column = $is_preview ? 'preview_path' : 'file_path';
        $file_data = db_get_row("SELECT $column, product_id FROM ?:product_files WHERE file_id = ?i", $file_id);

        if ($area == 'A' && !fn_company_products_check($file_data['product_id'], true)) {
            return false;
        }

        if (!empty($ekey)) {

            $ekey_info = fn_get_product_edp_info($file_data['product_id'], $ekey);

            if (empty($ekey_info) || $ekey_info['file_id'] != $file_id) {
                return false;
            }

            // Increase downloads for this file
            $max_downloads = db_get_field("SELECT max_downloads FROM ?:product_files WHERE file_id = ?i", $file_id);
            $file_downloads = db_get_field("SELECT downloads FROM ?:product_file_ekeys WHERE ekey = ?s AND file_id = ?i", $ekey, $file_id);

            if (!empty($max_downloads)) {
                if ($file_downloads >= $max_downloads) {
                    return false;
                }
            }

            db_query('UPDATE ?:product_file_ekeys SET ?u WHERE file_id = ?i AND product_id = ?i AND order_id = ?i', array('downloads' => $file_downloads + 1), $file_id, $file_data['product_id'], $ekey_info['order_id']);
        }

        $downloadsStorageService = container()->get('Wizacha\Storage\DownloadsStorageService');
        return $downloadsStorageService->get($file_data['product_id'] . '/' . $file_data[$column]);
    }

    return false;
}

function fn_get_product_pagination_steps($cols, $products_per_page)
{
    $min_range = $cols * 4;
    $max_ranges = 4;
    $steps = array();

    for ($i = 0; $i < $max_ranges; $i++) {
        $steps[] = $min_range;
        $min_range = $min_range * 2;
    }

    $steps[] = (int)$products_per_page;

    $steps = array_unique($steps);

    sort($steps, SORT_NUMERIC);

    return $steps;
}

function fn_get_product_option_data($option_id, $product_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $extra_variant_fields = '';

    $fields = "a.option_id, b.option_name, a.*, b.option_text, b.description, b.inner_hint, b.incorrect_message, b.comment";
    $join = db_quote(" LEFT JOIN ?:product_options_descriptions as b ON a.option_id = b.option_id AND b.lang_code = ?s"
        . " LEFT JOIN ?:product_global_option_links as c ON c.option_id = a.option_id", $lang_code);
    $condition = db_quote("a.option_id = ?i AND a.product_id = ?i", $option_id, $product_id);

    // FIXME 2tl show admin
    $condition .= fn_get_company_condition('company_id', true, '', true);

    $opt = db_get_row(
        "SELECT " . $fields
        . " FROM ?:product_options as a" . $join
        . " WHERE " . $condition
        . " ORDER BY a.position"
    );

    if (!empty($opt)) {
        $_cond = ($opt['option_type'] == 'C') ? ' AND a.position = 1' : '';

        $join = '';

        $opt['variants'] = db_get_hash_array("SELECT a.variant_id, a.position, a.modifier, a.modifier_type, a.weight_modifier, a.weight_modifier_type, a.status, $extra_variant_fields b.variant_name FROM ?:product_option_variants as a LEFT JOIN ?:product_option_variants_descriptions as b ON a.variant_id = b.variant_id AND b.lang_code = ?s $join WHERE a.option_id = ?i $_cond ORDER BY a.position", 'variant_id', $lang_code, $option_id);

        if (!empty($opt['variants'])) {
            foreach ($opt['variants'] as $k => $v) {
                $opt['variants'][$k]['image_pair'] = fn_get_image_pairs($v['variant_id'], 'variant_image', 'V', true, true, $lang_code);
            }
        }

        $opt['is_system'] = is_string($opt['code']);
    }

    return $opt;
}

/**
 * Product fields for multi update
 *
 * @return array Product fields
 */
function fn_get_product_fields()
{
    $fields = array(
        array(
            'name' => '[data][status]',
            'text' => __('status'),
            'disabled' => 'Y'
        ),
        array(
            'name' => '[data][product]',
            'text' => __('product_name'),
            'disabled' => 'Y'
        ),
        array(
            'name' => '[data][price]',
            'text' => __('price')
        ),
        array(
            'name' => '[data][list_price]',
            'text' => __('list_price')
        ),
        array(
            'name' => '[data][short_description]',
            'text' => __('short_description')
        ),
        array(
            'name' => '[categories]',
            'text' => __('categories')
        ),
        array(
            'name' => '[data][full_description]',
            'text' => __('full_description')
        ),
        array(
            'name' => '[data][search_words]',
            'text' => __('search_words')
        ),
        array(
            'name' => '[data][meta_keywords]',
            'text' => __('meta_keywords')
        ),
        array(
            'name' => '[data][meta_description]',
            'text' => __('meta_description')
        ),
        array(
            'name' => '[main_pair]',
            'text' => __('image_pair')
        ),
        array(
            'name' => '[data][min_qty]',
            'text' => __('min_order_qty')
        ),
        array(
            'name' => '[data][max_qty]',
            'text' => __('max_order_qty')
        ),
        array(
            'name' => '[data][qty_step]',
            'text' => __('quantity_step')
        ),
        array(
            'name' => '[data][list_qty_count]',
            'text' => __('list_quantity_count')
        ),
        array(
            'name' => '[data][product_code]',
            'text' => __('sku')
        ),
        array(
            'name' => '[data][weight]',
            'text' => __('weight')
        ),
        array(
            'name' => '[data][shipping_freight]',
            'text' => __('shipping_freight')
        ),
        array(
            'name' => '[data][is_edp]',
            'text' => __('downloadable')
        ),
        array(
            'name' => '[data][edp_shipping]',
            'text' => __('edp_enable_shipping')
        ),
        array(
            'name' => '[data][free_shipping]',
            'text' => __('free_shipping')
        ),
        array(
            'name' => '[data][zero_price_action]',
            'text' => __('zero_price_action')
        ),
        array(
            'name' => '[data][taxes]',
            'text' => __('taxes')
        ),
        array(
            'name' => '[data][features]',
            'text' => __('features')
        ),
        array(
            'name' => '[data][page_title]',
            'text' => __('page_title')
        ),
        array(
            'name' => '[data][timestamp]',
            'text' => __('creation_date')
        ),
        array(
            'name' => '[data][amount]',
            'text' => __('quantity')
        ),
        array(
            'name' => '[data][avail_since]',
            'text' => __('available_since')
        ),
        array(
            'name' => '[data][out_of_stock_actions]',
            'text' => __('out_of_stock_actions')
        ),
        array(
            'name' => '[data][details_layout]',
            'text' => __('product_details_layout')
        ),
        array(
            'name' => '[data][min_items_in_box]',
            'text' => __('minimum_items_in_box')
        ),
        array(
            'name' => '[data][max_items_in_box]',
            'text' => __('maximum_items_in_box')
        ),
        array(
            'name' => '[data][box_length]',
            'text' => __('box_length')
        ),
        array(
            'name' => '[data][box_width]',
            'text' => __('box_width')
        ),
        array(
            'name' => '[data][box_height]',
            'text' => __('box_height')
        ),
    );

    $fields[] = array(
        'name' => '[data][localization]',
        'text' => __('localization')
    );

    if (Registry::get('settings.General.inventory_tracking') == "Y") {
        $fields[] = array(
            'name' => '[data][tracking]',
            'text' => __('inventory')
        );
    }

    if (!Registry::get('runtime.company_id')) {
        $fields[] = array(
            'name' => '[data][company_id]',
            'text' =>  __('vendor')
        );
    }

    return $fields;
}

/**
 * Get product code by product_id
 *
 * @param int $product_id
 * @param array $product_options
 * @return (string) product code
 */
function fn_get_product_code($product_id, $product_options = array())
{
    if (!empty($product_id)) {
        $tracking = db_get_field("SELECT tracking FROM ?:products WHERE product_id = ?i", $product_id);
        $data['extra']['product_options'] = (array)$product_options;
        $combination_hash = fn_generate_cart_id($product_id, $data['extra']);
        $product_code = db_get_field("SELECT product_code FROM ?:product_options_inventory WHERE combination_hash = ?i AND product_id = ?i", $combination_hash, $product_id);

        if (empty($product_code) || $tracking != 'O') {
            $product_code = db_get_field("SELECT product_code FROM ?:products WHERE product_id = ?i", $product_id);
        }

        return $product_code;
    }

    return '';
}

/**
 * Gets categefories and products totals data
 *
 * @return array Array with categories and products totals
 */
function fn_get_categories_stats()
{
    if ($company_id = \Tygh\Registry::get('runtime.company_id')) {
        $stats = [
            'products_total' => (int) db_get_field('SELECT COUNT(1) FROM ?:products WHERE company_id = ?i', $company_id),
        ];
    } else {
        $stats = [
            'products_total' => (int) db_get_field('SELECT COUNT(1) FROM ?:products'),
        ];
    }

    $categories = db_get_hash_single_array('SELECT COUNT(1) AS category_count, status FROM ?:categories GROUP BY status', ['status', 'category_count']);

    $stats['categories_total'] = array_sum(array_values($categories));
    $stats['categories_active'] = (int) $categories['A'] ?? 0;
    $stats['categories_hidden'] = (int) $categories['H'] ?? 0;
    $stats['categories_disabled'] = (int) $categories['D'] ?? 0;

    return $stats;
}

/**
 * @param $links
 * $links must contains a list of [category_id => integer, vendor_category => string]
 */
function fn_w_update_links_vendor_categories($links)
{

    $links = array_reduce(
        $links,
        function (&$result, $link) {
            $link = fn_w_object_filter($link, 'link_vendor_category');
            if (count($link) == 2 && !empty($link['vendor_category'])) {
                $link['company_id'] = \Tygh\Registry::get('runtime.company_id');
                $result[] =  $link;
            }
            return $result;
        }
    );

    if ($links) {
        \Tygh\Database::query('REPLACE INTO ?:w_links_vendor_categories ?m', $links);
    }
}

/**
 * If there is product on failure category, send a notification
 */
function fn_w_exim_check_failure_category()
{
    if (\Tygh\Database::getField(
        "SELECT 1 FROM ?:products as p
         INNER JOIN ?:products_categories as c
         ON p.product_id = c.product_id
         WHERE company_id=?i AND category_id=?i
         LIMIT 1",
        \Tygh\Registry::get('runtime.company_id'),
        fn_w_get_category_failure_CSV()
    )) {
        fn_set_notification(
            'W',
            __('warning'),
            __('w_csv_import_product_on_failure_category', ['[url]' => fn_url('w_links_vendor_categories.update')])
        );
    }

    return true;
}

/**
 * @param $rates
 * @return array
 * Simplify rates for sending shippings by api
 */
function fn_w_simplify_rates($rates)
{
    return [
        ['amount' => 0, 'value' => $rates[0]['rate_value']['I'][0]['value']],
        ['amount' => 1, 'value' => $rates[0]['rate_value']['I'][1]['value']],
    ];
}

/**
 * @param $rates
 * @return array
 * Simplify rates for sending shippings by api
 */
function fn_w_complexify_rates($rates)
{
    if (empty($rates)) {
        return [];
    }
    if (!isset($rates[0]['value'], $rates[1]['value'], $rates[0]['amount'], $rates[1]['amount'])) {
        return false;
    }
    return [ 1 => [
        'rate_value' => [
            'C' => [0 => ['amount' => 0, 'value' => 0, 'type' => 'F']],
            'W' => [0 => ['amount' => 0, 'value' => 0, 'type' => 'F', 'per_unit' => 'N']],
            'I' => [
                0 => [

                    'type' => 'F',
                    'per_unit' => 'Y',
                    'amount' => $rates[0]['amount'],
                    'value' => $rates[0]['value'],
                ],
                1 => [

                    'type' => 'F',
                    'per_unit' => 'Y',
                    'amount' => $rates[1]['amount'],
                    'value' => $rates[1]['value'],
                ]
            ]
        ]
    ]
    ];
}

/**
 * @param integer $id
 * @return array
 * Return all inventory
 */
function fn_w_get_product_inventory($id)
{
    if (!$id) {
        return [];
    }

    $priceTierRepository = container()->get('marketplace.price_tier.price_tier_repository');
    $productOptionInventory = container()->get('marketplace.price_tier.product_option_inventory_repository')->findByProductId($id);

    return array_map(function(ProductOptionInventory $productOptionInventory) use ($priceTierRepository): array {
        $inventory = $productOptionInventory->expose();

        $inventory['priceTiers'] = $priceTierRepository->getPricesTiersPartialForAPiByProductAndInventory($productOptionInventory->getProductId(), $productOptionInventory->getId());

        foreach ($inventory['priceTiers'] as &$priceTier) {
            $priceTier = $priceTier instanceof PriceTier ? $priceTier->expose(true) : null;
        }

        return $inventory;
    }, $productOptionInventory);
}

/**
 * @param string  $table           //w_company_shipping_rate or w_product_shipping_rates
 * @param array   $rates           //rates to update or create
 * @param string  $extra_column    //name of the third column ($company or $ product)
 * @param integer $extra_colum_id  //id for the third column
 * @param integer $company_id      // Company Id
 * @param float $carriagePaidThreshold  // total before free shipping
 */
function fn_wizacha_shippings_w_update_rate(
    $table,
    $shipping_id,
    $rates,
    $extra_column,
    $extra_column_id,
    $company_id = 0,
    $carriagePaidThreshold = null
) {
    if (!Shipping::updateRate(
            $table,
            $shipping_id,
            $rates,
            $extra_column,
            $extra_column_id,
            $company_id,
            $carriagePaidThreshold
        ) && (isset($_REQUEST['redirect_url']))) {
        unset($_REQUEST['redirect_url']);
    }
}

/**
 * @param array $products
 * @param array $groups
 *
 * Change groups and group products by company and shippings.
 */
function fn_wizacha_shippings_shippings_group_products_list($products, &$groups)
{
    foreach ($groups as &$group){
        array_walk(
            $group['products'],
            function(&$product)
            {
                fn_w_set_shippings_product_data($product);
            }
        );

        $shippings_available_by_product = array_map(
            function($product)
            {
                if (is_array($product['shippings'])) {
                    $product['shippings'] = array_filter(
                        $product['shippings'],
                        function ($shipping) {
                            if ('A' == $shipping['status']) {
                                return true;
                            } else {
                                return false;
                            }
                        }
                    );
                    return $product;
                } else {
                    return ['shippings' => []];
                }

            },
            $group['products']
        );

        $group['subgroups'] = [];
        foreach ($shippings_available_by_product as $k => $v) {
            fn_w_create_subgroup_by_shippings($group['subgroups'], $v, $k);
        }
    }

    array_walk(
        $groups,
        function($group, $key_group) use(&$groups)
        {
            //Create supplementary groups if product's shippings are differents
            if (isset($group['subgroups'])){
                unset($groups[$key_group]['subgroups']);
                array_walk(
                    $group['subgroups'],
                    function($properties, $key_subgroup) use(&$groups, $key_group)
                    {
                        $key_fusion = $key_subgroup ? $key_group.'+'.$key_subgroup : $key_group;
                        $groups[$key_fusion] = ['products' => $properties['products'], 'shippings' => $properties['shippings']] + $groups[$key_group];
                    }
                );
            }
        }
    );
}

/**
 * @param array  $groups
 * @param array  $product (product_data)
 * @param int    $hash
 *
 * Group product by shippings method
 */
function fn_w_create_subgroup_by_shippings (&$groups, $product, $hash)
{
    if (empty($groups)) {
        $groups[] = [
            'shippings' => $product['shippings'],
            'products' => [$hash => $product]
        ];
    } else {
        foreach ($groups as &$group) {
            $common_shipp = array_intersect_key($group['shippings'], $product['shippings']);
            if (!empty($common_shipp)) {
                $group['products'][$hash] = $product;
                $group['shippings'] = $common_shipp;
                return;
            }
        }

        //If there is no group compatible, create a group
        $groups[] = [
            'shippings' => $product['shippings'],
            'products' => [$hash => $product],
        ];
    }
}


/**
 * @param $product_data
 * Add to product_data, shippings and their data.
 */
function fn_w_set_shippings_product_data(&$product_data)
{
    if (empty($product_data)) {
        return;
    }
    if ($product_data['is_edp'] == 'Y') {
        $product_data['shippings'] = ['is_edp' => ['status' => 'A']];
        return;
    }

    $product_id = $product_data['product_id'];
    $company_id = $product_data['company_id'];

    $product_data['w_disable_shippings'] = db_get_field("SELECT w_disable_shippings FROM ?:products WHERE product_id = $product_id");

    $rate_condition = db_quote("(SELECT COUNT(c.rate_value) FROM `?:w_product_shipping_rates` AS c WHERE c.product_id = ?i AND c.shipping_id = a.shipping_id)", $product_id);
    $rate_query = "IF($rate_condition>0, 1, 0) AS specific_rate";
    $status_query = db_quote("IF(a.shipping_id IN (?a), 'D', 'A') as status",explode(',',$product_data['w_disable_shippings']));
    $condition = db_quote(
        " a.shipping_id = b.shipping_id AND a.shipping_id IN (?a) AND a.status = 'A' AND b.lang_code=?s",
        fn_get_companies_shipping_ids($company_id),
        (string) GlobalState::contentLocale()
    );
    $global_query  = "
        SELECT
            a.shipping_id AS shipping_id, b.shipping AS shipping, b.lang_code AS lang_code, $rate_query, $status_query
        FROM
            `?:shippings` AS a,
            `?:shipping_descriptions` AS b
        WHERE $condition
        ORDER BY
            a.position ASC,
            a.shipping_id ASC
    ";
    $resultat = db_get_memoized_array($global_query);

    array_walk(
        $resultat,
        function(&$shipping) use($product_id, $company_id)
        {
            if ($shipping['specific_rate']) {
                $table = " ?:w_product_shipping_rates ";
                $condition = " product_id = $product_id ";
            } else {
                $table = " ?:w_company_shipping_rates ";
                $condition = " company_id = $company_id ";
            }
            $destinations = fn_get_destinations();

            foreach ($destinations as $k => $v) {
                if (!empty($shipping['localization'])) { // check available destinations, but skip default destination
                    $_s = fn_explode(',', $shipping['localization']);
                    $_l = fn_explode(',', $v['localization']);
                    if (!array_intersect($_s, $_l)) {
                        continue;
                    }
                }
                $rate = db_get_row("
                    SELECT rate_value, destination_id, IF(rate_value = '', 0, 1) as rates_defined
                    FROM $table
                    WHERE shipping_id = ?i
                    AND $condition  AND destination_id = ?i",
                    $shipping['shipping_id'],
                    $v['destination_id']
                );

                // Get free shipping threshold from company table
                $rate['carriage_paid_threshold'] = db_get_field(
                    "SELECT carriage_paid_threshold
                    FROM ?:w_company_shipping_rates
                    WHERE shipping_id = ?i
                    AND company_id = ?i
                    AND destination_id = ?i",
                    $shipping['shipping_id'],
                    $company_id,
                    $v['destination_id']
                );

                if (!empty($rate)) {
                    $rate['rate_value'] = unserialize($rate['rate_value']);
                }
                $destinations[$k] = array_merge($destinations[$k], $rate);
            }

            $shipping['rates'] = $destinations;
        }
    );

    foreach ($resultat as $shipping) {
        $shipping = array_merge(fn_get_shipping_info($shipping['shipping_id']), $shipping);

        // To avoid an additional request, we get the carriage_paid_threshold with the rates
        // then we move it from the lowest array level to the top, to expose it properly with the API
        if (false !== \array_key_exists('carriage_paid_threshold', $shipping['rates'][0])) {
            $shipping['carriage_paid_threshold'] = $shipping['rates'][0]['carriage_paid_threshold'];
            unset($shipping['rates'][0]['carriage_paid_threshold']);
        } else {
            $shipping['carriage_paid_threshold'] = null;
        }

        $product_data['shippings'][$shipping['shipping_id']] = $shipping ;
    }

}

/**
 * @param array $package
 * @param int   $shipping_id
 * @return int
 * Return the shipping's price.
 */
function fn_w_calculate_package_rate($package, $shipping_id)
{
    $max = ['id' => null, 'value' => 0];
    $condensed = array_reduce(
        $package['products'],
        function(&$result, $product) use ($shipping_id, &$max)
        {

            if (isset($result[$product['product_id']])) {//if there is multiple combinations of a product, just add amount of the combination.
                $result[$product['product_id']]['amount'] += $product['amount'];
            } else {
                $result[$product['product_id']] = [
                    'product_id' => $product['product_id'],
                    'rate' => $product['shippings'][$shipping_id]['rates'][0]['rate_value']['I'],
                    'amount' => $product['amount']
                ];
            }
            if ($result[$product['product_id']]['rate'][0]['value'] > $max['value']) {
                $max = ['id' => $product['product_id'], 'value' => $result[$product['product_id']]['rate'][0]['value']];
            }
            return $result;
        },
        []
    );

    return array_reduce(
        $condensed,
        function(&$result, $product) use($max)
        {
            if ($product['product_id'] == $max['id']) {
                $result += $product['rate'][0]['value'] + $product['rate'][1]['value']*($product['amount'] - 1);
            } else {
                $result +=  $product['rate'][1]['value']*$product['amount'];
            }
            return $result;
        },
        0
    );
}

/**
 * @param integer $company_id
 * @param integer $shipping_id
 * @return array
 */
function fn_w_get_shipping_info_for_company($company_id, $shipping_id = 0)
{
    $company_shippings = db_get_memoized_field('SELECT shippings FROM ?:companies WHERE company_id = ?i', $company_id);
    $conditions = [];
    $conditions[] = db_quote(
        'a.status = ?s',
        Status::ENABLED
    );

    if ($shipping_id > 0) {
        $conditions[] = db_quote("b.shipping_id = ?i", $shipping_id);
    }

    $companyShippingType = null;

    switch (db_get_memoized_field("SELECT w_company_type FROM ?:companies where company_id=?i", $company_id)) {
        case User::VENDOR_TYPE:
            $companyShippingType = Shipping::TYPE_STD;
            break;
        case User::CLIENT_TYPE:
            $companyShippingType = Shipping::TYPE_C2C;
            break;
    }

    if (\is_string($companyShippingType) === true) {
        $conditions[] = db_quote(
            "(a.w_type = ?s OR a.w_type = ?s)",
            $companyShippingType,
            Shipping::TYPE_ALL
        );
    }

    $subquery = db_quote(
        "IF(a.shipping_id IN (?n),?s,?s) AS status",
        explode(',', $company_shippings),
        Status::ENABLED,
        Status::DISABLED
    );

    return db_get_memoized_hash_array(
        "SELECT
            a.shipping_id,
            a.company_id,
            a.min_weight,
            a.max_weight,
            a.position,
            $subquery,
            b.shipping,
            b.delivery_time
        FROM
             ?:shippings as a
             LEFT JOIN ?:shipping_descriptions as b
                ON a.shipping_id = b.shipping_id
                AND b.lang_code = ?s
        WHERE
              " . implode(' AND ', $conditions) . "
        ORDER BY a.position
        ",
        'shipping_id',
        (string) GlobalState::contentLocale()
    );
}

/**
 * @param integer $product_id
 * @param integer $shipping_id
 */
function fn_w_delete_rates_for_product($product_id, $shipping_id)
{
    db_query("DELETE FROM ?:w_product_shipping_rates WHERE shipping_id = ?i AND product_id = ?i", $shipping_id, $product_id);
}

function fn_can_create_mvp_from_product(int $productId) : bool
{
    // Vendeur (donc pas admin)
    if (Registry::get('runtime.company_id')) {
        return false;
    }

    $container = container();

    // Feature flag
    if (!$container->getParameter('feature.multi_vendor_product')) {
        return false;
    }

    // Déjà linké
    try {
        $product = $container->get('marketplace.pim.product.service')->get($productId);
    } catch (\Wizacha\Marketplace\Exception\NotFound $e) {
        return false;
    }

    if (!empty($product->getLink())) {
        return false;
    }

    return true;
}

function fn_set_previous_template_hidden_fields_to_null(array $request): array
{
    $templateService = container()->get(TemplateService::class);
    if ((int) $request['product_id'] > 0) {

        $productTemplate = container()
            ->get('marketplace.pim.product.service')
            ->get($request['product_id'])
            ->getProductTemplateType();

        $template = $templateService->getTemplate($productTemplate);

        if (is_null($template)) {
            return $request;
        }

        $productTemplateFieldsToPurge = $template->getHiddenFields();

        foreach ($productTemplateFieldsToPurge as $field) {
            $request['product_data'][$field->getName()] = '';
        }
    }

    return $request;
}

function fn_set_new_template_hidden_fields_to_default(array $request): array
{
    $templateService = container()->get(TemplateService::class);

    if ($request['product_id'] > 0) {

        $productTemplate = $request['product_data']['product_template_type'];

        $template = $templateService->getTemplate($productTemplate);

        if (is_null($template)) {
            return $request;
        }

        $productTemplateFields = $template->getHiddenFields();

        foreach ($productTemplateFields as $productTemplateField) {
            if ($productTemplateField->getName() === 'infinite_stock') {
                switch ($productTemplateField->getValue()) {
                    case 'Y':
                        $request['product_data'][$productTemplateField->getName()] = true;
                        break;
                    case 'N':
                        $request['product_data'][$productTemplateField->getName()] = false;
                        break;
                    default:
                        $request['product_data'][$productTemplateField->getName()] = $productTemplateField->getValue();
                        break;
                }

                continue;
            }
            $request['product_data'][$productTemplateField->getName()] = $productTemplateField->getValue();
        }
    }

    return $request;
}

function fn_inventoryId_from_string(int $productId, string $combination): ?int
{
    $return = null;

    if ($combination !== 0 && $combination !== null) {
        $return = db_get_field(
            "SELECT pi.id FROM ?:product_options_inventory AS pi WHERE pi.product_id = ?i AND pi.combination = ?s LIMIT 1;",
            $productId,
            $combination
        );
    }

    return $return;
}

function fn_combination_from_product_code(string $combination, int $productId): string
{
    $combinationCode = db_get_field(
        "SELECT combination FROM ?:product_options_inventory WHERE product_code = ?s AND product_id = ?i LIMIT 1;",
        $combination,
        $productId
    );

    if ($combinationCode !== null) {
        return $combinationCode;
    }

    return "0";
}

/**
 * @var mixed[] $params
 * @var string[] $mvpRules
 * @var bool $canBeAbsent On update the field can be absent be can't be present and empty
 *
 * @return bool
 */
function fn_check_supplier_ref_mvp_rules(array $params, array $mvpRules, bool $canBeAbsent = false): bool
{
    if (false === container()->getParameter('feature.multi_vendor_product')){
        return true;
    }

    if (true === \in_array('supplier_reference', $mvpRules, true)){
        /*
          Through the API :
            If we create a product the supplier reference in $params is called 'w_supplier_ref'.
            If we update a product, it is called 'supplier_ref' in $params.
            We need to tests both keys in the array.

          Through the import :
            the supplier reference is always called w_supplier_ref
        */
        if ($canBeAbsent == true) {
            if (false === \array_key_exists('w_supplier_ref', $params)
                && false === \array_key_exists('supplier_ref', $params)
            ) {
                return true;
            }
        }


        return (true === \array_key_exists('w_supplier_ref', $params) && '' !== $params['w_supplier_ref'])
            || (true === \array_key_exists('supplier_ref', $params) && '' !== $params['supplier_ref'])
            ;

    }

    return true;
}
