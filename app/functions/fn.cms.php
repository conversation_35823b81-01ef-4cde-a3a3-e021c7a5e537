<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\BlockManager\Block;
use Tygh\Languages\Languages;
use Tygh\Menu;
use Tygh\Navigation\LastView;
use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;

// basic cms page types
define('PAGE_TYPE_LINK', 'L');
define('PAGE_TYPE_TEXT', 'T');

/**
 * Returns list of pages
 * <i>$params</i> - Array of various parameters used for element selection:
 * <ul>
 *      <li>page_id - If defined and not zero, get data for the page with this id; otherwise get data for all pages</li>
 *      <li>item_ids - A comma-delimited page identifiers list; if defined, get data for the pages with these ids; otherwise get data for all pages</li>
 *      <li>except_id - Identifier of the page to be excluded from the result</li>
 *   	<li>parent_id - If defined and not zero, get data for the pages with this parent page id</li>
 *   	<li>active_page_id - Identifier of the page being currently shown</li>
 *   	<li>current_page_id - The same as <i>active_page_id</i></li>
 *
 *   	<li>add_root - If defined, additionally returns root element data</li>
 *  	<li>subpages - If defined, additionally returns subpages</li>
 *  	<li>get_tree - If defined, pages will be returned as tree; otherwise as list. Possible value: <i>plain</i></li>
 *  	<li>visible - For pages tree: show visible branch only</li>
 *
 *  	<li>page - Number of the current page for pagination</li>
 *
 *  	<li>pdescr - If defined, additionally returns descriptions.  Possible value: <i>Y</i></li>
 *
 *   	<li>vendor_pages - If defined, try to return pages for the company defined by <i>company_id</i></li>
 *   	<li>company_id - If <i>vendor_pages</i> is defined: if defined, get data for the company with this id</li>
 *
 *   	<li>neighbours - If defined, try to return neighbor pages for the page with the id <i>neighbours_page_id</i></li>
 *   	<li>neighbours_page_id -  If <i>neighbours</i> is defined: if defined, get neighbor pages for the page with this id</li>
 *
 *   	<li>limit - If defined, used to limit your MySQL query results by this value</li>
 *   	<li>sort_by - Table field to sort by, default is position</li>
 *   	<li>sort_order - Sorting direction, ascending or descending; Possible values: <i>asc</i> or <i>desc</i>, default is <i>asc</i></li>
 *
 *   	<li>status - If defined, returns pages only with this status. Can be comma delimited statuses list</li>
 *
 *  	<li>period - If defined, get pages by time period. Time period is generated by ::fn_create_periods</li>
 *   	<li>time_from - Returns pages created earlier than this time</li>
 *   	<li>time_to - Returns pages created later than this time</li>
 *
 *   	<li>parent_page_id - Deprecated, <i>parent_id</i> used instead</li>
 *   	<li>from_page_id - Deprecated, <i>parent_id</i> used instead</li>
 * </ul>
 * @param array $params Array of params
 * @param int $items_per_page  Limit items per page
 * @param string $lang_code 2-letter language code
 * @return array List of pages, params
 */
function fn_get_pages($params = array(), $items_per_page = 0, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    // Init filter
    $params = LastView::instance()->update('pages', $params);

    $default_params = array(
        'page_id' => 0,
        'page' => 1,
        'visible' => false,
        'get_tree' => '',
        'pdescr' => '',
        'subpages' => '',
        'match' => '',
        'items_per_page' => $items_per_page
    );

    if (is_array($params)) {
        $params = array_merge($default_params, $params);
    } else {
        $params = $default_params;
    }

    if (empty($params['pname']) && empty($params['pdescr']) && empty($params['subpages'])) {
        $params['pname'] = 'Y';
    }

    // Remonte front: don't show old broken pages (forms, ...)
    if (container()->getParameter('feature.disable_front_office')) {
        $params['page_type'] = PAGE_TYPE_TEXT;
    }

    $fields = array (
        '?:pages.*',
        '?:page_descriptions.*'
    );

    // Define sort fields
    $sortings = array (
        'position' => array (
            '?:pages.position',
            '?:page_descriptions.page',
        ),
        'name' => '?:page_descriptions.page',
        'timestamp' => '?:pages.timestamp',
        'type' => '?:pages.page_type',
        'multi_level' => array (
            '?:pages.parent_id',
            '?:pages.position',
            '?:page_descriptions.page',
        ),
    );

    $auth = & $_SESSION['auth'];

    $condition = '1';
    $join = $limit = $group_by = '';

    if (isset($params['q']) && fn_string_not_empty($params['q'])) {

        $params['q'] = trim($params['q']);
        if ($params['match'] == 'any') {
            $pieces = fn_explode(' ', $params['q']);
            $search_type = ' OR ';
        } elseif ($params['match'] == 'all') {
            $pieces = fn_explode(' ', $params['q']);
            $search_type = ' AND ';
        } else {
            $pieces = array($params['q']);
            $search_type = '';
        }

        $_condition = array();
        foreach ($pieces as $piece) {
            if (strlen($piece) == 0) {
                continue;
            }

            $tmp = array();
            if (!empty($params['pname']) && $params['pname'] == 'Y') {
                $tmp[] = db_quote("?:page_descriptions.page LIKE ?l", "%$piece%"); // check search words
            }

            if ($params['pdescr'] == 'Y') {
                $tmp[] = db_quote("?:page_descriptions.description LIKE ?l", "%$piece%");
            }

            if (!empty($tmp)) {
                $_condition[] = '(' . implode(' OR ', $tmp) . ')';
            }
        }
        if (!empty($_condition)) {
            $condition .= ' AND (' . implode($search_type, $_condition) . ')';
        }
    }

    $condition .= fn_get_company_condition('?:pages.company_id');

    if (!empty($params['page_type'])) {
        $condition .= db_quote(" AND ?:pages.page_type = ?s", $params['page_type']);
    }

    if (isset($params['parent_id']) && $params['parent_id'] !== '') {
        $p_ids = array();
        if ($params['subpages'] == 'Y') {
            $p_ids = db_get_fields("SELECT a.page_id FROM ?:pages as a LEFT JOIN ?:pages as b ON b.page_id = ?i WHERE a.id_path LIKE CONCAT(b.id_path, '/%')", $params['parent_id']);
        }
        $p_ids[] = $params['parent_id'];

        $condition .= db_quote(" AND ?:pages.parent_id IN (?n)", $p_ids);
    }

    if (isset($params['parent_page_id'])) {
        // set parent id, that was set in block properties
        $params['from_page_id'] = $params['parent_page_id'];
    }
    if (!empty($params['from_page_id'])) {
        $from_id_path = db_get_field("SELECT id_path FROM ?:pages WHERE page_id = ?i", $params['from_page_id']);
        $condition .= db_quote(" AND ?:pages.id_path LIKE ?l", "$from_id_path/%");
    }

    if (!empty($params['status'])) {
        $condition .= db_quote(" AND ?:pages.status IN (?a)", $params['status']);
    }

    if (!empty($params['vendor_pages']) && empty($params['company_id'])) {
        return array(array(), $params);
    } elseif (!empty($params['company_id'])) {
        $condition .= db_quote(" AND ?:pages.company_id = ?i", $params['company_id']);
    }

    $condition .= db_quote(" AND ?:pages.page_type IN (?a)", array_keys(fn_get_page_object_by_type()));

    if (!empty($params['visible'])) {  // for pages tree: show visible branch only
        $page_ids = array();
        if (!empty($params['current_page_id'])) {
            $cur_id_path = db_get_field("SELECT id_path FROM ?:pages WHERE page_id = ?i", $params['current_page_id']);
            if (!empty($cur_id_path)) {
                $page_ids = explode('/', $cur_id_path);
            }
        }

        if (!empty($from_id_path)) {
            $_page_ids = explode('/', $from_id_path);
            $page_ids = array_merge($page_ids, $_page_ids);
            $page_ids = array_unique($page_ids);
        }

        $page_ids[] = $params['page_id'];
        $condition .= db_quote(" AND ?:pages.parent_id IN (?n)", $page_ids);
    }

    if (!empty($params['period']) && $params['period'] != 'A') {
        list($params['time_from'], $params['time_to']) = fn_create_periods($params);
        $condition .= db_quote(" AND (?:pages.timestamp >= ?i AND ?:pages.timestamp <= ?i)", $params['time_from'], $params['time_to']);
    }

    if (!empty($params['item_ids'])) { // get only defined pages
        $condition .= db_quote(" AND ?:pages.page_id IN (?n)", explode(',', $params['item_ids']));
    }

    if (!empty($params['except_id']) && (empty($params['item_ids']) || !empty($params['item_ids']) && !in_array($params['except_id'], explode(',', $params['item_ids'])))) {
        $condition .= db_quote(' AND ?:pages.page_id != ?i AND ?:pages.parent_id != ?i', $params['except_id'], $params['except_id']);
    }

    if (AREA != 'A') {
        $condition .= fn_get_localizations_condition('?:pages.localization', true);
        $condition .= db_quote(" AND (use_avail_period = ?s OR (use_avail_period = ?s AND avail_from_timestamp <= ?i AND avail_till_timestamp >= ?i))", 'N', 'Y', TIME, TIME);
    }

    $join = db_quote('LEFT JOIN ?:page_descriptions ON ?:pages.page_id = ?:page_descriptions.page_id AND ?:page_descriptions.lang_code = ?s', $lang_code);

    if (!empty($params['limit'])) {
        $limit = db_quote(" LIMIT 0, ?i", $params['limit']);
    }

    if (!empty($params['neighbours'])) {
        $parent_ids = array();
        if (!empty($params['neighbours_page_id'])) {
            $id_path = db_get_field("SELECT id_path FROM ?:pages WHERE page_id = ?i", $params['neighbours_page_id']);
            $parent_ids = explode('/', $id_path);
            if (count($parent_ids) == 1) {
                array_unshift($parent_ids, 0);
            }
            $params['root_id'] = $parent_ids[0];
        } else {
            $parent_ids[] = 0;
        }

        $condition .= db_quote(" AND ?:pages.parent_id IN (?n)", array_unique($parent_ids));
    }

    //seo hook
    if (isset($params['compact']) && $params['compact'] == 'Y') {
        $condition .= db_quote(' OR ?:seo_names.name LIKE ?s', '%' . preg_replace('/-[a-zA-Z]{1,3}$/i', '', str_ireplace(SEO_FILENAME_EXTENSION, '', $params['q'])) . '%');
    }
    $fields[] = '?:seo_names.name as seo_name';
    $join .= db_quote(
        " LEFT JOIN ?:seo_names ON ?:seo_names.object_id = CAST(?:pages.page_id AS CHAR) ?p",
        fn_get_seo_join_condition('a')
    );

    if (!empty($params['w_type'])) {
        $condition .= db_quote(" AND ?:pages.w_type = ?s", $params['w_type']);
    }

    if (!empty($params['get_tree'])) {
        $params['sort_by'] = 'multi_level';
    }

    $sorting = db_sort($params, $sortings, 'position', 'asc');

    if (!empty($group_by)) {
        $group_by = ' GROUP BY ' . $group_by;
    }

    // Get search conditions
    if (!empty($params['get_conditions'])) {
        return array($fields, $join, $condition);
    }

    if (!empty($params['items_per_page'])) {
        $params['total_items'] = db_get_field("SELECT COUNT(DISTINCT(?:pages.page_id)) FROM ?:pages ?p WHERE ?p ?p ?p", $join, $condition, $group_by, $sorting);
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    $pages = db_get_hash_array("SELECT " . implode(', ', $fields) ." FROM ?:pages ?p WHERE ?p ?p ?p ?p", 'page_id', $join, $condition, $group_by, $sorting, $limit);

    if (!empty($params['active_page_id']) && !empty($pages[$params['active_page_id']])) {
        $pages[$params['active_page_id']]['active'] = true;
        Registry::set('runtime.active_page_ids', explode('/', $pages[$params['active_page_id']]['id_path']));
    }

    if (!empty($pages)) {
        foreach ($pages as $k => $v) {
            $pages[$k]['level'] = substr_count($v['id_path'], '/');
        }

        if (!empty($params['get_tree'])) {
            $delete_keys = array();
            foreach ($pages as $k => $v) {
                if (!empty($v['parent_id']) && !empty($pages[$v['parent_id']])) {
                    $pages[$v['parent_id']]['subpages'][$v['page_id']] = & $pages[$k];
                    $delete_keys[] = $k;
                }

                if (!empty($v['parent_id']) && ((!isset($params['root_id']) && empty($pages[$v['parent_id']])) || (isset($params['root_id']) && $v['parent_id'] != $params['root_id'])) && (empty($params['from_page_id']) || $params['from_page_id'] != $v['parent_id'])) { // delete pages that don't have parent. FIXME: should be done on database layer
                    $delete_keys[] = $k;
                }
            }

            foreach ($delete_keys as $k) {
                unset($pages[$k]);
            }
        } elseif (!empty($params['item_ids'])) {
            $pages = fn_sort_by_ids($pages, explode(',', $params['item_ids']), 'page_id');
        }

        if ($params['get_tree'] == 'plain') {
            $pages = fn_multi_level_to_plain($pages, 'subpages');
        }

        if (!empty($params['get_children_count'])) {
            $where_condition = !empty($params['except_id']) ? db_quote(' AND page_id != ?i', $params['except_id']) : '';
            if ($params['get_tree'] == 'plain') {
                $_page_ids = array();
                foreach ($pages as $_p) {
                    $_page_ids[] = $_p['page_id'];
                }
            } else {
                $_page_ids = array_keys($pages);
            }
            $children = db_get_hash_single_array("SELECT parent_id, COUNT(page_id) as children FROM ?:pages WHERE parent_id IN (?n) ?p GROUP BY parent_id", array('parent_id', 'children'), $_page_ids, $where_condition);

            if (!empty($children)) {
                if ($params['get_tree'] == 'plain') {
                    foreach ($pages as $_id => $_p) {
                        if (!empty($children[$_p['page_id']])) {
                            $pages[$_id]['has_children'] = true;
                        }
                    }
                } else {
                    foreach ($children as $k => $v) {
                        $pages[$k]['has_children'] = !empty($v);
                    }
                }
            }
        }
    }

    if (!empty($params['add_root'])) {
        array_unshift($pages, array('page_id' => '', 'page' => $params['add_root']));
    }

    fn_dropdown_appearance_cut_second_third_levels($pages, 'subpages', $params);

    return array($pages, $params);
}


/**
 * Updates page data by id or create new
 *
 * @param array $page_data Page data
 * @param int $page_id Page idetifier, if equals zero new page will be created
 * @param string $lang_code 2 letters language code
 * @return int Page identifier on success, false otherwise
 */
function fn_update_page($page_data, $page_id = 0, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    if (!empty($page_id) && !fn_check_company_id('pages', 'page_id', $page_id)) {
        fn_company_access_denied_notification();

        return false;
    }

    if (!empty($page_data)) {
        if (!empty($page_data['avail_from_timestamp'])) {
            $page_data['avail_from_timestamp'] = fn_parse_date($page_data['avail_from_timestamp']);
        } else {
            $page_data['avail_from_timestamp'] = 0;
        }

        if (!empty($page_data['avail_till_timestamp'])) {
            $page_data['avail_till_timestamp'] = fn_parse_date($page_data['avail_till_timestamp']) + 86399;
        } else {
            $page_data['avail_till_timestamp'] = 0;
        }

        $page_data['add_items'] = empty($page_data['add_items']) ? array() : $page_data['add_items'];

        if (isset($page_data['timestamp'])) {
            $page_data['timestamp'] = fn_parse_date($page_data['timestamp']);
        }

        if (isset($page_data['localization'])) {
            $page_data['localization'] = empty($page_data['localization']) ? '' : fn_implode_localizations($page_data['localization']);
        }

        $old_page_data = array();

        if (!empty($page_data['page'])) {
            fn_set_company_id($_data, 'company_id', true);
        }

        // Sanitize page description
        if (true === \array_key_exists('description', $page_data) && $page_data['description'] !== null) {
            $purifierService = container()->get('purifier.default');
            $page_data['description'] = \trim($purifierService->purify($page_data['description']));
        }

        if (empty($page_id)) {
            // page title required
            if (empty($page_data['page'])) {
                return false;
            }

            // add new page
            $create = true;
            $page_data['page_id'] = $page_id = db_query('INSERT INTO ?:pages ?e', $page_data);

            \Tygh\Languages\Helper::insertTranslations('page_descriptions', $lang_code, $page_data);

            // now we need to update 'id_path' field, as we know $page_id
            /* Generate id_path for page */
            $parent_id = intval($page_data['parent_id']);
            if ($parent_id == 0) {
                $id_path = $page_id;
            } else {
                $id_path = db_get_row("SELECT id_path FROM ?:pages WHERE page_id = ?i", $parent_id);
                $id_path = $id_path['id_path'] . '/' . $page_id;
            }

            db_query('UPDATE ?:pages SET ?u WHERE page_id = ?i', array('id_path' => $id_path), $page_id);

        } else {
            $old_page_data = fn_get_page_data($page_id, $lang_code);
            $create = false;
            // page title is not updated
            if (empty($page_data['page'])) {
                unset($page_data['page']);
            }

            // update existing page
            db_query('UPDATE ?:pages SET ?u WHERE page_id = ?i', $page_data, $page_id);
            db_query('UPDATE ?:page_descriptions SET ?u WHERE page_id = ?i AND lang_code = ?s', $page_data, $page_id, $lang_code);

            // regenerate id_path for child pages
            if (isset($page_data['parent_id'])) {
                fn_change_page_parent($page_id, $page_data['parent_id']);
            }
        }
    }

    fn_form_builder_update_page_post($page_data, $page_id, $lang_code);

    if (Registry::get('runtime.company_id')) {
        $page_data['company_id'] = Registry::get('runtime.company_id');
    }
    fn_seo_update_object($page_data, $page_id, 'a', $lang_code);

    if (!empty($page_data['page']) && !$create) {
        //update page
        $page_childrens = db_get_fields("SELECT page_id FROM ?:pages WHERE id_path LIKE ?l AND parent_id != 0", '%' . $page_id . '%');

        if (!empty($page_childrens)) {
            //update childrens company if we update company for root page.
            if ($page_data['parent_id'] == 0 || $old_page_data['parent_id'] == 0) {
                fn_change_page_company($page_id, $page_data['company_id']);
            }
        }
    }


    return $page_id;
}

/**
 * Fucntion changes company_id on child pages.
 *
 * @param int $page_id
 * @param int $new_company_id
 */
function fn_change_page_company($page_id, $new_company_id)
{
    $current_path = db_get_field("SELECT id_path FROM ?:pages WHERE page_id = ?i", $page_id);
    if (!empty($current_path)) {
        db_query("UPDATE ?:pages SET company_id = ?i WHERE id_path LIKE ?l", $new_company_id, "$current_path/%");
    }
}

/**
 * Changes parent of page
 *
 * @param int $page_id Page identifier to change parent
 * @param int $new_parent_id Identifier of new parent page
 * @return bool True on success false otherwise
 */
function fn_change_page_parent($page_id, $new_parent_id)
{
    if (!empty($page_id)) {

        //$page_data['localization'] = empty($page_data['localization']) ? '' : fn_implode_localizations($page_data['localization']);

        $new_parent_path = db_get_field("SELECT id_path FROM ?:pages WHERE page_id = ?i", $new_parent_id);
        $current_path = db_get_field("SELECT id_path FROM ?:pages WHERE page_id = ?i", $page_id);

        if (!empty($new_parent_path) && !empty($current_path)) {
            db_query("UPDATE ?:pages SET parent_id = ?i, id_path = ?s WHERE page_id = ?i", $new_parent_id, "$new_parent_path/$page_id", $page_id);
            db_query("UPDATE ?:pages SET id_path = CONCAT(?s, SUBSTRING(id_path, ?i)) WHERE id_path LIKE ?l", "$new_parent_path/$page_id/", (strlen($current_path."/") + 1), "$current_path/%");

        } elseif (empty($new_parent_path) && !empty($current_path)) {
            db_query("UPDATE ?:pages SET parent_id = ?i, id_path = ?s WHERE page_id = ?i", $new_parent_id, $page_id, $page_id);
            db_query("UPDATE ?:pages SET id_path = CONCAT(?s, SUBSTRING(id_path, ?i)) WHERE id_path LIKE ?l", "$page_id/", (strlen($current_path."/") + 1), "$current_path/%");
        }

        return true;
    }

    return false;
}

/**
 * Clones page
 *
 * @param int $page_id Identifier of page to be cloned
 * @return array New page data
 */
function fn_clone_page($page_id)
{
    // Clone main data
    $data = db_get_row("SELECT * FROM ?:pages WHERE page_id = ?i", $page_id);
    unset($data['page_id']);
    $data['status'] = 'D';

    if (!fn_check_company_id('pages', 'page_id', $page_id)) {
        fn_company_access_denied_notification();
        unset($data);
    }

    if (empty($data)) {
        return false;
    }

    $new_page_id = db_query("INSERT INTO ?:pages ?e", $data);

    // Update parent-child deps
    $id_path = explode('/', $data['id_path']);
    array_pop($id_path);
    $id_path[] = $new_page_id;
    db_query("UPDATE ?:pages SET id_path = ?s WHERE page_id = ?i", implode('/', $id_path), $new_page_id);

    // Clone descriptions
    $data = db_get_array("SELECT * FROM ?:page_descriptions WHERE page_id = ?i", $page_id);
    foreach ($data as $v) {
        $v['page_id'] = $new_page_id;
        if ($v['lang_code'] == (string) GlobalState::interfaceLocale()) {
            $orig_name = $v['page'];
            $new_name = $v['page'] . ' [CLONE]';
        }

        $v['page'] .= ' [CLONE]';
        db_query("INSERT INTO ?:page_descriptions ?e", $v);
    }

    Block::instance()->cloneDynamicObjectData('pages', $page_id, $new_page_id);

    fn_form_builder_clone_page($page_id, $new_page_id);

    return array('page_id' => $new_page_id, 'orig_name' => $orig_name, 'page' => $new_name);
}

/**
 * Returns page object data by page type (page types may be expanded by Add-ons)
 *
 * @param string $page_type Page type
 * @return array Page object data
 */
function fn_get_page_object_by_type($page_type = '')
{
    $types = array (
        PAGE_TYPE_TEXT => array(
            'single' => 'page',
            'name' => 'pages',
            'add_name' => 'add_page',
            'edit_name' => 'editing_page',
            'new_name' => 'new_page',
        ),
        PAGE_TYPE_LINK => array(
            'single' => 'link',
            'name' => 'links',
            'add_name' => 'add_link',
            'edit_name' => 'editing_link',
            'new_name' => 'new_link',
        ),
    );

    $types[PAGE_TYPE_FORM] = array(
        'single' => 'form',
        'name' => 'forms',
        'add_name' => 'add_form',
        'edit_name' => 'editing_form',
        'new_name' => 'new_form',
    );
    return empty($page_type) ? $types : $types[$page_type];
}

/**
 * Gets list of pages and returns as plain list
 *
 * @return array List of pages
 */
function fn_get_pages_plain_list($params = array())
{
    $default_params = array(
        'get_tree' => 'plain',
        'get_parent_pages' => true
    );

    if (is_array($params)) {
        $params = array_merge($default_params, $params);
    } else {
        $params = $default_params;
    }

    list($pages) = fn_get_pages($params);

    return $pages;
}

/**
 * Function cut the second and third level childs arrays.
 *
 * @param array $data Given data
 * @param string $childs_name Name of array with child elements
 * @param array $params Array with parameters of displyaing second and third childs elements
 */
function fn_dropdown_appearance_cut_second_third_levels(&$data, $childs_name, $params)
{
    if (isset($params['dropdown_second_level_elements']) || isset($params['dropdown_third_level_elements'])) {
        $params['dropdown_second_level_elements'] = !empty($params['dropdown_second_level_elements']) ? intval($params['dropdown_second_level_elements']) : 0;
        $params['dropdown_third_level_elements'] = !empty($params['dropdown_third_level_elements']) ? intval($params['dropdown_third_level_elements']) : 0;
        if ($params['dropdown_second_level_elements'] < 0) {
            $params['dropdown_second_level_elements'] = 0;
        }
        if ($params['dropdown_third_level_elements'] < 0) {
            $params['dropdown_third_level_elements'] = 0;
        }

        foreach ($data as $k1 => $v1) {
            if (!empty($v1[$childs_name])) {
                $count2 = count($v1[$childs_name]);
                if ($count2 > $params['dropdown_second_level_elements']) {
                    $data[$k1][$childs_name] = array_slice($v1[$childs_name], 0, $params['dropdown_second_level_elements']);
                    $data[$k1]['show_more'] = true;
                }

                foreach ($data[$k1][$childs_name] as $k2 => $v2) {
                    if (!empty($v2[$childs_name])) {
                        $count3 = count($v2[$childs_name]);
                        if ($count3 > $params['dropdown_third_level_elements']) {
                            $data[$k1][$childs_name][$k2][$childs_name] = array_slice($v2[$childs_name], 0, $params['dropdown_third_level_elements']);
                            $data[$k1][$childs_name][$k2]['show_more'] = true;
                        }
                    }
                }
            }
        }
    }
}

/**
 * Fucntion forms detailed-page URL for elements in the block with Dropdown appearance
 *
 * @param array $object_data Current element of block
 * @param string $object_type Type of object
 * @return string URL to the detail page of the given object
 */
function fn_form_dropdown_object_link($object_data, $object_type)
{
    $result = '';

    if ($object_type == 'categories') {
        $result = fn_url('categories.view?category_id=' . $object_data['category_id']);
    } elseif ($object_type == 'pages') {
        if (isset($object_data['page_type']) && $object_data['page_type'] == 'L' && isset($object_data['link'])) {
            $result = $object_data['link'];
        } else {
            $result = fn_url('pages.view?page_id=' . $object_data['page_id']);
        }
    } elseif (!empty($object_data['href'])) {
        $result = fn_url($object_data['href']);
    }

    return $result;
}

/**
 * Function checks should the given menu element be marked as active or not
 *
 * @param array $object_data Menu item
 * @param string $object_type Type of menu item (category or page data)
 * @return bool
 */
function fn_check_is_active_menu_item($object_data, $object_type)
{
    $active_ids = array();
    $id = null;

    if ($object_type == 'categories') {
        $active_ids = Registry::ifGet('runtime.active_category_ids', array());
        $id = $object_data['category_id'];
    } elseif ($object_type == 'pages') {
        $active_ids = Registry::ifGet('runtime.active_page_ids', array());
        $id = $object_data['page_id'];
    } else {
        return false;
    }

    return in_array($id, $active_ids);
}

/**
 * Gets page data
 *
 * @param mixed $page_id Page identifier
 * @param string $lang_code 2 letters language code
 * @param bool $preview Page data for preview (ignore cahce)
 * @return array|bool Page data on success, false otherwise
 */
function fn_get_page_data($page_id, $lang_code = null, $preview = false, $setPlaceholder = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    static $cache = array();

    if (empty($page_id)) {
        return false;
    }

    if (empty($cache[$page_id]) || $setPlaceholder === true) {
        $condition = '';

        $condition .= fn_get_company_condition('?:pages.company_id');

        $condition .= db_quote(" AND ?:pages.page_type IN (?a)", array_keys(fn_get_page_object_by_type()));

        $cache[$page_id] = db_get_row(
            "SELECT * FROM ?:pages "
                . "INNER JOIN ?:page_descriptions ON ?:pages.page_id = ?:page_descriptions.page_id "
            . "WHERE ?:pages.page_id = ?i AND ?:page_descriptions.lang_code = ?s ?p",
            $page_id, $lang_code, $condition
        );

        if (empty($cache[$page_id]) || (AREA != 'A' && ($cache[$page_id]['status'] == 'D' || $cache[$page_id]['use_avail_period'] == 'Y' && ($cache[$page_id]['avail_from_timestamp'] > TIME || $cache[$page_id]['avail_till_timestamp'] < TIME))) && empty($preview)) {
            return false;
        }
        fn_seo_get_page_data($cache[$page_id], $lang_code);
        if (!empty($cache[$page_id]['page_type']) && $cache[$page_id]['page_type'] == PAGE_TYPE_FORM) {
            list($cache[$page_id]['form']['elements'], $cache[$page_id]['form']['general']) = fn_get_form_elements($cache[$page_id]['page_id'], true);
        }

        // Generate meta description automatically
        if (empty($cache[$page_id]['meta_description']) && defined('AUTO_META_DESCRIPTION') && AREA != 'A') {
            $cache[$page_id]['meta_description'] = fn_generate_meta_description($cache[$page_id]['description']);
        }
    }

    return (!empty($cache[$page_id]) ? $cache[$page_id] : false);
}

/**
 * Gets page name
 *
 * @param array $page_id Page identifier
 * @param string $lang_code 2 letters language code
 * @return string|bool Page name on success, false otherwise
 */
function fn_get_page_name($page_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();

    if (!empty($page_id)) {
        if (is_array($page_id)) {
            return db_get_hash_single_array("SELECT page_id, page FROM ?:page_descriptions WHERE page_id IN (?n) AND lang_code = ?s", array('page_id', 'page'), $page_id, $lang_code);
        } else {
            return db_get_field("SELECT page FROM ?:page_descriptions WHERE page_id = ?i AND lang_code = ?s", $page_id, $lang_code);
        }
    }

    return false;
}

function fn_get_file_description($path, $descr_key, $get_lang_var = false)
{
    $return = '';

    $fd = @fopen($path, 'r');
    if ($fd !== false) {
        $counter = 1;

        while (($s = fgets($fd, 4096)) && ($counter < 3)) {
            preg_match('/' . $descr_key . ':(\w+)/i', $s, $matches);
            if (!empty($matches[1])) {
                $return = $get_lang_var ? $matches[1] : __($matches[1]);
                break;
            }
        }

        fclose($fd);
    }

    return $return;
}

/**
 *  Delete page and its subpages
 *
 * @param int $page_id Page ID
 * @param bool $recurse Delete page recursively or not
 * @return array Returns ids of deleted pages or false if function can't delete page
 */
function fn_delete_page($page_id, $recurse = true)
{
    $page_id = (int) $page_id;
    if (!empty($page_id) && fn_check_company_id('pages', 'page_id', $page_id)) {
        // Delete all subpages
        if ($recurse == true) {
            $id_path = db_get_field("SELECT id_path FROM ?:pages WHERE page_id = ?i", $page_id);
            $page_ids	= db_get_fields("SELECT page_id FROM ?:pages WHERE page_id = ?i OR id_path LIKE ?l", $page_id, "$id_path/%");
        } else {
            $page_ids = array($page_id);
        }

        $seoService = container()->get('marketplace.seo.seo_service');

        foreach ($page_ids as $v) {
            // Deleting page
            db_query("DELETE FROM ?:pages WHERE page_id = ?i", $v);
            db_query("DELETE FROM ?:page_descriptions WHERE page_id = ?i", $v);
            // deleting form elements
            $element_ids = db_get_fields("SELECT element_id FROM ?:form_options WHERE page_id = ?i", $v);
            db_query("DELETE FROM ?:form_descriptions WHERE object_id IN (?n)", $element_ids);
            db_query("DELETE FROM ?:form_options WHERE page_id = ?i", $v);

            $seoService->removeSlug(\Wizacha\Marketplace\Seo\SlugTargetType::CMS_PAGE(), $v);
            Block::instance()->removeDynamicObjectData('pages', $v);
        }

        return $page_ids; // Returns ids of deleted pages
    } else {
        return false;
    }
}

/** Block manager **/

/**
 * Returns currencies list from registry
 *
 * @return array Currencies list
 */
function fn_get_currencies()
{
    return Registry::get('currencies');
}

/**
 * @deprecated
 *
 * Returns only active languages list for block (as lang_code => array(name, lang_code, status, country_code)
 *
 * @param string $default_value Default value defined in Block scheme
 * @param array $block filled block data
 * @param array $block_scheme Scheme of current block
 * @param bool $include_hidden if true get hidden languages too
 * @param array $params extra params
 *      area - get languages for specified area. Default: "C"
 * @return array Languages list
 */
function fn_get_languages($default_value = '', $block = array(), $block_scheme = array(), $include_hidden = false, $params = array())
{
    $area = isset($params['area']) ? $params['area'] : AREA;
    $languages = Languages::getAvailable($area, $include_hidden);

    return $languages;
}

/**
 * @deprecated
 *
 * Returns active and hidden languages list (as lang_code => array(name, lang_code, status, country_code)
 *
 * @param  string $area        Area ('A' for admin or 'C' for customer)
 * @param bool $include_hidden if true get hidden languages too
 * @return array Languages list
 */
function fn_get_avail_languages($area = AREA, $include_hidden = false)
{
    return Languages::getAvailable($area, $include_hidden);
}

/**
 * Gets currensies list
 *
 * @param bool $only_avail if true get only available currensies
 * @return array Currencies list
 */
function fn_get_simple_currencies($only_avail = true)
{
    $status_cond = ($only_avail) ? "WHERE status = 'A'" : '';

    return db_get_hash_single_array("SELECT a.*, b.description FROM ?:currencies as a LEFT JOIN ?:currency_descriptions as b ON a.currency_code = b.currency_code AND lang_code = ?s $status_cond ORDER BY a.position", array('currency_code' , 'description'), (string) GlobalState::interfaceLocale());
}

/**
 * @deprecated
 *
 * Gets only active languages list (as lang_code => name)
 *
 * @param bool $include_hidden if true get hiddenlanguages too
 * @return array Currencies list
 */
function fn_get_simple_languages($include_hidden = false)
{
    return Languages::getSimpleLanguages($include_hidden);
}

/**
 * Gets name of menu
 *
 * @param int $menu_id Menu identifier
 * @param string $lang_code 2 letters language code
 * @return string Menu mane
 */
function fn_get_menu_name($menu_id, $lang_code = null)
{
    return Menu::getName($menu_id, $lang_code ?? (string) GlobalState::contentLocale());
}

/**
 * Returns list of active menus
 *
 * @return array menus list
 */
function fn_get_menus()
{
    $menus = array();
    $_menus = Menu::getList(" AND status = 'A'");
    foreach ($_menus as $menu) {
        $menus[$menu['menu_id']] = $menu['name'];
    }

    return $menus;
}

/**
 * Used in app/schemas/static_data/schema.php
 *
 * @return bool True if menu can be modified, false otherwise
 */
function fn_check_menu_owner()
{
    return true;
}

/**
 * Gets list of menu items for block menu
 *
 * @param mixed $value Default value of current block content item
 * @param array $block Block params
 * @param array $block_scheme Scheme of block
 * @return array List of menu items
 */
function fn_get_menu_items($value, $block, $block_scheme)
{
    $menu_items = array();

    if (!empty($block['content']['menu']) && Menu::getStatus($block['content']['menu']) == 'A') {
        $params = array(
            'section' => 'A',
            'get_params' => true,
            'icon_name' => '',
            'multi_level' => true,
            'use_localization' => true,
            'status' => 'A',
            'generate_levels' => true,
            'request' => array(
                'menu_id' => $block['content']['menu'],
            )
        );

        $menu_items = fn_top_menu_form(fn_get_static_data($params));
        fn_dropdown_appearance_cut_second_third_levels($menu_items, 'subitems', $block['properties']);
    }

    return $menu_items;
}

/**
 * Form top menu
 *
 * @param array $top_menu top menu data from the database
 * @param int $level current menu level
 * @param boolean $active - menu item active flag, returned by reference to set tree branch as active
 * @return array formed top menu
 */
function fn_top_menu_form($top_menu, $level = 0, &$active = NULL)
{
    $_active = false;
    foreach ($top_menu as $k => $v) {
        if (!empty($v['param_3'])) { // get extra items
            list($type, $id, $use_name) = fn_explode(':', $v['param_3']);
            if ($type == 'C') { // categories
                $cats = fn_get_categories_tree($id, true);
                $v['subitems'] = fn_array_merge(fn_top_menu_standardize($cats, 'category_id', 'category', 'subcategories', 'categories.view?category_id='), !empty($v['subitems']) ? $v['subitems'] : array(), false);

                if ($use_name == 'Y' && !empty($id)) {
                    $v['descr'] = fn_get_category_name($id);
                    $v['param'] = 'categories.view?category_id=' . $id;
                }
            } elseif ($type == 'A') { // pages
                $params = array(
                    'from_page_id' => $id,
                    'get_tree' => 'multi_level',
                    'status' => 'A'
                );
                list($pages) = fn_get_pages($params);

                $v['subitems'] = fn_array_merge(fn_top_menu_standardize($pages, 'page_id', 'page', 'subpages', 'pages.view?page_id='), !empty($v['subitems']) ? $v['subitems'] : array(), false);

                if ($use_name == 'Y' && !empty($id)) {
                    $page_data = fn_get_page_data($id);
                    $v['descr'] = $page_data['page'];
                    $v['param'] = !empty($page_data['link']) ? $page_data['link'] : ('pages.view?page_id=' . $id);
                }
            }
        }

        if (!empty($v['param']) && fn_top_menu_is_current_url($v['param'], $v['param_2'])) {
            $top_menu[$k]['active'] = true;
            // Store active value
            $_active = true;
        }

        if (!empty($v['subitems'])) {
            $top_menu[$k]['subitems'] = fn_top_menu_form($v['subitems'], $level + 1, $active);

            // If active status was returned fron children
            if ($active) {
                $top_menu[$k]['active'] = $active;
                // Strore fo return and reset activity status for athother elements on this level
                // Because in one level may be only one active item
                $_active = true;
                $active = false;
            }
        }

        $top_menu[$k]['item'] = $v['descr'];
        $top_menu[$k]['href'] = $v['param'];
        $top_menu[$k]['level'] = $level;

        unset($top_menu[$k]['descr'], $top_menu[$k]['param']);
    }

    $active = $_active;

    return $top_menu;
}

/**
 * Standardize data for usage in top menu
 *
 * @param array $items data to standartize
 * @param string $id_name key with item ID
 * @param string $name key with item name
 * @param string $children_name key with subitems
 * @param string $href_prefix URL prefix
 * @return array standardized data
 */
function fn_top_menu_standardize($items, $id_name, $name, $children_name, $href_prefix)
{
    $result = array();
    foreach ($items as $v) {
        $result[$v[$id_name]] = array(
            'descr' => $v[$name],
            'param' => empty($v['link']) ? $href_prefix . $v[$id_name] : $v['link'],
            'new_window' => isset($v['new_window']) ? $v['new_window'] : 0
        );

        if (!empty($v[$children_name])) {
            $result[$v[$id_name]]['subitems'] = fn_top_menu_standardize($v[$children_name], $id_name, $name, $children_name, $href_prefix, $dir);
        }
    }

    return $result;
}

/**
 * Checks if menu item url is the same with current url
 * @param type $url - menu item url
 * @param type $active_for - menu item active_for parameter (dispatch/mode list)
 * @return boolean - true if item can be marked as active, false - otherwise
 */
function fn_top_menu_is_current_url($url, $active_for = '')
{
    $active = false;
    $controller = Registry::get('runtime.controller');
    $mode = Registry::get('runtime.mode');
    $action = Registry::get('runtime.action');
    $index_script = Registry::get('config.customer_index');

    if (!empty($active_for)) {
        $dispatch = explode('.', $active_for);
        if (isset($dispatch[0]) && $dispatch[0] == $controller) {
            if (isset($dispatch[1]) && $dispatch[1] == $mode) {
                if (!isset($dispatch[2]) || $dispatch[2] == $action || $action == '') {
                    $active = true;
                }
            } else {
                $active = true;
            }
        }
    } else {
        if (strpos($url, $index_script . '?') === false) {
            $url = fn_url($url);
        }

        $params = parse_url($url);

        if (!isset($params['host']) || $params['host'] == $_SERVER['HTTP_HOST']) {
            $current_params = parse_url($_SERVER['REQUEST_URI']);

            if (((!isset($params['path']) && !isset($current_params['path'])) || $params['path'] == $current_params['path']) && ((!isset($params['query']) && !isset($current_params['query'])) || $params['query'] == $current_params['query'])) {
                $active = true;
            }
        }
    }

    return $active;
}

/**
 * Delete links
 *
 * @param array $link_ids Array links identifier
 */
function fn_delete_sitemap_links($link_ids)
{
    if (!empty($link_ids)) {
        db_query("DELETE FROM ?:sitemap_links WHERE link_id IN (?n)", $link_ids);
        db_query("DELETE FROM ?:common_descriptions WHERE object_holder = 'sitemap_links' AND object_id IN (?n)", $link_ids);
    }
}

/**
 * Delete sections
 *
 * @param array $section_ids Array sections identifier
 */
function fn_delete_sitemap_sections($section_ids)
{
    if (!empty($section_ids)) {
        db_query("DELETE FROM ?:sitemap_sections WHERE section_id IN (?n)", $section_ids);
        db_query("DELETE FROM ?:common_descriptions WHERE object_holder = 'sitemap_sections' AND object_id IN (?n)", $section_ids);

        $links = db_get_fields("SELECT link_id FROM ?:sitemap_links WHERE section_id IN (?n)", $section_ids);
        if (!empty($links)) {
                db_query("DELETE FROM ?:sitemap_links WHERE section_id IN (?n)", $section_ids);
                db_query("DELETE FROM ?:common_descriptions WHERE object_holder = 'sitemap_links' AND object_id IN (?n)", $links);
        }
    }
}

/**
 * Get sitemap sections
 *
 * @param int $section_id Section identifier
 * @return array $sections
 */
function fn_get_sitemap_sections($params = array())
{
    $section_fields = array(
        's.*',
        'c.object as section'
    );

    $section_tables = array(
        '?:sitemap_sections as s',
    );

    $section_left_join = array(
        db_quote('?:common_descriptions as c ON c.object_id = s.section_id AND c.object_holder = ?s AND c.lang_code = ?s', 'sitemap_sections', (string) GlobalState::contentLocale()),
    );

    $section_condition = array();
    if (!empty($params['section_id'])) {
        $section_condition[] = db_quote('s.section_id = ?i', $params['section_id']);
    }

    if (Registry::get('runtime.company_id')) {
        $section_condition[] = db_quote('s.company_id = ?i', Registry::get('runtime.company_id'));

    } elseif (!empty($params['company_id'])) {
        $section_condition[] = db_quote('s.company_id = ?i', $params['company_id']);
    }

    $condition = empty($section_condition) ? '' : ' WHERE ' . implode(' AND ', $section_condition);

    $sections = db_get_hash_array('SELECT ' . implode(', ', $section_fields) . ' FROM ' . implode(', ', $section_tables) . ' LEFT JOIN ' . implode(', ', $section_left_join) . $condition . ' ORDER BY position, section', 'section_id');

    return $sections;
}

/**
 * Get sitemap links
 *
 * @param int $section_id Section identifier
 * @return array $links
 */
function fn_get_sitemap_links($section_id)
{
    $links_fields = array(
        'link_id',
        'link_href',
        'section_id',
        'status',
        'position',
        'link_type',
        'description',
        'object as link',
    );

    $links_tables = array(
        '?:sitemap_links',
    );

    $links_left_join = array(
        db_quote("?:common_descriptions ON ?:common_descriptions.object_id = ?:sitemap_links.link_id AND ?:common_descriptions.object_holder = 'sitemap_links' AND ?:common_descriptions.lang_code = ?s", (string) GlobalState::contentLocale()),
    );

    $links_condition = array(
        db_quote('section_id = ?i', $section_id),
    );

    $links = db_get_array('SELECT ' . implode(', ', $links_fields) . ' FROM ' . implode(', ', $links_tables) . ' LEFT JOIN ' . implode(', ', $links_left_join) . ' WHERE ' . implode(' AND ', $links_condition) . ' ORDER BY position, link');

    return $links;
}

/**
 * Create/Update sitemap section
 *
 * @param array $section_data Section update data
 * @param int $section_id Section identifier
 * @return int $section_id
 */
function fn_update_sitemap($section_data, $section_id = 0)
{
    // Add sitemap sections
    if (empty($section_id)) {
        if (Registry::get('runtime.company_id') && !isset($section_data['company_id'])) {
            $section_data['company_id'] = Registry::get('runtime.company_id');
        }

        $section_id = db_query("INSERT INTO ?:sitemap_sections ?e", $section_data);
        if (!empty($section_id)) {

            $_data = array();
            foreach (Languages::getAll() as $lang_code => $_lang_data) {
                $_data[] = array(
                    'object'        => $section_data['section'],
                    'object_id'     => $section_id,
                    'object_holder' => 'sitemap_sections',
                    'lang_code'     => $lang_code
                );
            }
            db_query("INSERT INTO ?:common_descriptions ?m", $_data);

        } else {
            return array(CONTROLLER_STATUS_NO_PAGE);
        }
    } else {
        db_query("UPDATE ?:sitemap_sections SET ?u WHERE section_id = ?i", $section_data, $section_id);
        db_query("UPDATE ?:common_descriptions SET object=?s WHERE object_id = ?i AND lang_code = ?s AND object_holder = 'sitemap_sections'", $section_data['section'], $section_id, (string) GlobalState::contentLocale());
    }

    return $section_id;
}

/**
 * Create/Update sitemap links
 *
 * @param array $links_data Links update data
 * @param int $section_id Section identifier
 */
function fn_update_sitemap_links($links_data, $section_id)
{
    $link_ids = array();
    foreach ($links_data as $link_data) {
        if (!empty($link_data['link'])) {

            if (empty($link_data['link_id'])) {

                $link_data['section_id'] = $section_id;

                $link_id = db_query("INSERT INTO ?:sitemap_links ?e", $link_data);
                $link_ids[] = $link_id;

                if (!empty($link_id)) {
                    $_data = array();
                    foreach (Languages::getAll() as $lang_code => $_lang_data) {
                        $_data[] = array(
                            'object'        => $link_data['link'],
                            'object_id'     => $link_id,
                            'object_holder' => 'sitemap_links',
                            'lang_code'     => $lang_code
                        );
                    }
                    db_query("INSERT INTO ?:common_descriptions ?m", $_data);
                }
            } else {
                $link_data['section_id'] = $section_id;
                $link_ids[] = $link_data['link_id'];

                db_query("UPDATE ?:sitemap_links SET ?u WHERE link_id = ?i", $link_data, $link_data['link_id']);
                db_query("UPDATE ?:common_descriptions SET object=?s WHERE object_id = ?i AND lang_code = ?s AND object_holder = 'sitemap_links'", $link_data['link'], $link_data['link_id'], (string) GlobalState::contentLocale());
            }
        }
    }

    $obsolete_ids = db_get_fields("SELECT link_id FROM ?:sitemap_links WHERE section_id = ?i AND link_id NOT IN (?n)", $section_id, $link_ids);

    if (!empty($obsolete_ids)) {
        fn_delete_sitemap_links($obsolete_ids);
    }
}
