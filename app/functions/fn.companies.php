<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, Ilya M<PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Symfony\Component\Intl\Countries;
use Symfony\Component\Validator\Validation;
use Tygh\Menu;
use Tygh\Navigation\LastView;
use Tygh\Registry;
use Wizacha\AppBundle\Controller\Api\Division\Dto\ApiDivisionSettingsDto;
use Wizacha\Company;
use Wizacha\Cscart\Common;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Commission\Commission;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Company\CompanyStatus;
use Wizacha\Marketplace\Company\Event\CompanyDisabled;
use Wizacha\Marketplace\Company\Event\CompanyPending;
use Wizacha\Marketplace\Company\Event\CompanyRejected;
use Wizacha\Marketplace\Company\LegacyCompanyCache;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonService;
use Wizacha\Marketplace\Division\CompanyDivisionSettings;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Review\ReviewType;
use Wizacha\Marketplace\User\Password;
use Wizacha\Marketplace\User\UserTitle;
use Wizacha\Marketplace\User\UserType;

/**
 * Gets brief company data array: <i>(company_id => company_name)</i>
 *
 * @param array $params Array of search params:
 * <ul>
 *        <li>string status - Status field from the <i>?:companies table</i></li>
 *        <li>string item_ids - Comma separated list of company IDs</li>
 *        <li>int displayed_vendors - Number of companies for displaying. Will be used as LIMIT condition</i>
 * </ul>
 * Global variable <i>$_REQUEST</i> can be passed as argument
 * @return mixed If <i>$params</i> was not empty returns array:
 * <ul>
 *   <li>companies - Hash array of companies <i>(company_id => company)</i></li>
 *   <li>count - Number of returned companies</li>
 * </ul>
 * else returns hash array of companies <i>(company_id => company)</i></li>
 */
function fn_get_short_companies($params = array())
{
    $condition = $limit = $join = $companies = '';

    if (!empty($params['status'])) {
        $condition .= db_quote(" AND ?:companies.status = ?s ", $params['status']);
    }

    if (!empty($params['item_ids'])) {
        $params['item_ids'] = fn_explode(",", $params['item_ids']);
        $condition .= db_quote(" AND ?:companies.company_id IN (?n) ", $params['item_ids']);
    }

    if (!empty($params['displayed_vendors'])) {
        $limit = 'LIMIT ' . $params['displayed_vendors'];
    }

    $condition .= Registry::get('runtime.company_id') ? fn_get_company_condition('?:companies.company_id', true, Registry::get('runtime.company_id')) : '';

    $count = db_get_field("SELECT COUNT(*) FROM ?:companies $join WHERE 1 $condition");

    $_companies = db_get_hash_single_array("SELECT ?:companies.company_id, ?:companies.company FROM ?:companies $join WHERE 1 $condition ORDER BY ?:companies.company $limit", array('company_id', 'company'));

    $companies[0] = Registry::get('settings.Company.company_name');
    $companies = $companies + $_companies;

    $return = array(
        'companies' => $companies,
        'count' => $count,
    );

    if (!empty($params)) {
        unset($return['companies'][0]);

        return array($return);
    }

    return $companies;
}

/**
 * Gets company name by id.
 *
 * @staticvar array $cache_names Static cache for company names
 * @param int $company_id Company id
 * @param string $zero_company_name_lang_var If <i>$company_id</i> is empty, this name will be returned (used in MVE for pages and shippings)
 * @return mixed Company name string in case company name for the given id is found, <i>null</i> otherwise
 */
function fn_get_company_name($company_id, $zero_company_name_lang_var = '')
{
    static $cache_names = array();

    if (empty($company_id)) {
        return __($zero_company_name_lang_var);
    }

    if (!isset($cache_names[$company_id])) {
        if (Registry::get('runtime.company_id') === $company_id) {
            $cache_names[$company_id] = Registry::get('runtime.company_data.company');
        } else {
            $cache_names[$company_id] = db_get_field("SELECT company FROM ?:companies WHERE company_id = ?i", $company_id);
        }
    }

    return $cache_names[$company_id];
}

/**
 * Gets company data array
 *
 * @param array $params Array of search params:
 * <ul>
 *		  <li>string company - Name of company</li>
 *		  <li>string status - Status of company</li>
 *		  <li>string email - Email of company</li>
 *		  <li>string address - Address of company</li>
 *		  <li>string zipcode - Zipcode of company</li>
 *		  <li>string country - 2-letters country code of company country</li>
 *		  <li>string state - State code of company</li>
 *		  <li>string city - City of company</li>
 *		  <li>string phone - Phone of company</li>
 *		  <li>string url - URL address of company</li>
 *		  <li>string fax - Fax number of company</li>
 *		  <li>mixed company_id - Company ID, array with company IDs or comma-separated list of company IDs.
 * If defined, data will be returned only for companies with such company IDs.</li>
 *		  <li>int exclude_company_id - Company ID, if defined,
 * result array will not include the data for company with such company ID.</li>
 *		  <li>int page - First page to displaying list of companies (if <i>$items_per_page</i> it not empty.</li>
 *		  <li>string sort_order - <i>ASC</i> or <i>DESC</i>: database query sorting order</li>
 *		  <li>string sort_by - One or list of database fields for sorting.</li>
 * </ul>
 * @param array $auth Array of user authentication data (e.g. uid, etc.)
 * @param int $items_per_page
 * @param string $lang_code 2-letter language code (e.g. 'en', 'ru', etc.)
 * @return array Array:
 * <ul>
 *		<li>0 - First element is array with companies data.</li>
 *		<li>1 - is possibly modified array with searh params (<i>$params</i>).</li>
 * </ul>
 */
function fn_get_companies($params, &$auth, $items_per_page = 0, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    // Init filter
    $_view = 'companies';
    $params = LastView::instance()->update($_view, $params);

    // Set default values to input params
    $default_params = array (
        'page' => 1,
        'items_per_page' => $items_per_page
    );

    $params = array_merge($default_params, $params);

    // Define fields that should be retrieved
    $fields = array (
        '?:companies.company_id',
        '?:companies.lang_code',
        '?:companies.email',
        '?:companies.company',
        '?:companies.timestamp',
        '?:companies.status',
    );

    // Define sort fields
    $sortings = array (
        'id' => '?:companies.company_id',
        'company' => '?:companies.company',
        'email' => '?:companies.email',
        'date' => '?:companies.timestamp',
        'status' => '?:companies.status',
    );

    $condition = $join = $group = '';

    $condition .= fn_get_company_condition('?:companies.company_id');

    $group .= " GROUP BY ?:companies.company_id";

    if (isset($params['company']) && fn_string_not_empty($params['company'])) {
        $condition .= db_quote(" AND ?:companies.company LIKE ?l", "%".trim($params['company'])."%");
    }

    if (!empty($params['status'])) {
        if (is_array($params['status'])) {
            $condition .= db_quote(" AND ?:companies.status IN (?a)", $params['status']);
        } else {
            $condition .= db_quote(" AND ?:companies.status = ?s", $params['status']);
        }
    }

    if (isset($params['email']) && fn_string_not_empty($params['email'])) {
        $condition .= db_quote(" AND ?:companies.email LIKE ?l", "%".trim($params['email'])."%");
    }

    if (isset($params['address']) && fn_string_not_empty($params['address'])) {
        $condition .= db_quote(" AND ?:companies.address LIKE ?l", "%".trim($params['address'])."%");
    }

    if (isset($params['zipcode']) && fn_string_not_empty($params['zipcode'])) {
        $condition .= db_quote(" AND ?:companies.zipcode LIKE ?l", "%".trim($params['zipcode'])."%");
    }

    if (!empty($params['country'])) {
        $condition .= db_quote(" AND ?:companies.country = ?s", $params['country']);
    }

    if (isset($params['state']) && fn_string_not_empty($params['state'])) {
        $condition .= db_quote(" AND ?:companies.state LIKE ?l", "%".trim($params['state'])."%");
    }

    if (isset($params['city']) && fn_string_not_empty($params['city'])) {
        $condition .= db_quote(" AND ?:companies.city LIKE ?l", "%".trim($params['city'])."%");
    }

    if (isset($params['phone']) && fn_string_not_empty($params['phone'])) {
        $condition .= db_quote(" AND ?:companies.phone LIKE ?l", "%".trim($params['phone'])."%");
    }

    if (isset($params['url']) && fn_string_not_empty($params['url'])) {
        $condition .= db_quote(" AND ?:companies.url LIKE ?l", "%".trim($params['url'])."%");
    }

    if (isset($params['fax']) && fn_string_not_empty($params['fax'])) {
        $condition .= db_quote(" AND ?:companies.fax LIKE ?l", "%".trim($params['fax'])."%");
    }

    if (!empty($params['company_id'])) {
        $condition .= db_quote(' AND ?:companies.company_id IN (?n)', $params['company_id']);
    }

    if (!empty($params['exclude_company_id'])) {
        $condition .= db_quote(' AND ?:companies.company_id != ?i', $params['exclude_company_id']);
    }

    if (!empty($params['sort_by']) && $params['sort_by'] == 'rating') {
        $fields[] = 'avg(?:discussion_rating.rating_value) AS rating';
        $fields[] = "CONCAT(?:companies.company_id, '_', IF (?:discussion_rating.thread_id, ?:discussion_rating.thread_id, '0')) AS company_thread_ids";
        $join .= db_quote(" LEFT JOIN ?:discussion ON ?:discussion.object_id = CAST(?:companies.company_id AS CHAR) AND ?:discussion.object_type = 'M'");
        $join .= db_quote(" LEFT JOIN ?:discussion_rating ON ?:discussion.thread_id=?:discussion_rating.thread_id");
        $join .= db_quote(" LEFT JOIN ?:discussion_posts ON ?:discussion_posts.post_id=?:discussion_rating.post_id AND ?:discussion_posts.status = 'A'");
        $group = 'GROUP BY company_thread_ids';
        $sortings['rating'] = 'rating';
    }
    if (!empty($params['get_description'])) {
        $fields[] = '?:company_descriptions.company_description';
        $join .= db_quote(' LEFT JOIN ?:company_descriptions ON ?:company_descriptions.company_id = ?:companies.company_id AND ?:company_descriptions.lang_code = ?s ', $lang_code);
    }
    $fields[] = '?:seo_names.name as seo_name';
    $join .= db_quote(
        " LEFT JOIN ?:seo_names ON ?:seo_names.object_id = CAST(?:companies.company_id AS CHAR) ?p", fn_get_seo_join_condition('m')
    );

    $sorting = db_sort($params, $sortings, 'company', 'asc');

    // Paginate search results
    $limit = '';
    if (!empty($params['items_per_page'])) {
        $params['total_items'] = db_get_field("SELECT COUNT(DISTINCT(?:companies.company_id)) FROM ?:companies $join WHERE 1 $condition");
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    $companies = db_get_array("SELECT " . implode(', ', $fields) . " FROM ?:companies $join WHERE 1 $condition $group $sorting $limit");

    return array($companies, $params);
}

function fn_company_products_check($product_ids, $notify = false)
{
    if (!empty($product_ids)) {
        $c = db_get_field("SELECT count(*) FROM ?:products WHERE product_id IN (?n) ?p", $product_ids, fn_get_company_condition('?:products.company_id'));
        if (count((array) $product_ids) == $c) {
            return true;
        } else {
            if ($notify) {
                fn_company_access_denied_notification();
            }

            return false;
        }
    }

    return true;
}

function fn_company_access_denied_notification()
{
    fn_set_notification('W', __('warning'), __('access_denied'), '', 'company_access_denied');
}

/**
 * Gets part of SQL-query with codition for company_id field.
 *
 * @staticvar array $sharing_schema Local static cache for sharing schema
 * @param string $db_field Field name (usually table_name.company_id)
 * @param bool $add_and Include or not AND keyword berofe condition.
 * @param mixed $company_id Company ID for using in SQL condition.
 * @param bool $show_admin Include or not company_id == 0 in condition (used in the MultiVendor Edition)
 * @param bool $force_condition_for_area_c Used in the MultiVendor Edition. By default, SQL codition should be empty in the customer area. But in some cases,
 * this condition should be enabled in the customer area. If <i>$force_condition_for_area_c</i> is set, condtion will be formed for the customer area.
 * @return string Part of SQL query with company ID condition
 */
function fn_get_company_condition($db_field = 'company_id', $add_and = true, $company_id = '', $show_admin = false, $force_condition_for_area_c = false)
{
    if ($company_id === '') {
        $company_id = Registry::ifGet('runtime.company_id', '');
    }

    $skip_cond = (AREA == 'C' && !$force_condition_for_area_c);

    if (!$company_id || $skip_cond) {
        $cond = '';
    } else {
        $cond = $add_and ? ' AND' : '';
        // FIXME 2tl show admin
        if ($show_admin && $company_id) {
            $cond .= " $db_field IN (0, $company_id)";
        } else {
            $cond .= " $db_field = $company_id";
        }
    }

    return $cond;
}

/**
 * Gets company data by it ID
 *
 * @staticvar array $company_data_cache Array with cached companies data
 * @param int $company_id Company ID
 * @param string $lang_code 2-letter language code (e.g. 'en', 'ru', etc.)
 * @param array $extra Array with extra parameters
 * @return boolean|array with company data
 */
function fn_get_company_data($company_id, $lang_code = null, $extra = array())
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $cache = container()->get(LegacyCompanyCache::class);

    if (empty($company_id)) {
        return false;
    }

    $cache_key = md5($company_id . $lang_code . serialize($extra));

    if (empty($extra['skip_cache'])) {
        $result = $cache->get($cache_key);
        if ($result !== null) {
            return $result;
        }
    }

    $fields = [
        'companies.*',
        'company_descriptions.company_description',
        'company_descriptions.company_terms'
    ];

    $join = '';

    $join .= db_quote(
        ' LEFT JOIN ?:company_descriptions AS company_descriptions'
        . ' ON company_descriptions.company_id = companies.company_id'
        . ' AND company_descriptions.lang_code = ?s',
        $lang_code
    );

    $condition = fn_get_company_condition('companies.company_id');

    $fields[] = '?:seo_names.name as seo_name';
    $join .= db_quote(
        " LEFT JOIN ?:seo_names ON ?:seo_names.object_id = ?s ?p",
        $company_id, fn_get_seo_join_condition('m')
    );

    $company_data = db_get_row(
        'SELECT ' . implode(', ', $fields) . ' FROM ?:companies AS companies ?p'
        . ' WHERE companies.company_id = ?i ?p',
        $join,
        $company_id,
        $condition
    );

    if ($company_data) {
        $company_data['category_ids'] = !empty($company_data['categories']) ? explode(',', $company_data['categories']) : array();
        $company_data['shippings_ids'] = !empty($company_data['shippings']) ? explode(',', $company_data['shippings']) : array();
        $company_data['countries_list'] = !empty($company_data['countries_list']) ? explode(',', $company_data['countries_list']) : array();
        $company_data['main_pair'] = fn_get_image_pairs($company_data['company_id'], 'company', 'M');
    }

    if (empty($company_data['seo_name']) && !empty($company_id)) {
        $company_data['seo_name'] = fn_seo_get_name('m', $company_id, '', null, $lang_code);
    }

    $to_add = db_get_array(
        "SELECT description, object_holder
        FROM ?:common_descriptions
        WHERE object_id = ?i AND lang_code = ?s AND object_holder in (?a)",
        $company_id,
        $lang_code,
        ['w_company_meta_description', 'w_company_meta_keywords', 'w_company_meta_title']
    );
    foreach ($to_add as $element) {
        $company_data[$element['object_holder']] = $element['description'];
    }
    if (isset($company_data['w_extras'])) {
        $company_data['w_extras'] = unserialize($company_data['w_extras']);
    }

    $company_data['extra'] = !empty($company_data['extra']) ? (array) unserialize($company_data['extra']) : [];

    if (empty($extra['skip_cache'])) {
        $cache->set($cache_key, $company_data);
    }

    return $company_data;
}

/**
 * Gets object's company ID value for given object from thegiven table.
 * Function checks is some object has the given company ID.
 *
 * @param string $table Table name
 * @param string $field Field name
 * @param mixed $field_value Value of given field
 * @param mixed $company_id Company ID for additional condition.
 * @return mixed Company ID or false, if check fails.
 */
function fn_get_company_id($table, $field, $field_value, $company_id = '')
{
    $condition = ($company_id !== '') ? db_quote(' AND company_id = ?i ', $company_id) : '';

    $id = db_get_field("SELECT company_id FROM ?:$table WHERE $field = ?s $condition", $field_value);

    return ($id !== NULL) ? $id : false;
}

/**
 * Gets company ID for the given company name
 *
 * @staticvar array $companies Little static cache for company ids
 * @param string $company_name Company name
 * @return integer Company ID or null, if company name was not found.
 */
function fn_get_company_id_by_name($company_name, $use_cache = true)
{
    static $companies = array();

    if (!empty($company_name)) {
        if (empty($companies[md5($company_name)]) || !$use_cache) {

            $condition = db_quote(' AND company = ?s', $company_name);

            $companies[md5($company_name)] = db_get_field("SELECT company_id FROM ?:companies WHERE 1 $condition");
        }

        return $companies[md5($company_name)];
    }

    return false;
}

function fn_get_available_company_ids($company_ids = array())
{
    $condition = '';
    if ($company_ids) {
        $condition = db_quote(' AND company_id IN (?a)', $company_ids);
    }

    return db_get_fields("SELECT company_id FROM ?:companies WHERE 1 ?p AND status IN ('A', 'P', 'N')", $condition);
}

function fn_check_company_id($table, $key, $key_id, $company_id = '')
{
    $registry = \Wizacha\Registry::defaultInstance();
    if ($company_id === '') {
        $company_id = Company::runtimeID($registry->get([\Wizacha\Config::REG_AREA]), $_SESSION, $registry);
    }

    if (!$company_id) {
        return 'A' == $registry->get([\Wizacha\Config::REG_AREA]);
    }

    $id = db_get_field("SELECT $key FROM ?:$table WHERE $key = ?i AND company_id = ?i", $key_id, $company_id);

    return (!empty($id)) ? true : false;
}

/**
 * Set company_id to actual company_id
 *
 * @param mixed $data Array with data
 */
function fn_set_company_id(&$data, $key_name = 'company_id', $only_defined = false)
{
    if (Registry::get('runtime.company_id')) {
        $data[$key_name] = Registry::get('runtime.company_id');
    } elseif (!isset($data[$key_name]) && !$only_defined) {
        $data[$key_name] = 0;
    }
}

function fn_get_companies_shipping_ids($company_id)
{
    return explode(',', db_get_field("SELECT shippings FROM ?:companies WHERE company_id = ?i", $company_id));
}

function fn_update_company($company_data, $company_id = 0, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    fn_vendor_data_premoderation_update_company_pre($company_data, $company_id, $lang_code);
    if (isset($company_data['w_extras'])) {
        $company_data['w_extras'] = serialize($company_data['w_extras']);
    }

    $company_data['add_extra'] = array_filter($company_data['add_extra'] ?? [], function ($extra_field) {
        return isset($extra_field['name'], $extra_field['value']) && $extra_field['name'] !== '' && $extra_field['value'] !== '';
    });
    if (!empty($company_data['add_extra'])) {
        $existingExtra = $company_data['extra'] ?? unserialize((string) db_get_field('SELECT extra FROM cscart_companies WHERE company_id = ?i', $company_id));
        if (!is_array($existingExtra)) {
            $existingExtra = [];
        }

        $extra_names = array_column($company_data['add_extra'], 'name');
        $extra_values = array_column($company_data['add_extra'], 'value');
        $extra_new = array_combine($extra_names, $extra_values);

        // array_merge replace numeric keys.
        $company_data['extra'] = $existingExtra + $extra_new;
    }
    if (\array_key_exists('corporate_name', $company_data) === false || \strlen($company_data['corporate_name']) === 0) {
        $company_data['corporate_name'] = $company_data['company'];
    }

    if (\array_key_exists('url', $company_data) === true) {
        $company_data['url'] = \trim($company_data['url']);
    }

    if (isset($company_data['extra'])  || isset($company_data['extra_force_delete'])) {
        $company_data['extra'] = serialize($company_data['extra'] ?? []);
        unset($company_data['extra_force_delete']);
    }

    if (\array_key_exists('invoicing_disabled', $company_data) === true
        && container()->get('marketplace.invoicing_settings_service')->getMarketplaceInvoicingDisplayed() === false
    ) {
        $companyService = container()->get('marketplace.company_service');
        $invoicingSettingsService = container()->get('marketplace.invoicing_settings_service');
        $invoicingBeforeEdit = $companyService->isInvoicingDisabled($company_id);
        if ($companyService->canEditInvoicingDisplayed($company_id, ACCOUNT_TYPE === 'admin') === true) {
            $company_data['invoicing_disabled'] = (int) $company_data['invoicing_disabled'];
            if ($company_data['invoicing_disabled'] === 1) {
                $invoicingSettingsService->resetOrdersAfterDisplayedInvoicing($company_id);
            }
            //check if is admin and if the field is edited
            if (ACCOUNT_TYPE === 'admin' &&  $invoicingBeforeEdit !== (bool) $company_data['invoicing_disabled']) {
                $company_data['invoicing_disabled_by_admin'] = $company_data['invoicing_disabled'];
            }
        }
    }

    if (Registry::get('runtime.company_id')) {
        unset($company_data['commission_type'], $company_data['commission_percent'], $company_data['commission_fixed'], $company_data['categories'], $company_data['shippings'], $company_data['iban'], $company_data['bic']);
    }

    unset($company_data['company_id']);
    $_data = $company_data;

    // Check if company with same email already exists
    $is_exist = db_get_field("SELECT email FROM ?:companies WHERE company_id != ?i AND email = ?s", $company_id, $_data['email']);
    if (!empty($is_exist)) {
        $_text = 'error_vendor_exists';
        fn_set_notification('E', __('error'), __($_text));

        return false;
    }

    if (isset($company_data['shippings'])) {
        $_data['shippings'] = empty($company_data['shippings']) ? '' : fn_create_set($company_data['shippings']);
    }

    if (!empty($_data['countries_list'])) {
        $_data['countries_list'] = implode(',', $_data['countries_list']);
    } else {
        $_data['countries_list'] = '';
    }

    if (isset($_data['country']) && empty($_data['country'])) {
        unset($_data['country']);
    }

    if (isset($_data['country']) && Countries::getName((string) $_data['country']) === null) {
        fn_set_notification('E', __('error'), __('error_country_code'));

        return false;
    }

    if (container()->get('marketplace.payment.mangopay')->isConfigured() === true
        && \array_key_exists('status', $_data) === true
        && $_data['status'] === CompanyStatus::PENDING()->getValue()
        && \array_key_exists('w_siret_number', $_data) === true
        && container()->get(CompanyService::class)->isSiretNumberValid($_data['w_siret_number']) === false
    ) {
        fn_set_notification('E', __('error'), __('error_siret_number'));

        return false;
    }

    if (isset($_data['naf_code']) && strlen($_data['naf_code']) > 6) {
        fn_set_notification('E', __('error'), __('error_naf_code'));

        return false;
    }

    // when feature.iban_validation is disabled, we don't want check the validity of Iban and Bic because some psp
    // (stripe by example) use incorrect IBAN on sandbox environment.
    $checkIbanAndBicValues = container()->getParameter('feature.iban_validation');

    $ibanBicChanged = false;
    if (!empty($_data['iban'])) {
        $_data['iban'] = str_replace(' ', '', strtoupper($_data['iban']));

        if ($checkIbanAndBicValues) {
            $ibanErrors = Validation::createValidator()->validate($_data['iban'], [new \Symfony\Component\Validator\Constraints\Iban()]);
            if (count($ibanErrors) !== 0) {
                fn_set_notification('E', __('error'), __('error_iban'));

                return false;
            }
        }

        // Iban is modified if the company is new and enabled or if the value changed
        $ibanBicChanged = (empty($company_id) && $_data['status'] === \Wizacha\Status::ENABLED) || $_data['iban'] !== db_get_field("SELECT iban FROM ?:companies WHERE company_id = ?i", $company_id);
    }

    if (!empty($_data['bic'])) {
        $_data['bic'] = str_replace(' ', '', strtoupper($_data['bic']));

        if ($checkIbanAndBicValues) {
            $bicErrors = Validation::createValidator()->validate($_data['bic'], [new \Symfony\Component\Validator\Constraints\Bic()]);
            if (count($bicErrors) !== 0) {
                fn_set_notification('E', __('error'), __('error_bic'));

                return false;
            }
        }

        // Bic is modified if the company is new and enabled or if the value changed
        // Check only if the value is not already flagged as modified
        if (!$ibanBicChanged) {
            $ibanBicChanged = (empty($company_id) && $_data['status'] === \Wizacha\Status::ENABLED) || $_data['bic'] !== db_get_field("SELECT bic FROM ?:companies WHERE company_id = ?i", $company_id);
        }
    }

    $purifierService = container()->get('purifier.default');

    if (true === \array_key_exists('company_terms', $_data) && $_data['company_terms'] !== null) {
        // Make sure that terms length limit is not exceeded, otherwise it'll be truncated by MySQL,
        // and our old ass TinyMCE version will get into an infinite loop.
        if (\strlen($_data['company_terms']) > Company::MAX_LENGTH_TERMS) {
            fn_set_notification('E', __('error'), __('error_company_terms'));
            $_data['company_terms'] = '';
        } else {
            // Sanitize company terms
            $_data['company_terms'] = \trim($purifierService->purify($_data['company_terms']));
        }
    }

    // Sanitize company description
    if (true === \array_key_exists('company_description', $_data) && $_data['company_description'] !== null) {
        $_data['company_description'] = \trim($purifierService->purify($_data['company_description']));
    }

    // add new company
    if (empty($company_id)) {
        $needProjection = false;

        // company title can't be empty
        if (empty($company_data['company'])) {
            fn_set_notification('E', __('error'), __('error_empty_company_name'));

            return false;
        }

        // company email can't be empty/invalid
        if (!filter_var($company_data['email'], FILTER_VALIDATE_EMAIL)) {
            fn_set_notification('E', __('error'), __('error_validator_email'));

            return false;
        }

        $_data['timestamp'] = TIME;

        // Status
        if (
            isset($_data['status']) === false
            || \Wizacha\Company::isValidStatus($_data['status']) === false
        ) {
            $_data['status'] = \Wizacha\Company::STATUS_NEW;
        }

        // Par défaut, l'adresse de retour de produits est l'adresse de la companie
        if (empty($_data['w_rma_address']) && empty($_data['w_rma_zipcode']) && empty($_data['w_rma_city'])) {
            $_data['w_rma_address'] = $_data['address'] ?? '';
            $_data['w_rma_zipcode'] = $_data['zipcode'] ?? '';
            $_data['w_rma_city'] = $_data['city'] ?? '';
            $_data['w_rma_country'] = $_data['country'] ?? '';
        }

        // Application de la commission par défaut
        $commissionService = container()->get('marketplace.commission.commission_service');
        $addCompanyCommission = true;
        $defaultCommission = $commissionService->getDefaultCommission();

        if ($_data['commission_percent'] === null && $_data['commission_fixed'] === null) {
            // Old system
            $_data['commission_percent'] = $commissionService->getDefaultCommissionPercent();
            $_data['commission_fixed'] = $commissionService->getDefaultCommissionFixed()->getConvertedAmount();
            $_data['commission_maximum'] = $commissionService->getDefaultCommissionMaximum();

            // New system
            $commissionPercent = $defaultCommission->getPercentAmount();
            $commissionFixed = $defaultCommission->getFixAmount();
            $commissionMaximum = $defaultCommission->getMaximumAmount();

            if (\floatval($commissionPercent) === 0. && \floatval($commissionFixed) === 0.) {
                $addCompanyCommission = false;
            }
        } else {
            $_data['commission_percent'] = $_data['commission_percent'] === null ? 0 : $_data['commission_percent'];
            $_data['commission_fixed'] = $_data['commission_fixed'] === null ? 0 : $_data['commission_fixed'];

            $commissionPercent = $_data['commission_percent'];
            $commissionFixed = $_data['commission_fixed'];
            $commissionMaximum = $_data['commission_maximum'] ? \floatval($_data['commission_maximum']) : null;

            if (($commissionPercent === '' && $commissionFixed === '')
                || ($defaultCommission->getPercentAmount() === 0.
                    && $defaultCommission->getFixAmount() === 0.
                    && \floatval($commissionPercent) === 0.
                    && \floatval($commissionFixed) === 0.)
            ) {
                $addCompanyCommission = false;
            }
        }

        $company_id = db_query("INSERT INTO ?:companies ?e", $_data);

        if (empty($company_id)) {
            return false;
        }

        // Assurer que nous n'avons pas des commissions enregistrées avec company_id
        $commissionService->deleteCommissionsByCompanyId($company_id);

        if ($addCompanyCommission) {
            $commissionService->saveCommission(
                new Commission(
                    '',
                    $company_id,
                    null,
                    \floatval($commissionPercent),
                    \floatval($commissionFixed),
                    $commissionMaximum
                )
            );
        }

        $_data['company_id'] = $company_id;

        if (\is_array($_data['commission_data'])) {
            $commissionsByCategory = $_data['commission_data'];
        } else {
            $commissionsByCategory = $commissionService->getDefaultCategoryCommissions();
        }

        if (\is_array($commissionsByCategory) && count($commissionsByCategory) > 0) {
            foreach ($commissionsByCategory as $commissionBycategory) {
                if ($commissionBycategory instanceof Commission) {
                    $commissionService->saveCommission(new Commission(
                        '',
                        $company_id,
                        $commissionBycategory->getCategoryId(),
                        $commissionBycategory->getPercentAmount(),
                        $commissionBycategory->getFixAmount(),
                        $commissionBycategory->getMaximumAmount()
                    ));
                } elseif (\intval($commissionBycategory['category_id']) !== 0) {
                    $commissionService->saveCommission(new Commission(
                        '',
                        $company_id,
                        \intval($commissionBycategory['category_id']),
                        \floatval($commissionBycategory['percent_amount'] ?? 0),
                        \floatval($commissionBycategory['fix_amount'] ?? 0),
                        $commissionBycategory['maximum_amount'] ? \floatval($commissionBycategory['maximum_amount']) : null
                    ));
                }
            }
        }

        // Default divisions settings
        if (true === container()->getParameter('feature.available_offers')
            && false === \array_key_exists('divisions', $_data)
        ) {
            $_data['divisions'] = [
                'included' => [CompanyDivisionSettings::DEFAULT_DIVISION],
                'excluded' => [],
            ];
        }

        \Tygh\Languages\Helper::insertTranslations('company_descriptions', $lang_code, $_data);

        $action = 'add';

    // update company information
    } else {
        // don't project everything
        $needProjectionClosure = function (array $from, array $to): bool {
            /**
             * Company data exposed in readmodel/algolia
             */
            $map = [
                'company',
                'corporate_name',
                'status'
            ];

            foreach ($map as $key) {
                if ($from[$key] != $to[$key]) {
                    return true;
                }
            }

            return false;
        };

        $needProjection = $needProjectionClosure(
            fn_get_company_data($company_id, null, ['skip_cache' => true]),
            $_data
        );

        if (isset($company_data['company']) && empty($company_data['company'])) {
            unset($company_data['company']);
        }

        // On ne doit pas pouvoir supprimer l'email d'une 'company', donc :

        // Si l'email est présent mais mal formaté : erreur
        if (!empty($company_data['email']) && !filter_var($company_data['email'], FILTER_VALIDATE_EMAIL)) {
            fn_set_notification('E', __('error'), __('error_validator_email'));

            return false;
        }

        if (
            true === container()->get('marketplace.payment.hipay')->isConfigured()
            && ($company_data['iban'] === '' || $company_data['bic'] === '')
        ) {
            fn_set_notification('E', __('error'), __('iban_or_bic_empty'));

            return false;
        }

        // Si l'email est présent mais vide : on supprime l'entrée pour ne pas écraser l'information en base
        if (isset($company_data['email']) && empty($company_data['email'])) {
            unset($company_data['email']);
        }

        if (!empty($_data['status'])) {
            $status_from = db_get_field("SELECT status FROM ?:companies WHERE company_id = ?i", $company_id);
        }
        db_query("UPDATE ?:companies SET ?u WHERE company_id = ?i", $_data, $company_id);

        if (isset($status_from) && $status_from != $_data['status']) {
            fn_companies_change_status($company_id, $_data['status'], '', $status_from, true);
        }

        // unset data lang code as it determines company main language not description language
        unset($_data['lang_code']);
        db_query(
            "UPDATE ?:company_descriptions SET ?u WHERE company_id = ?i AND lang_code = ?s",
            $_data, $company_id, $lang_code
        );

        $action = 'update';
    }

    $object_ids = array();

    if ($action == 'add') {

        $layout_id = 0;

        $types = fn_get_logo_types(true);

        foreach ($types as $type => $data) {
            $object_ids[$type] = fn_create_logo(array(
                'type' => $type,
                'layout_id' => !empty($data['for_layout']) ? $layout_id : 0
            ), $company_id);
        }
    }

    // @deprecated Logos have been removed on companies
    fn_attach_image_pairs('logotypes', 'logos', 0, $lang_code, $object_ids);

    // Création automatique du thread de discussion lié à la company
    $company_data['discussion_type'] = ReviewType::RATE_AND_COMMENT;
    fn_discussion_update_type($company_data, $company_id, 'M');

    fn_seo_update_object($company_data, $company_id, 'm', $lang_code);
    fn_wizacha_backend_design_update_company($company_data, $company_id, $lang_code, $action);

    if ($ibanBicChanged) {
        \Wizacha\Events\Config::dispatch(
            CompanyEvents::IBAN_BIC_UPDATED,
            IterableEvent::fromElement($company_id)
        );
    }

    \Wizacha\Events\Config::dispatch(
        CompanyEvents::UPDATED,
        IterableEvent::fromElement(
            $company_id,
            ['needProjection' => $needProjection]
        )
    );

    // Save divisions settings
    if (true === container()->getParameter('feature.available_offers')
        && true === \is_array($_data['divisions'])
    ) {
        container()
            ->get('marketplace.divisions_settings.service')
            ->updateCompanyDivisionSettings(
                $company_id,
                new ApiDivisionSettingsDto(
                    $_data['divisions']['included'],
                    $_data['divisions']['excluded']
                )
            )
        ;
    }

    return $company_id;
}

function fn_set_request_user_id(int $companyId, int $userId): void
{
    db_query("UPDATE ?:companies SET request_user_id = ?i WHERE company_id = ?i", $userId, $companyId);
}

function fn_companies_filter_company_product_categories(&$request, &$product_data)
{
    if (Registry::get('runtime.company_id')) {
        $company_id = Registry::get('runtime.company_id');
    } elseif (isset($product_data['company_id'])) {
        $company_id = $product_data['company_id'];
    } elseif (!empty($product_data['product_id'])) {
        $company_id = db_get_field('SELECT company_id FROM ?:products WHERE product_id = ?i', $product_data['product_id']);
    } else {
        return false;
    }

    $company_data = fn_get_company_data($company_id);

    if (empty($company_data['category_ids'])) {
        // all categories are allowed
        return true;
    }

    if (!empty($request['category_id']) && !in_array($request['category_id'], $company_data['category_ids'])) {
        unset($request['category_id']);
        $changed = true;
    }
    if (!empty($product_data['main_category']) && !in_array($product_data['main_category'], $company_data['category_ids'])) {
        unset($product_data['main_category']);
        $changed = true;
    }
    if (!empty($product_data['category_ids'])) {
        $categories = explode(',', $product_data['category_ids']);
        foreach ($categories as $k => $v) {
            if (!in_array($v, $company_data['category_ids'])) {
                unset($categories[$k]);
                $changed = true;
            }
        }
        $product_data['category_ids'] = implode(',', $categories);
    }

    return empty($changed);
}

function fn_delete_company($company_id)
{
    if (empty($company_id)) {
        return false;
    }

    $companyPersonService = container()->get(CompanyPersonService::class);

    if (\count($companyPersonService->getByCompanyId($company_id)) > 0) {
        fn_set_notification('W', __('warning'), __('unable_delete_vendor_persons_exists'));

        return false;
    }

    // Do not delete vendor if there're any orders associated with this company
    if (db_get_field("SELECT COUNT(*) FROM ?:orders WHERE company_id = ?i", $company_id)) {
        fn_set_notification('W', __('warning'), __('unable_delete_vendor_orders_exists'));

        return false;
    }

    db_query("DELETE FROM ?:companies WHERE company_id = ?i", $company_id);

    // deleting company admins
    $user_ids = db_get_fields("SELECT user_id FROM ?:users WHERE company_id = ?i AND user_type = 'V'", $company_id);
    foreach ($user_ids as $user_id) {
        fn_delete_user($user_id); // do not show notifications
    }

    $_SESSION['notifications'] = [];

    // deleting products
    $product_ids = db_get_fields("SELECT product_id FROM ?:products WHERE company_id = ?i", $company_id);
    foreach ($product_ids as $product_id) {
        try {
            fn_delete_product($product_id, true);
        } catch (Forbidden $e) {
            fn_set_notification('W', __('warning'), __('access_denied'));
        }
    }

    // deleting shipping
    $shipping_ids = db_get_fields("SELECT shipping_id FROM ?:shippings WHERE company_id = ?i", $company_id);
    foreach ($shipping_ids as $shipping_id) {
        fn_delete_shipping($shipping_id);
    }

    $_menus = Menu::getList(db_quote(" AND company_id = ?i" , $company_id));
    foreach ($_menus as $menu) {
        Menu::delete($menu['menu_id']);
    }


    db_query("DELETE FROM ?:company_descriptions WHERE company_id = ?i", $company_id);

    // deleting product_options
    $option_ids = db_get_fields("SELECT option_id FROM ?:product_options WHERE company_id = ?i", $company_id);
    foreach ($option_ids as $option_id) {
        fn_delete_product_option($option_id);
    }

    // deleting pages
    $page_ids = db_get_fields("SELECT page_id FROM ?:pages WHERE company_id = ?i", $company_id);
    foreach ($page_ids as $page_id) {
        fn_delete_page($page_id);
    }

    // deleting features
    $feature_ids = db_get_fields("SELECT feature_id FROM ?:product_features WHERE company_id = ?i", $company_id);
    foreach ($feature_ids as $feature_id) {
        fn_delete_feature($feature_id);
    }

    // @deprecated Logos have been removed on companies
    // deleting logos
    $types = fn_get_logo_types();
    foreach ($types as $type => $data) {
        fn_delete_logo($type, $company_id);
    }

    $payment_ids = db_get_fields('SELECT payment_id FROM ?:payments WHERE company_id = ?i', $company_id);
    foreach ($payment_ids as $payment_id) {
        fn_delete_payment($payment_id);
    }

    // Delete sitemap sections and links
    $params = array(
        'company_id' => $company_id,
    );
    $section_ids = fn_get_sitemap_sections($params);
    fn_delete_sitemap_sections(array_keys($section_ids));

    db_query(
        "DELETE FROM ?:common_descriptions
        WHERE object_id = ?i AND object_holder in (?a)",
        $company_id,
        ['w_company_meta_description', 'w_company_meta_keywords']
    );
    fn_delete_discussion($company_id, 'M');

    container()->get('marketplace.seo.seo_service')->removeSlug(\Wizacha\Marketplace\Seo\SlugTargetType::COMPANY(), $company_id);

    \Wizacha\Events\Config::dispatch(
        CompanyEvents::DELETED,
        IterableEvent::fromElement($company_id)
    );

    return true;
}

/**
 * Function returns address of company and emails of company' departments.
 *
 * @param integer $company_id ID of company
 * @param string $lang_code Language of retrieving data. If null, lang_code of company will be used.
 * @return array Company address, emails and lang_code.
 */
function fn_get_company_placement_info($company_id, $lang_code = null)
{
    $default_company_placement_info = Registry::get('settings.Company');

    if (empty($company_id)) {
        $company_placement_info = $default_company_placement_info;
        $company_placement_info['lang_code'] = $lang_code ?? (string) GlobalState::interfaceLocale();
    } else {
        $company = fn_get_company_data($company_id, $lang_code ?? (string) GlobalState::interfaceLocale());

        $company_placement_info = array(
            'company_name' => $company['company'],
            'company_corporate_name' => $company['corporate_name'],
            'company_address' => $company['address'],
            'company_city' => $company['city'],
            'company_country' => $company['country'],
            'company_state' => $company['state'],
            'company_zipcode' => $company['zipcode'],
            'company_phone' => $company['phone'],
            'company_phone_2' => '',
            'company_fax' => $company['fax'],
            'company_website' => \trim($company['url']),
            'company_users_department' => $company['email'],
            'company_site_administrator' => $company['email'],
            'company_orders_department' => $company['email'],
            'company_support_department' => $company['email'],
            'company_newsletter_email' => $company['email'],
            'lang_code' => $company['lang_code'],
        );
    }

    foreach ($default_company_placement_info as $k => $v) {
        $company_placement_info['default_' . $k] = $v;
    }

    $lang_code = !empty($lang_code) ? $lang_code : $company_placement_info['lang_code'];

    $company_placement_info['company_country_descr'] = fn_get_country_name($company_placement_info['company_country'], $lang_code);
    $company_placement_info['company_state_descr'] = fn_get_state_name($company_placement_info['company_state'], $company_placement_info['company_country'], $lang_code);
    $company_placement_info['company_w_capital']        = $company['w_extras']['w_capital'];
    $company_placement_info['company_w_RCS']            = $company['w_extras']['w_RCS'];
    $company_placement_info['company_w_type']           = $company['w_company_type'];
    $company_placement_info['company_w_siret_number']   = $company['w_siret_number'];
    $company_placement_info['company_w_vat_number']     = $company['w_vat_number'];
    $company_placement_info['company_w_legal_status']   = $company['w_legal_status'];
    return $company_placement_info;
}

function fn_get_company_language($company_id)
{
    if (empty($company_id)) {
        return Registry::get('settings.Appearance.backend_default_language');
    } else {
        $company = fn_get_company_data($company_id);

        return $company['lang_code'];
    }
}

/**
 * Fucntion changes company status. Allowed statuses are A(ctive) and D(isabled)
 *
 * @param int $company_id
 * @param string $status_to A or D
 * @param string $reason The reason of the change
 * @param string $status_from Previous status
 * @param boolean $skip_query By default false. Update query might be skipped if status is already changed.
 * @return boolean True on success or false on failure
 */
function fn_companies_change_status($company_id, $status_to, $reason = '', &$status_from = '', $skip_query = false, $notify = true)
{
    if (empty($status_from)) {
        $status_from = db_get_field("SELECT status FROM ?:companies WHERE company_id = ?i", $company_id);
    }

    if (!in_array($status_to, array('A', 'P', 'D')) || $status_from == $status_to) {
        return false;
    }

    $result = $skip_query ? true : db_query("UPDATE ?:companies SET status = ?s WHERE company_id = ?i", $status_to, $company_id);

    if (!$result) {
        return false;
    }

    $company_data = fn_get_company_data($company_id);

    $account = $username = '';
    if ($status_from === CompanyStatus::NEW()->getValue() && (
        $status_to === CompanyStatus::ENABLED()->getValue() || $status_to === CompanyStatus::PENDING()->getValue()
    )) {
        if (Registry::get('settings.Vendors.create_vendor_administrator_account') == 'Y' && Company::isProfessional($company_data)) {

            $request_account_data = unserialize($company_data['request_account_data']);
            //Common updated data
            $user_data = [
                'company_id' => $company_id,
                'user_type'  => UserType::VENDOR()->getValue(),
            ];
            // Title is currently required for users - but does not exist for companies
            $user_data['title'] = $user_data['b_title'] = $user_data['s_title'] = UserTitle::MR();
            $user_data['firstname'] = $user_data['b_firstname'] = $user_data['s_firstname'] = $request_account_data['admin_firstname'];
            $user_data['lastname']  = $user_data['b_lastname']  = $user_data['s_lastname']  = $request_account_data['admin_lastname'];

            if (!empty($company_data['request_user_id'])) {
                $password_change_timestamp = db_get_field("SELECT password_change_timestamp FROM ?:users WHERE user_id = ?i", $company_data['request_user_id']);
                if (empty($password_change_timestamp)) {
                    $user_data['password_change_timestamp'] = TIME;
                }
                fn_update_user($company_data['request_user_id'], $user_data, $null, false,  false);

                $username = fn_get_user_name($company_data['request_user_id']);
                $account = 'updated';

                $msg = __('new_administrator_account_created') . '<a href="' . fn_url('profiles.update?user_id=' . $company_data['request_user_id']) . '">' . __('you_can_edit_account_details') . '</a>';
                fn_set_notification('N', __('notice'), $msg, 'K');

            } else {
                if (!empty($company_data['request_account_name'])) {
                    $user_data['user_login'] = $company_data['request_account_name'];
                } else {
                    $user_data['user_login'] = $company_data['email'];
                }

                $user_data['fields'] = $request_account_data['fields'];

                $user_data['user_type'] = UserType::VENDOR()->getValue();
                $user_data['password1'] = Password::generatePassword();
                $user_data['password2'] = $user_data['password1'];
                $user_data['status'] = 'A';
                $user_data['email'] = $company_data['email'];
                $user_data['company'] = $company_data['company'];
                $user_data['last_login'] = 0;
                $user_data['lang_code'] = $company_data['lang_code'];
                $user_data['password_change_timestamp'] = 0;

                list($added_user_id, $null) = fn_update_user(0, $user_data, $null, false,  false);

                if ($added_user_id) {
                    $msg = __('new_administrator_account_created') . '<a href="' . fn_url('profiles.update?user_id=' . $added_user_id) . '">' . __('you_can_edit_account_details') . '</a>';
                    fn_set_notification('N', __('notice'), $msg, 'K');

                    $username = $user_data['user_login'];
                    $account = 'new';
                }
            }
        }

        \Wizacha\Events\Config::dispatch(
            CompanyEvents::UPDATED,
            IterableEvent::fromElement($company_id)
        );

        \Wizacha\Events\Config::dispatch(
            \Wizacha\Product::EVENT_UPDATE,
            (new IterableEvent)->setIterator(
                new \Wizacha\Core\Iterator\PdoColumnIterator(
                    \Tygh\Database::query('SELECT product_id FROM ?:products WHERE company_id = ?i', $company_id)
                )
            )
        );

        \Wizacha\Events\Config::dispatch(
            CompanyEvents::APPROVED,
            IterableEvent::fromElement($company_id)
        );

        \Wizacha\Events\Config::dispatch(
            CompanyEvents::IBAN_BIC_UPDATED,
            IterableEvent::fromElement($company_id)
        );
    }

    if (empty($user_data)) {
        $user_id = db_get_field("SELECT user_id FROM ?:users WHERE company_id = ?i AND is_root = 'Y' AND user_type = 'V'", $company_id);
        $user_data = fn_get_user_info($user_id);
    }

    if ($notify && !empty($company_data['email'])) {

        $e_username = '';
        $e_account = '';
        $e_password = '';

        if ($status_from == 'N' && ($status_to == 'A' || $status_to == 'P')) {
            $e_username = $username;
            $e_account = $account;
            if ($account == 'new') {
                $e_password = $user_data['password1'];
            }
        }

        $eventDispatcher = container()->get('event_dispatcher');
        if ($status_to === Company::STATUS_DISABLED) {
            if ($status_from === Company::STATUS_ENABLED) {
                $eventDispatcher->dispatch(new CompanyDisabled($company_data, $reason), CompanyDisabled::class);
            } else {
                $eventDispatcher->dispatch(new CompanyRejected($company_data, $reason), CompanyRejected::class);
            }
        } elseif ($status_from === Company::STATUS_NEW
                && $status_to === Company::STATUS_PENDING)
        {
            $eventDispatcher->dispatch(
                new CompanyPending($company_data, $user_data),
                CompanyPending::class
            );
        }
    }

    return $result;
}

function fn_get_companies_sorting($simple_mode = true)
{
    $sorting = array(
        'company' => array('description' => __('name'), 'default_order' => 'asc'),
    );
    if ($simple_mode) {
        foreach ($sorting as &$sort_item) {
            $sort_item = $sort_item['description'];
        }
    }
    if (in_array(Registry::get('addons.discussion.company_discussion_type'), array('B', 'R'))) {
        $sorting['rating'] = array('description' => __('rating'), 'default_order' => 'desc');
    }

    return $sorting;
}

function fn_get_companies_sorting_orders()
{
    return array('asc', 'desc');
}

/**
 * Gets ids of all companies
 *
 * @staticvar array $all_companies_ids Static cache variable
 * @param boolean $renew_cache If defined, cache of companies ids will be renewed.
 * @return array Ids of all companies
 */
function fn_get_all_companies_ids($renew_cache = false)
{
    static $all_companies_ids = null;

    if ($all_companies_ids === null || $renew_cache) {
        $all_companies_ids = db_get_fields("SELECT company_id FROM ?:companies");
    }

    return $all_companies_ids;
}

function fn_get_default_company_id()
{
    return db_get_field("SELECT company_id FROM ?:companies WHERE status = 'A' ORDER BY company_id LIMIT 1");
}

function fn_get_company_id_from_request_user_id(int $userId): ?int
{
    $companyId = db_get_field("SELECT company_id FROM ?:companies WHERE request_user_id = ?i", $userId);

    return is_numeric($companyId) ? (int) $companyId : null;
}
