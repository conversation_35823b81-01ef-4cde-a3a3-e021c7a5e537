<?php
/***************************************************************************
 *                                                                          *
 *   (c) 2004 <PERSON>, <PERSON><PERSON>, Ilya M<PERSON> Shalnev    *
 *                                                                          *
 * This  is  commercial  software,  only  users  who have purchased a valid *
 * license  and  accept  to the terms of the  License Agreement can install *
 * and use this program.                                                    *
 *                                                                          *
 ****************************************************************************
 * PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
 * "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
 ****************************************************************************/

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Twig\Environment;
use Twig\Loader\ArrayLoader;
use Tygh\Navigation\LastView;
use Tygh\Registry;
use Tygh\Session;
use Tygh\Settings;
use Tygh\Shippings\Shippings;
use Wizacha\AppBundle\Constant\ResponseContentType;
use Wizacha\Component\MondialRelay\Exception\ApiException;
use Wizacha\Component\PdfGenerator\PdfOptions;
use Wizacha\Cscart\AppearanceType;
use Wizacha\Marketplace\Exception\InvalidCombinationException;
use Wizacha\Events\Config;
use Wizacha\Marketplace\Basket\Prorata;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\AmountTaxesDetail\AmountsTaxesDetails;
use Wizacha\Marketplace\Order\Event\OrderStatusChanged;
use Wizacha\Marketplace\Order\Event\OrderStatusUpdated;
use Wizacha\Marketplace\Order\Event\ShipmentCreated;
use Wizacha\Marketplace\Order\Exception\MissingShippingIdException;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderDataType;
use Wizacha\Marketplace\Order\OrderEvents;
use Wizacha\Marketplace\Order\OrderProjector;
use Wizacha\Marketplace\Order\Refund\Exception\RefundNotFoundException;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\RmaStatus;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\PIM\Product\Event\ProductStockThresholdReached;
use Wizacha\Marketplace\PIM\Tax\Enum\TaxRateType;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\Promotion\PromotionType;
use Wizacha\Marketplace\ReadModel\Basket;
use Wizacha\Marketplace\Search\SearchCriteria;
use Wizacha\Marketplace\Shipping\DeliveryType;
use Wizacha\Marketplace\Subscription\SubscriptionLinkStatus;
use Wizacha\Marketplace\Subscription\SubscriptionProduct;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Money\Money;
use Wizacha\OrderStatus;
use Wizacha\OrderStatus as LegacyOrderStatus;
use Wizacha\Product;
use Wizacha\Component\Chronopost\Exceptions\SystemError;
use Wizacha\Component\Chronopost\Exceptions\PickupPointException;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Core\Concurrent\MutexService;
use Wizacha\Marketplace\Transaction\TransactionStatus;

use function Wizacha\Marketplace\Order\is_order_status_equal_to;

//
// Get product description to show it in the cart
//
function fn_get_cart_product_data($hash, &$product, $skip_promotion, &$cart, &$auth, $promotion_amount = 0)
{
    if (!empty($product['product_id'])) {

        $fields = array(
            '?:products.product_id',
            '?:products.company_id',
            "GROUP_CONCAT(IF(?:products_categories.link_type = 'M', CONCAT(?:products_categories.category_id, 'M'), ?:products_categories.category_id)) as category_ids",
            '?:products.product_code',
            '?:products.weight',
            '?:products.tracking',
            '?:product_descriptions.product',
            '?:product_descriptions.short_description',
            '?:products.is_edp',
            '?:products.edp_shipping',
            '?:products.shipping_freight',
            '?:products.free_shipping',
            '?:products.zero_price_action',
            '?:products.tax_ids',
            '?:products.qty_step',
            '?:products.list_qty_count',
            '?:products.max_qty',
            '?:products.min_qty',
            '?:products.amount as in_stock',
            '?:products.shipping_params',
            '?:companies.status as company_status',
            '?:companies.company as company_name'
        );

        $join  = db_quote("LEFT JOIN ?:product_descriptions ON ?:product_descriptions.product_id = ?:products.product_id AND ?:product_descriptions.lang_code = ?s", (string) GlobalState::interfaceLocale());

        $_p_statuses = array('A', 'H');
        $_c_statuses = array('A', 'H');
        $avail_cond = (AREA == 'C' && !(isset($auth['area']) && $auth['area'] == 'A')) ? db_quote(' AND ?:categories.status IN (?a) AND ?:products.status IN (?a)', $_c_statuses, $_p_statuses) : '';
        $avail_cond .= (AREA == 'C') ? fn_get_localizations_condition('?:products.localization') : '';

        $join .= " INNER JOIN ?:products_categories ON ?:products_categories.product_id = ?:products.product_id INNER JOIN ?:categories ON ?:categories.category_id = ?:products_categories.category_id $avail_cond";
        $join .= " LEFT JOIN ?:companies ON ?:companies.company_id = ?:products.company_id";

        if (array_key_exists('productClassName', $product) && SubscriptionProduct::class === $product['productClassName']) {
            $_pdata = $product;
        } else {
            $_pdata = db_get_memoized_row("SELECT " . implode(', ', $fields) . " FROM ?:products ?p WHERE ?:products.product_id = ?i GROUP BY ?:products.product_id", $join, $product['product_id']);
        }

        // delete product from cart if vendor was disabled.
        if (empty($_pdata) || (!empty($_pdata['company_id']) && !defined('ORDER_MANAGEMENT') && $_pdata['company_status'] != 'A')) {
            unset($cart['products'][$hash]);

            return false;
        }

        if (!empty($_pdata['category_ids'])) {
            list($_pdata['category_ids'], $_pdata['main_category']) = fn_convert_categories($_pdata['category_ids']);
        } else {
            $_pdata['category_ids'] = array();
        }

        $_pdata['options_count'] = db_get_memoized_field("SELECT COUNT(*) FROM ?:product_options WHERE product_id = ?i AND status = 'A'", $product['product_id']);

        $amount = !empty($product['amount_total']) ? $product['amount_total'] : $product['amount'];
        $_pdata['price'] = empty($product['price']) ? fn_get_product_price($product['product_id'], $amount): $product['price'];

        $_pdata['base_price'] = (isset($product['stored_price']) && $product['stored_price'] == 'Y') ? $product['price'] : $_pdata['price'];

        $product['stored_price'] = empty($product['stored_price']) ? 'N' : $product['stored_price'];
        $product['stored_discount'] = empty($product['stored_discount']) ? 'N' : $product['stored_discount'];
        $product['product_options'] = empty($product['product_options']) ? array() : $product['product_options'];

        if (empty($_pdata['product_id'])) { // FIXME - for deleted products for OM
            unset($cart['products'][$hash]);

            return array();
        }

        if (!empty($_pdata['options_count']) && empty($product['product_options'])) {
            $cart['products'][$hash]['product_options'] = fn_get_default_product_options($product['product_id']);
        }

        if (Registry::get('settings.General.inventory_tracking') == 'Y' && !empty($_pdata['tracking']) && $_pdata['tracking'] == 'O' && !empty($product['selectable_cart_id'])) {
            $_pdata['in_stock'] = db_get_field("SELECT amount FROM ?:product_options_inventory WHERE combination_hash = ?i", $product['selectable_cart_id']);
        }

        $product['amount'] = fn_check_amount_in_stock($product['product_id'], $product['amount'], $product['product_options'], $hash, $_pdata['is_edp'], !empty($product['original_amount']) ? $product['original_amount'] : 0, $cart);

        if ($product['amount'] == 0) {
            unset($cart['products'][$hash]);
            $out_of_stock = true;

            return false;
        }

        $exceptions = Product::getExceptions($product['product_id']);
        if (!isset($product['options_type']) || !isset($product['exceptions_type'])) {
            $product = array_merge($product, db_get_memoized_row('SELECT options_type, exceptions_type FROM ?:products WHERE product_id = ?i', $product['product_id']));
        }
        if (!\Wizacha\Option::isCombinationValid($product['product_options'], $exceptions) && !defined('GET_OPTIONS')) {
            throw new InvalidCombinationException(
                $product['product_id'],
                $product['product_options']
            );
        }

        if (isset($product['extra']['custom_files'])) {
            $_pdata['extra']['custom_files'] = $product['extra']['custom_files'];
        }

        $_pdata['calculation'] = array();

        if (isset($product['extra']['exclude_from_calculate'])) {
            $_pdata['exclude_from_calculate'] = $product['extra']['exclude_from_calculate'];
            $_pdata['aoc'] = !empty($product['extra']['aoc']);
            $_pdata['price'] = 0;
        } else {
            if ($product['stored_price'] == 'Y') {
                $_pdata['price'] = $product['price'];
            }
        }

        $product['price'] = ($_pdata['zero_price_action'] == 'A' && isset($product['custom_user_price'])) ? $product['custom_user_price'] : floatval($_pdata['price']);
        $cart['products'][$hash]['price'] = $product['price'];

        $_pdata['original_price'] = $product['price'];

        if ($product['stored_price'] != 'Y' && !isset($product['extra']['exclude_from_calculate']) && $skip_promotion === true) {
            $_tmp = $product['price'];

            $product['price'] = fn_apply_options_modifiers($product['product_options'], $product['price'], 'P', array(), array('product_data' => $product), $amount);
            $product['modifiers_price'] = $_pdata['modifiers_price'] = $product['price'] - $_tmp; // modifiers
        }
        // hotfix
        $product['modifiers_price'] = $_pdata['modifiers_price'] = 0;


        if (isset($product['modifiers_price']) && $_pdata['zero_price_action'] == 'A') {
            $_pdata['base_price'] = $product['price'] - $product['modifiers_price'];
        }

        $_pdata['weight'] = fn_apply_options_modifiers($product['product_options'], $_pdata['weight'], 'W', array(), array('product_data' => $product), $amount);
        $_pdata['amount'] = $product['amount'];
        $_pdata['price'] = $_pdata['original_price'] = fn_format_price($product['price']);

        $_pdata['stored_price'] = $product['stored_price'];

        if ($cart['options_style'] == 'F') {
            $_pdata['product_options'] = fn_get_selected_product_options($product['product_id'], $product['product_options'], (string) GlobalState::interfaceLocale());
            $_pdata['product_options'] = \Wizacha\Option::filterExceptions(
                $_pdata['product_options'],
                $exceptions
            );
        } elseif ($cart['options_style'] == 'I') {
            $_pdata['product_options'] = fn_get_selected_product_options_info($product['product_options'], (string) GlobalState::interfaceLocale());
        } else {
            $_pdata['product_options'] = $product['product_options'];
        }

        if (($_pdata['free_shipping'] != 'Y' || AREA == 'A') && ($_pdata['is_edp'] != 'Y' || ($_pdata['is_edp'] == 'Y' && $_pdata['edp_shipping'] == 'Y'))) {
            $cart['shipping_required'] = true;
        }

        $cart['products'][$hash]['is_edp'] = (!empty($_pdata['is_edp']) && $_pdata['is_edp'] == 'Y') ? 'Y' : 'N';
        $cart['products'][$hash]['edp_shipping'] = (!empty($_pdata['edp_shipping']) && $_pdata['edp_shipping'] == 'Y') ? 'Y' : 'N';

        if (empty($cart['products'][$hash]['extra']['parent'])) { // count only products without parent
            if ($skip_promotion == true && !empty($promotion_amount)) {
                $cart['amount'] += $promotion_amount;
            } else {
                $cart['amount'] += $product['amount'];
            }
        }


        if (!empty($product['object_id'])) {
            $_pdata['object_id'] = $product['object_id'];
        }

        $_pdata['shipping_params'] = empty($_pdata['shipping_params']) ? array() : unserialize($_pdata['shipping_params']);

        $_pdata['stored_discount'] = $product['stored_discount'];
        $cart['products'][$hash]['modifiers_price'] = $product['modifiers_price'];

        $_pdata['subtotal'] = $_pdata['price'] * $product['amount'];
        $cart['original_subtotal'] += $_pdata['original_price'] * $product['amount'];
        $cart['subtotal'] += $_pdata['subtotal'];

        return $_pdata;
    }

    return array();
}

/**
 * Update cart products data
 *
 * @param array $cart Array of cart content and user information necessary for purchase
 * @param array $cart_products Array of new data for products information update
 * @return boolean Always true
 */
function fn_update_cart_data(&$cart, &$cart_products)
{
    foreach ($cart_products as $k => $v) {
        if (isset($cart['products'][$k])) {
            if (!isset($v['base_price'])) {
                $cart['products'][$k]['base_price'] = $v['base_price'] = $cart['products'][$k]['stored_price'] != 'Y' ? $v['price'] : $cart['products'][$k]['price'];
            } else {
                if ($cart['products'][$k]['stored_price'] == 'Y') {
                    $cart_products[$k]['base_price'] = $cart['products'][$k]['price'];
                }
            }

            $cart['products'][$k]['base_price'] = $cart['products'][$k]['stored_price'] != 'Y' ? $v['base_price'] : $cart['products'][$k]['price'];
            $cart['products'][$k]['price'] = $cart['products'][$k]['stored_price'] != 'Y' ? $v['price'] : $cart['products'][$k]['price'];
            if (isset($v['discount'])) {
                $cart['products'][$k]['discount'] = $v['discount'];
            }
            if (isset($v['promotions'])) {
                $cart['products'][$k]['promotions'] = $v['promotions'];
            }
        }
    }

    return true;
}

/**
 * Get all available payment methods for current area
 *
 * @param string $lang_code 2-letter language code
 * @return array found payment methods
 */
function fn_get_payment_methods($lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $condition = '';
    if (AREA == 'C') {
        $condition .= " AND ?:payments.status = 'A' ";
        $condition .= fn_get_localizations_condition('?:payments.localization');
    }

    $payment_methods = db_get_memoized_hash_array("SELECT ?:payments.payment_id, ?:payments.a_surcharge, ?:payments.p_surcharge, ?:payments.payment_category, ?:payment_descriptions.*, ?:payment_processors.processor, ?:payment_processors.type AS processor_type FROM ?:payments LEFT JOIN ?:payment_descriptions ON ?:payments.payment_id = ?:payment_descriptions.payment_id AND ?:payment_descriptions.lang_code = ?s LEFT JOIN ?:payment_processors ON ?:payment_processors.processor_id = ?:payments.processor_id WHERE 1 $condition ORDER BY ?:payments.position", 'payment_id', $lang_code);

    return $payment_methods;
}

/**
 * Gets payment methods names list
 *
 * @param boolean $is_active Flag determines if only the active methods should be returned; default value is false
 * @param string $lang_code 2-letter language code
 * @return array Array of payment method names with payment_ids as keys
 */
function fn_get_simple_payment_methods($is_active = true, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $condition = '';
    if ($is_active) {
        $condition .= " AND status = 'A'";
    }

    return db_get_memoized_hash_single_array("SELECT ?:payments.payment_id, ?:payment_descriptions.payment FROM ?:payments LEFT JOIN ?:payment_descriptions ON ?:payments.payment_id = ?:payment_descriptions.payment_id AND ?:payment_descriptions.lang_code = ?s WHERE 1 $condition ORDER BY ?:payments.position, ?:payment_descriptions.payment", array('payment_id', 'payment'), $lang_code);
}

/**
 * Get payment method data
 *
 * @param int $payment_id payment ID
 * @param string $lang_code 2-letter language code
 * @return array payment information
 */
function fn_get_payment_method_data($payment_id, $lang_code = null, $setPlaceholder = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    static $payments = array();

    if (empty($payments[$payment_id]) || $setPlaceholder === true) {
        $fields = array(
            '?:payments.*',
            '?:payment_descriptions.*',
            '?:payment_processors.processor',
            '?:payment_processors.type AS processor_type'
        );

        $join = db_quote(" LEFT JOIN ?:payment_descriptions ON ?:payments.payment_id = ?:payment_descriptions.payment_id AND ?:payment_descriptions.lang_code = ?s", $lang_code);
        $join .= db_quote(" LEFT JOIN ?:payment_processors ON ?:payment_processors.processor_id = ?:payments.processor_id");

        $payment = db_get_memoized_row("SELECT " . implode(', ', $fields) . " FROM ?:payments ?p WHERE ?:payments.payment_id = ?i", $join, $payment_id);

        if (!empty($payment)) {
            $payment['processor_params'] = (!empty($payment['processor_params'])) ? unserialize($payment['processor_params']) : '';
            $payment['tax_ids'] = !empty($payment['tax_ids']) ? fn_explode(',', $payment['tax_ids']) : array();
            $payment['image'] = fn_get_image_pairs($payment_id, 'payment', 'M', true, true, $lang_code);
        }

        $payments[$payment_id] = $payment;
    }

    return $payments[$payment_id];
}

/**
 * Get payments method data
 *
 * @param string $lang_code 2-letter language code
 * @return array payments information
 */

function fn_get_payments($lang_code = null)
{
    return db_get_memoized_array("SELECT ?:payments.*, ?:payment_descriptions.* FROM ?:payments LEFT JOIN ?:payment_descriptions ON ?:payment_descriptions.payment_id = ?:payments.payment_id AND ?:payment_descriptions.lang_code = ?s ORDER BY ?:payments.position", $lang_code ?? (string) GlobalState::interfaceLocale());
}

/**
 * Create/Update payments data
 *
 * @param array $payment_data
 * @param int $payment_id
 * @param string $lang_code 2-letter language code
 * @return int Payment id
 */
function fn_update_payment($payment_data, $payment_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    $payment_data['localization'] = !empty($payment_data['localization']) ? fn_implode_localizations($payment_data['localization']) : '';
    $payment_data['tax_ids'] = !empty($payment_data['tax_ids']) ? fn_create_set($payment_data['tax_ids']) : '';

    // Update payment processor settings
    if (!empty($payment_data['processor_params'])) {
        $payment_data['processor_params'] = serialize($payment_data['processor_params']);
    }

    // Sanitize payment description
    if (true === \array_key_exists('instructions', $payment_data) && $payment_data['instructions'] !== null) {
        $purifierService = container()->get('purifier.default');
        $payment_data['instructions'] = \trim($purifierService->purify($payment_data['instructions']));
    }

    // check reference is unique
    if (isset($payment_data['external_reference'])) {
        if ($payment_id > 0) {
            $referenceAlreadyUsed = (bool) db_get_field('SELECT COUNT(*) FROM ?:payments WHERE external_reference = ?s AND payment_id != ?i', $payment_data['external_reference'], $payment_id);
        } else {
            $referenceAlreadyUsed = (bool) db_get_field('SELECT COUNT(*) FROM ?:payments WHERE external_reference = ?s', $payment_data['external_reference']);
        }

        if ($referenceAlreadyUsed) {
            fn_set_notification('E', __('error'), __('payment_external_reference_already_used', ['%externalReference%' => $payment_data['external_reference']]));
            return false;
        }
    }

    if (array_key_exists('external_reference', $payment_data) && $payment_data['external_reference'] === '') {
        $payment_data['external_reference'] = null;
    }

    if (!empty($payment_id)) {
        $arow = db_query("UPDATE ?:payments SET ?u WHERE payment_id = ?i", $payment_data, $payment_id);
        db_query("UPDATE ?:payment_descriptions SET ?u WHERE payment_id = ?i AND lang_code = ?s", $payment_data, $payment_id, $lang_code);

        if ($arow === false) {
            fn_set_notification('E', __('error'), __('object_not_found', array('[object]' => __('payment'))),'','404');
            $payment_id = false;
        }
    } else {
        $payment_data['payment_id'] = $payment_id = db_query("INSERT INTO ?:payments ?e", $payment_data);
        \Tygh\Languages\Helper::insertTranslations('payment_descriptions', $lang_code, $payment_data);
    }

    fn_attach_image_pairs('payment_image', 'payment', $payment_id, $lang_code);

    return $payment_id;
}

//
// Update product amount
//
// returns true if inventory successfully updated and false if amount
// is negative is allow_negative_amount option set to false

function fn_update_product_amount($product_id, $amount, $product_options, $sign)
{
    if (Registry::get('settings.General.inventory_tracking') != 'Y') {
        return true;
    }

    ['tracking' => $tracking, 'infinite_stock' => $infiniteStock] = db_get_memoized_row("SELECT tracking, infinite_stock FROM ?:products WHERE product_id = ?i", $product_id);

    // Tracking is disabled, or Tracking is about a product (not his options) and stock is infinite
    if ($tracking == 'D' || ($tracking === 'B' && $infiniteStock)) {
        return true;
    }

    if ($tracking == 'B') {
        $product = db_get_row("SELECT amount, product_code FROM ?:products WHERE product_id = ?i", $product_id);
        $current_amount = $product['amount'];
        $product_code = $product['product_code'];
    } else {
        $cart_id = fn_generate_cart_id($product_id, array('product_options' => $product_options), true);
        $product = db_get_row("SELECT amount, product_code, infinite_stock FROM ?:product_options_inventory WHERE combination_hash = ?i", $cart_id);

        if ($product['infinite_stock']) {
            return true;
        }

        $current_amount = empty($product['amount']) ? 0 : $product['amount'];

        if (empty($product['product_code'])) {
            $product_code = db_get_memoized_field("SELECT product_code FROM ?:products WHERE product_id = ?i", $product_id);
        } else {
            $product_code = $product['product_code'];
        }
    }

    if ($sign == '-') {
        $new_amount = $current_amount - $amount;

        // Notify administrator about inventory low stock
        if ($new_amount <= Registry::get('settings.General.low_stock_threshold')) {
            // Log product low-stock
            $company_id = fn_get_company_id('products', 'product_id', $product_id);

            //Remove low stock notification for C2C
            if (\Wizacha\Company::isProfessional(fn_get_company_data($company_id))) {
                $lang_code = fn_get_company_language($company_id);
                $lang_code = !empty($lang_code) ? $lang_code : Registry::get('settings.Appearance.backend_default_language');
                $selected_product_options = ($tracking == 'O') ? fn_get_selected_product_options_info($product_options, $lang_code) : '';

                $eventDispatcher = container()->get('event_dispatcher');
                $eventDispatcher->dispatch(
                    new ProductStockThresholdReached(
                        (int)$product_id, fn_get_options_combination($product_options), (int)$new_amount
                    ),
                    ProductStockThresholdReached::class
                );
            }
        }

        if ($new_amount < 0 && Registry::get('settings.General.allow_negative_amount') != 'Y') {
            return false;
        }
    } else {
        $new_amount = $current_amount + $amount;
    }

    if ($tracking == 'B') {
        db_query("UPDATE ?:products SET amount = ?i WHERE product_id = ?i", $new_amount, $product_id);
    } else {
        db_query("UPDATE ?:product_options_inventory SET amount = ?i WHERE combination_hash = ?i", $new_amount, $cart_id);
    }

    if (Product::hasChanged($product_id)) {
        Config::dispatch(
            Product::EVENT_UPDATE,
            (new \Wizacha\Events\IterableEvent)->setElement($product_id)
        );
    }

    return true;
}

function fn_set_orders_promotions(&$order, $cart)
{
    if ($order['company_id'] !== 0) {
        $promotionService = container()->get('marketplace.promotion.promotionservice');
        foreach ($order['new_promotion_ids'] as $orderPromotionId) {
            $promotion = $promotionService->get($orderPromotionId);
            if (false === is_null($promotion->getCompanyId())
                && (
                    $order['company_id'] !== $promotion->getCompanyId()
                    || $order['subtotal_discount'] === 0.0
                )
            ) {
                unset($order['promotions'][$orderPromotionId]);
            }
        }

        $orderPromotion = $order['promotions'];
        $order['promotions'] = serialize(!empty($order['promotions']) ? $order['promotions'] : array());
        if (!empty($order['promotions'])) {
            $order['promotion_ids'] = fn_create_set(array_keys($orderPromotion));
        }
    } else {
        $order['promotions'] = serialize(!empty($cart['promotions']) ? $cart['promotions'] : array());
        if (!empty($cart['promotions'])) {
            $order['promotion_ids'] = fn_create_set(array_keys($cart['promotions']));
        }
    }
}

function fn_update_order(&$cart, $order_id = 0)
{
    $cart['user_data'] = (isset($cart['user_data'])) ? $cart['user_data'] : array();

    // Update de l'adresse de facturation spécifique (si elle est présente).
    // Utilisé par exemple pour les commandes passées par un membre d'une
    // organisation.
    $billingAddress = [];
    if ($cart['billing_address']) {
        /** @var $address Address */
        $address = $cart['billing_address'];
        $address->assertValid();

        $billingAddress = [
            'b_label' => $address->getLabel(),
            'b_title' => $address->getTitle(),
            'b_firstname' => $address->getFirstName(),
            'b_lastname' => $address->getLastName(),
            'b_company' => $address->getCompany(),
            'b_address' => $address->getAddress(),
            'b_address_2' => $address->getAdditionalAddress(),
            'b_city' => $address->getCity(),
            'b_county' => '',
            'b_state' => $address->getState(),
            'b_country' => $address->getCountry(),
            'b_zipcode' => $address->getZipCode(),
            'b_phone' => $address->getPhone(),
            'b_comment' => $address->getComment(),
        ];
    }

    // Update de l'adresse de livraison spécifique (si elle est présente).
    // Actuellement utilisée pour les adresses des relais colis de Chronopost
    // ainsi que pour les commandes passées par un membre d'une organisation.
    $shippingAddress = [];
    if ($cart['shipping_address']) {
        /** @var $address Address */
        $address = $cart['shipping_address'];
        $address->assertValid();

        $shippingAddress = [
            's_label' => $address->getLabel(),
            's_title' => $address->getTitle(),
            's_firstname' => $address->getFirstName(),
            's_lastname' => $address->getLastName(),
            's_company' => $address->getCompany(),
            's_address' => $address->getAddress(),
            's_address_2' => $address->getAdditionalAddress(),
            's_city' => $address->getCity(),
            's_county' => '',
            's_state' => $address->getState(),
            's_country' => $address->getCountry(),
            's_zipcode' => $address->getZipCode(),
            's_pickup_point_id' => $address->getPickupPointId(),
            's_phone' => $address->getPhone(),
            's_comment' => $address->getComment(),
        ];
    }

    if (\array_key_exists('chosen_shipping', $cart) === true
        && \is_array($cart['chosen_shipping']) === true
        && \count($cart['chosen_shipping']) === 1
    ) {
        foreach ($cart['chosen_shipping'] as $chosen_shipping) {
            foreach ($cart['product_groups'] as $cartGroups) {
                $infoShipping = $cartGroups['shippings'][$chosen_shipping];
                if ($infoShipping['w_delivery_type'] === (string) DeliveryType::CHRONO_RELAIS()
                    && \is_string($cartGroups['pickupPointId']) === true
                ) {
                    // Récupération du détail du pickup point (FR en dur pour l'instant)
                    try {
                        $shippingAddress = container()->get('marketplace.chronopost.client')->getShippingAddress(
                            $cartGroups
                        );
                    } catch (SystemError $exception) {
                        throw new PickupPointException($exception->getMessage());
                    }
                }
                if ($infoShipping['w_delivery_type'] === (string) DeliveryType::MONDIAL_RELAY()
                    && \is_string($cartGroups['pickupPointId']) === true
                ) {
                    try {
                        $shippingAddress = container()->get('marketplace.mondial_relay.client')->getShippingAddress(
                            $cartGroups
                        );
                    } catch (ApiException $exception) {
                        throw new PickupPointException(__(sprintf('mondial_relay.error.%s', $exception->getCode())));
                    }
                }
            }
        }
    }

    // Merge de toutes les informations
    $order = fn_array_merge($cart, $cart['user_data'], $shippingAddress, $billingAddress);
    unset($order['user_data']);

    // filter hidden fields, which were hidden to checkout
    fn_filter_hidden_profile_fields($order, 'O');

    // If the contact information fields were disabled, fill the information from the billing/shipping
    container()->getParameter('checkout.address.billing_first') ? $address_zone = 'b' : $address_zone = 's';
    if (!empty($order['firstname']) || !empty($order[$address_zone . '_firstname'])) {
        $order['firstname'] = empty($order['firstname']) && !empty($order[$address_zone . '_firstname']) ? $order[$address_zone . '_firstname'] : $order['firstname'];
    }
    if (!empty($order['lastname']) || !empty($order[$address_zone . '_lastname'])) {
        $order['lastname'] = empty($order['lastname']) && !empty($order[$address_zone . '_lastname']) ? $order[$address_zone . '_lastname'] : $order['lastname'];
    }
    if (!empty($order['phone']) || !empty($order[$address_zone . '_phone'])) {
        $order['phone'] = empty($order['phone']) && !empty($order[$address_zone . '_phone']) ? $order[$address_zone . '_phone'] : $order['phone'];
    }

    // Shipping is required but there is no shipping id selected and some shipping are available in $cart['shipping']
    if (!empty($cart['shipping']) && !$cart['shipping_ids'] && $cart['shipping_required']) {

        // For a parent order or a single order, we get the shipping id from available shipping list
        if ($cart['parent_order_id'] === 0) {
            $order['shipping_ids'] = fn_create_set(array_column($cart['shipping'], 'shipping_id'));

        } else {
            // Child orders must already have a shipping id selected
            throw new MissingShippingIdException('Unable to create order: missing required shipping ID', [
                'parent_order_id' => $cart['parent_order_id'],
                'company_id' => $cart['company_id'],
                'shipping' => $cart['shipping']
            ]);
        }
    }

    if (!empty($cart['payment_surcharge'])) {
        $cart['total'] += $cart['payment_surcharge'];
        $order['total'] = $cart['total'];

        $cart['companies'] = fn_get_products_companies($cart['products']);
        $take_payment_surcharge_from_vendor = fn_take_payment_surcharge_from_vendor();

        if (Registry::get('settings.Vendors.include_payment_surcharge') == 'Y' && $take_payment_surcharge_from_vendor) {
            $cart['companies_count'] = count($cart['companies']);
            $cart['total'] -= $cart['payment_surcharge'];
            $order['total'] = $cart['total'];
        }
    }

    // Sauvegarde de l'id du panier dans la commande
    $order['basket_id'] = $cart['basket_id'] ?? '';

    // Sauvegarde de l'id de la subscription dans l'order
    $order['subscription_id'] = $cart['subscriptionId'];

    $order['carriage_paid'] = false;
    if (true === \array_key_exists('product_groups', $order)) {
        $order['carriage_paid'] = true;
        // Determine if every shipping group of the order has free shipping
        foreach ($order['product_groups'] as $productGroup) {
            if (true !== $productGroup['carriage_paid']) {
                $order['carriage_paid'] = false;
                break;
            }
        }
    }

    // Add customer info to order
    $order['is_customer_professional'] = $cart['is_professional'] ?? '0';
    if (true === \array_key_exists('is_customer_professional', $order) && '1' === $order['is_customer_professional']) {
        $order['customer_company'] = $cart['company'] ?? '';
        $order['customer_legal_identifier'] = $cart['legal_identifier'] ?? '';
        $order['customer_intra_european_community_vat'] = $cart['intra_european_community_vat'] ?? '';
        $order['customer_job_title'] = $cart['job_title'] ?? '';
    }
    $order['customer_account_comment'] = $cart['comment'] ?? '';
    $order['customer_external_identifier'] = $cart['external_identifier'] ?? '';

    if (empty($order_id)) {
        $ip = fn_get_ip();
        $order['ip_address'] = $ip['host'];
        $order['timestamp']  = TIME;
        $order['lang_code']  = (string) GlobalState::interfaceLocale();
        $order['company_id'] = 0;
        $order['notes']      = '';
        $order['status']     = STATUS_INCOMPLETED_ORDER; // incomplete by default to increase inventory
        $order['customer_total'] = $order['total'];
        $order_status        = $order['status'];

        if (is_array($order['product_groups']) && count($order['product_groups']) > 1 && !$order['parent_order_id']) {
            $order['is_parent_order'] = 'Y';
            $order['status'] = STATUS_PARENT_ORDER;
            $order['total_excl_tax'] = $cart['total'] - $cart['tax_subtotal'];
        } elseif (!empty($order['product_groups'][0]['company_id'])) {
            $order['is_parent_order'] = 'N';
            $order['company_id'] = $order['product_groups'][0]['company_id'];
            $order['notes'] = $order['product_groups'][0]['comment'];
        }

        if (defined('CART_LOCALIZATION')) {
            $order['localization_id'] = CART_LOCALIZATION;
        }

        if (!empty($cart['rewrite_order_id'])) {
            $order['order_id'] = array_shift($cart['rewrite_order_id']);
        }

        fn_set_orders_promotions($order, $cart);

        $order = fn_place_marketplace_discount($order, $cart);

        $order_id = db_query("INSERT INTO ?:orders ?e", $order);

    } else {
        unset($order['order_id']);

        // We're editing existing order
        if (isset($cart['promotions'])) {
            fn_set_orders_promotions($order, $cart);
        }

        $old_order = db_get_row("SELECT company_id, payment_id, status FROM ?:orders WHERE order_id = ?i", $order_id);
        $order['status']     = $old_order['status'];
        $order['company_id'] = $old_order['company_id'];
        $order_status        = $order['status'];

        if (!empty($cart['payment_id']) && $cart['payment_id'] == $old_order['payment_id']) {
            $payment_info = db_get_field(
                "SELECT data FROM ?:order_data WHERE order_id = ?i AND type = ?s",
                $order_id,
                OrderDataType::PAYMENT_INFO()->getValue()
            );
            if (!empty($payment_info)) {
                $payment_info = unserialize(fn_decrypt_text($payment_info));
                $cart['payment_info'] = array_merge($payment_info, (!empty($cart['payment_info'])) ? $cart['payment_info'] : array());
            }
        }

        // incomplete the order to increase inventory amount.
        //fn_change_order_status($order_id, STATUS_INCOMPLETED_ORDER, $old_order['status'], fn_get_notification_rules(array(), false));

        $profile_fields = db_get_hash_array("SELECT field_id, value FROM ?:profile_fields_data WHERE object_id = ?i AND object_type = 'O'", 'field_id', $order_id);
        foreach ($profile_fields as $k => $v) {
            if (!isset($cart['user_data']['fields'][$k])) {
                $cart['user_data']['fields'][$k] = $v['value'];
            }
        }

        db_query("UPDATE ?:orders SET ?u WHERE order_id = ?i", $order, $order_id);

        if (!empty($order['products'])) {
            db_query("DELETE FROM ?:order_details WHERE order_id = ?i", $order_id);
        }
    }

    fn_store_profile_fields($cart['user_data'], $order_id, 'O');
    fn_create_order_details($order_id, $cart);
    fn_update_order_data($order_id, $cart);

    // Log order creation/update
    $log_action = !empty($order['order_id']) ? 'update' : 'create';
    fn_log_event('orders', $log_action, array(
        'order_id' => $order_id,
        'company_id' => 0,
    ));

    //
    // Place the order_id to new_orders table for all admin profiles
    //
    if (empty($order['parent_order_id'])) {
        $condition = db_quote(" AND (user_type = 'A' OR (user_type = 'V' AND ?:users.company_id = ?i))", $order['company_id']);

        $admins = db_get_memoized_fields("SELECT user_id FROM ?:users WHERE 1 $condition");
        foreach ($admins as $k => $v) {
            db_query("REPLACE INTO ?:new_orders (order_id, user_id) VALUES (?i, ?i)", $order_id, $v);
        }
    }
    $isParentOrder = true;

    //set total_excl_tax for no parent order
    if ('N' === $order['is_parent_order']) {
        // We call the order to use the OrderAmountsCalculator for generating and freeze the OrderAmounts in DB
        $order = container()->get('marketplace.order.order_service')->getAnyOrder($order_id);
        $isParentOrder = false;

        $amountExcludingTaxes = $order->getAmountExcludingTaxes()->getConvertedAmount();
        if ($amountExcludingTaxes !== null) {
            db_query('UPDATE ?:orders SET ?u WHERE order_id = ?i', array('total_excl_tax'=>$amountExcludingTaxes), $order_id);
        }
    }

    return array($order_id, $order_status, $isParentOrder);
}

function fn_place_marketplace_discount(array $order, array $cart): array
{
    $promotionMarketplace = null;
    if (\array_key_exists('promotions',$cart) === true && \is_array($cart['promotions']) === true) {
        foreach ($cart['promotions'] as $promotion) {
            if ($promotion['promotion_type'] === PromotionType::MARKETPLACE) {
                $promotionMarketplace = $promotion;
            }
        }
    }

    //On vérfie qu'il s'agit bien d'une promotion marketplace
    if (\array_key_exists('marketplace_discount_total', $cart) === false || $cart['marketplace_discount_total'] <= 0 || \is_null($promotionMarketplace)) {
        return $order;
    }

    // Parent or single order case
    if ($order['is_parent_order'] === 'Y' || $order['parent_order_id'] === 0) {
        $order['marketplace_discount_total'] = $cart['marketplace_discount_total'];
        $order['customer_total'] = $order['total'];
        $order['total'] = Money::fromVariable($cart['total'])
            ->add(Money::fromVariable($order['marketplace_discount_total']))
            ->getConvertedAmount();
    }

    // Child order case
    if ($order['parent_order_id'] > 0) {
        if ($promotionMarketplace['target'] === 'shipping') {
            //Case with a promotion on shipping
            $prorata = new Prorata(
                Money::fromVariable($cart['display_shipping_cost']),
                Money::fromVariable($cart['shipping_cost']),
                Money::fromVariable($cart['marketplace_discount_total'])
            );
        } else {
            //Case with a promotion on basket

            // We need to calculate the prorata on the subtotal not discounted
            $subtotalNotDiscounted = Money::fromVariable($cart['display_subtotal'])
                ->add(Money::fromVariable($cart['marketplace_discount_total']));

            $prorata = new Prorata(
                $subtotalNotDiscounted,
                Money::fromVariable($cart['subtotal']),
                Money::fromVariable($cart['marketplace_discount_total'])
            );
        }

        $order['marketplace_discount_total'] = $prorata->getDiscount()->getConvertedAmount();
        //Il s'agit ici d'un calcul direct sur le customer total et non d'un prorata
        $order['customer_total'] = Money::fromVariable($cart['total'])->subtract(Money::fromVariable($order['marketplace_discount_total']))->getConvertedAmount();

        if ($prorata->getDiscount()->isZero()) {
            $order['marketplace_discount_id'] = null;
        }
    }

    return $order;
}

function fn_create_order_details($order_id, $cart)
{
    if (!empty($cart['products'])) {
        foreach ((array) $cart['products'] as $k => $v) {
            $product_code = $v['product_code'] ?? '';
            $extra = empty($v['extra']) ? array() : $v['extra'];
            $v['discount'] = empty($v['discount']) ? 0 : $v['discount'];

            $extra['product'] = empty($v['product']) ? fn_get_product_name($v['product_id']) : $v['product'];

            $extra['company_id'] = !empty($v['company_id']) ? $v['company_id'] : 0;

            if (isset($v['is_edp'])) {
                $extra['is_edp'] = $v['is_edp'];
            }
            if (isset($v['edp_shipping'])) {
                $extra['edp_shipping'] = $v['edp_shipping'];
            }
            if (isset($v['discount'])) {
                $extra['discount'] = $v['discount'];
            }
            if (isset($v['base_price'])) {
                $extra['base_price'] = floatval($v['base_price']);
            }
            if (!empty($v['promotions'])) {
                $extra['promotions'] = $v['promotions'];
            }
            if (!empty($v['stored_price'])) {
                $extra['stored_price'] = $v['stored_price'];
            }

            if (!empty($v['product_options'])) {
                $_options = fn_get_product_options($v['product_id']);
                if (!empty($_options)) {
                    foreach ($_options as $option_id => $option) {
                        if (!isset($v['product_options'][$option_id])) {
                            $v['product_options'][$option_id] = '';
                        }
                    }
                }

                $extra['product_options'] = $v['product_options'];
                $cart_id = fn_generate_cart_id($v['product_id'], array('product_options' => $v['product_options']), true);
                $tracking = db_get_memoized_field("SELECT tracking FROM ?:products WHERE product_id = ?i", $v['product_id']);

                if ($tracking == 'O') {
                    $product_code = db_get_field("SELECT product_code FROM ?:product_options_inventory WHERE combination_hash = ?i", $cart_id);
                }

                $extra['product_options_value'] = fn_get_selected_product_options_info($v['product_options']);
            } else {
                $v['product_options'] = array();
            }

            if (empty($product_code)) {
                $product_code = db_get_memoized_field("SELECT product_code FROM ?:products WHERE product_id = ?i", $v['product_id']);
            }

            // Check the cart custom files
            if (isset($extra['custom_files'])) {
                $dir_path = 'order_data/' . $order_id;

                foreach ($extra['custom_files'] as $option_id => $files) {
                    if (is_array($files)) {
                        $customFilesStorage = container()->get('Wizacha\Storage\CustomFilesStorageService');
                        foreach ($files as $file_id => $file) {
                            $file['path'] = 'sess_data/' . fn_basename($file['path']);

                            $customFilesStorage->copy($file['path'], $dir_path . '/' . $file['file']);

                            $customFilesStorage->delete($file['path']);
                            $customFilesStorage->delete($file['path'] . '_thumb');

                            $extra['custom_files'][$option_id][$file_id]['path'] = $dir_path . '/' . $file['file'];
                        }
                    }
                }
            }

            $extra['w_green_tax'] = db_get_memoized_field("SELECT w_green_tax FROM ?:products WHERE product_id = ?i", $v['product_id']);

            $supplier_reference = null;
            if(\array_key_exists('product_id', $v) === true){
                $supplier_reference = db_get_field("SELECT supplier_reference FROM ?:product_options_inventory WHERE product_id = ?i AND combination_hash =?i", $v['product_id'], $cart_id);
                if (strlen($supplier_reference) === 0){
                    $supplier_reference = db_get_memoized_field("SELECT w_supplier_ref FROM ?:products WHERE product_id = ?i", $v['product_id']);
                }
            }
            $order_details = array (
                'item_id' => $k,
                'order_id' => $order_id,
                'product_id' => $v['product_id'],
                'product_code' => $product_code,
                'price' => (!empty($v['stored_price']) && $v['stored_price'] == 'Y') ? $v['price'] - $v['discount'] : $v['price'],
                'amount' => $v['amount'],
                'comment' => $v['comment'],
                'extra' => serialize($extra),
                'supplier_reference' => $supplier_reference,
            );

            db_query("INSERT INTO ?:order_details ?e", $order_details);
        }
    }

}

function fn_update_order_data($order_id, $cart)
{
    $_data = array();
    $clear_types = array();

    if (!empty($cart['product_groups'])) {

        // Save products groups
        $_data[] = array (
            'order_id' => $order_id,
            'type' => OrderDataType::GROUP_INFO()->getValue(),
            'data' => serialize($cart['product_groups']),
        );

        // Save shipping information
        $chosen_shippings = array();
        foreach ($cart['product_groups'] as $group) {
            $group_shipping = !empty($group['chosen_shippings']) ? $group['chosen_shippings'] : array();
            $chosen_shippings = array_merge($chosen_shippings, $group_shipping);
        }

        fn_apply_stored_shipping_rates($cart, $order_id);
        $_data[] = array (
            'order_id' => $order_id,
            'type' => OrderDataType::SHIPPING_INFO()->getValue(),
            'data' => serialize($chosen_shippings)
        );
    }

    // Save taxes
    if (!empty($cart['taxes'])) {
        $_data[] = array (
            'order_id' => $order_id,
            'type' => OrderDataType::TAX_INFO()->getValue(),
            'data' => serialize($cart['taxes']),
        );
    } elseif (isset($cart['taxes'])) {
        $clear_types[] = OrderDataType::TAX_INFO()->getValue();
    }

    // Save payment information
    if (isset($cart['payment_info'])) {
        $_data[] = array (
            'order_id' => $order_id,
            'type' => OrderDataType::PAYMENT_INFO()->getValue(),
            'data' => fn_encrypt_text(serialize($cart['payment_info'])),
        );
    }

    // Save coupons information
    if (!empty($cart['coupons'])) {
        $_data[] = array (
            'order_id' => $order_id,
            'type' => OrderDataType::COUPON_INFO()->getValue(),
            'data' => serialize($cart['coupons']),
        );
    } elseif (isset($cart['coupons'])) {
        $clear_types[] = OrderDataType::COUPON_INFO()->getValue();
    }

    if (!empty($clear_types)) {
        db_query("DELETE FROM ?:order_data WHERE order_id = ?i AND type IN (?a)", $order_id, $clear_types);
    }

    if (!empty($_data)) {
        db_query("REPLACE INTO ?:order_data ?m", $_data);
    }

    return true;
}

/**
 * Places an order
 *
 * @param array $cart Array of the cart contents and user information necessary for purchase
 * @param array $auth Array of user authentication data (e.g. uid, etc.)
 * @param string $action Current action. Can be empty or "save"
 * @param int $issuer_id
 * @param int $parent_order_id
 * @return array [order_id, bool] in case of success, otherwise False
 */
function fn_place_order(&$cart, &$auth, $action = '', $issuer_id = null, $parent_order_id = 0)
{
    if (fn_cart_is_empty($cart)) {
        return [false, false];
    }

    $cart['parent_order_id'] = $parent_order_id;

    // Remove unallowed chars from cc number
    if (!empty($cart['payment_info']['card_number'])) {
        $cart['payment_info']['card_number'] = str_replace(array(' ', '-'), '', $cart['payment_info']['card_number']);
    }

    try {
        if (empty($cart['order_id'])) {
            $cart['user_id'] = $auth['user_id'];
            $cart['tax_exempt'] = $auth['tax_exempt'];
            $cart['issuer_id'] = $issuer_id;
            // Create order
            list($order_id, $order_status, $isParentOrder) = fn_update_order($cart);
        } else {
            // Update order
            list($order_id, $order_status, $isParentOrder) = fn_update_order($cart, $cart['order_id']);
        }
    } catch (PickupPointException $exception) {
        return [$order_id, false];
    }

    // Send an email if the country tax is undefined
    if (false === $isParentOrder) {
        $companyId = reset($cart['product_groups'])['company_id'];
        $companyCountry = fn_get_company_data($companyId)['country'];
        $internationalTaxService = container()->get('marketplace.international_tax.shipping');

        if(true === \is_null($internationalTaxService->getOneByCountryCode($companyCountry))) {
            $internationalTaxService->sendMissingTaxConfigurationEmail($order_id);
        }
    }

    if (!empty($order_id)) {

        // If customer is not logged in, store order ids in the session
        if (empty($auth['user_id'])) {
            $auth['order_ids'][] = $order_id;
        }

        // If order total is zero, just save the order without any processing procedures
        if (floatval($cart['total']) == 0) {
            $action = 'save';
            $order_status = 'P';
        }

        $is_processor_script = false;
        if ($action != 'save') {
            $is_processor_script = fn_check_processor_script($cart['payment_id'], true);
        }

        if (!$is_processor_script && $order_status == STATUS_INCOMPLETED_ORDER) {
            $order_status = 'O';
        }

        if ($order_status == STATUS_PARENT_ORDER) {
            $notification_rules = fn_get_notification_rules(array(), true);
        } else {
            $notification_rules = [];
        }

        // Set new order status
        fn_change_order_status($order_id, $order_status, '', $notification_rules, true, $auth);

        $cart['processed_order_id'] = array();
        $cart['processed_order_id'][] = $order_id;

        if (!$parent_order_id && count($cart['product_groups']) > 1) {
            $child_orders = fn_place_suborders($order_id, $cart, $auth, $action, $issuer_id);
            // Now that all suborders are created, we can freeze the parent order
            container()->get('app.order.amount.calculator')->freezeParentOrder($order_id);

            array_unshift($child_orders, $order_id);
            $cart['processed_order_id'] = $child_orders;
        }

        return [$order_id, $action != 'save'];
    }
}

function fn_place_suborders($order_id, $cart, &$auth, $action, $issuer_id)
{
    $order_ids = array();
    $rewrite_order_id = empty($cart['rewrite_order_id']) ? array() : $cart['rewrite_order_id'];
    foreach ($cart['product_groups'] as $key_group => $group) {
        $_cart = $cart;
        $total_products_price = 0;
        $total_shipping_cost = 0;
        $total_company_part = 0;
        foreach ($group['products'] as $product) {
            $total_products_price += $product['price'];
        }

        //we remove from the current basket'copy every products which are not in the current group
        foreach ($_cart['products'] as $cart_id => $product) {
            if (!in_array($cart_id, array_keys($group['products']))) {
                unset($_cart['products'][$cart_id]);
            }
        }

        //we manage the $total_shipping_cost and redefine the chosen_shipping of the current basket'copy
        if (!empty($_cart['chosen_shipping'][$key_group])) {

            $chosen_shipping_id = $_cart['chosen_shipping'][$key_group];

            if (empty($group['chosen_shippings'])) {
                $total_shipping_cost += $group['shippings'][$chosen_shipping_id]['rate'];
            } else {
                foreach ($group['chosen_shippings'] as $shipping) {
                    $total_shipping_cost += $shipping['rate'];
                }
            }

            $_cart['chosen_shipping'] = array($chosen_shipping_id);

        } else {
            $_cart['chosen_shipping'] = array();
        }

        if (($cart['subtotal'] + $cart['shipping_cost']) == 0) {
            $total_company_part = 0;
        } else {
            $total_company_part = (($total_products_price + $total_shipping_cost) * 100) / ($cart['subtotal'] + $cart['shipping_cost']);
        }
        $_cart['payment_surcharge'] = $total_company_part * $cart['payment_surcharge'] / 100;
        $_cart['recalculate'] = true;
        if (empty($_cart['stored_shipping'])) {
            $_cart['calculate_shipping'] = true;
        }
        $_cart['rewrite_order_id'] = array();
        if ($next_id = array_shift($rewrite_order_id)) {
            $_cart['rewrite_order_id'][] = $next_id;
        }

        $_cart['subtotal_discount'] = $group['subtotal_discount'] ?? 0;
        $_cart['company_id'] = $group['company_id'];
        $_cart['parent_order_id'] = $order_id;


        if ($cart['total'] == 0) {
            $_cart['total'] = 0;
        }

        $_cart['product_groups'] = array($group);

        //the values of $group are from $basket (see fn_calculate_cart_content)
        //so we override the datas from the copy of the original order
        if (is_array($group['chosen_shippings']) && count($group['chosen_shippings']) > 0) {
            $_cart['shipping_ids'] = reset($group['chosen_shippings'])['shipping_id'];
        }
        $_cart['shipping_cost'] = $group['shipping_cost'];
        $_cart['subtotal'] = $group['subtotal'];
        $_cart['total'] = $group['total'];

        if ($group['all_edp_free_shipping']) {
            $_cart['shipping_required'] = false;
        }

        list($order_ids[],) = fn_place_order($_cart, $auth, $action, $issuer_id, $order_id);
    }

    return $order_ids;
}

//
// Store cart content in the customer's profile
//
function fn_save_cart_content(&$cart, $user_id, $type = 'C', $user_type = 'R')
{
    if (empty($user_id)) {
        if (fn_get_session_data('cu_id')) {
            $user_id = fn_get_session_data('cu_id');
        } else {
            $user_id = fn_crc32(uniqid(TIME));
            fn_set_session_data('cu_id', $user_id, COOKIE_ALIVE_TIME);
        }
        $user_type = 'U';
    }

    if (!empty($user_id)) {
        $condition = db_quote("user_id = ?i AND type = ?s AND user_type = ?s", $user_id, $type, $user_type);

        db_query("DELETE FROM ?:user_session_products WHERE " . $condition);
        if (!empty($cart['products']) && is_array($cart['products'])) {
            $_cart_prods = $cart['products'];
            foreach ($_cart_prods as $_item_id => $_prod) {
                $_cart_prods[$_item_id]['user_id'] = $user_id;
                $_cart_prods[$_item_id]['timestamp'] = TIME;
                $_cart_prods[$_item_id]['type'] = $type;
                $_cart_prods[$_item_id]['user_type'] = $user_type;
                $_cart_prods[$_item_id]['item_id'] = $_item_id;
                $_cart_prods[$_item_id]['item_type'] = 'P';
                $_cart_prods[$_item_id]['extra'] = serialize($_prod);
                $_cart_prods[$_item_id]['amount'] = empty($_cart_prods[$_item_id]['amount']) ? 1 : $_cart_prods[$_item_id]['amount'];
                $_cart_prods[$_item_id]['session_id'] = Session::getId();
                $ip = fn_get_ip();
                $_cart_prods[$_item_id]['ip_address'] = $ip['host'];

                if (!empty($_cart_prods[$_item_id])) {
                    db_query('REPLACE INTO ?:user_session_products ?e', $_cart_prods[$_item_id]);
                }
            }
        }
    }

    return true;
}

/**
 * Extract cart content from the customer's profile.
 * $type : C - cart, W - wishlist
 *
 * @param array $cart
 * @param integer $user_id
 * @param string $type
 *
 * @return void
 */
function fn_extract_cart_content(&$cart, $user_id, $type = 'C', $user_type = 'R')
{
    $auth = & $_SESSION['auth'];

    // Restore cart content
    if (!empty($user_id)) {
        $item_types = fn_get_cart_content_item_types();
        $condition = db_quote("user_id = ?i AND type = ?s AND user_type = ?s AND item_type IN (?a)", $user_id, $type, $user_type, $item_types);

        $_prods = db_get_hash_array("SELECT * FROM ?:user_session_products WHERE " . $condition, 'item_id');
        if (!empty($_prods) && is_array($_prods)) {
            $cart['products'] = empty($cart['products']) ? array() : $cart['products'];
            foreach ($_prods as $_item_id => $_prod) {
                $_prod_extra = unserialize($_prod['extra']);
                unset($_prod['extra']);
                $cart['products'][$_item_id] = empty($cart['products'][$_item_id]) ? fn_array_merge($_prod, $_prod_extra, true) : $cart['products'][$_item_id];
            }
        }
    }

    if ($type == 'C') {
        fn_calculate_cart_content($cart, $auth, 'S', false, 'I');
    }
}

/**
 * get cart content item types
 * @return array
 */
function fn_get_cart_content_item_types()
{
    return array('P');
}

/**
 * Generate title string for order details page
 *
 * @param int $order_id order identifier
 * @return string
 */
function fn_get_order_name($order_id)
{
    $total = db_get_field("SELECT total FROM ?:orders WHERE order_id = ?i", $order_id);
    if ($total == '') {
        return false;
    }

    if (Registry::get('settings.General.alternative_currency') == 'use_selected_and_alternative') {
        $result = fn_format_price_by_currency($total, CART_PRIMARY_CURRENCY);
        if (CART_SECONDARY_CURRENCY != CART_PRIMARY_CURRENCY) {
            $result .= ' (' . fn_format_price_by_currency($total) . ')';
        }
    } else {
        $result = fn_format_price_by_currency($total);
    }

    return $order_id . ' - ' . $result;
}

/**
 * Gets order paid statuses
 *
 * @return array Available paid statuses
 */
function fn_get_order_paid_statuses()
{
    return db_get_fields('SELECT status FROM ?:status_data WHERE type = ?s AND param = ?s AND value = ?s', 'O', 'inventory', 'D');
}

function fn_format_price_by_currency($price, $currency_code = CART_SECONDARY_CURRENCY)
{
    $currencies = Registry::get('currencies');
    $currency = $currencies[$currency_code];
    $result = fn_format_rate_value($price, 'F', $currency['decimals'], $currency['decimals_separator'], $currency['thousands_separator'], $currency['coefficient']);
    if ($currency['after'] == 'Y') {
        $result .= ' ' . $currency['symbol'];
    } else {
        $result = $currency['symbol'] . $result;
    }

    return $result;
}

/**
 * Returns true if order exists and is unique
 * @param $order_id
 * @return bool
 */
function fn_order_exists($order_id)
{
    $order = db_get_row("SELECT order_id FROM ?:orders WHERE ?:orders.order_id = ?i", $order_id);

    if(count($order) === 1) {
        return true;
    }
}

/**
 * Get order info
 * @deprecated Instead use the OrderService to get an order, or use OrderService->overrideLegacyOrder() if you really
 * need the old legacy array
 */
function fn_get_order_info($order_id, $native_language = false, $format_info = true, $get_edp_files = false, $skip_static_values = false, $auth = null, $force_area = null)
{
    if (!empty($order_id)) {

        if (\is_null($force_area)) {
            $area = defined('AREA') && AREA !== null ? AREA : 'C';
        } else {
            $area = $force_area;
        }
        $condition = fn_get_company_condition('?:orders.company_id');
        $condition = '?:orders.order_id = ?i '.$condition;
        $userId = $auth ? $auth['user_id'] : $_SESSION['auth']['user_id'];
        // If the user id is not found with previous method, the user should use API. try with new method
        $userId = $userId ?: container()->get('marketplace.user_service')->getCurrentUserId();
        $condition = \Wizacha\Order::statusCondition(
            \Wizacha\Company::runtimeID($area, $_SESSION ?? [], \Wizacha\Registry::defaultInstance()),
            $area,
            $userId,
            $condition
        );

        $order = db_get_row("SELECT * FROM ?:orders WHERE $condition", $order_id);
        if (!empty($order)) {
            $lang_code = ($native_language == true) ? $order['lang_code'] : (string) GlobalState::interfaceLocale();
            $order['adjustable'] = false;
            $order['payment_method'] = fn_get_payment_method_data($order['payment_id'], $lang_code);
            // Get additional profile fields
            $additional_fields = db_get_memoized_hash_single_array(
                "SELECT field_id, value FROM ?:profile_fields_data "
                . "WHERE object_id = ?i AND object_type = 'O'",
                array('field_id', 'value'), $order_id
            );
            $order['fields'] = $additional_fields;
            $order['transaction_reference'] = container()->get('marketplace.transaction.transaction_service')->findBankWireTransactionReference($order_id);
            $order['products'] = db_get_memoized_hash_array(
                "SELECT ?:order_details.*, ?:product_descriptions.product, ?:order_details.supplier_reference as w_supplier_ref, ?:products.status as product_status, ?:products.max_price_adjustment, ?:products.weight as weight FROM ?:order_details "
                . "LEFT JOIN ?:product_descriptions ON ?:order_details.product_id = ?:product_descriptions.product_id AND ?:product_descriptions.lang_code = ?s "
                . "LEFT JOIN ?:products ON ?:order_details.product_id = ?:products.product_id "
                . "WHERE ?:order_details.order_id = ?i ORDER BY ?:product_descriptions.product",
                'item_id', $lang_code, $order_id
            );

            $order['promotions'] = unserialize($order['promotions']);
            if (\is_array($order['promotions'])) {
                foreach ($order['promotions'] as $promoId => $promoData)
                {
                    if (is_array($promoData['bonus'])) {
                        foreach($promoData['bonus'] as $id => $bonus)
                        {
                            if (isset($bonus['maxAmount']) && is_int($bonus['maxAmount']))
                            {
                                $order['promotions'][$promoId]['bonus'][$id]['maxAmount'] = new Money($bonus['maxAmount']);
                            }
                        }
                    }
                }
            }

            // Get additional data
            $additional_data = db_get_hash_single_array("SELECT type, data FROM ?:order_data WHERE order_id = ?i", array('type', 'data'), $order_id);

            $order['taxes'] = array();
            $order['tax_subtotal'] = 0;
            $order['display_shipping_cost'] = $order['shipping_cost'];

            // Replace country, state and title values with their descriptions
            $order_company_id = isset($order['company_id']) ? $order['company_id'] : ''; // company_id will be rewritten by user field, so need to save it.

            // Fill user info
            $userData = fn_get_all_user_profiles($order['user_id']);
            $order['is_professional'] = $userData['is_professional'];
            $order['legal_identifier'] = $userData['legal_identifier'];
            $order['job_title'] = $userData['job_title'];
            $order['intra_european_community_vat'] = $userData['intra_european_community_vat'];
            $order['user_company'] = fn_get_user_company_name($order['user_id']);

            fn_add_user_data_descriptions($order, $lang_code);
            $order['company_id'] = $order_company_id;

            $order['need_shipping'] = false;
            $deps = array();

            // Get shipping information
            if (!empty($additional_data[OrderDataType::SHIPPING_INFO()->getValue()])) {
                $order['shipping'] = unserialize($additional_data[OrderDataType::SHIPPING_INFO()->getValue()]);

                foreach ($order['shipping'] as $key => $v) {
                    $shipping_id = isset($v['shipping_id']) ? $v['shipping_id'] : 0;
                    $shipping_name = fn_get_shipping_name($shipping_id, $lang_code);
                    if ($shipping_name) {
                        $order['shipping'][$key]['shipping'] = $shipping_name;
                    }
                }
            }

            // Get workflow state
            if (container()->getParameter('feature.activate_workflow_translation')) {
                $translateKey = "workflow_{$order['workflow_current_module_name']}_{$order['workflow_current_step_name']}_{$order['workflow_status']}";
                $order['workflow'] = str_replace('-', '_', $translateKey);
            }

            // Get shipments common information
            $order['shipment_ids'] = db_get_fields(
                "SELECT sh.shipment_id FROM ?:shipments AS sh LEFT JOIN ?:shipment_items AS s_items ON (sh.shipment_id = s_items.shipment_id) "
                . "WHERE s_items.order_id = ?i GROUP BY s_items.shipment_id",
                $order_id
            );

            // Get order refund status
            $order['doctrine_order_refund_status'] = db_get_field(
                "SELECT MAX(status) FROM doctrine_order_refunds WHERE order_id = ?i GROUP BY order_id",
                $order_id
            );

            $_products = db_get_array("SELECT item_id, SUM(amount) AS amount FROM ?:shipment_items WHERE order_id = ?i GROUP BY item_id", $order_id);
            $shipped_products = array();

            if (!empty($_products)) {
                foreach ($_products as $_product) {
                    $shipped_products[$_product['item_id']] = $_product['amount'];
                }
            }
            unset($_products);

            foreach ($order['products'] as $k => $v) {
                //Check for product existance
                if (empty($v['product'])) {
                    $order['products'][$k]['deleted_product'] = true;
                } else {
                    $order['products'][$k]['deleted_product'] = false;
                }

                if($v['max_price_adjustment'] > 0) {
                    $order['adjustable'] = true;
                }

                $order['products'][$k]['discount'] = 0;

                $v['extra'] = @unserialize($v['extra']);
                if ($order['products'][$k]['deleted_product'] == true && !empty($v['extra']['product'])) {
                    $order['products'][$k]['product'] = $v['extra']['product'];
                } else {
                    $order['products'][$k]['product'] = fn_get_product_name($v['product_id']);
                }

                $order['products'][$k]['company_id'] = empty($v['extra']['company_id']) ? 0 : $v['extra']['company_id'];

                if (!empty($v['extra']['discount']) && floatval($v['extra']['discount'])) {
                    $order['products'][$k]['discount'] = $v['extra']['discount'];
                    $order['use_discount'] = true;
                }

                if (!empty($v['extra']['promotions'])) {
                    $order['products'][$k]['promotions'] = $v['extra']['promotions'];
                }

                if (isset($v['extra']['base_price'])) {
                    $order['products'][$k]['base_price'] = floatval($v['extra']['base_price']);
                } else {
                    $order['products'][$k]['base_price'] = $v['price'];
                }

                if (!empty($v['extra']['w_green_tax'])) {
                    $order['products'][$k]['green_tax'] = $v['extra']['w_green_tax'];
                }

                $order['products'][$k]['original_price'] = $order['products'][$k]['base_price'];

                // Form hash key for this product
                $order['products'][$k]['cart_id'] = $v['item_id'];
                $deps['P_'.$order['products'][$k]['cart_id']] = $k;

                // Unserialize and collect product options information
                if (!empty($v['extra']['product_options'])) {
                    if ($format_info == true) {
                        if (!empty($v['extra']['product_options_value'])) {
                            $order['products'][$k]['product_options'] = $v['extra']['product_options_value'];
                        } else {
                            $order['products'][$k]['product_options'] = fn_get_selected_product_options_info($v['extra']['product_options'], $lang_code);
                        }
                    }
                }

                $order['products'][$k]['extra'] = $v['extra'];
                $order['products'][$k]['tax_value'] = 0;
                $order['products'][$k]['display_subtotal'] = $order['products'][$k]['subtotal'] = ($v['price'] * $v['amount']);

                // Get information about edp
                if ($get_edp_files == true && $order['products'][$k]['extra']['is_edp'] == 'Y') {
                    $order['products'][$k]['files'] = db_get_memoized_array(
                        "SELECT ?:product_files.file_id, ?:product_files.activation_type, ?:product_files.max_downloads, "
                        . "?:product_file_descriptions.file_name, ?:product_file_ekeys.active, ?:product_file_ekeys.downloads, "
                        . "?:product_file_ekeys.ekey, ?:product_file_ekeys.ttl FROM ?:product_files "
                        . "LEFT JOIN ?:product_file_descriptions ON ?:product_file_descriptions.file_id = ?:product_files.file_id "
                        . "AND ?:product_file_descriptions.lang_code = ?s "
                        . "LEFT JOIN ?:product_file_ekeys ON ?:product_file_ekeys.file_id = ?:product_files.file_id "
                        . "AND ?:product_file_ekeys.order_id = ?i WHERE ?:product_files.product_id = ?i",
                        $lang_code, $order_id, $v['product_id']
                    );
                }

                // Get shipments information
                // If current edition is FREE, we still need to check shipments accessibility (need to display promotion link)
                if (isset($shipped_products[$k])) {
                    $order['products'][$k]['shipped_amount'] = $shipped_products[$k];
                    $order['products'][$k]['shipment_amount'] = $v['amount'] - $shipped_products[$k];

                } else {
                    $order['products'][$k]['shipped_amount'] = 0;
                    $order['products'][$k]['shipment_amount'] = $v['amount'];
                }

                if ($order['products'][$k]['shipped_amount'] < $order['products'][$k]['amount']) {
                    if (!empty($order['shipping'])) {
                        $group_key = empty($v['extra']['group_key']) ? 0 : $v['extra']['group_key'];
                        $order['shipping'][$group_key]['need_shipment'] = true;
                    } else {
                        $order['need_shipment'] = true;
                    }
                }

                // Check if the order needs the shipping method
                if (!($v['extra']['is_edp'] == 'Y' && (!isset($v['extra']['edp_shipping']) || $v['extra']['edp_shipping'] != 'Y'))) {
                    $order['need_shipping'] = true;
                }

                // Adds flag that defines if product page is available
                $order['products'][$k]['is_accessible'] = fn_is_accessible_product($v);
            }

            // Unserialize and collect taxes information
            if (!empty($additional_data[OrderDataType::TAX_INFO()->getValue()])) {
                $order['taxes'] = unserialize($additional_data[OrderDataType::TAX_INFO()->getValue()]);
                if (is_array($order['taxes'])) {
                    foreach ($order['taxes'] as  $tax_id => $tax_data) {
                        $order['taxes'][$tax_id]["tax_subtotal"] = 0;
                        foreach ($tax_data['applies'] as $_id => $value) {
                            if (strpos($_id, 'P_') !== false) {
                                if (isset($deps[$_id])) {
                                    $order['taxes'][$tax_id]["tax_subtotal"] += $value;
                                    $order['products'][$deps[$_id]]['tax_value'] += $value;
                                    if ($tax_data['price_includes_tax'] != 'Y') {
                                        $order['products'][$deps[$_id]]['subtotal'] += $value;
                                        $order['products'][$deps[$_id]]['display_subtotal'] += (Registry::get('settings.Appearance.cart_prices_w_taxes') == 'Y') ? $value : 0;
                                        $order['tax_subtotal'] += $value;
                                    }
                                } else {
                                    unset($order['taxes'][$tax_id]['applies'][$_id]);
                                }
                            }
                            if (strpos($_id, 'S_') !== false && Registry::get('settings.Appearance.cart_prices_w_taxes') == 'Y') {
                                if ($tax_data['price_includes_tax'] != 'Y') {
                                    $order['display_shipping_cost'] += $value;
                                }
                            }
                        }
                    }
                } else {
                    $order['taxes'] = array();
                }
            }

            if (!empty($additional_data[OrderDataType::COUPON_INFO()->getValue()])) {
                $order['coupons'] = unserialize($additional_data[OrderDataType::COUPON_INFO()->getValue()]);
            }

            if (!empty($order['issuer_id'])) {
                $order['issuer_data'] = fn_get_user_short_info($order['issuer_id']);
            }

            $previousSubtotal = $order['subtotal'];

            // Recalculate subtotal
            $order['subtotal'] = $order['display_subtotal'] = 0;
            foreach ($order['products'] as $v) {
                $order['subtotal'] += $v['subtotal'];
                $order['display_subtotal'] += $v['display_subtotal'];
            }

            // fix round error on display key (allow 1 cent error)
            if (abs($previousSubtotal - $order['display_subtotal']) <= 0.01) {
                $order['display_subtotal'] = $previousSubtotal;
            }

            // Unserialize and collect payment information
            if (!empty($additional_data[OrderDataType::PAYMENT_INFO()->getValue()])) {
                $order['payment_info'] = unserialize(fn_decrypt_text($additional_data[OrderDataType::PAYMENT_INFO()->getValue()]));
            }

            if (empty($order['payment_info']) || !is_array($order['payment_info'])) {
                $order['payment_info'] = array();
            }

            // Get shipping information
            if (!empty($additional_data[OrderDataType::GROUP_INFO()->getValue()])) {
                $order['product_groups'] = unserialize($additional_data[OrderDataType::GROUP_INFO()->getValue()]);
                if (false === $order['product_groups']) {
                    trigger_error("Impossible to unserialize shipping information of order $order_id");
                }
            }

            $order['doc_ids'] = db_get_hash_single_array("SELECT type, doc_id FROM ?:order_docs WHERE order_id = ?i", array('type', 'doc_id'), $order_id);

            $order['do_not_create_invoice'] = (bool) $order['do_not_create_invoice'];
        }

        fn_rma_get_order_info($order, $additional_data);
        $order = \Wizacha\C2C\Order::setCode($order, $additional_data);

        if (!empty($order)) {
            //-----------------------------------------------------------------------
            // Dénormalisation des types de shipping pour en simplifier l'utilisation
            //-----------------------------------------------------------------------
            $shippingId = $order['shipping'][0]['shipping_id'] ?? null;

            // A compléter selon les besoins.
            $order['shipping_type'] = [
                'is_standard' => !is_null($shippingId) && \Wizacha\Shipping::isStandardDelivery($shippingId),
                'is_hand_delivery' => !is_null($shippingId) && \Wizacha\Shipping::isHandDelivery($shippingId),
            ];
        }

        // Renaming subtotal_discount in discount for the API and a better readability
        // NOTE: We can use the discount key because it not used for product discount anymore (deprecated use)
        if (!empty($order) && !empty($order['subtotal_discount']) ) {
            $order['discount'] = $order['subtotal_discount'];
        }

        if (\count($order) > 0) {
            //check if payment is refused by psp or bank
            $order['isPaymentRefused'] = false;
            $isPaymentRefused = container()->get('marketplace.transaction.transaction_service')->hasRefusedPayment($order_id);
            if ($isPaymentRefused === true && $order['status'] === OrderStatus::STANDBY_BILLING) {
                $order['isPaymentRefused'] = true;
            }
        }

        return $order;
    }

    return false;
}

/**
 * @param mixed[] $data @see fn_get_order_info
 * @param bool $isAdmin
 *
 * @return mixed[]
 */
function fn_api_order_info(array $data, bool $isAdmin = false): array
{
    $data = fn_w_object_filter($data, 'order', 'api', 'display_objects');
    fn_w_fields_cast($data);
    $data = fn_w_convert_fields($data, 'order', 'send', 'convert_fields_api');

    if (is_array($data) && count($data) > 0) {
        // Payment
        /** @var Order $order */
        $order = container()->get('marketplace.order.order_service')->getOrder($data['order_id']);
        $data['total'] = $order->getTotal()->getConvertedAmount();
        $data['payment'] = $order->getPayment()->expose();
        $data['workflow'] = $order->deduceWorkflowTranslationKey();
        $data['customer_total'] = $order->getCustomerTotal()->getConvertedAmount();
        $data['marketplace_discount_total'] = $order->getMarketplaceDiscountTotal()->getConvertedAmount();
        $data['company_name'] = fn_get_company_name($data['company_id']);
        $data['refunded'] = $data['refund_status'] > 0 ;
        unset($data['refund_status']);
        $data['balance'] = $order->getBalanceTotal()->getConvertedAmount();
        if (true === \array_key_exists('products', $data)) {
            // Add declination_id to product.
            foreach ($order->getItems() as $orderItem) {
                if (false === \array_key_exists($orderItem->getItemId(), $data['products'])) {
                    continue;
                }

                $data['products'][$orderItem->getItemId()]['declination_id'] = $orderItem->getDeclinationId();
            }
        }


        if (true === \array_key_exists('promotions', $data)) {
            // Add bonuses field with all bonuses and set bonus with the first bonus to avoid BC break
            foreach ($data['promotions'] as &$promotion) {
                $promotion['bonuses'] = $promotion['bonus'];
                $promotion['bonus'] = $promotion['bonus'][0];
            }
            unset($promotion);
        }

        // Subscription
        $data['is_subscription_initiator'] = $order->isSubscriptionInitiator();

        // Amounts taxes details
        $data[AmountsTaxesDetails::TOTALS] = $order
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::TOTALS)
            ->expose();

        $data[AmountsTaxesDetails::SHIPPING_COSTS] = $order
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::SHIPPING_COSTS)
            ->expose();

        $data[AmountsTaxesDetails::COMMISSIONS] = $order
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::COMMISSIONS)
            ->expose();

        $data[AmountsTaxesDetails::VENDOR_SHARE] = $order
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::VENDOR_SHARE)
            ->expose();

        if (\array_key_exists('extra', $data) === true) {
            $data['extra'] = \json_decode($data['extra'], true);
        }

        $data['parent_order_id'] = $data['parent_order_id'] > 0 ? \intval($data['parent_order_id']) : null;
        if ($isAdmin === false) {
            unset($data['parent_order_id']);
        }
    }

    return $data;
}

/**
 * Check that the first paid order
 *
 * @param array $order Order data
 *
 */
function fn_check_first_order(&$order)
{
    if (AREA == 'A' && !fn_get_storage_data('first_order') && $_SESSION['auth']['is_root'] == 'Y') {
        $status = !empty($order['status']) ? $order['status'] : '';

        if ($status == 'P' || $status == 'C') {
            $order['first_order'] = true;
            fn_set_storage_data('first_order', true);
            Registry::get('view')->assign('mode','notification');
        }
    }
}

/**
 * Checks if product is currently accessible for viewing
 *
 * @param array $product Product data
 * @return boolean Flag that defines if product is accessible
 */
function fn_is_accessible_product($product)
{
    $result = false;
    $status = db_get_memoized_field('SELECT status FROM ?:products WHERE product_id = ?i', $product['product_id']);
    if (!empty($status) && $status != "D") {
        $result = true;
    }
    return $result;
}

//
// Get order short info
//
function fn_get_order_short_info($order_id)
{
    if (!empty($order_id)) {
        $order = db_get_row("SELECT total, status, issuer_id, firstname, lastname, timestamp, is_parent_order FROM ?:orders WHERE order_id = ?i", $order_id);

        return $order;
    }

    return false;
}

/**
 * Change order status
 *
 * -------------------------------------------------------------------------
 * REMARQUE : cette fonction ne change pas le statut d'un order parent, mais
 * uniquement celui de tous ses orders enfants.
 * -------------------------------------------------------------------------
 *
 * @param int $order_id Order identifier
 * @param string $status_to New order status (one char)
 * @param string $status_from Old order status (one char)
 * @param array $force_notification Array with notification rules
 * @param boolean $place_order True, if this function have been called inside of fn_place_order function.
 * @return boolean
 */
function fn_change_order_status($order_id, $status_to, $status_from = '', $force_notification = array(), $place_order = false, $auth = null)
{
    /** @var MutexService */
    $mutexService = container()->get(MutexService::class);

    $orderStatusMutex = $mutexService->createBlockingMutex(
        Order::class,
        $order_id
    );

    $order_info = container()
        ->get('marketplace.order.order_service')
        ->overrideLegacyOrder($order_id, true, true, false, false, $auth);

    if (defined('CART_LOCALIZATION') && $order_info['localization_id'] && CART_LOCALIZATION != $order_info['localization_id']) {
        Registry::get('view')->assign('localization', fn_get_localization_data(CART_LOCALIZATION));
    }

    $order_statuses = fn_get_statuses(STATUSES_ORDER, array(), true, false, (string) GlobalState::interfaceLocale(), $order_info['company_id']);

    if (empty($status_from)) {
        $status_from = $order_info['status'];
    }

    if (empty($order_info) || empty($status_to) || $status_from == $status_to) {
        return false;
    }

    if ($order_info['is_parent_order'] == 'Y') {
        $child_ids = db_get_fields("SELECT order_id FROM ?:orders WHERE parent_order_id = ?i", $order_id);
        $res = $_res = true;
        foreach ($child_ids as $child_order_id) {
            $_res = fn_change_order_status($child_order_id, $status_to, '', $force_notification, $place_order);
        }
        $res = $res && $_res;

        return $res;
    }

    fn_bestsellers_change_order_status($status_to, $status_from, $order_info, $force_notification, $order_statuses);
    fn_rma_change_order_status($status_to, $status_from, $order_info);

    $order_info = \Wizacha\C2C\Order::onUpdateStatus($order_info, $status_to, \Wizacha\Registry::defaultInstance());

    fn_log_event('orders', 'status', array (
        'order_id' => $order_id,
        'status_from' => $status_from,
        'status_to' => $status_to,
        'company_id' => $order_info['company_id'],
    ));

    $appearance_type = $order_statuses[$status_to]['params']['appearance_type'];
    if (
        !empty($appearance_type)
        && (
            $appearance_type === AppearanceType::INVOICE()->getValue()
            || $appearance_type === AppearanceType::CREDIT_MEMO()->getValue()
        )
        && !db_get_field(
            "SELECT doc_id FROM ?:order_docs WHERE type = ?s AND order_id = ?i",
            $appearance_type,
            $order_id
        )
    ) {
        $_data = array (
            'order_id' => $order_id,
            'type' => $appearance_type
        );
        $order_info['doc_ids'][$appearance_type] = db_query("INSERT INTO ?:order_docs ?e", $_data);
    }

    // Check if we need to remove CC info
    if (!empty($order_statuses[$status_to]['params']['remove_cc_info']) && $order_statuses[$status_to]['params']['remove_cc_info'] == 'Y' && !empty($order_info['payment_info'])) {
        fn_cleanup_payment_info($order_id, $order_info['payment_info'], true);
    }

    $edp_data = fn_generate_ekeys_for_edp(array('status_from' => $status_from, 'status_to' => $status_to), $order_info);
    $order_info['status'] = $status_to;

    $set = db_quote("status = ?s", $status_to);
    $set .= ', w_last_status_change = NOW() ';

    db_query("UPDATE ?:orders SET $set WHERE order_id = ?i", $order_id);

    fn_order_notification($order_info, $edp_data, $force_notification);

    Config::dispatch(
        OrderEvents::UPDATED,
        new OrderStatusUpdated((int) $order_id, $status_from, $status_to)
    );

    $orderService = container()->get('marketplace.order.order_service');
    $order = $orderService->getOrder($order_id);

    if (container()->getParameter('feature.enable_yavin') === true) {
        $orderService->publishOrderCreated($status_from, $status_to, $order);
    }

    return true;
}

/**
 * Function generate edp ekeys for email notification
 *
 * @param array $statuses order statuses
 * @param array $order_info order information
 * @param array $active_files array with file download statuses
 * @return array $edp_data
 */

function fn_generate_ekeys_for_edp($statuses, $order_info, $active_files = array())
{
    $edp_data = array();
    $order_statuses = fn_get_statuses(STATUSES_ORDER, array(), true);

    foreach ($order_info['products'] as $v) {

        // Generate ekey if EDP is ordered
        if ((!empty($v['extra']['is_edp']) && $v['extra']['is_edp'] == 'Y')
            || container()->getParameter('feature.allow_files_on_non_edp_products')) {

            $activations = db_get_hash_single_array("SELECT activation_type, file_id FROM ?:product_files WHERE product_id = ?i", array('file_id', 'activation_type'), $v['product_id']);

            foreach ($activations as $file_id => $activation_type) {

                // Check if ekey already was generated for this file
                $_ekey = db_get_row("SELECT ekey, active, file_id, product_id, order_id, ekey FROM ?:product_file_ekeys WHERE file_id = ?i AND order_id = ?i", $file_id, $order_info['order_id']);
                if (!empty($_ekey)) {
                    // If order status changed to "Processed"
                    if (($activation_type == 'P') && !empty($statuses)) {
                        if ($order_statuses[$statuses['status_to']]['params']['inventory'] == 'D' && substr_count('O', $statuses['status_to']) == 0 && ($order_statuses[$statuses['status_from']]['params']['inventory'] != 'D' || substr_count('O', $statuses['status_from']) > 0)) {
                            $active_files[$v['product_id']][$file_id] = 'Y';
                        } elseif (($order_statuses[$statuses['status_to']]['params']['inventory'] != 'D' && substr_count('O', $statuses['status_from']) == 0 || substr_count('O', $statuses['status_to']) > 0) && $order_statuses[$statuses['status_from']]['params']['inventory'] == 'D') {
                            $active_files[$v['product_id']][$file_id] = 'N';
                        }
                    }

                    if (!empty($active_files[$v['product_id']][$file_id])) {
                        db_query('UPDATE ?:product_file_ekeys SET ?u WHERE file_id = ?i AND product_id = ?i AND order_id = ?i', array('active' => $active_files[$v['product_id']][$file_id]), $_ekey['file_id'], $_ekey['product_id'], $_ekey['order_id']);

                        if ($active_files[$v['product_id']][$file_id] == 'Y' && $_ekey['active'] !== 'Y') {
                            $edp_data[$v['product_id']]['files'][$file_id] = $_ekey;
                        }
                    }

                } else {
                    $_data = array (
                        'file_id' => $file_id,
                        'product_id' => $v['product_id'],
                        'ekey' => md5(uniqid(rand())),
                        'ttl' => (TIME + (Registry::get('settings.General.edp_key_ttl') * 60 * 60)),
                        'order_id' => $order_info['order_id'],
                        'activation' => $activation_type
                    );

                    // Activate the file if type is "Immediately" or "After full payment" and order statuses is from "paid" group
                    if ($activation_type == 'I' || !empty($active_files[$v['product_id']][$file_id]) && $active_files[$v['product_id']][$file_id] == 'Y' || ($activation_type == 'P' && $order_statuses[$statuses['status_to']]['params']['inventory'] == 'D' && substr_count('O', $statuses['status_to']) == 0 && ($order_statuses[$statuses['status_from']]['params']['inventory'] != 'D' || substr_count('O', $statuses['status_from']) > 0 ))) {
                        $_data['active'] = 'Y';
                        $edp_data[$v['product_id']]['files'][$file_id] = $_data;
                    }

                    db_query('REPLACE INTO ?:product_file_ekeys ?e', $_data);
                }

                if (!empty($edp_data[$v['product_id']]['files'][$file_id])) {
                    $edp_data[$v['product_id']]['files'][$file_id]['file_size'] = db_get_field("SELECT file_size FROM ?:product_files WHERE file_id = ?i", $file_id);
                    $edp_data[$v['product_id']]['files'][$file_id]['file_name'] = db_get_field("SELECT file_name FROM ?:product_file_descriptions WHERE file_id = ?i AND lang_code = ?s", $file_id, (string) GlobalState::interfaceLocale());
                }
            }
        }
    }

    return $edp_data;
}

/**
 * Updates order payment information
 *
 * @param int $order_id
 * @param array $pp_response Response from payment processor
 * @return boolean true
 */
function fn_update_order_payment_info($order_id, $pp_response)
{
    if (empty($order_id) || empty($pp_response) || !is_array($pp_response)) {
        return false;
    }

    $payment_info = db_get_field(
        "SELECT data FROM ?:order_data WHERE order_id = ?i AND type = ?s",
        $order_id,
        OrderDataType::PAYMENT_INFO()->getValue()
    );
    if (!empty($payment_info)) {
        $payment_info = unserialize(fn_decrypt_text($payment_info));
    } else {
        $payment_info = array();
    }

    foreach ($pp_response as $k => $v) {
        $payment_info[$k] = $v;
    }

    $data = array (
        'data' => fn_encrypt_text(serialize($payment_info)),
        'order_id' => $order_id,
        'type' => 'P'
    );

    db_query("REPLACE INTO ?:order_data ?e", $data);

    $child_orders_ids = db_get_fields("SELECT order_id FROM ?:orders WHERE parent_order_id = ?i", $order_id);
    if (!empty($child_orders_ids)) {
        foreach ($child_orders_ids as $child_id) {
            fn_update_order_payment_info($child_id, $pp_response);
        }
    }

    return true;
}

/**
 * Get shipping info
 *
 * @param int $shipping_id Shipping ID
 * @param string $lang_code Language code
 * @return array Shipping info
 */
function fn_get_shipping_info($shipping_id, $lang_code = null, $companyId = null)
{
    if (null === $companyId) {
        $companyId = Registry::get('runtime.company_id');
    }

    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $cacheKey = $shipping_id.$lang_code.$companyId;

    static $cache = [];
    if (AREA !== 'A' && !empty($cache[$cacheKey])) {
        return $cache[$cacheKey];
    }

    $fields = array(
        '?:shippings.*',
        '?:shipping_descriptions.shipping',
        '?:shipping_descriptions.delivery_time'
    );
    $join = db_quote(" LEFT JOIN ?:shipping_descriptions ON ?:shipping_descriptions.shipping_id = ?:shippings.shipping_id AND ?:shipping_descriptions.lang_code = ?s", $lang_code);
    $conditions = "";

    $fields[] = '?:shipping_descriptions.w_description';

    $shipping = array();
    if (!empty($shipping_id)) {
        $shipping = db_get_memoized_row("SELECT ?p FROM ?:shippings ?p WHERE ?:shippings.shipping_id = ?i ?p", implode(', ', $fields), $join, $shipping_id, $conditions);
    }

    if (!empty($shipping)) {
        $shipping['tax_ids'] = empty($shipping['tax_ids']) ? array() : fn_explode(',', $shipping['tax_ids']);
        $shipping['icon'] = fn_get_image_pairs($shipping['shipping_id'], 'shipping', 'M', true, true, $lang_code);

        if (!empty($shipping['service_params'])) {
            $shipping['service_params'] = unserialize($shipping['service_params']);
        }

        $destinations = array();
        if ($shipping['rate_calculation'] == 'M') {
            $destinations = fn_get_destinations();

            foreach ($destinations as $k => $v) {

                if (!empty($shipping['localization'])) { // check available destinations, but skip default destination
                    $_s = fn_explode(',', $shipping['localization']);
                    $_l = fn_explode(',', $v['localization']);
                    if (!array_intersect($_s, $_l)) {
                        continue;
                    }
                }
                $destinations[$k] = array_merge($destinations[$k], fn_get_shipping_rate($shipping_id, $v['destination_id'],  Registry::get('runtime.company_id')));
            }
        } else {
            $destinations[0] = db_get_memoized_row("SELECT rate_id, rate_value, destination_id FROM ?:shipping_rates WHERE shipping_id = ?i AND destination_id = 0", $shipping_id);
            $destinations[0] = array_merge($destinations[0], fn_get_shipping_rate($shipping_id, 0,  Registry::get('runtime.company_id')));
        }

        $shipping['rates'] = $destinations;

        if (AREA !== 'A') {
            $cache[$cacheKey] = $shipping;
        }
    }

    return $shipping;
}

/**
 * Get shipping rate
 *
 * @param int $shipping_id Shipping ID
 * @param int $destination_id location
 * @return array rate info
 */
function fn_get_shipping_rate($shipping_id, $destination_id, $company_id)
{
    $rate = [];
    if (isset($_REQUEST['product_id'])) {
        // Get shipping rates from product
        $rate = db_get_memoized_row("SELECT w_product_shipping_id AS rate_id, rate_value, destination_id, IF(rate_value = '', 0, 1) as rate_defined FROM ?:w_product_shipping_rates WHERE shipping_id = ?i AND destination_id = ?i AND product_id = ?i", $shipping_id, $destination_id, $_REQUEST['product_id']);

        // If product has specific rates, we get the threshold from the company rates table
        if ([]  !== $rate) {
            // For now we get the carriage paid threshold from the company shipping rates table
            $rate['carriage_paid_threshold'] = db_get_memoized_field(
                "SELECT carriage_paid_threshold
                 FROM ?:w_company_shipping_rates
                 WHERE shipping_id = ?i AND destination_id = ?i AND company_id = ?i",
                $shipping_id, $destination_id, $company_id
            );
        }
    }

    // If product has no specific rates, or if we filter by company
    // we get the rates from the company table
    if ((true === \array_key_exists('product_id', $_REQUEST) && [] === $rate)
        || false === \array_key_exists('product_id', $_REQUEST)) {
        // Get shipping rates from company rates
        $rate = db_get_memoized_row(
            "SELECT w_rate_id AS rate_id, rate_value, destination_id, IF(rate_value = '', 0, 1) as rate_defined, carriage_paid_threshold
             FROM ?:w_company_shipping_rates
             WHERE shipping_id = ?i AND destination_id = ?i AND company_id = ?i",
            $shipping_id, $destination_id, $company_id
        );
    }

    if (!empty($rate)) {
        $rate['rate_value'] = unserialize($rate['rate_value']);
    }

    if (empty($rate['rate_value']['C'][0])) {
        $rate['rate_value']['C'][0] = array();
    }
    if (empty($rate['rate_value']['W'][0])) {
        $rate['rate_value']['W'][0] = array();
    }
    if (empty($rate['rate_value']['I'][0])) {
        $rate['rate_value']['I'][0] = array();
    }
    if (empty($rate['carriage_paid_threshold'])) {
        $rate['carriage_paid_threshold'] = null;
    }

    return $rate;
}

//
// Get all shippings list
//
function fn_get_shippings($simple, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $conditions = '1';

    if (AREA == 'C') {
        $conditions .= " AND a.status = 'A'";
        $conditions .= fn_get_localizations_condition('a.localization');
    }

    if ($simple == true) {
        return db_get_memoized_hash_single_array("SELECT a.shipping_id, b.shipping FROM ?:shippings as a LEFT JOIN ?:shipping_descriptions as b ON a.shipping_id = b.shipping_id AND b.lang_code = ?s WHERE ?p ORDER BY a.position", array('shipping_id', 'shipping'), $lang_code, $conditions);
    } else {
        return db_get_memoized_array("SELECT a.shipping_id, a.min_weight, a.max_weight, a.position, a.status, b.shipping, b.delivery_time FROM ?:shippings as a LEFT JOIN ?:shipping_descriptions as b ON a.shipping_id = b.shipping_id AND b.lang_code = ?s WHERE ?p ORDER BY a.position", $lang_code, $conditions);
    }
}

/**
 * Gets shipping name
 *
 * @param int $shipping_id shipping identifier
 * @param string $lang_code 2-letter language code (e.g. 'en', 'ru', etc.)
 * @return string Shipping name if shipping identifier is not null; false otherwise
 */
function fn_get_shipping_name($shipping_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();

    if (!empty($shipping_id)) {
        return db_get_memoized_field("SELECT shipping FROM ?:shipping_descriptions WHERE shipping_id = ?i AND lang_code = ?s", $shipping_id, $lang_code);
    }

    return false;
}

/**
 * Create/Update shipping name
 *
 * @param array $shipping_data shipping info
 * @param int $shipping_id shipping identifier
 * @param string $lang_code 2-letter language code (e.g. 'en', 'ru', etc.)
 * @return string Shipping name if shipping identifier is not null; false otherwise
 */
function fn_update_shipping($shipping_data, $shipping_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    if (!empty($shipping_data['shipping']) || !empty($shipping_id)) {
        $shipping_data['localization'] = empty($shipping_data['localization']) ? '' : fn_implode_localizations($shipping_data['localization']);
        if (isset($shipping_data['tax_ids'])) {
            if (!empty($shipping_data['tax_ids'])) {
                $shipping_data['tax_ids'] = fn_create_set($shipping_data['tax_ids']);
            } else {
                unset($shipping_data['tax_ids']);
            }
        }
        unset($shipping_data['shipping_id']);

        // Sanitize shipping description
        if (true === \array_key_exists('w_description', $shipping_data) && $shipping_data['w_description'] !== null) {
            $purifierService = container()->get('purifier.default');
            $shipping_data['w_description'] = \trim($purifierService->purify($shipping_data['w_description']));
        }

        if (isset($shipping_data['service_params'])) {
            $shipping_data['service_params'] = serialize($shipping_data['service_params']);
        }

        if (!empty($shipping_id)) {
            $arow = db_query("UPDATE ?:shippings SET ?u WHERE shipping_id = ?i", $shipping_data, $shipping_id);
            db_query("UPDATE ?:shipping_descriptions SET ?u WHERE shipping_id = ?i AND lang_code = ?s", $shipping_data, $shipping_id, $lang_code);

            if ($arow === false) {
                fn_set_notification('E', __('error'), __('object_not_found', array('[object]' => __('shipping'))),'','404');
                $shipping_id = false;
            }
        } else {
            $shipping_id = $shipping_data['shipping_id'] = db_query("INSERT INTO ?:shippings ?e", $shipping_data);

            \Tygh\Languages\Helper::insertTranslations('shipping_descriptions', $lang_code, $shipping_data);
        }

        if ($shipping_id) {
            fn_attach_image_pairs('shipping', 'shipping', $shipping_id, $lang_code);
            if (!empty($shipping_data['rates'])) {
                fn_update_shipping_rates($shipping_data, $shipping_id);
            }
            Config::dispatch(
                \Wizacha\Shipping::EVENT_UPDATE,
                (new \Wizacha\Events\IterableEvent)->setElement($shipping_id)
            );
        }
    }

    return $shipping_id;
}

/**
 * Update shipping rates
 *
 * @param array $shipping_data shipping info
 * @param int $shipping_id shipping identifier
 * @return string Shipping name if shipping identifier is not null; false otherwise
 */
function fn_update_shipping_rates($shipping_data, $shipping_id)
{
    if (!empty($shipping_id)) {

        foreach ($shipping_data['rates'] as $destination_id => $rate) {

            if (!empty($rate['destination_id'])) {
                $destination_id = $rate['destination_id'];
            }

            $rate_types = array('C','W','I'); // Rate types: Cost, Weight, Items
            $normalized_data = array();

            if (empty($rate['rate_value']['C'][0])) {
                $rates['C'][0] = array();
            }
            if (empty($rates['rate_value']['W'][0])) {
                $rates['W'][0] = array();
            }
            if (empty($rates['rate_value']['I'][0])) {
                $rates['I'][0] = array();
            }

            foreach ($rate_types as $type) {
                // Update rate values
                if (!empty($rate['rate_value'][$type]) && is_array($rate['rate_value'][$type])) {
                    fn_normalized_shipping_rate($normalized_data, $rate['rate_value'][$type], $type);
                }

                // Add new rate values
                if (!empty($shipping_data['add_rates']) && is_array($shipping_data['add_rates'][$destination_id]['rate_value'][$type])) {
                    fn_normalized_shipping_rate($normalized_data, $shipping_data['add_rates'][$destination_id]['rate_value'][$type], $type);
                }

                if (!empty($normalized_data[$type]) && is_array($normalized_data[$type])) {
                    ksort($normalized_data[$type], SORT_NUMERIC);
                }
            }

            if (is_array($normalized_data)) {
                foreach ($normalized_data as $k => $v) {
                    if ((count($v)==1) && (floatval($v[0]['value'])==0)) {
                        unset($normalized_data[$k]);
                        continue;
                    }
                }
            }

            if (fn_is_empty($normalized_data)) {
                db_query("DELETE FROM ?:shipping_rates WHERE shipping_id = ?i AND destination_id = ?i", $shipping_id, $destination_id);
            } else {
                $normalized_data = serialize($normalized_data);
                db_query("REPLACE INTO ?:shipping_rates (rate_value, shipping_id, destination_id) VALUES(?s, ?i, ?i)", $normalized_data, $shipping_id, $destination_id);
            }
        }
    }

}

/**
 * Normalized shipping rates
 *
 * @param array $normalized_data shipping info
 * @param array $rate rate info
 * @param string $rate_type rate types: Cost, Weight, Items
 */
function fn_normalized_shipping_rate(&$normalized_data, $rate, $rate_type)
{
    foreach ($rate as $amount => $v) {

        if (!isset($v['value'])) {
            $v['value'] = 0;
        }
        if (!isset($v['type'])) {
            $v['type'] = TaxRateType::FLAT()->getValue();
        }
        if (!isset($v['amount'])) {
            $v['amount'] = $amount;
        }

        $v['amount'] = strval(($rate_type == 'I') ? intval($v['amount']) : floatval($v['amount']));
        $v['value'] = fn_format_price($v['value']);
        $v['per_unit'] = empty($v['per_unit']) ? 'N' : $v['per_unit'];

        if (!isset($normalized_data[$rate_type][$v['amount']]) || floatval($normalized_data[$rate_type][$v['amount']]['value']) == 0) {
            $normalized_data[$rate_type]["$v[amount]"] = array ('amount' => $v['amount'], 'value' => $v['value'], 'type' => $v['type'], 'per_unit' => $v['per_unit']);
        }
    }
}

//
// Get all taxes list
//
function fn_get_taxes($lang_code = null, array $pagination = null): array
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $limit = '';

    if ($pagination !== null) {
        $limit = db_paginate($pagination['page'], $pagination['items_per_page']);
    }

    return db_get_memoized_hash_array("
        SELECT  t.*, td.tax, tr.rate_value, tr.rate_type
        FROM ?:taxes as t
        LEFT JOIN ?:tax_descriptions as td ON td.tax_id = t.tax_id
        AND td.lang_code = ?s
        LEFT JOIN ?:tax_rates as tr ON tr.tax_id = t.tax_id
        ORDER BY t.priority
        $limit",
        'tax_id',
        $lang_code
    );
}

/**
 * Gets tax data
 *
 * @param int $tax_id tax identifier
 * @param string $lang_code 2-letter language code (e.g. 'en', 'ru', etc.)
 * @return array Tax data if tax identifier is not null; false otherwise
 */
function fn_get_tax($tax_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $tax = array();
    if (!empty($tax_id)) {
        $tax = db_get_memoized_row("SELECT a.*, tax, rate_value, rate_type FROM ?:taxes as a LEFT JOIN ?:tax_descriptions as b ON b.tax_id = a.tax_id AND b.lang_code = ?s LEFT JOIN ?:tax_rates as c ON c.tax_id = a.tax_id WHERE a.tax_id = ?i", $lang_code, $tax_id);
    }

    return $tax;
}

//
// Get tax name
//
function fn_get_tax_name($tax_id = 0, $lang_code = null, $as_array = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();

    if (!empty($tax_id)) {
        if (!is_array($tax_id) && strpos($tax_id, ',') !== false) {
            $tax_id = explode(',', $tax_id);
        }
        if (is_array($tax_id) || $as_array == true) {
            return db_get_memoized_hash_single_array("SELECT tax_id, tax FROM ?:tax_descriptions WHERE tax_id IN (?n) AND lang_code = ?s", array('tax_id', 'tax'), $tax_id, $lang_code);
        } else {
            return db_get_memoized_field("SELECT tax FROM ?:tax_descriptions WHERE tax_id = ?i AND lang_code = ?s", $tax_id, $lang_code);
        }
    }

    return false;
}

//
// Get all rates for specific tax
//
function fn_get_tax_rates($tax_id, $destination_id = 0)
{
    if (empty($tax_id)) {
        return false;
    }

    return db_get_memoized_array("SELECT * FROM ?:tax_rates WHERE tax_id = ?i AND destination_id = ?i", $tax_id, $destination_id);
}

//
// Get selected taxes
//
function fn_get_set_taxes($taxes_set)
{
    if (empty($taxes_set)) {
        return false;
    }

    if (!is_array($taxes_set)) {
        $taxes_set = explode(',', $taxes_set);
    }

    return db_get_memoized_hash_array("SELECT tax_id, address_type, priority, price_includes_tax, regnumber FROM ?:taxes WHERE tax_id IN (?n) AND status = 'A' ORDER BY priority", 'tax_id', $taxes_set);
}

/**
 * Create/update tax
 *
 * @param array $data Tax data
 * @param int $tax_id tax identifier
 * @param string $lang_code 2-letter language code (e.g. 'en', 'ru', etc.)
 * @return int $tax_id tax identifier update or create tax
 */
function fn_update_tax($tax_data, $tax_id, $lang_code = null)
{
    $tax_data['price_includes_tax'] = fn_is_prices_includes_tax() ? "Y" : "N";

    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();

    if (!empty($tax_id)) {
        $arow = db_query('UPDATE ?:taxes SET ?u WHERE tax_id = ?i', $tax_data, $tax_id);
        db_query('UPDATE ?:tax_descriptions SET ?u WHERE tax_id = ?i AND lang_code = ?s', $tax_data, $tax_id, $lang_code);

        if ($arow === false) {
            fn_set_notification('E', __('error'), __('object_not_found', array('[object]' => __('tax'))),'','404');
            $tax_id = false;
        }
    } else {
        unset($tax_data['tax_id']);
        $tax_id = $tax_data['tax_id'] = db_query("INSERT INTO ?:taxes ?e", $tax_data);

        \Tygh\Languages\Helper::insertTranslations('tax_descriptions', $lang_code, $tax_data);
    }

    // Update rates data
    if (!empty($tax_id) && !empty($tax_data['rates'])) {
        $destination_ids = db_get_fields("SELECT destination_id FROM ?:destinations");

        foreach ($tax_data['rates'] as $destination_id => $v) {
            if (in_array($destination_id, $destination_ids)) {

                $rate_id = db_get_field("SELECT rate_id FROM ?:tax_rates WHERE destination_id = ?i AND tax_id = ?i", $destination_id, $tax_id);

                if (!empty($rate_id)) {
                    $v['rate_value'] = floatval($v['rate_value']);
                    if (empty($v['rate_value'])) {
                        db_query("DELETE FROM ?:tax_rates WHERE rate_id = ?i", $rate_id);
                    } else {
                        db_query("UPDATE ?:tax_rates SET ?u WHERE rate_id = ?i", $v, $rate_id);
                    }
                } elseif (!empty($v['rate_value'])) {
                    $v['destination_id'] = $destination_id;
                    $v['tax_id'] = $tax_id;
                    db_query("INSERT INTO ?:tax_rates ?e", $v);
                }
            }
        }
    }

    return $tax_id;
}

function fn_is_prices_includes_tax(): bool
{
    return "Y" === db_get_memoized_field("SELECT price_includes_tax FROM ?:taxes LIMIT 1");
}

function fn_set_prices_includes_tax(bool $priceIncludesTax): void
{
    db_query("UPDATE ?:taxes SET price_includes_tax = ?s", $priceIncludesTax ? "Y" : "N");
}

/**
 * Delete tax
 *
 * @param int $tax_id ID of the tax to be removed.
 * @return boolean true
 */
function fn_delete_tax($tax_id)
{
    db_query("DELETE FROM ?:taxes WHERE tax_id = ?i", $tax_id);
    db_query("DELETE FROM ?:tax_descriptions WHERE tax_id = ?i", $tax_id);
    db_query("DELETE FROM ?:tax_rates WHERE tax_id = ?i", $tax_id);
    db_query("UPDATE ?:products SET tax_ids = ?p", fn_remove_from_set('tax_ids', $tax_id));
    db_query("UPDATE ?:shippings SET tax_ids = ?p", fn_remove_from_set('tax_ids', $tax_id));
    return true;
}

/**
 * Delete taxes
 *
 * @param array $tax_ids IDs of the taxes to be removed.
 * @return boolean true
 */
function fn_delete_taxes($tax_ids)
{
    foreach ((array) $tax_ids as $v) {
        fn_delete_tax($v);
    }
    return true;
}

function fn_add_exclude_products(&$cart)
{
    $subtotal = 0;
    $original_subtotal = 0;

    if (isset($cart['products']) && is_array($cart['products'])) {
        foreach ($cart['products'] as $cart_id => $product) {
            if (empty($product['product_id'])) {
                continue;
            }

            if (isset($product['extra']['exclude_from_calculate'])) {
                if (empty($cart['order_id']) && !isset($cart['company_id'])) {
                    unset($cart['products'][$cart_id]);
                }
            } else {
                if (!isset($product['product_options'])) {
                    $product['product_options'] = array();
                }

                $product_subtotal = fn_apply_options_modifiers($product['product_options'], $product['price'], 'P', array(), array('product_data' => $product), $product['amount']) * $product['amount'];
                $original_subtotal += $product_subtotal;
                $subtotal += $product_subtotal - ((isset($product['discount'])) ? $product['discount'] : 0);
            }
        }
    }
}

//
// Calculate cart content
//
// options style:
// F - full
// S - skip selection
// I - info
// calculate_shipping:
// A - calculate all available methods
// E - calculate selected methods only (from cart[shipping])
// S - skip calculation

// Products prices definition
// base_price - price without options modifiers
// original_price - price without discounts (with options modifiers)
// price - price includes discount and taxes
// original_subtotal - original_price * product qty
// subtotal - price * product qty
// discount - discount for this product
// display_price - the displayed price (price does not use in the calculaton)
// display_subtotal - the displayed subtotal (price does not use in the calculaton)

// Cart prices definition
// shipping_cost - total shipping cost
// subtotal - sum (price * amount) of all products
// original_subtotal - sum (original_price * amount) of all products
// tax_subtotal - sum of all the tax values
// display_subtotal - the displayed subtotal (does not use in the calculaton)

// subtotal_discount - the order discount - Used in case of a basket discount
// discount - Deprecated: Product discounts are now considered as a new product price

// total - order total

/**
 * @param $cart
 * @param $auth
 * @param string $calculate_shipping
 * @param bool $calculate_taxes
 * @param string $options_style
 * @param bool $apply_cart_promotions
 * @param Basket|null $basket Optionally provide basket data to update the prices based on what was calculated in the basket.
 * @param array|null $basketData Optionally provide array basket data for performance reason
 * @return array
 */
function fn_calculate_cart_content(
    &$cart,
    $auth,
    $calculate_shipping = 'A',
    $calculate_taxes = true,
    $options_style = 'F',
    $apply_cart_promotions = true,
    Basket $basket = null,
    array $basketData = null
) {
    $shipping_rates = array();
    $product_groups = array();
    $cart_products = array();
    $cart['subtotal'] = $cart['display_subtotal'] = $cart['original_subtotal'] = $cart['amount'] = $cart['total'] = $cart['discount'] = $cart['tax_subtotal'] = 0;
    $cart['use_discount'] = false;
    $cart['shipping_required'] = false;
    $cart['shipping_failed'] = $cart['company_shipping_failed'] = false;
    $cart['stored_taxes'] = empty($cart['stored_taxes']) ? 'N': $cart['stored_taxes'];
    $cart['display_shipping_cost'] = $cart['shipping_cost'] = 0;
    $cart['coupons'] = empty($cart['coupons']) ? array() : $cart['coupons'];
    $cart['recalculate'] = isset($cart['recalculate']) ? $cart['recalculate'] : false;
    $cart['free_shipping'] = array();
    $cart['options_style'] = $options_style;
    $cart['products'] = !empty($cart['products']) ? $cart['products'] : array();
    $cart['marketplace_discount_total'] = $basket ? $basket->getMarketplaceDiscountTotal()->getConvertedAmount() : 0;
    $cart['marketplace_discount_id'] = $basket ? $basket->getMarketplaceDiscountId() : null;

    fn_add_exclude_products($cart);

    if (isset($cart['products']) && is_array($cart['products'])) {

        //remove not salable products
        $cart['products'] = array_filter(
            $cart['products'],
            function(array $product) {
                return TransactionMode::TRANSACTIONAL()->equals($product['transaction_mode']);
            }
        );

        $amount_totals = array();
        if (Registry::get('settings.General.disregard_options_for_discounts') == 'Y') {
            foreach ($cart['products'] as $k => $v) {
                if (!empty($amount_totals[$v['product_id']])) {
                    $amount_totals[$v['product_id']] += $v['amount'];
                } else {
                    $amount_totals[$v['product_id']] = $v['amount'];
                }
            }
        }

        // Collect product data
        foreach ($cart['products'] as $k => $v) {
            //If I have updated the $amount_total already, I use its value, else I use the $amount defined on the product
            $cart['products'][$k]['amount_total'] = isset($amount_totals[$v['product_id']]) ? $amount_totals[$v['product_id']] : $v['amount'];

            if (SubscriptionProduct::class === $cart['products'][$k]['productClassName']) {
                $_cproduct = $cart['products'][$k];
            } else {
                $_cproduct = fn_get_cart_product_data($k, $cart['products'][$k], false, $cart, $auth);
            }

            if (empty($_cproduct)) { // FIXME - for deleted products for OM
                unset($cart['products'][$k]);
                continue;
            }

            $cart_products[$k] = $_cproduct;
        }

        \Wizacha\AgeVerification::updateCart($cart);


        if (Registry::get('settings.Shippings.disable_shipping') == 'Y') {
            $cart['shipping_required'] = false;
        }

        if (!empty($cart['change_cart_products'])) {
            $location = fn_get_customer_location($auth, $cart);
            $product_groups = Shippings::groupProductsList($cart_products, $location);
            if (!empty($cart['product_groups']) && count($product_groups) == count($cart['product_groups'])) {
                foreach ($product_groups as $key_group => $group) {
                    $cart['product_groups'][$key_group]['products'] = $group['products'];
                }
            } else {
                if (!empty($cart['chosen_shipping']) && count($cart['chosen_shipping']) == count($product_groups)) {
                    $cart['calculate_shipping'] = true;
                }
                if (!empty($cart['product_groups']) && count($cart['product_groups']) !== count($product_groups)) {
                    unset($cart['product_groups']);
                }
            }

            unset($cart['change_cart_products']);
            $cart['stored_taxes'] = 'N';
        }

        if (!empty($cart['calculate_shipping']) || empty($cart['product_groups'])) {
            $location = fn_get_customer_location($auth, $cart);
            $product_groups = Shippings::groupProductsList($cart_products, $location);
            $shippings = array();

            //if at least one group need shipping, we set the shipping to the parent order, else, no shipping required
            if ($cart['shipping_required'] !== false) {
                $cart['shipping_required'] = false;
                foreach ($product_groups as $key_group => $group) {
                    if ($group['shipping_no_required'] === false) {
                        $cart['shipping_required'] = true;
                        break;
                    }
                }
            }

            foreach ($product_groups as $key_group => $group) {
                //if the whole basket does not need shipping, we set free_shipping for each sub-groups
                if ($cart['shipping_required'] === false) {
                    $product_groups[$key_group]['free_shipping'] = true;
                    $product_groups[$key_group]['shipping_no_required'] = true;
                }

                if(!isset($product_groups[$key_group]['shippings'])) {//Default procedure
                    $product_groups[$key_group]['shippings'] = array();
                    $shippings_group = Shippings::getShippingsList($group);
                } else {//With wizacha addon's
                    $shippings_group = $product_groups[$key_group]['shippings'];
                }

                // Adding a shipping method from the created order, if the shipping is not yet in the list.
                if (!empty($cart['chosen_shipping']) && !empty($cart['shipping']) && !empty($cart['order_id'])) {
                    foreach ($cart['shipping'] as $shipping) {
                        if (!isset($shippings_group[$shipping['shipping_id']])) {
                            $shippings_group[$shipping['shipping_id']] = $shipping;
                        }
                    }
                }

                foreach ($shippings_group as $shipping_id => $shipping) {
                    $_shipping = $shipping;
                    $_shipping['package_info'] = $group['package_info'];
                    $_shipping['keys'] = array(
                        'group_key' => $key_group,
                        'shipping_id' => $shipping_id,
                    );
                    $shippings[] = $_shipping;

                    $shipping['group_key'] = $key_group;
                    $shipping['rate'] = 0;

                    if (in_array($shipping_id, $cart['free_shipping'])) {
                        $shipping['free_shipping'] = true;
                    }

                    $product_groups[$key_group]['shippings'][$shipping_id] = $shipping;
                }
            }

            if (!empty($cart['calculate_shipping'])) {
                $rates = Shippings::calculateRates($shippings);

                foreach ($rates as $rate) {
                    $g_key = $rate['keys']['group_key'];
                    $sh_id = $rate['keys']['shipping_id'];

                    if ($rate['price'] !== false) {
                        // if price increased in terms of shipment weight
                        $rate['price'] += !empty($product_groups[$g_key]['package_info']['shipping_freight']) ? $product_groups[$g_key]['package_info']['shipping_freight'] : 0;
                        $product_groups[$g_key]['shippings'][$sh_id]['rate'] = empty($product_groups[$g_key]['shippings'][$sh_id]['free_shipping']) ? $rate['price'] : 0;
                    } else {
                        unset($product_groups[$g_key]['shippings'][$sh_id]);
                    }

                }
            }
            $cart['product_groups'] = $product_groups;
        }

        fn_apply_stored_shipping_rates($cart);
        $product_groups = $cart['product_groups'];

        // FIXME
        $cart['shipping_cost'] = 0;
        $cart['shipping'] = array();
        $cart['chosen_shipping'] = !empty($cart['chosen_shipping']) ? $cart['chosen_shipping'] : array();

        $count_shipping_failed = 0;

        //calculate shipping costs for each groups and for the grand total
        foreach ($product_groups as $key_group => &$group) {
            $group['shipping_cost'] = 0;
            //if there is no selected shipping, we select the least expensive one
            if (!empty($cart['calculate_shipping']) && (!isset($cart['chosen_shipping'][$key_group]) || empty($group['shippings'][$cart['chosen_shipping'][$key_group]])) && !$group['free_shipping']) {
                $first_shipping_id = key($group['shippings']);
                list($best_shipping_id,) = array_reduce($group['shippings'],function(array &$result,$shipping){
                    if($shipping['rate']<$result[1]){
                        $result = [$shipping['shipping_id'],$shipping['rate']];
                    }
                    return $result;
                },[$first_shipping_id,$group['shippings'][$first_shipping_id]['rate']]);
                $cart['chosen_shipping'][$key_group] = $best_shipping_id;
            }

            if ($group['shipping_no_required']) {
                unset($cart['chosen_shipping'][$key_group]);
            }

            if (!isset($cart['chosen_shipping'][$key_group]) && !$group['free_shipping'] && !$group['shipping_no_required']) {
                $count_shipping_failed++;
                $cart['company_shipping_failed'] = true;
            }

            foreach ($group['shippings'] as $shipping_id => $shipping) {
                if (isset($cart['chosen_shipping'][$key_group]) && $cart['chosen_shipping'][$key_group] == $shipping_id) {
                    $cart['shipping_cost'] += $shipping['rate'];
                    $group['shipping_cost'] += $shipping['rate'];
                }
            }

            if (!empty($group['shippings']) && isset($cart['chosen_shipping'][$key_group])) {
                $shipping = $group['shippings'][$cart['chosen_shipping'][$key_group]];
                $shipping_id = $shipping['shipping_id'];
                if (empty($cart['shipping'][$shipping_id])) {
                    $cart['shipping'][$shipping_id] = $shipping;
                    $cart['shipping'][$shipping_id]['rates'] = array();
                }
                $cart['shipping'][$shipping_id]['rates'][$key_group] = $shipping['rate'];
            }
        }
        $cart['display_shipping_cost'] = $cart['shipping_cost'];

        if (!empty($product_groups) && count($product_groups) == $count_shipping_failed) {
            $cart['shipping_failed'] = true;
        }

        foreach ($cart['chosen_shipping'] as $key_group => $shipping_id) {
            if (!empty($product_groups[$key_group]) && !empty($product_groups[$key_group]['shippings'][$shipping_id])) {
                $shipping = $product_groups[$key_group]['shippings'][$shipping_id];
                $shipping['group_name'] = $product_groups[$key_group]['name'];
                $product_groups[$key_group]['chosen_shippings'] = array($shipping);
            } else {
                unset($cart['chosen_shipping'][$key_group]);
            }
        }

        $calculated_taxes_summary = array();

        if ($basketData === null) {
            $basketData = $basket ? $basket->getData() : null;
        }

        foreach ($product_groups as $key_group => &$group) {
            foreach ($group['products'] as $cart_id => $product) {
                if (!empty($cart_products[$cart_id])) {
                    $group['products'][$cart_id] = $cart_products[$cart_id];
                }
            }

            // Calculate taxes
            if ($calculate_taxes == true) {
                $calculated_taxes = fn_calculate_taxes($cart, $key_group, $group['products'], $group['shippings'], $group['company_id'], $auth);

                // Bon, c'est la que les ennuis commencent
                // Si il y a une promo sur les shippings, le calcul des taxes est pas bon car cscart ne tiens pas compte de la promo
                // Du coup on le laisse calculer, pour qu'il nous génere le tableau de la forme qu'il veut, et on vient écraser les valeurs
                if (!empty($basketData)) {
                    foreach ($calculated_taxes as $taxItemKey => &$taxItems) {
                        // On parcours les item ( products, shippings ) sur lesquels des taxes s'appliquent
                        // On ignore tout ce qui n'est pas shipping
                        if (strpos($taxItemKey, 'S_') !== 0 || empty($taxItems)) {
                            continue;
                        }

                        // On se base sur le fait que le shipping ne peut avoir qu'une seule taxe, et on récupere la premiere
                        $taxItem = &$taxItems[key($taxItems)];

                        // On fait la corespondance entre les groupes de cscart et nos groupes, histoire de séléctioner la bonne taxe a appliquer
                        $lookupProductId = reset($group['products'])['product_id'];
                        $resultLookupGroupId = null;
                        foreach ($basketData['groups'][$group['company_id']]['products'] as $lookupGroupId => $lookupGroup) {
                            foreach ($lookupGroup['products'] as $lookupGroupProduct) {
                                if ($lookupGroupProduct['declination']['product_id'] == $lookupProductId) {
                                    $resultLookupGroupId = $lookupGroupId;
                                    break 2;
                                }
                            }
                        }

                        // On écrase la taxe calculée par cscart avec les vrais rates, par celle que l'on a calculé, avec les promos
                        $taxItem['tax_subtotal'] = $basketData['groups'][$group['company_id']]['products'][$resultLookupGroupId]['prices']->get(\Wizacha\Marketplace\Price\PriceFields::SHIPPING_TAX())->getPreciseConvertedAmount();
                    }
                }

                if (empty($calculated_taxes_summary)) {
                    $calculated_taxes_summary = array();
                }
                foreach ($calculated_taxes as $tax_id => $tax) {
                    if (empty($calculated_taxes_summary[$tax_id])) {
                        $calculated_taxes_summary[$tax_id] = $calculated_taxes[$tax_id];
                    } else {
                        $calculated_taxes_summary[$tax_id]['tax_subtotal'] += $calculated_taxes[$tax_id]['applies']['S'];
                        $calculated_taxes_summary[$tax_id]['applies']['S'] += $calculated_taxes[$tax_id]['applies']['S'];
                        $calculated_taxes_summary[$tax_id]['tax_subtotal'] += $calculated_taxes[$tax_id]['applies']['P'];
                        $calculated_taxes_summary[$tax_id]['applies']['P'] += $calculated_taxes[$tax_id]['applies']['P'];
                    }
                }
            } elseif ($cart['stored_taxes'] != 'Y') {
                $cart['taxes'] = $cart['tax_summary'] = array();
            }

            unset($group);
        }

        fn_apply_calculated_taxes($calculated_taxes_summary, $cart);
        // /FIXME

        $cart['subtotal'] = $cart['display_subtotal'] = 0;

        fn_update_cart_data($cart, $cart_products);

        foreach ($cart['products'] as $product_code => $product) {
            foreach ($product_groups as $key_group => $group) {
                if (in_array($product_code, array_keys($group['products']))) {
                    $product_groups[$key_group]['products'][$product_code] = $product;
                }
            }
        }

        // Calculate totals
        // For shipping groups with a shipping id, these values will be override a few lines later
        // when we assign basket values to shipping groups.
        foreach ($product_groups as $key_group => &$group) {
            $group['total'] = 0;
            $group['subtotal'] = 0;
            foreach ($group['products'] as $product_code => $product) {
                $_tax = (!empty($product['tax_summary']) ? ($product['tax_summary']['added'] / $product['amount']) : 0);
                $cart_products[$product_code]['display_price'] = $cart_products[$product_code]['price'] + (Registry::get('settings.Appearance.cart_prices_w_taxes') == 'Y' ? $_tax : 0);
                $cart_products[$product_code]['subtotal'] = ($cart_products[$product_code]['price'] + $_tax) * $product['amount'];

                $cart_products[$product_code]['display_subtotal'] = $cart_products[$product_code]['display_price'] * $product['amount'];

                if (!empty($product['tax_summary'])) {
                    $cart_products[$product_code]['tax_summary'] = $product['tax_summary'];
                }

                $cart['subtotal'] += $cart_products[$product_code]['subtotal'];
                $group['subtotal'] += $cart_products[$product_code]['subtotal'];
                $cart['display_subtotal'] += $cart_products[$product_code]['display_subtotal'];
                $cart['products'][$product_code]['display_price'] = $cart_products[$product_code]['display_price'];
                $product_groups[$key_group]['products'][$product_code]['display_price'] = $cart_products[$product_code]['display_price'];

                $cart['tax_subtotal'] += (!empty($product['tax_summary']) ? ($product['tax_summary']['added']) : 0);
                $cart['total'] += ($cart_products[$product_code]['price'] - 0) * $product['amount'];
                $group['total'] += ($cart_products[$product_code]['price'] - 0) * $product['amount'];

                if (!empty($product['discount'])) {
                    $cart['discount'] += $product['discount'] * $product['amount'];
                }
            }
        }

        $cart['subtotal'] = fn_format_price($cart['subtotal']);
        $cart['display_subtotal'] = fn_format_price($cart['display_subtotal']);

        $cart['total'] += $cart['tax_subtotal'];

        $cart['total'] = fn_format_price($cart['total'] + $cart['shipping_cost']);

        if (!empty($cart['subtotal_discount'])) {
            $cart['total'] -= ($cart['subtotal_discount'] < $cart['total']) ? $cart['subtotal_discount'] : $cart['total'];
        }
    }

    if (!empty($cart['calculate_shipping']) || empty($cart['product_groups'])) {
        $cart['product_groups'] = $product_groups;
    }
    $cart['recalculate'] = false;
    $cart['calculate_shipping'] = false;

    // Update price information from the basket (which contains prices with promotions applied)
    if ($basket !== null) {
        $cart['subtotal'] = fn_format_price($basketData['subtotal']->getPreciseConvertedAmount(), CART_PRIMARY_CURRENCY, 4);
        $cart['display_subtotal'] = $cart['subtotal'];
        if ($basketData['prices']->get(PriceFields::SHIPPING_REAL()) !== null) {
            $cart['shipping_cost'] = $basketData['prices']->get(PriceFields::SHIPPING_REAL())->getConvertedAmount();
        } else {
            $cart['shipping_cost'] = fn_format_price($basketData['shipping_total']);
        }
        $cart['display_shipping_cost'] = $cart['shipping_cost'];
        $cart['subtotal_discount'] = fn_format_price($basketData['subtotal_discount']->getPreciseConvertedAmount(), CART_PRIMARY_CURRENCY, 4);
        $cart['total'] = $basketData['total'];
        $cart['tax_subtotal'] = $basketData['prices']->get(\Wizacha\Marketplace\Price\PriceFields::BASKET_TAX())->getPreciseConvertedAmount();

        //Set basket selected shippings
        $selectedShippings = $basket->getSelectedShippings();
        $cart['shipping'] = [];

        // Ici on récupère le shipping selectionné par default dans le readmodel dans le cas ou on n'a pas appelé
        // explicitement l'endpoint select shipping et donc que $basket->getSelectedShippings() renvoie un tableau vide.
        if (true === \is_array($selectedShippings)
            && \count($selectedShippings) === 0
        ) {
            $readModel = new \Wizacha\Marketplace\Basket\ReadModel\Basket($basketData);
            foreach ($readModel->getGroups() as $companyGroup) {
                foreach ($companyGroup->getShippingGroups() as $shippingGroup) {
                    foreach ($shippingGroup->expose()['shippings'] as $shipping) {
                        if ($shipping['selected'] === true) {
                            $basket->setSelectedGroupShipping($shippingGroup->getGroupId(), $shipping['id']);

                            break;
                        }
                    }
                }
            }

            $selectedShippings = $basket->getSelectedShippings();
        }

        // Shipping groups without shipping id won't get this treatment
        foreach ($cart['product_groups'] as $legacyGroupId => &$cartGroup) {
            $groupId = null;
            $cartGroup['comment'] = $basketData['comment'] ?? '';
            $firstShippingId = reset($cartGroup['shippings'])['shipping_id'];

            // Recherche la correspondance entre l'id CsCart et l'Id dans le panier.
            // On se base sur l'assertion qu'un produit ne peut pas se trouver dans deux shippings groups
            foreach ($basketData['groups'][$cartGroup['company_id']]['products'] as $id => $data) {
                if (!empty($data['shippings']) && array_key_exists($firstShippingId, $data['shippings'])) {
                    $groupId = $id;
                    break;
                }
            }
            if (!is_null($groupId)) {
                // On peut ne pas rentrer dans ce cas si les produits ne necessitent pas de livraison (services, bien téléchargeables)
                // ou s'il n'y a qu'un seul moyen de livraison
                if ($selectedShippings[$groupId]) {
                    $cart['shipping'][$legacyGroupId] =
                        $cartGroup['shippings'][$selectedShippings[$groupId]];
                    $cartGroup['chosen_shippings'] = [0 => $cartGroup['shippings'][$selectedShippings[$groupId]]];
                    $cart['chosen_shipping'][$legacyGroupId] = $selectedShippings[$groupId];
                    $cartGroup['chosen_shippings'][0]['customShippingPrice'] = [
                        'price' => $basketData['groups'][$cartGroup['company_id']]['products'][$groupId]['shippings'][$selectedShippings[$groupId]]['shippingPrice'],
                        'isCustomShippingPrice' => $basketData['groups'][$cartGroup['company_id']]['products'][$groupId]['shippings'][$selectedShippings[$groupId]]['isCustomShippingPrice'],
                    ];
                } else if (!empty($cartGroup['shippings'])) {
                    $cart['shipping'][$legacyGroupId] =  current($cartGroup['shippings']);
                    $cartGroup['chosen_shippings'] = [0 => current($cartGroup['shippings'])];
                    $cart['chosen_shipping'][$legacyGroupId] = key($cartGroup['shippings']);
                }

                //promotion calculation for the current shipping group
                /** @var \Wizacha\Marketplace\Price\PriceComposition $originalGroupPrice */
                $originalGroupPrice = $basketData['groups'][$cartGroup['company_id']]['products'][$groupId]['original_prices'];
                /** @var \Wizacha\Marketplace\Price\PriceComposition $finalGroupPrice */
                $finalGroupPrice = $basketData['groups'][$cartGroup['company_id']]['products'][$groupId]['prices'];

                $originalTotalPrice = $originalGroupPrice->get(\Wizacha\Marketplace\Price\PriceFields::BASKET_TOTAL())
                    ->add($originalGroupPrice->get(\Wizacha\Marketplace\Price\PriceFields::SHIPPING_TOTAL()));
                $finalTotalPrice = $finalGroupPrice->get(\Wizacha\Marketplace\Price\PriceFields::BASKET_TOTAL())
                    ->add($finalGroupPrice->get(\Wizacha\Marketplace\Price\PriceFields::SHIPPING_TOTAL()));

                $cartGroup['subtotal_discount'] = $originalTotalPrice->subtract($finalTotalPrice)->getConvertedAmount();

                // We get the carriage paid information from the basket
                $cartGroup['carriage_paid'] =
                    $basketData['groups'][$cartGroup['company_id']]['products'][$groupId]['shippings'][$cart['chosen_shipping'][$legacyGroupId]]['carriagePaid'];

                //we redefined the values in $cart[product_group] with the datas from $basket->product_group
                /** @var \Wizacha\Marketplace\Price\PriceComposition $prices */
                $prices = $basketData['groups'][$cartGroup['company_id']]['products'][$groupId]['prices'];
                $cartGroup['shipping_cost'] = $prices->get(\Wizacha\Marketplace\Price\PriceFields::SHIPPING_TOTAL())->getConvertedAmount();
                $cartGroup['subtotal'] = $prices->get(\Wizacha\Marketplace\Price\PriceFields::BASKET_TOTAL())->getConvertedAmount();
                $cartGroup['total'] = $prices->get(\Wizacha\Marketplace\Price\PriceFields::BASKET_TOTAL())
                    ->add( $prices->get(\Wizacha\Marketplace\Price\PriceFields::SHIPPING_TOTAL()))
                    ->getConvertedAmount();
            } else {
                $cartGroup['total'] = $basketData['groups'][$cartGroup['company_id']]['prices']
                    ->get(\Wizacha\Marketplace\Price\PriceFields::BASKET_TOTAL())
                    ->getConvertedAmount();
            }
        }
    }

    return array(
        $cart_products,
        $product_groups
    );
}

function fn_cart_is_empty($cart)
{
    $result = true;
    if (!empty($cart['products'])) {
        foreach ($cart['products'] as $v) {
            if (!isset($v['extra']['exclude_from_calculate']) && empty($v['extra']['parent'])) {
                $result = false;
                break;
            }
        }
    }
    return $result;
}

// Get Payment processor data
function fn_get_processor_data($payment_id)
{
    $pdata = db_get_memoized_row("SELECT processor_id, processor_params FROM ?:payments WHERE payment_id = ?i", $payment_id);
    if (empty($pdata)) {
        return false;
    }

    $processor_data = db_get_memoized_row("SELECT * FROM ?:payment_processors WHERE processor_id = ?i", $pdata['processor_id']);
    $processor_data['processor_params'] = unserialize($pdata['processor_params']);

    $processor_data['currencies'] = (!empty($processor_data['currencies'])) ? explode(',', $processor_data['currencies']) : array();

    return $processor_data;
}

/**
 * Get processor data by processor script
 *
 * @param string $processor_script name of processor script
 * @return array processor data
 */
function fn_get_processor_data_by_name($processor_script)
{
    $processor_data = db_get_memoized_row("SELECT * FROM ?:payment_processors WHERE processor_script = ?s", $processor_script);

    return $processor_data;
}

/**
 * Get payment method by processor_id
 *
 * @param string $processor_id
 * @param string $lang_code
 * @return array payment methods which use this processor
 */
function fn_get_payment_by_processor($processor_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();

    $payment_methods = db_get_memoized_hash_array("SELECT ?:payments.payment_id, ?:payments.a_surcharge, ?:payments.p_surcharge, ?:payments.payment_category, ?:payment_descriptions.*, ?:payment_processors.type AS processor_type, ?:payments.status FROM ?:payments LEFT JOIN ?:payment_descriptions ON ?:payments.payment_id = ?:payment_descriptions.payment_id AND ?:payment_descriptions.lang_code = ?s LEFT JOIN ?:payment_processors ON ?:payment_processors.processor_id = ?:payments.processor_id WHERE ?:payments.processor_id = ?i ORDER BY ?:payments.position", 'payment_id', $lang_code, $processor_id);

    return $payment_methods;
}

//
// Returns customer location or default location
//
function fn_get_customer_location($auth, $cart, $billing = false)
{
    $s_info = array();
    $prefix = 's_';
    if ($billing == true) {
        $prefix = 'b_';
    }

    $u_info = (!empty($cart['user_data'])) ? $cart['user_data'] : ((empty($cart['user_data']) && !empty($auth['user_id'])) ? fn_get_user_info($auth['user_id'], true, $cart['profile_id']) : array());

    if (empty($u_info)) {
        foreach (Registry::get('settings.General') as $f_name => $f_value) {
            if (strpos($f_name, 'default_') === 0) {
                $f_name = substr($f_name, 8);
                $u_info[$prefix . $f_name] = $f_value;
            }
        }
    }

    $s_info = array();
    foreach ($u_info as $field_name => $field_value) {
        if (strpos($field_name, $prefix) === 0) {
            $f_name = substr($field_name, 2);
            $s_info[$f_name] = !empty($field_value) ? $field_value : Registry::get('settings.General.default_' . $f_name);;
        }
    }

    if (empty($s_info)) {
        return array();
    }

    // Add residential address flag
    $s_info['address_type'] = (!empty($u_info['s_address_type'])) ? $u_info['s_address_type'] : 'residential';

    // Get First and Last names
    $u_info['firstname'] = !empty($u_info['firstname']) ? $u_info['firstname'] : 'John';
    $u_info['lastname'] = !empty($u_info['lastname']) ? $u_info['lastname'] : 'Doe';

    if ($prefix == 'b') {
        $s_info['firstname'] = (!empty($u_info['b_firstname'])) ? $u_info['b_firstname'] : $u_info['firstname'];
        $s_info['lastname'] = (!empty($u_info['b_lastname'])) ? $u_info['b_lastname'] : $u_info['lastname'];
    } else {
        $s_info['firstname'] = (!empty($u_info['s_firstname'])) ? $u_info['s_firstname'] : (!empty($u_info['b_firstname']) ? $u_info['b_firstname'] : $u_info['firstname']);
        $s_info['lastname'] = (!empty($u_info['s_lastname'])) ? $u_info['s_lastname'] : (!empty($u_info['b_lastname']) ? $u_info['b_lastname'] : $u_info['lastname']);
    }

    // Get country/state descriptions
    $avail_country = db_get_memoized_field("SELECT COUNT(*) FROM ?:countries WHERE code = ?s AND status = 'A'", $s_info['country']);
    if (empty($avail_country)) {
        return array();
    }

    $avail_state = db_get_memoized_field("SELECT COUNT(*) FROM ?:states WHERE country_code = ?s AND code = ?s AND status = 'A'", $s_info['country'], $s_info['state']);
    if (empty($avail_state)) {
        $s_info['state'] = '';
    }

    return $s_info;
}

/**
 * Calculate products and shipping taxes
 *
 * @param array $cart Cart data
 * @param array $group_key Group number
 * @param array $group_products Products data
 * @param array $shipping_rates
 * @param array $auth Auth data
 * @return array
 */
function fn_calculate_taxes(&$cart, $group_key, &$group_products, &$shipping_rates, $groupCompanyId, $auth)
{
    $userService = container()->get('marketplace.user.user_service');
    $calculated_data = array();

    try {
        $user = $userService->get($auth['user_id']);
    } catch (\Wizacha\Marketplace\Exception\NotFound $e) {
        $user = null;
    }

    $company = fn_get_company_data($groupCompanyId);
    $companyCountry = $company['country'];
    $companyVat = $company['w_vat_number'];

    // Calculate product taxes
    foreach ($group_products as $k => $product) {
        $taxes = fn_get_product_taxes($k, $cart, $group_products);

        if (empty($taxes)) {
            continue;
        }

        if (isset($product['subtotal'])) {
            if ($product['price'] == $product['subtotal'] && $product['amount'] != 1) {
                $price = fn_format_price($product['price'], CART_PRIMARY_CURRENCY, 4);
            } else {
                $price = fn_format_price($product['subtotal'] / $product['amount'], CART_PRIMARY_CURRENCY, 4);
            }

            $calculated_data['P_' . $k] = fn_calculate_tax_rates(
                $taxes,
                $price,
                $product['amount'],
                $auth,
                $cart,
                $groupCompanyId,
                "Y" === $product['is_edp']
            );

            $group_products[$k]['tax_summary'] = array('included' => 0, 'added' => 0, 'total' => 0); // tax summary for 1 unit of product
            $cart['products'][$k]['tax_summary'] = array('included' => 0, 'added' => 0, 'total' => 0); // tax summary for 1 unit of product

            // Apply taxes to product subtotal
            if (!empty($calculated_data['P_' . $k])) {
                foreach ($calculated_data['P_' . $k] as $_k => $v) {
                    $group_products[$k]['taxes'][$_k] = $v;
                    $cart['products'][$k]['taxes'][$_k] = $v;
                    if ($taxes[$_k]['price_includes_tax'] != 'Y') {
                        $group_products[$k]['tax_summary']['added'] += $v['tax_subtotal'];
                        $cart['products'][$k]['tax_summary']['added'] += $v['tax_subtotal'];
                    } else {
                        $group_products[$k]['tax_summary']['included'] += $v['tax_subtotal'];
                        $cart['products'][$k]['tax_summary']['included'] += $v['tax_subtotal'];
                    }
                }
                $group_products[$k]['tax_summary']['total'] = $group_products[$k]['tax_summary']['added'] + $group_products[$k]['tax_summary']['included'];
                $cart['products'][$k]['tax_summary']['total'] = $cart['products'][$k]['tax_summary']['added'] + $cart['products'][$k]['tax_summary']['included'];
            }
        }
    }

    // Calculate shipping taxes
    if (!empty($shipping_rates)) {
        foreach ($shipping_rates as $shipping_id => $shipping) {
            $taxes = fn_get_shipping_taxes($shipping_id, $shipping_rates, $cart, $user, $companyCountry, $companyVat);

            if (!empty($taxes)) {
                $shipping_rates[$shipping_id]['taxes'] = array();
                $calculate_rate = true;

                if (!empty($cart['chosen_shipping'][$group_key]) && $cart['chosen_shipping'][$group_key] == $shipping_id) {
                    $calculated_data['S_' . $group_key . '_' . $shipping_id] = fn_calculate_tax_rates($taxes, $shipping['rate'], 1, $auth, $cart);

                    if (!empty($calculated_data['S_' . $group_key . '_' . $shipping_id])) {
                        foreach ($calculated_data['S_' . $group_key . '_' . $shipping_id] as $__k => $__v) {
                            if ($taxes[$__k]['price_includes_tax'] != 'Y') {
                                $cart['display_shipping_cost'] += Registry::get('settings.Appearance.cart_prices_w_taxes') == 'Y' ? $__v['tax_subtotal'] : 0;
                                $cart['tax_subtotal'] += $__v['tax_subtotal'];
                            }

                            if ($cart['stored_taxes'] == 'Y') {
                                $cart['taxes'][$__k]['applies']['S_' . $group_key . '_' . $shipping_id] = $__v['tax_subtotal'];
                            }
                        }

                        $shipping_rates[$shipping_id]['taxes']['S_' . $group_key . '_' . $shipping_id] = $calculated_data['S_' . $group_key . '_' . $shipping_id];
                        $calculate_rate = false;
                    }
                }

                if ($calculate_rate) {
                    $cur_shipping_rates = fn_calculate_tax_rates($taxes, $shipping['rate'], 1, $auth, $cart);
                    if (!empty($cur_shipping_rates)) {
                        $shipping_rates[$shipping_id]['taxes'] = $cur_shipping_rates;
                    }
                }
            }
        }

        foreach ($shipping_rates as $shipping_id => $shipping) {
            // Calculate taxes for each shipping rate
            $taxes = fn_get_shipping_taxes($shipping_id, $shipping_rates, $cart, $user, $companyCountry, $companyVat);

            $shipping_rates[$shipping_id]['taxed_price'] = 0;
            unset($shipping_rates[$shipping_id]['inc_tax']);

            if (!empty($taxes)) {
                $shipping_rates[$shipping_id]['taxes'] = array();

                $tax = fn_calculate_tax_rates($taxes, fn_format_price($shipping['rate']), 1, $auth, $cart);

                $shipping_rates[$shipping_id]['taxes'] = $tax;

                if (!empty($tax) && Registry::get('settings.Appearance.cart_prices_w_taxes') == 'Y') {
                    foreach ($tax as $_id => $_tax) {
                        if ($_tax['price_includes_tax'] != 'Y') {
                            $shipping_rates[$shipping_id]['taxed_price'] += $_tax['tax_subtotal'];
                        }
                    }
                    $shipping_rates[$shipping_id]['inc_tax'] = true;
                }

                if (!empty($shipping_rates[$shipping_id]['rate']) && $shipping_rates[$shipping_id]['taxed_price'] > 0) {
                    $shipping_rates[$shipping_id]['taxed_price'] += $shipping_rates[$shipping_id]['rate'];
                }
            }
        }
    }

    return $calculated_data;
}

/**
 * Calculate payment surcharge taxes, calculated separately from products and shipping taxes
 * becuase payment surcharge is calculated based on cart totals.
 *
 * @param array $cart Cart data
 * @param array $auth Auth data
 * @return boolean always false
 */
function fn_calculate_payment_taxes(&$cart, $auth)
{
    if (Registry::get('settings.Vendors.include_payment_surcharge') == 'Y' && fn_take_payment_surcharge_from_vendor()) {
        return;
    }
    $calculated_data = array();

    if (!empty($cart['payment_id']) && !empty($cart['payment_surcharge'])) {
        $payment_id = $cart['payment_id'];
        $taxes = fn_get_payment_taxes($payment_id, $cart);

        if (!empty($taxes)) {
            $calculated_data['PS_' . $payment_id] = fn_calculate_tax_rates($taxes, fn_format_price($cart['payment_surcharge']), 1, $auth, $cart);

            if (!empty($calculated_data['PS_' . $payment_id])) {
                foreach ($calculated_data['PS_' . $payment_id] as $__k => $__v) {
                    if ($taxes[$__k]['price_includes_tax'] != 'Y') {
                        if (Registry::get('settings.Appearance.cart_prices_w_taxes') == 'Y') {
                            $cart['payment_surcharge'] += $__v['tax_subtotal'];
                        }
                    }
                }
                $calculate_rate = false;
            }
        }
    }

    fn_apply_payment_taxes($calculated_data, $cart);

    return false;
}

/**
 * Apply payment surcharge taxes to cart, payment surcharge taxes calculated and applied
 * separately from products and shipping taxes
 * cart taxes are supposed to keep shippings and products taxes
 *
 * @param array $calculated_data payment data taxes
 * @param array $cart cart data
 * @return boolean always true
 */
function fn_apply_payment_taxes($calculated_data, &$cart)
{
    $tax_added = 0;

    if (empty($cart['taxes'])) {
        $cart['taxes'] = array();
        $cart['tax_subtotal'] = 0;
    }
    if (!empty($calculated_data)) {
        foreach ($calculated_data as $id => $_taxes) {
            if (empty($_taxes)) {
                continue;
            }
            foreach ($_taxes as $k => $v) {
                if (empty($cart['taxes'][$k])) {
                    $cart['taxes'][$k] = $v;
                    $cart['taxes'][$k]['tax_subtotal'] = 0;
                }
                $cart['taxes'][$k]['applies'][$id] = $v['tax_subtotal'];
                $cart['taxes'][$k]['tax_subtotal'] += $v['tax_subtotal'];

                if ($v['price_includes_tax'] == 'N') {
                    if (Registry::get('settings.Appearance.cart_prices_w_taxes') != 'Y') {
                        $tax_added += $v['tax_subtotal'];
                    }
                    $cart['tax_subtotal'] += $v['tax_subtotal'];
                }
            }
        }
    }
    if (!empty($tax_added)) {
        $cart['total'] = fn_format_price($cart['total'] + $tax_added);
    }

    return true;
}

/**
 * Init taxes array: add additional params to tax array for calculation
 *
 * @param array $tax base tax array
 * @return array array with inited params
 */
function fn_init_tax_subtotals($tax)
{
    $tax['subtotal'] = $tax['applies']['P'] = $tax['applies']['S'] = 0;
    $tax['applies']['items']['P'] = $tax['applies']['items']['S'] = array();
    return $tax;
}

function fn_get_product_taxes($idx, $cart, $cart_products)
{
    if ($cart['stored_taxes'] == 'Y') {
        $_idx = '';
        if (isset($cart['products'][$idx]['original_product_data']['cart_id'])) {
            $_idx = $cart['products'][$idx]['original_product_data']['cart_id'];
        }

        $taxes = array();
        foreach ((array) $cart['taxes'] as $_k => $_v) {
            $tax = array();
            if (isset($_v['applies']['P_'.$idx]) || isset($_v['applies']['items']['P'][$idx]) || isset($_v['applies']['P_'.$_idx]) || isset($_v['applies']['items']['P'][$_idx])) {
                $taxes[$_k] = $_v;
            }
        }
    }
    if ($cart['stored_taxes'] != 'Y' || empty($taxes)) {
        $taxes = fn_get_set_taxes($cart_products[$idx]['tax_ids']);
    }

    return $taxes;
}

/**
 * Get payment taxes
 *
 * @param integer $payment_id payment method id
 * @param array $cart cart data
 * @return array array with taxes
 */
function fn_get_payment_taxes($payment_id, $cart)
{
    // get current tax ids
    $tax_ids = db_get_field("SELECT tax_ids FROM ?:payments WHERE payment_id = ?i", $payment_id);
    if (!empty($tax_ids)) {
        $taxes = fn_get_set_taxes($tax_ids);

        // apply new rates if exists
        if ($cart['stored_taxes'] == 'Y' && !empty($cart['stored_taxes_data'])) {

            foreach ((array) $cart['stored_taxes_data'] as $_k => $_v) {

                if (!empty($taxes[$_k]) && (!empty($_v['applies']['PS_'.$payment_id]) || !empty($_v['applies']['items']['PS'][$payment_id]))) {
                    if (!empty($_v['rate_value']) && !empty($_v['rate_type'])) {
                        $taxes[$_k]['rate_value'] = $_v['rate_value'];
                        $taxes[$_k]['rate_type'] = $_v['rate_type'];
                    }
                }
            }
        }

    }
    return $taxes;
}

function fn_get_shipping_taxes($shipping_id, $shipping_rates, $cart, $user = null, $companyCountry = null, $companyVat = null)
{
    $tax_ids = array();
    if (defined('ORDER_MANAGEMENT')) {
        $shipping_ids = array();
        foreach ($shipping_rates as $shipping) {
            $shipping_ids[] = $shipping['shipping_id'];
        }
        $_taxes = db_get_memoized_hash_single_array("SELECT tax_ids, shipping_id FROM ?:shippings WHERE shipping_id IN (?n)", array('shipping_id', 'tax_ids'), $shipping_ids);

        if (!empty($_taxes)) {
            foreach ($_taxes as $_ship => $_tax) {
                if (!empty($_tax)) {
                    $_tids = explode(',', $_tax);
                    foreach ($_tids as $_tid) {
                        $tax_ids[$_ship][$_tid] = $_tax;
                    }
                }
            }
        }
    }

    if ($cart['stored_taxes'] == 'Y') {
        $taxes = array();

        foreach ((array) $cart['taxes'] as $_k => $_v) {
            isset($_v['applies']['items']['S'][$shipping_id]) ? $exists = true : $exists = false;
            foreach ($_v['applies'] as $aid => $av) {
                if (strpos($aid, 'S_' . $shipping_id . '_') !== false) {
                    $exists = true;

                }
            }
            if ($exists == true || (!empty($tax_ids[$shipping_id]) && !empty($tax_ids[$shipping_id][$_k]))) {
                $taxes[$_k] = $_v;
                $taxes[$_k]['applies'] = array();
            }
        }
    }

    $internationalTaxService = container()->get('marketplace.international_tax.shipping');
    $shippingTaxId = $internationalTaxService->getTaxId($companyCountry, $companyVat, $user);

    if ('0' !== $shippingTaxId) {
        $taxes[$shippingTaxId] = fn_get_tax($shippingTaxId);
    }

    return $taxes;
}

/**
 * Apply calculated products and shipping taxes to cart
 * cart taxes are supposed to be empty
 *
 * @param array $calculated_data payment data taxes
 * @param array $cart cart data
 * @return boolean always true
 */
function fn_apply_calculated_taxes($calculated_data, &$cart)
{
    if ($cart['stored_taxes'] == 'Y') {
        // save taxes to prevent payment taxes loss
        $cart['stored_taxes_data'] = $cart['taxes'];
    }

    $cart['taxes'] = array();
    $cart['tax_subtotal'] = !empty($cart['tax_subtotal']) ? $cart['tax_subtotal'] : 0;
    $cart['tax_summary'] = array(
        'included' => 0,
        'added' => 0,
        'total' => 0
    );

    if (!empty($calculated_data)) {
        $taxes_data = array();
        foreach ($calculated_data as $id => $_taxes) {
            if (empty($_taxes)) {
                continue;
            }
            foreach ($_taxes as $k => $v) {
                if (empty($taxes_data[$k])) {
                    $taxes_data[$k] = $v;
                    $taxes_data[$k]['tax_subtotal'] = 0;
                }
                $taxes_data[$k]['applies'][$id] = $v['tax_subtotal'];
                $taxes_data[$k]['tax_subtotal'] += $v['tax_subtotal'];
            }
        }

        $calculated_data = $taxes_data;

        foreach ($calculated_data as $tax_id => $v) {
            $cart['taxes'][$tax_id] = $v;

            if ($v['price_includes_tax'] == 'Y') {
                $cart['tax_summary']['included'] += $v['tax_subtotal'];
            } else {
                $cart['tax_summary']['added'] += $v['tax_subtotal'];
            }

            $cart['tax_summary']['total'] += $v['tax_subtotal'];
        }

    } else { // FIXME!!! Test on order management
        $cart['taxes'] = array();
        $cart['tax_summary'] = array();
    }

    return true;
}

function fn_format_rate_value($rate_value, $rate_type, $decimals='2', $dec_point='.', $thousands_sep=',', $coefficient = '')
{
    if (!empty($coefficient) && @$rate_type !== TaxRateType::PERCENT()->getValue()) {
        $rate_value = $rate_value / \floatval($coefficient);
    }

    if (empty($rate_type)) {
        $rate_type = TaxRateType::FLAT()->getValue();
    }

    $value = number_format(fn_format_price($rate_value, '', $decimals), $decimals, $dec_point, $thousands_sep);
    if ($rate_type === TaxRateType::FLAT()->getValue()) {
        return $value;
    } elseif ($rate_type === TaxRateType::PERCENT()->getValue()) {
        return $value . '%';
    }

    return $rate_value;
}

function fn_check_amount_in_stock($product_id, $amount, $product_options, $cart_id, $is_edp, $original_amount, &$cart, $update_id = 0)
{
    // If the product is EDP don't track the inventory
    if ($is_edp == 'Y' || db_get_memoized_field("SELECT infinite_stock FROM ?:products WHERE product_id = ?i", $product_id)) {
        //Infinite stock for edp
        return $amount;
    }

    $product = db_get_memoized_row("SELECT ?:products.tracking, ?:products.amount, ?:products.min_qty, ?:products.max_qty, ?:products.qty_step, ?:products.list_qty_count, ?:product_descriptions.product FROM ?:products LEFT JOIN ?:product_descriptions ON ?:product_descriptions.product_id = ?:products.product_id AND lang_code = ?s WHERE ?:products.product_id = ?i", (string) GlobalState::interfaceLocale(), $product_id);

    if (isset($product['tracking']) && Registry::get('settings.General.inventory_tracking') == 'Y' && $product['tracking'] != 'D') {
        // Track amount for ordinary product
        if ($product['tracking'] == 'B') {
            $current_amount = $product['amount'];

            // Track amount for product with options
        } elseif ($product['tracking'] == 'O') {
            $selectable_cart_id = fn_generate_cart_id($product_id, array('product_options' => $product_options), true);
            $current_amount = db_get_field("SELECT amount FROM ?:product_options_inventory WHERE combination_hash = ?i", $selectable_cart_id);
            $current_amount = intval($current_amount);
        }

        if (!empty($cart['products']) && is_array($cart['products'])) {
            $product_not_in_cart = true;
            foreach ($cart['products'] as $k => $v) {
                if ($k != $cart_id) { // Check if the product with the same selectable options already exists ( for tracking = O)
                    if (isset ($product['tracking']) && ($product['tracking'] == 'B' && $v['product_id'] == $product_id) || ($product['tracking'] == 'O' && @$v['selectable_cart_id'] == $selectable_cart_id)) {
                        $current_amount -= $v['amount'];
                    }
                } else {
                    $product_not_in_cart = false;
                }
            }

            if ($product['tracking'] == 'B' && !empty($update_id) && $product_not_in_cart && !empty($cart['products'][$update_id])) {
                $current_amount += $cart['products'][$update_id]['amount'];
            }

            if ($product['tracking'] == 'O') {
                // Store cart_id for selectable options in cart variable, so if the same product is added to
                // the cart with the same selectable options, but different text options,
                // the total amount will be tracked anyway as it is the one product
                if (!empty($selectable_cart_id) && isset($cart['products'][$cart_id])) {
                    $cart['products'][$cart_id]['selectable_cart_id'] = $selectable_cart_id;
                }
            }
        }
    }

    $min_qty = 1;

    if (!empty($product['min_qty']) && $product['min_qty'] > $min_qty) {
        $min_qty = fn_ceil_to_step($product['min_qty'], $product['qty_step']);
    }

    if (!empty($product['qty_step']) && $product['qty_step'] > $min_qty) {
        $min_qty = $product['qty_step'];
    }

    $cart_amount_changed = false;
    // Step parity check
    if (!empty($product['qty_step']) && $amount % $product['qty_step']) {
        $amount = fn_ceil_to_step($amount, $product['qty_step']);
        $cart_amount_changed = true;
    }

    if (isset($current_amount) && $current_amount >= 0 && $current_amount - $amount < 0 && Registry::get('settings.General.allow_negative_amount') != 'Y') {
        // For order edit: add original amount to existent amount
        $current_amount += $original_amount;

        if ($current_amount > 0 && $current_amount - $amount < 0 && Registry::get('settings.General.allow_negative_amount') != 'Y') {
            if (!defined('ORDER_MANAGEMENT')) {
                fn_set_notification('W', __('important'), __('text_cart_amount_corrected', array(
                    '[product]' => $product['product']
                )));
                $amount = fn_ceil_to_step($current_amount, $product['qty_step']);
            } else {
                if ($product['tracking'] == 'O') {
                    fn_set_notification('E', __('warning'), __('text_combination_out_of_stock'));
                } else {
                    fn_set_notification('W', __('warning'), __('text_cart_not_enough_inventory'));
                }
            }
        } elseif ($current_amount - $amount < 0 && Registry::get('settings.General.allow_negative_amount') != 'Y') {
            if ($product['tracking'] == 'O') {
                fn_set_notification('E', __('notice'), __('text_combination_out_of_stock'));
            } else {
                fn_set_notification('E', __('notice'), __('text_cart_zero_inventory', array(
                    '[product]' => $product['product']
                )));
            }

            return false;
        } elseif ($current_amount <= 0 && $amount <= 0 && Registry::get('settings.General.allow_negative_amount') != 'Y') {
            fn_set_notification('E', __('notice'), __('text_cart_zero_inventory_and_removed', array(
                '[product]' => $product['product']
            )));

            return false;
        }
    }

    if ($amount < $min_qty || (isset($current_amount) && $amount > $current_amount && Registry::get('settings.General.allow_negative_amount') != 'Y' && Registry::get('settings.General.inventory_tracking') == 'Y') && isset($product_not_in_cart) && !$product_not_in_cart) {
        if (($current_amount < $min_qty || $current_amount == 0) && Registry::get('settings.General.allow_negative_amount') != 'Y' && Registry::get('settings.General.inventory_tracking') == 'Y') {
            if ($product['tracking'] == 'O') {
                fn_set_notification('E', __('warning'), __('text_combination_out_of_stock'));
            } else {
                fn_set_notification('W', __('warning'), __('text_cart_not_enough_inventory'));
            }
            if (!defined('ORDER_MANAGEMENT')) {
                $amount = false;
            }
        } elseif ($amount > $current_amount && Registry::get('settings.General.allow_negative_amount') != 'Y' && Registry::get('settings.General.inventory_tracking') == 'Y') {
            if ($product['tracking'] == 'O') {
                fn_set_notification('E', __('warning'), __('text_combination_out_of_stock'));
            } else {
                fn_set_notification('W', __('warning'), __('text_cart_not_enough_inventory'));
            }
            if (!defined('ORDER_MANAGEMENT')) {
                $amount = fn_floor_to_step($current_amount, $product['qty_step']);
            }
        } elseif ($amount < $min_qty) {
            fn_set_notification('W', __('notice'), __('text_cart_min_qty', array(
                '[product]' => $product['product'],
                '[quantity]' => $min_qty
            )));

            $cart_amount_changed = false;

            if (!defined('ORDER_MANAGEMENT')) {
                $amount = $min_qty;
            }
        }
    }

    $max_qty = fn_floor_to_step($product['max_qty'], $product['qty_step']);
    if (!empty( $max_qty) && $amount >  $max_qty) {
        fn_set_notification('W', __('notice'), __('text_cart_max_qty', array(
            '[product]' => $product['product'],
            '[quantity]' =>  $max_qty
        )));
        $cart_amount_changed = false;

        if (!defined('ORDER_MANAGEMENT')) {
            $amount = $max_qty;
        }
    }

    if ($cart_amount_changed) {
        fn_set_notification('W', __('important'), __('text_cart_amount_changed', array('[product]' => $product['product'])));
    }

    return empty($amount) ? false : $amount;
}

//
// Calculate unique product id in the cart
//
function fn_generate_cart_id($product_id, $extra, $only_selectable = false)
{
    $_cid = array();

    if (!empty($extra['product_options']) && is_array($extra['product_options'])) {
        foreach ($extra['product_options'] as $k => $v) {
            Registry::set('runtime.skip_sharing_selection', true);
            if ($only_selectable == true && ((string) intval($v) != $v || db_get_field("SELECT inventory FROM ?:product_options WHERE option_id = ?i", $k) != 'Y')) {
                continue;
            }
            Registry::set('runtime.skip_sharing_selection', false);
            $_cid[] = $v;
        }
    }

    if (isset($extra['exclude_from_calculate'])) {
        $_cid[] = $extra['exclude_from_calculate'];
    }

    natsort($_cid);
    array_unshift($_cid, $product_id);
    $cart_id = fn_crc32(implode('_', $_cid));

    return $cart_id;
}


//
// Normalize product amount
//
function fn_normalize_amount($amount = '1')
{
    $amount = abs(intval($amount));

    return empty($amount) ? 0 : $amount;
}


function fn_order_placement_routines($action = '', $order_id = 0, $force_notification = array(), $clear_cart = true, $area = AREA) : Response
{
    // In test mode the AREA is A and for this function we want to test it with AREA C (see constant usages)
    if (defined('PHPUNIT_PAYMENT_TEST')) {
        $area = 'C';
    }

    if ($action == 'checkout_redirect') {
        if ($area == 'A') {
            return fn_redirect("order_management.edit?order_id=" . reset($_SESSION['cart']['processed_order_id']));
        } else {
            return fn_redirect('checkout.' . (Registry::get('settings.General.checkout_style') != 'multi_page' ? 'checkout' : 'summary'));
        }
    } elseif (in_array($action, array('save', 'repay', 'route')) && !empty($order_id)) {
        $order_info = container()
            ->get('marketplace.order.order_service')
            ->overrideLegacyOrder($order_id, true);
        $display_notification = true;

        if (!empty($_SESSION['cart']['placement_action'])) {
            if (empty($action)) {
                $action = $_SESSION['cart']['placement_action'];
            }
            unset($_SESSION['cart']['placement_action']);
        }

        if ($area == 'C' && !empty($order_info['user_id'])) {
            $__fake = '';
            fn_save_cart_content($__fake, $order_info['user_id']);
        }

        $edp_data = fn_generate_ekeys_for_edp(array(), $order_info);
        fn_order_notification($order_info, $edp_data, $force_notification);

        $_error = false;

        if ($action == 'save') {
            if ($display_notification) {
                fn_set_notification('N', __('congratulations'), __('text_order_saved_successfully'));
            }
        } else {
            if ($order_info['status'] == STATUS_PARENT_ORDER) {
                $child_orders = db_get_hash_single_array("SELECT order_id, status FROM ?:orders WHERE parent_order_id = ?i", array('order_id', 'status'), $order_id);
                $status = reset($child_orders);
                $child_orders = array_keys($child_orders);
            } else {
                $status = $order_info['status'];
            }
            if (in_array($status, fn_get_order_paid_statuses())) {
                if ($action == 'repay') {
                    fn_set_notification('N', __('congratulations'), __('text_order_repayed_successfully'));
                } else {
                    fn_set_notification('N', __('order_placed'), __('text_order_placed_successfully'));
                }
            } elseif ($status == STATUS_BACKORDERED_ORDER) {
                fn_set_notification('W', __('important'), __('text_order_backordered'));
            } else {
                if ($area == 'A' || $action == 'repay') {
                    if ($status != LegacyOrderStatus::CANCEL) {
                        $_payment_info = db_get_field(
                            "SELECT data FROM ?:order_data WHERE order_id = ?i AND type = ?s",
                            $order_id,
                            OrderDataType::PAYMENT_INFO()->getValue()
                        );
                        if (!empty($_payment_info)) {
                            $_payment_info = unserialize(fn_decrypt_text($_payment_info));
                            $_msg = !empty($_payment_info['reason_text']) ? $_payment_info['reason_text'] : '';
                            $_msg .= empty($_msg) ? __('text_order_placed_error') : '';
                            fn_set_notification('E', '', $_msg);
                        }
                    }
                } else {
                    $_error = true;
                    if (!empty($child_orders)) {
                        array_unshift($child_orders, $order_id);
                    } else {
                        $child_orders = array();
                        $child_orders[] = $order_id;
                    }
                    $_SESSION['cart'][($status == STATUS_INCOMPLETED_ORDER ? 'processed_order_id' : 'failed_order_id')] = $child_orders;
                }
                if ($status == STATUS_INCOMPLETED_ORDER || ($action == 'repay' && $status == LegacyOrderStatus::CANCEL)) {
                    fn_set_notification('W', __('important'), __('text_transaction_cancelled'));
                }
            }
        }

        // Empty cart
        if ($clear_cart == true && $_error == false) {
            container()->get('session')->set('basketId', null);
            if (!empty($order_info['user_id'])) {
                $user = new \Wizacha\User($order_info['user_id']);
                $user->setBasketId('');
            }


            db_query('DELETE FROM ?:user_session_products WHERE session_id = ?s AND type = ?s', Session::getId(), 'C');
        }

        if (!defined('PHPUNIT_PAYMENT_TEST') && ($area == 'A' || $action == 'repay')) {
            return fn_redirect("orders.details?order_id=$order_id");
        } else {
            $container = container();
            if ($container->getParameter('use_new_checkout')) {
                if ($_error) {
                    return new RedirectResponse($container->get('router')->generate('checkout_payment'));
                } else {
                    return new RedirectResponse(
                        $container->get('router')->generate('checkout_complete', ['orderId' => $order_id]),
                        302,
                        [
                            'X-TransactionSuccessful' => 1,
                            'X-OrderId' => $order_id, // used by payment notification controller
                        ]
                    );
                }
            } else {
                if (!$_error) {
                    return new RedirectResponse(
                        $container->get('router')->generate('checkout_complete', ['orderId' => $order_id]),
                        302,
                        [
                            'X-TransactionSuccessful' => 1,
                            'X-OrderId' => $order_id,
                        ]
                    );
                } else {
                    return fn_redirect('checkout.'.(Registry::get('settings.General.checkout_style') != 'multi_page' ? 'checkout' : 'summary'));
                }
            }
        }
    } elseif ($action == 'index_redirect') {
        return fn_redirect(fn_url('', 'C', 'http'));
    } else {
        return fn_redirect(fn_url($action, 'C', 'http'));
    }
}

//
// Check if product was added to cart
//
function fn_check_add_product_to_cart($cart, $product, $product_id)
{
    $result = true;
    if (isset($cart['company_id'])) {
        $product_company_id = db_get_memoized_field('SELECT company_id FROM ?:products WHERE product_id = ?i', $product_id);
    }
    if (isset($cart['company_id']) && $product_company_id != $cart['company_id']) {
        $result = false;
    }
    return $result;
}

//
// Add product to cart
//
// @param array $product_data array with data for the product to add)(product_id, price, amount, product_options, is_edp)
// @return mixed cart ID for the product if addition is successful and false otherwise
//
function fn_add_product_to_cart($product_data, &$cart, $update = false)
{
    $ids = array();
    if (!empty($product_data) && is_array($product_data)) {
        if (!defined('GET_OPTIONS')) {
            list($product_data, $cart) = fn_add_product_options_files($product_data, $cart, $update);
        }

        foreach ($product_data as $key => $data) {
            if (empty($key)) {
                continue;
            }
            if (empty($data['amount'])) {
                continue;
            }

            $data['stored_price'] = (!empty($data['stored_price']) && AREA != 'C') ? $data['stored_price'] : 'N';

            if (empty($data['extra'])) {
                $data['extra'] = array();
            }

            $product_id = (!empty($data['product_id'])) ? intval($data['product_id']) : intval($key);
            if (!fn_check_add_product_to_cart($cart, $data, $product_id)) {
                continue;
            }

            // Check if product options exist
            if (!isset($data['product_options'])) {
                $data['product_options'] = fn_get_default_product_options($product_id);
            }

            // Generate cart id
            $data['extra']['product_options'] = $data['product_options'];

            $_id = fn_generate_cart_id($product_id, $data['extra'], false);

            if (isset($ids[$_id]) && $key == $_id) {
                continue;
            }

            if (isset($data['extra']['exclude_from_calculate'])) {
                if (!empty($cart['products'][$key]) && !empty($cart['products'][$key]['extra']['aoc'])) {
                    $cart['saved_product_options'][$cart['products'][$key]['extra']['saved_options_key']] = $data['product_options'];
                }
                if (isset($cart['deleted_exclude_products'][$data['extra']['exclude_from_calculate']][$_id])) {
                    continue;
                }
            }
            $amount = fn_normalize_amount(@$data['amount']);

            $price = $data['price'];

            if (false === is_float($price) && false === is_int($price)) {
                if (!isset($data['extra']['exclude_from_calculate'])) {
                    if ($data['stored_price'] != 'Y') {
                        $allow_add = true;
                        // Check if the product price with options modifiers equals to zero
                        $price = fn_get_product_price($product_id, $amount);
                        $zero_price_action = db_get_memoized_field("SELECT zero_price_action FROM ?:products WHERE product_id = ?i", $product_id);
                        if (!floatval($price) && $zero_price_action == 'A') {
                            if (isset($cart['products'][$key]['custom_user_price'])) {
                                $price = $cart['products'][$key]['custom_user_price'];
                            } else {
                                $custom_user_price = empty($data['price']) ? 0 : $data['price'];
                            }
                        }
                        $price = empty($data['price'])?fn_apply_options_modifiers($data['product_options'], $price, 'P', array(), array('product_data' => $data), $data['amount']): $data['price'];
                        if (!floatval($price)) {
                            $data['price'] = isset($data['price']) ? fn_parse_price($data['price']) : 0;

                            if (($zero_price_action == 'R' || ($zero_price_action == 'A' && floatval($data['price']) < 0)) && AREA == 'C') {
                                if ($zero_price_action == 'A') {
                                    fn_set_notification('E', __('error'), __('incorrect_price_warning'));
                                }
                                $allow_add = false;
                            }

                            $price = empty($data['price']) ? 0 : $data['price'];
                        }
                        if (!$allow_add) {
                            continue;
                        }

                    } else {
                        $price = empty($data['price']) ? 0 : $data['price'];
                    }
                } else {
                    $price = 0;
                }
            }

            if (SubscriptionProduct::class !== $data['productClassName']) {
                $_data = db_get_memoized_row('SELECT is_edp, options_type, tracking, unlimited_download FROM ?:products WHERE product_id = ?i', $product_id);
                if (isset($_data['is_edp'])) {
                    $data['is_edp'] = $_data['is_edp'];
                } elseif (!isset($data['is_edp'])) {
                    $data['is_edp'] = 0;
                }
                if (isset($_data['options_type'])) {
                    $data['options_type'] = $_data['options_type'];
                }
                if (isset($_data['tracking'])) {
                    $data['tracking'] = $_data['tracking'];
                }
                if (isset($_data['unlimited_download'])) {
                    $data['extra']['unlimited_download'] = $_data['unlimited_download'];
                }
            }

            // Check the sequential options
            if (!empty($data['tracking']) && $data['tracking'] == 'O' && $data['options_type'] == 'S') {
                $inventory_options = db_get_memoized_fields("SELECT a.option_id FROM ?:product_options as a LEFT JOIN ?:product_global_option_links as c ON c.option_id = a.option_id WHERE c.product_id = ?i AND a.status = 'A' AND a.inventory = 'Y'", $product_id);

                $sequential_completed = true;
                if (!empty($inventory_options)) {
                    foreach ($inventory_options as $option_id) {
                        if (!isset($data['product_options'][$option_id]) || empty($data['product_options'][$option_id])) {
                            $sequential_completed = false;
                            break;
                        }
                    }
                }

                if (!$sequential_completed) {
                    fn_set_notification('E', __('error'), __('select_all_product_options'));
                    // Even if customer tried to add the product from the catalog page, we will redirect he/she to the detailed product page to give an ability to complete a purchase
                    $redirect_url = fn_url('products.view?product_id=' . $product_id . '&combination=' . fn_get_options_combination($data['product_options']));
                    $_REQUEST['redirect_url'] = $redirect_url; //FIXME: Very very very BAD style to use the global variables in the functions!!!

                    return false;
                }
            }

            if (!isset($cart['products'][$_id])) { // If product doesn't exists in the cart
                $amount = empty($data['original_amount']) ? fn_check_amount_in_stock($product_id, $amount, $data['product_options'], $_id, $data['is_edp'], 0, $cart, $update == true ? $key : 0) : $data['original_amount'];

                if ($amount === false) {
                    continue;
                }

                $cart['products'][$_id]['product_id'] = $product_id;
                $cart['products'][$_id]['product_code'] = $data['product_code'] ?? fn_get_product_code($product_id, $data['product_options']);
                $cart['products'][$_id]['product'] = $data['product'] ?? fn_get_product_name($product_id);
                $cart['products'][$_id]['amount'] = $amount;
                $cart['products'][$_id]['product_options'] = $data['product_options'];
                $cart['products'][$_id]['price'] = $price;
                if (!empty($zero_price_action) && $zero_price_action == 'A') {
                    if (isset($custom_user_price)) {
                        $cart['products'][$_id]['custom_user_price'] = $custom_user_price;
                    } elseif (isset($cart['products'][$key]['custom_user_price'])) {
                        $cart['products'][$_id]['custom_user_price'] = $cart['products'][$key]['custom_user_price'];
                    }
                }
                $cart['products'][$_id]['stored_price'] = $data['stored_price'];

                // add image for minicart
                $cart['products'][$_id]['main_pair'] = fn_get_cart_product_icon($product_id, $data);

                $cart['products'][$_id]['transaction_mode'] = $data['transaction_mode'] ?? (new Product($product_id))->getTransactionMode();

                fn_define_original_amount($product_id, $_id, $cart['products'][$_id], $data);

                if ($update == true && $key != $_id) {
                    unset($cart['products'][$key]);
                }

            } else { // If product is already exist in the cart

                $_initial_amount = empty($cart['products'][$_id]['original_amount']) ? $cart['products'][$_id]['amount'] : $cart['products'][$_id]['original_amount'];

                // If ID changed (options were changed), summ the total amount of old and new products
                if ($update == true && $key != $_id) {
                    $amount += $_initial_amount;
                    unset($cart['products'][$key]);
                }

                $cart['products'][$_id]['amount'] = fn_check_amount_in_stock($product_id, (($update == true) ? 0 : $_initial_amount) + $amount, $data['product_options'], $_id, (!empty($data['is_edp']) && $data['is_edp'] == 'Y' ? 'Y' : 'N'), 0, $cart, $update == true ? $key : 0);
            }

            $cart['products'][$_id]['is_subscription'] = $data['is_subscription'];
            $cart['products'][$_id]['is_renewable'] = $data['is_renewable'];
            $cart['products'][$_id]['comment'] = $data['comment'] ?? '';
            $cart['products'][$_id]['extra'] = (empty($data['extra'])) ? array() : $data['extra'];
            $cart['products'][$_id]['stored_discount'] = @$data['stored_discount'];
            if (defined('ORDER_MANAGEMENT')) {
                $cart['products'][$_id]['discount'] = @$data['discount'];
            }

            $company_id = $data['company_id'] ?? db_get_memoized_field("SELECT company_id FROM ?:products WHERE product_id = ?i", $product_id);
            $cart['products'][$_id]['company_id'] = $company_id;

            if (!empty($data['saved_object_id'])) {
                $cart['products'][$_id]['object_id'] = $data['saved_object_id'];
            }

            $cart['products'][$_id]['w_green_tax'] = db_get_memoized_field("SELECT w_green_tax FROM ?:products WHERE product_id = ?i", $product_id);

            $return_period = fn_is_returnable_product($product_id);
            if ($return_period && !empty($cart['products'][$_id]['product_id'])) {
                $cart['products'][$_id]['return_period'] = $cart['products'][$_id]['extra']['return_period'] = $return_period;
            }
            $ids[$_id] = $product_id;

            if (SubscriptionProduct::class === $data['productClassName']) {
                $cart['products'][$_id] = array_merge(
                    $cart['products'][$_id],
                    $data
                );
            }
        }

        $cart['recalculate'] = true;

        return $ids;

    } else {
        return false;
    }
}

function fn_form_cart($order_id, &$cart, &$auth)
{
    $order_info = container()
        ->get('marketplace.order.order_service')
        ->overrideLegacyOrder($order_id, false, false);

    if (empty($order_info)) {
        fn_set_notification('E', __('error'), __('object_not_found', array('[object]' => __('order'))),'','404');

        return false;
    }

    // Fill the cart
    foreach ($order_info['products'] as $_id => $item) {
        $_item = array (
            $item['product_id'] => array (
                'amount' => $item['amount'],
                'product_options' => (!empty($item['extra']['product_options']) ? $item['extra']['product_options'] : array()),
                'price' => $item['original_price'],
                'stored_discount' => 'Y',
                'stored_price' => 'Y',
                'discount' => (!empty($item['extra']['discount']) ? $item['extra']['discount'] : 0),
                'original_amount' => $item['amount'], // the original amount, that stored in order
                'original_product_data' => array ( // the original cart ID and amount, that stored in order
                    'cart_id' => $_id,
                    'amount' => $item['amount'],
                ),
            ),
        );
        if (isset($item['extra'])) {
            $_item[$item['product_id']]['extra'] = $item['extra'];
        }

        fn_add_product_to_cart($_item, $cart);
    }

    // Workaround for the add-ons that do not add a product to cart unless the parent product is already added.
    if (count($order_info['products']) > count($cart['products'])) {
        foreach ($order_info['products'] as $_id => $item) {
            if (empty($cart['products'][$_id])) {
                $_item = array (
                    $item['product_id'] => array (
                        'amount' => $item['amount'],
                        'product_options' => (!empty($item['extra']['product_options']) ? $item['extra']['product_options'] : array()),
                        'price' => $item['original_price'],
                        'stored_discount' => 'Y',
                        'stored_price' => 'Y',
                        'discount' => (!empty($item['extra']['discount']) ? $item['extra']['discount'] : 0),
                        'original_amount' => $item['amount'], // the original amount, that stored in order
                        'original_product_data' => array ( // the original cart ID and amount, that stored in order
                            'cart_id' => $_id,
                            'amount' => $item['amount'],
                        ),
                    ),
                );
                if (isset($item['extra'])) {
                    $_item[$item['product_id']]['extra'] = $item['extra'];
                }
                fn_add_product_to_cart($_item, $cart);
            }
        }
    }

    // Restore custom files
    $dir_path = 'order_data/' . $order_id;
    $customFilesStorage = container()->get('Wizacha\Storage\CustomFilesStorageService');

    if ($customFilesStorage->isExist($dir_path)) {
        $customFilesStorage->copy($dir_path, 'sess_data');
    }

    $cart['payment_id'] = $order_info['payment_id'];
    $cart['stored_taxes'] = 'Y';
    $cart['stored_discount'] = 'Y';
    $cart['taxes'] = $order_info['taxes'];
    $cart['promotions'] = !empty($order_info['promotions']) ? $order_info['promotions'] : array();

    $cart['shipping'] = (!empty($order_info['shipping'])) ? $order_info['shipping'] : array();
    $cart['stored_shipping'] = array();
    foreach ($cart['shipping'] as $sh_id => $v) {
        if (!empty($v['rates'])) {
            $cart['stored_shipping'][$sh_id] = array_sum($v['rates']);
        }
    }

    if (!empty($order_info['product_groups'])) {
        $cart['product_groups'] = $order_info['product_groups'];
        foreach ($order_info['product_groups'] as $group) {
            if (!empty($group['chosen_shippings'])) {
                foreach ($group['chosen_shippings'] as $key => $chosen_shipping) {
                    foreach ($group['shippings'] as $shipping_id => $shipping) {
                        if ($shipping_id == $chosen_shipping['shipping_id']) {
                            $cart['chosen_shipping'][$chosen_shipping['group_key']] = $shipping_id;
                        }
                    }
                }
            }
        }
    } else {
        $cart['product_groups'] = array();
    }

    $cart['notes'] = $order_info['notes'];
    $cart['details'] = $order_info['details'];
    $cart['payment_info'] = @$order_info['payment_info'];

    // Add order discount
    if (floatval($order_info['subtotal_discount'])) {
        $cart['stored_subtotal_discount'] = 'Y';
        $cart['subtotal_discount'] = $cart['original_subtotal_discount'] = fn_format_price($order_info['subtotal_discount']);
    }

    // Fill the cart with the coupons
    if (!empty($order_info['coupons'])) {
        $cart['coupons'] = $order_info['coupons'];
    }

    // Set the customer if exists
    $_data = array();
    if (!empty($order_info['user_id'])) {
        $_data = db_get_row("SELECT user_id, user_login as login FROM ?:users WHERE user_id = ?i", $order_info['user_id']);
    }
    $auth = fn_fill_auth($_data, array(), false, 'C');
    $auth['tax_exempt'] = $order_info['tax_exempt'];

    // Fill customer info
    $cart['user_data'] = fn_check_table_fields($order_info, 'user_profiles');
    $cart['user_data'] = fn_array_merge(fn_check_table_fields($order_info, 'users'), $cart['user_data']);
    if (!empty($order_info['fields'])) {
        $cart['user_data']['fields'] = $order_info['fields'];
    }
    fn_add_user_data_descriptions($cart['user_data']);

    return true;
}

//
// Calculate taxes for products or shippings
//
function fn_calculate_tax_rates($taxes, $price, $amount, $auth, &$cart, $companyId = null, $isEdp = false)
{
    static $destination_id;
    static $tax_description;
    static $user_data;

    $taxed_price = $price;

    if (!empty($cart['user_data'])) {
        $profile_fields = fn_get_profile_fields('O', $auth);
        $billing_population = fn_check_profile_fields_population($cart['user_data'], 'B', $profile_fields);
        $shipping_population = fn_check_profile_fields_population($cart['user_data'], 'S', $profile_fields);

        if (empty($auth['user_id']) && (!$shipping_population || !$billing_population)) {
            fn_define('ESTIMATION', true);
        }
    }

    if (empty($auth['user_id']) && (empty($cart['user_data']) || fn_is_empty($cart['user_data']) || $billing_population != true || $shipping_population != true) && Registry::get('runtime.checkout') && Registry::get('settings.Appearance.taxes_using_default_address') !== 'Y' && !defined('ESTIMATION')) {
        return false;
    }

    if ((empty($destination_id) || $user_data != @$cart['user_data'])) {
        // Get billing location
        $location = fn_get_customer_location($auth, $cart, true);
        $destination_id['B'] = fn_get_available_destination($location);

        // Get shipping location
        $location = fn_get_customer_location($auth, $cart);
        $destination_id['S'] = fn_get_available_destination($location);
    }

    $user = null;
    if (!empty($cart['user_data'])) {
        $user = container()->get('marketplace.user.user_service')->get($cart['user_data']['user_id']);
    }

    $company_data = fn_get_company_data($companyId);
    if (null !== $user
        && false !== $company_data
        && true === container()->get('marketplace.international_tax.shipping')->isTransnational(
            $user,
            $company_data['w_vat_number'],
            $company_data['country'],
            $isEdp
        )
    ) {
        return false;
    }

    $previous_priority = -1;
    $previous_price = '';

    foreach ($taxes as $key => $tax) {
        if (empty($tax['tax_id'])) {
            $tax['tax_id'] = $key;
        }

        if (empty($tax['priority'])) {
            $tax['priority'] = 0;
        }

        $_is_zero = floatval($taxed_price);
        if (empty($_is_zero)) {
            continue;
        }

        if (!empty($cart['stored_taxes']) && $cart['stored_taxes'] == 'Y' && (!empty($tax['rate_type']) || isset($cart['taxes'][$tax['tax_id']]['rate_value']))) {
            $rate = array (
                'rate_value' => isset($cart['taxes'][$tax['tax_id']]['rate_value']) ? $cart['taxes'][$tax['tax_id']]['rate_value'] : $tax['rate_value'],
                'rate_type' => isset($cart['taxes'][$tax['tax_id']]['rate_type']) ? $cart['taxes'][$tax['tax_id']]['rate_type'] : $tax['rate_type']
            );

        } else {
            if (!isset($destination_id[$tax['address_type']])) {
                continue;
            }

            $rate = db_get_memoized_row("SELECT destination_id, rate_value, rate_type FROM ?:tax_rates WHERE tax_id = ?i AND destination_id = ?i", $tax['tax_id'], $destination_id[$tax['address_type']]);
            if (!@floatval($rate['rate_value'])) {
                continue;
            }
        }

        $base_price = ($tax['priority'] == $previous_priority) ? $previous_price : $taxed_price;

        if ($rate['rate_type'] == TaxRateType::PERCENT()->getValue()) {
            // If tax is included into the price
            if ($tax['price_includes_tax'] == 'Y') {
                $_tax = fn_format_price($base_price - $base_price / ( 1 + ($rate['rate_value'] / 100)), CART_PRIMARY_CURRENCY, 4);
                // If tax is NOT included into the price
            } else {
                $_tax = fn_format_price($base_price * ($rate['rate_value'] / 100), CART_PRIMARY_CURRENCY, 4);
                $taxed_price += $_tax;
            }

        } else {
            $_tax = fn_format_price($rate['rate_value'], CART_PRIMARY_CURRENCY, 4);
            // If tax is NOT included into the price
            if ($tax['price_includes_tax'] != 'Y') {
                $taxed_price += $_tax;
            }
        }

        $previous_priority = $tax['priority'];
        $previous_price = $base_price;

        if (empty($tax_description[$tax['tax_id']])) {
            $tax_description[$tax['tax_id']] = db_get_memoized_field("SELECT tax FROM ?:tax_descriptions WHERE tax_id = ?i AND lang_code = ?s", $tax['tax_id'], (string) GlobalState::interfaceLocale());
        }

        $taxes_data[$tax['tax_id']] = array (
            'rate_type' => $rate['rate_type'],
            'rate_value' => $rate['rate_value'],
            'price_includes_tax' => $tax['price_includes_tax'],
            'regnumber' => @$tax['regnumber'],
            'priority' => @$tax['priority'],
            'tax_subtotal' => fn_format_price($_tax * $amount, CART_PRIMARY_CURRENCY, 4),
            'description' => $tax_description[$tax['tax_id']],
        );
    }

    return empty($taxes_data) ? false : $taxes_data;
}

function fn_get_predefined_statuses($type)
{
    $statuses = array(
        'profiles' => array(
            'A' => __('active'),
            'P' => __('pending'),
            'F' => __('available'),
            'D' => __('declined')
        ),
    );

    if ($type == 'discussion') {
        $statuses['discussion'] = array(
            'A' => __('approved'),
            'D' => __('disapproved')
        );
    }
    if ($type == 'news') {
        $statuses['news'] = array(
            'A' => __('active'),
            'D' => __('disabled'),
            'S' => __('sent')
        );
    }
    return $statuses[$type];
}

//
//Get order payment data
//
function fn_get_payment_data($payment_id, $object_id = 0, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $data = db_get_memoized_row("SELECT * FROM ?:payment_descriptions WHERE payment_id = ?i AND lang_code = ?s", $payment_id, $lang_code);

    return $data;
}

/**
 * Gets list of default statuses
 *
 * @param string $status current object status
 * @param boolean $add_hidden includes 'hiden' status
 * @param string $lang_code 2-letter language code (e.g. 'en', 'ru', etc.)
 * @return array statuses list
 */
function fn_get_default_statuses($status, $add_hidden, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $statuses = array (
        'A' => __('active', '', $lang_code),
        'D' => __('disabled', '', $lang_code),
    );

    if ($add_hidden) {
        $statuses['H'] = __('hidden', '', $lang_code);
    }

    if ($status === 'N') {
        $statuses['P'] = __('pending', '', $lang_code);
        $statuses['N'] = __('new', '', $lang_code);
    }

    return $statuses;
}

function fn_get_status_params($status, $type = STATUSES_ORDER)
{
    return db_get_memoized_hash_single_array("SELECT param, value FROM ?:status_data WHERE status = ?s AND type = ?s", array('param', 'value'), $status, $type);
}

/**
 * Gets parameter value of the status
 *
 * @param string $status Status code
 * @param string $param Parameter name
 * @param string $type Status type (order type defualt)
 * @return string Parameter value
 */
function fn_get_status_param_value($status, $param, $type = STATUSES_ORDER)
{
    return db_get_memoized_field("SELECT value FROM ?:status_data WHERE status = ?s AND param = ?s AND type = ?s", $status, $param, $type);
}

//
// Delete product from the cart
//
function fn_delete_cart_product(&$cart, $cart_id, $full_erase = true)
{
    if (!empty($cart_id) && !empty($cart['products'][$cart_id])) {
        // Delete saved product files
        if (isset($cart['products'][$cart_id]['extra']['custom_files'])) {
            $customFilesStorage = container()->get('Wizacha\Storage\CustomFilesStorageService');
            foreach ($cart['products'][$cart_id]['extra']['custom_files'] as $option_id => $images) {
                if (!empty($images)) {
                    foreach ($images as $image) {
                        $customFilesStorage->delete($image['path']);
                        $customFilesStorage->delete($image['path'] . '_thumb');
                    }
                }
            }
        }

        unset($cart['products'][$cart_id]);
        foreach ($cart['product_groups'] as $group_key => $group) {
            if (isset($group['products'][$cart_id])) {
                unset($cart['product_groups'][$group_key]['products'][$cart_id]);
            }
        }
        $cart['recalculate'] = true;
    }

    return true;
}

//
// This function calculates product prices without taxes and with taxes
//
function fn_get_taxed_and_clean_prices(&$product, &$auth)
{
    $tax_value = 0;
    $included_tax = false;

    if (empty($product) || empty($product['product_id']) || empty($product['tax_ids'])) {
        return false;
    }
    if (isset($product['subtotal'])) {
        $tx_price =  $product['subtotal'];
    } elseif (empty($product['price'])) {
        $tx_price = 0;
    } elseif (isset($product['discounted_price'])) {
        $tx_price = $product['discounted_price'];
    } else {
        $tx_price = $product['price'];
    }

    $product_taxes = fn_get_set_taxes($product['tax_ids']);

    $calculated_data = fn_calculate_tax_rates($product_taxes, $tx_price, 1, $auth, $_SESSION['cart']);
    // Apply taxes to product subtotal
    if (!empty($calculated_data)) {
        foreach ($calculated_data as $_k => $v) {
            $tax_value += $v['tax_subtotal'];
            if ($v['price_includes_tax'] != 'Y') {
                $included_tax = true;
                $tx_price += $v['tax_subtotal'];
            }
        }
    }

    $product['clean_price'] = $tx_price - $tax_value;
    $product['taxed_price'] = $tx_price;
    $product['taxes'] = $calculated_data;
    $product['included_tax'] = $included_tax;

    return true;
}

function fn_clear_cart(&$cart, $complete = false, $clear_all = false)
{
    if ($clear_all) {
        $cart = array();
    } else {
        $cart = array (
            'products' => array(),
            'recalculate' => false,
            'user_data' => !empty($cart['user_data']) && $complete == false ? $cart['user_data'] : array(),
        );
    }

    return true;
}

//
// Cleanup payment information
//
function fn_cleanup_payment_info($order_id = '', $payment_info, $silent = false)
{
    if ($silent == false) {
        fn_set_progress('echo', __('processing_order') . '&nbsp;<b>#'.$order_id.'</b>...');
    }

    if (!is_array($payment_info)) {
        $info = @unserialize(fn_decrypt_text($payment_info));
    } else {
        $info = $payment_info;
    }

    if (!empty($info['cvv2'])) {
        $info['cvv2'] = 'XXX';
    }
    if (!empty($info['card_number'])) {
        $info['card_number'] = str_replace(array('-', ' '), '', $info['card_number']);
        $info['card_number'] = substr_replace($info['card_number'], str_repeat('X', strlen($info['card_number']) - 4), 0, strlen($info['card_number']) - 4);
    }

    foreach (array('expiry_month', 'expiry_year') as $v) {
        if (!empty($info[$v])) {
            $info[$v] = 'XX';
        }
    }

    $_data = fn_encrypt_text(serialize($info));
    if (!empty($order_id)) {
        db_query(
            "UPDATE ?:order_data SET data = ?s WHERE order_id = ?i AND type = ?s",
            $_data,
            $order_id,
            OrderDataType::PAYMENT_INFO()->getValue()
        );
    } else {
        return $_data;
    }
}

//
// Checks if order can be placed
//
function fn_allow_place_order(&$cart)
{
    $total = Registry::get('settings.General.min_order_amount_type') == 'products_with_shippings' ? $cart['total'] : $cart['subtotal'];

    $cart['amount_failed'] = (Registry::get('settings.General.min_order_amount') > $total && floatval($total));

    if (!empty($cart['amount_failed']) || !empty($cart['shipping_failed']) || !empty($cart['company_shipping_failed'])) {
        return false;
    }

    return true;
}

/**
 * Returns orders
 *
 * @param array $params array with search params
 * @param int $items_per_page
 * @param bool $get_totals
 * @param string $lang_code
 * @return array
 */

function fn_get_orders($params, $items_per_page = 0, $get_totals = false, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    // Init filter
    $params = LastView::instance()->update('orders', $params);

    // Set default values to input params
    if (\array_key_exists('page', $params) === true
        && (int) $params['page'] >= 1
    ) {
        $page = $params['page'];
    } else {
        $page = 1;
    }
    $default_params = array (
        'page' => $page,
        'items_per_page' => $items_per_page
    );

    $params = array_merge($default_params, $params);
    $status_param_list = empty($params['status']) ? [] : (\is_array($params['status']) ? $params['status'] : [$params['status']]);

    if (AREA != 'C') {
        $params['include_incompleted'] = empty($params['include_incompleted']) ? false : $params['include_incompleted']; // default incomplited orders should not be displayed
        if (in_array(STATUS_INCOMPLETED_ORDER, $status_param_list)) {
            $params['include_incompleted'] = true;
        }
    } else {
        $params['include_incompleted'] = false;
    }

    // Define fields that should be retrieved
    $fields = array (
        "distinct ?:orders.order_id",
        "?:orders.issuer_id",
        "?:orders.user_id",
        "?:orders.basket_id",
        "?:orders.is_parent_order",
        "?:orders.parent_order_id",
        "?:orders.company_id",
        "?:orders.timestamp",
        "?:orders.firstname",
        "?:orders.lastname",
        "?:orders.email",
        "?:orders.status",
        "?:orders.is_customer_professional",
        "?:orders.customer_company",
        "?:orders.customer_legal_identifier",
        "?:orders.customer_intra_european_community_vat",
        "?:orders.customer_job_title",
        "?:orders.customer_account_comment",
        "?:orders.customer_external_identifier",
        "?:orders.w_last_status_change",
        "oa.total_incl_taxes/10000 total",
        "?:orders.customer_total",
        "?:orders.marketplace_discount_total",
        "?:orders.workflow_status",
        "?:orders.workflow_current_module_name",
        "?:orders.workflow_current_step_name",
        "?:orders.canceled",
        "?:orders.refunded",
        "?:orders.subscription_id",
        "?:orders.is_paid",
        "?:orders.carriage_paid",
        "?:orders.refund_status",
        "?:orders.extra",
        "invoice_docs.doc_id as invoice_id",
        "memo_docs.doc_id as credit_memo_id",
        "?:orders.do_not_create_invoice",
    );

    // Define sort fields
    $sortings = array (
        'order_id' => "?:orders.order_id",
        'status' => "?:orders.status",
        'customer' => array("?:orders.lastname", "?:orders.firstname"),
        'email' => "?:orders.email",
        'date' => array("?:orders.timestamp", "?:orders.order_id"),
        'total' => "?:orders.total",
    );

    if($params['w_company_type']) {
        $params['company_name'] = true; // the function fn_get_orders will make the join thanks to this line.
        $fields[] = '?:companies.w_company_type as w_company_type';
    }

    if (isset($params['compact']) && $params['compact'] == 'Y') {
        $union_condition = ' OR ';
    } else {
        $union_condition = ' AND ';
    }

    $_condition = $group = '';
    $join = ' LEFT JOIN doctrine_order_transactions ot ON ot.order_id = ?:orders.order_id
    LEFT JOIN doctrine_order_amounts oa ON oa.order_id = ?:orders.order_id ';
    $condition = " AND ?:orders.is_parent_order != 'Y' ";
    $condition .= fn_get_company_condition('?:orders.company_id');

    //Do not add status condition when we comes from Organisation
    if (!isset($params['organisation_orders'])) {
        $condition = \Wizacha\Order::statusCondition(
            \Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance()),
            AREA,
            $_SESSION['auth']['user_id'],
            $condition
        );
    }

    if (isset($params['cname']) && fn_string_not_empty($params['cname'])) {
        $customer_name = fn_explode(' ', $params['cname']);
        $customer_name = array_filter($customer_name, "fn_string_not_empty");
        if (sizeof($customer_name) == 2) {
            $_condition .= db_quote(" $union_condition ?:orders.firstname LIKE ?l AND ?:orders.lastname LIKE ?l", "%" . array_shift($customer_name) . "%", "%" . array_shift($customer_name) . "%");
        } else {
            $_condition .= db_quote(" $union_condition (?:orders.firstname LIKE ?l OR ?:orders.lastname LIKE ?l)", "%" . trim($params['cname']) . "%", "%" . trim($params['cname']) . "%");
        }
    }

    //Join table cscart_order_details if one condition (product_name or product_code) is true
    if ((\array_key_exists('product_name', $params) === true && $params['product_name'] != '')
        || (\array_key_exists('product_code', $params) === true && $params['product_code'] != '')
    ) {
        $join .= ' LEFT JOIN cscart_order_details ON cscart_order_details.order_id = ?:orders.order_id';
    }

    if (\array_key_exists('product_name', $params) === true && $params['product_name'] != '') {
        $join .= ' LEFT JOIN cscart_product_descriptions ON cscart_product_descriptions.product_id = cscart_order_details.product_id';
        $_condition .= db_quote(" $union_condition cscart_product_descriptions.product LIKE ?s", "%" . trim($params['product_name']) . "%");
    }

    if (\array_key_exists('product_code', $params) === true && $params['product_code'] != '') {
        $_condition .= db_quote(" $union_condition cscart_order_details.product_code LIKE ?s", "%" . trim($params['product_code']) . "%");
    }

    if (isset($params['issuer']) && fn_string_not_empty($params['issuer'])) {
        $issuer_name = fn_explode(' ', $params['issuer']);
        $issuer_name = array_filter($issuer_name, "fn_string_not_empty");
        if (sizeof($issuer_name) == 2) {
            $issuer_ids = db_get_field("SELECT user_id FROM ?:users WHERE user_type IN ('A', 'V') AND  firstname LIKE ?l AND lastname LIKE ?l", "%" . array_shift($issuer_name) . "%", "%" . array_shift($issuer_name) . "%");
        } else {
            $issuer_ids = db_get_field("SELECT user_id FROM ?:users WHERE user_type IN ('A', 'V') AND (firstname LIKE ?l OR lastname LIKE ?l)", "%" . trim($params['issuer']) . "%", "%" . trim($params['issuer']) . "%");
        }

        $_condition .= db_quote(" $union_condition ?:orders.issuer_id IN (?a)", $issuer_ids);
    }

    if (isset($params['company_id']) && $params['company_id'] != '') {
        $condition .= db_quote(' AND ?:orders.company_id = ?i ', $params['company_id']);
    }

    if (!empty($params['tax_exempt'])) {
        $condition .= db_quote(" AND ?:orders.tax_exempt = ?s", $params['tax_exempt']);
    }

    if (isset($params['email']) && fn_string_not_empty($params['email'])) {
        $_condition .= db_quote(" $union_condition ?:orders.email LIKE ?l", "%" . trim($params['email']) . "%");
    }

    if (!empty($params['user_id'])) {
        $condition .= db_quote(' AND ?:orders.user_id IN (?n)', $params['user_id']);
    }

    if (!empty($params['payment_id'])) {
        $condition .= db_quote(' AND ?:orders.payment_id IN (?n)', $params['payment_id']);
    }

    if (!empty($params['created_after'])) {
        $date = new DateTime($params['created_after']);

        $condition .= db_quote(" AND ?:orders.timestamp >= ?i", $date->getTimestamp());
    }

    if (!empty($params['created_before'])) {
        $date = new DateTime($params['created_before']);

        $condition .= db_quote(" AND ?:orders.timestamp <= ?i", $date->getTimestamp());
    }

    if (isset($params['last_status_change_is_after']) && $params['last_status_change_is_after'] !== '') {
        if (is_numeric($params['last_status_change_is_after'])) {
            // BO use a timestamp
            $formattedDate = date('Y-m-d H:i:s', $params['last_status_change_is_after']);
        } else {
            // API use a string RFC3339 formatted
            $dateTime = \DateTime::createFromFormat(\DateTime::RFC3339, $params['last_status_change_is_after']);
            if (false === ($dateTime instanceof \DateTime)) {
                throw new InvalidArgumentException('"last_status_change_is_after" is not a valid RFC3339 date format.');
            }
            $formattedDate = $dateTime->format('Y-m-d H:i:s');
        }

        $condition .= db_quote(" AND ?:orders.w_last_status_change >= ?s", $formattedDate);
    }

    if (isset($params['last_status_change_is_before']) && $params['last_status_change_is_before'] !== '') {
        if (is_numeric($params['last_status_change_is_before'])) {
            // BO use a timestamp
            $formattedDate = date('Y-m-d H:i:s', $params['last_status_change_is_before']);
        } else {
            // API use a string RFC3339 formatted
            $dateTime = \DateTime::createFromFormat(\DateTime::RFC3339, $params['last_status_change_is_before']);
            if (false === ($dateTime instanceof \DateTime)) {
                throw new InvalidArgumentException('"last_status_change_is_before" is not a valid RFC3339 date format.');
            }
            $formattedDate = $dateTime->format('Y-m-d H:i:s');
        }

        $condition .= db_quote(" AND ?:orders.w_last_status_change <= ?s", $formattedDate);
    }

    if (isset($params['total_from']) && fn_is_numeric($params['total_from'])) {
        $condition .= db_quote(" AND ?:orders.total >= ?d", fn_convert_price($params['total_from']));
    }

    if (!empty($params['total_to']) && fn_is_numeric($params['total_to'])) {
        $condition .= db_quote(" AND ?:orders.total <= ?d", fn_convert_price($params['total_to']));
    }

    $paid_statuses = LegacyOrderStatus::getPaidStatuses();

    if (count($status_param_list) > 0) {
        // if status translations from the workflow is activated AND is filtered by STATUS_INCOMPLETED_ORDER
        if (container()->getParameter('feature.activate_workflow_translation') === true && $params['include_incompleted'] === false) {
            if (\array_key_exists('inclusive_status_list', $params) && $params['inclusive_status_list'] === true) {
                $condition .= db_quote(' AND (CONCAT_WS("_", "workflow", ?:orders.workflow_current_module_name, ?:orders.workflow_current_step_name, ?:orders.workflow_status) IN (?a) and canceled = 0)', $status_param_list);
            } else {
                foreach ($status_param_list as $status) {
                    if ($status === 'workflow_canceled') {
                        $condition .= db_quote(' AND canceled = ?a', 1);
                    } elseif ($status === 'workflow_refunded') {
                        $condition .= db_quote(' AND refunded = ?a', 1);
                    } else {
                        $condition .= db_quote(' AND CONCAT_WS("_", "workflow", ?:orders.workflow_current_module_name, ?:orders.workflow_current_step_name, ?:orders.workflow_status) = ?a', $status);
                    }
                }
            }
        } else {
            $condition .= db_quote(' AND ?:orders.status IN (?a)', $status_param_list);
            $paid_statuses = array_intersect($paid_statuses, $status_param_list);
        }
    }

    if (!empty($params['workflow'])) {

        if ($params['workflow'] == 'workflow_canceled') {
            $condition .= db_quote(' AND canceled = ?a', 1);
        } elseif ($params['workflow'] == 'workflow_refunded') {
            $condition .= db_quote(' AND refunded = ?a', 1);
        } else {
            $condition .= db_quote(' AND REPLACE(CONCAT_WS("", "workflow", ?:orders.workflow_current_module_name, ?:orders.workflow_current_step_name, ?:orders.workflow_status), "-", "") = ?a', str_replace(['_', '-'], ['', ''], $params['workflow']));
        }
    }

    if (empty($params['include_incompleted'])) {
        $condition .= db_quote(' AND ?:orders.status != ?s', STATUS_INCOMPLETED_ORDER);
    }

    if (!empty($params['order_id'])) {
        $_condition .= db_quote($union_condition . ' ?:orders.order_id IN (?n)', (!is_array($params['order_id']) && (strpos($params['order_id'], ',') !== false) ? explode(',', $params['order_id']) : $params['order_id']));
    }

    if (!empty($params['p_ids']) || !empty($params['product_view_id'])) {
        $arr = (strpos($params['p_ids'], ',') !== false || !is_array($params['p_ids'])) ? explode(',', $params['p_ids']) : $params['p_ids'];

        if (empty($params['product_view_id'])) {
            $condition .= db_quote(" AND ?:order_details.product_id IN (?n)", $arr);
        } else {
            $condition .= db_quote(" AND ?:order_details.product_id IN (?n)", db_get_fields(fn_get_products(array('view_id' => $params['product_view_id'], 'get_query' => true))));
        }

        $join .= " LEFT JOIN ?:order_details ON ?:order_details.order_id = ?:orders.order_id";
        $group .=  " GROUP BY ?:orders.order_id ";
    }

    if (\array_key_exists('transaction_reference', $params) === true && fn_string_not_empty($params['transaction_reference']) === true) {
        $tranRef = \trim($params['transaction_reference']);
        /**
         * Pour mangopay : la transaction est enregistrée dans 2 colonnes de la table doctrine_order_transactions
         * 1. transaction_reference
         * 2. Le libelle est enregistré dans la colonne processor_informations
         * NB: La colonne processor_informations contient des données sérialisées
         *
         * Pour lemonway : la transaction de référence est enregistrée dans une seule colonne
         * C'est la transaction_refernce
         *
         * Principe de fonctionnement du filtre
         * Récuperer la transaction de référence, et la comparer avec la valeur du filtre
         */
        $condition .= db_quote(' AND ot.type = ?l
                    AND ?:orders.order_id IN (
                        SELECT dot.order_id
                        FROM `doctrine_order_transactions` AS `dot`
                        where dot.order_id = ?:orders.order_id
                        AND LOCATE (?l, IF(processor_name = \'mangopay\',
                               CONCAT(dot.transaction_label, "-" , dot.transaction_reference)
                                -- Transaction reference LEMONWAY
                                , dot.transaction_reference
                            )
                        ) > 0
                    )', TransactionType::BANK_WIRE(), $tranRef);
    }

    if (!empty($params['product_ids'])) {

        $condition .= db_quote(" AND ?:order_details.product_id IN (?n)", $params['product_ids']);

        $join .= " LEFT JOIN ?:order_details ON ?:order_details.order_id = ?:orders.order_id";
        $group .=  " GROUP BY ?:orders.order_id ";
    }

    if (true === \array_key_exists('refund_status_label', $params)) {
        $fields[] = "MAX(doctrine_order_refunds.status) as doctrine_order_refund_status";
        $sortings["refund_status_label"] = "doctrine_order_refunds.status";

        $join .= " LEFT JOIN doctrine_order_refunds ON ?:orders.order_id = doctrine_order_refunds.order_id ";

        if (\substr_count($group, "GROUP BY ?:orders.order_id") === 0) {
            $group .=  " GROUP BY ?:orders.order_id ";
        }

    }

    if (\array_key_exists('company_ids', $params) === true) {
        if (\is_countable($params['company_ids']) === true && \count($params['company_ids']) > 0) {
            $condition .= db_quote(" AND ?:orders.company_id IN (?n)", $params['company_ids']);
        } elseif (\is_numeric($params['company_ids'])) {
            $condition .= db_quote(" AND ?:orders.company_id=?i", $params['company_ids']);
        } else {
            throw new \UnexpectedValueException("parameter company_ids must be an array or an integer.");
        }
    }

    if (!empty($params['admin_user_id'])) {
        $condition .= db_quote(" AND ?:new_orders.user_id = ?i", $params['admin_user_id']);
        $join .= " LEFT JOIN ?:new_orders ON ?:new_orders.order_id = ?:orders.order_id";
    }

    $docs_conditions = array();
    if (!empty($params['invoice_id']) || !empty($params['has_invoice'])) {
        if (!empty($params['has_invoice'])) {
            $docs_conditions[] = "invoice_docs.doc_id IS NOT NULL";
        } elseif (!empty($params['invoice_id'])) {
            $docs_conditions[] = db_quote("invoice_docs.doc_id = ?i", $params['invoice_id']);
        }
    }
    $join .= \sprintf(
        " LEFT JOIN ?:order_docs as invoice_docs
            ON invoice_docs.order_id = ?:orders.order_id
            AND invoice_docs.type = '%s'
        ",
        AppearanceType::INVOICE()->getValue()
    );

    if (!empty($params['credit_memo_id']) || !empty($params['has_credit_memo'])) {
        if (!empty($params['has_credit_memo'])) {
            $docs_conditions[] = "memo_docs.doc_id IS NOT NULL";
        } elseif (!empty($params['credit_memo_id'])) {
            $docs_conditions[] = db_quote("memo_docs.doc_id = ?i", $params['credit_memo_id']);
        }
    }
    $join .= \sprintf(
        " LEFT JOIN ?:order_docs as memo_docs
            ON memo_docs.order_id = ?:orders.order_id
            AND memo_docs.type = '%s'
        ",
        AppearanceType::CREDIT_MEMO()->getValue()
    );

    if (!empty($docs_conditions)) {
        $condition .= ' AND (' . implode(' OR ', $docs_conditions) . ')';
    }

    if (!empty($params['shippings'])) {
        $set_conditions = array();
        foreach ($params['shippings'] as $v) {
            $set_conditions[] = db_quote("FIND_IN_SET(?s, ?:orders.shipping_ids)", $v);
        }
        $condition .= ' AND (' . implode(' OR ', $set_conditions) . ')';
    }

    if (!empty($params['payments'])) {
        $condition .= db_quote(" AND ?:orders.payment_id IN (?a)", $params['payments']);
    }

    if (!empty($params['period']) && $params['period'] != 'A') {
        list($params['time_from'], $params['time_to']) = fn_create_periods($params);

        $condition .= db_quote(" AND (?:orders.timestamp >= ?i AND ?:orders.timestamp <= ?i)", $params['time_from'], $params['time_to']);
    }

    if (!empty($params['custom_files']) && $params['custom_files'] == 'Y') {
        $condition .= db_quote(" AND ?:order_details.extra LIKE ?l", '%custom_files%');

        if (empty($params['p_ids']) && empty($params['product_view_id'])) {
            $join .= " LEFT JOIN ?:order_details ON ?:order_details.order_id = ?:orders.order_id";
        }
    }

    if (!empty($params['company_name'])) {
        $fields[] = '?:companies.company as company_name';
        $join .= " LEFT JOIN ?:companies ON ?:companies.company_id = ?:orders.company_id";
    }

    if (\strlen($params['payments_failed']) > 0) {
        $join .= " LEFT JOIN doctrine_order_transactions ON doctrine_order_transactions.order_id = ?:orders.order_id";
        $condition .= db_quote(' AND doctrine_order_transactions.status = ?s', TransactionStatus::FAILED()->getValue());
        $condition .= db_quote(' AND ?:orders.status = ?s', STATUSES_ORDER);
    }

    //Specific condition for the GET path /organisations/{id}/orders
    if (isset($params['organisation_orders'])) {
        $condition .= db_quote(' AND ?:orders.order_id IN (?n)', $params['organisation_orders']);
    }

    if (!empty($_condition)) {
        $condition .= ' AND (' . ($union_condition == ' OR ' ? '0 ' : '1 ') . $_condition . ')';
    }

    $sorting = db_sort($params, $sortings, 'date', 'desc');

    // Used for Extended search
    if (!empty($params['get_conditions'])) {
        return array($fields, $join, $condition);
    }

    if (\is_string($params['refund_status']) && \strlen($params['refund_status']) > 0) {
        $condition .= db_quote(' AND ?:orders.refund_status = ?i', (int) $params['refund_status']);
    }

    if (\is_string($params['subscription_link_status']) === true
        && $params['subscription_link_status'] !== ''
        && SubscriptionLinkStatus::isValid((int) $params['subscription_link_status']) === true
    ) {
        if ((int) $params['subscription_link_status'] === SubscriptionLinkStatus::RECURRING()->getValue()) {
            $join .= ' LEFT JOIN doctrine_subscription sub ON sub.first_order_id = ?:orders.order_id';
            $condition .= ' AND ?:orders.subscription_id IS NOT NULL';
        }

        if ((int) $params['subscription_link_status'] === SubscriptionLinkStatus::INITIATING()->getValue()) {
            $join .= ' LEFT JOIN doctrine_subscription sub ON sub.first_order_id = ?:orders.order_id';
            $condition .= ' AND sub.first_order_id = ?:orders.order_id';
        }

        if ((int) $params['subscription_link_status'] === SubscriptionLinkStatus::NOT_LINKED()->getValue()) {
            $join .= ' LEFT JOIN doctrine_subscription sub ON sub.first_order_id = ?:orders.order_id';
            $condition .= ' AND ?:orders.subscription_id IS NULL';
            $condition .= ' AND sub.id IS NULL';
        }
    }

    if (true === \array_key_exists('extra', $params) && true === \is_array($params['extra'])) {
        $extraCondition = SearchCriteria::getFormattedConditionFromExtraCriteria('?:orders', $params['extra']);

        if ('' !== $extraCondition && '' !== $condition) {
            $condition .= ' AND (' . $extraCondition . ') ';
        }
    }

    if (true === \array_key_exists('extra_start_with', $params)) {
        $extraCondition = SearchCriteria::getFormattedConditionFromExtraCriteria(
            '?:orders',
            $params['extra_start_with'],
            true
        );

        if ('' !== $extraCondition && '' !== $condition) {
            $condition .= ' AND (' . $extraCondition . ') ';
        }
    }

    $params['total_items'] = db_get_field("SELECT COUNT(DISTINCT (?:orders.order_id)) FROM ?:orders $join WHERE 1 $condition");

    $limit = '';
    if (!empty($params['items_per_page'])) {
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    $orders = db_get_array('SELECT ' . implode(', ', $fields) . " FROM ?:orders $join WHERE 1 $condition $group $sorting $limit");

    $excludedTypes = \implode(',', array_map(
        static function ($transactionType) {
            return sprintf("'%s'", $transactionType->getValue());
        },
        TransactionType::getDispatchFundsTransferTypes()
    ));

    $grossTotalSql = "SELECT sum(t.total)/10000
            FROM (
                SELECT oa.total_incl_taxes total
                FROM ?:orders $join
                WHERE ot.type NOT IN ($excludedTypes)
                $condition $group
            ) as t";

    if ($get_totals == true) {
        $totals = array (
            'gross_total' => db_get_field($grossTotalSql),
            'totally_paid' => db_get_field("SELECT sum(t.total)/10000 FROM ( SELECT oa.total_incl_taxes total FROM ?:orders $join WHERE ?:orders.status IN (?a) $condition $group) as t", $paid_statuses),
        );

        $params['paid_statuses'] = $paid_statuses;
    }

    LastView::instance()->processResults('orders', $orders, $params);

    /** @var TransactionService */
    $transactionService = container()->get('marketplace.transaction.transaction_service');

    $transactionReferences = $transactionService
        ->findBankWireTransactionReferences(
            \array_column($orders, 'order_id')
        )
    ;

    $orders = array_map(
        function($order) use ($transactionReferences) {
            $order['workflow'] = str_replace(
                '-',
                '_',
                "workflow_{$order['workflow_current_module_name']}_{$order['workflow_current_step_name']}_{$order['workflow_status']}"
            );

            if ($order['canceled']) {
                $order['workflow'] = 'workflow_canceled';
            }

            //check if payment is refused by psp or bank
            $order['isPaymentRefused'] = false;
            $isPaymentRefused = container()->get('marketplace.transaction.transaction_service')->hasRefusedPayment($order['order_id']);
            if ($isPaymentRefused === true && $order['status'] === OrderStatus::STANDBY_BILLING) {
                $order['isPaymentRefused'] = true;
            }

            if ($order['refunded']) {
                $order['workflow'] = 'workflow_refunded';
            }
            $order['transaction_reference'] = container()->get('marketplace.transaction.transaction_service')->findBankWireTransactionReference($order['order_id']);

            if ($order['refunded']) {
                $order['workflow'] = 'workflow_refunded';
            }

            $order['transaction_reference'] = $transactionReferences[$order['order_id']];

            return $order;
        },
        $orders
    );

    return array($orders, $params, ($get_totals == true ? $totals : array()));
}

/**
 * Gets shipping method parameters by identifier
 *
 * @param int $shipping_id Shipping identifier
 * @return array Shipping parameters
 */
function fn_get_shipping_params($shipping_id)
{
    $params = array();
    if ($shipping_id) {
        $params = db_get_memoized_field("SELECT service_params FROM ?:shippings WHERE shipping_id = ?i", $shipping_id);
        $params = unserialize($params);
    }

    return $params;
}

/**
 * Send order notification
 *
 * @param array $order_info order information
 * @param array $edp_data information about downloadable products
 * @param mixed $force_notification user notification flag (true/false), if not set, will be retrieved from status parameters
 * @return array structured data
 */
function fn_order_notification(&$order_info, $edp_data = array(), $force_notification = array())
{
    $send_order_notification = true;

    if ($order_info['status'] == STATUS_INCOMPLETED_ORDER || $order_info['status'] == STATUS_PARENT_ORDER) {
        $send_order_notification = false;
    }

    //Copy edp_data in order_info an clean edp_data (avoid the specific mail for edp_data)
    $order_info['edp_data'] = $edp_data;
    $edp_data = null;

    if (!$send_order_notification) {
        return true;
    }

    if (!is_array($force_notification)) {
        $force_notification = fn_get_notification_rules($force_notification, !$force_notification);
    }
    $order_statuses = fn_get_statuses(STATUSES_ORDER, array(), true, false, (string) GlobalState::interfaceLocale(), $order_info['company_id']);
    $status_params = $order_statuses[$order_info['status']]['params'];

    $notify_user = isset($force_notification['C']) ? $force_notification['C'] : (!empty($status_params['notify']) && $status_params['notify'] == 'Y' ? true : false);
    $notify_department = isset($force_notification['A']) ? $force_notification['A'] : (!empty($status_params['notify_department']) && $status_params['notify_department'] == 'Y' ? true : false);
    $notify_vendor = isset($force_notification['V']) ? $force_notification['V'] : (!empty($status_params['notify_vendor']) && $status_params['notify_vendor'] == 'Y' ? true : false);

    if ($notify_user == true || $notify_department == true || $notify_vendor == true) {
        $eventDispatcher = container()->get('event_dispatcher');
        $eventDispatcher->dispatch(
            new OrderStatusChanged($order_info, $notify_user, $notify_vendor, $notify_department),
            OrderStatusChanged::class
        );
    }
}

/**
 *
 * @param int $payment_id payment ID
 * @param string $action action
 * @return bool true if the payment_id is not a fake payment, and have a real processor
 */
function fn_check_processor_script($payment_id, $additional_params = false)
{

    if ($additional_params) {
        if (!empty($_REQUEST['skip_payment']) && AREA == 'C') {
            return false;
        }
    }

    $payment = fn_get_payment_method_data((int) $payment_id);

    return !empty($payment['processor_id']);
}

function fn_add_product_options_files($product_data, &$cart, $update = false, $location = 'cart')
{
    // Check if products have cusom images
    if (!$update) {
        $uploaded_data = fn_filter_uploaded_data('product_data');
    } else {
        $uploaded_data = fn_filter_uploaded_data('cart_products');
    }

    // Check for the already uploaded files
    if (!empty($product_data['custom_files']['uploaded'])) {
        $imagesStorageService = container()->get('Wizacha\Storage\ImagesStorageService');
        foreach ($product_data['custom_files']['uploaded'] as $file_id => $file_data) {
            if ($imagesStorageService->isExist('sess_data/' . fn_basename($file_data['path']))) {
                $id = $file_data['product_id'] . $file_data['option_id'] . $file_id;
                $uploaded_data[$id] = array(
                    'name' => $file_data['name'],
                    'path' => 'sess_data/' . fn_basename($file_data['path']),
                );

                $product_data['custom_files'][$id] = $file_data['product_id'] . '_' . $file_data['option_id'];
            }
        }
    }

    if (!empty($uploaded_data) && !empty($product_data['custom_files'])) {
        $files_data = array();

        foreach ($uploaded_data as $key => $file) {
            $file_info = fn_pathinfo($file['name']);
            $file['extension'] = empty($file_info['extension']) ? '' : $file_info['extension'];
            $file['is_image'] = fn_get_image_extension($file['type']);

            $_data = explode('_', $product_data['custom_files'][$key]);
            $product_id = empty($_data[0]) ? 0 : $_data[0];
            $option_id = empty($_data[1]) ? 0 : $_data[1];
            $file_id = str_replace($option_id . $product_id, '', $key);

            if (empty($file_id)) {
                $files_data[$product_id][$option_id][] = $file;
            } else {
                $files_data[$product_id][$option_id][$file_id] = $file;
            }
        }
    }

    unset($product_data['custom_files']);

    foreach ($product_data as $key => $data) {
        $product_id = (!empty($data['product_id'])) ? $data['product_id'] : $key;

        // Check if product has cusom images
        if ($update || isset($files_data[$key])) {
            $hash = $key;
        } else {
            $hash = $product_id;
        }

        $_options = fn_get_product_options($product_id);
        if (!empty($files_data[$hash]) && is_array($files_data[$hash])) {

            foreach ($files_data[$hash] as $option_id => $files) {
                foreach ($files as $file_id => $file) {
                    // Check for the allowed extensions
                    if (!empty($_options[$option_id]['allowed_extensions'])) {
                        if ((empty($file['extension']) && !empty($_options[$option_id]['allowed_extensions'])) || !preg_match("/\b" . $file['extension'] . "\b/i", $_options[$option_id]['allowed_extensions'])) {
                            fn_set_notification('E', __('error'), $file['name'] . ': ' . __('text_forbidden_uploaded_file_extension', array(
                                    '[ext]' => $file['extension'],
                                    '[exts]' => $_options[$option_id]['allowed_extensions']
                                )));
                            unset($files_data[$hash][$option_id][$file_id]);
                            continue;
                        }
                    }

                    // Check for the max file size

                    if (!empty($_options[$option_id]['max_file_size'])) {
                        if (empty($file['size'])) {
                            $file['size'] = filesize($file['path']);
                        }

                        if ($file['size'] > $_options[$option_id]['max_file_size'] * 1024) {
                            fn_set_notification('E', __('error'), $file['name'] . ': ' . __('text_forbidden_uploaded_file_size', array(
                                    '[size]' => $_options[$option_id]['max_file_size'] . ' kb'
                                )));
                            unset($files_data[$hash][$option_id][$file_id]);
                            continue;
                        }
                    }

                    $_file_path = 'sess_data/file_' . uniqid(TIME);

                    $customFilesStorage = container()->get('Wizacha\Storage\CustomFilesStorageService');
                    list(, $_file_path) = $customFilesStorage->put($_file_path, array(
                        'file' => $file['path']
                    ));

                    if (!$_file_path) {
                        fn_set_notification('E', __('error'), __('text_cannot_create_file', array(
                            '[file]' => $file['name']
                        )));

                        unset($files_data[$hash][$option_id][$file_id]);
                        continue;
                    }

                    $file['path'] = $_file_path;
                    $file['file'] = fn_basename($file['path']);

                    if ($file['is_image']) {
                        $file['thumbnail'] = 'image.custom_image?image=' . $file['file'] . '&type=T';
                        $file['detailed'] = 'image.custom_image?image=' . $file['file'] . '&type=D';
                    }

                    $file['location'] = $location;

                    if ($update) {
                        $cart['products'][$key]['extra']['custom_files'][$option_id][] = $file;
                    } else {
                        $data['extra']['custom_files'][$option_id][] = $file;

                    }
                }

                if ($update) {
                    if (!empty($cart['products'][$key]['product_options'][$option_id])) {
                        $cart['products'][$key]['product_options'][$option_id] = md5(serialize($cart['products'][$key]['extra']['custom_files'][$option_id]));
                    }
                } else {
                    if (!empty($data['extra']['custom_files'][$option_id])) {
                        $data['product_options'][$option_id] = md5(serialize($data['extra']['custom_files'][$option_id]));
                    }
                }
            }

            // Check the required options
            if (empty($data['extra']['parent'])) {
                foreach ($_options as $option) {
                    if ($option['option_type'] == 'F' && $option['required'] == 'Y' && !$update) {
                        if (empty($data['product_options'][$option['option_id']])) {
                            fn_set_notification('E', __('error'), __('product_cannot_be_added'));

                            unset($product_data[$key]);

                            return array($product_data, $cart);
                        }
                    }
                }
            }

        } else {
            if (empty($data['extra']['parent'])) {
                foreach ($_options as $option) {
                    if ($option['option_type'] == 'F' && $option['required'] == 'Y' && empty($cart['products'][$hash]['extra']['custom_files'][$option['option_id']]) && empty($data['extra']['custom_files'][$option['option_id']])) {
                        fn_set_notification('E', __('error'), __('product_cannot_be_added'));

                        unset($product_data[$key]);

                        return array($product_data, $cart);
                    }
                }
            }
        }

        if ($update) {
            foreach ($_options as $option) {
                if ($option['option_type'] == 'F' && empty($cart['products'][$key]['extra']['custom_files'][$option['option_id']])) {
                    unset($cart['products'][$key]['extra']['custom_files'][$option['option_id']]);
                    unset($cart['products'][$key]['product_options'][$option['option_id']]);
                    unset($data['product_options'][$option['option_id']]);
                }
            }
        }

        if (isset($cart['products'][$key]['extra']['custom_files'])) {
            foreach ($cart['products'][$key]['extra']['custom_files'] as $option_id => $files) {
                foreach ($files as $file) {
                    $data['extra']['custom_files'][$option_id][] = $file;
                }

                $data['product_options'][$option_id] = md5(serialize($files));
            }
        }

        $product_data[$key] = $data;
    }

    return array($product_data, $cart);
}

/**
 *   save stored taxes for products
 * @param array $cart cart
 * @param int $update_id   key of $cart['products'] to be updated
 * @param int $new_id  new key
 * @param bool $consider_existing  whether consider or not existing key
 */
function fn_update_stored_cart_taxes(&$cart, $update_id, $new_id, $consider_existing = false)
{
    if (!empty($cart['taxes']) && is_array($cart['taxes'])) {
        foreach ($cart['taxes'] as $t_id => $s_tax) {
            if (!empty($s_tax['applies']) && is_array($s_tax['applies'])) {
                $compare_key = 'P_' . $update_id;
                $new_key = 'P_' . $new_id;
                if (array_key_exists($compare_key, $s_tax['applies'])) {
                    $cart['taxes'][$t_id]['applies'][$new_key] = (isset($s_tax['applies'][$new_key]) && $consider_existing ? $s_tax['applies'][$new_key] : 0) + $s_tax['applies'][$compare_key];
                    unset($cart['taxes'][$t_id]['applies'][$compare_key]);
                }
            }
        }
    }
}

function fn_define_original_amount($product_id, $cart_id, &$product, $prev_product)
{
    if (!empty($prev_product['original_product_data']) && !empty($prev_product['original_product_data']['amount'])) {
        $tracking = db_get_memoized_field("SELECT tracking FROM ?:products WHERE product_id = ?i", $product_id);
        if ($tracking != 'O' || $tracking == 'O' && $prev_product['original_product_data']['cart_id'] == $cart_id) {
            $product['original_amount'] = $prev_product['original_product_data']['amount'];
        }
        $product['original_product_data'] = $prev_product['original_product_data'];
    } elseif (!empty($prev_product['original_amount'])) {
        $product['original_amount'] = $prev_product['original_amount'];
    }
}

function fn_get_shipments_info($params, $items_per_page = 0)
{
    // Init view params
    $params = LastView::instance()->update('shipments', $params);

    // Set default values to input params
    $default_params = array (
        'page' => 1,
        'items_per_page' => $items_per_page
    );

    $params = array_merge($default_params, $params);

    $fields_list = array(
        '?:shipments.shipment_id',
        '?:shipments.timestamp AS shipment_timestamp',
        '?:shipments.comments',
        '?:shipment_items.order_id',
        '?:orders.timestamp AS order_timestamp',
        '?:orders.s_firstname',
        '?:orders.s_lastname',
        '?:orders.user_id',
        '?:shippings.w_delivery_type',
        '?:shipments.delivery_date',
    );

    $joins = array(
        'LEFT JOIN ?:shipment_items ON (?:shipments.shipment_id = ?:shipment_items.shipment_id)',
        'LEFT JOIN ?:orders ON (?:shipment_items.order_id = ?:orders.order_id)',
    );

    $condition = '';
    if (Registry::get('runtime.company_id')) {
        $joins[] = 'LEFT JOIN ?:companies ON (?:companies.company_id = ?:orders.company_id)';
        $condition = db_quote(' AND ?:companies.company_id = ?i', Registry::get('runtime.company_id'));
    }

    $group = array(
        '?:shipments.shipment_id',
    );

    // Define sort fields
    $sortings = array (
        'id' => "?:shipments.shipment_id",
        'order_id' => "?:orders.order_id",
        'shipment_date' => "?:shipments.timestamp",
        'order_date' => "?:orders.timestamp",
        'customer' => array("?:orders.s_lastname", "?:orders.s_firstname"),
    );

    $sorting = db_sort($params, $sortings, 'id', 'desc');

    if (isset($params['advanced_info']) && $params['advanced_info']) {
        $fields_list[] = '?:shipments.shipping_id';
        $fields_list[] = '?:shipping_descriptions.shipping AS shipping';
        $fields_list[] = '?:shipments.tracking_number';
        $fields_list[] = '?:shipments.chronopost_skybill_number';
        $fields_list[] = '?:shipments.carrier';
        $fields_list[] = '?:shipments.label_url';

        $joins[] = ' LEFT JOIN ?:shippings ON (?:shipments.shipping_id = ?:shippings.shipping_id)';
        $joins[] = ' LEFT JOIN ?:shipping_descriptions ON (?:shippings.shipping_id = ?:shipping_descriptions.shipping_id)';

        $condition .= db_quote(' AND ?:shipping_descriptions.lang_code = ?s', (string) GlobalState::contentLocale());
    }

    if (!empty($params['order_id'])) {
        $condition .= db_quote(' AND ?:shipment_items.order_id = ?i', $params['order_id']);
    }

    if (!empty($params['shipment_id'])) {
        $condition .= db_quote(' AND ?:shipments.shipment_id = ?i', $params['shipment_id']);
    }

    if (isset($params['cname']) && fn_string_not_empty($params['cname'])) {
        $arr = fn_explode(' ', $params['cname']);
        foreach ($arr as $k => $v) {
            if (!fn_string_not_empty($v)) {
                unset($arr[$k]);
            }
        }
        if (sizeof($arr) == 2) {
            $condition .= db_quote(" AND ?:orders.firstname LIKE ?l AND ?:orders.lastname LIKE ?l", "%".array_shift($arr)."%", "%".array_shift($arr)."%");
        } else {
            $condition .= db_quote(" AND (?:orders.firstname LIKE ?l OR ?:orders.lastname LIKE ?l)", "%".trim($params['cname'])."%", "%".trim($params['cname'])."%");
        }
    }

    if (!empty($params['p_ids']) || !empty($params['product_view_id'])) {
        $arr = (strpos($params['p_ids'], ',') !== false || !is_array($params['p_ids'])) ? explode(',', $params['p_ids']) : $params['p_ids'];

        if (empty($params['product_view_id'])) {
            $condition .= db_quote(" AND ?:shipment_items.product_id IN (?n)", $arr);
        } else {
            $condition .= db_quote(" AND ?:shipment_items.product_id IN (?n)", db_get_fields(fn_get_products(array('view_id' => $params['product_view_id'], 'get_query' => true)), ','));
        }

        $joins[] = "LEFT JOIN ?:order_details ON ?:order_details.order_id = ?:orders.order_id";
    }

    if (!empty($params['shipment_period']) && $params['shipment_period'] != 'A') {
        $params['time_from'] = $params['shipment_time_from'];
        $params['time_to'] = $params['shipment_time_to'];
        $params['period'] = $params['shipment_period'];

        list($params['shipment_time_from'], $params['shipment_time_to']) = fn_create_periods($params);

        $condition .= db_quote(" AND (?:shipments.timestamp >= ?i AND ?:shipments.timestamp <= ?i)", $params['shipment_time_from'], $params['shipment_time_to']);
    }

    if (!empty($params['order_period']) && $params['order_period'] != 'A') {
        $params['time_from'] = $params['order_time_from'];
        $params['time_to'] = $params['order_time_to'];
        $params['period'] = $params['order_period'];

        list($params['order_time_from'], $params['order_time_to']) = fn_create_periods($params);

        $condition .= db_quote(" AND (?:orders.timestamp >= ?i AND ?:orders.timestamp <= ?i)", $params['order_time_from'], $params['order_time_to']);
    }

    $fields_list = implode(', ', $fields_list);
    $joins = implode(' ', $joins);
    $group = implode(', ', $group);

    if (!empty($group)) {
        $group = ' GROUP BY ' . $group;
    }

    $limit = '';
    if (!empty($params['items_per_page'])) {
        $params['total_items'] = db_get_field("SELECT COUNT(DISTINCT(?:shipments.shipment_id)) FROM ?:shipments $joins WHERE 1 $condition");
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    $shipments = db_get_array("SELECT $fields_list FROM ?:shipments $joins WHERE 1 $condition $group $sorting $limit");

    if (isset($params['advanced_info']) && $params['advanced_info'] && !empty($shipments)) {
        $shipment = reset($shipments);

        $order_info = container()
            ->get('marketplace.order.order_service')
            ->overrideLegacyOrder($shipment['order_id']);

        foreach ($shipments as $id => $shipment) {
            $items = db_get_array('SELECT item_id, amount FROM ?:shipment_items WHERE shipment_id = ?i', $shipment['shipment_id']);
            if (!empty($items)) {
                foreach ($items as $item) {
                    $shipments[$id]['products'][$item['item_id']] = $item['amount'];
                    if (!empty($order_info['products'][$item['item_id']]['extra']['group_key'])) {
                        $shipments[$id]['group_key'] = $order_info['products'][$item['item_id']]['extra']['group_key'];
                    } else {
                        $shipments[$id]['group_key'] = 0;
                    }
                }

            }
        }

        if (Settings::instance()->getValue('use_shipments', '', $order_info['company_id']) != 'Y') {
            foreach ($shipments as $id => $shipment) {
                $shipments[$id]['one_full'] = true;

                foreach ($order_info['products'] as $product_key => $product) {
                    $group_key = !empty($product['extra']['group_key']) ? $product['extra']['group_key'] : 0;
                    if ($shipment['group_key'] == $group_key) {
                        if (empty($shipment['products'][$product_key]) || $shipment['products'][$product_key] < $product['amount']) {
                            $shipments[$id]['one_full'] = false;
                            break;
                        }
                    }
                }
            }
        }
    }

    LastView::instance()->processResults('shipments_info', $shipments, $params);

    return array($shipments, $params);
}

/**
 * Verification that at least one product was chosen.
 *
 * @param array $products Array products data
 * @return bool true - if at least one product was chosen, else "false".
 */
function fn_check_shipped_products($products)
{
    $allow = true;
    $total_amount = 0;

    if (!empty($products) && is_array($products)) {
        foreach ($products as $key => $amount) {
            $total_amount += is_numeric($amount) ? $amount : 0;
        }

        if ($total_amount == 0) {
            $allow = false;
        }

    } else {
        $allow = false;
    }

    return $allow;
}

/**
 * Verification, that all products were delivered by the same shipment.
 *
 * @param array $shipments - shipments data.
 * @return bool true - if all products in the order were delivered by the same shipment
 */
function fn_one_full_shipped(&$shipments)
{
    $full_shipment = true;
    $sort_shipments = array();

    if (!empty($shipments) && is_array($shipments)) {
        foreach ($shipments as $shipment) {
            if (empty($shipment['one_full'])) {
                $full_shipment = false;
                break;
            }
            $sort_shipments[$shipment['group_key']] = $shipment;
        }
        if ($full_shipment) {
            $shipments = $sort_shipments;
        }
    }

    return $full_shipment;
}

/**
 * Create/update shipment
 *
 * @param array $shipment_data Array of shipment data.
 * @param int $shipment_id Shipment identifier
 * @param int $group_key Group number
 * @param bool $all_products
 * @param mixed $force_notification user notification flag (true/false), if not set, will be retrieved from status parameters
 * @param bool $force_all_product_shippment Used with handDelivery : Create one shippment  for all products, even if $shipment_data['products'] is empty.
 * @return int $shipment_id
 */
function fn_update_shipment($shipment_data, $shipment_id = 0, $group_key = 0, $all_products = false, $force_notification = array(), $force_all_product_shippment = false)
{

    if (!empty($shipment_id)) {
        $arow = db_query("UPDATE ?:shipments SET tracking_number = ?s, chronopost_skybill_number = ?s, carrier = ?s, label_url = ?s WHERE shipment_id = ?i", $shipment_data['tracking_number'], $shipment_data['chronopost_skybill_number'] ?? '', $shipment_data['carrier'], $shipment_data['label_url'] ?? '', $shipment_id);
        if ($arow === false) {
            fn_set_notification('E', __('error'), __('object_not_found', array('[object]' => __('shipment'))),'','404');
            $shipment_id = false;
        }
    } else {

        if (empty($shipment_data['order_id']) || empty($shipment_data['shipping_id'])) {
            return false;
        }

        $order_info = container()
            ->get('marketplace.order.order_service')
            ->overrideLegacyOrder($shipment_data['order_id'], false, true, true);

        $use_shipments = (Settings::instance()->getValue('use_shipments', '', $order_info['company_id']) == 'Y') ? true : false;

        if (!$use_shipments && empty($shipment_data['tracking_number'])) {
            return false;
        }

        if ((!$use_shipments && $all_products) || $force_all_product_shippment) {
            foreach ($order_info['product_groups'] as $group) {
                foreach ($group['products'] as $item_key => $product) {

                    if (!empty($product['extra']['group_key'])) {
                        if ($group_key == $product['extra']['group_key']) {
                            $shipment_data['products'][$item_key] = $product['amount'];
                        }
                    } elseif ($group_key == 0) {
                        $shipment_data['products'][$item_key] = $product['amount'];
                    }
                }
            }
        }

        if (!empty($shipment_data['products']) && fn_check_shipped_products($shipment_data['products'])) {

            foreach ($shipment_data['products'] as $key => $amount) {
                if (isset($order_info['products'][$key])) {
                    $amount = intval($amount);

                    if ($amount > ($order_info['products'][$key]['amount'] - $order_info['products'][$key]['shipped_amount'])) {
                        $shipment_data['products'][$key] = $order_info['products'][$key]['amount'] - $order_info['products'][$key]['shipped_amount'];
                    }
                } else {
                    // Stop if the item_id is not is the order products (e.g. in the API, a product_id given rather than an item_id)
                    return false;
                }
            }

            if (fn_check_shipped_products($shipment_data['products'])) {

                $shipment_data['timestamp'] = time();
                $shipment_id = (int) db_query("INSERT INTO ?:shipments ?e", $shipment_data);

                foreach ($shipment_data['products'] as $key => $amount) {

                    if ($amount == 0) {
                        continue;
                    }

                    $_data = array(
                        'item_id' => $key,
                        'shipment_id' => $shipment_id,
                        'order_id' => $shipment_data['order_id'],
                        'product_id' => $order_info['products'][$key]['product_id'],
                        'amount' => $amount,
                    );

                    db_query("INSERT INTO ?:shipment_items ?e", $_data);
                }

                if (!empty($shipment_data['order_status'])) {
                    fn_change_order_status($shipment_data['order_id'], $shipment_data['order_status']);
                }

                if (!empty($force_notification['C'])) {
                    $shipment = array(
                        'shipment_id' => $shipment_id,
                        'timestamp' => $shipment_data['timestamp'],
                        'shipping' => db_get_memoized_field('SELECT shipping FROM ?:shipping_descriptions WHERE shipping_id = ?i AND lang_code = ?s', $shipment_data['shipping_id'], $order_info['lang_code']),
                        'tracking_number' => $shipment_data['tracking_number'],
                        'chronopost_skybill_number' => $shipment_data['chronopost_skybill_number'] ?? '',
                        'carrier' => $shipment_data['carrier'],
                        'comments' => $shipment_data['comments'],
                        'items' => $shipment_data['products'],
                    );

                    $orderService = container()->get('marketplace.order.order_service');
                    $eventDispatcher = container()->get('event_dispatcher');

                    $event = new ShipmentCreated($orderService->getShipment($shipment_id));
                    $eventDispatcher->dispatch($event, ShipmentCreated::class);
                }

                fn_set_notification('N', __('notice'), __('shipment_has_been_created'));
            }

        } else {
            fn_set_notification('E', __('error'), __('products_for_shipment_not_selected'));
        }

    }

    return $shipment_id;
}

function fn_delete_shipments($shipment_ids)
{
    if (!empty($shipment_ids)) {
        db_query('DELETE FROM ?:shipments WHERE shipment_id IN (?a)', $shipment_ids);
        db_query('DELETE FROM ?:shipment_items WHERE shipment_id IN (?a)', $shipment_ids);
    }

    return true;
}

/**
 * Deletes shipping method by identifier
 *
 * @param int $shipping_id Shipping identifier
 * @return bool Always true
 */
function fn_delete_shipping($shipping_id)
{
    db_query("DELETE FROM ?:shipping_rates WHERE shipping_id = ?i", $shipping_id);
    db_query("DELETE FROM ?:shipping_descriptions WHERE shipping_id = ?i", $shipping_id);
    db_query("DELETE FROM ?:shippings WHERE shipping_id = ?i", $shipping_id);
    db_query("UPDATE ?:companies SET shippings = ?p", fn_remove_from_set('shippings', $shipping_id));

    return true;
}

function fn_apply_stored_shipping_rates(&$cart, $order_id = 0)
{
    if (!empty($cart['stored_shipping'])) {
        $total_cost = 0;
        foreach ($cart['product_groups'] as $group_key => $group) {
            foreach ($group['chosen_shippings'] as $shipping_key => $shipping) {
                if (isset($cart['stored_shipping'][$group_key][$shipping_key])) {
                    if (!empty($cart['free_shipping']) && in_array($shipping['shipping_id'], $cart['free_shipping'])) {
                        if (!empty($cart['stored_shipping'][$group_key][$shipping_key])) {
                            // save original value
                            $cart['original_stored_shipping'][$group_key][$shipping_key] = $cart['stored_shipping'][$group_key][$shipping_key];
                            // apply free shipping
                            $cart['stored_shipping'][$group_key][$shipping_key] = 0;
                        } else {
                            // save calulated rates as orignal: shipping is zero due to free shipping
                            $cart['original_stored_shipping'][$group_key][$shipping_key] = $shipping['rate'];
                        }
                    } elseif (empty($cart['stored_shipping'][$group_key][$shipping_key]) && isset($cart['original_stored_shipping'][$group_key][$shipping_key])) {
                        // free shiping was disabled - restore previous price
                        $cart['stored_shipping'][$group_key][$shipping_key] = !empty($cart['original_stored_shipping'][$group_key][$shipping_key]) ? $cart['original_stored_shipping'][$group_key][$shipping_key] : $shipping['rate'];
                        unset($cart['original_stored_shipping'][$group_key][$shipping_key]);
                    }

                    $piece = fn_format_price($cart['stored_shipping'][$group_key][$shipping_key]);
                    $cart['product_groups'][$group_key]['chosen_shippings'][$shipping_key]['rate'] = $piece;
                    $cart['product_groups'][$group_key]['shippings'][$shipping['shipping_id']]['rate'] = $piece;
                    $cart['product_groups'][$group_key]['chosen_shippings'][$shipping_key]['stored_shipping'] = true;
                    $cart['product_groups'][$group_key]['shippings'][$shipping['shipping_id']]['stored_shipping'] = true;
                    $shipping['rate'] = $piece;
                    $total_cost += $piece;
                } else {
                    if (!empty($shipping['rate'])) {
                        $total_cost += $shipping['rate'];
                    }
                }
            }
        }
        if (!empty($order_id)) {
            db_query("UPDATE ?:orders SET shipping_cost = ?i WHERE order_id = ?i", $total_cost, $order_id);
        }
        $cart['shipping_cost'] = $total_cost;
    }
}

function fn_checkout_update_shipping(&$cart, $shipping_ids)
{

    $cart['chosen_shipping'] = $shipping_ids;

    return true;
}

/**
 * Applies surcharge of selected payment to cart total
 *
 * @param array $cart Array of the cart contents and user information necessary for purchase
 * @param array $auth Array of user authentication data (e.g. uid, etc.)
 * @param string $lang_code 2-letter language code (e.g. 'en', 'ru', etc.)
 * @return bool Always true
 */
function fn_update_payment_surcharge(&$cart, $auth, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $cart['payment_surcharge'] = 0;
    if (!empty($cart['payment_id'])) {
        $_data = db_get_row("SELECT a_surcharge, p_surcharge FROM ?:payments WHERE payment_id = ?i", $cart['payment_id']);

        if (!empty($_data)) {
            if (floatval($_data['a_surcharge'])) {
                $cart['payment_surcharge'] += $_data['a_surcharge'];
            }
            if (floatval($_data['p_surcharge'])) {
                $cart['payment_surcharge'] += fn_format_price($cart['total'] * $_data['p_surcharge'] / 100);
            }
        }
    }

    if (!empty($cart['payment_surcharge'])) {
        $cart['payment_surcharge_title'] = db_get_field("SELECT surcharge_title FROM ?:payment_descriptions WHERE payment_id = ?i AND lang_code = ?s", $cart['payment_id'], $lang_code);

        // apply tax
        fn_calculate_payment_taxes($cart, $auth);
    }

    return true;
}

function fn_get_cart_product_icon($product_id, $product_data = array())
{
    if (!empty($product_data['product_options'])) {
        $combination_hash = fn_generate_cart_id($product_id, array('product_options' => $product_data['product_options']), true);
        $image = fn_get_image_pairs($combination_hash, 'product_option', 'M', true, true);
        if (!empty($image)) {
            return $image;
        }
    }

    return fn_get_image_pairs($product_id, 'product', 'M', true, true);
}

function fn_prepare_checkout_payment_methods(&$cart, &$auth, $lang_code = null, $hasAdjustableProducts = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    static $payment_methods, $payment_groups;

    //Get payment methods
    if (empty($payment_methods)) {
        $payment_methods = fn_get_payment_methods();
    }

    // Check if payment method has surcharge rates
    foreach ($payment_methods as $k => $v) {
        $payment_methods[$k]['surcharge_value'] = 0;
        if (floatval($v['a_surcharge'])) {
            $payment_methods[$k]['surcharge_value'] += $v['a_surcharge'];
        }
        if (floatval($v['p_surcharge']) && !empty($cart['total'])) {
            $payment_methods[$k]['surcharge_value'] += fn_format_price($cart['total'] * $v['p_surcharge'] / 100);
        }

        $payment_methods[$k]['image'] = fn_get_image_pairs($v['payment_id'], 'payment', 'M', true, true, $lang_code);

        $payment_groups[$v['payment_category']][$k] = $payment_methods[$k];
    }

    $featureOrderAdjustment = (bool) container()->getParameter('feature.order_adjustment');
    $paymentService = container()->get('marketplace.payment.payment_service');

    foreach ($payment_groups as &$group) {
        $group = array_filter($group, function (array $payment) use ($paymentService, $featureOrderAdjustment, $hasAdjustableProducts): bool {
            if ($featureOrderAdjustment && $hasAdjustableProducts) {
                return false === PaymentType::CREDIT_CARD()->equals($paymentService->getPaymentType((int) $payment['payment_id']));
            } else {
                return false === PaymentType::CREDIT_CARD_CAPTURE()->equals($paymentService->getPaymentType((int) $payment['payment_id']));
            }
        });
    }

    return $payment_groups;
}

function fn_print_order_invoices($order_ids, $pdf = false, $area = AREA, $lang_code = null) : Response
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $container = container();

    if (!is_array($order_ids)) {
        $order_ids = array($order_ids);
    }

    $html = null;

    if ($customInvoiceTemplateUrl = container()->getParameter('marketplace.invoice.template_url')) {
        $customTwig = @file_get_contents($customInvoiceTemplateUrl);

        if (empty($customTwig)) { // 404 or empty
            $container->get('logger')->error('Cannot get custom invoice body at '.$customInvoiceTemplateUrl);
        } else {
            try {
                $html = fn_print_order_invoices_twig($customTwig, $order_ids, $lang_code);
            } catch (\Twig_Error $e) {
                $container->get('logger')->error('Cannot generate custom invoice, falling back to the default template', [
                    'exception' => $e,
                ]);
            }
        }
    }

    if ($html === null) {
        $html = fn_print_order_invoices_cscart($order_ids, $area, $lang_code, $pdf);
    }

    if ($pdf) {
        /*
         * Les options du pdf correspondent aux options disponibles dans la bibliothèque wkhtmltopdf (https://wkhtmltopdf.org/usage/wkhtmltopdf.txt)
         */
        $pdfOptions = [
            'encoding' => 'UTF-8',
            'footer-center' => 'Page [page] sur [toPage]',
            'footer-font-size' => 8
        ];
        $pdfOptions = [PdfOptions::ENCODING()->getValue() => PdfOptions::ENCODING_UTF8()->getValue()];

        /*
         * Les templates des headers et footers sont des url vers des fichiers Html
         */
        if ($header = $container->getParameter('marketplace.invoice.header.template_url')) {
            $headerHtml = @file_get_contents($header);

            if ($headerHtml === false) {
                $container->get('logger')->error('Cannot get custom invoice header at '.$header);
            } else {
                $pdfOptions[PdfOptions::HEADER_HTML()->getValue()] = $headerHtml;
            }
        }

        if ($footer = $container->getParameter('marketplace.invoice.footer.template_url')) {
            $footerHtml = @file_get_contents($footer);

            if ($footerHtml === false) {
                $container->get('logger')->error('Cannot get custom invoice footer at '.$footer);
            } else {
                $pdfOptions[PdfOptions::FOOTER_HTML()->getValue()] = $footerHtml;
            }
        }

        $marginTop = $container->getParameter('marketplace.invoice.options.margin.top');
        $marginBottom = $container->getParameter('marketplace.invoice.options.margin.bottom');

        if ($marginTop !== '') {
            $pdfOptions[PdfOptions::MARGIN_TOP()->getValue()] = $marginTop;
        }

        if ($marginBottom !== '') {
            $pdfOptions[PdfOptions::MARGIN_BOTTOM()->getValue()] = $marginBottom;
        }

        return new Response(
            $container->get('knp_snappy.pdf')->getOutputFromHtml($html, $pdfOptions),
            200,
            ResponseContentType::getHeader( __('orders') . '-' . implode('-', $order_ids))
        );
    }

    return new Response($html);
}

/**
 * @throws \Twig_Error
 */
function fn_print_order_invoices_twig(string $templateContent, array $order_ids, $lang_code = null): string
{
    $html = [];

    $container = container();
    $priceFormatter = $container->get('marketplace.price.formatter');
    $orderService = $container->get('marketplace.order.order_service');

    $safeTwigSandbox= new \Twig_Environment(new \Twig_Loader_Array());
    $template = $safeTwigSandbox->createTemplate($templateContent);

    foreach ($order_ids as $orderId) {
        $order = $orderService->getOrder((int) $orderId);
        $orderInfo = $orderService->overrideLegacyOrder($orderId, false, true, false, true);

        $paymentMethod = fn_get_payment_data(
            (true === \array_key_exists('payment_id', $orderInfo['payment_method'])
                ? $orderInfo['payment_method']['payment_id']
                : 0),
            $orderInfo['order_id'],
            $lang_code
        );

        $html[] = $template->render([
            'order' => $order->exposeForCustomInvoice($priceFormatter),
            'paymentMethod' => $paymentMethod["payment"],
        ]);
    }

    return implode("<div style='page-break-before: always;'>&nbsp;</div>", $html);
}

function fn_print_order_credit_note(int $orderId, int $refundId, bool $renderPdf): Response
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $container = container();

    $orderService = $container->get('marketplace.order.order_service');
    $refundService = $container->get('marketplace.order.refund.refund_service');
    $creditNoteHelper = $container->get('marketplace.order.credit_note.credit_note_helper');
    $creditNoteService = $container->get('marketplace.order.credit_note.credit_note_service');

    try {
        $refund = $refundService->get($refundId);
    } catch (RefundNotFoundException $e) {
        throw new NotFoundHttpException($e->getMessage(), $e);
    }

    $order = $orderService->getOrder($orderId);
    $creditNote = $creditNoteService->buildCreditNote($refund, $order);
    $template = $creditNoteHelper->retrieveRemoteTemplate($container->getParameter('marketplace.rma.template_url'));
    $content = null;

    if (is_string($template) && strlen($template) > 0) {
        $twigSandbox = new Environment(new ArrayLoader());
        $twigTemplate = $twigSandbox->createTemplate($template);
        $content = $twigTemplate->render(['creditNote' => $creditNote]);
    }

    if ($content === null || strlen($content) === 0) {
        $twig = $container->get('templating');
        $content = $twig->render('@App/common/order/credit-note.html.twig', [
            'creditNote' => $creditNote,
            'currency' => $container->getParameter('currency.sign'),
        ]);
    }

    if ($renderPdf === true) {
        $header = $creditNoteHelper->retrieveRemoteTemplate(
            $container->getParameter('marketplace.rma.header.template_url')
        );
        $footer = $creditNoteHelper->retrieveRemoteTemplate(
            $container->getParameter('marketplace.rma.footer.template_url')
        );
        $pdfOptions = [
            PdfOptions::ENCODING()->getValue() => PdfOptions::ENCODING_UTF8()->getValue(),
            PdfOptions::HEADER_HTML()->getValue() => $header,
            PdfOptions::FOOTER_HTML()->getValue() => $footer,
            PdfOptions::MARGIN_TOP()->getValue() => $container->getParameter('marketplace.rma.options.margin.top'),
            PdfOptions::MARGIN_BOTTOM()->getValue() => $container->getParameter(
                'marketplace.rma.options.margin.bottom'
            ),
        ];

        return new Response(
            $container->get('pdf_generator')->convertHtmlToPdf($content, $pdfOptions),
            Response::HTTP_OK,
            ResponseContentType::getHeader($creditNoteHelper->buildCreditNoteFilename($order->getId(), $refund->getId()))
        );
    }

    return new Response($content);
}

/**
 * @throws \Twig_Error
 */
function fn_print_rma($rma_id, $order_id, $pdf = false, $area = AREA, $lang_code = null) : Response
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $container = container();

    $html = null;

    if ($customRmaTemplateUrl = container()->getParameter('marketplace.rma.template_url')) {
        $customTwig = @file_get_contents($customRmaTemplateUrl);

        if (empty($customTwig)) { // 404 or empty
            $container->get('logger')->error('Cannot get custom rma body at '.$customRmaTemplateUrl);
        } else {
            try {
                $html = fn_print_rma_twig($customTwig, $rma_id, $order_id, $lang_code);
            } catch (\Twig_Error $e) {
                $container->get('logger')->error('Cannot generate custom rma, falling back to the default template', [
                    'exception' => $e,
                ]);
            }
        }
    }

    if ($pdf) {
        /*
         * Les options du pdf correspondent aux options disponibles dans la bibliothèque wkhtmltopdf (https://wkhtmltopdf.org/usage/wkhtmltopdf.txt)
         */
        $pdfOptions = [PdfOptions::ENCODING()->getValue() => PdfOptions::ENCODING_UTF8()->getValue()];

        /*
         * Les templates des headers et footers sont des url vers des fichiers Html
         */
        if ($header = $container->getParameter('marketplace.rma.header.template_url')) {
            $headerHtml = @file_get_contents($header);

            if ($headerHtml === false) {
                $container->get('logger')->error('Cannot get custom rma header at '.$header);
            } else {
                $pdfOptions[PdfOptions::HEADER_HTML()->getValue()] = $headerHtml;
            }
        }

        if ($footer = $container->getParameter('marketplace.rma.footer.template_url')) {
            $footerHtml = @file_get_contents($footer);

            if ($footerHtml === false) {
                $container->get('logger')->error('Cannot get custom rma footer at '.$footer);
            } else {
                $pdfOptions[PdfOptions::FOOTER_HTML()->getValue()] = $footerHtml;
            }
        }

        $marginTop = $container->getParameter('marketplace.rma.options.margin.top');
        $marginBottom = $container->getParameter('marketplace.rma.options.margin.bottom');

        if ($marginTop !== '') {
            $pdfOptions[PdfOptions::MARGIN_TOP()->getValue()] = $marginTop;
        }

        if ($marginBottom !== '') {
            $pdfOptions[PdfOptions::MARGIN_BOTTOM()->getValue()] = $marginBottom;
        }

        return new Response(
            $container->get('knp_snappy.pdf')->getOutputFromHtml($html, $pdfOptions),
            200,
            ResponseContentType::getHeader(__('returns') . '-' . $rma_id)
        );
    }

    return new Response($html);
}

/**
 * @throws \Twig_Error
 */
function fn_print_rma_twig(string $templateContent, int $rma_id, int $order_id, $lang_code = null): string
{
    $container = container();

    $priceFormatter = $container->get('marketplace.price.formatter');
    $orderService = $container->get('marketplace.order.order_service');
    $rawOrder = $orderService->getOrder($order_id);
    $order = $rawOrder->exposeForCustomInvoice($priceFormatter);

    $returnService = $container->get('marketplace.order_return.service');
    $return = $returnService->getReturn($rma_id);
    $rma = $return->exposeForCustomRma($rawOrder, $priceFormatter);

    $safeTwigSandbox= new \Twig_Environment(new \Twig_Loader_Array());
    $template = $safeTwigSandbox->createTemplate($templateContent);

    return $template->render([
        'order' => $order,
        'rma' => $rma,
    ]);
}

function fn_print_order_invoices_cscart(array $order_ids, $area = AREA, $lang_code = null, $pdf = false): string
{
    $html = [];

    $view = Registry::get('view');
    $view->assign('order_status_descr', fn_get_simple_statuses(STATUSES_ORDER, true, true));
    $view->assign('profile_fields', fn_get_profile_fields('I'));
    $view->assign('pdf', $pdf);

    /** @var OrderService */
    $orderService = container()->get('marketplace.order.order_service');

    foreach ($order_ids as $order_id) {
        $order_info = $orderService->overrideLegacyOrder($order_id, false, true, false, true);

        if (empty($order_info)) {
            continue;
        }

        $order_info['marketplace_discount_coupon'] = current(array_filter($order_info['promotions'], function (array $promotion) use($order_info): bool {
            return $promotion['id'] === $order_info['marketplace_discount_id'];
        }))['name'];

        $view->assign('take_surcharge_from_vendor', fn_take_payment_surcharge_from_vendor());

        list($shipments) = fn_get_shipments_info(array('order_id' => $order_info['order_id'], 'advanced_info' => true));
        $use_shipments = !fn_one_full_shipped($shipments);

        $status_settings = fn_get_status_params($order_info['status']);
        $order = $orderService->getOrder($order_id);
        $order_status = fn_get_status_data(
            $order_info['status'],
            STATUSES_ORDER,
            $order_info['order_id'],
            $lang_code,
            $order_info['company_id']
        );

        if ($order->isCustomerProfessional() === true) {
            $professionalUserData = [
                'company' => $order->getCustomerCompany() ?? '',
                'jobTitle' => $order->getCustomerJobTitle() ?? '',
                'legalIdentifier' => $order->getCustomerLegalIdentifier() ?? '',
                'intraEuropeanCommunityVAT' => $order->getCustomerIntraEuropeanCommunityVat() ?? '',
            ];
        }

        $view->assign('show_as_invoice', $order->shouldShowAsInvoice());
        $view->assign('order_info', $order_info);
        $view->assign('shipments', $shipments);
        $view->assign('use_shipments', $use_shipments);
        $view->assign('payment_method', fn_get_payment_data((!empty($order_info['payment_method']['payment_id']) ? $order_info['payment_method']['payment_id'] : 0), $order_info['order_id'], $lang_code));
        $view->assign('order_status', $order_status);
        $view->assign('status_settings', $status_settings);
        $view->assign('invoice_data', $order_info['invoice_data']);
        $view->assign('company_data', fn_get_company_placement_info($order_info['company_id']));
        $view->assign('is_professional', $order->isCustomerProfessional() );
        $view->assign('professional_user_data', $professionalUserData ?? []);
        $view->assign('display_order_summary', true);

        $html[] = $view->displayMail('orders/print_invoice.tpl', false, $area, $order_info['company_id'], $lang_code);
    }

    return implode("<div style='page-break-before: always;'>&nbsp;</div>", $html);
}

/**
 * Returns all available shippings for root/vendor company
 *
 * @param int $company_id Company identifier
 * @return array List of shippings
 */
function fn_get_available_shippings($company_id = null)
{
    $condition = '';
    if ($company_id != null) {
        $company_shippings = db_get_memoized_field('SELECT shippings FROM ?:companies WHERE company_id = ?i', $company_id);
        $condition .= db_quote('AND (a.company_id = ?i ', $company_id);

        if (!empty($company_shippings)) {
            $condition .= db_quote(' OR a.shipping_id IN (?n)', explode(',', $company_shippings));
        }

        $condition .= ')';
    }

    $res = db_get_memoized_hash_array("SELECT a.shipping_id, a.company_id, a.min_weight, a.max_weight, a.position, a.status, b.shipping, b.delivery_time, c.company as company_name FROM ?:shippings as a LEFT JOIN ?:shipping_descriptions as b ON a.shipping_id = b.shipping_id AND b.lang_code = ?s LEFT JOIN ?:companies c ON c.company_id = a.company_id WHERE 1 $condition ORDER BY a.position", 'shipping_id', (string) GlobalState::contentLocale());

    // For vendor, change $res for all shippings allowed.
    // For administrator, don't change $res.
    if (!is_null($company_id)) {
        $res = fn_w_get_shipping_info_for_company($company_id);
    }
    return $res;
}

/**
 * Update cart products from passed products data
 *
 * @param array $cart Array of cart content and user information necessary for purchase
 * @param array $product_data Array of new products data
 * @param array $auth Array of user authentication data (e.g. uid, etc.)
 * @return boolean Always true
 */
function fn_update_cart_products(&$cart, $product_data)
{
    if (is_array($cart['products']) && !empty($product_data)) {

        list($product_data, $cart) = fn_add_product_options_files($product_data, $cart, true);
        unset($product_data['custom_files']);

        foreach ($product_data as $k => $v) {
            if (!isset($cart['products'][$k]['extra']['exclude_from_calculate'])) {
                if (empty($v['extra'])) {
                    $v['extra'] = array();
                }

                if ($v['price'] < 0) {
                    $v['price'] = 0;
                }

                unset($v['object_id']);

                $amount = fn_normalize_amount($v['amount']);
                $price = fn_get_product_price($v['product_id'], $amount);

                $v['extra'] = empty($cart['products'][$k]['extra']) ? array() : $cart['products'][$k]['extra'];
                $v['extra']['product_options'] = empty($v['product_options']) ? array() : $v['product_options'];
                $_id = fn_generate_cart_id($v['product_id'], $v['extra']);

                if (!isset($cart['products'][$_id])) { //if combination doesn't exist in the cart
                    $cart['products'][$_id] = $v;
                    $cart['products'][$_id]['company_id'] = !empty($cart['products'][$k]['company_id']) ? $cart['products'][$k]['company_id'] : 0;
                    $_product = $cart['products'][$k];

                    fn_define_original_amount($v['product_id'], $_id, $cart['products'][$_id], $_product);

                    unset($cart['products'][$k]);

                } elseif ($k != $_id) { // if the combination is exist but differs from the current
                    $amount += $cart['products'][$_id]['amount'];
                    unset($cart['products'][$k]);
                }

                if (empty($amount)) {
                    fn_delete_cart_product($cart, $_id);
                    continue;
                } else {
                    $_product_options = !empty($v['product_options']) ? $v['product_options'] : array();
                    $cart['products'][$_id]['amount'] = fn_check_amount_in_stock($v['product_id'], $amount, $_product_options, $_id, (!empty($cart['products'][$_id]['is_edp']) && $cart['products'][$_id]['is_edp'] == 'Y' ? 'Y' : 'N'), !empty($cart['products'][$_id]['original_amount']) ? $cart['products'][$_id]['original_amount'] : 0, $cart);

                    if ($cart['products'][$_id]['amount'] == false && !empty($_product)) {
                        $cart['products'][$_id] = $_product;
                        unset($_product);
                    }
                }

                if ($k != $_id) {
                    $cart['products'][$_id]['prev_cart_id'] = $k;

                    // save stored taxes for products
                    fn_update_stored_cart_taxes($cart, $k, $_id, true);

                } elseif (isset($cart['products'][$_id]['prev_cart_id'])) {
                    unset($cart['products'][$_id]['prev_cart_id']);
                }

                $cart['products'][$_id]['stored_price'] = !empty($v['stored_price']) ? $v['stored_price'] : 'N';
                if ($cart['products'][$_id]['stored_price'] == 'Y') {
                    $cart['products'][$_id]['price'] = $v['price'];
                }

                $cart['products'][$_id]['stored_discount'] = !empty($v['stored_discount']) ? $v['stored_discount'] : 'N';
                if ($cart['products'][$_id]['stored_discount'] == 'Y') {
                    $cart['products'][$_id]['discount'] = $v['discount'];
                }
            }
        }
    }

    return true;
}

/**
 * Update cart products and etc. from passed params
 *
 * @param array $cart Array of cart content and user information necessary for purchase
 * @param array $new_cart_data Array of new data for products, totals, discounts and etc. update
 * @param array $auth Array of user authentication data (e.g. uid, etc.)
 * @return boolean Always true
 */
function fn_update_cart_by_data(&$cart, $new_cart_data)
{
    // Clean up saved shipping rates
    unset($_SESSION['shipping_rates']);

    // update products
    $product_data = !empty($new_cart_data['cart_products']) ? $new_cart_data['cart_products'] : array();
    fn_update_cart_products($cart, $product_data);

    // Update shipping cost
    $cart['stored_shipping'] = array();
    if (!empty($cart['product_groups'])) {
        foreach ($cart['product_groups'] as $group_key => $group) {
            if (!empty($group['chosen_shippings'])) {
                foreach ($group['chosen_shippings'] as $shipping_key => $shipping) {
                    if (!empty($new_cart_data['stored_shipping'][$group_key][$shipping_key]) && $new_cart_data['stored_shipping'][$group_key][$shipping_key] != 'N') {
                        $cart['stored_shipping'][$group_key][$shipping_key] = (float) $new_cart_data['stored_shipping_cost'][$group_key][$shipping_key];
                        $cart['product_groups'][$group_key]['chosen_shippings'][$shipping_key]['rate'] = $cart['stored_shipping'][$group_key][$shipping_key];
                    } else {
                        unset($cart['product_groups'][$group_key]['chosen_shippings'][$shipping_key]['stored_shippings']);
                        unset($cart['product_groups'][$group_key]['shippings'][$shipping['shipping_id']]['stored_shippings']);
                    }
                }
            }
        }
    }

    // Update taxes
    if (!empty($new_cart_data['taxes']) && @$new_cart_data['stored_taxes'] == 'Y') {
        foreach ($new_cart_data['taxes'] as $id => $rate) {
            $cart['taxes'][$id]['rate_value'] = $rate;
        }
    }

    $cart['stored_taxes'] = !empty($new_cart_data['stored_taxes']) ? $new_cart_data['stored_taxes'] : array();

    if (!empty($new_cart_data['stored_subtotal_discount']) && $new_cart_data['stored_subtotal_discount'] == 'Y') {
        $cart['stored_subtotal_discount'] = 'Y';
        $cart['subtotal_discount'] = $new_cart_data['subtotal_discount'] ?? 0;
    } else {
        unset($cart['stored_subtotal_discount']);
        $cart['subtotal_discount'] = !empty($cart['original_subtotal_discount']) ? $cart['original_subtotal_discount'] : 0;
    }

    // Apply coupon
    if (!empty($new_cart_data['coupon_code'])) {
        fn_trusted_vars('coupon_code');
        $cart['pending_coupon'] = $new_cart_data['coupon_code'];
    }

    return true;
}

/**
 * Enables checkout mode
 */
function fn_enable_checkout_mode()
{
    Registry::set('runtime.checkout', true);
}

function fn_checkout_summary(&$cart)
{
    if (fn_cart_is_empty($cart) == true) {
        return false;
    }

    //Get payment methods
    $payment_data = fn_get_payment_method_data($cart['payment_id']);

    Registry::get('view')->assign('payment_method', $payment_data);

    // Downlodable files agreements
    $agreements = array();
    if (!empty($cart['products'])) {
        foreach ($cart['products'] as $item) {
            if ($item['is_edp'] == 'Y') {
                if ($_agreement = fn_get_edp_agreements($item['product_id'], true)) {
                    $agreements[$item['product_id']] = $_agreement;
                }
            }
        }
    }

    if (!empty($agreements)) {
        Registry::get('view')->assign('cart_agreements', $agreements);
    }
}

function fn_need_shipping_recalculation(&$cart)
{
    if ($cart['recalculate'] == true) {
        return true;
    }

    $recalculate_shipping = false;
    if (!empty($_SESSION['customer_loc'])) {
        foreach ($_SESSION['customer_loc'] as $k => $v) {
            if (!empty($v) && empty($cart['user_data'][$k])) {
                $recalculate_shipping = true;
                break;
            }
        }
    }

    if ($recalculate_shipping == false && !empty($_SESSION['checkout_mode']) && ($_SESSION['checkout_mode'] == 'cart' && Registry::get('runtime.mode') == 'checkout')) {
        $recalculate_shipping = true;
    }

    unset($_SESSION['customer_loc']);

    return $recalculate_shipping;

}

function fn_get_default_credit_card(&$cart, $user_data)
{
    if (!empty($user_data['credit_cards'])) {
        $cards = unserialize(fn_decrypt_text($user_data['credit_cards']));
        foreach ((array) $cards as $cc) {
            if ($cc['default']) {
                $cart['payment_info'] = $cc;
                break;
            }
        }
    } elseif (isset($cart['payment_info'])) {
        unset($cart['payment_info']);
    }
}

function fn_get_shipping_hash($product_groups)
{
    // If shipping methods changed and shipping step is completed, display notification
    $shipping_hash = '';

    if (!empty($product_groups)) {
        $rates = array();
        foreach ($product_groups as $key_group => $group) {
            $rates[$key_group] = array();
            foreach ($group['shippings'] as $key_shipping => $shipping) {
                $rates[$key_group][$key_shipping] = $shipping['rate'];
            }
            ksort($rates[$key_group]);
        }
        ksort($rates);
        $shipping_hash = md5(fn_recursive_makehash($rates));
    }

    return $shipping_hash;
}
/**
 * @param char   $prior
 * @return array
 * Return all status than vendor can update from one specific status
 */
function fn_w_get_order_statuses_by_prior_status($prior, $type = 'orders')
{
    switch ($type) {
        case 'orders':
            $where_type = STATUSES_ORDER;
            break;
        case 'rma':
            $where_type = STATUSES_RETURN;
            break;
        default:
            return null;
    }

    $schema = fn_get_permissions_schema('change_status');
    if(!isset($schema[$type][$prior])) {
        return null;
    }

    $condition = db_quote("AND a.status IN (?a)", array_keys($schema[$type][$prior]));

    $statuses = db_get_memoized_hash_single_array(
        "SELECT a.status, b.description"
        . " FROM ?:statuses as a"
        . " LEFT JOIN ?:status_descriptions as b ON b.status = a.status AND b.type = a.type AND b.lang_code = ?s"
        . " WHERE a.type = '$where_type' $condition",
        array('status', 'description'),
        (string) GlobalState::contentLocale()
    );

    $schema[$type][$prior] = array_filter(
        $schema[$type][$prior]
    );
    array_walk(
        $schema[$type][$prior],
        function(&$v, $k) use ($statuses)
        {
            if ($v === true) {
                $v = $statuses[$k];
            }
        }
    );

    return $schema[$type][$prior];
}

/**
 * @param int $company_id
 * Return RMA address.
 */
function fn_w_get_rma_address($company_id)
{
    $company_data = fn_get_company_data($company_id);
    return [
        'company' => $company_data['company'],
        'company_description' => $company_data['company_description'],
        'address' => $company_data['w_rma_address'] ? $company_data['w_rma_address'] : $company_data['address'],
        'city' => $company_data['w_rma_city'] ? $company_data['w_rma_city'] : $company_data['city'],
        'zipcode' => $company_data['w_rma_zipcode'] ? $company_data['w_rma_zipcode'] : $company_data['zipcode'],
        'country' => $company_data['w_rma_country'] ? $company_data['w_rma_country'] : $company_data['country'],
    ];
}

/**
 * @param  int $order_id
 * @return bool
 * Return true if rma exist for this order
 */
function fn_w_exist_rma($order_id)
{
    $exist = db_get_field("SELECT 1 FROM ?:rma_returns WHERE order_id = ?i LIMIT 1", $order_id);
    return (bool) $exist;
}

function fn_w_all_closed_rma($order_id)
{
    $exist = db_get_field("SELECT 1 FROM ?:rma_returns WHERE order_id = ?i AND status != ?s LIMIT 1", $order_id, RmaStatus::COMPLETED);
    return ! (bool) $exist ;
}

/**
 * @param  $order_data
 * @param  $type A: all, P: products , S: shippings
 * @return float
 */
function fn_w_get_subtotal_tax_from_order($order_data, $type = 'A')
{
    if (!is_array($order_data['taxes'])) {
        return 0;
    }
    if ('A' == $type) {
        $taxes = array_column($order_data['taxes'], 'tax_subtotal');
    } else {
        $taxes = array_map(
            function ($tax) use ($type) {
                $keys = array_keys($tax['applies']);
                $sum = 0;
                foreach ($keys as $key) {
                    if (strpos($key, $type) === 0) {
                        $sum += $tax['applies'][$key];
                    }
                }
                return $sum;
            },
            $order_data['taxes']
        );
    }

    return array_sum($taxes);
}

/**
 * @param  $order_data
 * @return bool True if all products in order are sent.
 */
function fn_w_check_all_shipped_order_after_current_shippment($order_data)
{
    $shipped = db_get_field("SELECT SUM(amount) FROM ?:shipment_items WHERE order_id = ?i",$order_data['order_id']);
    return  $shipped == $order_data['product_groups'][0]['package_info']['I'];
}

/**
 * @param $rma
 * @return int
 *
 * Return the value of an rma, regardless of taxes included in prices or not
 * (so you may need to add it manually later to have real total)
 */
function fn_w_rma_refund_sum($rma)
{
    if (is_array(fn_get_return_info($rma['return_id'])['items']['A'])) {
        return array_reduce(
            fn_get_return_info($rma['return_id'])['items']['A'],
            function (&$result, $item) {
                return $result += $item['price'] * $item['amount'];
            },
            0
        );
    }

    return 0;
}

/**
 * @param $return_info
 * @param $order_info
 * @return array
 * Merge RMA and Order info for the email send to client when the rma is refund.
 */
function fn_w_rma_merge_product_rma_and_order(array $return_info, array $order_info)
{
    return array_reduce(
        array_reduce(
            $return_info['items'],
            function(&$product_list, $type)
            {
                $product_list += array_values($type);
                return $product_list;
            },
            []
        ),
        function(&$return, $product) use($order_info)
        {
            $return[$product['item_id']] = array_merge($order_info['products'][$product['item_id']], $product);
            $return[$product['item_id']]['tax_value'] = ($return[$product['item_id']]['tax_value']/$order_info['products'][$product['item_id']]['amount'])*($return[$product['item_id']]['amount']);
            $return[$product['item_id']]['display_subtotal'] = $return[$product['item_id']]['price']*$return[$product['item_id']]['amount'];
            return $return;
        }
    );
}

/**
 * @param $return_info
 * @param $order_info
 * @return mixed
 * Used when the final email is send to customer.
 */
function fn_w_rma_get_total_info($return_info, $order_info)
{
    $total['total'] = $total['display_subtotal'] = fn_w_rma_refund_sum($return_info);
    $total['products'] = fn_w_rma_merge_product_rma_and_order($return_info, $order_info);
    $total['taxes'] = $order_info['taxes'];
    $total['tax_subtotal'] = 0;

    array_walk(
        $total['taxes'],
        function(&$tax) use ($order_info, &$total)
        {
            array_walk(
                $tax['applies'],
                function(&$value, $key) use ($order_info, &$total)
                {
                    if (strpos($key,'P_') === 0) {
                        $productId = substr($key, 2);
                        $value = ($value/$order_info['products'][$productId]['amount'])*($total['products'][$productId]['amount']?:0);
                    }
                }
            );

            $tax['tax_subtotal'] = array_sum($tax['applies']);
            $total['tax_subtotal'] += $tax['tax_subtotal'];
        }
    );

    return $total;
}

/**
 * @param $id
 * @param $status
 * @return null|string
 */
function fn_w_rma_check_rma_infos ($id, $status)
{
    $schema = fn_get_permissions_schema('change_status');
    $old_status = db_get_field("SELECT status FROM ?:rma_returns WHERE return_id = ?i", $id);
    if (!isset($schema['rma'][$old_status][$status])) {
        return __('w_change_status_order_not_allowed');
    }

    return null;
}

/**
 * @param $id
 * @param $status
 * @param $rma_number
 * @param $mail_rules
 */
function fn_w_rma_update_status($id, $status, $rma_number, $mail_rules)
{
    $extra = db_get_field("SELECT extra FROM ?:rma_returns WHERE return_id = ?i", $id);
    if ($extra) {
        $extra = unserialize($extra);
    }
    if ($rma_number) {
        $extra['w_rma_number'] = $rma_number;
    }

    $rmaNumber = $extra['w_rma_number'] ?? null;
    $extra = serialize($extra);

    $data = [
        'status' => $status,
        'extra' => $extra,
        'rma_number' => $rmaNumber,
    ];

    db_query('UPDATE ?:rma_returns SET ?u WHERE return_id = ?i', $data, $id);

    //Send mail
    $return_info = fn_get_return_info($id);
    $return_info['extra'] = unserialize($return_info['extra']);
    $order_info = container()
        ->get('marketplace.order.order_service')
        ->overrideLegacyOrder((int)$return_info['order_id']);

    fn_send_return_mail($return_info, $order_info, $mail_rules);
}


//
// Calculate gross total and totally paid values for the current set of orders
//
function fn_display_order_totals($orders)
{
    $result = array();
    $result['gross_total'] = 0;
    $result['totally_paid'] = 0;

    if (is_array($orders)) {
        foreach ($orders as $k => $v) {
            $result['gross_total'] += $v['total'];
            if (is_order_status_equal_to($v['order_id'], LegacyOrderStatus::COMPLETED, LegacyOrderStatus::PROCESSED)) {
                $result['totally_paid'] += $v['total'];
            }
        }
    }

    return $result;
}

function fn_google_send_order_command($post, $processor_data, $request_url, $action, $order_id)
{
    $_id = base64_encode($processor_data['processor_params']['merchant_id'] . ":" . $processor_data['processor_params']['merchant_key']);

    $return = Http::post($request_url, implode("\n", $post), array(
        'headers' => array(
            'Content-type: application/xml',
            "Authorization: Basic $_id",
            'Accept: application/xml'
        )
    ));

    preg_match("/<error-message>(.*)<\/error-message>/", $return, $error);

    if (!empty($error[1])) {
        fn_set_notification('E', __('notice'), $error[1]);
    } else {
        if (in_array($action, array('refund', 'cancel', 'deliver'))) {
            $_SESSION['google_info'] = db_get_field(
                "SELECT data FROM ?:order_data WHERE order_id = ?i AND type = ?s",
                $order_id,
                OrderDataType::GOOGLE_CHECKOUT_INFO()->getValue()
            );
            echo "Request is successfully sent.<br />";
            echo "Waiting for a Google response. Please be patient.";

            return array(CONTROLLER_STATUS_OK, "orders.google.wait_response?order_id=$order_id");
        }
        fn_set_notification('N', __('notice'), __('google_request_sent', array(
            '[action]' => __($action)
        )));
    }

    return true;
}

function fn_print_order_packing_slips($order_ids, $pdf = false, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $view = Registry::get('view');
    $html = array();
    $view->assign('pdf', $pdf);

    if (!is_array($order_ids)) {
        $order_ids = array($order_ids);
    }

    $orderService = container()->get('marketplace.order.order_service');

    foreach ($order_ids as $order_id) {
        $order_info = $orderService->overrideLegacyOrder($order_id, false, true, false, true);

        if (empty($order_info)) {
            continue;
        }

        $view->assign('order_info', $order_info);

        if ($pdf == true) {
            $html[] = $view->displayMail('orders/print_packing_slip.tpl', false, 'A', $order_info['company_id'], $lang_code);
        } else {
            $view->displayMail('orders/print_packing_slip.tpl', true, 'A', $order_info['company_id'], $lang_code);
        }

        if ($order_id != end($order_ids)) {
            echo("<div style='page-break-before: always;'>&nbsp;</div>");
        }
    }

    if ($pdf === true) {
        /*
         * Les options du pdf correspondent aux options disponibles dans la bibliothèque wkhtmltopdf (https://wkhtmltopdf.org/usage/wkhtmltopdf.txt)
         */
        $pdfOptions = [
            'encoding' => 'UTF-8',
            'footer-center' => 'Page [page] sur [toPage]',
            'footer-font-size' => 8
        ];

        if (\is_array($html) === true) {
            $html = implode("<div style='page-break-before: always;'>&nbsp;</div>", $html);
        }
        header('Content-disposition: attachment; filename="' .  __('packing_slip') . '-' . implode('-', $order_ids) . '.pdf"');
        header('Content-type: application/pdf');
        exit(container()->get('knp_snappy.pdf')->getOutputFromHtml($html, $pdfOptions));
    }

    return true;
}

function fn_set_subscription_id_on_order(string $subscriptionId, int $orderId): void
{
    db_query("UPDATE ?:orders SET subscription_id = ?s WHERE order_id = ?i", $subscriptionId, $orderId);
}
