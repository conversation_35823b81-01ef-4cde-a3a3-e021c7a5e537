<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, Ilya M<PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tygh\Bootstrap;
use Tygh\Languages\Helper as LanguageHelper;
use Tygh\Languages\Languages;
use Tygh\Less;
use Tygh\Registry;
use Tygh\Session;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\User\UserType;

/**
 * Returns True if the object can be saved, otherwise False.
 *
 * @param array $object_data Object data
 * @param string $object_type Object name
 * @param bool $skip_edition_checking Skip edition checking condition
 * @return bool Returns True if the object can be saved, otherwise False.
 */
function fn_allow_save_object($object_data, $object_type, $skip_edition_checking = false)
{
    $allow = true;

    $registry = \Wizacha\Registry::defaultInstance();
    $selected_company_id = \Wizacha\Company::runtimeID(
        $registry->get([\Wizacha\Config::REG_AREA]),
        $_SESSION,
        $registry
    );

    if ($skip_edition_checking) {
        if ($selected_company_id) {
            $allow = false;
        }

    } else {
        if (
            isset($object_data['company_id']) && $selected_company_id
            && $selected_company_id != $object_data['company_id']
        ) {
            $allow = false;
        }
    }
    if ($object_type == "shippings") {
        $allow = true;
    }
    return $allow;
}

/**
 * Returns theme path in the required format
 *
 * Examples:
 * [themes] -> /var/www/design/themes/
 * [themes]/[theme] -> /var/www/design/themes/theme
 * [relative]/[theme] -> design/themes/theme
 * [repo]/[theme] -> /var/www/var/themes_repository/theme
 *
 * In format string:
 * [theme] will be replaced by actual theme name
 * [repo] will be replaced by real path to repository
 * [themes] will be replaced by real path of actual themes folder
 * [relative] will be replaced by path of actual themes folder relative root directory
 *
 * @param $path string Format string.
 * @param $area string Area (C/A) to get setting for
 * @param $company_id int Company identifier
 * @return string Path to theme
 */
function fn_get_theme_path($path = '[theme]/', $area = AREA, $company_id = null)
{
    static $theme_names = array();

    if ($area == 'A') {
        $theme_name = '';
        $dir_design = rtrim(Registry::get('config.dir.design_backend'), '/');

        if (strpos($path, '/[theme]/') !== false) { // FIXME THEMES: bad code
            $path = str_replace('/[theme]/', '/', $path);
        } elseif (strpos($path, '/[theme]') !== false) {
            $path = str_replace('/[theme]', '', $path);
        } elseif (strpos($path, '[theme]/') !== false) {
            $path = str_replace('[theme]/', '', $path);
        }
    } else {
        if (empty($theme_names['c_' . $company_id])) {
            $theme_names['c_' . $company_id] = \Wizacha\Registry::defaultInstance()->get(['config', 'theme_name']);
        }

        $theme_name = $theme_names['c_' . $company_id];
        $dir_design = rtrim(Registry::get('config.dir.design_frontend'), '/');
    }

    $path = str_replace('[theme]', $theme_name, $path);
    $dir_repo = rtrim(Registry::get('config.dir.themes_repository'), '/');

    $dir_common = rtrim(Registry::get('config.dir.design_common'), '/');

    $path = str_replace('[relative]', str_replace(Registry::get('config.dir.root') . '/', '', $dir_design), $path);
    $path = str_replace('[themes]', $dir_design, $path);
    $path = str_replace('[repo]', $dir_repo, $path);
    $path = str_replace('[common]', $dir_common, $path);

    return $path;
}

/**
 * Gets path for caching documents
 *
 * @param boolean $relative Flag that defines if path should be relative
 * @param string $area Area (C/A) to get setting for
 * @param integer $company_id Company identifier
 * @return string Path to files cache
 */
function fn_get_cache_path($relative = true, $area = AREA, $company_id = null)
{
    $path = Registry::get('config.dir.cache_misc');

    if ($relative) {
        $path = str_replace(Registry::get('config.dir.root') . '/', '', $path);
    }

    return $path;
}

/**
 * Prints any data like a print_r function
 * @param mixed ... Any data to be printed
 */
function fn_print_r()
{
    static $count = 0;
    $args = func_get_args();

    if (defined('CONSOLE')) {
        $prefix = "\n";
        $suffix = "\n\n";
    } else {
        $prefix = '<ol style="font-family: Courier; font-size: 12px; border: 1px solid #dedede; background-color: #efefef; float: left; padding-right: 20px;">';
        $suffix = '</ol><div style="clear:left;"></div>';
    }

    if (!empty($args)) {
        fn_echo($prefix);
        foreach ($args as $k => $v) {

            if (defined('CONSOLE')) {
                fn_echo(print_r($v, true));
            } else {
                fn_echo('<li><pre>' . htmlspecialchars(print_r($v, true)) . "\n" . '</pre></li>');
            }
        }
        fn_echo($suffix);

        if (defined('AJAX_REQUEST')) {
            $ajax_vars = Registry::get('ajax')->getAssignedVars();
            if (!empty($ajax_vars['debug_info'])) {
                $args = array_merge($ajax_vars['debug_info'], $args);
            }
            Registry::get('ajax')->assign('debug_info', $args);
        }
    }
    $count++;
}

/**
* Redirect browser to the new location
*
* @param string $location - destination of redirect
* @param bool $allow_external_redirect - allow redirection to external resource
*/
function fn_redirect($location, $allow_external_redirect = false) : Response
{
    $external_redirect = false;
    $protocol = defined('HTTPS') ? 'https' : 'http';
    $meta_redirect = false;

    // Cleanup location from &amp; signs and call fn_url()
    $location = fn_url(str_replace(array('&amp;', "\n", "\r"), array('&', '', ''), $location));

    // Convert absolute link with location to relative one
    if (strpos($location, '://') !== false || substr($location, 0, 7) == 'mailto:') {
        if (strpos($location, Registry::get('config.http_location')) !== false) {
            $location = str_replace(array(Registry::get('config.http_location') . '/', Registry::get('config.http_location')), '', $location);
            $protocol = 'http';

        } elseif (strpos($location, Registry::get('config.https_location')) !== false) {
            $location = str_replace(array(Registry::get('config.https_location') . '/', Registry::get('config.https_location')), '', $location);
            $protocol = 'https';

        } else {
            if ($allow_external_redirect == false) { // if external redirects aren't allowed, redirect to index script
                $location = '';
            } else {
                $external_redirect = true;
            }
        }

    // Convert absolute link without location to relative one
    } else {
        $_protocol = "";
        $_location = "";
        $http_path = Registry::get('config.http_path');
        $https_path = Registry::get('config.https_path');
        if (!empty($http_path) && substr($location, 0, strlen($http_path)) == $http_path) {
            $_location = substr($location, strlen($http_path) + 1);
            $_protocol = 'http';

        }
        if (!empty($https_path) && substr($location, 0, strlen($https_path)) == $https_path) {
            // if https path partially equal to http path check if https path is not just a part of http path
            // e. g. http://example.com/pathsimple & https://example.com/path
            if ($_protocol != 'http' || empty($http_path) || substr($http_path, 0, strlen($https_path)) != $https_path) {
                $_location = substr($location, strlen($https_path) + 1);
                $_protocol = 'https';
            }
        }
        $protocol = (Registry::get('config.http_path') != Registry::get('config.https_path') && !empty($_protocol)) ? $_protocol : $protocol;
        $location = !empty($_protocol) ? $_location : $location;
    }

    if ($external_redirect == false) {

        $protocol_changed = (defined('HTTPS') && $protocol == 'http') || (!defined('HTTPS') && $protocol == 'https');

        // For correct redirection, location must be absolute with path
        $location = (($protocol == 'http') ? Registry::get('config.http_location') : Registry::get('config.https_location')) . '/' . ltrim($location, '/');

        // Parse the query string
        $fragment = '';
        $query_array = array();
        $parced_location = parse_url($location);
        if (!empty($parced_location['query'])) {
            parse_str($parced_location['query'], $query_array);
            $location = str_replace('?' . $parced_location['query'], '', $location);
        }

        if (!empty($parced_location['fragment'])) {
            $fragment = '#' . $parced_location['fragment'];
            $location = str_replace($fragment, '', $location);
        }

        if ($protocol_changed && (Registry::get('config.http_host') != Registry::get('config.https_host') || Registry::get('config.http_path') != Registry::get('config.https_path'))) {
            $query_array[Session::getName()] = Session::getId();
        }

        // If this is not ajax request, remove ajax specific parameters
        if (!defined('AJAX_REQUEST')) {
            unset($query_array['is_ajax']);
            unset($query_array['result_ids']);
        } else {
            $query_array['result_ids'] = implode(',', Registry::get('ajax')->result_ids);
            $query_array['is_ajax'] = Registry::get('ajax')->redirect_type;
            $query_array['full_render'] = !empty($_REQUEST['full_render']) ? $_REQUEST['full_render'] : false;
            $query_array['callback'] = Registry::get('ajax')->callback;

            $ajax_assigned_vars = Registry::get('ajax')->getAssignedVars();
            if (!empty($ajax_assigned_vars['html'])) {
                unset($ajax_assigned_vars['html']);
            }
            $query_array['_ajax_data'] = $ajax_assigned_vars;
        }

        if (!empty($query_array)) {
            $location .= '?' . http_build_query($query_array) . $fragment;
        }

        // Redirect from https to http location
        if ($protocol_changed && defined('HTTPS')) {
            $meta_redirect = true;
        }
    }

    if (!defined('AJAX_REQUEST') && !empty($_SESSION['init_context'])) {
        if (strpos($location, Registry::get('config.http_location')) === 0) {
            $location = str_replace(Registry::get('config.http_location'), '', $location);
        } elseif (strpos($location, Registry::get('config.https_location')) === 0) {
            $location = str_replace(Registry::get('config.https_location'), '', $location);
        }

        $location = $_SESSION['init_context'] . '#!' . urlencode($location);
    }

    if (defined('AJAX_REQUEST')) { // make in-script redirect during ajax request
        $_purl = parse_url($location);

        $_GET = array();
        $_POST = array();

        if (!empty($_purl['query'])) {
            parse_str($_purl['query'], $_GET);
        }
        $_REQUEST = Bootstrap::safeInput($_GET);
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_URI'] = $_purl['path'];
        $_SERVER['QUERY_STRING'] = !empty($_purl['query']) ? $_purl['query'] : '';
        $_FILES = [];

        Registry::save(); // save registry cache to execute cleanup handlers
        fn_init_user();
        fn_init_settings();
        fn_init_addons();

        Registry::clearCacheLevels();

        Registry::get('ajax')->updateRequest();
        $requestStack = container()->get('request_stack');
        $currentRequest = $requestStack->getCurrentRequest();

        if (null !== $currentRequest) {
            $path = [
                '_forwarded' => $currentRequest->attributes,
                '_controller' => 'fn_dispatch',
            ];
            $subRequest = $currentRequest->duplicate($_GET, null, $path, null, null, $_SERVER);
        } else {
            $subRequest = Request::createFromGlobals();
        }

        // On refait tout le process de la request symfony pour repasser par
        // le router listener pour la détection de la route CsCart
        return container()->get('http_kernel')->handle($subRequest, HttpKernelInterface::SUB_REQUEST);
    }

    // On renvoi désormais une réponse de redirection, c'est au kernel de Symfony de la renvoyer proprement,
    // en passant par fn_dispatch
    return new RedirectResponse($location);
}

/**
 * Functions check if subarray with child exists in the given array
 *
 * @param array $data Array with nodes
 * @param string $childs_name Name of array with child nodes
 * @return boolean true if there are child subarray, false otherwise.
 */
function fn_check_second_level_child_array($data, $childs_name)
{
    foreach ($data as $l2) {
        if (!empty($l2[$childs_name]) && is_array($l2[$childs_name]) && count($l2[$childs_name])) {
            return true;
        }
    }

    return false;
}

/**
 * Sets notification message
 *
 * @param string $type notification type (E - error, W - warning, N - notice, O - order error on checkout, I - information)
 * @param string $title notification title
 * @param string $message notification message
 * @param string $message_state (S - notification will be displayed unless it's closed, K - only once, I - will be closed by timer)
 *                              This parameter is deprecated: all notifications are now displayed only once.
 * @param mixed $extra extra data to save with notification
 * @param bool $init_message $title and $message will be processed by __ function if true
 * @return boolean always true
 */
function fn_set_notification($type, $title, $message, $message_state = '', $extra = '', $init_message = false)
{
    if (empty($_SESSION['notifications'])) {
        $_SESSION['notifications'] = array();
    }

    $key = md5($type . $title . $message . $extra);

    $_SESSION['notifications'][$key] = array(
        'type' => $type,
        'title' => $title,
        'message' => $message,
        'message_state' => 'K',
        'new' => true,
        'extra' => $extra,
        'init_message' => $init_message,
    );

    return true;
}

/**
 * Deletes notification message
 *
 * @param string $extra condition for "extra" parameter
 * @return boolean always true
 */
function fn_delete_notification($extra)
{
    if (!empty($_SESSION['notifications'])) {
        foreach ($_SESSION['notifications'] as $k => $v) {
            if (!empty($v['extra']) && $v['extra'] == $extra) {
                unset($_SESSION['notifications'][$k]);
            }
        }
    }

    return true;
}

/**
 * Checks if notification message exists
 * <i>$check_type</i> - type of notification existance checking
 * <ul>
 *      <li>any - checks if at least one notification exist</li>
 *      <li>extra - checks if notification with "extra" field equals to $value exist </li>
 *      <li>type - checks if at least one notification of certain type exist</li>
 * </ul>
 * @param string $check_type check type
 * @param string $value value to compare with
 * @return boolean true if notification exists, false - if not
 */
function fn_notification_exists($check_type, $value = '')
{
    if (!empty($_SESSION['notifications'])) {
        if ($check_type == 'any') {
            return true;
        }

        foreach ($_SESSION['notifications'] as $k => $v) {
            if ($check_type == 'type' && $v['type'] == $value) {
                return true;
            }

            if ($check_type == 'extra' && !empty($v['extra']) && $v['extra'] == $value) {
                return true;
            }
        }
    }

    return false;
}

/**
 * Gets notifications list
 *
 * @param boolean $w_count_call
 * @return array notifications list
 */
function fn_get_notifications($w_update_flags = true)
{
    if (empty($_SESSION['notifications'])) {
        $_SESSION['notifications'] = array();
    }

    $_notifications = array();

    foreach ($_SESSION['notifications'] as $k => $v) {
        if (!empty($v['init_message'])) {
            $v['title'] = __($v['title']);
            $v['message'] = __($v['message']);
        }

        // Display notification if this is not ajax request, or ajax request and notifiactions was just set
        if (!defined('AJAX_REQUEST') || (defined('AJAX_REQUEST') && $v['new'] == true)) {
            $_notifications[$k] = $v;
        }

        if ($w_update_flags) {
            if ($v['message_state'] != 'S') {
                unset($_SESSION['notifications'][$k]);
            } else {
                $_SESSION['notifications'][$k]['new'] = false; // preparing notification for display, reset new flag
            }
        }
    }

    return $_notifications;
}

/**
 * Saves data, posted to script via POST request in session to use it later
 *
 * @param string 1 or more keys of $_POST array
 * @return boolean always true
 */
function fn_save_post_data()
{
    $_SESSION['saved_post_data'] = array();
    $args = func_get_args();

    foreach ($args as $key) {
        if (isset($_POST[$key])) {
            $_SESSION['saved_post_data'][$key] = $_POST[$key];
        }
    }

    return true;
}

/**
 * Restores data, saved by fn_save_post_data function
 *
 * @param string $key key to restore
 * @return mixed restored data of success or null on failure
 */
function fn_restore_post_data($key)
{
    if (isset($_SESSION['saved_post_data'][$key])) {
        $data = $_SESSION['saved_post_data'][$key];
        unset($_SESSION['saved_post_data'][$key]);

        return $data;
    }

    return null;

}

/**
 * @deprecated
 *
 * Loads received language variables into language cache
 *
 * @param array $var_names Language variable that to be loaded
 * @param string $lang_code 2-letter language code
 *
 * @return boolean True if any of received language variables were added into cache; false otherwise
 */
function fn_preload_lang_vars($var_names, $lang_code = null)
{
    return LanguageHelper::preloadLangVars($var_names, $lang_code ?? (string) GlobalState::interfaceLocale());
}

/**
 * Function defines and assigns pages
 *
 * @param array $params params to generate pagination from
 * @param string $area Area
 * @return array pagination structure
 */
function fn_generate_pagination($params, $area = AREA)
{
    if (empty($params['total_items']) || empty($params['items_per_page'])) {
        return array();
    }

    $deviation = ($area == 'A') ? 5 : 7;
    $max_pages = 10;
    $per_page = 10;

    $total_pages = ceil((int) $params['total_items'] / $params['items_per_page']);

    if ($params['page'] > $total_pages) {
        $params['page'] = $total_pages;
    }

    // Pagination in other areas displayed as in any search engine
    $page_from = fn_get_page_from($params['page'], $deviation);
    $page_to = fn_get_page_to($params['page'], $deviation, $total_pages);

    $pagination = array (
        'navi_pages' => range($page_from, $page_to),
        'prev_range' => ($page_from > 1) ? $page_from - 1 : 0,
        'next_range' => ($page_to < $total_pages) ? $page_to + 1: 0,
        'current_page' => $params['page'],
        'prev_page' => ($params['page'] > 1) ? $params['page'] - 1 : 0,
        'next_page' => ($params['page'] < $total_pages) ? $params['page'] + 1 : 0,
        'total_pages' => $total_pages,
        'total_items' => $params['total_items'],
        'items_per_page' => $params['items_per_page'],
        'per_page_range' => range($per_page, $per_page * $max_pages, $per_page)
    );

    if ($pagination['prev_range']) {
        $pagination['prev_range_from'] = fn_get_page_from($pagination['prev_range'], $deviation);
        $pagination['prev_range_to'] = fn_get_page_to($pagination['prev_range'], $deviation, $total_pages);
    }

    if ($pagination['next_range']) {
        $pagination['next_range_from'] = fn_get_page_from($pagination['next_range'], $deviation);
        $pagination['next_range_to'] = fn_get_page_to($pagination['next_range'], $deviation, $total_pages);
    }

    if (!in_array($params['items_per_page'], $pagination['per_page_range'])) {
        $pagination['per_page_range'][] = $params['items_per_page'];
        sort($pagination['per_page_range']);
    }

    return $pagination;
}

function fn_get_page_from($page, $deviation)
{
    return ($page - $deviation < 1) ? 1 : $page - $deviation;
}

function fn_get_page_to($page, $deviation, $total_pages)
{
    return ($page + $deviation > $total_pages) ? $total_pages : $page + $deviation;
}

//
// Advanced checking for variable emptyness
//
function fn_is_empty($var)
{
    return \Wizacha\Cscart\Common::fn_is_empty($var);
}

function fn_is_not_empty($var)
{
    return !fn_is_empty($var);
}

/**
 * Format price
 *
 * @param int $price
 * @param string $currency
 * @param int|null $decimals number of decimals
 * @param bool $return_as_float
 * @return float|string
 */
function fn_format_price($price = 0, $currency = CART_PRIMARY_CURRENCY, $decimals = null, $return_as_float = true)
{
    if ($decimals === null) {
        $currency_settings = Registry::get('currencies.' . $currency);
        $decimals = !empty($currency_settings) ? $currency_settings['decimals'] + 0 : 2; //set default value if not exist
    }
    $price = sprintf('%.' . $decimals . 'f', round((double) $price + 0.00000000001, $decimals));

    return $return_as_float ? (float) $price : $price;
}

/**
 * Add new node the breadcrumbs
 *
 * @param string $lang_value name of language variable
 * @param string $link breadcrumb URL
 * @param boolean $nofollow Include or not "nofollow" attribute
 * @return boolean True if breadcrumbs were added, false otherwise
 */
function fn_add_breadcrumb($lang_value, $link = '', $nofollow = false)
{
    //check permissions in the backend
    if (AREA == 'A' && !fn_check_view_permissions($link, 'GET')) {
        return false;
    }

    $bc = Registry::get('view')->getTemplateVars('breadcrumbs');

    // Add home link
    if (AREA == 'C' && empty($bc)) {
        $bc[] = array(
            'title' => __('home'),
            'link' => fn_url('')
        );
    }

    $bc[] = array(
        'title' => $lang_value,
        'link' => $link,
        'nofollow' => $nofollow,
    );

    Registry::get('view')->assign('breadcrumbs', $bc);

    return true;
}

/**
 * Merge several arrays preserving keys (recursively!) or not preserving
 *
 * @param array ... unlimited number of arrays to merge
 * @param bool ... if true, the array keys are preserved
 * @return array merged data
 */
function fn_array_merge()
{
    $arg_list = func_get_args();
    $preserve_keys = true;
    $result = array();
    if (is_bool(end($arg_list))) {
        $preserve_keys = array_pop($arg_list);
    }

    foreach ((array) $arg_list as $arg) {
        foreach ((array) $arg as $k => $v) {
            if ($preserve_keys == true) {
                $result[$k] = !empty($result[$k]) && is_array($result[$k]) ? fn_array_merge($result[$k], $v) : $v;
            } else {
                $result[] = $v;
            }
        }
    }

    return $result;
}

//
// Restore original variable content (unstripped)
// Parameters should be the variables names
// E.g. fn_trusted_vars("product_data","big_text","etcetc")
function fn_trusted_vars()
{
    $args = func_get_args();
    fn_wizacha_backend_design_w_trusted_vars($args,$_POST,$_GET);
    if (sizeof($args) > 0) {
        foreach ($args as $k => $v) {
            if (isset($_POST[$v])) {
                $_REQUEST[$v] = $_POST[$v];
            } elseif (isset($_GET[$v])) {
                $_REQUEST[$v] = $_GET[$v];
            }
        }
    }

    return true;
}

// EnCrypt text wrapper function
function fn_encrypt_text($text)
{
    // Padding input to fix length to a multiple of 8
    $block_size = 8;
    $pad = $block_size - (strlen($text) % $block_size);
    $text = str_pad($text, strlen($text) + $pad, chr(0));
    return base64_encode(container()->get('cscart.crypt')->encrypt($text));
}

// DeCrypt text wrapper function
function fn_decrypt_text($text)
{
    return container()->get('cscart.crypt')->decrypt(base64_decode($text));
}

function fn_recursive_makehash($tab)
{
    if (!is_array($tab)) {
        return $tab;
    }

    $p = '';
    foreach ($tab as $a => $b) {
        $p .= sprintf('%08X%08X', crc32($a), crc32(fn_recursive_makehash($b)));
    }

    return $p;
}

function fn_delete_static_data($param_id)
{
    $scheme = fn_get_schema('static_data', 'schema');

    if (!empty($param_id)) {
        $static_data = db_get_row("SELECT id_path, section FROM ?:static_data WHERE param_id = ?i", $param_id);
        $id_path = $static_data['id_path'];
        $section = $static_data['section'];

        if (!empty($scheme[$section]['skip_edition_checking']) && Registry::get('runtime.company_id')) {
            fn_set_notification('E', __('error'), __('access_denied'));

            return false;
        }

        $delete_ids = db_get_fields("SELECT param_id FROM ?:static_data WHERE param_id = ?i OR id_path LIKE ?l", $param_id, "$id_path/%");

        db_query("DELETE FROM ?:static_data WHERE param_id IN (?n)", $delete_ids);
        db_query("DELETE FROM ?:static_data_descriptions WHERE param_id IN (?n)", $delete_ids);
    }

    return true;
}

function fn_get_static_data($params, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $default_params = array();

    $params = array_merge($default_params, $params);

    $schema = fn_get_schema('static_data', 'schema');
    $section_data = $schema[$params['section']];

    $fields = array(
        'sd.param_id',
        'sd.param',
        '?:static_data_descriptions.descr'
    );

    $condition = '';
    $sorting = "sd.position";

    if (!empty($params['multi_level'])) {
        $sorting = "sd.parent_id, sd.position, ?:static_data_descriptions.descr";
    }

    if (!empty($params['status'])) {
        $condition .= db_quote(" AND sd.status = ?s", $params['status']);
    }

    // Params from request
    if (!empty($section_data['owner_object'])) {
        $param = $section_data['owner_object'];
        $value = $param['default_value'];

        if (!empty($params['request'][$param['key']])) {
            $value = $params['request'][$param['key']];
        } elseif (!empty($_REQUEST[$param['key']])) {
            $value = $_REQUEST[$param['key']];
        }

        $condition .= db_quote(" AND sd.?p = ?s", $param['param'], $value);
    }

    if (!empty($params['use_localization'])) {
        $condition .= fn_get_localizations_condition('sd.localization');
    }

    if (!empty($params['get_params'])) {
        $fields[] = "sd.param_2";
        $fields[] = "sd.param_3";
        $fields[] = "sd.param_4";
        $fields[] = "sd.param_5";
        $fields[] = "sd.status";
        $fields[] = "sd.position";
        $fields[] = "sd.parent_id";
        $fields[] = "sd.id_path";
    }

    $s_data = db_get_hash_array("SELECT " . implode(', ', $fields) . " FROM ?:static_data AS sd LEFT JOIN ?:static_data_descriptions ON sd.param_id = ?:static_data_descriptions.param_id AND ?:static_data_descriptions.lang_code = ?s WHERE sd.section = ?s ?p ORDER BY sd.position", 'param_id', $lang_code, $params['section'], $condition);

    if (!empty($params['icon_name'])) {
        $_icons = fn_get_image_pairs(array_keys($s_data), $params['icon_name'], 'M', true, true, $lang_code);
        foreach ($s_data as $k => $v) {
            $s_data[$k]['icon'] = !empty($_icons[$k]) ? array_pop($_icons[$k]) : array();
        }
    }

    if (!empty($params['generate_levels'])) {
        foreach ($s_data as $k => $v) {
            if (!empty($v['id_path'])) {
                $s_data[$k]['level'] = substr_count($v['id_path'], '/');
            }
        }
    }

    if (!empty($params['multi_level']) && !empty($params['get_params'])) {
        $s_data = fn_make_tree($s_data, 0, 'param_id', 'subitems');
    }

    if (!empty($params['plain'])) {
        $s_data = fn_multi_level_to_plain($s_data, 'subitems');
    }

    return $s_data;
}

function fn_update_static_data($data, $param_id, $section, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $current_id_path = '';
    $schema = fn_get_schema('static_data', 'schema');
    $section_data = $schema[$section];

    if (!empty($section_data['has_localization'])) {
        $data['localization'] = empty($data['localization']) ? '' : fn_implode_localizations($data['localization']);
    }

    if (!empty($data['megabox'])) { // parse megabox value
        foreach ($data['megabox']['type'] as $p => $v) {
            if (!empty($v)) {
                $data[$p] = $v . ':' . intval($data[$p][$v]) . ':' . $data['megabox']['use_item'][$p];
            } else {
                $data[$p] = '';
            }
        }
    }

    $condition = db_quote('param_id = ?i', $param_id);

    if (!empty($param_id)) {
        $current_id_path = db_get_field("SELECT id_path FROM ?:static_data WHERE $condition");
        db_query("UPDATE ?:static_data SET ?u WHERE param_id = ?i", $data, $param_id);
        db_query('UPDATE ?:static_data_descriptions SET ?u WHERE param_id = ?i AND lang_code = ?s', $data, $param_id, $lang_code);
    } else {
        $data['section'] = $section;

        $param_id = $data['param_id'] = db_query("INSERT INTO ?:static_data ?e", $data);
        foreach (Languages::getAll() as $data['lang_code'] => $_v) {
            db_query('REPLACE INTO ?:static_data_descriptions ?e', $data);
        }
    }

    // Generate ID path
    if (isset($data['parent_id'])) {
        if (!empty($data['parent_id'])) {
            $new_id_path = db_get_field("SELECT id_path FROM ?:static_data WHERE param_id = ?i", $data['parent_id']);
            $new_id_path .= '/' . $param_id;
        } else {
            $new_id_path = $param_id;
        }

        if (!empty($current_id_path) && $current_id_path != $new_id_path) {
            db_query("UPDATE ?:static_data SET id_path = CONCAT(?s, SUBSTRING(id_path, ?i)) WHERE id_path LIKE ?l", "$new_id_path/", strlen($current_id_path . '/') + 1, "$current_id_path/%");
        }
        db_query("UPDATE ?:static_data SET id_path = ?s WHERE param_id = ?i", $new_id_path, $param_id);
    }

    if (!empty($section_data['icon'])) {
        fn_attach_image_pairs('static_data_icon', $section_data['icon']['type'], $param_id, $lang_code);
    }

    return $param_id;
}

function fn_static_data_megabox($value)
{
    if (!empty($value)) {
        list($type, $id, $use_item) = explode(':', $value);

        return array(
            'types' => array(
                $type => array(
                    'value' => intval($id)
                ),
            ),
            'use_item' => $use_item,
        );
    } else {
        return array();
    }
}

function fn_make_tree($tree, $parent_id, $key, $parent_key)
{
    $res = array();
    foreach ($tree as $id => $row) {
        if ($row['parent_id'] == $parent_id) {
            $res[$id] = $row;
            $res[$id][$parent_key] = fn_make_tree($tree, $row[$key], $key, $parent_key);
        }
    }

    return $res;
}

/**
 * Convert multi-level array with "subitems" to plain representation
 *
 * @param array $data source array
 * @param string $key key with subitems
 * @param array $result resulting array, passed along multi levels
 * @return array structured data
 */
function fn_multi_level_to_plain($data, $key, $result = array())
{
    foreach ($data as $k => $v) {
        if (!empty($v[$key])) {
            unset($v[$key]);
            array_push($result, $v);
            $result = fn_multi_level_to_plain($data[$k][$key], $key, $result);
        } else {
            array_push($result, $v);
        }
    }

    return $result;
}

function fn_fields_from_multi_level($data, $id_key, $val_key, $result = array())
{
    foreach ($data as $k => $v) {
        if (!empty($v[$id_key]) && !empty($v[$val_key])) {
            $result[$v[$id_key]] = $v[$val_key];
        }
    }

    return $result;
}

function fn_array_multimerge($array1, $array2, $name)
{
    if (is_array($array2) && count($array2)) {
        foreach ($array2 as $k => $v) {
            if (is_array($v) && count($v)) {
                $array1[$k] = fn_array_multimerge(@$array1[$k], $v, $name);
            } else {
                $array1[$k][$name] = ($name == 'error') ? 0 : $v;
            }
        }
    } else {
        $array1 = $array2;
    }

    return $array1;
}

/**
 * Validate email address
 *
 * @param string $email email
 * @return boolean is email correct?
 */
function fn_validate_email($email, $show_error = false)
{
    if (\filter_var(\stripslashes($email), \FILTER_VALIDATE_EMAIL)) {
        return true;
    } elseif ($show_error) {
        fn_set_notification('E', __('error'), __('text_not_valid_email', array(
            '[email]' => $email
        )));
    }

    return false;
}

function fn_flush()
{
    if (defined('AJAX_REQUEST') && !Registry::get('runtime.comet')) { // do not flush output during ajax request, but flush it for COMET

        return false;
    }

    if (function_exists('ob_flush')) {
        @ob_flush();
    }

    flush();
}

function fn_echo($value)
{
    if (defined('CONSOLE')) {
        $value = str_replace(array('<br>', '<br />'), "\n", $value);
        $value = strip_tags($value);
    }

    echo $value;

    fn_flush();
}

/**
* Set state for time-consuming processes
*
* @param string $prop property name
* @param string $value value to set
* @param mixed $extra extra data
* @return boolean - always true
*/
function fn_set_progress($prop, $value, $extra = null)
{
    if (Registry::get('runtime.comet') == true) {
        if ($prop == 'step_scale') {
            Registry::get('ajax')->setStepScale($value);

        } elseif ($prop == 'parts') {
            Registry::get('ajax')->setProgressParts($value);

        } elseif ($prop == 'echo') {
            Registry::get('ajax')->progressEcho($value, ($extra === false) ? $extra : true);

        } elseif ($prop == 'title') {
            Registry::get('ajax')->changeTitle($value);
        }
    } else {
        if ($prop == 'echo') {
            fn_echo($value);
        }
    }

    return true;
}

//
// Creates a new description for all languages
//
function fn_create_description($table_name, $id_name = '', $field_id = '', $data = '')
{
    if (empty($field_id) || empty($data) || empty($id_name)) {
        return false;
    }

    $data[$id_name] = $field_id;

    foreach (Languages::getAll() as $data['lang_code'] => $v) {
        db_query("REPLACE INTO ?:$table_name ?e", $data);
    }

    return true;
}

function fn_js_escape($str)
{
    return strtr($str, array('\\' => '\\\\',  "'" => "\\'", '"' => '\\"', "\r" => '\\r', "\n" => '\\n', "\t" => '\\t', '</' => '<\/', "/" => '\\/'));
}

function fn_object_to_array($object)
{
    if (!is_object($object) && !is_array($object)) {
        return $object;
    }
    if (is_object($object)) {
        $object = get_object_vars($object);
    }

    return array_map('fn_object_to_array', $object);
}

function fn_define($const, $value)
{
    if (!defined($const)) {
        define($const, $value);
    }
}

function fn_create_periods($params)
{
    $today = getdate(TIME);
    $period = !empty($params['period']) ? $params['period'] : null;

    $time_from = !empty($params['time_from']) ? fn_parse_date($params['time_from']) : 0;
    $time_to = !empty($params['time_to']) ? fn_parse_date($params['time_to'], true) : TIME;

    // Current dates
    if ($period == 'D') {
        $time_from = mktime(0, 0, 0, $today['mon'], $today['mday'], $today['year']);
        $time_to = TIME;

    } elseif ($period == 'W') {
        $wday = empty($today['wday']) ? "6" : (($today['wday'] == 1) ? "0" : $today['wday'] - 1);
        $wstart = getdate(strtotime("-$wday day"));
        $time_from = mktime(0, 0, 0, $wstart['mon'], $wstart['mday'], $wstart['year']);
        $time_to = TIME;

    } elseif ($period == 'M') {
        $time_from = mktime(0, 0, 0, $today['mon'], 1, $today['year']);
        $time_to = TIME;

    } elseif ($period == 'Y') {
        $time_from = mktime(0, 0, 0, 1, 1, $today['year']);
        $time_to = TIME;

    // Last dates
    } elseif ($period == 'LD') {
        $today = getdate(strtotime("-1 day"));
        $time_from = mktime(0, 0, 0, $today['mon'], $today['mday'], $today['year']);
        $time_to = mktime(23, 59, 59, $today['mon'], $today['mday'], $today['year']);

    } elseif ($period == 'LW') {
        $today = getdate(strtotime("-1 week"));
        $wday = empty($today['wday']) ? 6 : (($today['wday'] == 1) ? 0 : $today['wday'] - 1);
        $wstart = getdate(strtotime("-$wday day", mktime(0, 0, 0, $today['mon'], $today['mday'], $today['year'])));
        $time_from = mktime(0, 0, 0, $wstart['mon'], $wstart['mday'], $wstart['year']);

        $wend = getdate(strtotime("+6 day", $time_from));
        $time_to = mktime(23, 59, 59, $wend['mon'], $wend['mday'], $wend['year']);

    } elseif ($period == 'LM') {
        $today = getdate(strtotime("-1 month"));
        $time_from = mktime(0, 0, 0, $today['mon'], 1, $today['year']);
        $time_to = mktime(23, 59, 59, $today['mon'], date('t', strtotime("-1 month")), $today['year']);

    } elseif ($period == 'LY') {
        $today = getdate(strtotime("-1 year"));
        $time_from = mktime(0, 0, 0, 1, 1, $today['year']);
        $time_to = mktime(23, 59, 59, 12, 31, $today['year']);

    // Last dates
    } elseif ($period == 'HH') {
        $today = getdate(strtotime("-23 hours"));
        $time_from = mktime($today['hours'], $today['minutes'], $today['seconds'], $today['mon'], $today['mday'], $today['year']);
        $time_to = TIME;

    } elseif ($period == 'HW') {
        $today = getdate(strtotime("-6 day"));
        $time_from = mktime($today['hours'], $today['minutes'], $today['seconds'], $today['mon'], $today['mday'], $today['year']);
        $time_to = TIME;

    } elseif ($period == 'HM') {
        $today = getdate(strtotime("-29 day"));
        $time_from = mktime($today['hours'], $today['minutes'], $today['seconds'], $today['mon'], $today['mday'], $today['year']);
        $time_to = TIME;

    } elseif ($period == 'HC') {
        $today = getdate(strtotime('-' . $params['last_days'] . ' day'));
        $time_from = mktime($today['hours'], $today['minutes'], $today['seconds'], $today['mon'], $today['mday'], $today['year']);
        $time_to = TIME;
    }

    Registry::get('view')->assign('time_from', $time_from);
    Registry::get('view')->assign('time_to', $time_to);

    return array($time_from, $time_to);
}

function fn_parse_date($timestamp, $end_time = false)
{
    if (!empty($timestamp)) {
        if (is_numeric($timestamp)) {
            return $timestamp;
        }

        $ts = explode('/', $timestamp);
        $ts = array_map('intval', $ts);
        if (empty($ts[2])) {
            $ts[2] = date('Y');
        }
        if (count($ts) == 3) {
            list($h, $m, $s) = $end_time ? array(23, 59, 59) : array(0, 0, 0);
            if (Registry::get('settings.Appearance.calendar_date_format') == 'month_first') {
                $timestamp = mktime($h, $m, $s, $ts[0], $ts[1], $ts[2]);
            } else {
                $timestamp = mktime($h, $m, $s, $ts[1], $ts[0], $ts[2]);
            }
        } else {
            $timestamp = TIME;
        }
    }

    return !empty($timestamp) ? $timestamp : TIME;
}

//
// Set the session data entry
// we use session.cookie_domain and session.cookie_path
//
function fn_set_session_data($var, $value, $expiry = 0)
{
    $_SESSION['settings'][$var] = array (
        'value' => $value
    );

    if (!empty($expiry)) {
        $_SESSION['settings'][$var]['expiry'] = TIME + $expiry;
    }
}

//
// Delete the session data entry
//
function fn_delete_session_data()
{
    $args = func_get_args();
    if (!empty($args)) {
        foreach ($args as $var) {
            unset($_SESSION['settings'][$var]);
        }

        return true;
    }

    return false;
}

//
// Get the session data entry
//
function fn_get_session_data($var = '')
{
    if (!$var) {
        $return = array();
        if (is_array($_SESSION['settings'])) {
            foreach ($_SESSION['settings'] as $name => $setting) {
                if (empty($setting['expiry']) || $setting['expiry'] > TIME) {
                    $return[$name] = $setting['value'];
                } else {
                    unset($_SESSION['settings'][$name]);
                }
            }
        }
    } else {
        if (!empty($_SESSION['settings'][$var]) && (empty($_SESSION['settings'][$var]['expiry']) ||  $_SESSION['settings'][$var]['expiry'] > TIME)) {
            $return = isset($_SESSION['settings'][$var]['value']) ? $_SESSION['settings'][$var]['value'] : '';
        } else {
            if (!empty($_SESSION['settings'][$var])) {
                unset($_SESSION['settings'][$var]);
            }

            $return = false;
        }
    }

    return $return;
}

//
// The function returns Host IP and Proxy IP.
//
function fn_get_ip($return_int = false)
{
    $forwarded_ip = '';
    $fields = array(
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_FORWARDED',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED',
        'HTTP_forwarded_ip',
        'HTTP_X_COMING_FROM',
        'HTTP_COMING_FROM',
        'HTTP_CLIENT_IP',
        'HTTP_VIA',
        'HTTP_XROXY_CONNECTION',
        'HTTP_PROXY_CONNECTION');

    $matches = array();
    foreach ($fields as $f) {
        if (!empty($_SERVER[$f])) {
            preg_match("/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/", $_SERVER[$f], $matches);
            if (!empty($matches) && !empty($matches[0]) && $matches[0] != $_SERVER['REMOTE_ADDR']) {
                $forwarded_ip = $matches[0];
                break;
            }
        }
    }

    $ip = array('host' => $forwarded_ip, 'proxy' => $_SERVER['REMOTE_ADDR']);

    if ($return_int) {
        foreach ($ip as $k => $_ip) {
            $ip[$k] = empty($_ip) ? 0 : sprintf("%u", ip2long($_ip));
        }
    }

    if (empty($ip['host']) || !fn_is_inet_ip($ip['host'], $return_int)) {
        $ip['host'] = $ip['proxy'];
        $ip['proxy'] = $return_int ? 0 : '';
    }

    return $ip;
}

//
// If there is IP address in address scope global then return true.
//
function fn_is_inet_ip($ip, $is_int = false)
{
    if ($is_int) {
        $ip = long2ip($ip);
    }
    $_ip = explode('.', $ip);

    return
        ($_ip[0] == 10 ||
        ($_ip[0] == 172 && $_ip[1] >= 16 && $_ip[1] <= 31) ||
        ($_ip[0] == 192 && $_ip[1] == 168) ||
        ($_ip[0] == 127 && $_ip[1] == 0 && $_ip[2] == 0 && $_ip[3] == 1) ||
        ($_ip[0] == 255 && $_ip[1] == 255 && $_ip[2] == 255 && $_ip[3] == 255))
        ? false : true;
}

//
// Converts unicode encoded strings like %u0414%u0430%u043D to correct utf8 representation.
//
function fn_unicode_to_utf8($str)
{
    preg_match_all("/(%u[0-9A-F]{4})/", $str, $subs);
    $utf8 = array();
    if (!empty($subs[1])) {
        foreach ($subs[1] as $unicode) {
            $_unicode = hexdec(substr($unicode, 2, 4));
            if ($_unicode < 128) {
                $_utf8 = chr($_unicode);
            } elseif ($_unicode < 2048) {
                $_utf8 = chr(192 +  (($_unicode - ($_unicode % 64)) / 64));
                $_utf8 .= chr(128 + ($_unicode % 64));
            } else {
                $_utf8 = chr(224 + (($_unicode - ($_unicode % 4096)) / 4096));
                $_utf8 .= chr(128 + ((($_unicode % 4096) - ($_unicode % 64)) / 64));
                $_utf8 .= chr(128 + ($_unicode % 64));
            }
            $utf8[$unicode] = $_utf8;
        }
    }
    if (!empty($utf8)) {
        foreach ($utf8 as $unicode => $_utf8) {
            $str = str_replace($unicode, $_utf8, $str);
        }
    }

    return $str;
}

function fn_array_key_intersect(&$a, &$b)
{
    $array = array();
    foreach ($a as $key => $value) {
        if (isset($b[$key])) {
            $array[$key] = $value;
        }
    }

    return $array;
}

/**
 * Attaches parameters to url. If parameter already exists, it removed.
 *
 * @param string $url URN (Uniform Resource Name or Query String)
 * @param string $attachment query sting with parameters
 * @return string URL with attached parameters
 */
function fn_link_attach($url, $attachment)
{
    $url = str_replace('&amp;', '&', $url);
    parse_str($attachment, $arr);

    $params = array_keys($arr);
    array_unshift($params, $url);
    $url = call_user_func_array('fn_query_remove', $params);
    $url = rtrim($url, '?&');
    $url .= ((strpos($url, '?') === false) ? '?' : '&') . $attachment;

    return $url;
}

/**
 * Get views for the object
 *
 * @param string $object object to init view for
 * @return array views list
 */
function fn_get_views($object)
{
    return db_get_hash_array("SELECT name, view_id FROM ?:views WHERE object = ?s AND user_id = ?i", 'view_id', $object, $_SESSION['auth']['user_id']);
}

/**
 * Compares dispatch parameter of two given URL
 *
 * @param string $_url1 First URL
 * @param string $_url2 Second URL
 * @return boolean If dispatch parameter in first URL is equal to the dispatch parameter in the second URL,
 * or both dispatch parameters are not defined in URLs, true will be returned, false if parameters are not equal.
 */
function fn_compare_dispatch($_url1, $_url2)
{
    $q1 = $q2 = array();

    $url1 = $_url1;
    $url2 = $_url2;

    $url1 = str_replace('&amp;', '&', $url1);
    if (($pos = strpos($url1, '?')) !== false) {
        $url1 = substr($url1, $pos + 1);
    } elseif (strpos($url1, '&') === false) {
        $url1 = '';
    }

    $url2 = str_replace('&amp;', '&', $url2);
    if (($pos = strpos($url2, '?')) !== false) {
        $url2 = substr($url2, $pos + 1);
    } elseif (strpos($url2, '&') === false) {
        $url2 = '';
    }

    parse_str($url1, $q1);
    parse_str($url2, $q2);

    $result = (isset($q1['dispatch']) && isset($q2['dispatch']) && $q1['dispatch'] == $q2['dispatch'] || !isset($q1['dispatch']) && !isset($q2['dispatch']));

    //From seo hook
    $url1 = fn_url($url1);
    $url2 = fn_url($url2);
    $pos1 = strpos($url1, '?');
    if ($pos1 !== false) {
        $url1 = substr($url1, 0, $pos1);
    }
    $pos2 = strpos($url2, '?');
    if ($pos2 !== false) {
        $url2 = substr($url2, 0, $pos2);
    }
    $result = ($url1 == $url2);


    return $result;
}

/**
 * Get all schema files (e.g. exim schemas, admin area menu)
 *
 * @param string $schema_dir schema name (subdirectory in /schema directory)
 * @param string $name file name/prefix
 * @param bool $force_addon_init initialize disabled addons also
 * @return array schema definition (if exists)
 */
function fn_get_schema($schema_dir, $name, $force_addon_init = false)
{
    static $schema_files_cache = [];
    $key_register_cache = sha1(Registry::get('config.dir.schemas')).'_schema_' . $schema_dir . '_' . $name;

    if (!array_key_exists($key_register_cache,$schema_files_cache) || $force_addon_init) {
        Registry::registerCache($key_register_cache, array('settings', 'addons'), Registry::cacheLevel('static')); // FIXME: hardcoded for settings-based schemas

        if (!Registry::isExist('schema_' . $schema_dir . '_' . $name)) {

            $files = array();
            $path_name = Registry::get('config.dir.schemas') . $schema_dir . '/' . $name;
            if (file_exists($path_name . '.php')) {
                $files[] = $path_name . '.php';
            }

            if (file_exists($path_name . '_' . fn_strtolower(PRODUCT_EDITION) . '.php')) {
                $files[] = $path_name . '_' . fn_strtolower(PRODUCT_EDITION) . '.php';
            }

            $addons = Registry::get('addons');
            if (!empty($addons)) {
                foreach ($addons as $k => $v) {
                    if ($force_addon_init && $v['status'] == 'D' && file_exists(Registry::get('config.dir.addons') . $k . '/func.php')) { // force addon initialization
                        include_once(Registry::get('config.dir.addons') . $k . '/func.php');
                    }

                    if (empty($v['status'])) {
                        // FIX ME: Remove me
                        error_log("ERROR: Schema $schema_dir:$name initialization: Bad '$k' addon data:" . serialize($v) . " Addons Registry:" . serialize(Registry::get('addons')));
                    }

                    if ((!empty($v['status']) && $v['status'] == 'A') || $force_addon_init) {

                        $path_name = Registry::get('config.dir.addons') . $k . '/schemas/' . $schema_dir . '/' . $name;
                        if (file_exists($path_name . '_' . fn_strtolower(PRODUCT_EDITION) . '.php')) {
                            array_unshift($files, $path_name . '_' . fn_strtolower(PRODUCT_EDITION) . '.php');
                        }
                        if (file_exists($path_name . '.php')) {
                            array_unshift($files, $path_name . '.php');
                        }
                        if (file_exists($path_name . '.post.php')) {
                            $files[] = $path_name . '.post.php';
                        }
                        if (file_exists($path_name . '_' . fn_strtolower(PRODUCT_EDITION) . '.post.php')) {
                            $files[] = $path_name . '_' . fn_strtolower(PRODUCT_EDITION) . '.post.php';
                        }
                    }
                }
            }

            Registry::set('schema_' . $schema_dir . '_' . $name, $files);
        } else {
            $files = Registry::get('schema_' . $schema_dir . '_' . $name);
        }

        $schema_files_cache[$key_register_cache] = $files;
    }

    $schema = [];
    $include_once = (strpos($name, '.functions') !== false);

    foreach ($schema_files_cache[$key_register_cache] as $file) {
        $schema = $include_once ? include_once($file) : include($file);
    }

    return $schema;
}

/**
 * Check access permissions for certain controller/modes
 *
 * @param string $controller controller to check permissions for
 * @param string $mode controller mode to check permissions for
 * @param string $schema_name permissions schema name (demo_mode/production)
 * @param string $request_method check permissions for certain method (POST/GET)
 * @return boolean true if access granted, false otherwise
 */
function fn_check_permissions($controller, $mode, $schema_name, $request_method = '', $request_variables = array())
{
    $request_method = empty($request_method) ? $_SERVER['REQUEST_METHOD'] : $request_method;

    $schema = fn_get_permissions_schema($schema_name);

    if ($schema_name == 'admin') {
        if (Registry::get('runtime.company_id') && !Registry::get('runtime.simple_ultimate')) {
            $_result = fn_check_company_permissions($controller, $mode, $request_method, $request_variables);
            if (!$_result) {
                return false;
            }
        }

        return fn_check_admin_permissions($schema, $controller, $mode, $request_method, $request_variables);
    }

    if ($schema_name == 'demo') {

        if (isset($schema[$controller])) {
            if ((isset($schema[$controller]['restrict']) && in_array($request_method, $schema[$controller]['restrict'])) || (isset($schema[$controller]['modes'][$mode]) && in_array($request_method, $schema[$controller]['modes'][$mode]))) {
                return false;
            }
        }
    }

    if ($schema_name == 'trusted_controllers') {

        $allow = !empty($schema[$controller]['allow']) ? $schema[$controller]['allow'] : 0;
        if (!is_array($allow)) {
            return $allow;
        } else {
            return (!empty($allow[$mode]) ? $allow[$mode] : 0);
        }
    }

    return true;
}

/**
 * Gets corresponding permission or condition depanding of reques parameters
 *
 * @param array $param_permissions Parameters permissions schema
 * @param array $parms Request parameters
 * @return mixed Permission or condition, NULL if value was not found
 */
function fn_get_request_param_permissions($param_permissions, $params)
{
    $permission = null;

    foreach ($param_permissions as $param => $values) {
        if (!empty($params[$param]) && !empty($values[$params[$param]])) {
            $permission = $values[$params[$param]];
            break;
        }
    }

    return $permission;
}

function fn_check_company_permissions($controller, $mode, $request_method = '', $request_variables = array())
{
    $schema = fn_get_permissions_schema('vendor');
    $default_permission = isset($schema['default_permission']) ? $schema['default_permission'] : false;
    $schema = $schema['controllers'];

    if (isset($schema[$controller])) {
        // Check if permissions set for certain mode
        if (isset($schema[$controller]['modes']) && isset($schema[$controller]['modes'][$mode])) {
            if (isset($schema[$controller]['modes'][$mode]['permissions'])) {
                $permission = is_array($schema[$controller]['modes'][$mode]['permissions']) ? $schema[$controller]['modes'][$mode]['permissions'][$request_method] : $schema[$controller]['modes'][$mode]['permissions'];
                if (isset($schema[$controller]['modes'][$mode]['condition'])) {
                    $condition = $schema[$controller]['modes'][$mode]['condition'];
                }
            } elseif (!empty($request_variables)) {
                if (isset($schema[$controller]['modes'][$mode]['param_permissions'])) {
                    $permission = fn_get_request_param_permissions($schema[$controller]['modes'][$mode]['param_permissions'], $request_variables);
                    if (!isset($permission) && isset($schema[$controller]['modes'][$mode]['param_permissions']['default_permission'])) {
                        $default_permission = $schema[$controller]['modes'][$mode]['param_permissions']['default_permission'];
                    }
                }
                if (isset($schema[$controller]['modes'][$mode]['condition'])) {
                    $condition = fn_get_request_param_permissions($schema[$controller]['modes'][$mode]['condition'], $request_variables);
                }
            }
        }

        // Check common permissions
        if (!isset($permission) && isset($schema[$controller]['permissions'])) {
            $permission = is_array($schema[$controller]['permissions']) ? $schema[$controller]['permissions'][$request_method] : $schema[$controller]['permissions'];
        }
    }

    $permission = isset($permission) ? $permission : $default_permission;

    if (isset($condition)) {
        if ($condition['operator'] == 'or') {
            $permission = ($permission || fn_execute_permission_condition($condition));
        } elseif ($condition['operator'] == 'and') {
            $permission = ($permission && fn_execute_permission_condition($condition));
        }
    }

    return $permission;
}

function fn_check_admin_permissions(&$schema, $controller, $mode, $request_method = '', $request_variables = array())
{
    $_schema = isset($schema['root']) ? $schema['root'] : array();

    if (isset($_schema[$controller])) {
        // Check if permissions set for certain mode
        if (isset($_schema[$controller]['modes']) && isset($_schema[$controller]['modes'][$mode])) {
            if (isset($_schema[$controller]['modes'][$mode]['permissions'])) {
                $permission = is_array($_schema[$controller]['modes'][$mode]['permissions']) ? $_schema[$controller]['modes'][$mode]['permissions'][$request_method] : $_schema[$controller]['modes'][$mode]['permissions'];
                if (isset($_schema[$controller]['modes'][$mode]['condition'])) {
                    $condition = $_schema[$controller]['modes'][$mode]['condition'];
                }

             } elseif (!empty($request_variables) & !empty($_schema[$controller]['modes'][$mode]['param_permissions'])) {
                $permission = fn_get_request_param_permissions($_schema[$controller]['modes'][$mode]['param_permissions'], $request_variables);
            }
        }

        // Check common permissions
        if (empty($permission) && !empty($_schema[$controller]['permissions'])) {
            $permission = is_array($_schema[$controller]['permissions']) ? $_schema[$controller]['permissions'][$request_method] : $_schema[$controller]['permissions'];
            if (isset($_schema[$controller]['condition'])) {
                $condition = $_schema[$controller]['condition'];
            }
        }

        if (empty($permission)) { // This controller does not have permission checking

            return true;
        } else {
            $result = false;

            if (isset($condition)) {
                if ($condition['operator'] == 'or') {
                    return ($result || fn_execute_permission_condition($condition));
                } elseif ($condition['operator'] == 'and') {
                    return ($result && fn_execute_permission_condition($condition));
                }
            }

            return $result;
        }
    }

    return true;
}

/**
 * Execute additional condition for permissions
 * Condition may be function or other conditions(will be implemented later)
 *
 * @param array $condition
 *
 * @return boolean result of $condition
 */
function fn_execute_permission_condition($condition)
{
    if (isset($condition['function'])) {
        $func_name = array_shift($condition['function']);
        $params = $condition['function'];
        // here we can process parameters
        return call_user_func_array($func_name, $params);
    }

    return false;
}

/**
 * Function checks do user want to manage his own profile
 *
 * @return boolean true, if user want to view/edit own profile, false otherwise.
 */
function fn_check_permission_manage_own_profile()
{
    if (Registry::get('runtime.controller') == 'profiles' && \in_array(Registry::get('runtime.mode'), ['update', 'manage', 'delete']) === true) {
        $company_id = db_get_field("SELECT company_id FROM ?:users WHERE user_id = ?i", $_REQUEST['user_id']);

        if (empty($_REQUEST['user_id']) || // Il edite son propre compte
            ($_REQUEST['user_id'] == $_SESSION['auth']['user_id']) || // Il edite son propre compte
            ($_SESSION['auth']['user_type'] === UserType::ADMIN()->getValue()) || // C'est un admin de MP
            (!empty($company_id) && !empty($_SESSION['auth']['company_id']) && $_SESSION['auth']['company_id'] == $company_id)) // Un admin vendor édite le compte d'un autre admin vendor de la même companie
        {
            return true;
        } else {
            return false;
        }
    } elseif (Registry::get('runtime.controller') == 'auth' && Registry::get('runtime.mode') == 'password_change') {
        return true;
    } else {
        return false;
    }
}

/**
 * Check if an admin vendor can create another admin vendor
 *
 * @return boolean true if user_type is V
 */
function fn_check_permission_add_vendor_admin()
{
    return $_GET['user_type'] == 'V';
}

function fn_check_view_permissions($data, $request_method = '')
{
    if ((!defined('RESTRICTED_ADMIN') && !Registry::get('runtime.company_id')) || !trim($data) || $data == 'submit') {
        return true;
    }

    // dispatch=controller.mode
    if (!preg_match("/dispatch=(\w+)\.(\w+)/", $data, $m)) {
        $request_method = !empty($request_method) ? $request_method : 'POST';

        // dispatch[controller.mode]
        if (!preg_match("/dispatch(?:\[|%5B)(\w+)\.(\w+)/", $data, $m)) {

            // just get something :)
            preg_match("/(\w+)\.?(\w+)?/", $data, $m);
        }
    } else {
        $request_method = !empty($request_method) ? $request_method : 'GET';
    }

    list(, $request_params, ) = fn_parse_urn($data);

    return fn_check_permissions($m[1], $m[2], 'admin', $request_method, $request_params);
}

/**
 * Used in templates to check access to forms
 *
 * @return boolean True, if form should be restricted, false if form should be processed as usual
 */
function fn_check_form_permissions()
{
    if (Registry::get('runtime.company_id') || defined('RESTRICTED_ADMIN')) {
        return !fn_check_permissions(Registry::get('runtime.controller'), Registry::get('runtime.mode'), 'admin', 'POST');
    } else {
        return false;
    }
}

/**
 * This function searches placeholders in the text and converts the found data.
 *
 * @param string $text
 * @return changed text
 */
function fn_text_placeholders($text)
{
    static $placeholders = array(
        'price',
        'weight'
    );

    $pattern = '/%([,\.\w]+):(' . implode('|', $placeholders) . ')%/U';
    $text = preg_replace_callback($pattern, 'fn_apply_text_placeholders', $text);

    return $text;
}

function fn_apply_text_placeholders($matches)
{
    if (isset($matches[1]) && !empty($matches[2])) {
        if ($matches[2] == 'price') {
            $currencies = Registry::get('currencies');
            $currency = $currencies[CART_SECONDARY_CURRENCY];
            $value = fn_format_rate_value($matches[1], 'F', $currency['decimals'], $currency['decimals_separator'], $currency['thousands_separator'], $currency['coefficient']);

            return $currency['after'] == 'Y' ? $value . $currency['symbol'] : $currency['symbol'] . $value;
        } elseif ($matches[2] == 'weight') {
            return $matches[1] . '&nbsp;' . Registry::get('settings.General.weight_symbol');
        }
    }
}

function fn_generate_code($prefix = '', $length = 12)
{
    $postfix = '';
    $chars = implode('', range('0', '9')) . implode('', range('A', 'Z'));

    for ($i = 0; $i < $length; $i++) {
        $ratio = (strlen(str_replace('-', '', $postfix)) + 1) / 4;
        $postfix .= $chars[rand(0, strlen($chars) - 1)];
           $postfix .= ((ceil($ratio) == $ratio) && ($i < $length - 1)) ? '-' : '';
    }

    return (!empty($prefix)) ?  strtoupper($prefix) . '-' . $postfix : $postfix;
}

function fn_get_shipping_images()
{
    $data = db_get_memoized_array("SELECT ?:shippings.shipping_id, ?:shipping_descriptions.shipping FROM ?:shippings INNER JOIN ?:images_links ON CAST(?:shippings.shipping_id AS CHAR) = ?:images_links.object_id AND ?:images_links.object_type = 'shipping' LEFT JOIN ?:shipping_descriptions ON ?:shippings.shipping_id = ?:shipping_descriptions.shipping_id AND ?:shipping_descriptions.lang_code = ?s WHERE ?:shippings.status = 'A' ORDER BY ?:shippings.position, ?:shipping_descriptions.shipping", (string) GlobalState::interfaceLocale());

    if (empty($data)) {
        return array ();
    }

    $images = array ();

    foreach ($data as $key => $entry) {
        $image = fn_get_image_pairs($entry['shipping_id'], 'shipping', 'M');

        if (!empty($image['icon'])) {
            $image['icon']['alt'] = empty($image['icon']['alt']) ? $entry['shipping'] : $image['icon']['alt'];
            $images[] = $image['icon'];
        }
    }

    return $images;
}

function fn_get_payment_methods_images()
{
    $data = db_get_array("SELECT ?:payments.payment_id, ?:payment_descriptions.payment FROM ?:payments INNER JOIN ?:images_links ON CAST(?:payments.payment_id AS CHAR) = ?:images_links.object_id AND ?:images_links.object_type = 'payment' LEFT JOIN ?:payment_descriptions ON ?:payments.payment_id = ?:payment_descriptions.payment_id AND ?:payment_descriptions.lang_code = ?s WHERE ?:payments.status = 'A' ORDER BY ?:payments.position, ?:payment_descriptions.payment", (string) GlobalState::interfaceLocale());

    if (empty($data)) {
        return array ();
    }

    $images = array ();

    foreach ($data as $key => $entry) {
        $image = fn_get_image_pairs($entry['payment_id'], 'payment', 'M');

        if (!empty($image['icon'])) {
            $image['icon']['alt'] = empty($image['icon']['alt']) ? $entry['payment'] : $image['icon']['alt'];
            $images[] = $image['icon'];
        }
    }

    return $images;
}

//
// Helper function: trims trailing and leading spaces
//
function fn_trim_helper(&$value)
{
    $value = is_string($value) ? trim($value) : $value;
}

/**
 * Sort array by key
 * @param array $array Array to be sorted
 * @param string $key Array key to sort by
 * @param int $order Sort order (SORT_ASC/SORT_DESC)
 * @return array Sorted array
 */
function fn_sort_array_by_key($array, $key, $order = SORT_ASC)
{
    $result = array();
    $values = array();
    foreach ($array as $id => $value) {
        $values[$id] = isset($value[$key]) ? $value[$key] : '';
    }

    if ($order == SORT_ASC) {
        asort($values);
    } else {
        arsort($values);
    }

    foreach ($values as $key => $value) {
        $result[$key] = $array[$key];
    }

    return $result;
}

/**
* Explode string by delimiter and trim values
*
* @param string $delim - delimiter to explode by
* @param string $string - string to explode
* @return array
*/
function fn_explode($delim, $string)
{
    $a = explode($delim, $string);
    array_walk($a, 'fn_trim_helper');

    return $a;
}

/**
* Formats date using current language
*
* @param int $timestamp - timestamp of the date to format
* @param string $format - format string (see strftim)
* @return string formatted date
*/
function fn_date_format($timestamp, $format = '%b %e, %Y')
{
    if (substr(PHP_OS,0,3) == 'WIN') {
        $hours = strftime('%I', $timestamp);
        $short_hours = ($hours < 10) ? substr($hours, -1) : $hours;
        $_win_from = array ('%e', '%T', '%D', '%l');
        $_win_to = array ('%d', '%H:%M:%S', '%m/%d/%y', $short_hours);
        $format = str_replace($_win_from, $_win_to, $format);
    }

    $date = getdate($timestamp);
    $m = $date['mon'];
    $d = $date['mday'];
    $y = $date['year'];
    $w = $date['wday'];
    $hr = $date['hours'];
    $pm = ($hr >= 12);
    $ir = ($pm) ? ($hr - 12) : $hr;
    $dy = $date['yday'];
    $fd = getdate(mktime(0, 0, 0, 1, 1, $y)); // first day of year
    $wn = (int) (($dy + $fd['wday']) / 7);
    if ($ir == 0) {
        $ir = 12;
    }
    $min = $date['minutes'];
    $sec = $date['seconds'];

    // Preload language variables if needed
    $preload = array();
    if (strpos($format, '%a') !== false) {
        $preload[] = 'weekday_abr_' . $w;
    }
    if (strpos($format, '%A') !== false) {
        $preload[] = 'weekday_' . $w;
    }

    if (strpos($format, '%b') !== false) {
        $preload[] = 'month_name_abr_' . $m;
    }

    if (strpos($format, '%B') !== false) {
        $preload[] = 'month_name_' . $m;
    }

    fn_preload_lang_vars($preload);

    $s['%a'] = __('weekday_abr_'. $w); // abbreviated weekday name
    $s['%A'] = __('weekday_'. $w); // full weekday name
    $s['%b'] = __('month_name_abr_' . $m); // abbreviated month name
    $s['%B'] = __('month_name_' . $m); // full month name
    $s['%c'] = ''; // !!!FIXME: preferred date and time representation for the current locale
    $s['%C'] = 1 + floor($y / 100); // the century number
    $s['%d'] = ($d < 10) ? ('0' . $d) : $d; // the day of the month (range 01 to 31)
    $s['%e'] = $d; // the day of the month (range 1 to 31)
    $s['%ð'] = $s['%b'];
    $s['%H'] = ($hr < 10) ? ('0' . $hr) : $hr; // hour, range 00 to 23 (24h format)
    $s['%I'] = ($ir < 10) ? ('0' . $ir) : $ir; // hour, range 01 to 12 (12h format)
    $s['%j'] = ($dy < 100) ? (($dy < 10) ? ('00' . $dy) : ('0' . $dy)) : $dy; // day of the year (range 001 to 366)
    $s['%k'] = $hr; // hour, range 0 to 23 (24h format)
    $s['%l'] = $ir; // hour, range 1 to 12 (12h format)
    $s['%m'] = ($m < 10) ? ('0' . $m) : $m; // month, range 01 to 12
    $s['%M'] = ($min < 10) ? ('0' . $min) : $min; // minute, range 00 to 59
    $s['%n'] = "\n"; // a newline character
    $s['%p'] = $pm ? 'PM' : 'AM';
    $s['%P'] = $pm ? 'pm' : 'am';
    $s['%s'] = floor($timestamp / 1000);
    $s['%S'] = ($sec < 10) ? ('0' . $sec) : $sec; // seconds, range 00 to 59
    $s['%t'] = "\t"; // a tab character
    $s['%T'] = $s['%H'] .':'. $s['%M'] .':'. $s['%S'];
    $s['%U'] = $s['%W'] = $s['%V'] = ($wn < 10) ? ('0' . $wn) : $wn;
    $s['%u'] = $w + 1;  // the day of the week (range 1 to 7, 1 = MON)
    $s['%w'] = $w; // the day of the week (range 0 to 6, 0 = SUN)
    $s['%y'] = substr($y, 2, 2); // year without the century (range 00 to 99)
    $s['%Y'] = $y; // year with the century
    $s['%%'] = '%'; // a literal '%' character
    $s['%D'] = $s['%m'] .'/'. $s['%d'] .'/'. $s['%y']; // american date style: %m/%d/%y
    // FIXME: %x : preferred date representation for the current locale without the time
    // FIXME: %X : preferred time representation for the current locale without the date
    // FIXME: %G, %g (man strftime)
    // FIXME: %r : the time in am/pm notation %I:%M:%S %p
    // FIXME: %R : the time in 24-hour notation %H:%M
    return preg_replace_callback("/(%.)/", function($m) use ($s) {
        if (isset($s[$m[1]])) {
            return $s[$m[1]];
        } else {
            return false;
        }
    }, $format);
}

/**
 * Return cleaned text string (for meta description use)
 *
 * @param string $html - html code to generate description from
 * @param int $max_letters - maximum letters in description
 * @return string - cleaned text
 */
function fn_generate_meta_description($html, $max_letters = 250)
{
    $meta = array();
    if (!empty($html)) {
        $html = str_replace('&nbsp;', ' ', $html);
        $html = str_replace(array("\r\n", "\n", "\r"), ' ', html_entity_decode(trim($html), ENT_QUOTES, 'UTF-8'));
        $html = preg_replace('/\<br(\s*)?\/?\>/i', " ", $html);
        $html = preg_replace('|<style[^>]*>.*?</style>|i', '', $html);
        $html = strip_tags($html);
        $html = str_replace(array('.', ',', ':', ';', '`', '"', '~', '\'', '(', ')'), ' ', $html);
        $html = preg_replace('/\s+/', ' ', $html);
        $html = explode(' ', $html);
        $letters_count = 0;
        foreach ($html as $k => $v) {
            $letters_count += fn_strlen($v);
            if ($letters_count <= $max_letters) {
                $meta[] = $v;
            } else {
                break;
            }
        }
    }

    return implode(' ', $meta);
}

/**
 * Calculate unsigned crc32 sum
 *
 * @param string $key - key to calculate sum for
 * @return int - crc32 sum
 */
function fn_crc32($key)
{
    return sprintf('%u', crc32($key));
}

/**
 * Check whether string is UTF-8 encoded
 *
 * @param string $str
 * @return boolean
 */
function fn_is_utf8($str)
{
    $c = 0; $b = 0;
    $bits = 0;
    $len = strlen($str);
    for ($i = 0; $i < $len; $i++) {
        $c = ord($str[$i]);
        if ($c > 128) {
            if (($c >= 254)) {
                return false;
            } elseif ($c >= 252) {
                $bits = 6;
            } elseif ($c >= 248) {
                $bits = 5;
            } elseif ($c >= 240) {
                $bits = 4;
            } elseif ($c >= 224) {
                $bits = 3;
            } elseif ($c >= 192) {
                $bits = 2;
            } else {
                return false;
            }

            if (($i + $bits) > $len) {
                return false;
            }

            while ($bits > 1) {
                $i++;
                $b = ord($str[$i]);
                if ($b < 128 || $b > 191) {
                    return false;
                }
                $bits--;
            }
        }
    }

    return true;
}

/**
 * Detect the cyrillic encoding of string
 *
 * @param string $str
 * @return string cyrillic encoding
 */
function fn_detect_cyrillic_charset($str)
{
    fn_define('LOWERCASE', 3);
    fn_define('UPPERCASE', 1);

    $charsets = array(
        'KOI8-R' => 0,
        'CP1251' => 0,
        'CP866' => 0,
        'ISO-8859-5' => 0,
        'MAC-CYRILLIC' => 0
    );

    for ($i = 0, $length = strlen($str); $i < $length; $i++) {
        $char = ord($str[$i]);
        //non-russian characters
        if ($char < 128 || $char > 256) {
            continue;
        }

        //CP866
        if (($char > 159 && $char < 176) || ($char > 223 && $char < 242)) {
            $charsets['CP866'] += LOWERCASE;
        }

        if (($char > 127 && $char < 160)) {
            $charsets['CP866'] += UPPERCASE;
        }

        //KOI8-R
        if (($char > 191 && $char < 223)) {
            $charsets['KOI8-R'] += LOWERCASE;
        }
        if (($char > 222 && $char < 256)) {
            $charsets['KOI8-R'] += UPPERCASE;
        }

        //CP1251
        if ($char > 223 && $char < 256) {
            $charsets['CP1251'] += LOWERCASE;
        }
        if ($char > 191 && $char < 224) {
            $charsets['CP1251'] += UPPERCASE;
        }

        //MAC-CYRILLIC
        if ($char > 221 && $char < 255) {
            $charsets['MAC-CYRILLIC'] += LOWERCASE;
        }
        if ($char > 127 && $char < 160) {
            $charsets['MAC-CYRILLIC'] += UPPERCASE;
        }

        //ISO-8859-5
        if ($char > 207 && $char < 240) {
            $charsets['ISO-8859-5'] += LOWERCASE;
        }
        if ($char > 175 && $char < 208) {
            $charsets['ISO-8859-5'] += UPPERCASE;
        }
    }

    arsort($charsets);

    return current($charsets) > 0 ? key($charsets) : '';
}

/**
 * Detect encoding by language
 *
 * @param string $resource string or file path
 * @param string $resource_type 'S' (string) or 'F' (file)
 * @param string $lang_code language of the file characters
 * @return string  detected encoding
 */

function fn_detect_encoding($resource, $resource_type = 'S', $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $enc = '';
    $str = $resource;

    if ($resource_type == 'F') {
        $str = file_get_contents($resource);
        if ($str == false) {
            return $enc;
        }
    }

    if (!fn_is_utf8($str)) {
        if (in_array($lang_code, array('en', 'fr', 'es', 'it', 'nl', 'da', 'fi', 'sv', 'pt', 'nn', 'no'))) {
            $enc = 'ISO-8859-1';
        } elseif (in_array($lang_code, array('hu', 'cs', 'pl', 'bg', 'ro'))) {
            $enc = 'ISO-8859-2';
        } elseif (in_array($lang_code, array('et', 'lv', 'lt'))) {
            $enc = 'ISO-8859-4';
        } elseif ($lang_code == 'ru') {
            $enc = fn_detect_cyrillic_charset($str);
        } elseif ($lang_code == 'ar') {
            $enc = 'ISO-8859-6';
        } elseif ($lang_code == 'el') {
            $enc = 'ISO-8859-7';
        } elseif ($lang_code == 'he') {
            $enc = 'ISO-8859-8';
        } elseif ($lang_code == 'tr') {
            $enc = 'ISO-8859-9';
        }
    } else {
        $enc = 'UTF-8';
    }

    return $enc;
}

/**
 * Convert encoding of string or file
 *
 * @param string $from_enc  the encoding of the initial string/file
 * @param string $to_enc  the encoding of the result string/file
 * @param string $resource string or file path
 * @param string $resource_type 'S' (string) or 'F' (file)
 * @return string  string or file path
 */

function fn_convert_encoding($from_enc, $to_enc, $resource, $resource_type = 'S')
{
    if (empty($from_enc) || empty($to_enc) || ($resource_type == 'F' && empty($resource))) {
        return false;
    }

    if (strtoupper($from_enc) == strtoupper($to_enc)) {
        return $resource;
    }

    $str = $resource;
    if ($resource_type == 'F') {
        $str = file_get_contents($resource);
        if ($str == false) {
            return false;
        }
    }

    $_str = false;
    if (function_exists('iconv')) {
        if (strtoupper($to_enc) == 'UCS-2') {
            $to_enc = 'UCS-2BE';
        }
        $_str = iconv($from_enc, $to_enc . '//TRANSLIT', $str);
    } elseif (function_exists('mb_convert_encoding')) {
        $_str = mb_convert_encoding($str, $to_enc, $from_enc);
    }

    if ($resource_type == 'F') {
        if ($_str != false) {
            $f = fopen($resource, 'wb');
            if ($f) {
                fwrite($f, $_str);
                fclose($f);
                @chmod($resource, DEFAULT_FILE_PERMISSIONS);
            } else {
                $resource = false;
            }
        }

        return $resource;
    } else {
        return $_str;
    }
}

function fn_check_meta_redirect($url)
{
    if (empty($url)) {
        return false;
    }

    if (strpos($url, '://') && !(strpos($url, Registry::get('config.http_location')) === 0 || strpos($url, Registry::get('config.https_location')) === 0)) {
        return false;
    } else {
        return $url;
    }
}

function fn_get_notification_rules($params, $disable_notification = false)
{
    $force_notification = array();
    if ($disable_notification) {
        $force_notification = array('C' => false, 'A' => false, 'V' => false);
    } else {
        if (!empty($params['notify_user']) || array_key_exists('notify_customer', $params) || $params === true) {
            $force_notification['C'] = true;
        } else {
            if (AREA == 'A') {
                $force_notification['C'] = false;
            }
        }
        if (!empty($params['notify_department']) || $params === true) {
            $force_notification['A'] = true;
        } else {
            if (AREA == 'A') {
                $force_notification['A'] = false;
            }
        }
        if (!empty($params['notify_vendor']) || $params === true) {
            $force_notification['V'] = true;
        } else {
            if (AREA == 'A') {
                $force_notification['V'] = false;
            }
        }
    }

    return $force_notification;
}

/**
 * substr() with full UTF-8 support
 *
 * @param string $string The input string.
 * @param integer $start If start  is non-negative, the returned string will start at the start 'th position in string , counting from zero. If start is negative, the returned string will start at the start 'th character from the end of string.
 * @param integer $length  If length  is given and is positive, the string returned will contain at most length  characters beginning from start  (depending on the length of string ). If length is given and is negative, then that many characters will be omitted from the end of string (after the start position has been calculated when a start is negative). If start denotes a position beyond this truncation, an empty string will be returned.
 * @param integer $encoding The encoding parameter is the character encoding. If it is omitted, UTF-8 character encoding value will be used.
 * @return mixed Returns the extracted part of string or false if string is less than or equal to start characters long
 */
function fn_substr($string, $start, $length = null, $encoding = 'UTF-8')
{
    if (empty($encoding)) {
        $encoding = 'UTF-8';
    }

    if ($length === null) {
        return fn_substr($string, $start, fn_strlen($string, $encoding), $encoding);
    }

    if (function_exists('iconv_substr')) {
        // there was strange bug in iconv_substr when use negative length parameter
        // so we recalculate start and length here
        if ($length < 0) {
            $length = ceil($length);
            $len = iconv_strlen($string, $encoding);
            if ($start < 0) {
                $start += $len;
            }
            $length += $len - $start;
        }

        return iconv_substr($string, $start, $length, $encoding);
    } elseif (function_exists('mb_substr')) {
        return mb_substr($string, $start, $length, $encoding);
    } else {
        preg_match_all('/./su', $string, $ar);

        return join('', array_slice($ar[0], $start, $length));
    }
}

/**
 * strlen() with full UTF-8 support
 *
 * @param string $string The string being measured for length.
 * @param string $encoding The encoding parameter is the character encoding. If it is omitted, UTF-8 character encoding value will be used.
 * @return integer The length of the string on success, and 0 if the string is empty.
 */
function fn_strlen($string, $encoding = 'UTF-8')
{
    if (empty($encoding)) {
        $encoding = 'UTF-8';
    }

    if (function_exists('iconv_strlen')) {
        return @iconv_strlen($string, $encoding);
    } elseif (function_exists('mb_strlen')) {
        return mb_strlen($string, $encoding);
    } else {
        preg_match_all('/./su', $string, $ar);

        return count($ar[0]);
    }
}

/**
 * Converts URN to URI
 *
 * @param string $url URN (Uniform Resource Name or Query String)
 * @param string $area Area
 * @param string $protocol Output URL protocol (protocol://). If equals 'rel', no protocol will be included
 * @param string $lang_code 2 letters language code
 * @param bool $override_area
 * @return string URI
 */
function fn_url($url = '', $area = AREA, $protocol = 'current', $lang_code = null, $override_area = false)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    static $init_vars = false;
    static $admin_index, $_admin_index, $vendor_index, $customer_index, $http_location, $https_location, $current_location;

    $vendor_index = Registry::get('config.vendor_index');
    $_admin_index = Registry::get('config.admin_index');
    $customer_index = Registry::get('config.customer_index');
    $http_location = Registry::get('config.http_location');
    $https_location = Registry::get('config.https_location');
    $current_location = Registry::get('config.current_location');
    $https_path = Registry::get('config.https_path');
    $http_path = Registry::get('config.http_path');
    $current_path = Registry::get('config.current_path');

    $admin_index_area = $override_area ? 'A' : '';
    $admin_index = fn_get_index_script($admin_index_area);

    if ($area != 'A' && $area != 'C') {
        $prev_admin_index = $admin_index;
        $admin_index = fn_get_index_script($area);
        $area = 'A';
    }

    $url = str_replace('&amp;', '&', $url);
    $parced_url = parse_url($url);
    $no_shorted = false;
    $full_query = false;

    if (!empty($parced_url['scheme']) || !empty($parced_url['host'])) {

        if (!empty($parced_url['scheme'])) { // do not prefix URL is its absolute already
            $protocol = 'no_prefix';
        }

        $no_shorted = true;
    } else {
        if (!empty($parced_url['path'])) {
            if (stripos($parced_url['path'], $_admin_index) !== false) {
                $area = 'A';
                $no_shorted = true;
            } elseif (stripos($parced_url['path'], $customer_index) !== false) {
                $area = 'C';
                $no_shorted = true;
            } elseif (!empty($vendor_index) && stripos($parced_url['path'], $vendor_index) !== false) {
                $area = 'A';
                $no_shorted = true;
            }
        } else {
            if (strpos($url, '?') === 0) {
                $full_query = true;
            } else {
                $no_shorted = true;
                $url = $_url = ($area == 'C') ? $customer_index : $admin_index;
            }
        }
    }

    $index_script = ($area == 'C') ? $customer_index : $admin_index;

    $_url = '';
    if ($no_shorted) {
        // full url passed
        $_url = $url;
    } elseif ($full_query) {
        // full query passed
        $_url = $index_script . $url;
    } else {
        $_url = $index_script . '?dispatch=' . str_replace('?', '&', $url);
    }

    $concatUrl = function ($location, $path, $url) {
        //remove $path from $url
        if (strpos($url, $path.'/') === 0) {
            $url = substr($url, strlen($path)+1);
        }
        return $location.'/'.$url;
    };
    if ($protocol != 'rel') {
        if ($protocol == 'http') {
            $_url = $concatUrl($http_location, $http_path, $_url);
        } elseif ($protocol == 'https') {
            $_url = $concatUrl($https_location, $https_path, $_url);
        } elseif ($protocol == 'current') {
            $_url = $concatUrl($current_location, $current_path, $_url);
        }

    }

    $company_id_in_url = fn_get_company_id_from_uri($url);

    fn_seo_url_post($_url, $area, $url, $protocol, $company_id_in_url, $lang_code);
    if (!empty($prev_admin_index)) {
        $admin_index = $prev_admin_index;
    }

    return $_url;
}

/**
 * Returns company_id if it is present in $uri, otherwise false
 *
 * @param string $uri URI | URN
 * @return int|bool company_id if it is present in $uri, otherwise false
 */
function fn_get_company_id_from_uri($uri)
{
    $company_id = false;

    if (preg_match("%(\?|&|&amp;)company_id=(\d+)%", $uri, $match)) {
        if (!empty($match[2])) {
            $company_id = $match[2];
        }
    }

    return $company_id;
}

/**
 * Checks can user get access to the some area or not.
 *
 * @param $user_data Array with user data
 * @param $account_type string First char of account type (Customer, Vendor, Admin)
 * @return bool True, if user can access area, defined in the const ACCOUNT_TYPE, false otherwise
 */
function fn_check_user_type_access_rules($user_data, $account_type = ACCOUNT_TYPE)
{
    $rules = array(
        'A' => array('admin', 'customer'),
        'V' => array('vendor', 'customer'),
        'C' => array('customer'),
    );
    return !empty($user_data['user_type']) && in_array($account_type, $rules[$user_data['user_type']]);
}

/**
 * Check for non empty string
 *
 * @param string $str string
 * @return boolean string is not empty?
 */
function fn_string_not_empty($str)
{
    return (strlen((trim($str)))>0) ? true : false;
}

/**
 * Check for number
 *
 * @param string $num number
 * @return boolean string is number?
 */
function fn_is_numeric($num)
{
    return is_numeric(trim($num));
}

/**
 * @Fancy recursive function to search for substring in an array
 * @param string $neele
 * @param mixed $haystack
 * @return bool
 * <AUTHOR>
 */
function fn_substr_in_array($what_str, $where_arr)
{
    foreach ($where_arr as $v) {
        if (is_array($v)) {
            $sub_arr = fn_substr_in_array($what_str, $v);
            if ($sub_arr) {
                return true;
            }
        } else {
            if (strpos($v, $what_str) !== false) {
                return true;
            }
        }
    }

    return false;
}

function fn_return_bytes($val)
{
    $last = fn_strtolower($val{strlen($val)-1});

    switch ($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
        break;
    }

    return $val;
}

/**
 * Funtion formats user-entered price into float.
 *
 * @param string $price
 * @param string $currency
 * @return float Well-formatted price.
 */
function fn_parse_price($price, $currency = CART_PRIMARY_CURRENCY)
{
    $decimals = Registry::get('currencies.' . $currency . '.decimals');
    $dec_sep = Registry::get('currencies.' . $currency . '.decimals_separator');
    $thous_sep = Registry::get('currencies.' . $currency . '.thousands_separator');

    if ($dec_sep == $thous_sep) {
        if (($last = strrpos($price, $dec_sep)) !== false) {
            if ($thous_sep == '.') {
                $price = str_replace('.', ',', $price);
            }
            $price = substr_replace($price, '.', $last, 1);
        }
    } else {
        if ($thous_sep == '.') {
            // is it really thousands separator?
            // if there is decimals separator, than we can replace ths_sep
            if (strpos($price, $dec_sep) !== false) {
                $price = str_replace($thous_sep, '', $price);
            } else {
                //if there are 3 digits rigth of the separator - it is ths_sep too.
                if (($last = strrpos($price, '.')) !== false) {
                    $last_part = substr($price, $last);
                    $last_part = preg_replace('/[^\d]/', '', $last_part);
                    if (strlen($last_part) == 3 && $decimals != 3) {
                        $price = str_replace($thous_sep, '', $price);
                    }
                }
            }
        }

        if ($dec_sep != '.') {
            $price = str_replace($dec_sep, '.', $price);
        }
    }

    $price = preg_replace('/[^\d\.]/', '', $price);

    return round(floatval($price), $decimals);
}

/**
 * Function returns the index script for requested data.
 *
 * @param mixed $for. If array is given, then index script will be returned for $for['user_type'].
 * If $for is empty, index script will be returned for defined ACCOUNT_TYPE
 * The following string are allowed: 'A', 'admin', 'V', 'vendor', 'C', 'customer'
 * @return string Path to index script
 */
function fn_get_index_script($for = '')
{
    if (is_array($for)) {
        $for = !empty($for['user_type']) ? $for['user_type'] : '';
    }

    if (empty($for) || !in_array($for, array('A', 'admin', 'V', 'vendor', 'C', 'customer'))) {
        $for = ACCOUNT_TYPE;
    } elseif ($for == 'A') {
        $for = 'admin';
    } elseif ($for == 'V') {
        $for = 'vendor';
    } elseif ($for == 'C') {
        $for = 'customer';
    }

    return Registry::get("config.{$for}_index");
}

/**
 * Updates statuses
 * @param string $status One letter status code that should be updated
 * @param array $status_data Status information
 * @param string $type One letter status type
 * @param string $lang_code Language code
 * @return array Updated status data
 */
function fn_update_status($status, $status_data, $type, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    if (empty($status_data['status'])) {
        // Generate new status code
        $existing_codes = db_get_fields('SELECT status FROM ?:statuses WHERE type = ?s GROUP BY status', $type);
        $existing_codes[] = 'N'; // STATUS_INCOMPLETED_ORDER
        $existing_codes[] = 'T'; // STATUS_PARENT_ORDER
        $codes = array_diff(range('A', 'Z'), $existing_codes);
        $status_data['status'] = reset($codes);
    }

    $can_continue = true;

    if ($can_continue) {
        if (empty($status)) {
            $status_data['type'] = $type;
            db_query("INSERT INTO ?:statuses ?e", $status_data);
            $status = $status_data['status'];

            foreach (Languages::getAll() as $status_data['lang_code'] => $_v) {
                db_query('REPLACE INTO ?:status_descriptions ?e', $status_data);
            }
        } else {
            db_query("UPDATE ?:statuses SET ?u WHERE status = ?s AND type = ?s", $status_data, $status, $type);
            db_query('UPDATE ?:status_descriptions SET ?u WHERE status = ?s AND type = ?s AND lang_code = ?s', $status_data, $status, $type, $lang_code);
        }

        if (!empty($status_data['params'])) {
            foreach ((array) $status_data['params'] as $param_name => $param_value) {
                $_data = array(
                    'status' => $status,
                    'type' => $type,
                    'param' => $param_name,
                    'value' => $param_value
                );
                db_query("REPLACE INTO ?:status_data ?e", $_data);
            }
        }
    }

    return $status_data['status'];
}

/**
 * Get simple statuses description (P - Processed, O - Open)
 * @param string $type One letter status type
 * @param boolean $additional_statuses Flag that determines whether additional (hidden) statuses should be retrieved
 * @param boolean $exclude_parent Flag that determines whether parent statuses should be excluded
 * @param string $lang_code Language code
 * @return array Statuses
 */
function fn_get_simple_statuses($type = STATUSES_ORDER, $additional_statuses = false, $exclude_parent = false, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $statuses = db_get_hash_single_array(
        "SELECT a.status, b.description"
        . " FROM ?:statuses as a"
        . " LEFT JOIN ?:status_descriptions as b ON b.status = a.status AND b.type = a.type AND b.lang_code = ?s"
        . " WHERE a.type = ?s",
        array('status', 'description'),
        $lang_code, $type
    );
    if ($type == STATUSES_ORDER && !empty($additional_statuses)) {
        $statuses['N'] = __('incompleted', '', $lang_code);
        if (empty($exclude_parent)) {
            $statuses[STATUS_PARENT_ORDER] = __('parent_order', '', $lang_code);
        }
    }

    return $statuses;
}

/**
 * Gets full information about particular statuses
 * @param string $type One letter status type
 * @param array $status_to_select Array of statuses that should be retrieved. If empty, all statuses will be retrieved
 * @param boolean $additional_statuses Flag that determines whether additional (hidden) statuses should be retrieved
 * @param boolean $exclude_parent Flag that determines whether parent statuses should be excluded
 * @param string $lang_code Language code
 * @param int $company_id Company identifier
 * @return array Statuses
 */
function fn_get_statuses($type = STATUSES_ORDER, $status_to_select = array(), $additional_statuses = false, $exclude_parent = false, $lang_code = null, $company_id = 0)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $join = db_quote(" LEFT JOIN ?:status_descriptions ON ?:status_descriptions.status = ?:statuses.status AND ?:status_descriptions.type = ?:statuses.type AND ?:status_descriptions.lang_code = ?s", $lang_code);
    $condition = db_quote(" AND ?:statuses.type = ?s", $type);
    $condition .= !empty($status_to_select) ? db_quote(" AND ?:statuses.status IN (?a)", $status_to_select) : '';

    $statuses = db_get_hash_array(
        "SELECT ?:statuses.*, ?:status_descriptions.*"
        . " FROM ?:statuses"
        . $join
        . " WHERE 1 $condition",
        'status'
    );

    $statuses_params = db_get_hash_multi_array("SELECT param, value, status FROM ?:status_data WHERE type = ?s", array('status', 'param'), $type);
    foreach ($statuses as $status => $status_data) {
        $statuses[$status]['params'] = array();
        if (isset($statuses_params[$status])) {
            foreach ($statuses_params[$status] as $param_name => $param_data) {
                $statuses[$status]['params'][$param_name] = $param_data['value'];
            }
        }
    }

    if ($type == STATUSES_ORDER && $additional_statuses && empty($status_to_select)) {
        $statuses[STATUS_INCOMPLETED_ORDER] = array (
            'status' => STATUS_INCOMPLETED_ORDER,
            'description' => __('incompleted', '', $lang_code),
            'type' => STATUSES_ORDER,
            'params' => array(
                'inventory' => 'I',
            ),
        );
        if (empty($exclude_parent)) {
            $statuses[STATUS_PARENT_ORDER] = array (
                'status' => STATUS_PARENT_ORDER,
                'description' => __('parent_order', '', $lang_code),
                'type' => STATUSES_ORDER,
                'params' => array(
                    'inventory' => 'I',
                ),
            );
        }
    }

    return $statuses;
}

/**
 * Gets full information about a particular status
 * @param string $status One letter status code
 * @param string $type One letter status type
 * @param int $object_id Recurring billing: ID of an object to be checked for subscriptions
 * @param string $lang_code Language code
 * @param int $company_id Company identifier
 * @return array Status data
 */
function fn_get_status_data($status, $type, $object_id = 0, $lang_code = null, $company_id = 0)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    if (empty($status) || empty($type)) {
        return array();
    }

    $status_data = fn_get_statuses($type, !is_array($status) ? (array) $status : $status, false, false, $lang_code, $company_id);

    return reset($status_data);
}

/**
 * Gets full information about a particular status by identifier
 * @param int $status_id Status identifier
 * @param string $lang_code Language code
 * @return array Status idata
 */
function fn_get_status_by_id($status_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $status_data = array();

    $status = db_get_row("SELECT status, type FROM ?:statuses WHERE status_id = ?i", $status_id);
    if (!empty($status)) {
        $status_data = fn_get_status_data($status['status'], $status['type'], 0, $lang_code);
    }

    return $status_data;
}

/**
 * Deletes status
 * @param string $status One letter status code
 * @param string $type One letter status type
 * @return boolean True or false depending on whether the status is removed
 */
function fn_delete_status($status, $type)
{
    $can_delete = db_get_field("SELECT status FROM ?:statuses WHERE status = ?s AND type = ?s AND is_default = 'N'", $status, $type);

    if (!empty($can_delete)) {
        db_query('DELETE FROM ?:statuses WHERE status = ?s AND type = ?s', $status, $type);
        db_query('DELETE FROM ?:status_descriptions WHERE status = ?s AND type = ?s', $status, $type);
        db_query('DELETE FROM ?:status_data WHERE status = ?s AND type = ?s', $status, $type);
    }

    return (!empty($can_delete)) ? true : false;
}

function fn_tools_update_status($params)
{
    if ($params['table'] == 'product_features' &&
        in_array($params['id'], container()->getParameter('readonly_attributes'))) {

        fn_set_notification('E', __('error'), __('cannot_update_delete_protected_attribute'));
        exit;
    }

    if (!preg_match("/^[a-z_]+$/", $params['table'])) {
        return false;
    }

    $old_status = db_get_field("SELECT status FROM ?:$params[table] WHERE ?w", array($params['id_name'] => $params['id']));

    $permission = true;
    if (Registry::get('runtime.company_id')) {
        $cols = db_get_fields("SHOW COLUMNS FROM ?:$params[table]");
        if (in_array('company_id', $cols)) {
            $condition = fn_get_company_condition('?:' . $params['table'] . '.company_id');
            $permission = db_get_field("SELECT company_id FROM ?:$params[table] WHERE ?w $condition", array($params['id_name'] => $params['id']));
        }
    }
    if (empty($permission)) {
        fn_set_notification('W',  __('warning'), __('access_denied'));

        if (defined('AJAX_REQUEST')) {
            Registry::get('ajax')->assign('return_status', $old_status);
        }

        return false;
    }

    // In case of product status modification: we check if the product can be in the desired status (because of shipping conditions, etc)
    if ($params['table'] === 'products' && $product_data = fn_get_product_data($params['id'], $auth = null)) {
        $product_data['status'] = $params['status'];
        if (!\Wizacha\Product::checkShippingsBeforeUpdate($product_data, $params['id'], Registry::get('runtime.company_id'))) {
            // notification already done by checkShippingsBeforeUpdate
            Registry::get('ajax')->assign('return_status', $old_status);

            return false;
        }
    }

    $visibleToHiddenOrDisabled = $old_status == \Wizacha\Status::ENABLED && in_array($params['status'], [
        \Wizacha\Status::HIDDEN,
        \Wizacha\Status::DISABLED,
    ]);
    $hiddenOrDisabledToVisible = $params['status'] == \Wizacha\Status::ENABLED && in_array($old_status, [
        \Wizacha\Status::HIDDEN,
        \Wizacha\Status::DISABLED,
    ]);

    // Attributes cannot be hidden
    if ($params['table'] == 'product_features' && !in_array($params['status'], [\Wizacha\Status::ENABLED, \Wizacha\Status::DISABLED])) {
        fn_set_notification('E', __('error'), __('error'));

        exit;
    }

    $result = db_query("UPDATE ?:$params[table] SET status = ?s WHERE ?w", $params['status'], array($params['id_name'] => $params['id']));
    //update search engine
    switch($params['table']) {
        case 'products':
            if (!empty($product_data) && ($visibleToHiddenOrDisabled || $hiddenOrDisabledToVisible)) {
                container()->get('marketplace.pim.category_service')->updateProductCount($product_data['category_ids']);
            }

            \Wizacha\Events\Config::dispatch(
                \Wizacha\Product::EVENT_UPDATE,
                (new \Wizacha\Events\IterableEvent)->setElement($params['id'])
            );
            break;
        case 'categories':
            if ($visibleToHiddenOrDisabled || $hiddenOrDisabledToVisible) {
                container()->get('marketplace.pim.category_service')->updateProductCount([$params['id']]);
            }

            \Wizacha\Events\Config::dispatch(
                \Wizacha\Category::EVENT_UPDATE,
                (new \Wizacha\Events\IterableEvent)->setElement($params['id'])
            );
            break;
        case 'product_features':
            fn_set_notification('N', __('notice'), __('text_if_attribute_has_been_updated'));

            \Wizacha\Events\Config::dispatch(
                \Wizacha\Marketplace\PIM\Attribute\AttributeService::EVENT_UPDATE,
                (new \Wizacha\Events\IterableEvent)->setElement($params['id'])
            );
            break;
        case 'discussion_posts':
            $threadPost = new \Wizacha\Marketplace\Entities\ThreadPost($params['id']);
            $discussionData = db_get_row('SELECT * FROM cscart_discussion WHERE thread_id = ?i', $threadPost->getThreadId());
            $normalizedObjectType = \strtoupper($discussionData['object_type']);

            if ($normalizedObjectType === \Wizacha\Marketplace\Review\ReviewTarget::PRODUCT) {
                if (\Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct::isMultiVendorProductId($discussionData['object_id'])) {
                    try {
                        $mvp = container()->get('marketplace.multi_vendor_product.service')->get($discussionData['object_id']);
                        $event = new \Wizacha\Marketplace\PIM\MultiVendorProduct\Event\MultiVendorProductEvent($mvp);
                        \Wizacha\Events\Config::dispatch(
                            \Wizacha\Marketplace\PIM\MultiVendorProduct\Event\MultiVendorProductEvent::UPDATED,
                            $event
                        );
                    } catch (\Wizacha\Marketplace\Exception\NotFound $e) {

                    }
                } else {
                    \Wizacha\Events\Config::dispatch(
                        \Wizacha\Product::EVENT_UPDATE,
                        (new \Wizacha\Events\IterableEvent)->setElement($discussionData['object_id'])
                    );
                }
            } elseif ($normalizedObjectType === \Wizacha\Marketplace\Review\ReviewTarget::COMPANY) {
                \Wizacha\Events\Config::dispatch(
                    CompanyEvents::UPDATED,
                    IterableEvent::fromElement($discussionData['object_id'])
                );
            }
            break;
        case 'languages':
            $lang_code = db_get_field('SELECT lang_code FROM ?:languages WHERE lang_id = ?i', $params['id']);

            if ($old_status === Languages::ACTIVE) {
                container()
                    ->get('marketplace.catalog')
                    ->cleanup();
            }

            if ($params['status'] === Languages::ACTIVE) {
                container()
                    ->get('marketplace.catalog')
                    ->rebuild();
            }
            break;

        case 'taxes':
            // updates product's readModels
            $productIds = db_get_fields('SELECT product_id FROM cscart_products WHERE tax_ids REGEXP ?s', '(,|^)'.$params['id'].'(,|$)');
            \Wizacha\Events\Config::dispatch(
                \Wizacha\Product::EVENT_UPDATE,
                (new \Wizacha\Events\IterableEvent)->setArray($productIds)
            );
    }

    if ($result) {
        fn_set_notification('N', __('notice'), __('status_changed'));
    } else {
        fn_set_notification('E', __('error'), __('error_status_not_changed'));
        Registry::get('ajax')->assign('return_status', $old_status);
    }

    return true;
}

/**
 * Make a string lowercase
 *
 * @param string $string - the string being lowercased
 * @param string $charset - charset being used
 * @return string
 */
function fn_strtolower($string, $charset = CHARSET)
{
    return mb_strtolower($string, $charset);
}

/**
 * Checks and save languages integrity by enable
 * $default_lang language if all languages in cart disabled
 * and checks and changes appeareance settings if it are using hidden or disabled languages
 *
 * @param string $default_lang Two-letters language code, that will be set active, if there are no active languages.
 * @return bool Always true
 */

/**
 * Returns list of tables that has language depended data
 *
 * @return array Array of table names without prefix
 */
function fn_get_description_tables()
{
    $description_tables = db_get_fields("SHOW TABLES LIKE '?:%_descriptions'");
    $description_tables[] = 'language_values';
    $description_tables[] = 'product_features_values';
    $description_tables[] = 'bm_blocks_content';
    foreach ($description_tables as $key => $table) {
        $description_tables[$key] = str_replace(Registry::get('config.table_prefix'), '', $table);
    }
    return $description_tables;
}

/**
 * Delete installed payment
 *
 * @param int $payment_id Payment id to be deleted
 * @return bool True if payment was sucessfully deleted, false otherwise
 */
function fn_delete_payment($payment_id)
{
    $result = true;
    $payment_id = (int) $payment_id;

    if (empty($payment_id) || !fn_check_company_id('payments', 'payment_id', $payment_id)) {
        return false;
    }

    db_query("DELETE FROM ?:payments WHERE payment_id = ?i", $payment_id);
    db_query("DELETE FROM ?:payment_descriptions WHERE payment_id = ?i", $payment_id);

    fn_delete_image_pairs($payment_id, 'payment');

    return $result;
}

/**
 * Gets count of directory subdirectories
 *
 * @param string $path directory path
 * @return int number of subdirectories
 */
function fn_dirs_count($path)
{
    $dirscount = 0;

    if (empty($path) || !is_dir($path) || !($dir = opendir($path))) {
        return $dirscount;
    }

    while (($file = readdir($dir)) !== false) {
        if ($file == '.' || $file == '..') {
            continue;
        }

        if (is_dir($path . '/' . $file)) {
            $dirscount++;
            $dirscount += fn_dirs_count($path . '/' . $file);
        }
    }

    closedir($dir);

    return $dirscount;
}

/**
 * Gets all logos
 * @param int $company_id company ID
 * @param int $layout_id layout ID
 * @return array logos list
 */
function fn_get_logos($company_id = null)
{
    $company_condition = '';
    if (is_null($company_id) && Registry::get('runtime.company_id')) {
        $company_id = Registry::get('runtime.company_id');
    }

    if (!is_null($company_id)) {
        $company_condition = db_quote(' AND company_id = ?i', $company_id);
    }


    $layout_id = 1;
    $logos = db_get_hash_array("SELECT * FROM ?:logos WHERE IF(layout_id = 0, 1, IF(layout_id = ?i, 1, 0)) ?p", 'type', $layout_id, $company_condition);

    $logo_ids = array();
    foreach ($logos as $l) {
        $logo_ids[] = $l['logo_id'];
    }

    $images = fn_get_image_pairs($logo_ids, 'logos', 'M', true, false);

    foreach ($logos as $k => $v) {
        if (empty($images[$v['logo_id']])) {
            $logos[$k]['image'] = array();
            continue;
        }

        $image = reset($images[$v['logo_id']]);
        $logos[$k]['image'] = $image['icon'];

    }

    return $logos;
}


/**
 * Adds logo
 * @param array $logo_data logo data (layout_id, image path, type)
 * @param integer $company_id company ID
 * @return integer ID of created logo
 */
function fn_create_logo($logo_data, $company_id = null)
{
    $condition = '';
    if (!empty($logo_data['layout_id'])) {
        $condition .= db_quote(" AND layout_id = ?i", $logo_data['layout_id']);
    }

    if (!empty($company_id)) {
        $condition .= db_quote(" AND company_id = ?i", $company_id);
    }

    $logo_id = db_get_field("SELECT logo_id FROM ?:logos WHERE type = ?s ?p", $logo_data['type'], $condition);

    if (empty($logo_id)) {
        $logo_id = db_query("INSERT INTO ?:logos ?e", array(
            'type' => $logo_data['type'],
            'layout_id' => !empty($logo_data['layout_id']) ? $logo_data['layout_id'] : 0,
            'company_id' => $company_id
        ));
    }

    if (!empty($logo_data['image_path'])) {
        if (file_exists($logo_data['image_path'])) {
            $_REQUEST['logotypes_image_data'] = array(
                array(
                    'type' => 'M',
                    'object_id' => $logo_id
                )
            );
            $_REQUEST['type_logotypes_image_icon'] = array('server');
            $_REQUEST['file_logotypes_image_icon'] = array($logo_data['image_path']);

            fn_attach_image_pairs('logotypes', 'logos');
        }
    }

    return $logo_id;
}

/**
 * Deletes logo by type
 * @param string $type logo type
 * @param integer $company_id - ID of company to delete logo for
 * @return bool always true
 */
function fn_delete_logo($type, $company_id = null)
{
    $condition = '';
    if (!empty($company_id)) {
        $condition .= db_quote(" AND company_id = ?i", $company_id);
    }

    $logo_ids = db_get_fields("SELECT logo_id FROM ?:logos WHERE type = ?s ?p", $type, $condition);

    foreach ($logo_ids as $logo_id) {
        fn_delete_image_pairs($logo_id, 'logos');
    }

    db_query("DELETE FROM ?:logos WHERE logo_id IN (?n)", $logo_ids);

    return true;
}

/**
 * Gets list of logo types
 *
 * @param boolean $for_company - indicates that logo types should be retrieved for company, not for root
 * @return array list of logo types
 */
function fn_get_logo_types($for_company = false)
{
    $types = array(
        'theme' => array (
            'for_layout' => true,
            'text' => 'text_customer_area_logo',
        ),
        'favicon' => array(
            'for_layout' => true,
            'text' => ''
        ),
        'mail' => array (
            'text' => 'text_mail_area_logo'
        )
    );

    if ($for_company == true) {
        unset($types['favicon']);
        unset($types['theme']['for_layout']);
    }
    return $types;
}

/**
 * Gets area name by its type
 * @param string $area - area type
 * @return string area name
 */
function fn_get_area_name($area = AREA)
{
    return ($area == 'C') ? 'frontend' : 'backend';
}

/**
 * Add/remove html special chars
 *
 * @param mixed $data data to filter
 * @param bool $revert if true, decode special chars
 * @return mixed filtered variable
 */
function fn_html_escape($data, $revert = false)
{
    if (is_array($data)) {
        foreach ($data as $k => $sub) {
            if (is_string($k)) {
                $_k = ($revert == false) ? htmlspecialchars($k, ENT_QUOTES, 'UTF-8') : htmlspecialchars_decode($k, ENT_QUOTES);
                if ($k != $_k) {
                    unset($data[$k]);
                }
            } else {
                $_k = $k;
            }
            if (is_array($sub) === true) {
                $data[$_k] = fn_html_escape($sub, $revert);
            } elseif (is_string($sub)) {
                $data[$_k] = ($revert == false) ? htmlspecialchars($sub, ENT_QUOTES, 'UTF-8') : htmlspecialchars_decode($sub, ENT_QUOTES);
            }
        }
    } else {
        $data = ($revert == false) ? htmlspecialchars($data, ENT_QUOTES, 'UTF-8') : htmlspecialchars_decode($data, ENT_QUOTES);
    }

    return $data;
}

/**
 * Add slashes
 *
 * @param mixed $var variable to add slashes to
 * @param boolean $escape_nls if true, escape "new line" chars with extra slash
 * @return mixed filtered variable
 */
function fn_add_slashes(&$var, $escape_nls = false)
{
    if (!is_array($var)) {
        return ($var === null) ? null : (($escape_nls == true) ? str_replace("\n", "\\n", addslashes($var)) : addslashes($var));
    } else {
        $slashed = array();
        foreach ($var as $k => $v) {
            $sk = addslashes($k);
            if (!is_array($v)) {
                $sv = ($v === null) ? null : (($escape_nls == true) ? str_replace("\n", "\\n", addslashes($v)) : addslashes($v));
            } else {
                $sv = fn_add_slashes($v, $escape_nls);
            }
            $slashed[$sk] = $sv;
        }

        return ($slashed);
    }
}

/**
 * Gets and caches permissions schema
 * @param string $name schema name
 * @return array schema data
 */
function fn_get_permissions_schema($name)
{
    static $cache = array();

    if (empty($cache[$name])) {
        $cache[$name] = fn_get_schema('permissions', $name);
    }

    return $cache[$name];
}

/**
 * Convert multi-level array to single-level array
 *
 * @param array $item Multi-level array
 * @param string $delimiter Delimiter name
 * @return array Single-level array
 */
function fn_foreach_recursive($item, $delimiter, $name = '', $arr = array())
{
    if (is_array($item)) {
        foreach ($item as $key => $value) {
            $new_key = $name === '' ? $key : $name . $delimiter . $key;
            $arr = fn_foreach_recursive ($value, $delimiter, $new_key, $arr);
        }
    } else {
        $arr[$name] = $item;
    }

    return $arr;
}

/**
 * Translate language variable wrapper (for usage in templates and scripts)
 * @param string $var variable to translate
 * @param array $params placeholder replacements
 * @param string $lang_code language code to get variable for
 * @return string variable value
 */
function __($var, $params = array(), $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $translator = container()->get('translator');
    $params = is_array($params) ? $params : []; // 😭

    if (!empty($params) && is_array($params)) {

        reset($params);
        if (key($params) === 0) { // if first parameter has number key, we need to get plural form
            $number = array_shift($params);
            return $translator->trans($var, ['%count%' => $number] + $params, 'messages', $lang_code);
        }
    }

    return $translator->trans($var, $params, 'messages', $lang_code);
}

/**
 * Get product edition acronym
 *
 * @staticvar array $edition_acronyms Array with PRODUCT_EDITION => acronym
 * @param string $edition Edition name
 * @return string Edition acronym or false, if nothing was found.
 */
function fn_get_edition_acronym($edition)
{
    static $edition_acronyms = array(
        'PROFESSIONAL' => 'pro',
        'MULTIVENDOR'  => 'mve',
        'ULTIMATE'     => 'ult',
    );

    return !empty($edition_acronyms[$edition]) ? $edition_acronyms[$edition] : false;
}

/**
 * Parse the URN query part
 *
 * @param string $urn URN (Uniform Resource Name or Query String)
 * @return string parse query
 */
function fn_parse_urn($urn)
{
    $escaped = false;
    $path = '';
    if (($i = strpos($urn, '?')) !== false) { // full url with query string
        $qs = substr($urn, $i + 1);
        $path = str_replace('?' . $qs, '', $urn);
    } elseif (strpos($urn, '&') !== false || strpos($urn, '=') !== false) { // just query string
        $qs = $urn;
    } else { // no query string
        $qs = '';
        $path = $urn;
    }

    if (strpos($qs, '&amp;') !== false) {
        $escaped = true;
        $qs = str_replace('&amp;', '&', $qs);
    }

    parse_str($qs, $params);

    return array($path, $params, $escaped);
}

/**
 * Build the URN
 *
 * @param string $path
 * @param string $params
 * @param bool $escaped
 * @return string $urn URN (Uniform Resource Name or Query String)
 */
function fn_build_urn($path, $params, $escaped)
{
    $urn = $path;
    if (!empty($params)) {
        $res = http_build_query($params, '', ($escaped ? '&amp;' : '&'));
        $urn .= (!empty($path)) ? ('?' . $res) : $res;
    }

    return $urn;
}

/**
 * Remove parameter from the URL query part
 *
 * @param string ... query
 * @param string ... parameters to remove
 * @return string modified query
 */
function fn_query_remove()
{
    $args = func_get_args();
    $url = array_shift($args);

    if (!empty($args)) {
        list($path, $params, $escaped) = fn_parse_urn($url);

        foreach ($args as $param_name) {
            unset($params[$param_name]);
        }

        $url = fn_build_urn($path, $params, $escaped);
    }

    return $url;
}

/**
 * Replaces placeholders with request vars
 * @param string $href URL with placeholders
 * @param array $request Request parameters
 * @return string  processed URL
 */
function fn_substitute_vars($href, $request)
{
    if (strpos($href, '%') !== false) {
        list($dispatch, $params_list) = explode('?', $href);

        if (preg_match_all("/%(\w+)/", $params_list, $m)) {
            foreach ($m[1] as $value) {
                $_val = strtolower($value);
                if (!empty($request[$_val])) {
                    $params_list = str_replace('%' . $value, urlencode($request[$_val]), $params_list);
                }
            }
        }

        $href = $dispatch . '?' . $params_list;
    }

    return $href;
}

/**
 * Rounds a value down with a given step
 *
 * @param int $value
 * @param int $step
 * @return int Rounded value
 */
function fn_floor_to_step($value, $step)
{
    $floor = false;

    if (empty($step) && !empty($value)) {
        $floor = $value;

    } elseif (!empty($value) && !empty($step)) {
        if ($value % $step) {
            $floor = floor($value / $step) * $step;
        } else {
            $floor = $value;
        }
    }

    return $floor;
}

/**
 * Rounds a value up with a given step
 *
 * @param int $value
 * @param int $step
 * @return int Rounded value
 */
function fn_ceil_to_step($value, $step)
{
    $ceil = false;

    if (empty($step) && !empty($value)) {
        $ceil = $value;

    } elseif (!empty($value) && !empty($step)) {
        if ($value % $step) {
            $ceil = ceil($value / $step) * $step;
        } else {
            $ceil = $value;
        }
    }

    return $ceil;
}

/**
 * Gets list of customer css files
 *
 * @return string list of css files separated with comma
 */
function fn_get_frontend_css()
{
    $theme_path = fn_get_theme_path('[relative]/[theme]', 'C') . '/css/';

    $files = array (
        $theme_path . 'reset.css',
        $theme_path . 'scheme.less',
        $theme_path . 'base.css',
        $theme_path . 'styles.css'
    );

    $url = '';
    if (!empty($files)) {
        $styles = array();
        foreach ($files as $file) {
            $styles[] = array(
                'file' => Registry::get('config.dir.root') . '/' . $file,
                'relative' => $file,
                'media' => ''
            );
        }

        $url = fn_merge_styles($styles);
    }

    return $url;
}

/**
 * Merges css and less files
 *
 * @param array $files Array with style files
 * @param string $styles Style code
 * @param string $prepend_prefix Prepend prefix
 * @param array $params additional params
 */
function fn_merge_styles($files, $styles='', $prepend_prefix = '', $params = array())
{
    $prefix = (!empty($prepend_prefix) ? 'embedded' : 'standalone');
    $output = '';
    $less_output = '';
    $less_import_dirs = array();
    $relative_path = fn_get_theme_path('[relative]/[theme]/css');

    $names = array_map(function($v) {
        return !empty($v['relative']) ? $v['relative'] : false;
    }, $files);

    $names[] = md5($styles);
    arsort($names);
    $version = container()->getParameter('marketplace.version');
    $hash = md5(implode(',', $names) . $version) . fn_get_storage_data('cache_id');

    $filename = $prefix . '.' . $hash . '.css';

    $staticsStorageService = container()->get('Wizacha\Storage\StaticsStorageService');
    if (!$staticsStorageService->isExist($relative_path . '/' . $filename)) {
        foreach ($files as $src) {
            $m_prefix = '';
            $m_suffix = '';
            if (!empty($src['media'])) {
                $m_prefix = "\n@media " . $src['media'] . " {\n";
                $m_suffix = "\n}\n";
            }

            if (strpos($src['file'], '.css') !== false) {
                $output .= "\n" . $m_prefix . fn_get_contents($src['file']) . $m_suffix;
            } else {
                $less_output .= "\n" . $m_prefix . fn_get_contents($src['file']) . $m_suffix;
                $less_import_dirs[] = dirname($src['file']);
            }
        }

        if (!empty($styles)) {
            $less_output .= $styles;
        }

        // Prepend all styles
        $less_output = $output . "\n" . $less_output;

        $less = new Less();
        $less->setFormatter('compressed');
        $less->setImportDir($less_import_dirs);
        $compiled_less = $less->customCompile($less_output, $prepend_prefix);

        $staticsStorageService->put($relative_path . '/' . $filename, array(
            'contents' => $compiled_less,
            'caching' => true
        ));

        if (!empty($params['use_scheme'])) {
            fn_put_contents(fn_get_cache_path(false) . 'theme_editor/' . $filename, '#LESS#' . $less_output);
        }
    }

    $url = $staticsStorageService->getUrl($relative_path . '/' . $filename);

    return $url;
}

/**
 * Convert underscored string to CamelCase
 *
 * @param string  $string String
 * @param bool $upper  upper-camelcase/lower-camelcase
 * @return string
 */
function fn_camelize($string, $upper = true)
{
    $regexp = $upper ? '/(?:^|_)(.?)/' : '/_(.?)/';

    return preg_replace_callback($regexp, function($matches) {
        return strtoupper($matches[1]);
    }, $string);
}

function fn_exim_json_encode($data)
{
    if (is_callable('mb_encode_numericentity') && is_callable('mb_decode_numericentity')) {
        $_data = fn_exim_prepare_data_to_convert($data);

        return mb_decode_numericentity(json_encode($_data), array (0x80, 0xffff, 0, 0xffff), 'UTF-8');
    } else {
        return json_encode($data);
    }
}

function fn_exim_prepare_data_to_convert($data)
{
    $_data = array();
    if (is_array($data) && is_callable('mb_encode_numericentity')) {
        foreach ($data as $k => $v) {
            $key = mb_encode_numericentity($k, array (0x80, 0xffff, 0, 0xffff), 'UTF-8');
            if (is_array($v)) {
                $_data[$key] = fn_exim_prepare_data_to_convert($v);
            } else {
                $_data[$key] = mb_encode_numericentity($v, array (0x80, 0xffff, 0, 0xffff), 'UTF-8');
            }
        }
    } else {
        $_data = $data;
    }

    return $_data;
}

/**
 * Checks if correct url was requested
 *
 * @param string $area Area
 * @return boolean Return true if currecnt url requested or requested url was correct, false otherwise
 */
function fn_check_requested_url($area = AREA)
{
    if ($area == 'C' && !empty($_SERVER['REQUEST_URI']) && !empty($_SERVER['SCRIPT_NAME'])) {
        $request_path = rtrim(@parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');

        if ($request_path != $_SERVER['SCRIPT_NAME']) {
            $index_script = Registry::get('config.customer_index');
            $current_path = Registry::get('config.current_path');

            return preg_match("!^$current_path(/$index_script)?$!", $request_path);
        }
    }

    return true;
}

/**
 * Gets uri for administrator's preview from common uri
 *
 * @param string $uri Common url
 * @param array $object_data Preview object data
 * @param array $user_id User identifier
 * @return string Preview uri
 */
function fn_get_preview_url($uri, $object_data, $user_id)
{
    $_uri = fn_link_attach($uri, 'action=preview');
    $_uri = urlencode($_uri);
    $preview_url = fn_url("profiles.view_product_as_user?user_id=$user_id&area=C&redirect_url=$_uri", 'A');

    if ($object_data['status'] == 'A' && isset($object_data['approved']) && $object_data['approved'] == 'Y' ) {
        $preview_url = fn_url($uri, 'C', 'http', (string) GlobalState::contentLocale());
    }
    return $preview_url;
}

/**
 * Cast fields according to api/fields_cast schema, recursively
 * @param array $fields An array of fields
 */
function fn_w_fields_cast(array &$fields){
    $schema = fn_get_schema('api', 'fields_cast');
    array_walk_recursive(
        $fields,
        function(&$val,$key) use($schema){
            if(isset($schema[$key])){
                $val = call_user_func($schema[$key],$val);
            }
        }
    );
}

/**
 * Used in smarty templates
 */
function fn_global_state_interface_locale(): string
{
    return (string) GlobalState::interfaceLocale();
}

/**
 * Used in smarty templates
 */
function fn_global_state_content_locale(): string
{
    return (string) GlobalState::contentLocale();
}

/**
 * Converts an input to a DateTime instance.
 *
 * @param DateTimeInterface|string|null  $date     A date or null to use the current time
 * @param DateTimeZone|string|null|false $timezone The target timezone, null to use the default, false to leave unchanged
 *
 * @return DateTimeImmutable A DateTimeImmutable instance
 */
function fn_date_converter($date = null, $timezone = null)
{
    // determine the timezone
    if (false !== $timezone) {
        if (null === $timezone) {
            $timezone = new DateTimeZone(date_default_timezone_get());
        } elseif (!$timezone instanceof DateTimeZone) {
            $timezone = new DateTimeZone($timezone);
        }
    }

    // immutable dates
    if ($date instanceof DateTimeImmutable) {
        return false !== $timezone ? $date->setTimezone($timezone) : $date;
    }

    if ($date instanceof DateTimeInterface) {
        $date = DateTimeImmutable::createFromMutable($date);
        if (false !== $timezone) {
            $date = $date->setTimezone($timezone);
        }

        return $date;
    }

    if (null === $date || 'now' === $date) {
        return new DateTimeImmutable($date, false !== $timezone ? $timezone : new DateTimeZone(date_default_timezone_get()));
    }

    $asString = (string) $date;
    if (ctype_digit($asString) || (!empty($asString) && '-' === $asString[0] && ctype_digit(substr($asString, 1)))) {
        $date = new DateTimeImmutable('@'.$date);
    } else {
        $date = new DateTimeImmutable($date, new DateTimeZone(date_default_timezone_get()));
    }

    if (false !== $timezone) {
        $date = $date->setTimezone($timezone);
    }

    return $date;
}

/**
 * Gets a number formatter instance according to given locale and formatter.
 *
 * @param string $locale Locale in which the number would be formatted
 * @param int    $style  Style of the formatting
 *
 * @return NumberFormatter A NumberFormatter instance
 */
function fn_get_number_formatter($locale, $style)
{
    static $formatter, $currentStyle;

    $locale = $locale !== null ? $locale : \Locale::getDefault();

    if ($formatter && $formatter->getLocale() === $locale && $currentStyle === $style) {
        // Return same instance of NumberFormatter if parameters are the same
        // to those in previous call
        return $formatter;
    }

    static $styleValues = array(
        'decimal' => NumberFormatter::DECIMAL,
        'currency' => NumberFormatter::CURRENCY,
        'percent' => NumberFormatter::PERCENT,
        'scientific' => NumberFormatter::SCIENTIFIC,
        'spellout' => NumberFormatter::SPELLOUT,
        'ordinal' => NumberFormatter::ORDINAL,
        'duration' => NumberFormatter::DURATION,
    );

    if (!isset($styleValues[$style])) {
        throw new InvalidArgumentException(sprintf('The style "%s" does not exist. Known styles are: "%s"', $style, implode('", "', array_keys($styleValues))));
    }

    $currentStyle = $style;

    $formatter = NumberFormatter::create($locale, $styleValues[$style]);

    return $formatter;
}

//to get attachments for message in tpl
function fn_get_attachments_by_message(int $messageId): ?array
{
    return container()->get('marketplace.message_attachment.message_attachment_service')->getAttachmentsByMessage($messageId);
}
