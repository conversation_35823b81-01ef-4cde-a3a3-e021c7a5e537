<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Tygh\Settings;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Seo\Slug\SlugGenerator;
use Wizacha\Marketplace\Seo\SlugTargetType;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

function fn_get_seo_join_condition($object_type)
{
    $res = db_quote(" AND ?:seo_names.type = ?s ", $object_type);

    if ($object_type != 's') {
        $res .= " AND ?:seo_names.dispatch = ''";
    }

    return $res;
}

function fn_create_seo_name($object_id, $object_type, $object_name, $index = 0, $dispatch = '', $company_id = '')
{
    $seoService = container()->get('marketplace.seo.seo_service');

    return $seoService->registerSlugLegacy($object_id, $object_type, $object_name, $index, $dispatch);
}

function fn_get_corrected_seo_lang_code($lang_code)
{
    return (Registry::get('addons.seo.single_url') == 'Y') ? Registry::get('settings.Appearance.frontend_default_language') : $lang_code;
}

function fn_seo_get_products(&$params, &$fields, &$sortings, &$condition, &$join, &$sorting, &$group_by)
{
    if (isset($params['compact']) && $params['compact'] == 'Y') {
        $condition .= db_quote(' OR ?:seo_names.name LIKE ?s', '%' . preg_replace('/-[a-zA-Z]{1,3}$/i', '', str_ireplace(SEO_FILENAME_EXTENSION, '', $params['q'])) . '%');
    }

    $fields[] = '?:seo_names.name as seo_name';
    $join .= db_quote(
        " LEFT JOIN ?:seo_names ON ?:seo_names.object_id = CAST(products.product_id AS CHAR) AND ?:seo_names.type = 'p' AND ?:seo_names.dispatch = ''"
    );
}

function fn_seo_get_page_data(&$page_data, &$lang_code)
{

    $page_data['seo_name'] = db_get_field(
        "SELECT name FROM ?:seo_names WHERE object_id = ?s ?p",
        $page_data['page_id'], fn_get_seo_join_condition('a')
    );

    if (empty($page_data['seo_name'])) {
        // generate seo name
        $page_data['seo_name'] = fn_seo_get_name('a', $page_data['page_id'], '', null, $lang_code);
    }

    return true;
}

function fn_get_seo_vars($type = '', $param = '')
{
    $seo = array(
        'p' => array(
            'table' => '?:product_descriptions',
            'description' => 'product',
            'dispatch' => 'products.view',
            'item' => 'product_id',
            'condition' => '',
            'not_shared' => true,
        ),
        'c' => array(
            'table' => '?:category_descriptions',
            'description' => 'category',
            'dispatch' => 'categories.view',
            'item' => 'category_id',
            'condition' => '',
            'not_shared' => true,
        ),
        'a' => array(
            'table' => '?:page_descriptions',
            'description' => 'page',
            'dispatch' => 'pages.view',
            'item' => 'page_id',
            'condition' => ''
        ),
        'e' => array(
            'table' => '?:product_feature_variant_descriptions',
            'description' => 'variant',
            'dispatch' => 'product_features.view',
            'item' => 'variant_id',
            'condition' => ''
        ),
        's' => array(
            'table' => '?:seo_names',
            'description' => 'name',
            'dispatch' => '',
            'item' => 'object_id',
            'condition' => '',
            'not_shared' => true,
        ),
    );

    $seo['m'] = array(
        'table' => '?:companies',
        'description' => 'company',
        'dispatch' => 'companies.view',
        'item' => 'company_id',
        'condition' => '',
        'skip_lang_condition' => true
    );

    $res = (!empty($type)) ? $seo[$type] : $seo;

    if (!empty($param)) {
        $res = !empty($res[$param]) ? $res[$param] : false;
    }

    return $res;
}

function fn_get_rewrite_rules()
{
    $current_path = (defined('HTTPS')) ? Registry::get('config.https_path') : Registry::get('config.http_path');

    $prefix = ((Registry::get('addons.seo.seo_language') == 'Y') ? '\/([a-z]+)' : '()');

    $rewrite_rules = array();

    $extension = str_replace('.', '', SEO_FILENAME_EXTENSION);

    $rewrite_rules['!^' . $current_path . $prefix . '\/(.*\/)?([^\/]+)-page-([0-9]+|full_list)\.(' . $extension . ')$!'] = 'object_name=$matches[3]&page=$matches[4]&sl=$matches[1]&extension=$matches[5]';
    $rewrite_rules['!^' . $current_path . $prefix . '\/(.*\/)?([^\/]+)\.(' . $extension . ')$!'] = 'object_name=$matches[3]&sl=$matches[1]&extension=$matches[4]';

    if (Registry::get('addons.seo.seo_language') == 'Y') {
        $rewrite_rules['!^' . $current_path . $prefix . '\/?$!'] = '$customer_index?sl=$matches[1]';
    }
    if (Registry::get('addons.seo.seo_category_type') != 'file') {
        $rewrite_rules['!^' . $current_path . $prefix . '\/(.*\/)?([^\/]+)\/page-([0-9]+|full_list)(\/)?$!'] = 'object_name=$matches[3]&page=$matches[4]&sl=$matches[1]';
    }

    $rewrite_rules['!^' . $current_path . $prefix . '\/(.*\/)?([^\/?]+)\/?$!'] = 'object_name=$matches[3]&sl=$matches[1]';
    $rewrite_rules['!^' . $current_path . $prefix . '/$!'] = '';

    return $rewrite_rules;
}

/**
 * "get_route" hook implemetation
 * @param array &$req input request
 * @param array &$result result of init function
 * @param string $area Area
 * @param boolean $is_allowed_url Flag that determines if url is supported
 * @return bool true on success, false on failure
 */
function fn_seo_get_route(&$req, &$result, &$area, &$is_allowed_url)
{
    $current_path = Registry::get('config.current_path');

    if (($area == 'C') && !$is_allowed_url) {

        // Remove web directory from request
        $url_pattern = @parse_url(urldecode($_SERVER['REQUEST_URI']));
        if (empty($url_pattern)) {
            // Unable to parse URL
            $req = array(
                'dispatch' => '_no_page'
            );

            return false;
        }

        $rule_matched = false;
        $rewrite_rules = fn_get_rewrite_rules();
        foreach ($rewrite_rules as $pattern => $query) {
            if (preg_match($pattern, $url_pattern['path'], $matches) || preg_match($pattern, urldecode($query), $matches)) {
                $is_allowed_url = true;
                $rule_matched = true;
                $_query = preg_replace("!^.+\?!", '', $query);
                parse_str($_query, $objects);
                $result_values = 'matches';
                $url_query = "";
                foreach ($objects as $key => $value) {
                    preg_match('!^.+\[([0-9])+\]$!', $value, $_id);
                    $objects[$key] = (substr($value, 0, 1) == '$') ? ${$result_values}[$_id[1]] : $value;
                }

                // For the locations wich names stored in the table
                if (!empty($objects) && !empty($objects['object_name'])) {
                    if (Registry::get('addons.seo.single_url') == 'Y') {
                        $objects['sl'] = (Registry::get('addons.seo.seo_language') == 'Y') ? $objects['sl'] : '';
                        $objects['sl'] = !empty($req['sl']) ? $req['sl'] : $objects['sl'];
                    }

                    $_seo = db_get_row("SELECT * FROM ?:seo_names WHERE name = ?s", $objects['object_name']);

                    if (empty($_seo)) {
                        $_seo = db_get_row("SELECT * FROM ?:seo_names WHERE name = ?s ", $objects['object_name']);
                    }

                    if (empty($_seo) && !empty($objects['extension'])) {
                        $_seo = db_get_row("SELECT * FROM ?:seo_names WHERE name = ?s", $objects['object_name'] . '.' . $objects['extension']);
                        if (empty($_seo)) {
                            $_seo = db_get_row("SELECT * FROM ?:seo_names WHERE name = ?s ", $objects['object_name'] . '.' . $objects['extension']);
                        }
                    }

                    if (!empty($_seo) && ($_seo['type'] == 's' && !empty($objects['extension']) && strpos($_seo['name'], '.' . $objects['extension']) === false || Registry::get('addons.seo.seo_category_type') == 'file' && $_seo['type'] == 'c' && empty($objects['extension']))) {
                        $_seo = array();
                        $objects['object_name'] = '_wrong_path_';
                    }

                    if (!empty($_seo)) {
                        if (Registry::get('addons.seo.single_url') != 'Y' && empty($objects['sl'])) {
                            $objects['sl'] = $_seo['lang_code'];
                        }

                        $req['sl'] = $objects['sl'];

                        if (fn_seo_validate_object($_seo, $url_pattern['path'], $objects) == false) {
                            $req = array(
                                'dispatch' => '_no_page'
                            );

                            return false;
                        }

                        // Product pages are now routed by Symfony
                        if ($_seo['type'] === SlugTargetType::PRODUCT) {
                            return false;
                        }
                        // Attribute pages are now routed by Symfony
                        if ($_seo['type'] === SlugTargetType::ATTRIBUTE_VARIANT) {
                            return false;
                        }
                        // Company pages are now routed by Symfony
                        if ($_seo['type'] === SlugTargetType::COMPANY) {
                            return false;
                        }
                        // Category pages are now routed by Symfony
                        if ($_seo['type'] === SlugTargetType::CATEGORY) {
                            return false;
                        }
                        // CMS pages are now routed by Symfony
                        if ($_seo['type'] === SlugTargetType::CMS_PAGE) {
                            return false;
                        }

                        $_seo_vars = fn_get_seo_vars($_seo['type']);
                        if ($_seo['type'] == 's') {
                            $url_query = 'dispatch=' . $_seo['dispatch'];
                            $req['dispatch'] = $_seo['dispatch'];
                        } else {
                            $page_suffix = (!empty($objects['page'])) ? ('&page=' . $objects['page']) : '';
                            $url_query = 'dispatch=' . $_seo_vars['dispatch'] . '&' . $_seo_vars['item'] . '=' . $_seo['object_id'] . $page_suffix;

                            $req['dispatch'] = $_seo_vars['dispatch'];
                        }
                        $req[$_seo_vars['item']] = $_seo['object_id'];
                    } elseif (($current_path != $objects['object_name']) || strlen($objects['object_name']) == 2) {
                        $req = array(
                            'dispatch' => '_no_page'
                        );

                        return false;
                    }

                    if (!empty($objects['page'])) {
                        $req['page'] = $objects['page'];
                    }

                    // For the locations wich names are not in the table
                } elseif (!empty($objects)) {
                    if (empty($objects['dispatch'])) {
                        if (!empty($req['dispatch'])) {
                            $req['dispatch'] = is_array($req['dispatch']) ? key($req['dispatch']) : $req['dispatch'];
                            $url_query = 'dispatch=' . $req['dispatch'];
                        }
                    } else {
                        $url_query = 'dispatch=' . $objects['dispatch'];
                        $req['dispatch'] = $objects['dispatch'];
                    }
                    if (!empty($objects['sl'])) {
                        $req['sl'] = $objects['sl'];
                        if (Registry::get('addons.seo.seo_language') == 'Y') {
                            $lang_statuses = !empty($_SESSION['auth']['area']) && $_SESSION['auth']['area'] == 'A' ? array('A', 'H') : array('A');
                            $check_language = db_get_field("SELECT count(*) FROM ?:languages WHERE lang_code = ?s AND status IN (?a)", $req['sl'], $lang_statuses);
                            if ($check_language == 0) {
                                $req = array(
                                    'dispatch' => '_no_page'
                                );

                                return false;
                            }
                        }
                    }
                    $req += $objects;

                    // Empty query
                } else {
                    $url_query = '';
                }

                $lang_code = empty($objects['sl']) ? Registry::get('settings.Appearance.frontend_default_language') : $objects['sl'];
                $_SERVER['REQUEST_URI'] = fn_url('?' . $url_query, 'C', 'rel', $lang_code);
                $_SERVER['QUERY_STRING'] = $url_query . (!empty($_SERVER['QUERY_STRING']) ? '&' . $_SERVER['QUERY_STRING'] : '');
                $_SERVER['X-SEO-REWRITE'] = true;
                break;
            }
        }

        if (empty($rule_matched)) {
            $req = array(
                'dispatch' => '_no_page'
            );

            return false;
        }
    }
}

function fn_seo_update_object($object_data, $object_id, $type, $lang_code)
{
    if (!empty($object_id) && isset($object_data['seo_name'])) {

        $_object_name = '';
        $seo_vars = fn_get_seo_vars($type);
        $seoService = container()->get('marketplace.seo.seo_service');

        // check if seo name is invalid
        if ($seoService->isSeoNameValid((string) $object_data['seo_name']) === false
            && $type === SlugTargetType::PRODUCT
        ) {
            return true;
        }

        if ($type === SlugTargetType::CATEGORY && \mb_strlen($object_data['seo_name']) === 0) {
            $oldSeoName = $seoService->getSeoName($object_id);
            if ($oldSeoName !== null && \mb_strlen($oldSeoName) > 0) {
                $_object_name = $oldSeoName;
            }
        }

        if (empty($_object_name)) {
            if (!empty($object_data['seo_name'])) {
                $_object_name = $object_data['seo_name'];
            } elseif (!empty($object_data[$seo_vars['description']])) {
                $_object_name = $object_data[$seo_vars['description']];
            } else {
                $_object_name = fn_seo_get_default_object_name($object_id, $type, $lang_code);
            }
        }

        $seoService->registerSlug(new SlugTargetType($type), $object_id, $_object_name);

        return true;
    }

    return false;
}

function fn_seo_validate_object($seo, $path, $objects)
{
    $result = true;

    if (AREA == 'C') {
        $avail_langs = fn_get_simple_languages(!empty($_SESSION['auth']['area']) && $_SESSION['auth']['area'] == 'A');
        if (!empty($objects['sl']) && !in_array($objects['sl'], array_keys($avail_langs))) {
            return false;
        }
    }

    $path = substr($path, strlen((defined('HTTPS') ? Registry::get('config.https_path') : Registry::get('config.http_path'))) + 1); // remove path prefix

    if (preg_match('/^(.*\/)?((' . $objects['object_name'] . ')(([\/\-]page[\-]?[\d]*)?(\/|(\.'.str_replace('.', '', SEO_FILENAME_EXTENSION).'))?)?)$/', $path, $matches)) {
        // remove object from path
        $path = substr_replace($path, '', strrpos($path, $matches[2]));
    }

    if (Registry::get('addons.seo.seo_language') == 'Y') {
        $path = substr($path, 3); // remove language prefix
    }

    $path = rtrim($path, '/'); // remove trailing slash
    // check parent objects
    $vars = fn_get_seo_vars($seo['type']);
    $id_path = '';
    if ($seo['type'] == 'p') {
        if (Registry::get('addons.seo.seo_product_type') == 'product_category') {
            $id_paths = db_get_array("SELECT id_path, c.category_id FROM ?:categories as c LEFT JOIN ?:products_categories as p ON p.category_id = c.category_id WHERE p.product_id = ?i ", $seo['object_id']);
            $result = false;
            foreach ($id_paths as $id_path) {
                if (fn_seo_validate_parents($path, $id_path['id_path'], 'c', false)) {
                    $_SESSION['current_category_id'] = $id_path['category_id'];
                    $result = true;
                }
            }
        } else {
            $result = fn_seo_validate_parents($path, $id_path, 'c', false);
        }
    } elseif ($seo['type'] == 'c') {
        $id_path = db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i AND parent_id != 0", $seo['object_id']);
        $result = (Registry::get('addons.seo.seo_category_type') == 'root_category') ? empty($path) : fn_seo_validate_parents($path, $id_path, 'c', true);
    } elseif ($seo['type'] == 'a') {
        if (Registry::get('addons.seo.seo_product_type') == 'product_category') {
            $id_path = db_get_field("SELECT id_path FROM ?:pages WHERE page_id = ?i AND parent_id != 0", $seo['object_id']);
        }
        $result = fn_seo_validate_parents($path, $id_path, 'a', true);
    } elseif ($seo['type'] == 's') {
        if (!empty($path)) {
            $result = false;
        }
    }

    // check for SEO_FILENAME_EXTENSION extension for the current object
    if ((in_array($seo['type'], array('p', 'a')) && empty($objects['extension'])) || ($seo['type'] == 'c' && Registry::get('addons.seo.seo_category_type') != 'file' && !empty($objects['extension']))) {
        $result = false;
    }

    if ($seo['type'] == 'n') {
        $result = empty($objects['extension']) ? false : fn_seo_validate_parents($path, '', 'n', true);
    }

    return $result;
}

function fn_seo_validate_parents($path, $id_path, $parent_type, $trim_last = false)
{
    $result = true;

    if (!empty($id_path)) {
        if ($trim_last == true) {
            $id_path = explode('/', $id_path);
            array_pop($id_path);
        }

        $parent_names = explode('/', $path);
        $parent_ids = is_array($id_path) ? $id_path : explode('/', $id_path);

        if (count($parent_ids) == count($parent_names)) {
            $parents = db_get_hash_single_array(
                "SELECT object_id, name FROM ?:seo_names WHERE name IN (?a) AND type = ?s",
                array('object_id', 'name'), $parent_names, $parent_type
            );

            foreach ($parent_ids as $k => $id) {
                if (empty($parents[$id]) || $parent_names[$k] != $parents[$id]) {
                    $result = false;
                    break;
                }
            }
        } else {
            $result = false;
        }
    } elseif (!empty($path)) { // if we have no parents, but some was passed via URL
        $result = false;
    }

    return $result;
}

/**
 * Create cache for static items
 * @param string $lang_code language code
 * @return boolean always true
 */
function fn_seo_cache_static_create()
{
    Registry::registerCache('seo', array('seo_names'));
    // Get and cache names for pages, extended features and static names

    if (!Registry::isExist('seo')) {
        $cache = array();
        $object_types = array(
            'a' => array('object_id', 'name'),
            's' => array('dispatch', 'name'),
            'e' => array('object_id', 'name'),
            'm' => array('object_id', 'name'),
        );

        // Combine types to make less db queries
        $combined_types = array();
        foreach ($object_types as $type => $fields) {
            $fields_list = implode(',', $fields);
            $combined_types[$fields_list]['types'][] = $type;
            $combined_types[$fields_list]['fields'] = fn_array_merge(array('type'), $fields, false);
        }

        foreach ($combined_types as $fields_list => $data) {
            $cache = fn_array_merge($cache, db_get_hash_multi_array("SELECT ?p, type FROM ?:seo_names WHERE type IN (?a)", $data['fields'], implode(',', $data['fields']), $data['types']));
        }

        Registry::set('seo', array(
            'names' => $cache
        ));
    }

    return true;
}

/**
 * Cache products names
 * @param array $products products
 * @param array $params input params for fn_get_products function
 * @param string $lang_code language code
 * @return boolean always true
 */
function fn_seo_get_products_post(&$products, &$params)
{
    if (AREA == 'C' && !empty($products)) {

        $product_ids = array();
        foreach ($products as $k => $product) {
            $product_ids[] = $product['product_id'];
            fn_seo_cache_name('p', $product['product_id'], $product['seo_name'],  isset($product['company_id']) ? $product['company_id'] : '');
        }

        if (Registry::get('addons.seo.seo_product_type') == 'product_category') {
            $id_paths = db_get_array("SELECT pc.product_id, c.id_path, pc.link_type FROM ?:products_categories as pc LEFT JOIN ?:categories as c ON pc.category_id = c.category_id WHERE pc.product_id IN (?n) ORDER BY pc.link_type ASC", $product_ids);

            foreach ($id_paths as $path) {
                fn_seo_cache_parent_items_path('p', $path['product_id'], $path['id_path']);
            }
        }
    }

    return true;
}


/**
 * Cache parent items path of names for seo object
 * @param string $object_type object type of seo object
 * @param string $object_id object id of seo object
 * @param string $id_path id path for seo object
 * @return boolean always true
 */
function fn_seo_cache_parent_items_path($object_type, $object_id, $id_path)
{
    static $count_cache_parent_items_path = 0;

    if ($count_cache_parent_items_path < SEO_RUNTIME_CACHE_COUNT) {
        Registry::set('runtime.seo.parent_items.' . $object_type . '.' . $object_id, $id_path);
        $count_cache_parent_items_path++;
    }

    return true;
}

/**
 * Get parent items path of names for seo object
 * @param string $object_type object type of seo object
 * @param string $object_id object id of seo object
 * @param bool $is_pop - skip current object name
 * @param int $company_id Company identifier
 * @param string $lang_code language code
 * @return array parent items path of names
 */
function fn_seo_get_parent_items_path($object_type, $object_id, $is_pop = false, $company_id = null, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $id_path = Registry::get('runtime.seo.parent_items.' . $object_type . '.' . $object_id);

    if (empty($id_path)) {
        if ($object_type == 'p') {
            if (is_numeric($object_id)) {
                $id_path = db_get_field("SELECT c.id_path FROM ?:products_categories as pc LEFT JOIN ?:categories as c ON pc.category_id = c.category_id WHERE pc.product_id = ?i ORDER BY pc.link_type LIMIT 1", $object_id);
            } else {
                $mvpService = container()->get('marketplace.multi_vendor_product.service');
                try {
                    $id_path = $mvpService->get($object_id)->getCategory()->getPathId();
                } catch (NotFound $e) {
                    return [];
                }
            }
        } elseif ($object_type == 'c') {
            $id_path = db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i", $object_id);
        } elseif ($object_type == 'a') {
            $id_path = db_get_field("SELECT id_path FROM ?:pages WHERE page_id = ?i", $object_id);
        }
        fn_seo_cache_parent_items_path($object_type, $object_id, $id_path);
    }

    $parent_item_names = array();

    if (!empty($id_path)) {
        $path_ids = explode("/", $id_path);

        if ($is_pop) {
            array_pop($path_ids);
        }

        foreach ($path_ids as $v) {
            $object_type_for_name = ($object_type == 'p') ? 'c' : $object_type;
            $parent_item_names[] = fn_seo_get_name($object_type_for_name, $v, '', $company_id, $lang_code);
        }

        return $parent_item_names;
    }

    return array();
}

/**
 * Cache name for seo object
 * @param string $object_type object type of seo object
 * @param string $object_id object id of seo object
 * @param string $object_name  dispatch of seo object
 * @param int $company_id Company identifier
 * @return bool always true
 */
function fn_seo_cache_name($object_type, $object_id, $object_name, $company_id)
{
    static $count_cache_name = 0;

    if ($count_cache_name < SEO_RUNTIME_CACHE_COUNT) {
        $object_id = str_replace('.', '', $object_id); // here can be dispatch, so we need to remove dots
        Registry::set('runtime.seo.' . $object_type . $company_id .'.' . $object_id, $object_name);
        $count_cache_name++;
    }

    return true;
}

/**
 * Get name for seo object
 *
 * @param string $object_type object type of seo object
 * @param string $object_id object id of seo object
 * @param string $dispatch  dispatch of seo object
 * @param int $company_id Company identifier
 * @param string $lang_code language code
 * @return string name for seo object
 */
function fn_seo_get_name($object_type, $object_id = 0, $dispatch = '', $company_id = null, $lang_code = null)
{
    if ($object_type === SlugTargetType::PRODUCT()->getValue()) {
        $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    } else {
        $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
        $lang_code = fn_get_corrected_seo_lang_code($lang_code);
    }

    $company_id_condition = '';

    if ($company_id == null) {
        $company_id = '';
    }

    if (empty($object_id)) {
        $name = Registry::get("seo.names.$object_type." . str_replace('.', '', $dispatch)); // so we need to remove dots from dispatch
    }

    if (!empty($object_id)) {
        $name = Registry::get('runtime.seo.' . $object_type . $company_id . '.' . $object_id);
    }

    if (empty($name)) {

        $where_params = array(
            'type' => $object_type,
            'dispatch' => $dispatch,
        );

        $name = db_get_field("SELECT name FROM ?:seo_names WHERE object_id = ?s AND ?w ?p", $object_id, $where_params, $company_id_condition);

        if (empty($name)) {
            if ($object_type == 's') {
                $alt_name = db_get_field(
                    "SELECT name FROM ?:seo_names WHERE object_id = ?s AND type = ?s AND dispatch = ?s ?p",
                    $object_id, $object_type, $dispatch, $company_id_condition
                );
                if (!empty($alt_name)) {
                    $name = fn_create_seo_name($object_id, $object_type, str_replace('.', '-', $dispatch), 0, $dispatch, $company_id);
                }
            } else {
                $_seo = fn_get_seo_vars($object_type);

                $object_name = '';
                // Get object name from its descriptions
                if (!empty($_seo['table']) && isset($_seo['condition'])) {
                    $lang_condition = '';
                    if (empty($_seo['skip_lang_condition'])) {
                        $lang_condition = db_quote("AND lang_code = ?s", $lang_code);
                    }
                    $object_name = db_get_field(
                        "SELECT $_seo[description] FROM $_seo[table] WHERE $_seo[item] = ?i ?p ?p",
                        $object_id, $lang_condition, $_seo['condition']
                    );
                }

                $name = fn_create_seo_name($object_id, $object_type, $object_name, 0, $dispatch, $company_id);
            }
        }

        $_object_id = !empty($object_id) ? $object_id : $dispatch;
        fn_seo_cache_name($object_type, $_object_id, $name, $company_id);
    }

    return $name;
}

/**
 * Check if object was called by direct link or language code was not passed to url
 * @param array $req input request array
 * @param string $area current application area
 * @param string $lang_code language code
 * @return array init function status
 */
function fn_seo_check_dispatch(&$req, $area = AREA, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    fn_seo_cache_static_create();
    if ($area == 'C') {
        if (Registry::get('addons.seo.seo_language') == 'Y' && (empty($req) || $req['dispatch'] == 'index.index')) {
            if (fn_url('', 'C', 'rel', $lang_code) != $_SERVER['REQUEST_URI']) {
                // redirect from "www.site.com" to "www.site.com/en/" in case of multilanguage urls.
                header("HTTP/1.0 301 Moved Permanently");

                return array(INIT_STATUS_REDIRECT, fn_url("", 'C', 'rel', $lang_code));
            }
        }

        if ($_SERVER['REQUEST_METHOD'] == 'GET' && empty($_SERVER['X-SEO-REWRITE']) && !empty($req['dispatch'])) {
            $_req = $req;
            $dispatch = $_req['dispatch'];
            unset($_req['dispatch']);

            $seo_url = fn_url($dispatch . '?' . http_build_query($_req), 'C', 'rel', !empty($_req['sl']) ? $_req['sl'] : $lang_code);

            if (strpos($seo_url, 'dispatch=') === false) {
                header("HTTP/1.0 301 Moved Permanently");

                return array(INIT_STATUS_REDIRECT, $seo_url);
            }
        }
    }

    return array(INIT_STATUS_OK);
}

/**
 * Get seo url
 * @param string $url url
 * @param string $area area for area
 * @param string $original_url original url from fn_url
 * @param string $prefix prefix
 * @param int $company_id_in_url Company identifier
 * @param string $lang_code language code
 * @return string seo url
 */
function fn_seo_url_post(&$url, &$area, &$original_url, &$prefix, &$company_id_in_url, &$lang_code)
{
    static $seo_settings_cache = array();

    if ($area != 'C') {
        return $url;
    }

    $d = SlugGenerator::DELIMITER;
    $parced_query = array();
    $parced_url = parse_url($url);

    $index_script = Registry::get('config.customer_index');
    if ('index.php' != $index_script) {
        return $url;
    }
    $http_path = Registry::get('config.http_path');
    $https_path = Registry::get('config.https_path');

    $settings_company_id = empty($company_id_in_url) ? 0 : $company_id_in_url;

    if (isset($seo_settings_cache[$settings_company_id])) {
        $seo_settings = $seo_settings_cache[$settings_company_id];
    } else {
        $seo_settings = Settings::instance()->getValues('seo', Settings::ADDON_SECTION, false, $company_id_in_url);
        $seo_settings_cache[$settings_company_id] = $seo_settings;
    }

    $current_path = '';
    if (empty($parced_url['scheme'])) {
        $current_path = (defined('HTTPS')) ? $https_path . '/' : $http_path . '/';
    }

    if (!empty($parced_url['scheme']) && ($parced_url['scheme'] != 'http' && $parced_url['scheme'] != 'https')) {
        return $url;  // This is no http/https url like mailto:, ftp:
    } elseif (!empty($parced_url['scheme'])) {
        if (
            !empty($parced_url['host'])
            && ($parced_url['host'] != Registry::get('config.http_host')
                && $parced_url['host'] != Registry::get('config.https_host'))) {
            return $url;  // This is external link
        } elseif (!empty($parced_url['path']) && (($parced_url['scheme'] == 'http' && !empty($http_path) && stripos($parced_url['path'], $http_path) === false) || ($parced_url['scheme'] == 'https' && !empty($https_path) && stripos($parced_url['path'], $https_path) === false))) {
            return $url;  // This is external link
        } else {
            if (rtrim($url, '/') == Registry::get('config.http_location') || rtrim($url, '/') == Registry::get('config.https_location')) {
                $url = rtrim($url, '/') . "/" . $index_script;
                $parced_url['path'] = rtrim($parced_url['path'], '/') . "/" . $index_script;
            }
        }
    }

    if (!empty($parced_url['query'])) {
        parse_str($parced_url['query'], $parced_query);
    }

    if (!empty($parced_query['lc'])) {
        //if localization parameter is exist we will get language code for this localization.
        $loc_languages = db_get_hash_single_array("SELECT a.lang_code, a.name FROM ?:languages as a LEFT JOIN ?:localization_elements as b ON b.element_type = 'L' AND b.element = a.lang_code WHERE b.localization_id = ?i ORDER BY position", array('lang_code', 'name'), $parced_query['lc']);
        $new_lang_code = (!empty($loc_languages)) ? key($loc_languages) : '';
        $lang_code = (!empty($new_lang_code)) ? $new_lang_code : $lang_code;
    }

    if (!empty($parced_url['path']) && empty($parced_url['query']) && $parced_url['path'] == $index_script) {
        $url = $current_path . (($seo_settings['seo_language'] == 'Y') ? $lang_code . '/' : '');

        return $url;
    }

    $path = str_replace($index_script, '', $parced_url['path'], $count);

    if ($count == 0) {
        return $url; // This is currently seo link
    }

    $fragment = !empty($parced_url['fragment']) ? '#' . $parced_url['fragment'] : '';

    $link_parts = array(
        'scheme' => !empty($parced_url['scheme']) ? $parced_url['scheme'] . '://' : '',
        'host' => !empty($parced_url['host']) ? $parced_url['host'] : '',
        'path' => $current_path . $path,
        'lang_code' => ($seo_settings['seo_language'] == 'Y') ? $lang_code . '/' : '',
        'parent_items_names' => '',
        'name' => '',
        'page' => '',
        'extension' => '',
    );

    if (!empty($parced_query)) {
        if (!empty($parced_query['sl'])) {
            $lang_code = $parced_query['sl'];

            if ($seo_settings['single_url'] != 'Y') {
                $unset_lang_code = $parced_query['sl'];
                unset($parced_query['sl']);
            }

            if ($seo_settings['seo_language'] == 'Y') {
                $link_parts['lang_code'] = $lang_code . '/';
                $unset_lang_code = isset($parced_query['sl']) ? $parced_query['sl'] : $unset_lang_code;
                unset($parced_query['sl']);
            }
        }

        $lang_code = fn_get_corrected_seo_lang_code($lang_code);

        if (!empty($parced_query['dispatch']) && is_string($parced_query['dispatch'])) {

            if (!empty($original_url) && (stripos($parced_query['dispatch'], '/') !== false || substr($parced_query['dispatch'], -1 * strlen(SEO_FILENAME_EXTENSION)) == SEO_FILENAME_EXTENSION)) {
                $url = $original_url;

                return $url; // This is currently seo link
            }

            // Convert products links
            if ($parced_query['dispatch'] == 'products.view' && !empty($parced_query['product_id'])) {
                if ($seo_settings['seo_product_type'] == 'product_category') {
                    $parent_item_names = fn_seo_get_parent_items_path('p', $parced_query['product_id'], false, $company_id_in_url, $lang_code);
                    $link_parts['parent_items_names'] = !empty($parent_item_names) ? join('/', $parent_item_names) . "/" : "";
                }

                $link_parts['name'] = fn_seo_get_name('p', $parced_query['product_id'], '', $company_id_in_url, $lang_code);
                $link_parts['extension'] = SEO_FILENAME_EXTENSION;

                fn_seo_parced_query_unset($parced_query, 'product_id');

            // Convert categories links
            } elseif ($parced_query['dispatch'] == 'categories.view' && !empty($parced_query['category_id'])) {
                if ($seo_settings['seo_category_type'] != 'root_category') {
                    $parent_item_names = fn_seo_get_parent_items_path('c', $parced_query['category_id'], true, $company_id_in_url, $lang_code);
                    $link_parts['parent_items_names'] = !empty($parent_item_names) ? join('/', $parent_item_names) . "/" : "";
                }

                $link_parts['name'] = fn_seo_get_name('c', $parced_query['category_id'], '', $company_id_in_url, $lang_code);

                $page = isset($parced_query['page']) ? $parced_query['page'] : 0;
                if ($seo_settings['seo_category_type'] != 'file') {
                    $link_parts['name'] .= '/';
                    if (!empty($page) && $page != '1') {
                        $link_parts['name'] .= 'page' . $d . $page . '/';
                    }
                    unset($parced_query['page']);
                } else {
                    $link_parts['extension'] = SEO_FILENAME_EXTENSION;
                    if (!empty($page) && $page != '1') {
                        $link_parts['name'] .= $d . 'page' . $d . $page;
                    }
                    unset($parced_query['page']);
                }

                fn_seo_parced_query_unset($parced_query, 'category_id');

            //Convert pages links
            } elseif ($parced_query['dispatch'] == 'pages.view' && !empty($parced_query['page_id'])) {

                if ($seo_settings['seo_product_type'] == 'product_category') {
                    $parent_item_names = fn_seo_get_parent_items_path('a', $parced_query['page_id'], true, $company_id_in_url, $lang_code);
                    $link_parts['parent_items_names'] = !empty($parent_item_names) ? join('/', $parent_item_names) . "/" : "";
                }

                $link_parts['name'] = fn_seo_get_name('a', $parced_query['page_id'], '', $company_id_in_url, $lang_code);
                $link_parts['extension'] = SEO_FILENAME_EXTENSION;

                fn_seo_parced_query_unset($parced_query, 'page_id');

            // Convert extended features links
            } elseif ($parced_query['dispatch'] == 'product_features.view' && !empty($parced_query['variant_id'])) {

                $link_parts['name'] = fn_seo_get_name('e', $parced_query['variant_id'], '', $company_id_in_url, $lang_code);
                $link_parts['extension'] = SEO_FILENAME_EXTENSION;

                fn_seo_parced_query_unset($parced_query, 'variant_id');

            // Convert companies links
            } elseif ($parced_query['dispatch'] == 'companies.view' && !empty($parced_query['company_id'])) {

                $link_parts['name'] = fn_seo_get_name('m', $parced_query['company_id'], '', $company_id_in_url, $lang_code);

                fn_seo_parced_query_unset($parced_query, 'company_id');

            // Other conversions
            } else {

                // Convert static links
                if (empty($link_parts['name'])) {
                    $name = fn_seo_get_name('s', 0, $parced_query['dispatch'], $company_id_in_url, $lang_code);
                    if (!empty($name)) {
                        $link_parts['name'] = $name;
                        fn_seo_parced_query_unset($parced_query);
                    } else {
                        // for non-rewritten links
                        $link_parts['path'] .= $index_script;
                        $link_parts['lang_code'] = '';
                        if (!empty($unset_lang_code)) {
                            $parced_query['sl'] = $unset_lang_code;
                        }
                    }
                }
            }
        } elseif ($seo_settings['seo_language'] != 'Y' && !empty($unset_lang_code)) {
            $parced_query['sl'] = $unset_lang_code;
        }
    }

    $url = join('', $link_parts);
    if (!empty($parced_query)) {
        $url .= '?' . http_build_query($parced_query) . $fragment;
    }

    return $url;
}

/**
 * Unset some keys in parced_query array
 * @param array $parts_array link parts
 * @param mixed $keys keys for unseting
 * @return string name for seo object
 */
function fn_seo_parced_query_unset(&$parts_array, $keys = array())
{
    $keys = is_array($keys) ? $keys : array($keys);
    $keys[] = 'dispatch';

    foreach ($keys as $v) {
        unset($parts_array[$v]);
    }

    return true;
}

function fn_seo_get_default_object_name($object_id, $object_type, $lang_code)
{
    $object_name = '';

    switch ($object_type) {
        case 'c':
            $object_name = db_get_field('SELECT category FROM ?:category_descriptions WHERE category_id = ?i AND lang_code = ?s', $object_id, $lang_code);
            break;

        case 'p':
            $object_name = db_get_field('SELECT product FROM ?:product_descriptions WHERE product_id = ?i AND lang_code = ?s', $object_id, $lang_code);
            break;

        case 'm':
            $object_name = db_get_field('SELECT company_description FROM ?:company_descriptions WHERE company_id = ?i AND lang_code = ?s', $object_id, $lang_code);
            break;

        case 'a':
            $object_name = db_get_field('SELECT page FROM ?:page_descriptions WHERE page_id = ?i AND lang_code = ?s', $object_id, $lang_code);
            break;

        case 'e':
            $object_name = db_get_field('SELECT variant FROM ?:product_feature_variant_descriptions WHERE variant_id = ?i AND lang_code = ?s', $object_id, $lang_code);
            break;
    }

    return $object_name;
}
