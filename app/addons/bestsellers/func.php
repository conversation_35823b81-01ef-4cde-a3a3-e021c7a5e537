<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>nev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

if (!defined('BOOTSTRAP')) { die('Access denied'); }

//
// Update sales stats for the product
//
function fn_bestsellers_change_order_status(&$status_to, &$status_from, &$order_info, &$force_notification, &$order_statuses)
{
    // Code utilisé
    // log_unused_code(__FILE__, __LINE__);

    $product_ids = db_get_fields("SELECT product_id FROM ?:order_details WHERE order_id = ?i GROUP BY product_id", $order_info['order_id']);

    if ($order_statuses[$status_to]['params']['inventory'] == 'D' && $order_statuses[$status_from]['params']['inventory'] == 'I') {
        $increase = true;
    } elseif ($order_statuses[$status_to]['params']['inventory'] == 'I' && $order_statuses[$status_from]['params']['inventory'] == 'D') {
        $increase = false;
    } else {
        return true;
    }

    foreach ($product_ids as $product_id) {
        $cids = db_get_fields("SELECT category_id FROM ?:products_categories WHERE product_id = ?i", $product_id);
        if (!empty($cids)) {
            foreach ($cids as $cid) {
                $c_amount = db_get_field("SELECT amount FROM ?:product_sales WHERE category_id = ?i AND product_id = ?i", $cid, $product_id);
                $c_amount = ($increase == true) ? ($c_amount + 1) : ($c_amount - 1);
                db_query("REPLACE INTO ?:product_sales (category_id, product_id, amount) VALUES (?i, ?i, ?i)", $cid, $product_id, $c_amount);
            }
        }
    }

    return db_query("DELETE FROM ?:product_sales WHERE amount = 0");
}

function fn_bestsellers_get_products(&$params, &$fields, &$sortings, &$condition, &$join, &$sorting, &$group_by)
{
    if (!empty($params['bestsellers'])) {
        $fields[] = 'SUM(?:product_sales.amount) as sales_amount';
        $sortings['sales_amount'] = 'sales_amount';
        $join .= ' LEFT JOIN ?:product_sales ON ?:product_sales.product_id = products.product_id AND ?:product_sales.category_id = products_categories.category_id ';
        $group_by = 'products.product_id';
        if (!empty($params['category_id'])) {
            $condition .= db_quote(" AND ?:product_sales.category_id = ?i", $params['category_id']);
        }
    }

    $sortings['bestsellers'] = '?:product_sales.amount';

    if (isset($params['sales_amount_from']) && fn_is_numeric($params['sales_amount_from'])) {
        $condition .= db_quote(' AND ?:product_sales.amount >= ?i', trim($params['sales_amount_from']));
    }

    if (isset($params['sales_amount_to']) && fn_is_numeric($params['sales_amount_to'])) {
        $condition .= db_quote(' AND ?:product_sales.amount <= ?i', trim($params['sales_amount_to']));
    }

    if ((in_array('sales', $params['extend']) && empty($params['bestsellers']))) {
        $join .= ' LEFT JOIN ?:product_sales ON ?:product_sales.product_id = products.product_id AND ?:product_sales.category_id = products_categories.category_id ';
    }

    return true;
}

function fn_bestsellers_update_product_post(&$product_data, &$product_id)
{
    if (!isset($product_data['sales_amount'])) {
        return false;
    }

    // Code utilisé
    // log_unused_code(__FILE__, __LINE__);

    db_query("DELETE FROM ?:product_sales WHERE product_id = ?i", $product_id);
    $cids = db_get_fields("SELECT category_id FROM ?:products_categories WHERE product_id = ?i", $product_id);
    if (!empty($cids)) {
        foreach ($cids as $category_id) {
            $_data = array (
                'category_id' => $category_id,
                'product_id' => $product_id,
                'amount' => $product_data['sales_amount']
            );

            db_query("REPLACE INTO ?:product_sales ?e", $_data);
        }
    }

    return true;
}
