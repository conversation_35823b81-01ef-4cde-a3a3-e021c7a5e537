<?php


//Add a parameter for product blocks, in order to search in subcategories
//NOTE: 'subcats' is a fn_get_products native parameter, do *NOT* change the name
$schema['products']['settings']['subcats'] = [
    'type' => 'checkbox',
    'default_value' => 'Y',
];
//Modify the lower limit of sales amount for bestsellers block
unset($schema['products']['content']['items']['fillings']['bestsellers']['params']['sales_amount_from']);

//Banners block
unset($schema['banners']['content']['items']['fillings']['newest']);
$schema['banners']['content']['items']['fillings']['category'] = [
    'params' => [
        'display_on_categories' => true,
        'request' => [
            'cid' => '%CATEGORY_ID%',
        ],
    ],
];
$schema['banners']['settings'] = [
    'limit' => [
        'type' => 'input',
        'default_value' => 10,
    ],
    'sort_by' => [
        'type'  => 'selectbox',
        'values' => [
            'random'    => 'random',
            'position'  => 'position',
            'timestamp' => 'creation_date',
            'name'      => 'name',
        ],
    ],
    'sort_order' => [
        'type'  => 'selectbox',
        'values' => [
            'asc' => 'asc',
            'desc'  => 'desc',
        ],
    ],
];

//Promotions clear products blocks cache
$schema['products']['cache']['update_handlers'][] = 'promotions';

return $schema;
