<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

$schema['blocks/products/products_thematic.tpl'] = array (
    'settings' => array(
        'display_illustration' => array(
            'type' => 'checkbox',
            'default_value' => 'Y'
        ),
        'display_links' => array(
            'type' => 'checkbox',
            'default_value' => 'Y'
        ),
        'nb_chars_title' => array(
            'type' => 'input',
            'default_value' => 25
        ),
        'description_height' => array(
            'type' => 'input',
            'default_value' => 48
        ),
        'show_price' => array (
            'type' => 'checkbox',
            'default_value' => 'Y'
        ),
        'enable_quick_view' => array (
            'type' => 'checkbox',
            'default_value' => 'N'
        ),
        'not_scroll_automatically' => array (
            'type' => 'checkbox',
            'default_value' => 'N'
        ),
        'scroller_direction' => array (
            'type' => 'selectbox',
            'values' => array (
                'up' => 'up',
                'down' => 'down',
                'left' => 'left',
                'right' => 'right'
            ),
            'default_value' => 'left'
        ),
        'speed' => array (
            'type' => 'selectbox',
            'values' => array (
                'slow' => 'slow',
                'normal' => 'normal',
                'fast' => 'fast'
            ),
            'default_value' => 'normal'
        ),
        'easing' => array (
            'type' => 'selectbox',
            'values' => array (
                'linear' => 'linear',
                'swing' => 'swing'
            ),
            'default_value' => 'swing'
        ),
        'pause_delay' =>  array (
            'type' => 'input',
            'default_value' => 3
        ),
        'item_quantity' =>  array (
            'type' => 'input',
            'default_value' => 1
        ),
        'thumbnail_width' =>  array (
            'type' => 'input',
            'default_value' => 80
        )
    ),
    'params' => array(
        'request' => array(
            'current_product_id' => '%PRODUCT_ID%',
        ),
    ),
    'bulk_modifier' => array (
        'fn_gather_additional_products_data' => array (
            'products' => '#this',
            'params' => array (
                'get_icon' => true,
                'get_detailed' => true,
                'get_options' => true,
            ),
        ),
    ),
);

$schema['blocks/brands/brands_scroller.tpl'] = array (
    'settings' => array(
        'not_scroll_automatically' => array (
            'type' => 'checkbox',
            'default_value' => 'N'
        ),
        'scroller_direction' => array (
            'type' => 'selectbox',
            'values' => array (
                'up' => 'up',
                'down' => 'down',
                'left' => 'left',
                'right' => 'right'
            ),
            'default_value' => 'left'
        ),
        'speed' => array (
            'type' => 'selectbox',
            'values' => array (
                'slow' => 'slow',
                'normal' => 'normal',
                'fast' => 'fast'
            ),
            'default_value' => 'normal'
        ),
        'easing' => array (
            'type' => 'selectbox',
            'values' => array (
                'linear' => 'linear',
                'swing' => 'swing'
            ),
            'default_value' => 'swing'
        ),
        'pause_delay' =>  array (
            'type' => 'input',
            'default_value' => 3
        ),
        'item_quantity' =>  array (
            'type' => 'input',
            'default_value' => 1
        ),
        'nb_items_horizontal' => array(
            'type' => 'input',
            'default_value' => 3
        ),
        'nb_items_vertical' => array(
            'type' => 'input',
            'default_value' => 2
        ),
        'thumbnail_width' =>  array (
            'type' => 'input',
            'default_value' => 80
        ),
        'delim_width' =>  array (
            'type' => 'input',
            'default_value' => 4
        )
    ),
);

return $schema;
