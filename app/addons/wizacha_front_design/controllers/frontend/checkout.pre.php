<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license	    Proprietary
 */


if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    if (('cart' == $mode || 'update_steps' == $mode )  && $_SESSION['edit_step'] == 'step_two') {

        if ($_SESSION['cart']['age_verification']) {
            if (empty($_REQUEST['user_data']['birthday'])) {
                fn_set_notification('E', __('error'), __('text_fill_the_mandatory_fields'));
                fn_save_post_data('user_data');
                return array(CONTROLLER_STATUS_REDIRECT, "checkout.checkout");
            }
        }
    }

    return;
}
