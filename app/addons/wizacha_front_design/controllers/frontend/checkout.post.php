<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license	    Proprietary
 */

use Wizacha\Marketplace\ReadModel\Basket;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

/*
 * This part is outside GET / POST control because step_four can be accessed through GET and POST
 * and this part is necessary in the 2 cases
 */
if ($_SESSION['edit_step'] == 'step_four' && 'checkout' == $mode) {
    $basketId = container()->get('session')->get('basketId');
    $basket =  container()->get('marketplace.basket.repository.read_model')->find($basketId);
    $ageVerification = $basket->getCscartData($auth['user_id'])['age_verification'];
    if (!\Wizacha\AgeVerification::check(
        fn_get_user_info($auth['user_id']),
        $ageVerification
    )
    ) {
        fn_set_notification('E', __('error'), __('w_age_verification_too_young', ['[age]'=>$ageVerification]));
        $_SESSION['edit_step'] = 'step_two';

        return [
            CONTROLLER_STATUS_REDIRECT,
            container()->get('router')->generate('basket_view')
        ];
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    if('update_shipping' == $mode){
        $_SESSION['cart']['calculate_shipping'] = true;
    }
    return;
}

if ('cart' == $mode) {
    log_unused_code(__FILE__, __LINE__);

    $product_groups = Tygh\Registry::get('view')->getTemplateVars('product_groups');

    array_walk(
        $product_groups,
        function(&$group)
        {
            uasort(
                $group['shippings'],
                function($shipp_a, $shipp_b)
                {
                    return ($shipp_a['rate'] - $shipp_b['rate']);
                }
            );
        }
    );

    Tygh\Registry::get('view')->assign('product_groups', $product_groups);
}

if('checkout' == $mode){
    if($_SESSION['edit_step'] == 'step_three'){
        $view  = Tygh\Registry::get('view');
        $completed_steps = $view->getTemplateVars('completed_steps');
        $edit_step = 'step_four';
        foreach(['step_one','step_two','step_three'] as $step){
            if(empty($completed_steps[$step])){
                $edit_step = $step;
            }
        }
        if($edit_step == 'step_three'){
            return array(CONTROLLER_STATUS_REDIRECT, "checkout.cart");
        }
        $_SESSION['edit_step'] = $edit_step;
        return array(CONTROLLER_STATUS_REDIRECT, "checkout.checkout");
    } elseif ($_SESSION['edit_step'] == 'step_two') {

        /** @var Basket $cart */
        $cart = container()->get('marketplace.basket.repository.read_model')->find($_SESSION['basketId']);
        $cart = $cart->getCscartData($auth['user_id']);
        if ($cart['age_verification']) {
            $profile_fields = \Tygh\Registry::get('view')->getTemplateVars('profile_fields');
            array_walk($profile_fields['C'], function (&$field) {
                if ('birthday' == $field['field_name']) {
                    $field['required'] = 'Y';
                }
            });

            \Tygh\Registry::get('view')->assign('profile_fields', $profile_fields);
        }
        $display_s_address = is_array($cart['chosen_shipping'])
                            && array_filter(array_map('\Wizacha\Shipping::isStandardDelivery', $cart['chosen_shipping']));

        \Tygh\Registry::get('view')->assign('display_s_address', $display_s_address);
    }
}

if('update_steps' == $mode || 'checkout' == $mode || 'cart' == $mode ){
    //Remove 'NOTICE' && 'text_shipping_rates_changed' notifications
    if (is_array($_SESSION['notifications'])) {
        $_SESSION['notifications'] = array_filter($_SESSION['notifications'], function ($n) {
            if(
                $n['message'] == __('text_shipping_rates_changed')
                || $n['type'] == 'N'
                || false !== stripos($n['message'] , __('text_applied_promotions'))
            )
            {
                return false;
            }
            return true;
        });
    }
}
