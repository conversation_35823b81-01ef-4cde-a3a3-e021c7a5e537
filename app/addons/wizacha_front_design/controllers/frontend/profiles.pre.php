<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($mode == 'update') {
        fn_restore_processed_user_password($_REQUEST['user_data'], $_POST['user_data']);
        $min_length = Tygh\Registry::get('settings.Security.min_admin_password_length');
        if (isset($_REQUEST['user_data']['password1']) && strlen($_REQUEST['user_data']['password1']) < $min_length) {
            fn_set_notification('E', __('error'), __('error_password_min_symbols', ['[number]'=>$min_length]));
            return array(CONTROLLER_STATUS_REDIRECT, "profiles.update");
        }

        $is_update = !empty($auth['user_id']);
        if ($is_update && isset($_REQUEST['user_data']['password1'])) {
            if (isset($_SESSION['auth']['w_recover_password'])) {
                unset($_SESSION['auth']['w_recover_password']);
            } else {
                $user_data = db_get_row("SELECT `salt`,`password` FROM ?:users WHERE user_id=?i", $auth['user_id']);
                if (!isset($_REQUEST['user_data']['old_password'])
                    || !fn_verify_password($_REQUEST['user_data']['old_password'], $user_data['salt'], $user_data['password'])) {
                    fn_set_notification('E', __('error'), __('w_incorrect_old_password'));
                    fn_save_post_data('user_data');
                    return array(CONTROLLER_STATUS_REDIRECT, "profiles.update");
                }
            }
        }
    }
    return;
}
