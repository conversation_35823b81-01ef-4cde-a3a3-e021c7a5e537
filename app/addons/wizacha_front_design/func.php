<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license        Proprietary
 */

if (!defined('BOOTSTRAP')) { die('Access denied'); }

/**
 * @param array $product_features
 * @return string
 */
function fn_product_get_brand($product_features)
{
    if (!is_array($product_features)) {
        return '';
    }

    list($brand) = \Wizacha\Product::getBrandInfos($product_features);

    return $brand;
}

/**
 * Returns if a specific category is a leaf or not
 * @param $category_id
 * @return bool
 */
function fn_w_is_leaf_category($category_id){
    static $cache = [];
    if(!isset($cache[$category_id])){
        $cache[$category_id] = is_null(
            db_get_field(
                "SELECT category_id FROM ?:categories WHERE parent_id=?i LIMIT 1",
                $category_id));
    }
    return $cache[$category_id];
}

function fn_w_get_vendor_files_allowed_extensions()
{
    return [
        'jpeg', 'jpg', 'gif', 'png', 'tiff', 'tif', 'bmp', //images
        'pdf',
    ];
}

/**
 * @param array|\Symfony\Component\HttpFoundation\File\UploadedFile $file
 * @return bool true if the given file seems valid for vendor registration
 */
function fn_w_check_apply_for_vendor_files($file): bool
{
    if (is_array($file)) {
        $ext = fn_get_file_ext($file['name']);
        $fileCurrentPath = $file['tmp_name'] ?? $file['path'];
    } else if ($file instanceof \Symfony\Component\HttpFoundation\File\UploadedFile) {
        $ext = $file->getClientOriginalExtension();
        $fileCurrentPath = $file->getRealPath();
    } else {
        throw new \TypeError("Expected array or UploadedFile");
    }
    $ext = strtolower($ext);

    $allowed_extensions = array_flip(fn_w_get_vendor_files_allowed_extensions());

    $allowed_types = array_flip(
        [
            'image/jpeg', 'image/gif', 'image/png', 'image/tiff', 'image/bmp','image/x-ms-bmp', 'image/webp', //images
            'application/pdf',
        ]
    );

    return
        isset($allowed_extensions[$ext])
        && isset($allowed_types[fn_get_mime_content_type($fileCurrentPath)])
    ;
}

