<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\BlockManager\Block;
use Tygh\Languages\Languages;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

//
// Get banners
//
function fn_get_banners($params, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $default_params = array(
        'items_per_page' => 0,
        'w_sort_by'     => $params['sort_by'],
        'w_sort_order'  => $params['sort_order'],
    );

    $params = array_merge($default_params, $params);

    $sortings = array(
        'position' => '?:banners.position',
        'timestamp' => '?:banners.timestamp',
        'name' => '?:banner_descriptions.banner',
    );

    $limit = '';

    if (!empty($params['limit'])) {
        $limit = db_quote(' LIMIT 0, ?i', $params['limit']);
    }

    $sorting = db_sort($params, $sortings, 'name', 'asc');

    $condition = (AREA == 'A') ? '' : " AND ?:banners.status = 'A' ";
    $condition .= fn_get_localizations_condition('?:banners.localization');

    if (!empty($params['item_ids'])) {
        $condition .= db_quote(' AND ?:banners.banner_id IN (?n)', explode(',', $params['item_ids']));
    }

    if (!empty($params['period']) && $params['period'] != 'A') {
        list($params['time_from'], $params['time_to']) = fn_create_periods($params);
        $condition .= db_quote(" AND (?:banners.timestamp >= ?i AND ?:banners.timestamp <= ?i)", $params['time_from'], $params['time_to']);
    }

    if (!empty($params['device']) && $params['device'] !== 'all') {
        $condition .= db_quote(" AND (device = 'all' OR device = ?s)", $params['device']);
    }

    if (isset($params['display_on_homepage'])) {
        $condition .= " AND display_on_homepage = '" . ($params['display_on_homepage'] ? 'Y' : 'N') . "'";
    }

    //Categories
    if (isset($params['display_on_categories'])) {
        if ($params['display_on_categories']) {
            $condition .= " AND display_on_categories = 'Y'";
            if ($params['cid']) {
                $cids = is_array($params['cid']) ? $params['cid'] : array($params['cid']);
                $pids = db_get_fields("SELECT id_path FROM ?:categories WHERE category_id IN (?n)", $cids);
                $pids = array_reduce($pids, function (array &$result, $path) {
                    return $result += array_flip(explode('/', $path));
                }, []);
                $pids = array_diff(array_flip($pids), $cids);

                $find_cat_set = array_map(function ($id) {
                    return db_quote(" FIND_IN_SET(?i, w_categories_path) ", $id);
                }, $cids);
                $find_pcat_set = array_map(function ($id) {
                    return db_quote(" FIND_IN_SET(?i, w_categories_path) ", $id);
                }, $pids);

                if (empty($find_pcat_set)) {
                    $condition .= db_quote(" AND (?p OR w_categories_path='')",
                        implode(' OR ', $find_cat_set));
                } else {
                    $condition .= db_quote(" AND (?p OR (w_subcats='Y' AND (?p)) OR w_categories_path='')",
                        implode(' OR ', $find_cat_set),
                        implode(' OR ', $find_pcat_set));
                }
            } else {
                $condition .= " AND w_categories_path=''";
            }
        } else {
            $condition .= " AND display_on_categories = 'N'";
        }
    }

    //Date checking
    if (isset($params['w_effective'])) {
        $condition .= \Tygh\Database::quote(" AND (?i BETWEEN timestamp AND w_timestamp_end)", $params['w_effective']);
    }

    //Random sorting
    if ('random' == $params['w_sort_by']){
        $sorting = ' ORDER BY RAND()';
        $params['sort_by'] = 'random';
    }

    $fields = array (
        '?:banners.banner_id',
        '?:banners.url',
        '?:banners.status',
        '?:banners.type',
        '?:banners.target',
        '?:banners.localization',
        '?:banners.timestamp',
        '?:banners.position',
        '?:banners.w_timestamp_end',
        '?:banners.w_categories_path',
        '?:banners.w_subcats',
        '?:banners.display_on_categories',
        '?:banners.display_on_homepage',
        '?:banners.device',
        '?:banner_descriptions.banner',
        '?:banner_descriptions.description',
        '?:banner_descriptions.lang_code',
    );

    $banners = db_get_array(
        "SELECT ?p FROM ?:banners " .
        "LEFT JOIN ?:banner_descriptions ON ?:banner_descriptions.banner_id = ?:banners.banner_id AND ?:banner_descriptions.lang_code = ?s " .
        "WHERE 1 ?p ?p ?p",
        implode(", ", $fields), $lang_code, $condition, $sorting, $limit
    );

    foreach ($banners as $k => $v) {
        $banners[$k]['main_pair'] = fn_get_image_pairs($v['banner_id'], 'promo', 'M', true, false, $lang_code);
    }

    //From statistics addon
    if (AREA == 'C' && !fn_is_empty($banners) && !defined('AJAX_REQUEST')) {
        foreach ($banners as $k => $v) {
            if ($v['type'] == 'T' && !empty($v['description'])) {
                $i = $pos = 0;
                $matches = array();
                while (preg_match('/href=([\'|"])(.*?)([\'|"])/i', $banners[$k]['description'], $matches, PREG_OFFSET_CAPTURE, $pos)) {
                    $banners[$k]['description'] = substr_replace($banners[$k]['description'], fn_url("statistics.banners?banner_id=$v[banner_id]&amp;link=" . $i++ , 'C'), $matches[2][1], strlen($matches[2][0]));
                    $pos = $matches[2][1];
                }
            } elseif (!empty($v['url'])) {
                $banners[$k]['url'] = "statistics.banners?banner_id=$v[banner_id]";
            }

            if ($v['banner_id'] !== null) {
                $banner_stat = array(
                    'banner_id' => $v['banner_id'],
                    'type' => 'V',
                    'timestamp' => TIME
                );

                db_query('INSERT INTO ?:stat_banners_log ?e', $banner_stat);
            }
        }
    }

    return array($banners, $params);
}

//
// Get specific banner data
//
function fn_get_banner_data($banner_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $status_condition = (AREA == 'A') ? '' : " AND ?:banners.status IN ('A', 'H') ";

    $fields = array (
        '?:banners.*',
        '?:banner_descriptions.*',
    );

    $banner = db_get_row(
        "SELECT ?p FROM ?:banners " .
        "LEFT JOIN ?:banner_descriptions ON ?:banner_descriptions.banner_id = ?:banners.banner_id AND ?:banner_descriptions.lang_code = ?s " .
        "WHERE ?:banners.banner_id = ?i ?p",
        implode(", ", $fields), $lang_code, $banner_id, $status_condition
    );

    if (!empty($banner)) {
        $banner['main_pair'] = fn_get_image_pairs($banner['banner_id'], 'promo', 'M', true, false, $lang_code);
    }

    return $banner;
}

/**
 * Deletes banner and all related data
 *
 * @param int $banner_id Banner identificator
 */
function fn_delete_banner_by_id($banner_id)
{
    if (!empty($banner_id) && fn_check_company_id('banners', 'banner_id', $banner_id)) {
        db_query("DELETE FROM ?:banners WHERE banner_id = ?i", $banner_id);
        db_query("DELETE FROM ?:banner_descriptions WHERE banner_id = ?i", $banner_id);
        db_query("DELETE FROM ?:stat_banners_log WHERE banner_id = ?i", $banner_id);

        Block::instance()->removeDynamicObjectData('banners', $banner_id);

        fn_delete_image_pairs($banner_id, 'promo');
    }
}

//
// Get banner name
//
function fn_get_banner_name($banner_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    // Code utilisé
    // log_unused_code(__FILE__, __LINE__);
    if (!empty($banner_id)) {
        return db_get_field("SELECT banner FROM ?:banner_descriptions WHERE banner_id = ?i AND lang_code = ?s", $banner_id, $lang_code);
    }

    return false;
}

function fn_update_banner($data, $banner_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    if (isset($data['timestamp'])) {
        $data['timestamp'] = fn_parse_date($data['timestamp']);
    }

    $data['localization'] = empty($data['localization']) ? '' : fn_implode_localizations($data['localization']);

    if (!empty($banner_id)) {
        db_query("UPDATE ?:banners SET ?u WHERE banner_id = ?i", $data, $banner_id);
        db_query("UPDATE ?:banner_descriptions SET ?u WHERE banner_id = ?i AND lang_code = ?s", $data, $banner_id, $lang_code);
    } else {
        $banner_id = $data['banner_id'] = db_query("REPLACE INTO ?:banners ?e", $data);

        foreach (Languages::getAll() as $data['lang_code'] => $v) {
            db_query("REPLACE INTO ?:banner_descriptions ?e", $data);
        }
    }

    fn_attach_image_pairs('banners_main', 'promo', $banner_id, $lang_code);

    return $banner_id;
}
