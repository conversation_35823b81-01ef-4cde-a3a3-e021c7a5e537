<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>nev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

if (!defined('BOOTSTRAP')) { die('Access denied'); }
// Code utilisé
// log_unused_code(__FILE__, __LINE__);

/**
 * @see \Wizacha\AppBundle\Controller\RootAssetController::sitemapXmlAction
 * @deprecated Keep it for BC (many customers put this URL in google webmaster settings. Maybe it supports 301 redirection?
 * @todo Put a log here and delete this file if there is no HTTP call
 */
if ($mode == 'view') {
    if (!empty($_REQUEST['page'])) {
        $page = intval($_REQUEST['page']);
        header('Location: /sitemap.xml?page='.$page, true, 301);
    } else {
        header('Location: /sitemap.xml', true, 301);
    }

    exit;
}
