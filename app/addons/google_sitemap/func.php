<?php
/***************************************************************************
 *                                                                          *
 *   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>nev    *
 *                                                                          *
 * This  is  commercial  software,  only  users  who have purchased a valid *
 * license  and  accept  to the terms of the  License Agreement can install *
 * and use this program.                                                    *
 *                                                                          *
 ****************************************************************************
 * PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
 * "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
 ****************************************************************************/

use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

function fn_google_sitemap_generate_link($object, $value, $languages)
{
    switch ($object) {
        case 'product':
            $link = 'products.view?product_id=' . $value;

            break;
        case 'category':
            $link = 'categories.view?category_id=' . $value;

            break;
        case 'page':
            $link = 'pages.view?page_id=' . $value;

            break;
        case 'extended':
            $link = 'product_features.view?variant_id=' . $value;

            break;
        case 'companies':
            $link = 'companies.view?company_id=' . $value;

            break;
    }

    $links = array();
    if (count($languages) == 1) {
        $links[$object."_".$value][key($languages)] = fn_url($link, 'C', 'https', (string) GlobalState::interfaceLocale());
    } else {
        foreach ($languages as $lang_code => $lang) {
            $links[$object."_".$value][$lang_code] = fn_url($link . '&sl=' . $lang_code, 'C', 'https', $lang_code);
        }
    }

    return $links;
}

function fn_google_sitemap_print_item_info($links, $lmod, $frequency, $priority)
{
    $item = '';
    $lang_local = GlobalState::fallbackLocale();
    foreach ($links as $link) {
        if (count($link) == 1) {
            $link = fn_html_escape(current($link));
            $item .= <<<ITEM
    <url>
    <loc>$link</loc>
    <xhtml:link
               rel="alternate"
               hreflang="$lang_local"
               href="$link"/>
        <lastmod>$lmod</lastmod>
        <changefreq>$frequency</changefreq>
        <priority>$priority</priority>
    </url>\n
ITEM;
        } else {
            $linksLang = "";
            $defaultLink = "";
            foreach ($link as  $lang_code => $_link) {
                if ($lang_code == $lang_local) {
                    $defaultLink = fn_html_escape($_link);
                }
                $_link = fn_html_escape($_link);
                $linksLang .= <<<ITEM
        <xhtml:link rel="alternate" hreflang="$lang_code" href="$_link"/>\n
ITEM;
            }
            $item .= <<<ITEM
    <url>
        <loc>$defaultLink</loc>
$linksLang        <lastmod>$lmod</lastmod>
        <changefreq>$frequency</changefreq>
        <priority>$priority</priority>
    </url>\n
ITEM;
        }
    }

    return $item;
}

function fn_google_sitemap_get_content($cache_path)
{
    fn_define('ITEMS_PER_PAGE', 500);
    fn_define('MAX_URLS_IN_MAP', 50000); // 50 000 is the maximum for one sitemap file
    fn_define('MAX_SIZE_IN_KBYTES', 10000); // 10240 KB || 10 Mb is the maximum for one sitemap file

    $sitemap_settings = Registry::get('addons.google_sitemap');
    $location = fn_url('',\Wizacha\Config::AREA_CLIENT);
    $lmod = date("Y-m-d", TIME);

    // HEAD SECTION
    $simple_head = <<<HEAD
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
            http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">


HEAD;

    $simple_foot = <<<FOOT

</urlset>
FOOT;

    $index_map_url = <<<HEAD
    <url>
        <loc>$location/</loc>
        <lastmod>$lmod</lastmod>
        <changefreq>$sitemap_settings[site_change]</changefreq>
        <priority>$sitemap_settings[site_priority]</priority>
    </url>\n
HEAD;

    // END HEAD SECTION

    // SITEMAP CONTENT
    $link_counter = 1;
    $file_counter = 1;

    fn_mkdir($cache_path);
    $file = fopen($cache_path . 'sitemap' . $file_counter . '.xml', "wb");

    fwrite($file, $simple_head . $index_map_url);

    $languages = db_get_hash_single_array("SELECT lang_code, name FROM ?:languages WHERE status = 'A'", array('lang_code', 'name'));

    if ($sitemap_settings['include_categories'] == "Y") {
        $categories = db_get_fields("SELECT category_id FROM ?:categories WHERE status = 'A' ");

        //Add the all active categories
        foreach ($categories as $category) {
            $links = fn_google_sitemap_generate_link('category', $category, $languages);
            $item = fn_google_sitemap_print_item_info($links, $lmod, $sitemap_settings['categories_change'], $sitemap_settings['categories_priority']);

            fn_google_sitemap_check_counter($file, $link_counter, $file_counter, $links, $simple_head, $simple_foot, $cache_path);

            fwrite($file, $item);
        }
    }

    if ($sitemap_settings['include_products'] == "Y") {
        $products = container()->get('marketplace.product.productservice')->getSearcheableProductIdInFront();
        $total    = count($products);
        $progress = 0;
        foreach ($products as $product_id) {

            $links = fn_google_sitemap_generate_link('product', $product_id, $languages);
            $item = fn_google_sitemap_print_item_info($links, $lmod, $sitemap_settings['products_change'], $sitemap_settings['products_priority']);

            fn_google_sitemap_check_counter($file, $link_counter, $file_counter, $links, $simple_head, $simple_foot, $cache_path);

            fwrite($file, $item);

            if (0 == ++$progress % 1000) {
                fn_set_progress('echo', "$progress/$total<br />");
                // EXPERIMENTAL: An attempt to fix an unreproductible memory leak with sitemap:generate symfony command.
                container()->get('doctrine.orm.entity_manager')->clear();
            }

        }
        unset($products);
    }

    if ($sitemap_settings['include_pages'] == "Y") {
        $pages = db_get_fields("SELECT page_id FROM ?:pages WHERE status = 'A' AND page_type = 'T' AND (use_avail_period = 'N' OR (use_avail_period = 'Y' AND NOW() BETWEEN FROM_UNIXTIME(avail_from_timestamp) AND FROM_UNIXTIME(avail_till_timestamp)))");

        //Add the all active pages
        foreach ($pages as $page) {
            $links = fn_google_sitemap_generate_link('page', $page, $languages);
            $item = fn_google_sitemap_print_item_info($links, $lmod, $sitemap_settings['pages_change'], $sitemap_settings['pages_priority']);

            fn_google_sitemap_check_counter($file, $link_counter, $file_counter, $links, $simple_head, $simple_foot, $cache_path);

            fwrite($file, $item);
        }
    }

    if ($sitemap_settings['include_extended'] == "Y") {
        $vars = db_get_fields(
            "SELECT ?:product_feature_variants.variant_id FROM ?:product_feature_variants " .
            "LEFT JOIN ?:product_features ON (?:product_feature_variants.feature_id = ?:product_features.feature_id) " .
            "WHERE ?:product_features.feature_type = 'E' AND ?:product_features.status = 'A'"
        );

        //Add the all active extended features
        foreach ($vars as $var) {
            $links = fn_google_sitemap_generate_link('extended', $var, $languages);
            $item = fn_google_sitemap_print_item_info($links, $lmod, $sitemap_settings['extended_change'], $sitemap_settings['extended_priority']);

            fn_google_sitemap_check_counter($file, $link_counter, $file_counter, $links, $simple_head, $simple_foot, $cache_path);

            fwrite($file, $item);
        }
    }

    if ($sitemap_settings['include_companies'] == 'Y') {
        $companies = db_get_fields("SELECT company_id FROM ?:companies WHERE status = 'A' ");

        if (!empty($companies)) {
            foreach ($companies as $company_id) {
                $links = fn_google_sitemap_generate_link('companies', $company_id, $languages);
                $item = fn_google_sitemap_print_item_info($links, $lmod, $sitemap_settings['companies_change'], $sitemap_settings['companies_priority']);

                fn_google_sitemap_check_counter($file, $link_counter, $file_counter, $links, $simple_head, $simple_foot, $cache_path);

                fwrite($file, $item);
            }
        }
    }

    fwrite($file, $simple_foot);
    fclose($file);

    if ($file_counter == 1) {
        fn_rename($cache_path . 'sitemap' . $file_counter . '.xml', $cache_path . 'sitemap.xml');
    } else {
        // Make a map index file
        $maps = '';

        $router = container()->get('router');
        $frontDomain = container()->getParameter('entrypoint.marketplace_domain');
        $routerHost = $router->getContext()->getHost();

        if (!empty($frontDomain) && $frontDomain !== $routerHost) {
            // Router host is not the front host (ex: wizacha admin)
            $router->getContext()->setHost($frontDomain);
        }

        for ($i = 1; $i <= $file_counter; $i++) {
            $name = $router->generate('sitemap', ['page' => $i], \Symfony\Component\Routing\Generator\UrlGeneratorInterface::ABSOLUTE_URL);
            $name = htmlentities($name);
            $maps .= <<<MAP
    <sitemap>
        <loc>$name</loc>
        <lastmod>$lmod</lastmod>
    </sitemap>\n
MAP;
        }
        $index_map = <<<HEAD
<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
            http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">

$maps
</sitemapindex>
HEAD;
        $file = fopen($cache_path . 'sitemap.xml', "wb");
        fwrite($file, $index_map);
        fclose($file);

        $router->getContext()->setHost($routerHost);
    }
}

function fn_google_sitemap_check_counter(&$file, &$link_counter, &$file_counter, $links, $header, $footer, $cache_path)
{
    $stat = fstat($file);
    if ((count($links) + $link_counter) > MAX_URLS_IN_MAP || $stat['size'] >= MAX_SIZE_IN_KBYTES * 1024) {
        fwrite($file, $footer);
        fclose($file);
        $file_counter++;
        $filename = $cache_path.'/sitemap' . $file_counter . '.xml';
        $file = fopen($filename, "wb");
        $link_counter = count($links);
        fwrite($file, $header);
    } else {
        $link_counter += count($links);
    }
}
