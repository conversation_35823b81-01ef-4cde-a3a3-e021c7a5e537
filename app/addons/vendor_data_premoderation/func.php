<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

function fn_vendor_data_premoderation_import_pre_moderation(&$import_data, &$pattern)
{
    if (Registry::get('runtime.company_id') && !empty($import_data)) {
        $company_data = Registry::get('runtime.company_data');
        $products_prior_approval = Registry::get('addons.vendor_data_premoderation.products_prior_approval');
        if ($products_prior_approval == 'all' || ($products_prior_approval == 'custom' && $company_data['pre_moderation'] == 'Y')) {
            foreach ($import_data as $id => &$data) {
                $data['approved'] = 'P';
            }
        }
    }
    return true;
}

function fn_vendor_data_premoderation_update_company_pre(&$company_data, &$company_id, &$lang_code)
{
    if (Registry::get('runtime.company_id')) {

        $orig_company_data = fn_get_company_data($company_id, $lang_code);
        $vendor_profile_updates_approval = Registry::get('addons.vendor_data_premoderation.vendor_profile_updates_approval');

        if ($orig_company_data['status'] == 'A' && ($vendor_profile_updates_approval == 'all' || ($vendor_profile_updates_approval == 'custom' && !empty($orig_company_data['pre_moderation_edit_vendors']) && $orig_company_data['pre_moderation_edit_vendors'] == 'Y'))) {

            $logotypes = fn_filter_uploaded_data('logotypes_image_icon'); // FIXME: dirty comparison

            // check that some data is changed
            if (array_diff_assoc($company_data, $orig_company_data) || !empty($logotypes)) {
                $company_data['status'] = 'P';
            }
        }
    }
}

function fn_change_approval_status($p_ids, $status)
{
    if (is_array($p_ids)) {
        db_query('UPDATE ?:products SET approved = ?s WHERE product_id IN (?a)', $status, $p_ids);
    } else {
        db_query('UPDATE ?:products SET approved = ?s WHERE product_id = ?i', $status, $p_ids);
    }

    return true;
}
