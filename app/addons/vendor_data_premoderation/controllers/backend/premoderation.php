<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!empty($dispatch_extra)) {
        if (!empty($_REQUEST['approval_data'][$dispatch_extra])) {
            $_REQUEST['approval_data'] = $_REQUEST['approval_data'][$dispatch_extra];
        }
    }
    $moderationService = container()->get('marketplace.moderation.moderation_service');
    // We want to exclude in_progress moderation products
    $_REQUEST['moderation'] = 'exclude';
    // We only want to moderate pending products
    $_REQUEST['approved'] = 'P';

    if ($mode == 'products_approval' && !empty($_REQUEST['approval_data'])) {
        $status = \Wizacha\Premoderation::getStatusByAction(
            \Wizacha\Registry::defaultInstance()->get(['runtime', 'action'])
        );
        $reason = $_REQUEST['approval_data']['reason_' . $status];

        fn_set_notification('N', __('notice'), __('status_changed'));

        $sendNotification = true === isset($_REQUEST['approval_data']['notify_user_' . $status])
            &&  'Y' === $_REQUEST['approval_data']['notify_user_' . $status];
        $moderationService->prepareModerateProduct(
            [$_REQUEST['approval_data']['product_id']],
            $status,
            $reason,
            $sendNotification
        );
    } elseif (in_array($mode, [
                \Wizacha\Premoderation::ACTION_APPROVE,
                \Wizacha\Premoderation::ACTION_DISAPPROVE,
                \Wizacha\Premoderation::ACTION_STANDBY
            ]) && !empty($_REQUEST['product_ids'])
    ) {
        $status = \Wizacha\Premoderation::getStatusByAction($mode);
        $reason = $_REQUEST['action_reason_' . $mode] ? : '';
        $sendNotification = true === isset($_REQUEST['action_notification_' . $mode])
            && 'Y' === $_REQUEST['action_notification_' . $mode];

        if (isset($_REQUEST['all_pages']) && $_REQUEST['all_pages'] === '1') {
            $_REQUEST['items_per_page'] = 500;
            $_REQUEST['page'] = 1;

            do {
                list($products,) = fn_get_products($_REQUEST, 0, (string) GlobalState::contentLocale());

                $productDatas = [];
                $productIds = [];

                foreach ($products as $product) {
                    $productIds[] = $product['product_id'];
                    $productDatas[$product['product_id']] = [
                        'product' => $product['product'],
                        'company_id' => $product['company_id'],
                        'current_status' => $product['status'],
                    ];
                }

                if (count($productIds) > 0) {
                    $moderationService->prepareModerateProduct($productIds, $status, $reason, $sendNotification);
                }
            } while(count($products) > 0);
        } else {
            $productIds = $_REQUEST['product_ids'];
            $productDatas = $_REQUEST['products_data'];

            if (count($productIds) > 0) {
                $moderationService->prepareModerateProduct($productIds, $status, $reason, $sendNotification);
            }
        }

        fn_set_notification('N', __('notice'), __('status_changed'));
    }

    if (!empty($dispatch_extra)) {
        if (!empty($_REQUEST['approval_data'][$dispatch_extra])) {
            $_REQUEST['approval_data'] = $_REQUEST['approval_data'][$dispatch_extra];
        }
    }

    if ($mode == 'options_approval' && !empty($_REQUEST['approval_data'])) {
        $status            = Registry::get('runtime.action') == 'approve' ? 'Y' : 'N';
        $send_notification = false;
        $reason            = '';
        if (isset($_REQUEST['approval_data']['notify_user_' . $status]) && $_REQUEST['approval_data']['notify_user_' . $status] == 'Y') {
            $send_notification = true;
            $reason            = $_REQUEST['approval_data']['reason_' . $status];
        }

        fn_w_premoderate_option([$_REQUEST['approval_data']], $status, $send_notification, $reason);

    } elseif (($mode == 'm_option_approve' || $mode == 'm_option_decline') && !empty($_REQUEST['options_ids'])) {
        if ($mode == 'm_option_approve') {
            $status            = 'Y';
            $reason            = $_REQUEST['action_reason_approved'];
            $send_notification = isset($_REQUEST['action_notification_approved']) && $_REQUEST['action_notification_approved'] == 'Y' ? true : false;
        } else {
            $status            = 'N';
            $reason            = $_REQUEST['action_reason_declined'];
            $send_notification = isset($_REQUEST['action_notification_declined']) && $_REQUEST['action_notification_declined'] == 'Y' ? true : false;
        }

        $options = array_reduce(
            $_REQUEST['options_ids'],
            function (&$result, $option_id) {
                $result[] = array_merge(['option_id' => $option_id], $_REQUEST['options_data'][$option_id]);
                return $result;
            }
        );
        fn_w_premoderate_option($options, $status, $send_notification, $reason);
    }
    return;
}

if ($mode == 'products_approval' && !Registry::get('runtime.company_id')) {
    $params = $_REQUEST;
    $params['extend'][] = 'companies';
    $params['moderation'] = 'exclude';

    list($products, $search) = fn_get_products($params, Registry::get('settings.Appearance.admin_products_per_page'), (string) GlobalState::contentLocale());

    Registry::get('view')->assign('products', $products);
    Registry::get('view')->assign('search', $search);
}

if ($mode == 'options_approval' && !Registry::get('runtime.company_id')) {
    $params                  = $_REQUEST;
    $params['premoderation'] = true;

    list($options, $search,) = fn_get_product_global_options(
        $params,
        Registry::get('settings.Appearance.admin_elements_per_page')
    );

    array_walk(
        $options,
        function (&$option) {
            $option['extend_category_path'] = array_map('fn_get_category_path', explode(',', $option['w_categories_path']));
        }
    );

    Registry::get('view')->assign('options', $options);
    Registry::get('view')->assign('search', $search);
}
