<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ne<PERSON>    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (Registry::get('runtime.company_id')) {
        $company_data = Registry::get('runtime.company_data');
        $products_prior_approval = Registry::get('addons.vendor_data_premoderation.products_prior_approval');
        $products_updates_approval = Registry::get('addons.vendor_data_premoderation.products_updates_approval');
        $isEnableAutoValidateProduct = container()->getParameter('feature.config_enable_auto_validate_product');
        $conditionPreModerationEdit = $products_updates_approval === 'all' || ($products_updates_approval === 'custom' && $company_data['pre_moderation_edit'] === 'Y');
        $conditionPreModeration = $products_prior_approval === 'all' || ($products_prior_approval === 'custom' && $company_data['pre_moderation'] === 'Y');
        $approved = ($isEnableAutoValidateProduct === true) ? 'Y' : 'P';
        $productId = (int) $_REQUEST['product_id'];

        if ($mode == 'update') {
            if ($productId > 0) {
                if ($isEnableAutoValidateProduct === false && $conditionPreModerationEdit === true) {
                    $_REQUEST['product_data']['approved'] = $_POST['product_data']['approved'] = 'P';
                } elseif ($isEnableAutoValidateProduct === false) {
                    unset($_REQUEST['product_data']['approved'], $_POST['product_data']['approved']);
                } else {
                    $_REQUEST['product_data']['approved'] = $_POST['product_data']['approved'] = 'Y';
                }
            } elseif ($isEnableAutoValidateProduct === false && $conditionPreModeration === true) {
                $_REQUEST['product_data']['approved'] = $_POST['product_data']['approved'] = 'P';
            }

        } elseif ($mode === 'm_update' && $productId > 0) {
            if ($conditionPreModerationEdit === true) {
                foreach ($_REQUEST['products_data'] as $key => $data) {
                    $_REQUEST['products_data'][$key]['approved'] = $_POST['products_data'][$key]['approved'] = $approved;
                }
            } else {
                foreach ($_REQUEST['products_data'] as $key => $data) {
                    if ($isEnableAutoValidateProduct === false) {
                        unset($_REQUEST['products_data'][$key]['approved'], $_POST['products_data'][$key]['approved']);
                    } else {
                        $_REQUEST['products_data'][$key]['approved'] = $_POST['products_data'][$key]['approved'] = 'Y';
                    }
                }
            }
        } elseif ($mode === 'm_add' && !empty($_REQUEST['products_data'])) {
            if ($conditionPreModeration === true) {
                foreach ($_REQUEST['products_data'] as $key => $data) {
                    $_REQUEST['products_data'][$key]['approved'] = $_POST['products_data'][$key]['approved'] = $approved;
                }
            }
        } elseif ($mode === 'update_file' && $productId > 0) {
            if ($isEnableAutoValidateProduct === false && $conditionPreModerationEdit === true) {
                fn_change_approval_status($productId, 'P');
            }
        }
    }
}
