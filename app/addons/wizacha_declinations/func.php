<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright    Copyright (c) Wizacha
 * @license        Proprietary
 */

use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Option\Event\OptionApproved;
use Wizacha\Marketplace\PIM\Option\Event\OptionRejected;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

/**
 * @param $category_id
 * @return array SQL code, FROM and WHERE statements list($from,$where) =  fn_w_get_request_cat_options($product_data)
 */
function fn_w_get_request_cat_options($category_id)
{
    $path = db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i", $category_id);
    $c_ids = explode('/', $path);
    $c_ids = array_map(function ($v) {
        return db_quote(' SELECT ?i AS cat_id', $v);
    }, $c_ids);
    $c_ids = implode(' UNION ', $c_ids);

    return [
        " ($c_ids) as paths, ?:product_options as po ",
        " FIND_IN_SET(paths.cat_id,po.w_categories_path) AND po.product_id=0 "
    ];
}

/**
 * @param  int $category_id Product data
 * @param  bool $raw
 * @param  bool $show_only_admin_options
 * @return array list of options for a categories
 */
function fn_w_get_category_options($category_id, $raw = false, $show_only_admin_options = false, string $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

    if (!$category_id) {
        return [];
    }
    list ($from, $where) = fn_w_get_request_cat_options($category_id);
    $company_condition = $show_only_admin_options ? ' AND po.company_id = 0 ' : fn_get_company_condition('company_id',true,'',true);
    $query = db_quote(
        "SELECT po.option_id, d.option_name, b.variant_id, c.variant_name, po.status, po.code
         FROM $from
          LEFT JOIN ?:product_option_variants as b on b.option_id = po.option_id AND b.status = 'A'
          LEFT JOIN ?:product_option_variants_descriptions AS c on c.variant_id = b.variant_id and c.lang_code = '$lang_code',
           ?:product_options_descriptions AS d
         WHERE $where AND d.option_id = po.option_id AND d.lang_code = '$lang_code' $company_condition
         ORDER BY po.position, b.position"
    );
    if (!$raw) {
        $result = \Tygh\Database::getMultiHash($query, ['option_id', 'variant_id'], $lang_code);
        $option_fields = array_flip(['option_name', 'status', 'option_id', 'code']);
        $variant_fields = array_flip(['variant_name', 'variant_id']);
        $result = array_map(
            function ($variants) use ($option_fields, $variant_fields) {
                $option             = array_intersect_key(reset($variants), $option_fields);

                if (count($variants) === 1 && reset($variants)['variant_id'] === null) {
                    // The LEFT JOIN returned one row with no value from product_option_variants => there is no variants
                    $variants = [];
                } else {
                    foreach ($variants as &$variant) {
                        $variant = array_intersect_key($variant, $variant_fields);
                    }
                }
                $option['variants'] = $variants;
                $option['is_system'] = is_string($option['code']);

                return $option;
            },
            $result
        );
    } else {
        $result = db_get_array($query);
    }

    return $result;
}

/**
 * Add some variants for a specific option. Already existing values are not inserted
 * @param int $option_id Identifier of a valid existing option
 * @param array $variants_name New variants name
 * @param string $lang_code Language code for searching existing values
 * @return array Identifiers of new/existing variants, in the same order than input.
 * Invalid input will have identifier 0.
 */
function fn_w_add_option_variants_by_name($option_id, array $variants_name, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
    $existing_names = Tygh\Database::getHash(
        'SELECT povd.variant_id AS variant_id, UPPER(variant_name) AS variant_name FROM ?:product_option_variants_descriptions AS povd
         JOIN ?:product_option_variants AS pov ON pov.variant_id = povd.variant_id
         WHERE option_id=?i AND lang_code = ?s',
        'variant_name', $option_id, $lang_code
    ) ? : [];
    $variant_ids = [];
    $variants_name = array_map('trim', $variants_name);
    foreach ($variants_name as $name) {
        if (empty($name)) {
            $variant_ids[] = 0;
            continue;
        }
        $uc_name = mb_strtoupper($name);
        if (isset($existing_names[$uc_name])) {
            $variant_ids[] = $existing_names[$uc_name]['variant_id'];
            continue;
        }

        $variant      = [
            'option_id' => $option_id,
        ];
        $variant_desc = [
            'variant_id'   => Tygh\Database::query("INSERT INTO ?:product_option_variants ?e", $variant),
            'variant_name' => $name,
        ];
        \Tygh\Languages\Helper::insertTranslations('product_option_variants_descriptions', $lang_code, $variant_desc);
        $variant_ids[] = $variant_desc['variant_id'];
        $existing_names[$uc_name] = $variant_desc;
    }
    return $variant_ids;
}

/**
 * Checks that a set of options are available for current company
 * @param array $option_ids
 * @return bool
 */
function fn_w_company_options_check(array $option_ids)
{
    return
        empty($option_ids)
        || count($option_ids) == db_get_memoized_field(
            "SELECT count(*) FROM ?:product_options WHERE option_id IN (?n) ?p",
            $option_ids,
            fn_get_company_condition('?:product_options.company_id', true, '', true)
        );
}

/**
 * @deprecated
 * Invert $exceptions (at beginning, $exceptions is the allowed variants)
 *
 * @param int $product_id
 * @param array $exceptions
 * @param bool $short_list
 */
function fn_wizacha_declinations_get_product_exceptions_post($product_id, &$exceptions, $short_list)
{
    $main_category = fn_w_get_product_main_category($product_id);
    $options = fn_w_get_category_options($main_category);

    if (empty($options)) {
        return;
    }

    //$combinations contains all variants
    foreach ($options as $option => $properties) {
        foreach ($properties['variants'] as $variant) {
            $combinations[] = array($option => (int)$variant['variant_id']);
        }
    }

    if ($short_list) {
        $exceptions = array_reduce(
            $exceptions,
            function ($result, $property) {
                $option = key($property);
                $variant = current($property);
                $result[$option][$variant] = true;
                return $result;
            }
        );
    } else {
        $exceptions = array_reduce(
            $exceptions,
            function ($result, $property) {
                $option = key($property['combination']);
                $variant = current($property['combination']);
                $result[$option][$variant] = true;
                return $result;
            }
        );
    }

    //Check if certain options are completely forbidden.
    $true_exceptions = [];
    $totaly_forbidden_option = [];

    foreach ($options as $option => $properties) {
        if (!isset($exceptions[$option])) {
            $true_exceptions[] = [$option => -2];
            $totaly_forbidden_option[$option] = true;
        }
    }

    array_walk(
        $combinations,
        function ($pair) use (&$true_exceptions, $exceptions, $totaly_forbidden_option) {
            $option = key($pair);
            $variant = current($pair);
            if (isset($totaly_forbidden_option[$option]) || (isset($exceptions[$option]) && isset($exceptions[$option][$variant]))) {
            } else {
                $true_exceptions[] = [$option => $variant];
            }
        }
    );

    //Complete $exceptions
    array_walk(
        $options,
        function (&$value) {
            $value = -1;
        }
    );

    foreach ($true_exceptions as $key => $variant) {
        $option = key($variant);
        $variant = current($variant);
        $true_exceptions[$key] = [$option => $variant] + $options;

    }
    $exceptions = $true_exceptions;

}

/**
 * Set tracking to 'O' if product is link with option, to 'B" else
 *
 * @param $product_id
 */
function fn_w_set_product_tracking($product_id)
{
    $links = db_get_field("SELECT 1 FROM ?:product_global_option_links WHERE product_id = ?i LIMIT 1", $product_id);
    $value = $links ? 'O' : 'B';
    db_query("UPDATE ?:products SET tracking = ?s WHERE product_id = ?i", $value, $product_id);
}

/**
 * @param int $product_id
 * @return int Category_id for the main_category of product
 */
function fn_w_get_product_main_category($product_id)
{
    return db_get_field("SELECT category_id FROM ?:products_categories WHERE product_id = ?i AND link_type = 'M'", $product_id);
}

/**
 * @param int $id
 * Rebuild all declinations authorization for a product.
 */
function fn_w_update_product_options_authorization($product_id, array $exceptions)
{
    /** @var ProductManager */
    $productManager = container()->get('cscart.product_manager');
    // invalidate productManager cache
    $productManager->invalidate($product_id);

    //Check product ownership
    if (!fn_company_products_check([$product_id])) {
        return;
    }

    $main_category = fn_w_get_product_main_category($product_id);
    $category_options = fn_w_get_category_options($main_category) ? : [];

    //Filter non-active options
    $category_options = array_filter(
        $category_options,
        function ($option) {
            return $option['status'] == 'A';
        }
    );

    //remove unallowed options
    $exceptions = array_intersect_key($exceptions, $category_options);

    //Remove and creates links between product and allowed options
    db_query("DELETE FROM ?:product_global_option_links WHERE product_id = ?i", $product_id);
    array_walk(
        $exceptions,
        function (&$variant, $option_id) use ($product_id) {
            if (!empty($variant)) {
                db_query(
                    "REPLACE INTO ?:product_global_option_links (option_id, product_id) VALUES (?i, ?i)",
                    $option_id,
                    $product_id
                );
            }
        }
    );
    fn_w_set_product_tracking($product_id);

    $option_without_exception = true;
    $new_exception = [];
    foreach ($exceptions as $option => $variants) {
        if (!empty($variants)) {
            foreach (array_keys($variants) as $variant) {
                $new_exception[] = [$option => $variant];
                $option_without_exception = false;
            }
        }
    }
    db_query("DELETE FROM ?:product_options_exceptions WHERE product_id = ?i", $product_id);

    if ($option_without_exception) {
        fn_set_notification('W', __('warning'), __('w_no_exceptions'));
    }
    foreach ($new_exception as $v) {
        $_data = array(
            'product_id' => $product_id,
            'option_id' => \key($v),
            'variant_id' => \current($v),
        );
        db_query("INSERT INTO ?:product_options_exceptions ?e", $_data);
    }

    fn_rebuild_product_options_inventory($product_id, 0);
}

/**
 * @param array $options_data
 * @param string $status
 * @param bool $notifications
 * @param string $reason
 *
 */
function fn_w_premoderate_option(array $options_data, $status, $notifications = false, $reason = '')
{
    $option_ids = array_column($options_data, 'option_id');
    if ('Y' == $status) {
        array_map(
            'fn_update_product_option',
            array_fill(0, count($options_data), ['status' => 'A', 'company_id' => 0]),
            $option_ids
        );
    } else {
        array_map('fn_delete_product_option', $option_ids);
    }

    fn_set_notification('N', __('notice'), __('status_changed'));

    if ($notifications) {

        $mails_data = array_reduce(
            $options_data,
            function (&$result, $option) {
                $result[$option['company_id']][] = $option['option_name'];
                return $result;
            }
        );

        if ($status === 'Y') {
            foreach ($mails_data as $companyId => $options) {
                container()->get('event_dispatcher')
                    ->dispatch(new OptionApproved($companyId, $options, $reason ?: null), OptionApproved::class);
            }
        } else {
            foreach ($mails_data as $companyId => $options) {
                container()->get('event_dispatcher')
                    ->dispatch(new OptionRejected($companyId, $options, $reason ?: null), OptionRejected::class);
            }
        }
    }
}

const W_SUBIMTTED_OPTION_CONDITION = ' AND company_id!=0 AND status!="A" ';
