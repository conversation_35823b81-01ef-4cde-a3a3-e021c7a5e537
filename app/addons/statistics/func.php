<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ne<PERSON>    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

if (!defined('BOOTSTRAP')) { die('Access denied'); }

function fn_statistics_search_by_objects(&$conditions, &$params)
{
    // Code utilisé
    // log_unused_code(__FILE__, __LINE__);
    if (!empty($conditions['products'])) {
        $obj = $conditions['products'];
        $params['products_found'] = db_get_field("SELECT COUNT(DISTINCT($obj[table].$obj[key])) FROM ?:products as $obj[table] $obj[join] WHERE $obj[condition]");
    }
}
