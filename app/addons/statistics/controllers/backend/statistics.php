<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>nev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/
use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

$report = !empty($_REQUEST['selected_section']) ? $_REQUEST['selected_section'] : (!empty($_REQUEST['report']) ? $_REQUEST['report'] : '');
$chart_type = (!empty($_REQUEST['chart_type']) ? $_REQUEST['chart_type'] : 'table');
$reports_group = empty($_REQUEST['reports_group']) ? 'general' : $_REQUEST['reports_group'];
$view = Registry::get('view');

if (!empty($chart_type)) {
    $view->assign('chart_type', $chart_type);
}

if (!empty($params)) {
    $view->assign('search', $params);
}

// [Page sections]
$sections = array();

Registry::set('navigation.dynamic.sections', $sections);
Registry::set('navigation.dynamic.active_section', empty($active_section) ? $reports_group : $active_section);

$view->assign('reports_group', $reports_group);
