<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ne<PERSON>    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    $discussion_settings = Registry::get('addons.discussion');
    $discussion_object_types = fn_get_discussion_objects();

    $suffix = '';
    if ($mode == 'add') {
        $suffix = '&selected_section=discussion';
        $post_data = $_REQUEST['post_data'];

        if (!empty($post_data['thread_id'])) {
            $object = fn_discussion_get_object_by_thread($post_data['thread_id']);
            if (empty($object)) {
                fn_set_notification('E', __('error'), __('cant_find_thread'));

                return array(CONTROLLER_STATUS_REDIRECT, $_REQUEST['redirect_url'] . $suffix);
            }
            $object_name = $discussion_object_types[$object['object_type']];
            $object_data = fn_get_discussion_object_data($object['object_id'], $object['object_type']);
            $ip = fn_get_ip();
            $post_data['ip_address'] = $ip['host'];
            $post_data['status'] = 'A';

            // Check if post is permitted from this IP address
            if (AREA != 'A' && !empty($discussion_settings[$object_name . '_post_ip_check']) && $discussion_settings[$object_name . '_post_ip_check'] == 'Y') {
                $is_exists = db_get_field("SELECT COUNT(*) FROM ?:discussion_posts WHERE thread_id = ?i AND ip_address = ?s", $post_data['thread_id'], $ip['host']);
                if (!empty($is_exists)) {
                    fn_set_notification('E', __('error'), __('error_already_posted'));

                    return array(CONTROLLER_STATUS_REDIRECT, $_REQUEST['redirect_url'] . $suffix);
                }
            }

            // Check if post needs to be approved
            if (AREA != 'A' && !empty($discussion_settings[$object_name . '_post_approval'])) {
                if ($discussion_settings[$object_name . '_post_approval'] == 'any' || ($discussion_settings[$object_name . '_post_approval'] == 'anonymous' && empty($auth['user_id']))) {
                    fn_set_notification('W', __('text_thank_you_for_post'), __('text_post_pended'));
                    $post_data['status'] = 'D';
                }
            }

            $post_data['timestamp'] = TIME;
            $post_data['user_id'] = $auth['user_id'];
            $post_data['post_id'] = db_query("INSERT INTO ?:discussion_posts ?e", $post_data);

            db_query("REPLACE INTO ?:discussion_messages ?e", $post_data);
            db_query("REPLACE INTO ?:discussion_rating ?e", $post_data);

            fn_update_discussion_in_readmodel($object);
        }
    }

    if ($mode == 'update') {
        if (!empty($_REQUEST['posts'])) {
            fn_update_discussion_posts($_REQUEST['posts']);
        }
    }

    $redirect_url = "discussion_manager.manage";
    if (!empty($_REQUEST['redirect_url'])) {
        $redirect_url = $_REQUEST['redirect_url'] . $suffix;
    }

    return array(CONTROLLER_STATUS_OK, $redirect_url);
}

if ($mode == 'view') {
    $data = fn_discussion_get_object_by_thread($_REQUEST['thread_id']);
    if (empty($data)) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    if (AREA != 'A') {
        // Check if user has an access for this thread
        if (fn_is_accessible_discussion($data, $auth) == false) {
            return array(CONTROLLER_STATUS_DENIED);
        }

        if ($data['object_type'] == 'E' && !empty($_REQUEST['post_id'])) {
            $post_pos = db_get_field("SELECT COUNT(*) FROM ?:discussion_posts WHERE thread_id = ?i AND post_id >= ?i AND status = 'A' ORDER BY timestamp DESC", $_REQUEST['thread_id'], $_REQUEST['post_id']);
            if (!empty($post_pos)) {
                $sets = Registry::get('addons.discussion');
                $discussion_object_types = fn_get_discussion_objects();
                $items_per_page = $sets[$discussion_object_types[$data['object_type']] . '_posts_per_page'];
                $page = ceil($post_pos / $items_per_page);
                if ((empty($_REQUEST['page']) && $page != 1) || (!empty($_REQUEST['page']) && $page != $_REQUEST['page'])) {
                    $_REQUEST['page'] = $page;
                }
                $_SESSION['discussion_post_id'] = $_REQUEST['post_id'];

                return array(CONTROLLER_STATUS_REDIRECT, fn_query_remove(Registry::get('config.current_url'), 'page', 'post_id'));
            }
        }
    }

    $show_discussion_crumb = true;
    if ($data['object_type'] == 'E') { // testimonials
        $show_discussion_crumb = false;
    }

    $discussion_object_data = fn_get_discussion_object_data($data['object_id'], $data['object_type']);

    fn_add_breadcrumb($discussion_object_data['description'], $discussion_object_data['url']);

    if ($show_discussion_crumb && AREA != 'A') {
        fn_add_breadcrumb(__('discussion'));
    }

    if (!empty($_SESSION['discussion_post_id'])) {
        Registry::get('view')->assign('current_post_id', $_SESSION['discussion_post_id']);
        unset($_SESSION['discussion_post_id']);
    }

    $discussion = fn_get_discussion($data['object_id'], $data['object_type'], true, $_REQUEST);

    Registry::get('view')->assign('search', $discussion['search']);
    Registry::get('view')->assign('object_id', $data['object_id']);
    Registry::get('view')->assign('title', $discussion_object_data['description']);
    Registry::get('view')->assign('object_type', $data['object_type']);
}

function fn_discussion_get_object_by_thread($thread_id)
{
    static $cache = array();

    if (empty($cache[$thread_id])) {
        $cache[$thread_id] = db_get_row("SELECT object_type, object_id, type FROM ?:discussion WHERE thread_id = ?i", $thread_id);
    }

    return $cache[$thread_id];
}
