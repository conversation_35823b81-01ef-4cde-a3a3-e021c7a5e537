<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>nev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    if ($mode == 'm_delete') {
        if (!empty($_REQUEST['delete_posts']) && is_array($_REQUEST['delete_posts'])) {
            $objects_infos = [];
            foreach ($_REQUEST['delete_posts'] as $p_id => $v) {
                $object_infos = fn_get_object_infos_discussion($p_id);

                if (!is_null($object_infos['object_id']) && !is_null($object_infos['object_type'])) {
                    if (!isset($objects_infos[$object_infos['object_type']])) {
                        $objects_infos[$object_infos['object_type']] = [];
                    }

                    $objects_infos[$object_infos['object_type']][] = $object_infos['object_id'];
                }

                fn_discussion_delete_post($p_id);
            }

            fn_mulitple_update_discussions_in_readmodel($objects_infos);
        }
    }

    return array(CONTROLLER_STATUS_OK);
}

if ($mode == 'delete') {
    if (!empty($_REQUEST['post_id'])) {
        $object_infos = fn_get_object_infos_discussion($_REQUEST['post_id']);
        fn_discussion_delete_post($_REQUEST['post_id']);

        fn_update_discussion_in_readmodel($object_infos);
    }
}

if ($mode == 'update') {
    $discussion = array();
    if (!empty($_REQUEST['discussion_type'])) {
        $discussion = fn_get_discussion(0, $_REQUEST['discussion_type'], true, $_REQUEST);
    }

    if (!empty($discussion) && $discussion['type'] != 'D' && Registry::ifGet('addons.discussion.home_page_testimonials', 'N') != 'D') {
        Registry::set('navigation.tabs.discussion', array (
            'title' => __('discussion_title_home_page'),
            'js' => true,
        ));
    } else {
        $discussion['is_empty'] = true;

    }

    Registry::get('view')->assign('discussion', $discussion);
}
