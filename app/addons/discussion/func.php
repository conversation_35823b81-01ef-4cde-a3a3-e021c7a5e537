<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Rhumsaa\Uuid\Uuid;
use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Review\ReviewTarget;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

function fn_get_discussion($object_id, $object_type, $get_posts = false, $params = array())
{
    static $cache = array();
    static $customer_companies = null;

    $_cache_key = $object_id . '_' . $object_type;

    if (empty($cache[$_cache_key])) {
        $cache[$_cache_key] = db_get_row(
            "SELECT thread_id, type, object_type FROM ?:discussion WHERE object_id = ?s AND object_type = ?s ",
            $object_id, $object_type
        );

        if (empty($cache[$_cache_key]) && $object_type == 'M') {
            $company_discussion_type = Registry::ifGet('addons.discussion.company_discussion_type', 'D');
            if ($company_discussion_type != 'D') {
                $cache[$_cache_key] = array('object_type' => 'M', 'object_id' => $object_id, 'type' => $company_discussion_type);

                $cache[$_cache_key]['thread_id'] = db_query('INSERT INTO ?:discussion ?e', $cache[$_cache_key]);
            }
        }

        if (!empty($cache[$_cache_key]) && AREA == 'C' && $object_type == 'M' && Registry::ifGet('addons.discussion.company_only_buyers', 'Y') == 'Y') {
            if (empty($_SESSION['auth']['user_id'])) {
                $cache[$_cache_key]['disable_adding'] = true;
            } else {
                if ($customer_companies === null) {
                    $customer_companies = db_get_hash_single_array(
                        'SELECT company_id FROM ?:orders WHERE user_id = ?i',
                        array('company_id', 'company_id'), $_SESSION['auth']['user_id']
                    );
                }
                if (empty($customer_companies[$object_id])) {
                    $cache[$_cache_key]['disable_adding'] = true;
                }
            }
        }
    }

    if (!empty($cache[$_cache_key]) && !isset($cache[$_cache_key]['posts']) && $get_posts == true) {
        $params['thread_id'] = $cache[$_cache_key]['thread_id'];
        $params['avail_only'] = (AREA == 'C'); // FIXME

        $discussion_object_types = fn_get_discussion_objects();

        list($cache[$_cache_key]['posts'], $cache[$_cache_key]['search']) = fn_get_discussion_posts($params, Registry::get('addons.discussion.' . $discussion_object_types[$cache[$_cache_key]['object_type']] . '_posts_per_page'));
    }

    $saved_post_data = fn_restore_post_data('post_data');
    if (!empty($saved_post_data)) {
        $cache[$_cache_key]['post_data'] = $saved_post_data;
    }

    return !empty($cache[$_cache_key]) ? $cache[$_cache_key] : false;
}

function fn_get_discussion_posts($params, $items_per_page = 0)
{
    // Set default values to input params
    $default_params = array (
        'page' => 1,
        'thread_id' => 0,
        'avail_only' => false,
        'random' => false,
        'items_per_page' => $items_per_page
    );

    $params = array_merge($default_params, $params);

    $thread_data = db_get_row(
        "SELECT thread_id, type, object_type, object_id FROM ?:discussion WHERE thread_id = ?i ",
        $params['thread_id']
    );

    if ($thread_data['type'] == 'D') {
        return array(array(), $params);
    }

    $join = $fields = '';

    if ($thread_data['type'] == 'C' || $thread_data['type'] == 'B') {
        $join .= " LEFT JOIN ?:discussion_messages ON ?:discussion_messages.post_id = ?:discussion_posts.post_id ";
        $fields .= ", ?:discussion_messages.message";
    }

    if ($thread_data['type'] == 'R' || $thread_data['type'] == 'B') {
        $join .= " LEFT JOIN ?:discussion_rating ON ?:discussion_rating.post_id = ?:discussion_posts.post_id ";
        $fields .= ", ?:discussion_rating.rating_value";
    }

    $thread_condition = fn_generate_thread_condition($thread_data);

    if ($params['avail_only'] == true) {
        $thread_condition .= " AND ?:discussion_posts.status = 'A'";
    }

    $limit = '';

    if (!empty($params['limit'])) {
        $limit = db_quote("LIMIT ?i", $params['limit']);

    } elseif (!empty($params['items_per_page'])) {
        $params['total_items'] = db_get_field("SELECT COUNT(*) FROM ?:discussion_posts WHERE $thread_condition", $params['thread_id']);
        $limit = db_paginate($params['page'], $params['items_per_page']);
    }

    $order_by = empty($params['random']) ? '?:discussion_posts.timestamp DESC' : 'RAND()';

    $posts = db_get_array(
        "SELECT ?:discussion_posts.* $fields FROM ?:discussion_posts $join "
        . "WHERE $thread_condition ORDER BY ?p $limit",
        $order_by
    );

    return array($posts, $params);
}

function fn_generate_thread_condition($thread_data)
{
    $thread_condition = '';

    if (AREA == 'C') {
        if ($thread_data['object_type'] == 'P') {
            $thread_condition = fn_generate_thread_condition_by_setting('product_share_discussion', $thread_data);
        } elseif ($thread_data['object_type'] == 'A') {
            $thread_condition = fn_generate_thread_condition_by_setting('page_share_discussion', $thread_data);
        } elseif ($thread_data['object_type'] == 'E') {
            $thread_condition = fn_generate_thread_condition_by_setting('testimonials_from_all_stores', $thread_data);
        } elseif ($thread_data['object_type'] == 'N') {
            $thread_condition = fn_generate_thread_condition_by_setting('news_share_discussion', $thread_data);
        }
    }

    if (empty($thread_condition)) {
        $thread_condition = db_quote("?:discussion_posts.thread_id = ?i", $thread_data['thread_id']);
    }

    return $thread_condition;
}

function fn_generate_thread_condition_by_setting($setting_name, $thread_data)
{
    if (!empty($thread_data['object_type']) && isset($thread_data['object_id'])) {
        if (Registry::ifGet('addons.discussion.' . $setting_name, 'N') == 'Y') {
            return  db_quote(
                "?:discussion_posts.thread_id IN (?a)",
                db_get_fields(
                    "SELECT thread_id FROM ?:discussion WHERE object_type = ?s AND object_id = ?s",
                    $thread_data['object_type'], $thread_data['object_id']
                )
            );
        }
    }

    return '';
}

function fn_delete_discussion($object_id, $object_type)
{
    $thread_id = db_get_field("SELECT thread_id FROM ?:discussion WHERE object_id IN (?n) AND object_type = ?s", $object_id, $object_type);

    if (!empty($thread_id)) {
        db_query("DELETE FROM ?:discussion_messages WHERE thread_id = ?i", $thread_id);
        db_query("DELETE FROM ?:discussion_posts WHERE thread_id = ?i", $thread_id);
        db_query("DELETE FROM ?:discussion_rating WHERE thread_id = ?i", $thread_id);
        db_query("DELETE FROM ?:discussion WHERE thread_id = ?i", $thread_id);

        return true;
    }

    return false;
}

// Mauvais nom de fonction
// Aurait probablement du être : fn_discussion_update_product_type
function fn_discussion_update_product_post(&$product_data, &$product_id)
{
    return fn_discussion_update_type($product_data, $product_id, 'P');
}

function fn_discussion_update_type($data, $id, $type)
{
    if (empty($data['discussion_type'])) {
        return false;
    }

    $discussion = array(
        'object_type' => $type,
        'object_id' => $id,
        'type' => $data['discussion_type']
    );

    fn_update_discussion($discussion);
}

//
// Get average rating
//
function fn_get_discussion_rating($rating_value)
{
    static $cache = array();

    if (!isset($cache[$rating_value])) {
        $cache[$rating_value] = array();
        $cache[$rating_value]['full'] = floor($rating_value);
        $cache[$rating_value]['part'] = $rating_value - $cache[$rating_value]['full'];
        $cache[$rating_value]['empty'] = 5 - $cache[$rating_value]['full'] - (($cache[$rating_value]['part'] == 0) ? 0 : 1);

        if (!empty($cache[$rating_value]['part'])) {
            if ($cache[$rating_value]['part'] <= 0.25) {
                $cache[$rating_value]['part'] = 1;
            } elseif ($cache[$rating_value]['part'] <= 0.5) {
                $cache[$rating_value]['part'] = 2;
            } elseif ($cache[$rating_value]['part'] <= 0.75) {
                $cache[$rating_value]['part'] = 3;
            } elseif ($cache[$rating_value]['part'] <= 0.99) {
                $cache[$rating_value]['part'] = 4;
            }
        }
    }

    return $cache[$rating_value];
}

//
// Get thread average rating
//
function fn_get_average_rating($object_id, $object_type)
{

    $discussion = fn_get_discussion($object_id, $object_type);

    if (empty($discussion) || ($discussion['type'] != 'R' && $discussion['type'] != 'B')) {
        return false;
    }

    return db_get_field("SELECT AVG(a.rating_value) as val FROM ?:discussion_rating as a LEFT JOIN ?:discussion_posts as b ON a.post_id = b.post_id WHERE a.thread_id = ?i AND b.status = 'A' AND a.rating_value > ?i", $discussion['thread_id'], 0);
}

function fn_get_discussion_object_data($object_id, $object_type, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $data = array();

    // product
    if ($object_type == 'P') {
        if (Uuid::isValid($object_id)) {
            $data['description'] = db_get_field("SELECT name FROM doctrine_multi_vendor_product_translations WHERE multi_vendor_product_id = ?s AND locale = ?s", $object_id, $lang_code);
            $data['multiVendorProductId'] = $object_id;
        } else {
            $data['description'] = db_get_field("SELECT product FROM ?:product_descriptions WHERE product_id = ?i AND lang_code = ?s", $object_id, $lang_code);
        }

        if (AREA == 'A') {
            $data['url'] = "products.update?product_id=$object_id&selected_section=discussion";
        } else {
            $data['url'] = "products.view?product_id=$object_id";
        }

    } elseif ($object_type == 'C') { // category
        $data['description'] = db_get_field("SELECT category FROM ?:category_descriptions WHERE category_id = ?i AND lang_code = ?s", $object_id, $lang_code);
        if (AREA == 'A') {
            $data['url'] = "categories.update?category_id=$object_id&selected_section=discussion";
        } else {
            $data['url'] = "categories.view?category_id=$object_id";
        }

    } elseif ($object_type == 'M') { // company
        $data['description'] = fn_get_company_name($object_id);
        if (AREA == 'A') {
            $data['url'] = "companies.update?company_id=$object_id&selected_section=discussion";
        } else {
            $data['url'] = "companies.view?company_id=$object_id";
        }

    // order
    } elseif ($object_type == 'O') {
        $data['description'] = '#'.$object_id;
        if (AREA == 'A') {
            $data['url'] = "orders.details?order_id=$object_id&selected_section=discussion";
        } else {
            $data['url'] = "orders.details?order_id=$object_id";
        }

    // page
    } elseif ($object_type == 'A') {
        $data['description'] = db_get_field("SELECT page FROM ?:page_descriptions WHERE page_id = ?i AND lang_code = ?s", $object_id, $lang_code);

        if (AREA == 'A') {
            $data['url'] = "pages.update?page_id=$object_id&selected_section=discussion";
        } else {
            $data['url'] = "pages.view?page_id=$object_id";
        }

    // Site layout/testimonials
    } elseif ($object_type == 'E') {
        $data['description'] = __('discussion_title_home_page');
        if (AREA == 'A') {
            $data['url'] = "discussion.update?discussion_type=E";
        } else {
            $data['url'] = '';
        }
    }

    return $data;
}

function fn_get_discussion_objects()
{
    static $discussion_object_types = array(
        'P' => 'product',
        'C' => 'category',
        'A' => 'page',
        'O' => 'order',
        'E' => 'home_page',
    );

    $discussion_object_types['M'] = 'company';

    $discussion_object_types['N'] = 'news';

    return $discussion_object_types;

}

//
// Clone discussion
//
function fn_clone_discussion($object_id, $new_object_id, $object_type)
{

    // Clone attachment
    $data = db_get_row("SELECT * FROM ?:discussion WHERE object_id = ?s AND object_type = ?s", $object_id, $object_type);

    if (empty($data)) {
        return false;
    }

    $old_thread_id = $data['thread_id'];
    $data['object_id'] = $new_object_id;
    unset($data['thread_id']);
    $thread_id = db_query("REPLACE INTO ?:discussion ?e", $data);

    // Clone posts
    $data = db_get_array("SELECT * FROM ?:discussion_posts WHERE thread_id = ?i", $old_thread_id);
    foreach ($data as $v) {
        $old_post_id = $v['post_id'];
        $v['thread_id'] = $thread_id;
        unset($v['post_id']);
        $post_id = db_query("INSERT INTO ?:discussion_posts ?e", $v);

        $message = db_get_row("SELECT * FROM ?:discussion_messages WHERE post_id = ?i", $old_post_id);
        $message['post_id'] = $post_id;
        $message['thread_id'] = $thread_id;
        db_query("INSERT INTO ?:discussion_messages ?e", $message);

        $rating = db_get_row("SELECT * FROM ?:discussion_rating WHERE post_id = ?i", $old_post_id);
        $rating['post_id'] = $post_id;
        $rating['thread_id'] = $thread_id;
        db_query("INSERT INTO ?:discussion_rating ?e", $rating);
    }

    return true;
}

function fn_is_accessible_discussion($data, &$auth)
{
    $access = false;

    if ($data['object_type'] == 'P') {//product
        $access = fn_get_product_data($data['object_id'], $auth, (string) GlobalState::interfaceLocale(), $field_list = '?:products.product_id', false, false, false);

    } elseif ($data['object_type'] == 'C') {//category
        $access = fn_get_category_data($data['object_id'], '', $field_list = '?:categories.category_id', false);

    } elseif ($data['object_type'] == 'M') {//company
        $access = fn_get_company_data($data['object_id']);

    } elseif ($data['object_type'] == 'O') {//order
        if (!empty($auth['user_id'])) {
            $access = db_get_field("SELECT order_id FROM ?:orders WHERE order_id = ?i AND user_id = ?i", $data['object_id'], $auth['user_id']);
        } elseif (!empty($auth['order_ids'])) {
            $access = in_array($data['object_id'], $auth['order_ids']);
        }

    } elseif ($data['object_type'] == 'A') {// page
        $access = fn_get_page_data($data['object_id'], (string) GlobalState::interfaceLocale());

    } elseif ($data['object_type'] == 'E') {// testimonials
        $access = true;
    }

    return !empty($access);
}

function fn_update_discussion($data)
{
    $result = null;

    if (!empty($data['for_all_companies'])) {
        if (isset($data['thread_id'])) {
            unset($data['thread_id']);
        }

        foreach (fn_get_all_companies_ids() as $company) {
            $data['company_id'] = $company;
            $result = db_replace_into('discussion', $data);
        }
    } else {
        $result = db_replace_into('discussion', $data);
    }

    return $result;
}

function fn_discussion_get_products(&$params, &$fields, &$sortings, &$condition, &$join, &$sorting, &$limit)
{
    if (!empty($params['rating'])) {
        $fields[] = 'avg(?:discussion_rating.rating_value) AS rating';
        $join .= db_quote(" INNER JOIN ?:discussion ON ?:discussion.object_id = CAST(products.product_id AS CHAR) AND ?:discussion.object_type = 'P'");

        $join .= db_quote(" INNER JOIN ?:discussion_rating ON ?:discussion.thread_id=?:discussion_rating.thread_id");
        $join .= db_quote(" INNER JOIN ?:discussion_posts ON ?:discussion_posts.post_id=?:discussion_rating.post_id AND ?:discussion_posts.status = 'A'");

        $params['sort_by'] = 'rating';
        $params['sort_order'] = 'desc';
        $sortings['rating'] = 'rating';
    }

    return true;
}

/**
 * Delete post bu identifier
 *
 * @param int $post_id Post identifier
 */
function fn_discussion_delete_post($post_id)
{
    db_query("DELETE FROM ?:discussion_messages WHERE post_id = ?i", $post_id);
    db_query("DELETE FROM ?:discussion_rating WHERE post_id = ?i", $post_id);
    db_query("DELETE FROM ?:discussion_posts WHERE post_id = ?i", $post_id);
}

/**
 * Get the object_type of the discussions
 * Get the object_id of the discussions for Algolia
 *
 * @param $post_id
 *
 * @return array
 */
function fn_get_object_infos_discussion($post_id) : array
{
    $object_type = db_get_field("SELECT d.object_type FROM ?:discussion_posts as dp LEFT JOIN ?:discussion as d ON dp.thread_id = d.thread_id WHERE dp.post_id = ?s", $post_id);

    $object_id = null;
    if (!is_null($object_type)) {
        $object_id = db_get_field("SELECT d.object_id FROM ?:discussion as d LEFT JOIN ?:discussion_posts as dp ON d.thread_id = dp.thread_id WHERE dp.post_id = ?i AND d.object_type = ?s", $post_id, $object_type);
    }

    return [
        'object_type' => $object_type,
        'object_id'   => $object_id,
    ];
}

/**
 * Update multiple posts at once
 * @param array $posts posts data
 * @return boolean always true
 */
function fn_update_discussion_posts($posts)
{
    if (!empty($posts) && is_array($posts)) {
        $threads = db_get_hash_single_array("SELECT post_id, thread_id FROM ?:discussion_posts WHERE post_id IN (?n)", array('post_id', 'thread_id'), array_keys($posts));
        $messages_exist = db_get_fields("SELECT post_id FROM ?:discussion_messages WHERE post_id IN (?n)", array_keys($posts));
        $rating_exist = db_get_fields("SELECT post_id FROM ?:discussion_rating WHERE post_id IN (?n)", array_keys($posts));
        fn_delete_notification('company_access_denied');

        foreach ($posts as $p_id => $data) {
            db_query("UPDATE ?:discussion_posts SET ?u WHERE post_id = ?i", $data, $p_id);
            if (in_array($p_id, $messages_exist)) {
                db_query("UPDATE ?:discussion_messages SET ?u WHERE post_id = ?i", $data, $p_id);
            } else {
                $data['thread_id'] = $threads[$p_id];
                $data['post_id'] = $p_id;
                db_query("INSERT INTO ?:discussion_messages ?e", $data);
            }

            if (in_array($p_id, $rating_exist)) {
                db_query("UPDATE ?:discussion_rating SET ?u WHERE post_id = ?i", $data, $p_id);
            } else {
                $data['thread_id'] = $threads[$p_id];
                $data['post_id'] = $p_id;
                db_query("INSERT INTO ?:discussion_rating ?e", $data);
            }
        }
    }

    return true;
}

/**
 * Gets available rating values with titles
 *
 * @return array Rating values list
 */
function fn_get_discussion_ratings()
{
    $rates = array (
        5 => __("excellent"),
        4 => __("very_good"),
        3 => __("average"),
        2 => __("fair"),
        1 => __("poor")
    );

    return $rates;
}

function fn_mulitple_update_discussions_in_readmodel(array $objects): void
{
    foreach ($objects as $objectType => $objectIds) {
        switch ($objectType) {
            case 'M':
                \Wizacha\Events\Config::dispatch(
                    CompanyEvents::UPDATED,
                    IterableEvent::fromArray($objectIds)
                );
                break;
            case 'P' :
                \Wizacha\Events\Config::dispatch(
                    \Wizacha\Product::EVENT_UPDATE,
                    (new \Wizacha\Events\IterableEvent)->setArray($objectIds)
                );
                break;
            default:
                throw new \Exception('Invalid type');
        }
    }
}

function fn_update_discussion_in_readmodel(array $object): void
{
    $normalizedObjectType = \strtoupper($object['object_type']);

    switch ($normalizedObjectType) {
        case ReviewTarget::COMPANY:
            \Wizacha\Events\Config::dispatch(
                CompanyEvents::UPDATED, //\Wizacha\Marketplace\Company\CompanyEvents
                IterableEvent::fromElement($object['object_id'])
            );
            break;
        case ReviewTarget::PRODUCT:
            \Wizacha\Events\Config::dispatch(
                \Wizacha\Product::EVENT_UPDATE,
                (new \Wizacha\Events\IterableEvent)->setElement($object['object_id'])
            );
            break;
        default:
            throw new \Exception('Invalid type');
    }
}
