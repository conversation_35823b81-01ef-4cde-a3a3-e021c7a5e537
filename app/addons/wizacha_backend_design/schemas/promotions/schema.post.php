<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

$schema['conditions'] = array_intersect_key($schema['conditions'],[
    //catalog
    'price'         => true,
    'products'      => true,
    'categories'      => true,
    //cart
    'coupon_code'   => true,
    'number_of_usages'  => true,
    'once_per_customer' => true,
]);

$schema['conditions']['coupon_code']['operators'] = ['eq'];
$schema['conditions']['price']['zones'] =
    $schema['conditions']['categories']['zones'] =
        $schema['conditions']['products']['zones'] =
            ['catalog'];

$schema['bonuses'] = array_intersect_key($schema['bonuses'],[
    //catalog
    'product_discount'  => true,
    //cart
    'discount_on_products'  => true,
]);

$schema['bonuses']['product_discount']['discount_bonuses'] =
    $schema['bonuses']['discount_on_products']['discount_bonuses'] =
        ['by_percentage', 'to_fixed', 'by_fixed'];

//only allow leaves to be selected
$schema['conditions']['categories']['picker_props']['params']['w_leaves_only'] = true;

return $schema;