<?php




$schema = [
    'section' => 'categories',
    'name' => __('w_links_vendor_categories'),
    'pattern_id' => 'link_categories',
    'key' => ['vendor_category', 'category_id'],
    'order' => 1,
    'table' => 'w_links_vendor_categories',
    'condition' => [
        //Can't use fn_company_condition : it return no condition if the user is admin.
        'conditions' => ['company_id' => \Tygh\Registry::get('runtime.company_id')]
    ],
    'pre_processing' => [
        'add_company_id' => [
            'function' => '\Wizacha\Exim\Category::addCompanyId',
            'args' => ['$import_data', '$pattern', '#company_id'],
            'import_only' => true,
        ],
    ],
    'import_process_data' => [
        'check_category_id' => [
            'function' => '\Wizacha\Exim\Category::checkId',
            'args' => array('$object', '$skip_record', '$jobId'),
            'import_only' => true,
        ]
    ],
    'options' => [
        'lang_code' => [
            'title' => 'language',
            'type' => 'languages',
            'default_value' => array(DEFAULT_LANGUAGE),
        ],
        'category_delimiter' => [
            'title' => 'category_delimiter',
            'description' => 'text_category_delimiter',
            'type' => 'input',
            'default_value' => '///'
        ],
    ],
    'export_fields' => [
        //Category should be an alternative key, but alternative key must be in same table.
        'External Category' => [
            'db_field' => 'vendor_category',
            'required' => true,
            'alt_key' => true,
        ],
        'Wizacha Category' => [
            'db_field' => 'category_id',
            'process_get' => ['fn_get_category_path', '#this', '#lang_code','@category_delimiter'],
            'convert_put' => ['\Wizacha\Exim\Category::getIdByFullPath', '#this', '@category_delimiter', '#lang_code'],
            'required' => true,
            'alt_key' => true,
        ],
    ],
];

return $schema;
