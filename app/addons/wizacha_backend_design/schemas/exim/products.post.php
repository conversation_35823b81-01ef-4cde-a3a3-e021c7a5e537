<?php

use Wizacha\Product;

unset($schema['export_fields']['Product id']);
unset($schema['export_fields']['List price']);
unset($schema['export_fields']['Min quantity']);
unset($schema['export_fields']['Shipping freight']);
unset($schema['export_fields']['Date added']);
unset($schema['export_fields']['Files']);
unset($schema['export_fields']['Ship downloadable']);
unset($schema['export_fields']['Inventory tracking']);
unset($schema['export_fields']['Out of stock actions']);
unset($schema['export_fields']['Free shipping']);
unset($schema['export_fields']['Feature comparison']);
unset($schema['export_fields']['Zero price action']);
unset($schema['export_fields']['Thumbnail']);
unset($schema['export_fields']['Detailed image']);
unset($schema['export_fields']['Meta keywords']);
unset($schema['export_fields']['Meta description']);
unset($schema['export_fields']['Page title']);
unset($schema['export_fields']['Box size']);
unset($schema['export_fields']['Localizations']);
unset($schema['export_fields']['Vendor']);
unset($schema['export_fields']['Options']);
unset($schema['export_fields']['Items in box']);
unset($schema['export_fields']['SEO name']);
unset($schema['export_fields']['Search words']);
unset($schema['export_fields']['Secondary categories']);
unset($schema['export_fields']['Detailed image URL']);
unset($schema['options']['images_path']);
unset($schema['options']['files_path']);
unset($schema['options']['delete_files']);

$schema['import_skip_db_processing'] = true;

$schema['export_fields']['Product code']['process_put'] = array(
    '\\Wizacha\\Exim\\Product::handleCSVLineWithCounter',
    '#key',
    '#row',
    '#options',
    '#lang_code',
    '#counter',
    \Wizacha\Async\Config::FIELD_PROGRESS_ID
);

$schema['export_fields']['Image URL'] = [
    'process_get' => array('fn_w_exim_get_images', '#key', '@w_images_delimiter'),
    'linked' => false, // this field is not linked during import-export
    'multilang' => true,
];

$schema['export_fields']['Alt Text'] = [
    'process_get' => array('getImagesAlt', '#key', '@w_images_delimiter', '#lang_code'),
    'linked' => false, // this field is not linked during import-export
    'multilang' => true,
];

$schema['export_fields']['Code postal annonce'] = [
    'process_get' => array('Wizacha\Exim\Product::getPostal', '#key'),
    'linked' => false, // this field is not linked during import-export
    'multilang' => true,
];

$schema['export_fields']['Ville annonce'] = [
    'process_get' => array('Wizacha\Exim\Product::getCity', '#key'),
    'linked' => false, // this field is not linked during import-export
    'multilang' => true,
];

$schema['export_fields']['Latitude'] = [
    'process_get' => array('Wizacha\Exim\Product::getLatitude', '#key'),
    'linked' => false, // this field is not linked during import-export
    'multilang' => true,
];

$schema['export_fields']['Longitude'] = [
    'process_get' => array('Wizacha\Exim\Product::getLongitude', '#key'),
    'linked' => false, // this field is not linked during import-export
    'multilang' => true,
];

unset($schema['export_fields']['Image URL']['export_only']);
unset($schema['export_fields']['Alt Text']['export_only']);

$schema['export_fields']['Supplier Ref'] = [
    'db_field' => 'w_supplier_ref',
    'multilang' => true,
];

$schema['export_fields']['Green tax'] = [
    'db_field' => 'w_green_tax',
    'multilang' => true,
];

$schema['export_fields']['Condition'] = [
    'db_field' => 'w_condition',
    'multilang' => true,
];

$schema['export_fields']['Brand'] = [
    'process_get' => array('fn_w_exim_get_brand', '#key'),
    'linked' => false, // this field is not linked during import-export
    'multilang' => true,
];

$schema['export_fields']['Features']['process_get'] = [
    '\\Wizacha\\Exim\\Product::getFeatures',
    '#key',
    '@features_delimiter',
    '#lang_code',
];

$schema['export_fields']['Shippings'] = [
    'process_get' => array('\\Wizacha\\Exim\\Product::getShippings', '#key', '@shipping_price_delimiter'),
    'linked' => false,
    'multilang' => true,
];

$schema['options']['shipping_price_delimiter'] = [
    'title' => 'w_shipping_price_delimiter',
    'description' => 'w_text_shipping_price_delimiter',
    'type' => 'input',
    'default_value' => '///'
];

$schema['pre_processing']['set_default_values'] = [
        'function' => 'fn_product_set_default_values',
        'args' => array('$import_data','$pattern'),
        'import_only' => true,
];

$schema['pre_processing']['create_progress'] = [
    'function' => '\Wizacha\Exim\Product::createProgressFromData',
    'args' => array('$import_data','$pattern', '$'.\Wizacha\Config::FIELD_FILE),
    'import_only' => true,
];

$schema['options']['w_images_delimiter'] = [
    'title' => 'w_images_delimiter',
    'description' => 'w_text_images_delimiter',
    'type' => 'input',
    'default_value' => '####',
];


//Regroup product and combination in one CSV:
$schema['references']['product_options_inventory'] = [
    'reference_fields' => array('product_id' => '#key'),
    'join_type' => 'LEFT'
];

$schema['export_fields']['Combination'] = [
    'table' => 'product_options_inventory',
    'db_field' => 'combination',
    'process_get' => array('\Wizacha\Cscart\Exim::exim_get_product_combination', '#key', '#this', '#lang_code'),
    'multilang' => true,

    //On export, premoderation remove this value to be exported.
    'linked' => false,
];

//On export, premoderation build this value. (conflict in name_field  between products.product_code and product_options_inventory.product_code)
$schema['export_fields']['Combination Code'] = [
    'linked' => false,
    'multilang' => true,
];

$schema['export_fields']['Combination Supplier Ref'] = [
    'linked' => false,
    'multilang' => true
];

$schema['export_fields']['Combination image URL'] = [
    'process_get' => array('fn_w_exim_declination_get_images', '#key', '#row', '@w_images_delimiter'),
    'linked' => false,
    'multilang' => true,
];

$schema['export_fields']['Combination Alt Text'] = [
    'process_get' => array('getDeclinationImagesAlt', '#key', '#row', '#lang_code'),
    'linked' => false,
    'multilang' => true,
];

$schema['export_fields']['Price']['process_get'] = ['\Wizacha\Exim\Product::getPrice', '#row', '@price_dec_sign_delimiter'];
$schema['export_fields']['Affiliate link']['process_get'] = ['\Wizacha\Exim\Product::getAffiliateLink', '#row'];
$schema['export_fields']['Quantity']['process_get'] = ['\Wizacha\Exim\Product::getQuantity', '#row'];

$schema['export_pre_moderation']['all_in_one_csv'] = [
    'function' => '\Wizacha\Exim\Product::preModerateExport',
    'args' => array('$pattern', '$export_fields'),
];

return $schema;
