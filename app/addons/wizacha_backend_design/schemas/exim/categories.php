<?php




$schema = [
    'section' => 'categories',
    'name' => __('categories'),
    'pattern_id' => 'categories',
    'key' => ['category_id'],
    'order' => 0,
    'table' => 'categories',
    'notes' => ['w_exim_categories_note'],
    'import_skip_db_processing' => true,
    'references' => [
        'category_descriptions' => [
            'reference_fields' => ['category_id' => '#key', 'lang_code' => '#lang_code'],
            'join_type' => 'LEFT'
        ],
    ],
    'pre_processing' => [
        'add_category_id' => [
            'function' => '\Wizacha\Exim\Category::addCategoryId',
            'args' => array('$import_data'),
            'import_only' => true,
        ],
    ],
    'options' => [
        'lang_code' => [
            'title' => 'language',
            'type' => 'languages',
            'default_value' => array(DEFAULT_LANGUAGE),
        ],
        'category_delimiter' => [
            'title' => 'category_delimiter',
            'description' => 'text_category_delimiter',
            'type' => 'input',
            'default_value' => \Wizacha\Exim\Category::DEFAULT_SEPARATOR
        ],
    ],
    'export_fields' => [
        //Category should be an alternative key, but alternative key must be in same table.
        'Category' => [
            'db_field' => 'category',
            'process_get' => ['fn_get_category_path', '#key', '#lang_code' ,'@category_delimiter'],
            'process_put' => ['\Wizacha\Exim\Category::put', '#this', '@category_delimiter', '#row', '#lang_code', '#jobId'],
            'required' => true,
            'table' => 'category_descriptions',
            'multilang' => true,
        ],
        'Language' => array(
            'table' => 'category_descriptions',
            'db_field' => 'lang_code',
            'type' => 'languages',
            'multilang' => true,
        ),
        'Image' => array(
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Category::getImageUrl', '#key'],
            'export_only' => true
        ),
        //HOTFIX : If category_id is required, the line is not passed if the field is empty.
        //If the field is not required and there is not alternative key, the schema is invalid.
        //So : category_id is his own alternative key and is create on preprocessing for import.
        'Category ID' => [
            'db_field' => 'category_id',
            'alt_key' => true,
            'alt_field' => 'category_id',
            'multilang' => true,
        ],
        'Status' => [
            'db_field' => 'status',
            'multilang' => true,
        ],
        'Products in category' => [
            'db_field' => 'product_count',
            'multilang' => true,
        ],
        'Age verification' => [
            'db_field' => 'age_verification',
            'multilang' => true,
        ],
        'Age limit' => [
            'db_field' => 'age_limit',
            'multilang' => true,
        ],
        'Parent age verification' => [
            'db_field' => 'parent_age_verification',
            'export_only' => true,
            'multilang' => true,
        ],
        'Parent age limit' => [
            'db_field' => 'parent_age_limit',
            'export_only' => true,
            'multilang' => true,
        ],
        'Description' => [
            'db_field' => 'description',
            'table' => 'category_descriptions',
            'multilang' => true,
        ],
        'External id' => [
            'db_field' => 'external_id',
            'multilang' => true,
        ],
        'Meta keywords' => [
            'db_field' => 'meta_keywords',
            'table' => 'category_descriptions',
            'multilang' => true,
        ],
        'Meta description' => [
            'db_field' => 'meta_description',
            'table' => 'category_descriptions',
            'multilang' => true,
        ],
        'Page title' => [
            'db_field' => 'page_title',
            'table' => 'category_descriptions',
            'multilang' => true,
        ],
        'Old Category' => [
            'linked' => false,
            'import_only' => true,
        ],
        'Position' => [
            'db_field' => 'position',
            'multilang' => true,
        ],
        'Delete' => [
            'linked' => false,
            'import_only' => true,
        ],
        'SEO Name' => [
            'process_get' => array('fn_seo_get_name', 'c', '#key'),
            'db_field' => 'seo_name',
            'linked' => false,
            'multilang' => true,
        ],
    ],
];

return $schema;
