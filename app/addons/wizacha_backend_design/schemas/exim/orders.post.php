<?php

if (AREA !== 'A') {
    unset($schema['export_fields']['E-mail']);
}

unset($schema['export_fields']['IP address']);
unset($schema['export_fields']['Payment surcharge']);
unset($schema['export_fields']['Taxes']);
unset($schema['export_fields']['Coupons']);
unset($schema['export_fields']['Invoice ID']);
unset($schema['export_fields']['Credit memo ID']);
unset($schema['export_fields']['Billing: state']);
unset($schema['export_fields']['Shipping: state']);
unset($schema['export_fields']['Extra fields']);
unset($schema['export_fields']['Web site']);
unset($schema['export_fields']['Fax']);
unset($schema['export_fields']['Phone']);
unset($schema['export_fields']['Payment information']);
unset($schema['export_fields']['Subtotal']);
unset($schema['export_fields']['Payment ID']);
unset($schema['export_fields']['User ID']);
unset($schema['export_fields']['Tax exempt']);
unset($schema['export_fields']['Language']);
unset($schema['export_fields']['Notes']);

$schema['export_fields']['Invoice Number'] = [
    'db_field' => 'w_invoice_number'
];

if (AREA !== 'A') {
    $schema['condition']['conditions']['status'] = \Wizacha\OrderStatus::getVendorDisplayedStatuses();
}

$schema['export_fields']['Products'] = [
    'process_get' => ['\\Wizacha\\Exim\\Order::getProducts', '#key'],
    'export_only' => true,
    'linked' => false,
];

$schema['export_fields']['Products comments'] = [
    'process_get' => ['\\Wizacha\\Exim\\Order::getProductsComments', '#key'],
    'export_only' => true,
    'linked' => false,
];

$schema['export_fields']['Status'] = [
    'process_get' => [
        '\\Wizacha\\Exim\\Order::getStatus', '#key',
        container()->getParameter('feature.activate_workflow_translation'),
    ],
    'export_only' => true,
    'linked' => false,
];

$schema['export_fields']['Taxes'] = [
    'process_get' => ['\\Wizacha\\Exim\\Order::getTaxes', '#key'],
    'export_only' => true,
    'linked' => false,
];

$schema['export_fields']['Shipping'] = [
    'process_get' => ['\\Wizacha\\Exim\\Order::getShipping', '#key'],
    'export_only' => true,
    'linked' => false,
];

return $schema;
