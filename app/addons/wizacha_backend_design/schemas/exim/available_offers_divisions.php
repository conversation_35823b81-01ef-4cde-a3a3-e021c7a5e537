<?php
/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

$schema = [
    'section' => 'available_offers_divisions',
    'name' => __('available_offers_divisions'),
    'pattern_id' => 'available_offers_divisions',
    'key' => ['code'],
    'options' => [],
    'import_skip_db_processing' => true,
    'pre_processing' => [
        'put_division_blacklists' => [
            'function' => '\Wizacha\Exim\Division::put',
            'args' => ['$import_data', '$user', '$jobId', '$lineNumber'],
            'import_only' => true,
        ],
    ],
    'export_fields' => [
        'Divisions included' => [
            'alt_key' => true,
        ],
        'Divisions excluded' => [
        ],
    ],
];

return $schema;
