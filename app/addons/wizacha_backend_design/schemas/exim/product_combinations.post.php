<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

unset($schema['export_fields']['Language']);
unset($schema['export_fields']['Combination code']);
unset($schema['export_fields']['Combination Supplier Ref']);
unset($schema['export_fields']['Product ID']);
$schema['notes'][] = 'w_help_import_product_combination';
$schema['export_fields']['Quantity'] = $schema['export_fields']['Amount'];

unset($schema['export_fields']['Amount']);
$schema['import_get_primary_object_id'] = [
    'fill_product_id' => [
        'function' => 'fn_w_exim_import_get_primary_object_id_product_combination',
        'args' => ['$pattern', '$alt_keys', '$object', '$skip_get_primary_object_id'],
        'import_only' => true,
    ]
];
$schema['export_fields']['Parent Product code'] = [
    'process_get' => ['fn_w_exim_get_parent_product_code', '#key'],
    'linked' => false,
    'required' => true,
    'alt_key' => true,
    'alt_field' => 'product_id'
];

$schema['export_fields']['Product code'] = [
    'db_field' => 'product_code',
    'required' => false,
];

$schema['pre_processing'] = [
    'set_default_values' => array(
        'function' => 'fn_w_product_combinations_set_default_values',
        'args' => array('$import_data','$pattern'),
        'import_only' => true,
    ),
];

//Suppression of product_combinations post-processing : product_combinations CSV is to disappear

$schema['export_fields']['Price'] = [
    'required' => false,
    'db_field' => 'w_price',
];

$schema['export_fields']['Combination']['process_put'] = [
    'fn_exim_put_product_combination',
    '%Product ID%',
    '%Parent Product code%',
    '%Product code%',
    '#this',
    '%Quantity%',
    '#counter',
    '%Price%'
];

return $schema;
