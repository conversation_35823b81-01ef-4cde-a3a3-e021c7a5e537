<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */


/*
 * API and backend take all keys, CSV take the key if true and the csv value if exist
 */
$schema['shipping']['send'] = [
    'w_description' => ['value' => 'description'],
];
$schema['features']['send'] = [
    'feature_code' => ['value' => 'code'],
];
$schema['inventory']['send'] = [
    'w_price' => ['value' => 'price'],
    'product_code' => ['value' => 'combination_code']
];
$schema['product']['send'] = [
    'w_green_tax' => ['value' => 'green_tax'],
    'w_supplier_ref' => ['value' => 'supplier_ref'],
    'inventory' => [
        'recursive' => true,
        'is_list' => true,
        'object_name' => 'inventory'
    ],
    'w_condition' => ['value' =>\Wizacha\Product::CONDITION_API_NAME],
    'shippings' => [
        'recursive' => true,
        'is_list' => true,
        'object_name' => 'shipping'
    ]
];
$schema['order']['send'] = [
    'w_invoice_number' => ['value' => 'invoice_number'],
    'w_last_status_change' => ['value' => 'last_status_change'],
    'firstname' => ['value' => 'customer_firstname'],
    'lastname' => ['value' => 'customer_lastname'],
    'shipping' => [
        'recursive' => true,
        'is_list' => true,
        'object_name' => 'shipping'
    ],
    'products' => [
    'recursive' => true,
    'is_list' => true,
    'object_name' => 'order_product'
]
];

$schema['returns']['send']['rma_number'] = [
    'value' => 'number',
];

$schema['returns']['send']['extra'] = [
    'value' => 'number',
];

array_walk(
    $schema,
    function (&$data_type) {
        $data_type['get'] = [];
        foreach ($data_type['send'] as $from => $data) {
            if (isset($data['value'])) {
                $data_type['get'][$data['value']] = $data;
                $data_type['get'][$data['value']]['value'] = $from;
            } else {
                $data_type['get'][$from] = $data;
            }
        }
    }
);

$get_rma_number_from_extra = function ($extra) {
    $extras = unserialize($extra);
    if (isset($extras['w_rma_number'])) {
        return $extras['w_rma_number'];
    }
    return '';
};

$schema['returns']['send']['number'] = [
    'callback' => $get_rma_number_from_extra,
];

$convert_taxes = function ($array) {
    return array_map(
        function ($value) {
            return (int)$value;
        },
        $array
    );
};

$schema['product']['send']['tax_ids'] = [
    'callback' => $convert_taxes
];

$schema['inventory']['send']['combination'] = [
    'callback' => 'fn_w_get_product_options_by_combination'
];

$extract_option_name_and_variant_from_extra = function ($extra) {
    $combinations = [];
    if (is_array($extra['product_options_value'])) {
        $combinations = array_reduce(
            $extra['product_options_value'],
            function (&$result, $variant) {
                $result[] = array_intersect_key($variant, array_flip(['option_name', 'variant_name']));
                return $result;
            }
        );
    }
    return [
        'combinations' => $combinations,
    ];
};

$schema['order_product']['send']['extra'] = [
    'callback' => $extract_option_name_and_variant_from_extra
];

$schema['order_product']['send']['product_code'] = [
    'value' => 'selected_code'
];

$schema['order_product']['send']['w_supplier_ref'] = [
    'value' => 'supplier_ref'
];

$schema['order_product']['send']['product'] = [
    'value' => 'product_name'
];

$amount_in_shipment = function ($products) {
    return array_map(
        'intval',
        $products
    );
};

$schema['shipment']['send']['products'] = [
    'callback' => $amount_in_shipment
];

$schema['shipping']['send']['rates'] = [
    'callback' => 'fn_w_simplify_rates'
];

$schema['shipping']['get']['rates'] = [
    'callback' => 'fn_w_complexify_rates'
];

return $schema;
