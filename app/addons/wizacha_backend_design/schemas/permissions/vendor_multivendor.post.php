<?php

$schema['controllers']['currencies']    = ['permissions' => false ];
$schema['controllers']['countries']     = ['permissions' => false ];
$schema['controllers']['destinations']  = ['permissions' => false ];
$schema['controllers']['states']        = ['permissions' => false ];
$schema['controllers']['pages']         = ['permissions' => false ];
$schema['controllers']['product_filters']   = ['permissions' => false];
$schema['controllers']['taxes']   = ['permissions' => false];

// Products
$schema['controllers']['products'] = [
    'permissions'   => false,
    'modes' => [
        'clone'    => ['permissions' => true],
        'manage'    => ['permissions' => true],
        'update'    => ['permissions' => true],
        'm_update'  => ['permissions' => ['POST' => true]],
        'add'       => ['permissions' => true],
        'export_range'  => ['permissions' => true],
        'update_file'   => ['permissions' => true],
        'delete_file'   => ['permissions' => true],
        'get_file'   => ['permissions' => true],
        'picker'     => ['permissions' => true],
        'picker_json' => ['permissions' => true],
        'view'       => ['permissions' => true],
        'delete'     => ['permissions' => true],
        'm_delete'   => ['permissions' => true],
        'vendor_delete_all' => ['permissions' => true],
        'get_attachment' => ['permissions' => true],
        'delete_attachment' => ['permissions' => true],
        'product_templates_infos' => ['permissions' => true],
        'related_products_add' => ['permissions' => true],
        'related_products_picker' => ['permissions' => true],
        'related_products_delete' => ['permissions' => true],
    ],
];

// Product Features
$schema['controllers']['product_features']  = [
    'permissions' => false,
    'modes' => [
        'manage' => ['permissions' => ['GET'=>true]],
        'get_feature_variants_list' => ['permissions' => ['GET'=>true]],
        'get_variants' => ['permissions' => ['GET'=>true]],
    ],
];

// Product Options
$schema['controllers']['product_options']   = [
    'permissions' => false,
    'modes' => [
        'vendor_submit' => ['permissions' => ['POST'=>true]],
        'inventory' => ['permissions' => true],
        'change_declination_type' => ['permissions' => ['POST'=>true]],
        'exceptions' => ['permissions' => true],
        'update_combinations' => ['permissions' => true],
    ],
];

// Product Price Tiers
$schema['controllers']['price_tiers'] = [
    'permissions' => true,
    'modes' => [
        'update' => ['permissions' => true],
    ],
];

// Profiles
$schema['controllers']['profiles']   = [
    'permissions' => false,
    'modes' => [
        'permissions' => false,
        'manage' => [
            'param_permissions' => [
                'user_type' => [
                    'V' => true,
                ],
                'default_permission' => false,
            ],
        ],
        'add' => [
            'permissions' => [
                'POST' => true,
            ],
            'condition' => [
                'operator' => 'or',
                'function' => ['fn_check_permission_add_vendor_admin'],
            ],
        ],
        'update' => [
            'permissions' => false,
            'condition' => [
                'operator' => 'or',
                'function' => ['fn_check_permission_manage_own_profile'],
                ]
        ],
        'delete' => [
            'permissions' => false,
            'condition' => [
                'operator' => 'or',
                'function' => ['fn_check_permission_manage_own_profile'],
            ]
        ],
        'reNewApiKey' => [
            'permissions' => true,
        ]
    ],
];

// Promotions
$schema['controllers']['promotions']   = [
    'permissions' => false,
    'modes' => [
        'manage'    => ['permissions' => true],
        'update'    => ['permissions' => true],
        'dynamic'   => ['permissions' => true],
        'delete'    => ['permissions' => true],
        'm_delete'  => ['permissions' => ['POST' => true]],
        'add'       => ['permissions' => true],
    ],
];

$schema['controllers']['shipments']   = [
    'permissions' => false,
    'modes' => [
        'add' => [
            'permissions' => true,
        ],
        'manage' => [
            'permissions' => true,
        ],
        'details' => [
            'permissions' => true,
        ]
    ],
];

$schema['controllers']['elf_connector_s3']   = [
    'permissions' => false,
    'modes' => [
        'images' => [
            'permissions' => true,
        ],
    ],
];

$schema['controllers']['notifications'] = [
    'permissions' => true,
];

$schema['controllers']['companies']['modes']['w_automated_feeds'] = ['permission'=>true];

/*EXIM permissions*/

$schema['controllers']['exim']['modes']['export']['section']['orders'] = ['permission'=>true];
$schema['controllers']['exim']['modes']['export']['section']['subscribers'] = ['permission'=>false];
$schema['controllers']['exim']['modes']['import']['section']['subscribers'] = ['permission'=>false];
$schema['controllers']['exim']['modes']['export']['section']['related_products'] = ['permission'=>true];
$schema['controllers']['exim']['modes']['export']['section']['product_attributes'] = ['permission'=>true];
$schema['controllers']['exim']['modes']['store_layout'] = ['permissions' => false] ;
$schema['controllers']['exim']['modes']['delete_layout'] = ['permissions' => false] ;

$schema['export']['sections']['orders'] = ['permission'=>true];
$schema['export']['sections']['related_products'] = ['permission'=>true];
$schema['export']['sections']['subscribers'] = ['permission'=>false];
$schema['import']['sections']['subscribers'] = ['permission'=>false];
$schema['export']['sections']['categories'] = ['permission'=>false];
$schema['import']['sections']['categories'] = ['permission'=>false];
$schema['export']['sections']['product_attributes'] = ['permission'=>true];

$schema['controllers']['accounting']['modes']['view'] = ['permissions'=>true];

// Add help.view
// Remove test after help is created.
if (\Tygh\Registry::get('addons.wizacha_backend_design_patch_3.deploy_help') != 'Y') {
    $schema['controllers']['help']['modes']['view'] = ['permissions'=>true];
}

return $schema;
