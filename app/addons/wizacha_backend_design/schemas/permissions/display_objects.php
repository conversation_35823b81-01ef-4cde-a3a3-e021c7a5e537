<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */


/*
 * API and backend take all keys, CSV take the key if true and the csv value if exist
 */
$schema['product'] = [
    'approved' => true,
    'avail_since' => true,
    'company_id' => true,
    'category_ids' => true,
    'crossed_out_price' => true,
    'free_shipping' => true,
    'full_description' => true,
    'image_pairs' => [
        'recursive' => true,
        'is_list' => true,
        'object_name' => 'image_pair',
        'api' => true,
    ],
    'inventory' =>[
        'recursive' => true,
        'is_list' => true,
        'object_name' => 'inventory',
        'api' => true,
    ],
    'is_edp' => true,
    'main_category' => true,
    'main_pair' => [
        'recursive' => true,
        'object_name' => 'image_pair',
        'api' => true,
    ],
    'product' => true,
    'product_code' => true,
    'product_id' => true,
    'product_options' => true,
    'short_description' => true,
    'status' => true,
    'tax_ids' =>true,
    'timestamp' => true,
    'unlimited_download' => true,
    'updated_timestamp' => true,
    'weight' => true,
    'w_condition'   => true,
    'w_green_tax' => true,
    'w_supplier_ref' => true,
    'video' => true,
    'attachments' => true,
    'affiliate_link' => true,
    'product_template_type' => true,
    'infinite_stock' => true,
    'max_price_adjustment' => true,
    'priceTiers' => true,
    'is_subscription' => true,
    'is_renewable' => true,
    'seo_name'=>true,
    'page_title'=>true,
    'meta_description'=>true,
    'meta_keywords'=>true,
];

$schema['promotions'] = [
    'promotion_id'  => true,
    'to_date'       => true,
    'from_date'     => true,
    'status'        => true,
    'name'          => true,
];

$schema['inventory'] = [
    'combination' => true,
    'amount' => true,
    'w_price' => true,
    'product_code' => true,
    'crossed_out_price' => true,
    'affiliate_link' => true,
    'priceTiers' => true,
    'supplier_reference' => true,
];

$schema['tax'] = [
    'tax_id' => true,
    'tax' => true,
    'priority' => true,
    'regnumber' => true,
    'rate_value' => true,
    'status' => true,
];

$schema['image_pair'] = [
    'detailed' => [
        'recursive' => true,
        'object_name' => 'pair_fields',
        'api' => true,
    ],
];

$schema['pair_fields'] = [
    'image_path' => true,
    'altText'    => true,
    'position'   => true,
];

$schema['order'] = [
    'b_address'      => true,
    'b_address_2'    => true,
    'b_city'         => true,
    'b_country'      => true,
    'b_title'     => true,
    'b_firstname'    => true,
    'b_lastname'     => true,
    'b_company'      => true,
    'b_phone'        => true,
    'b_zipcode'      => true,
    'discount'       => true,
    'email'          => true,
    'is_customer_professional' => true,
    'customer_company' => true,
    'customer_legal_identifier' => true,
    'customer_intra_european_community_vat' => true,
    'customer_job_title'      => true,
    'customer_account_comment'        => true,
    'customer_external_identifier' => true,
    'need_shipping'  => true,
    'notes'          => true,
    'details'        => true,
    'order_id'       => true,
    'company_id'     => true,
    'company_name'   => true,
    'products'       => [
        'recursive' => true,
        'is_list' => true,
        'object_name' => 'order_product',
        'api' => true,
    ],
    'promotions'     => true,
    's_address'      => true,
    's_address_2'    => true,
    's_company'      => true,
    's_city'         => true,
    's_country'      => true,
    's_firstname'    => true,
    's_lastname'     => true,
    's_title'     => true,
    's_phone'        => true,
    's_zipcode'      => true,
    's_pickup_point_id' => true,
    'shipment_ids'   => true,
    'shipping'       => [
        'recursive' => true,
        'is_list' => true,
        'object_name' => 'shipping',
        'api' => true,
    ],
    'shipping_cost'  => true,
    'status'         => true,
    'w_last_status_change' => true,
    'tax_subtotal'   => true,
    'taxes'          => true,
    'timestamp'      => true,
    'total'          => true,
    'user_id'        => true,
    'basket_id'      => true,
    'w_invoice_number' => true,
    'do_not_create_invoice' => true,
    'invoice_date' => true,
    'decline_reason' => true,
    'firstname' => true,
    'lastname' => true,
    'subscription_id' => true,
    'is_paid' => true,
    'carriage_paid' => true,
    'refund_status' => true,
    'balance' => true,
    'extra' => true,
    'transaction_reference' => true,
    'parent_order_id' => true,
];

$schema['order_product'] = [
    'amount'        => true,
    'discount'      => true,
    'extra'         => true,
    'item_id'       => true,
    'price'         => true,
    'max_price_adjustment' => true,
    'product_id'    => true,
    'product'    => true,
    'product_code'  => true,
    'shipped_amount' => true,
    'shipment_amount' => true,
    'w_supplier_ref' => true,
    'comment' => true,
    'green_tax' => true,
];

$schema['shipment'] = [
    'comments' => true,
    'order_id' => true,
    'products' => true,
    'shipment_id' => true,
    'shipment_timestamp' => true,
    'shipping' => true,
    'shipping_id' => true,
    'tracking_number' => true,
    'label_url' => true,
    'delivery_date' => true,
];

$schema['statuses'] = [
    'type'          => true,
    'status'        => true,
    'description'   => true,
];

$schema['shipping'] = [
    'delivery_time'             => true,
    'product_id'                => true,
    'rates'                     => true,
    'shipping'                  => true,
    'shipping_id'               => true,
    'specific_rate'             => true,
    'status'                    => true,
    'w_description'             => true,
    'carriage_paid_threshold'   => true,
    'position'                  => true,
];

$schema['returns'] = [
    'return_id'     => true,
    'order_id'      => true,
    'timestamp'     => true,
    'status'        => true,
    'total_amount'  => true,
    'action'        => true,
    'firstname'     => true,
    'lastname'      => true,
    'extra'         => true,
    'rma_number'    => true,
];

$schema['options'] = [
    'option_id'     => true,
    'option_name'   => true,
    'status'        => true,
    'variants'      => [
        'recursive' => true,
        'is_list' => true,
        'object_name' => 'option_variants',
        'api' => true,
    ],
    'code' => true,
    'is_system' => true,
];

$schema['option_variants'] = [
    'variant_id'    => true,
    'variant_name'  => true,
];

$schema['features'] = [
    'description'       => true,
    'feature_code'      => true,
    'feature_type'      => true,
    'parent_id'         => true,
    'categories_path'   => true,
    'feature_id'        => true,
    'value_str'         => true,
    'variants'       => [
        'recursive' => true,
        'is_list' => true,
        'object_name' => 'feature_variants',
        'api' => true,
    ],
];

$schema['feature_variants'] = [
    'variant_id'    => true,
    'variant'       => true,
];

return $schema;
