<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */


/*
 * API and backend take all keys, CSV take the key if true and the csv value if exist
 */
$schema['product'] = [
    'allowed_options_variants' => ['api' => true],
    'product' => true,
    'product_code' => true,
    'company_id' => ['backend' => true, 'api' => true, 'csv' => true],
    'category_ids' => ['backend' => true, 'api' =>true],
    'Category' => ['csv' => true,],
    'Available since' => ['csv' => true,],
    'category id' => ['csv' => true,],
    'Code postal annonce' => ['csv' => true],
    'Ville annonce' => ['csv' => true],
    'Latitude' => ['csv' => true],
    'Longitude' => ['csv' => true],
    'combination' => ['csv' => true,],
    'Combination Code' => ['csv' => true,],
    'Combination Supplier Ref' => ['csv' => true,],
    'Combination image URL' => ['csv' => true,],
    'Combination Alt Text' => ['csv' => true,],
    'main_category' => ['backend' => true, 'api' =>true],
    'tax_ids' =>true,
    'short_description' => true,
    'full_description' => true,
    'status' => true,
    'price' => true,
    'amount' => true,
    'product_features' => ['backend' => true, 'api' =>true],
    'promos_catalog' => ['api' => true,],
    'promos_code' => ['api' => true,],
    'add_new_variant' => ['backend' => true],
    'Features' => ['csv' => true,],
    'avail_since' => ['backend' => true, 'api' =>true, 'csv'=> true],
    'weight' => true,
    'w_supplier_ref' => true,
    'approved' => ['backend' => true],
    'is_edp' => true,
    'w_green_tax' => true,
    'Promotions' => true,
    'unlimited_download' => true,
    'Brand' => true,
    'Image URL' => true,
    'Alt Text' => true,
    'inventory' => true,
    'main_pair' => true,
    'image_pairs' => true,
    'w_condition' => true,
    'crossed_out_price' => true,
    'Video' => ['csv' =>true],
    'video' => ['api' =>true],
    'attachments' => ['api' => true],
    'Attachments' => ['csv' => true],
    'affiliate_link' => true,
    'product_template_type' => true,
    'infinite_stock' => true,
    \Wizacha\Product::FIELD_FREE_FEATURES => true,
    \Wizacha\Product::FIELD_GEOLOCATION => true,
    'Price tiers' => ['csv' => true],
    'divisions_included' => ['csv' => true],
    'divisions_excluded' => ['csv' => true],
    'divisions' => ['api' => true],
    'max_price_adjustment' => true,
    'is_subscription' => true,
    'is_renewable' => true,
    'slug' => ['api' => true],
    'seoTitle' => ['api' => true],
    'seoDescription' => ['api' => true],
    'seoKeywords' => ['api' => true],
    'page_title' => ['csv' => true],
    'meta_description' => ['csv' => true],
    'meta_keywords'  => ['csv' => true],
    'seo_name' => ['csv' => true],
    'quote_requests_min_quantity' => ['api' => true],
    'is_exclusive_to_quote_requests' => ['api' => true],
];

if (container()->getParameter('are_shippings_editable_by_vendors')) {
    $schema['product'] += [
        'free_shipping' => ['backend' => true, 'api' => true],
        'Shippings' => true,
    ];
}

//Field generated in vendor_data_premoderation
$schema['product']['approved'] = ['csv' => true];

$schema['company'] = [
    'company_description' => true,
    'w_company_meta_description' => true,
    'w_company_meta_title' => true,
    'w_company_meta_keywords' => true,
    'company_terms' => true,
    'has_automatic_billing_number' => true,
    'initial_billing_number' => true,
    'initial_rma_number' => true,
    'prefix_billing_number' => true,
    'prefix_credit_note_number' => true,
    'invoicing_disabled' => true,
];

$schema['order'] = [
    'approved' => ['api' => true,],
    'w_invoice_number' => ['api' => true,],
    'create_automatic_billing_number' => ['api' => true],
    'do_not_create_invoice' => ['api' => true,],
    'decline_reason' => ['api' => true,],
    'extra' => ['api' => true,],
];

$schema['shipment'] = [
    'comments'          => true,
    'order_id'          => true,
    'tracking_number'   => true,
    'products'          => true,
    'label_url'         => true,
];

$schema['feature'] = [
    'feature_id'    => true,
    'product_id'    => true,
    'description'   => true,
    'feature_type'  => true,
];

$schema['product_file'] = [
    'product_id'   => true,
    'file_name'    => true,
    'max_download' => true,
    'license'      => true,
    'agreement'    => true,
    'readme'       => true,
];

$schema['link_vendor_category'] = [
    'vendor_category' => true,
    'category_id' => true,
];

return $schema;
