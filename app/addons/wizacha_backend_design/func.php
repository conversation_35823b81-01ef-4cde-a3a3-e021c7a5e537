<?php

use Tygh\Registry;
use Wizacha\Marketplace\Company\Event\CompanyLegalDocumentsUpdated;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Product;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

/**
 * @param $import_data
 * @param $pattern
 * @return bool
 *
 * Used as pre-process function for CSV import. Set default values to important datas
 */
function fn_product_set_default_values(&$import_data , &$pattern)
{
    $pattern['export_fields']['Vendor'] = [
        'table' => 'companies',
        'db_field' => 'company',
        'multilang' => true,
    ];

    $pattern['export_fields']['company_id'] = [
        'table' => 'products',
        'db_field' => 'company_id',
        'alt_key' => true,
        'alt_field' => 'product_id',
        'multilang' => true,
    ];


    $import_data = array_map('fn_product_set_default_values_for_line', $import_data);

    return true;
}

function fn_product_set_default_values_for_line($value)
{
    $lang_code = $value['lang_code'] ?? (string) GlobalState::contentLocale();
    //filter allowed fields
    $value = fn_w_object_filter($value, 'product', 'csv');
    $value['lang_code'] = $lang_code;
    //tracking : options
    $value['tracking'] = 'B';
    //zero price action : R - remove ?
    $value['zero_price_action'] = 'R';
    //Comments and rates
    $value['discussion_type'] = 'B';
    //vendor : current vendor
    $runtime = Registry::get('runtime');
    //if admin, company_id == 0 but, $runtime[company_data][company] is filled with 'All vendors' -> replaced with 'Wizacha'
    $company_name = ( isset($runtime['company_data']['company']) && $runtime['company_id'] != 0 ) ? $runtime['company_data']['company'] : 'Wizacha';
    $value['company'] = $company_name;
    if (Registry::get('runtime.company_id') > 0) {
        $value['company_id'] = Registry::get('runtime.company_id');
    }

    return $value;
}

/**
 *
 * @param integer  $category_id
 */
function fn_wizacha_backend_design_w_check_csv_category_pre(&$category_id, $category)
{
    static $create_link = [];
    if (!empty($category_id)) {
        return ;
    }

    //Try to match the category with vendor's data
    $category_id = \Wizacha\Exim\Category::getIdByVendorCategory($category, Tygh\Registry::get('runtime.company_id'));
    if ($category_id) {
        return;
    }

    $category_id = fn_w_get_category_failure_CSV();
    if (!$create_link[$category]) {
        $create_link[$category] = true;
        //Create the link between the vendor's category and the failure category
        fn_w_update_links_vendor_categories([['category_id' => $category_id, 'vendor_category' => $category]]);
    }
}

/**
 * @return int
 *
 * Return the if of the failure category or creates it.
 */
function fn_w_get_category_failure_CSV()
{
    return \Wizacha\Category::getGarbageCategoryId();
}

/**
 * @param $import_data
 * @param $pattern
 * @return bool
 *
 * Used as pre-process function for CSV import. Set default values to important datas
 */
function fn_w_product_combinations_set_default_values(&$import_data , &$pattern)
{

    $pattern['export_fields']['Language'] = [
        'table' => 'product_descriptions',
        'db_field' => 'lang_code',
        'type' => 'languages',
    ];

    $pattern['export_fields']['Product ID'] = [
        'db_field' => 'product_id',
        'alt_key' => true,
        'alt_field' => 'product_code'
    ];

    return true;
}

/**
 * @return array Example data for template
 */
function fn_w_set_data_template_csv_product_combination()
{
    $result = [];
    if(empty($_REQUEST['categories_ids'])) {
        return $result;
    }
    $category_id = $_REQUEST['categories_ids'];
    $options = fn_w_get_category_options($category_id, true);
    if (empty($options)) {
        return $result;
    }
    $options_strings = array_reduce(
        $options,
        function(&$result, $variant)
        {
            $result[$variant['option_id']][] = $variant['option_name'] . ': ' . $variant['variant_name'];
            return $result;
        }
    );

    foreach (fn_w_get_all_strings_option_variant($options_strings) as $_string_option_variant) {
        $result[] = [(string) GlobalState::contentLocale() => ['Combination' => $_string_option_variant]];
    }

    return $result;


}

/**
 * @param  array $string_option_variant
 * @return array mixed
 * Recursive function for generate all combination of options+variants
 */
function fn_w_get_all_strings_option_variant($string_option_variant)
{
    if (count($string_option_variant) < 2) {
        return current($string_option_variant);
    }
    $shift = array_pop($string_option_variant);

    return array_reduce(
        fn_w_get_all_strings_option_variant($string_option_variant),
        function(&$result, $string) use ($shift)
        {
            return array_merge(
                $result,
                array_reduce(
                    $shift,
                    function(&$subresult, $shift_string) use ($string)
                    {
                     $subresult[] = $string.', ' . $shift_string ;
                     return $subresult;
                    }
                )
            );
        },
        []
    );
}

/**
 * @return array Example data for template.
 */
function fn_w_set_data_template_csv_products()
{
    $result = [];
    foreach (\explode(',', $_REQUEST['categories_ids']) as $categoryId) {
        $idPath = db_get_field("SELECT id_path FROM ?:categories WHERE category_id = ?i", $categoryId);
        $allCategoriesIds = \explode('/', $idPath);
        $category_feature = fn_get_product_features(['category_ids' => $allCategoriesIds, 'statuses' => ['A']])[0];
        $category_path = fn_get_category_path($categoryId, (string) GlobalState::contentLocale(), '///');
        $feature_for_csv = [];
        $feature_for_csv = (array) array_reduce(
            $category_feature,
            function (&$feature_for_csv, $feature_group)
            {
                if ('G' == $feature_group['feature_type']) {
                    $subfeatures = (array) $feature_group['subfeatures'] ?? [];
                    foreach ($subfeatures as $feature) {
                        $feature_for_csv[] = ' ('.$feature_group['description'].') '.$feature['description'].': '.$feature['feature_type'].'[]' ;
                    }
                } else {
                    $feature_for_csv[] = $feature_group['description'].': '.$feature_group['feature_type'].'[]' ;
                }
                return $feature_for_csv;
            },
            $feature_for_csv
        );
        $feature_for_csv = implode(';', $feature_for_csv);
        $result[] = [
            (string) GlobalState::contentLocale() => [
                'Category' => $category_path,
                'Features' => $feature_for_csv
            ]
        ];
    }
    return $result;
}



/**
 * Check if option and variant exist for the product's category
 *
 * @param $product_id
 * @param $pair
 * @param $option_id
 * @param $variant_id
 */
function fn_wizacha_backend_design_w_exim_put_product_combination_set_option_variant($product_id, $pair, &$option_id, &$variant_id, string $lang_code, bool $updateOnlyPriceAndStock)
{
    $option_name = $pair[0];
    $variant_name = $pair[1];
    $option_id = $variant_id = null;
    $main_category = fn_w_get_product_main_category($product_id);
    $options = fn_w_get_category_options($main_category, true, false, $lang_code);

    // Filtrage des options pour avoir celles avec le nom demandé
    $options = array_filter($options, function ($properties) use ($option_name) {
        return $properties['option_name'] == $option_name;
    });

    if (empty($options)) {
        return;
    }

    $option = reset($options);
    $option_id = $option['option_id'];

    // Filtrage des options pour avoir la variante demandée
    $variants = array_filter($options, function ($properties) use ($variant_name) {
        return $properties['variant_name'] == $variant_name;
    });

    $variantIds = [];
    if (count($variants) >= 1) {
        // On prend l'ID directement si on a trouvé la variante
        $variant = reset($variants);
        $variant_id = $variant['variant_id'];
        foreach ($variants as $variant) {
            $variantIds[] = $variant['variant_id'];
        }
    } elseif ($updateOnlyPriceAndStock === false) {
        // Sinon on fait une recherche en base case insensitive
        $variantIds[] = fn_w_add_option_variants_by_name($option_id, [$variant_name], $lang_code);
    }

    return $variantIds;
}

/**
 * @param array $import_data
 * @param array $pattern
 * After a product_combination import, reroll option and variants allowed.
 */
function fn_w_exim_create_product_exceptions($import_data)
{
    include_once(__DIR__.'/../../../src/AppBundle/Controller/CsCart/backend/product_options.php');
    $product_ids = array_reduce(
        $import_data,
        function (&$result, $import)
        {
            //Parent product code if exist (pattern product_combination) else product_code (pattern product)
            $productId = Product::getIdByProductCode($import['product_code'], \array_key_exists('company_id', $import) === true ? \intval($import['company_id']) : 0);

            if (\strlen($productId) > 0) {
                $result[$productId] = true;
            }

            return $result;
        },
        []
    );

    foreach(array_keys($product_ids) as $product_id) {
        $product_option_inventory = fn_get_product_options_inventory(['product_id' => $product_id])[0];
        $product_option_inventory = array_filter(
            $product_option_inventory,
            function($var) {
                if ($var['w_price'] == '0.00' && $var['amount'] == '0'){
                    return false;
                }
                return true;
            }
        );

        if (!empty($product_option_inventory)) {
            $ref_combination = array_keys(reset($product_option_inventory)['combination']);
            while ($product = next($product_option_inventory)) {
                if ($ref_combination != array_keys($product['combination'])) {
                    //Case : All options in combination are not the same, do nothing
                    fn_set_notification(
                        'E',
                        __('error'),
                        __(
                            'w_csv_error_in_combination_async',
                            [
                                '[productId]' => $product['product_id']
                            ]
                        )
                    );
                    //Jump to next $product_id
                    continue 2;
                }
            }
        }


        $combinations = array_reduce(
          $product_option_inventory,
          function(&$result, $combination)
          {
              foreach($combination['combination'] as $option => $variant){
                $result[$option][$variant] = true;
              }
              return $result;
          },
          []
        );

        //Remove and creates links between product and allowed options
        db_query("DELETE FROM ?:product_global_option_links WHERE product_id = ?i", $product_id);
        array_walk(
            array_keys($combinations),
            function($option_id) use($product_id)
            {
                db_query("REPLACE INTO ?:product_global_option_links (option_id, product_id) VALUES (?i, ?i)", $option_id, $product_id);
            }
        );

        db_query("DELETE FROM ?:product_options_exceptions WHERE product_id = ?i", $product_id);

        foreach ($combinations as $option => $variants) {
            if(!empty($variants)){
                foreach (array_keys($variants) as $variant) {
                    db_query(
                        "INSERT INTO ?:product_options_exceptions ?e",
                        [
                            'product_id' => $product_id,
                            'option_id' => $option,
                            'variant_id' => $variant,
                        ]
                    );
                }
            }
        }

        fn_w_set_product_tracking($product_id);
        fn_rebuild_product_options_inventory($product_id, 0);
    }
}


/**
 * @param int[]|null $categoryIds category ids path of the brand feature
 *
 * @return int id of the first global brand
 */
function fn_w_get_brand_id(array $categoryIds = null): ?int
{
    return array_keys(fn_get_product_features([
        'feature_types' => ['E'],
        'category_ids' => is_array($categoryIds) ? implode(',', $categoryIds) : null,
    ])[0])[0];
}

function fn_wizacha_backend_design_w_trusted_vars($args , &$post , &$get){

    if (sizeof($args) > 0) {
        foreach ($args as $k => $v) {
            // Not an HTML field, is allowed to contain < or >
            if ($k == "free_features") {
                continue;
            }
            if (isset($post[$v])) {
                is_array($post[$v]) ? array_walk_recursive($post[$v] , '\Wizacha\Misc::processSubmittedContent') : \Wizacha\Misc::processSubmittedContent($post[$v] , $k);
            } elseif (isset($get[$v])) {
                is_array($get[$v]) ? array_walk_recursive($get[$v] , '\Wizacha\Misc::processSubmittedContent') : \Wizacha\Misc::processSubmittedContent($get[$v] , $k);
            }
        }
    }
}

/**
 * @param int $product_id
 * @param string $data
 * @param string $features_delimiter
 * @param string $lang_code
 *
 * Save Brand as if in Features column
 */
function fn_w_exim_put_brand(int $product_id, string $data, string $features_delimiter, string $lang_code): void
{
    $params = ['feature_types' => ['E']];

    if (is_int($product_id)) {
        $product = container()->get('marketplace.pim.product.service')->get($product_id);
        $params['category_ids'] = $product->getCategory()->getPathId();
    }
    $brandInfos = fn_get_product_features($params)[0];
    $brandId = array_keys($brandInfos)[0];
    $brand = $brandInfos[$brandId];

    $datas = '';

    if (!empty($brand['description']) && !empty($brand['feature_type'])) {
        $datas = $brand['description'].': '.$brand['feature_type'].'['.$data.'];';
    }

    fn_exim_set_product_features($product_id , $datas , $features_delimiter , $lang_code , true, explode('/', $params['category_ids']));
}

/**
 * Changes before product feature updating
 *
 * @param array $feature_data Feature data
 * @param int $feature_id Feature identifier
 * @param string $lang_code 2-letters language code
 */
function fn_wizacha_backend_design_update_product_feature_pre(&$feature_data, $feature_id, $lang_code) {
    $authorized_types = fn_get_schema('permissions', 'create_features');
    $company_id = Registry::get('runtime.company_id');

    if(
        !isset($authorized_types['feature_types'][$feature_data['feature_type']])
        && $company_id
    ) {
        $feature_data = null;
        return false;
    }

    //Set company ownership, and hide features during submission
    if($company_id){
        $feature_data['company_id'] = $company_id;
        $feature_data['status'] = 'H';
    }
    //Helper: if a product_id is set, the feature category will be its main category
    if(isset($feature_data['product_id'])){
        $category_id = fn_w_get_product_main_category($feature_data['product_id']);
        if($category_id){
            $feature_data['categories_path'] =
                (empty($feature_data['categories_path'])?'':($feature_data['categories_path'].',')).$category_id;
        }
    }
}

/**
 * @param array  $object_data
 * @param string  $object_name
 * @param string $env
 * @return array
 * Strip unallowed fields in object_data
 */
function fn_w_object_filter($object_data, $object_name, $env = 'backend', $schema_name = 'update_objects')
{
    if (!is_array($object_data)) {
        return [];
    }
    $schema = fn_get_schema('permissions', $schema_name);
    if(!isset($schema[$object_name])) {
        return [];
    }
    $allowed = [];
    array_walk(
        $schema[$object_name],
        function ($value, $key) use ($env, &$allowed, &$object_data, $schema_name) {
            if ($value === true || $value[$env] === true) {
                $allowed[] = $key;
                if (isset($object_data[$key]) && $value['recursive']) {
                    if ($value['is_list']) {
                        $d = &$object_data[$key];
                    } else {
                        $d = [&$object_data[$key]];
                    }
                    array_walk(
                        $d,
                        function (&$_d) use($value, $env, $schema_name) {
                            $_d = fn_w_object_filter($_d, $value['object_name'], $env, $schema_name);
                        }
                    );
                }
            }
        }
    );

    return array_intersect_key($object_data, array_flip($allowed));
}

/**
 * @param $product_id
 * @return null|string
 *
 * Display Brand as separate column
 */
function fn_w_exim_get_brand($product_id)
{
    if (!is_numeric($product_id)) {
        return null;
    }

    $product = new \Wizacha\Product($product_id);

    return $product->getBrand();
}

/**
 * @param $product_id
 */
function fn_w_exim_get_images($product_id , $images_delimiter){

    $images = fn_w_product_get_all_image_pairs($product_id);
    $images_path = [];
    foreach($images as $image_pair) {
        if (!empty($image_pair['detailed']['http_image_path'])) {
            $images_path[] = $image_pair['detailed']['http_image_path'];
            continue;
        }

        $images_path[] = fn_image_to_display($image_pair, Registry::get('settings.Thumbnails.product_details_thumbnail_width'), Registry::get('settings.Thumbnails.product_details_thumbnail_height'))['image_path'];
    }

    return implode($images_delimiter , $images_path);
}

function getImagesAlt(int $productId , string $imagesDelimiter, string $langCode): string
{
    $images = fn_w_product_get_all_image_pairs($productId);
    $imagesAlt = [];
    foreach($images as $image) {
        if (\is_array($image) === true && \array_key_exists('detailed_id', $image) === true) {
            $imagesAlt[] = getImagesSeoAltText($image['detailed_id'], $langCode);
        }
    }

    return implode($imagesDelimiter, $imagesAlt);
}

function getDeclinationImagesAlt(int $productId, array $row, string $langCode): string
{
    if (\array_key_exists('Combination', $row) === false) {
        return '';
    }

    $image = fn_get_image_pairs($productId . '_' . $row['Combination'], 'declinations', 'M', true, true);
    if (\is_array($image) === true && \array_key_exists('detailed_id', $image) === true) {
        $imageAlt = getImagesSeoAltText($image['detailed_id'], $langCode);
    }

    return $imageAlt ?? '';
}

function fn_w_exim_declination_get_images($productId, $row, $images_delimiter): string
{
    if (array_key_exists('Combination', $row) === false) {
        return '';
    }

    $images = fn_get_image_pairs($productId.'_'.$row['Combination'], 'declinations', 'M', true, true);
    $images_path = [];

    if (empty($images['detailed']['http_image_path']) === false) {
        $images_path[] = $images['detailed']['http_image_path'];
    } else {
        $images_path[] = fn_image_to_display($images, Registry::get('settings.Thumbnails.product_details_thumbnail_width'), Registry::get('settings.Thumbnails.product_details_thumbnail_height'))['image_path'];
    }

    return implode($images_delimiter , $images_path);
}

function fn_w_product_get_all_image_pairs($product_id){
    //get Main image first (never an array)
    $main_pair[] = fn_get_image_pairs($product_id, 'product', 'M', true, true);
    //then get Additionnal images (always an array)
    $additionnal_pairs = fn_get_image_pairs($product_id, 'product', 'A', true, true);

    if (is_array($additionnal_pairs)) {
        return array_merge($main_pair , $additionnal_pairs);
    }
    return $main_pair;
}

function fn_wizacha_backend_design_get_categories(&$params, $join, &$condition, $fields, $group_by, &$sortings, $lang_code) {
    if ($params['leaves']) {
        $condition .= db_quote(" AND NOT EXISTS (SELECT * FROM ?:categories AS s WHERE s.parent_id=?:categories.category_id LIMIT 1) ");
    }

    if(isset($params['w_search'])) {
        $values = empty($params['w_search']['looking_for']) ? '' :  '%' . $params['w_search']['looking_for'] . '%';
        $condition .= \Tygh\Database::quote(" AND ?:category_descriptions.category LIKE ?l", $values);
        $locale = \Tygh\Database::quote('?s', (string) GlobalState::contentLocale());

        $request = "SELECT COUNT(*) as number,
                    CASE WHEN NOT EXISTS (SELECT * FROM ?:categories AS s WHERE s.parent_id=?:categories.category_id LIMIT 1) THEN 'leaf'
                         ELSE 'not_leaf' END as leaf
                    FROM ?:categories
                    LEFT JOIN ?:category_descriptions ON ?:categories.category_id = ?:category_descriptions.category_id AND ?:category_descriptions.lang_code = $locale $join
                    WHERE 1 $condition
                    GROUP BY leaf";

        $result = \Tygh\Database::getHash($request, 'leaf');

        $params['w_search']['leaf'] = intval($result['leaf']['number']);
        $params['w_search']['not_leaf'] = intval($result['not_leaf']['number']);

        $params['total_items'] = $params['w_search']['leaf'] + $params['w_search']['not_leaf'];
        $params['items_per_page'] = $params['limit'];
        $params['max_page'] = $params['total_items'];
    }
}

/**
 * @param array  $data        object to convert
 * @param string $object_name
 * @param string $way         "send" or "get"
 * @param string $schema
 * @return array
 */
function fn_w_convert_fields($data, $object_name, $way, $schema_name ='convert_fields_api')
{
    if (!is_array($data)) {
        return [];
    }

    $schema = fn_get_schema('permissions', $schema_name);
    if(!isset($schema[$object_name])) {
        return $data;
    }
    array_walk(
        $schema[$object_name][$way],
        function ($value, $key) use (&$data, $way, $schema_name) {
            if (array_key_exists($key, $data)) {
                if ($value['is_list']) {
                    $d = &$data[$key];
                } else {
                    $d = [&$data[$key]];
                }
                if ($value['recursive']) {
                    array_walk(
                        $d,
                        function (&$_d) use($value, $way, $schema_name) {
                            $_d = fn_w_convert_fields($_d, $value['object_name'], $way, $schema_name);
                            }
                    );
                }
                if ($value['callback']) {
                    array_walk(
                        $d,
                        function (&$_d) use($value, $way, $schema_name) {
                            $_d = call_user_func($value['callback'], $_d);
                                //fn_w_convert_fields($_d, $value['object_name'], $way, $schema_name);
                        }
                    );
                }

                if ($value['value']) {
                    $data[$value['value']] = $data[$key];
                    unset($data[$key]);
                }
            }
        }
    );

    return $data;
}

/**
 * @param array $features_data
 * @param string $status
 * @param bool $notifications
 * @param string $reason
 *
 */
function fn_w_premoderate_feature(array $features_data, $status, $notifications = false, $reason ='')
{
    $feature_ids = array_column($features_data,'feature_id');
    if ('Y' == $status) {
        //Retrieve data to update
        $feature_update_datas = \Tygh\Database::getArray(
            'SELECT "A" AS status, 0 AS company_id, feature_type, description
             FROM ?:product_features AS pf
             JOIN ?:product_features_descriptions AS pfd ON pfd.feature_id=pf.feature_id
             WHERE pf.feature_id IN (?a) ORDER BY pf.feature_id',
            $feature_ids
        );
        sort($feature_ids);
        array_map('fn_update_product_feature', $feature_update_datas, $feature_ids);
    } elseif ('N' == $status) {
        array_map('fn_delete_feature',$feature_ids);
    }

    fn_set_notification('N', __('notice'), __('status_changed'));
}

/**
 * @param $combination
 * @return array
 * convert string to int from fn_get_product_options_by_combination
 */
function fn_w_get_product_options_by_combination($combination)
{
    if (empty($combination)) {
        return $combination;
    }
    return array_map(
        function ($value){
            return (int) $value;
        },
        fn_get_product_options_by_combination($combination)
    );
}

/**
 * @param $company_data
 * @param $company_id
 * @param $lang_code
 * @param $action
 * Add meta_description and keywords to companies.
 */
function fn_wizacha_backend_design_update_company($company_data, $company_id, $lang_code, $action)
{
    if (empty($company_id)) {
        return;
    }

    $insert_common = function ($field) use ($company_data, $company_id, $lang_code, $action) {
        if (array_key_exists($field, $company_data) || 'add' == $action) {
            if (false === is_string($company_data[$field])) {
                $company_data[$field] = __(
                    "w_default_$field",
                    ['[company_name]' => $company_data['company']]
                );
            }

            db_query(
                "REPLACE INTO
                ?:common_descriptions (object_id, object_holder, description, lang_code)
                VALUES (?i, ?s, ?s, ?s)",
                $company_id,
                $field,
                $company_data[$field],
                $lang_code
            );
        }
    };
    $insert_common('w_company_meta_description');
    $insert_common('w_company_meta_keywords');
    $insert_common('w_company_meta_title');
    if (container()->getParameter('feature.global_option_allowed_for_vendors')) {
        \Wizacha\Company::updateCompanyOptions();
    }
}

/**
 * @param $key
 * @return integer
 *
 * When a product_combination is exported, give the parent product code.
 */
function fn_w_exim_get_parent_product_code($product_id)
{
    static $product_code = [];
    if (!array_key_exists($product_id, $product_code)) {
        $product_code[$product_id] = Tygh\Database::getField("SELECT product_code FROM ?:products WHERE product_id = ?i", $product_id);
    }
    return $product_code[$product_id];
}

/**
 * @param $pattern
 * @param $alt_keys
 * @param $object
 * @param $skip_get_primary_object_id
 * Add product_id when a product_combination is imported by CSV
 */
function fn_w_exim_import_get_primary_object_id_product_combination($pattern, $alt_keys, &$object, &$skip_get_primary_object_id)
{
    $object['product_id'] = \Wizacha\Product::getIdByProductCode(array_shift($alt_keys));
    $skip_get_primary_object_id = true;
}

/**
 * @param integer $company_id
 */
function fn_w_put_files_for_vendor_post($company_id){
    $vendorSubStorage = container()->get('Wizacha\Storage\VendorSubscriptionStorageService');
    foreach($_FILES as $key => $file){

        if (!fn_w_check_apply_for_vendor_files($file)) {
            continue;
        }

        $filename = $key.'_'.$company_id.'_'.basename($file['name']);

        $vendorSubStorage->put($company_id.'/'.$filename, ['file'=>$file['tmp_name']]);
    }

    if (!empty($files)) {
        container()->get('event_dispatcher')->dispatch(
            new CompanyLegalDocumentsUpdated($company_id, array_keys($_FILES)),
            \Wizacha\Marketplace\Company\CompanyEvents::LEGAL_DOCUMENTS_UPDATED
        );
    }
}

/** Seconds to human readable duration */
function fn_w_human_duration(int $seconds, bool $withDays = false): string
{
    [$minutes, $seconds] = fn_w_divmod($seconds, 60);
    [$hours, $minutes] = fn_w_divmod($minutes, 60);

    if (true === $withDays) {
        [$days, $hours] = fn_w_divmod($hours, 24);
    }

    $data = \array_filter(
        [
            'd' => $days,
            'h' => $hours,
            'm' => $minutes,
            's' => $seconds,
        ]
    );

    return implode(
        ' ',
        \array_map(
            function (string $key, int $value): string {
                return "$value$key.";
            },
            \array_keys($data),
            \array_values($data)
        )
    );
}

/** Python divmod alike (for positive int only) */
function fn_w_divmod(int $num, int $div): array
{
    return [
        intdiv($num, $div),
        $num % $div
    ];
}
