<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\PIM\Product\Template\ProductInfoFormType;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return;
}

if ($mode == 'update' || 'add' == $mode ) {
    $tabs = Tygh\Registry::get('navigation.tabs');
    //Order corresponding tabs
    $tabOrder = array_flip([
        'detailed',
        'available_offers',
        'attachments',
        'features',
        'free_features',
        'shippings',
        'images',
        'related_products',
        'files',
        'discussion',
        'addons',
    ]);

    $product_data = Registry::get('view')->getTemplateVars('product_data');
    if (isset($product_data['is_edp']) && $product_data['is_edp'] == 'Y') {
        unset($tabs['shippings']);
    }


    //Remove data from templates
    if (Registry::get('runtime.company_id')){
        unset($tabs['discussion']);
        Registry::get('view')->clearAssign('discussion');
        unset($tabs['addons']); //Data removed by hook detailed_content.override.tpl

        //To be sure that we control displayed tabs
        $tabs = array_intersect_key($tabs,$tabOrder);
    }
    unset($tabs['qty_discounts']);
    unset($tabs['options']);

    if('update' == $mode) {
        if ($product_data['is_edp'] == 'N' && !container()->getParameter('feature.allow_files_on_non_edp_products')) {
            unset($tabs['files']);
        }
        Registry::get('view')->assign('product_data', $product_data);
    } else {//mode add
        unset($tabs['files']);
        $restored_data = fn_restore_post_data('product_data');
        $brand = fn_get_product_features(['variants' => true, 'feature_types' => ['E']])[0];
        if($restored_data) {
            $brand_id = fn_w_get_brand_id();
            $variant_id = $restored_data['product_features'][$brand_id];
            unset($restored_data['product_features']);
            if($variant_id) {
                $brand[$brand_id]['variant_id'] = $variant_id;
            }

            // If product add failed and a video was uploaded, we must return the video object (as fn_get_product_data) rather than the video id
            if (!empty($restored_data['video'])) {
                try {
                    $restored_data['video'] = container()->get('marketplace.pim.video_service')->get($restored_data['video']);
                } catch (NotFound $e) {
                    $restored_data['video'] = null;
                }
            }

            Registry::get('view')->assign('product_data', $restored_data);

            // Pass the restored data to the Symfony form
            $saveProductForm = container()->get('form.factory')->create(ProductInfoFormType::class);
            $saveProductForm->submit($restored_data);
            Registry::get('view')->assign('saveProductForm', $saveProductForm->createView());

            if (!empty($restored_data['category_ids'])) {
                Registry::get('view')->assign('category_obj', new \Wizacha\Category(explode(',', $restored_data['category_ids'])[0]));
            }
        }

        Registry::get('view')->assign('w_product_brands', $brand);
    }

    Registry::get('view')->clearAssign('product_options');
    Registry::get('view')->clearAssign('global_options');

    uksort($tabs,function($a,$b) use($tabOrder){
        $a = $tabOrder[$a];
        $b = $tabOrder[$b];
        return $a==$b?0:($a<$b?-1:1);
    });
    Registry::set('navigation.tabs',$tabs);
}

if ('manage' == $mode) {
    //too much filters / features in advanced search form cause 'Quick search' to slow down or crash
    Registry::get('view')->clearAssign('feature_items');
}
if (!Registry::get('runtime.company_id') || container()->getParameter('are_shippings_editable_by_vendors')) {
    Registry::get('view')->assign('allow_update_shippings', true);
}
