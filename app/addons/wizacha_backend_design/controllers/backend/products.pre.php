<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ('update' == $mode) {
        $product_with_company_ref = db_get_field(
            "SELECT product_id FROM ?:products WHERE product_code = ?s AND company_id = ?i",
            $_REQUEST['product_data']['product_code'],
            $_REQUEST['product_data']['company_id']
        );

        if (isset($product_with_company_ref) && $product_with_company_ref != $_REQUEST['product_id']) {
            fn_set_notification('E', __("error"), __("w_duplicate_product_code"));
            unset($_POST['product_data']['product_code']);
        }
        if (empty($_REQUEST['product_data']['tax_ids'])) {
            fn_set_notification('E', __("error"), __("w_tax_required"));
        }

        //Check shippings
        if (!$_REQUEST['product_id']
            && !\Wizacha\Product::isExemptShippings($_REQUEST['product_data'])
            && !\Wizacha\Company::hasShipping($_REQUEST['product_data']['company_id'])) {
            fn_set_notification('E', __("error"), __("w_shippings_required", ['[url]' => fn_url('shippings.manage')]));
        }

        if (fn_notification_exists('type', 'E')) {
            fn_save_post_data('product_data');
            if (!$_REQUEST['product_id']) {
                return array(CONTROLLER_STATUS_REDIRECT, "products.add");
            } else {
                return array(CONTROLLER_STATUS_REDIRECT, "products.update?product_id=".$_REQUEST['product_id']);
            }
        }
        $_POST['product_data'] = fn_w_product_authorized_fields($_POST['product_data']);
        $_POST['product_data']['free_shipping'] = 'N';

        if ($_POST['product_data']['is_edp'] == 'Y') {
            $_POST['product_data']['is_returnable'] = 'N';
        }

    }

    if ('m_update' == $mode) {
        array_walk(
            $_POST['products_data'],
            function(&$product_data) {
                $product_data  = fn_w_product_authorized_fields($product_data);
            }
        );
    }

    if ('update_file' == $mode) {
        $_POST['product_file'] = fn_w_object_filter($_POST['product_file'], 'product_file');
        $_POST['product_file']['activation_type'] = 'P';
        $_REQUEST['product_file'] = $_POST['product_file'];
    }

    if (!$_REQUEST['product_id']) {
        $_POST['product_data']['discussion_type'] = 'B';
    }

    if(!isset($_POST['product_data']['price'])){
        $_POST['product_data']['price'] = 0;
    }
    return;
}

/**
 * @param array $product_data
 * Remove unauthorized field for vendor
 */
function fn_w_product_authorized_fields($product_data){
    if (Tygh\Registry::get('runtime.company_id')){
        //Authorized fields
        $authorized = [
            'product',
            'product_code',
            'company_id',
            'category_ids',
            'main_category',
            'geoloc_lat',
            'geoloc_lng',
            'geoloc_label',
            'geoloc_postal',
            'tax_ids',
            'short_description',
            'full_description',
            'status',
            'price',
            'crossed_out_price',
            'amount',
            'product_features',
            'free_features',
            'avail_since',
            'weight',
            'add_new_variant',
            'w_supplier_ref',
            'w_condition',
            'approved',
            'is_edp',
            'w_green_tax',
            'unlimited_download',
            'video',
            'affiliate_link',
            'transaction_mode',
            'product_template_type',
            'infinite_stock',
            'max_price_adjustment',
            'is_subscription',
            'is_renewable',
        ];
        $product_data = array_intersect_key($product_data,array_flip($authorized));
    }

    return $product_data;
}
