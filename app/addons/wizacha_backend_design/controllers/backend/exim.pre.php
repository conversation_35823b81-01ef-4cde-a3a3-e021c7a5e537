<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */


use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ('export_example' == $mode) {
        if ('product_combinations' == $_REQUEST['example_type']) {
            $_REQUEST['categories_ids'] = $_REQUEST['categories_combination_ids'];
            if (empty($_REQUEST['categories_ids'])) {
                fn_set_notification('E', __('error'), __('w_no_category_select'), 'I', '', true);
                exit;
            }
            if (count(explode(',', $_REQUEST['categories_ids'])) >1) {
                fn_set_notification('E', __('error'), __('w_multiple_categories_select'), 'I', '', true);
                exit;
            }
        }

        if ('products' == $_REQUEST['example_type']) {
            $_REQUEST['categories_ids'] = $_REQUEST['categories_product_ids'];
            if (empty($_REQUEST['categories_ids'])) {
                fn_set_notification('E', __('error'), __('w_no_category_select'), 'I', '', true);
                exit;
            }
        }

        /*
         * fn_get_pattern_definition is defined in exim.php ; but, if exim.php is called with a POST method, it end with
         *  an exit;
         * __DIR__ is necessary because post and pre controller are load with a function.
         */
        $_SERVER['REQUEST_METHOD'] = '';
        include(__DIR__.'/../../../../../src/AppBundle/Controller/CsCart/backend/exim.php');
        $pattern = fn_get_pattern_definition($_REQUEST['example_type'], 'export');
        unset($pattern['post_processing']);
        unset($pattern['pre_processing']);

        $pattern_cols = [];
        array_walk(
            $pattern["export_fields"],
            function ($properties, $col) use (&$pattern_cols) {
                $pattern_cols[] = $col;
            }
        );

        fn_export(
            $pattern,
            $pattern_cols,
            [
                'example' => true,
                'output' => 'D',
                'filename' => $_REQUEST['filename'],
                true,
                'lang_code' => [
                    (string) GlobalState::contentLocale(),
                ],
            ],
            false
        );
        $url = fn_url("exim.get_file?filename=" .$_REQUEST['filename'], 'A', 'current');


        if (defined('AJAX_REQUEST') && !empty($url)) {
            Tygh\Registry::get('ajax')->assign('force_redirection', $url);

            exit;
        }
        exit;
    }

    if ('view_feature' == $mode) {
        if (empty($_REQUEST['categories_product_ids'])) {
            fn_set_notification('E', __('error'), __('w_no_category_select'), 'I', '', true);
            exit;
        }


        $category_id = explode(',', $_REQUEST['category_product_ids'])[0];
        $url = fn_url("product_features.manage?is_search=Y&items_per_page=100&category_ids=`$category_id`", 'A', 'current');
        if (defined('AJAX_REQUEST') && !empty($url)) {
            Tygh\Registry::get('ajax')->assign('force_redirection', $url);
            exit;
        }
        exit;
    }
    return;
}
