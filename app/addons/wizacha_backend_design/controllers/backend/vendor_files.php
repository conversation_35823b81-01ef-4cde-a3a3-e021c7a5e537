<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return;
}

if (!Registry::get('runtime.company_id')) {
    $vendorSubStorage = container()->get('Wizacha\Storage\VendorSubscriptionStorageService');
    if ('download' == $mode) {
        if (isset($_REQUEST['company_id'])
        && is_numeric($_REQUEST['company_id'])
        && isset($_REQUEST['file'])
        ) {
            $filename = $_REQUEST['company_id'].'/'.basename($_REQUEST['file']);
            return $vendorSubStorage->get($filename);
        }
    }

    if ('delete' == $mode) {
        if (isset($_REQUEST['company_id'])
            && is_numeric($_REQUEST['company_id'])
            && isset($_REQUEST['file'])
        ) {
            $filename = $_REQUEST['company_id'].'/'.basename($_REQUEST['file']);
            if ($vendorSubStorage->delete($filename)) {
                fn_set_notification('N', '', __('text_file_deleted', ['[file]'=>$_REQUEST['file']]));
            } else {
                fn_set_notification('E', __('error'), __('text_cannot_delete_file', ['[file]'=>$_REQUEST['file']]));
            }

            return array(CONTROLLER_STATUS_REDIRECT, 'companies.update&company_id='.$_REQUEST['company_id']);
        }
    }
}
