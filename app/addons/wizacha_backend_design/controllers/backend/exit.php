<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }
$schema = fn_get_schema('last_edited_items', 'schema');
$last_items_cnt = LAST_EDITED_ITEMS_COUNT;

if (empty($_SESSION['last_edited_items'])) {
    $stored_items = fn_get_user_additional_data('L');
    $last_edited_items = empty($stored_items) ? array() : $stored_items;
    $_SESSION['last_edited_items'] = $last_edited_items;
} else {
    $last_edited_items = $_SESSION['last_edited_items'];
}

if (!empty($schema[Registry::get('runtime.controller') . '.' . Registry::get('runtime.mode')])) {
    $items_schema = $schema[Registry::get('runtime.controller') . '.' . Registry::get('runtime.mode')];
    if (empty($items_schema['func'])) {
        $c_elm = '';
    } else {
        $c_elm = $items_schema['func'];
        foreach ($c_elm as $k => $v) {
            if (strpos($v, '@') !== false) {
                $ind = str_replace('@', '', $v);
                if (!empty($auth[$ind]) || !empty($_REQUEST[$ind])) {
                    $c_elm[$k] = ($ind == 'user_id' && empty($_REQUEST[$ind])) ? $auth[$ind] : $_REQUEST[$ind];
                }
            }
        }
    }

    $url = Registry::get('config.current_url');

    $last_item = array('func' => $c_elm, 'url' => $url, 'icon' => (empty($items_schema['icon']) ? '' : $items_schema['icon']), 'text' => (empty($items_schema['text']) ? '' : $items_schema['text']));
    $current_hash = fn_crc32(!empty($c_elm) ? implode('', $c_elm) : $items_schema['text']);

    // remove element if it already exists and add it to the end of history
    unset($last_edited_items[$current_hash]);
    $last_edited_items[$current_hash] = $last_item;

    if (count($last_edited_items) > $last_items_cnt) {
        foreach ($last_edited_items as $k => $v) {
            unset($last_edited_items[$k]);
            if (count($last_edited_items) == $last_items_cnt) {
                break;
            }
        }
    }
}

$last_items = array();
if (!empty($last_edited_items)) {
    foreach ($last_edited_items as $hash => $v) {

        if (!empty($current_hash) && $hash == $current_hash) {
            // ignore current page
            continue;
        }

        if (!empty($v['func'])) {
            $func = array_shift($v['func']);
            if (function_exists($func)) {
                $content = call_user_func_array($func, $v['func']);
                if (!empty($content)) {
                    $name = (empty($v['text']) ? '' : __($v['text']) . ': ') . $content;
                    array_unshift($last_items, array('name' => $name, 'url' => $v['url'], 'icon' => $v['icon']));
                } else {
                    unset($last_edited_items[$hash]);
                }
            } else {
                unset($last_edited_items[$hash]);
            }
        } else {
            array_unshift($last_items, array('name' => __($v['text']), 'url' => $v['url'], 'icon' => $v['icon']));
        }
    }
}

Registry::get('view')->assign('last_edited_items', $last_items);

// save changed items history
$_SESSION['last_edited_items'] = $last_edited_items;
fn_save_user_additional_data('L', $last_edited_items);
