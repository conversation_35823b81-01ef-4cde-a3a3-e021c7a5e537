<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return;
}
if ('manage' == $mode && Tygh\Registry::get('runtime.company_id')) {
    $features = Tygh\Registry::get('view')->getTemplateVars('features');
    array_walk(
        $features,
        function (&$data)
        {
            if (isset($data['subfeatures'])) {
                array_walk(
                    $data['subfeatures'],
                    function (&$feature_data, $feature_id)
                    {
                        $feature_data['variants'] = fn_get_product_feature_variants(['feature_id' => $feature_id])[0];
                    }
                );
            }

        }
    );
    Tygh\Registry::get('view')->assign('features', $features);
}
