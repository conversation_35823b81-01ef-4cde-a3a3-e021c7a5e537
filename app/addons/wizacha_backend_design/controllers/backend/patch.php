<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Aws\Sqs\SqsClient;
use Eventio\BBQ;
use Eventio\BBQ\Queue\AbstractQueue;
use Symfony\Component\HttpFoundation\JsonResponse;
use Tygh\Languages\Languages;
use Wizacha\Async\Debouncer\DebouncerService;
use Wizacha\BBQ\Queue\AmqpQueue;
use Wizacha\BBQ\Queue\SqsQueue;
use Wizacha\Component\Notification\TestNotificationRequested;
use Wizacha\Marketplace\ReadModel\ReadModelService;
use Wizacha\Search\Engine\AlgoliaService;
use Wizacha\Async\Config;
use Wizacha\AppBundle\Command\ImportAutomatedFeedsCommand;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return;
}

/**
 * Regenerate seo names
 */
$regenerate_seo_names = function () {
    container()->get('marketplace.seo.seo_service')->refreshNames();
};

/**
 * Update all the product read models
 */
$update_read_model_product = function () {
    container()->get(ReadModelService::class)->create();
};

/**
 * Send to search engine all values for indexing and faceting.
 */
$updateProductIndexParams = function () {
    container()->get(AlgoliaService::class)->pushConfig();
};

/**
 * Recreate index to search engine
 */
$createSearchIndex = function () {
    foreach (Languages::getAll() as $lang) {
        container()->get(AlgoliaService::class)->createIndex($lang['lang_code']);
    }
};

/**
 *Debug : Dump the read model of a specific product.
 */
$dump_read_model_product = function () {
    echo <<<HTML
<form method="get">
<input type="text" name="productId" placeholder="Product ID" />
<input type="hidden" name="dispatch" value="patch.dump_read_model_product">
<input type="submit" />
</form>
HTML;

    if (!empty($_REQUEST['productId'])) {
        echo "<pre>";
        var_export(container()->get('marketplace.product.productservice')->getProduct($_REQUEST['productId']));
        echo "</pre>";
    }

    exit;
};

/**
 * Send a <NAME_EMAIL>
 */
$test_email = function () {
    container()->get('event_dispatcher')->dispatch(new TestNotificationRequested(), TestNotificationRequested::class);
};

/**
 * Clear the product read models
 */
$clear_read_model_product = function () {
    container()->get(ReadModelService::class)->clear();
};

/**
 * Pousse tous les produits au moteur de recherche
 */
$update_search_product_index = function () {
    container()->get(AlgoliaService::class)->pushProducts();
};

/**
 * Create missing keys for the translations of all available locales.
 */
$create_missing_translations_keys = function () {
    $affectedRows = container()->get('app.translation')->createMissingKeys();

    return "{$affectedRows} missing translations keys have been created.";
};


/**
 * Treat automated feeds
 */
$importAutomatedFeedsCommand = function () {
    return container()
        ->get(ImportAutomatedFeedsCommand::class)
        ->run(
            new \Symfony\Component\Console\Input\ArrayInput([]),
            new \Symfony\Component\Console\Output\NullOutput()
        );
};

/**
 * Show a phpinfo() page
 */
$phpinfo = function () {

    phpinfo();
    exit;
};

/**
 * Debouncer stats and last 64 jobs details
 * (up to 1024 with '&limit=1024' parameter)
 * as JSON data
 */
$debouncer_info = function () {
    /** @var DebouncerService */
    $debouncerService = container()->get('marketplace.debouncer.service');
    $limit = \min(
        (int) ($_REQUEST['limit'] ?? 64),
        1024
    );

    $data = [
        'title' => 'Debouncer stats',
        'limit' => $limit,
        'stats' => $debouncerService->getStats(),
        'jobs' => $debouncerService->debouncedJobRepository->getFirsts($limit),
    ];

    (new JsonResponse($data))->send();
};

/**
 * Pay payment deferment orders command
 */
$paymentDefermentOrdersCommand = function () {
    $command = new \Wizacha\AppBundle\Command\PayPaymentDefermentOrdersCommand();
    $command->setContainer(container());

    return $command->run(
        new \Symfony\Component\Console\Input\ArrayInput([]),
        new \Symfony\Component\Console\Output\NullOutput()
    );
};

/**
 * Renew a specific subscription
 */
$subscriptionRenew = function () {
    echo <<<HTML
<form method="get">
<input type="text" name="subscriptionId" placeholder="Subscription Id" />
<input type="hidden" name="dispatch" value="patch.subscription_renew">
<input type="submit" />
</form>
HTML;

    if (\is_string($_REQUEST['subscriptionId']) === true) {
        try {
            container()->get('Wizacha\Marketplace\Subscription\SubscriptionService')
                ->renew(
                    container()->get('Wizacha\Marketplace\Subscription\SubscriptionRepository')
                        ->findOneById($_REQUEST['subscriptionId']),
                    true
                );
        } catch (\Throwable $exception) {
            return 'Error on renewal attempt. Exception message: ' . $exception->getMessage();
        }

        return 'Subscription id ' . $_REQUEST['subscriptionId'] . ' successfully renewed.';
    }

    exit;
};

/**
 * Queues length (AMQP/SQS)
 * as JSON data
 */
$queueInfo = function () {
    # AWS SQS queue
    $sqsInfo = function (BBQ $bbq) {
        $clientRP = new ReflectionProperty(SqsQueue::class, '_client');
        $clientRP->setAccessible(true);
        $queueUrlRP = new ReflectionProperty(SqsQueue::class, '_queueUrl');
        $queueUrlRP->setAccessible(true);

        return \array_map(
            function (AbstractQueue $queue) use ($clientRP, $queueUrlRP) {
                /** @var SqsClient */
                $sqsClient = $clientRP->getValue($queue);
                /** @var string */
                $queueUrl = $queueUrlRP->getValue($queue);
                try {
                    return (int)
                        $sqsClient->getQueueAttributes(
                            [
                                'QueueUrl' => $queueUrl,
                                'AttributeNames' => ['ApproximateNumberOfMessages'],
                            ]
                        )->get('Attributes')
                        ['ApproximateNumberOfMessages']
                    ;
                } catch(\Exception $e) {
                    return 'unavailable';
                }
            },
            $bbq->getQueues()
        );
    };

    # AMQP queue
    $amqpInfo = function (BBQ $bbq) {
        $channelRP = new ReflectionProperty(AmqpQueue::class, 'channel');
        $channelRP->setAccessible(true);

        return \array_map(
            function (AbstractQueue $queue) use ($channelRP) {
                /** @var AmqpChannel */
                $channel = $channelRP->getValue($queue);

                return $channel
                    ->queue_declare(
                        $queue->getId(),
                        true
                    )[1]
                ;
            },
            $bbq->getQueues()
        );
    };

    $queueType = container()->getParameter('queue.type');
    $queueInfo = [
            Config::TYPE_SQS => $sqsInfo,
            Config::TYPE_AMQP => $amqpInfo,
        ]
        [$queueType](
            container()->get('marketplace.queue_manager')
        )
    ;

    (
        new JsonResponse(
            [
                'title' => "Messages in $queueType queues",
                'queues' => $queueInfo,
            ]
        )
    )
    ->send();
};

/**
 * OpenSSL cipher info
 */
$cipherInfo =  function() {
    $lemonWayCiphers = [
        'TLS 1.2' => [
            'ECDHE-RSA-AES128-GCM-SHA256',
            'ECDHE-RSA-AES256-GCM-SHA384',
            'DHE-RSA-AES128-GCM-SHA256',
            'DHE-RSA-AES256-GCM-SHA384',
         ],
        'TLS 1.3' => [
            'TLS_AES_256_GCM_SHA384',
            'TLS_CHACHA20_POLY1305_SHA256',
            'TLS_AES_128_GCM_SHA256',
        ]
    ];

    $serviceUrl = 'https://www.howsmyssl.com/a/check';

    // as in LemonwayHttpClient
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $serviceUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_ANY);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $proxyHost = container()->getParameter('lemonway_proxy.host');
    $proxyPort = container()->getParameter('lemonway_proxy.port');
    $proxyUser = container()->getParameter('lemonway_proxy.user');
    $proxyPassword = container()->getParameter('lemonway_proxy.password');

    // Init proxy
    if ("" !== $proxyHost && \is_int($proxyPort)) {
        curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, true);

        // Proxy URL
        curl_setopt($ch, CURLOPT_PROXY, $proxyHost);
        curl_setopt($ch, CURLOPT_PROXYPORT, $proxyPort);

        // Proxy authenticate
        if ("" !== $proxyUser && "" != $proxyPassword) {
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxyUser . ":" . $proxyPassword);
        }
    }

    $result = \json_decode(
        \curl_exec($ch),
        true,
    );

    $availableCiphers = $result['given_cipher_suites'];

    $compatibleCiphers = \array_map(
        function ($ciphers) use ($availableCiphers) {
            return \array_filter(
                $ciphers,
                fn($cipher) => \in_array($cipher, $availableCiphers),
            );
        },
        $lemonWayCiphers,
    );

    (
        new JsonResponse(
            [
                'Wizaplace Lemonway compatible ciphers' => $compatibleCiphers,
                'Lemonway ciphers' => $lemonWayCiphers,
                'howsmyssl.com' => $result,
            ]
        )
    )
    ->send();
};

/**
 * List all products with invalid inventory
 */
$listDiffBetweenInventoryAndExceptions = function() {
    $doctrine = container()->get('doctrine.dbal.default_connection');
    $exceptions=$doctrine->fetchAllAssociative(
        'SELECT * FROM cscart_product_options_exceptions;'
    );
    /*
     * La requete au dessus génére une liste de la forme
     * [
     *     ["product_id" => 1, "combination" =>"a:1:{i:5;i:50;}", "option_id" => 5, "variant_id" => 50]
     *     ["product_id" => 1, "combination" =>"a:1:{i:5;i:51;}", "option_id" => 5, "variant_id" => 51]
     *     ["product_id" => 1, "combination" =>"a:1:{i:6;i:80;}", "option_id" => 6, "variant_id" => 80]
     *     ["product_id" => 2, "combination" =>"a:1:{i:5;i:50;}", "option_id" => 5, "variant_id" => 50]
     * ]
     * Le array_reduce renvoit :
     * [
     *      1 => [
     *              5 => [ 50 => true, 51 => true]
     *              6 => [ 80 => true]
     *            ],
     *      2 => [
     *              5=>50
     *            ]
     * ]
     */
    $exceptions = array_reduce(
        $exceptions,
        function($carry, $item) {
            $carry[$item['product_id']][$item['option_id']][$item['variant_id']] = true;
            return $carry;
        }
    );

    $inventory = $doctrine->fetchAllAssociative(
        'SELECT product_id, combination FROM cscart_product_options_inventory;'
    );

    /*
     * La requete renvoit un tableau :
     * [
     *      ["product_id" = 1, "combination"=> "5_50_6_80",
     *      ["product_id" = 1, "combination"=> "5_55_6_80",
     *      ["product_id" = 1, "combination"=> "5_55_6_81",
     *      ["product_id" = 2, "combination"=> "5_50"
     * ]
     * Le array_filter va retourner seulement les combinaisons qui ne sont pas compatibles avec les exceptions.
     * Dans notre cas :
     * [
     *      ["product_id" = 1, "combination"=> "5_55_6_80",
     *      ["product_id" = 1, "combination"=> "5_55_6_81",
     * ]
     */
    $invalidCombinations = array_filter(
        $inventory,
        function($inventory) use ($exceptions) {
            $combination = explode('_', $inventory['combination']);
            $pId = $inventory['product_id'];
            for ($i=0; $i<= count($combination)/2; $i+=2) {
                if (!$exceptions[$pId][$combination[$i]][$combination[$i+1]]) {
                    return true;
                }

            }
            return false;
        }
    );

    /*
     * Le array_reduce permet de regrouper les lignes par product_id. Dans le cas de l'exemple précédent :
     * [
     *      1 => ["5_55_6_80", "5_55_6_81"]
     * ]
     */
    $invalidCombinations = array_reduce(
        $invalidCombinations,
        function ($carry, $item) {
            $carry[$item['product_id']][] = $item['combination'];
            return $carry;
        },
        []
    );

    header('Content-type: application/json');
    echo json_encode($invalidCombinations);
    die();
};

$actions = [
    'update_sitemap' => ['\Wizacha\Misc', 'updateSitemapFilesDelayed'],
    'automated_products_feeds' => $importAutomatedFeedsCommand,
    'company_options' => ['\Wizacha\Company', 'updateCompanyOptions'],
    'payment_deferment_orders_command' => $paymentDefermentOrdersCommand,
    'synchronise_company_type' => ['\Wizacha\Company', 'synchronizeDataC2C'],
    'update_orders_to_completed' => ['\Wizacha\OrderStatus', 'completeProcessed'],
    'search_engine_update_products_config'  => $updateProductIndexParams,
    'search_engine_update_products' => $update_search_product_index,
    'search_engine_create_index' => $createSearchIndex,
    'update_read_model_product' => $update_read_model_product,
    'clear_read_model_product' => $clear_read_model_product,
    'dump_read_model_product' => $dump_read_model_product,
    'synchronize_product_count'  => ['\Wizacha\Category', 'synchronizeCount'],
    'regenerate_seo_names' => $regenerate_seo_names,
    'create_missing_translations_keys' => $create_missing_translations_keys,
    'test_email' => $test_email,
    'phpinfo' => $phpinfo,
    'install_division_products' => ['\Wizacha\Division', 'installProducts'],
    'debouncer_info' => $debouncer_info,
    'queue_info' => $queueInfo,
    'subscription_renew' => $subscriptionRenew,
    'cipher_info' => $cipherInfo,
    'invalid_inventory_list' => $listDiffBetweenInventoryAndExceptions
];
ksort($actions);

if (isset($actions[$mode]) && is_callable($actions[$mode])) {
    try {
        $return = call_user_func($actions[$mode]);
        $message = (is_string($return)) ? $mode . "<br>" . $return : $mode;

        fn_set_notification('N', __('information'), $message);
    } catch (\Throwable $exception) {
        fn_set_notification('E', __('error'), $exception->getMessage());
    }

    return [CONTROLLER_STATUS_OK, 'patch.manage'];
}

//Use method documentation for descriptions
$descriptions = array_map(
    function ($callable) {
        if (is_array($callable)) {
            list($class, $method) = $callable;
            $reflection = new ReflectionMethod($class, $method);
        } else {
            $reflection = new ReflectionFunction($callable);
        }

        return trim(
            preg_replace(
                '@^\s*[/\*]+\s?@m',
                '',
                $reflection->getDocComment()
            )
        );
    },
    $actions
);
\Tygh\Registry::get('view')->assign('content_tpl', 'addons/wizacha_backend_design/views/patch/manage.tpl');
\Tygh\Registry::get('view')->assign('actions', $descriptions);
\Tygh\Registry::get('view')->assign('paymentNotificationUrl', container()->get('router')->generate(
    'payment_notification_transfer_standby',
    [
        'order_id' => 1,
        'payment_redirect_url' => 'https://example.com',
        'utm_nooverride' => '1',
    ],
    \Symfony\Component\Routing\RouterInterface::ABSOLUTE_URL
));

