<?php

use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!empty($dispatch_extra)) {
        if (!empty($_REQUEST['approval_data'][$dispatch_extra])) {
            $_REQUEST['approval_data'] = $_REQUEST['approval_data'][$dispatch_extra];
        }
    }


    if ($mode == 'features_approval' && !empty($_REQUEST['approval_data'])) {
        $status = Registry::get('runtime.action') == 'approve' ? 'Y' : 'N';
        $send_notification = false;
        $reason = '';
        if (isset($_REQUEST['approval_data']['notify_user_' . $status]) && $_REQUEST['approval_data']['notify_user_' . $status] == 'Y') {
            $send_notification = true;
            $reason = $_REQUEST['approval_data']['reason_' . $status];
        }

        fn_w_premoderate_feature([$_REQUEST['approval_data']], $status, $send_notification, $reason);

    } elseif (($mode == 'm_feature_approve' || $mode == 'm_feature_decline') && !empty($_REQUEST['features_ids'])) {
        if ($mode == 'm_feature_approve') {
            $status = 'Y';
            $reason = $_REQUEST['action_reason_approved'];
            $send_notification = isset($_REQUEST['action_notification_approved']) && $_REQUEST['action_notification_approved'] == 'Y' ? true : false;
        } else {
            $status = 'N';
            $reason = $_REQUEST['action_reason_declined'];
            $send_notification = isset($_REQUEST['action_notification_declined']) && $_REQUEST['action_notification_declined'] == 'Y' ? true : false;
        }

        $features = array_reduce(
            $_REQUEST['features_ids'],
            function (&$result, $feature_id) {
                $result[] = array_merge(['feature_id' => $feature_id], $_REQUEST['features_data'][$feature_id]);
                return $result;
            }
        );
        fn_w_premoderate_feature($features, $status, $send_notification, $reason);
    }

    return;
}

if ($mode == 'features_approval' && !Registry::get('runtime.company_id')) {
    $params = $_REQUEST;
    $params['statuses'] = 'H';

    list($features, $search, ) = fn_get_product_features($params, Registry::get('settings.Appearance.admin_elements_per_page'), (string) GlobalState::contentLocale());
    array_walk(
        $features,
        function (&$feature) {
            $feature['extend_category_path'] = array_map('fn_get_category_path',explode(',',$feature['categories_path']));
        }
    );

    Registry::get('view')->assign('features', $features);
    $schema = fn_get_schema('features', 'features_type');
    Registry::get('view')->assign('features_type', $schema);
    Registry::get('view')->assign('search', $search);
} elseif ('products_approval' == $mode) {
    $products = Registry::get('view')->getTemplateVars('products');
    fn_gather_additional_products_data(
        $products,
        [
            'get_icon'         => true,
            'get_detailed'     => true,
            'get_additional'   => false,
            'get_options'      => false,
            'get_discounts'    => false,
            'get_features'     => false,
            'get_extra'        => false,
            'get_taxed_prices' => false,
        ]
    );
    Registry::get('view')->assign('products', $products);
}
