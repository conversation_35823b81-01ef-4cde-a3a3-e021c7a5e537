<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */


if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return;
}

if ('import' == $mode) {
    //See elf_connector_s3.php
    $extra_path = '';
    if (Tygh\Registry::get('runtime.company_id')) {
        $extra_path .= 'companies/' . Tygh\Registry::get('runtime.company_id') . '/';
    }
    $elfinderStorage = container()->get('Wizacha\Storage\ElfinderStorageService');
    Tygh\Registry::get('view')->assign('images_root_url', $elfinderStorage->getUrl($extra_path));

    $tabs = Tygh\Registry::get('navigation.tabs');
    unset($tabs['qty_discounts']);
    Tygh\Registry::set('navigation.tabs', $tabs);
}
