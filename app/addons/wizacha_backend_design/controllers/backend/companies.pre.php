<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($mode == 'update') {
        if(($id = Registry::get('runtime.company_id')))
        {
            if(isset($_REQUEST['company_data']))
            {
                $_REQUEST['company_data'] = fn_w_object_filter($_REQUEST['company_data'],'company');
                $_POST['company_data'] = fn_w_object_filter($_POST['company_data'],'company');
                //we HAVE to put a company name, else the original controller won't register changes
                $_POST['company_data']['company']
                    = $_REQUEST['company_data']['company']
                        = Registry::get('runtime.company_data')['company'];
            }
        }
    } elseif ('w_automated_feeds' == $mode) {
        if (isset($_REQUEST['feed']) && is_array($_REQUEST['feed'])) {
            list($results, $errors) = \Wizacha\Company::updateFeeds(
                $_REQUEST['feed'],
                \Tygh\Registry::get('runtime.company_id')
            );

            foreach ($errors as $url) {
                fn_set_notification('E', __('error'), __('text_invalid_url').' : '.$url);
            }

            $results = array_filter(
                $results,
                function ($result) {
                    return !$result;
                }
            );
            if (!empty($results)) {
                fn_set_notification('E', __('error'), __('text_changes_not_saved'));
            } else {
                fn_set_notification('N', __('notice'), __('text_changes_saved'));
            }
        }
        //to avoid redirection to 'companies'
        $_REQUEST['redirect_url'] = 'companies.w_automated_feeds';
    }
    return;
}

if ('w_automated_feeds' == $mode) {
    if (\Tygh\Registry::get('runtime.company_id')) {
        $feed_infos = \Wizacha\Company::getFeeds(\Tygh\Registry::get('runtime.company_id'));

        \Tygh\Registry::get('view')->assign('feed', $feed_infos);

    }
}
