<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($mode == 'images') {

    $extra_path = '';

    if (Registry::get('runtime.company_id')) {
        $extra_path .= 'companies/' . Registry::get('runtime.company_id') . '/';
    }

    $opts = [
        'roots' => [
            container()->get('Wizacha\Storage\ElfinderStorageService')->getElFinderOpts($extra_path),
        ],
    ];

    $opts['roots'][0]['alias'] = __('home');
    $opts['roots'][0]['uploadAllow'] = ['image'];
    $opts['roots'][0]['uploadMaxSize'] = TINY_UPLOAD_MAX_SIZE;
    $fm = new elFinderConnector(new elFinder($opts));
    $fm->run();
}

if ($mode == 'csv') {

    $registry = \Wizacha\Registry::defaultInstance();
    if (\Wizacha\Company::runtimeID(
        $registry->get([\Wizacha\Config::REG_AREA]),
        $_SESSION,
        $registry
    )) {
        return [CONTROLLER_STATUS_DENIED];
    }

    $extra_path = '';

    $opts = [
        'roots' => [
            container()->get('Wizacha\Storage\CsvStorageService')->getElFinderOpts($extra_path),
        ],
    ];

    $opts['roots'][0]['alias'] = __('home');
    $opts['roots'][0]['uploadAllow'] = [];

    $fm = new elFinderConnector(new elFinder($opts));
    $fm->run();
}

exit;
