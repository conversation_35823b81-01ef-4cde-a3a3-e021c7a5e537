<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return;
}
if ($mode == 'update') {
    //Remove data from templates
    if (Registry::get('runtime.company_id')){
        $tabs = Tygh\Registry::get('navigation.tabs');
        $tabs = array_intersect_key($tabs,['detailed'=>1]);
        Registry::get('view')->clearAssign('discussion');
        Registry::set('navigation.tabs',$tabs); //Data removed by hook detailed_content.override.tpl

        $authorized = [
            'category_id',
            'category',
            'parent_id',
            'description',
            'status',
        ];
        $category_data = Registry::get('view')->getTemplateVars('category_data');
        $category_data = array_intersect_key($category_data,array_flip($authorized));
        Registry::get('view')->assign('category_data', $category_data);
    }
}
