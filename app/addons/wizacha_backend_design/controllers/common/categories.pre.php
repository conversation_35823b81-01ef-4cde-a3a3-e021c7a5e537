<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return;
}
if ($mode == 'manage') {

    /* get the first level of categories to the select list*/
    $list_higher_level = fn_get_subcategories('0');
    Registry::get('view')->assign('first_level_categories',  $list_higher_level);

    if (isset($_REQUEST['search'])) {

        $added_params['page'] = empty($_REQUEST['page']) ? 1 : intval($_REQUEST['page']);
        $added_params['category_id'] = empty($_REQUEST['search_in']) ? 0 : intval($_REQUEST['search_in']);
        $added_params['limit'] = empty($_REQUEST['items_per_page']) ? \Tygh\Registry::get('settings.Appearance.admin_elements_per_page') : intval($_REQUEST['items_per_page']);
        $added_params['status'] = array(\Wizacha\Status::ENABLED , \Wizacha\Status::HIDDEN);
        list($result, $params) = \Wizacha\Category::search($_REQUEST['search'], (string) GlobalState::contentLocale(), $added_params);

        $separating_char = '>';
        $special_char = '$#@+-';
        // get the full path of a category and Wrap his name with a bold tag.
        foreach ($result as $index => &$category) {
            if ($category['parent_id'] > 0) {
                $path = fn_get_category_path($category['category_id'], (string) GlobalState::contentLocale(), $special_char);
                $path = explode($special_char, $path);
                $lastRefPath = & $path[count($path) - 1];
                $lastRefPath = '<bold >' . $lastRefPath . '</bold>';
                $category['path'] = implode($separating_char, $path);
            } else {
                $category['path'] = '<bold>' . $category['category'] . '</bold>';
            }
            $category['has_children'] = !fn_w_is_leaf_category($category['category_id']);

            if (!isset($category['is_transactional'])) { // can be already set
                $categoryObject = new \Wizacha\Category($category['category_id']);
                if($categoryObject->isStored()) {
                    $category['is_transactional'] = $categoryObject->isTransactional();
                } else {
                    container()->get('logger')->addError("Search engine gave us category #{$category['category_id']} but it wasn't found in the DB");
                    unset($result[$index]);
                }
            }
        }

        Registry::get('view')->assign('search', $params);
        Registry::get('view')->assign('categories_tree', $result);

        if (defined('AJAX_REQUEST')) {
            if(!empty($_REQUEST['search_id'])) {
                Registry::get('view')->assign('search_id',$_REQUEST['search_id']);
            }
            Registry::get('view')->display('views/categories/components/categories_search_tree.tpl');
            exit;
        }

    }
}
