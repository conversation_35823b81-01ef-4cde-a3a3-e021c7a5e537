<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */


use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Accounting\AccountingPayment;
use Wizacha\Marketplace\Accounting\Period;
use Wizacha\Tax;
use Wizacha\Marketplace\Order\OrderStatus;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return;
}

if ('view' == $mode) {
    $item = $_REQUEST['page']?:1;
    fn_add_breadcrumb(__('w_c2c_accounting'));

    $company_id = \Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance());
    //Check if user is an admin
    $is_admin = false;
    if (!$company_id) {
        if ('A' == AREA) {
            $is_admin = true;
        } else {
            return [CONTROLLER_STATUS_OK];
        }
    }

    if ($company_id) {
        $items_per_page = $_REQUEST['items_per_page']?:10;
    } else {
        $items_per_page = 1;
    }

    // hack that show the current 15 days period
    if(isset($_GET['current'])){
        $item = 0;
    }
    $period = Period::getPeriod(time(), $item);

    // hack that show today orders instead of the last completed half month orders
    if(isset($_GET['today'])){
        $day =  60 * 60 * 24 * 1; // a full day
        $now = time();
        $period = [
            'start' => $now - $day,
            'end' => $now,
        ];
    };

    /** @var AccountingPayment $payment */
    $payment = container()->get('marketplace.accounting.payment');
    if ($is_admin) {
        $data = $payment->getPayments($period['start'], $period['end']);
        $label = array_map(
            function ($company_data) {
                return ['title'=>fn_get_company_name(reset($company_data)['company_id'])];
            },
            $data
        );
        $label[0] = $period;
    } elseif ($company_id) {
        $start = ($item-1) * $items_per_page + 1;
        $end = ($item)*$items_per_page;
        $startPeriod = Period::getPeriod(time(), $start);
        $endPeriod = Period::getPeriod(time(), $end);
        $date_format = \Tygh\Registry::get('settings.Appearance.date_format');
        $data = [];
        for ($selectedPeriod = $start; $selectedPeriod <= $end; $selectedPeriod++){
            $period = Period::getPeriod(time(), $selectedPeriod);
            $dataSelected = $payment->getVendorPayments(
                $period['start'],
                $period['end'],
                $company_id
            );
            if(\count($dataSelected) > 0) {
                $data[\key($dataSelected)] = $dataSelected[\key($dataSelected)];
            }
        }
        $label = array_map(
            function ($company_data) use ($date_format) {
                $period = Period::getPeriodByID(reset($company_data)['period_id']);
                if ($period) {
                    return [
                        'period' => $period,
                        'title' => sprintf(
                            '%s %s %s %s',
                            __('w_from'),
                            strftime($date_format, $period['start']),
                           __('w_to'),
                           strftime($date_format, $period['end']-1)
                        ),
                    ];
                }
            },
            $data
        );
        $label[0] = ['start' => $endPeriod['start'], 'end' => $startPeriod['end']];
    }

    if (!empty($data)) {
        $accountingService = container()->get('marketplace.accounting.service');

        [$totalsTaxes, $shippingTaxes, $orders] = $accountingService->getOrderTaxes($data, $is_admin);

        \Tygh\Registry::get('view')->assign('totalsTaxes', $totalsTaxes);
        \Tygh\Registry::get('view')->assign('shippingTaxes', $shippingTaxes);
        \Tygh\Registry::get('view')->assign('orders', $orders);

        $aggregate = [];

        $aggregate_function = function ($data) {
            return [
                'donated_total' => array_sum(array_column($data, 'donated')),
                'w_commission' => array_sum(array_column($data, 'w_commission')),
                'vat' => array_sum(array_column($data, 'vat')),
                'shipping_cost' => array_sum(array_column($data, 'shipping_cost')),
            ];
        };

        $aggregate = array_map($aggregate_function, $data);
        $aggregate[0] = $aggregate_function($aggregate);
        \Tygh\Registry::get('view')->assign('aggregate', $aggregate);

        $getShippingTaxes = function ($shippingTaxes): array {
            return ['fullShippingTaxes' => array_sum(array_column($shippingTaxes, 'shippingTaxe'))];
        };

        $fullShipping = array_map($getShippingTaxes, $shippingTaxes);
        \Tygh\Registry::get('view')->assign('fullShipping', $fullShipping);

        $getTotalTaxes = function ($totalsTaxes): array {
            return ['fullTotalTaxes' => array_sum(array_column($totalsTaxes, 'totalsTaxe'))];
        };

        $fullTotal = array_map($getTotalTaxes, $totalsTaxes);
        \Tygh\Registry::get('view')->assign('fullTotal', $fullTotal);

        $tax = Tax::getFullRateValue();
        \Tygh\Registry::get('view')->assign('tax', $tax);
    }

    if ($company_id) {
        krsort($data);
    }

    \Tygh\Registry::get('view')->assign('data', $data);
    \Tygh\Registry::get('view')->assign('data_label', $label);

    \Tygh\Registry::get('view')->assign(
        'statusCompleted',
        container()->getParameter('feature.activate_workflow_translation') ? 'workflow_withdrawal-period_pending-withdrawal-period-end_completed' : OrderStatus::COMPLETED()
    );

    $search = [
        'item' => $item,
        'total_items' => Period::getNumberOfPeriod(time())-1,
        'items_per_page' =>$items_per_page,
        'page' => $item,
    ];

    \Tygh\Registry::get('view')->assign('search', $search);
    \Tygh\Registry::get('view')->assign('company_id', $company_id);

    $templating = container()->get('templating');
    if (AREA == 'C' && $templating->exists('@App/frontend/views/profile/c2c/accounting.html.twig')) {
        $html = $templating->render('@App/frontend/views/profile/c2c/accounting.html.twig');
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }

    \Tygh\Registry::get('view')->assign('access', $_REQUEST['price']);
}
