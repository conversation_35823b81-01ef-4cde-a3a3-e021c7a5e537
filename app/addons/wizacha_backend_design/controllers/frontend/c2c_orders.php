<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Symfony\Component\HttpFoundation\Response;
use Tygh\Registry;
use function Wizacha\Marketplace\Order\is_order_status_equal_to;
use Wizacha\Marketplace\Order\Event\OrderCodeFailed;
use Wizacha\User;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

// C2C disabled
if (!container()->getParameter('feature.enable_c2c')) {
    return array(CONTROLLER_STATUS_REDIRECT, fn_url());
}

if (empty($auth['user_id'])) {
    return array(CONTROLLER_STATUS_REDIRECT, "auth.login_form?return_url=".urlencode(Registry::get('config.current_url')));
}

// Admin and Professional vendor can't access orders within c2c
$user = new User($auth['user_id']);
if ($user->isAdmin() || $user->isProfessionalVendor()) {
    fn_set_notification('W', __('warning'), __('c2c_access_denied_text'));
    return array(CONTROLLER_STATUS_REDIRECT, fn_url(''));
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ('update' == $mode) {
        if (!isset($_REQUEST['shipment_data'])) {
            fn_set_notification('E', __('error'), __('w_missing_parameters'));
            return array(CONTROLLER_STATUS_REDIRECT, "c2c_orders.manage");
        }

        if (!fn_check_company_id('orders', 'order_id', $_REQUEST['shipment_data']['order_id'])) {
            return [CONTROLLER_STATUS_NO_PAGE];
        }

        fn_update_shipment($_REQUEST['shipment_data'], 0, 0, false, ['C' => true]);

        \Wizacha\OrderStatus::automaticUpdate($_REQUEST['shipment_data']['order_id']);

        $order = container()->get('marketplace.order.order_service')->getOrder($_REQUEST['shipment_data']['order_id']);

        /**
         * Avancement du workflow
         */
        if ($order->areAllShipmentsSent()) {
            $markAsShipped = container()->get('marketplace.order.action.mark_as_shipped');
            if ($markAsShipped->isAllowed($order)) {
                $markAsShipped->execute($order);
            }
        }

        return array(CONTROLLER_STATUS_OK, "c2c_orders.manage");
    }

    if ('update_code' == $mode) {
        if (!fn_check_company_id('orders', 'order_id', $_REQUEST['order_id'])) {
            return [CONTROLLER_STATUS_NO_PAGE];
        }

        if (!empty(trim($_REQUEST['try_code']))) {
            $order = container()->get('marketplace.order.order_service')->overrideLegacyOrder($_REQUEST['order_id']);
            $result = $order[\Wizacha\C2C\Order::KEY_CODE_IN_ORDER]->validateWithLog(trim($_REQUEST['try_code']));
            \Wizacha\C2C\Order::saveCode($order);

            if ($result) {
                fn_change_order_status($_REQUEST['order_id'], \Wizacha\OrderStatus::COMPLETED);
                fn_set_notification('N', __('notice'), __('w_successful_code_registration'), 'S');
                return array(CONTROLLER_STATUS_OK, "c2c_orders.manage");
            } else {
                $remain = \Wizacha\C2C\Code::MAX_LOGGED_ATTEMPT - count($order[\Wizacha\C2C\Order::KEY_CODE_IN_ORDER]->logs());
                fn_set_notification('W', __('warning'), __('w_fail_code_registration', ['[remain_try]'=> $remain]));
                if ($remain <= 0) {
                    $orderObj = container()->get('marketplace.order.order_service')->getOrder($order['order_id']);
                    container()->get('event_dispatcher')->dispatch(new OrderCodeFailed($orderObj), OrderCodeFailed::class);
                }
            }
        }
        return [CONTROLLER_STATUS_OK, "c2c_orders.update?order_id=".$_REQUEST['order_id']];
    }
    return;
}

if (!empty($_REQUEST['order_id']) && !fn_check_company_id('orders', 'order_id', $_REQUEST['order_id'])) {
    return array(CONTROLLER_STATUS_NO_PAGE);
}

if ('manage' == $mode) {
    fn_add_breadcrumb(__('w_c2c_orders_manage'));
    $orders = [];
    $orderService = container()->get('marketplace.order.order_service');

    if ($auth['company_id']) {
        $params['company_id'] = $auth['company_id'];

        list($orders) = fn_get_orders($params);

        //filter orders to eliminate unpaid / canceled / refused
        $orders = array_filter($orders, function ($order) use ($orderService){
            return $orderService->isPaid($order['order_id']);
        });

        foreach ($orders as &$order) {
            $order['products'] = $orderService->overrideLegacyOrder($order['order_id'])['products'];
        }
    }

    Registry::get('view')->assign('orders', $orders);

    $templating = container()->get('templating');
    if ($templating->exists('@App/frontend/views/profile/c2c/orders.html.twig')) {
        // When orders are objects this can be a method
        $orders = array_map(function (array $order) use ($orderService) {
            $order['is_paid'] = $orderService->isPaid($order['order_id']);
            return $order;
        }, $orders);
        $html = $templating->render('@App/frontend/views/profile/c2c/orders.html.twig', [
            'orders' => $orders,
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }
} elseif ('update' == $mode) {
    if (!isset($_REQUEST['order_id'])) {
        fn_set_notification('E', __('error'), __('w_missing_parameters'));
        return array(CONTROLLER_STATUS_REDIRECT, "c2c_orders.manage");
    }

    $order = container()->get('marketplace.order.order_service')->overrideLegacyOrder($_REQUEST['order_id']);

    $params = [
        'order_id' => $order['order_id'],
        'advanced_info' => true
    ];
    list($order['shipment_datas']) = fn_get_shipments_info($params);

    fn_add_breadcrumb(__('w_c2c_orders_manage'));
    Registry::get('view')->assign('order', $order);

    $shipping_id = $order['shipping_ids']?:reset($order['shipping'])['shipping_id'];
    Registry::get('view')->assign('order_require_code', \Wizacha\Shipping::requiredCode($shipping_id));

    $templating = container()->get('templating');
    if ($templating->exists('@App/frontend/views/profile/c2c/order.html.twig')) {
        $html = $templating->render('@App/frontend/views/profile/c2c/order.html.twig', [
            'order' => $order,
            'orderRequiresCode' => \Wizacha\Shipping::requiredCode($shipping_id),
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }
} elseif ('accept' == $mode) {

    if (empty($_REQUEST['order_id'])) {
        fn_set_notification('E', __('error'), __('w_missing_parameters'));
        return array(CONTROLLER_STATUS_REDIRECT, "c2c_orders.manage");
    }

    if (is_order_status_equal_to($_REQUEST['order_id'], \Wizacha\OrderStatus::STANDBY_VENDOR)) {
        /**
         * Avancement du workflow
         */
        $order = container()->get('marketplace.order.order_service')->getOrder($_REQUEST['order_id']);
        $validate = container()->get('marketplace.order.action.accept');
        if ($validate->isAllowed($order)) {
            $validate->execute($order);
        }

        if (!fn_change_order_status(
            $order->getId(),
            \Wizacha\OrderStatus::PROCESSING_SHIPPING,
            '',
            fn_get_notification_rules($_REQUEST)
        )) {
            fn_set_notification('E', __('error'), __('w_change_status_order_not_allowed'));
        }
    } else {
        fn_set_notification('E', __('error'), __('w_change_status_order_not_allowed'));
    }

    return array(CONTROLLER_STATUS_REDIRECT, "c2c_orders.update?order_id=".$_REQUEST['order_id']);
} elseif ('refuse' == $mode) {

    if (empty($_REQUEST['order_id'])) {
        fn_set_notification('E', __('error'), __('w_missing_parameters'));
        return array(CONTROLLER_STATUS_REDIRECT, "c2c_orders.manage");
    }

    $order = container()->get('marketplace.order.order_service')->overrideLegacyOrder($_REQUEST['order_id']);

    foreach ($order['products'] as $order_product) {
        $product['amount'] = 0;
        $product['product_id'] = $order_product['product_id'];
        fn_update_product($product, $product['product_id']);
    }

    fn_set_notification('W', __('warning'), __('w_c2c_refuse_order_notification', ['[URL]' => fn_url('c2c_products.manage')]));

    if (is_order_status_equal_to($order['order_id'], \Wizacha\OrderStatus::STANDBY_VENDOR)) {
        /**
         * Avancement du workflow
         */
        $order = container()->get('marketplace.order.order_service')->getOrder($order['order_id']);
        $refuse = container()->get('marketplace.order.action.refuse');
        if ($refuse->isAllowed($order)) {
            $refuse->execute($order);
        }

        if (!fn_change_order_status(
            $order->getId(),
            \Wizacha\OrderStatus::VENDOR_DECLINED,
            '',
            fn_get_notification_rules($_REQUEST)
        )) {
            fn_set_notification('E', __('error'), __('w_change_status_order_not_allowed'));
        }
    } else {
        fn_set_notification('E', __('error'), __('w_change_status_order_not_allowed'));
    }

    return array(CONTROLLER_STATUS_OK, "c2c_orders.manage?selected_section=c2c_orders_manage");
}
