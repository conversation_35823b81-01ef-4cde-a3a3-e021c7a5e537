<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Symfony\Component\HttpFoundation\Response;
use Tygh\Registry;
use Wizacha\Marketplace\Company\Event\C2cCompanyApplied;
use Wizacha\User;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

// C2C disabled
if (!container()->getParameter('feature.enable_c2c')) {
    return array(CONTROLLER_STATUS_REDIRECT, fn_url());
}

if (empty($auth['user_id'])) {
    return array(CONTROLLER_STATUS_REDIRECT, "auth.login_form?return_url=".urlencode(Registry::get('config.current_url')));
}

// Admin and Professional vendor can't access company information within c2c
$user = new User($auth['user_id']);
if ($user->isAdmin() || $user->isProfessionalVendor()) {
    fn_set_notification('W', __('warning'), __('c2c_access_denied_text'));
    return array(CONTROLLER_STATUS_REDIRECT, fn_url(''));
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ('update_informations' == $mode) {

        $company_id = \Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance());
        $is_new_company = !boolval($company_id);

        $acceptable_user_fields = [
            'fields',
            'birthday',
            'lastname',
            'firstname'
        ];

        $user_data = array_intersect_key($_REQUEST['user_data'], array_flip($acceptable_user_fields));

        fn_update_user($auth['user_id'], $user_data, $auth, false, false, false);

        if (!\Wizacha\Company::checkApplyForC2CAdditionalFields([], $_FILES)) {
            fn_save_post_data('user_data', 'company_data');
            fn_set_notification(
                'E',
                __('warning'),
                __('text_fill_the_mandatory_fields')
            );
            return array(CONTROLLER_STATUS_REDIRECT , $_REQUEST['return_url']);
        }

        foreach ($_FILES as $file) {
            if (!fn_w_check_apply_for_vendor_files($file)) {
                fn_save_post_data('user_data', 'company_data');
                fn_set_notification(
                    'E',
                    __('error'),
                    __(
                        'text_forbidden_uploaded_file_extension',
                        [
                            '[ext]'=>strtolower(fn_get_file_ext($file['name'])),
                            '[exts]'=>implode(',', fn_w_get_vendor_files_allowed_extensions())
                        ]
                    )
                );
                return array(CONTROLLER_STATUS_REDIRECT , $_REQUEST['return_url']);
            }
        }

        $acceptable_company_fields = [
            'phone',
            'address',
            'zipcode',
            'city'
        ];

        $company_data = array_intersect_key($_REQUEST['company_data'], array_flip($acceptable_company_fields));

        if ($is_new_company) {
            $auth['company_id'] = $company_id = \Wizacha\Company::newC2C($auth['user_id']);
        }

        fn_update_company($company_data, \Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance()));

        fn_w_put_files_for_vendor_post($company_id);
        $current_company_data = fn_get_company_data($company_id);
        if ($current_company_data['status'] == \Wizacha\Company::STATUS_NEW) {
            $new_data = [
                'company' => $_REQUEST['company_data']['company'],
                'w_company_meta_description'=> '',
                'w_company_meta_keywords'=> '',
                'seo_name'=> '',
            ];
            fn_update_company($new_data, $company_id);
        }

        fn_set_notification('N', __('notice'), __('w_c2c_update_informations_success'));

        if ($is_new_company) {
            //Notify user
            $adminFirstName = $_REQUEST['user_data']['firstname'] ?? '';
            $adminLastName = $_REQUEST['user_data']['lastname'] ?? '';
            $eventDispatcher = container()->get('event_dispatcher');
            $eventDispatcher->dispatch(
                new C2cCompanyApplied($current_company_data, $adminFirstName, $adminLastName),
                C2cCompanyApplied::class
            );
        }

        return [CONTROLLER_STATUS_REDIRECT, 'c2c_company.update_informations'];
    }

    if ('update' == $mode) {
        fn_trusted_vars('company_data');

        if (!$auth['company_id']) {
            $auth['company_id'] = \Wizacha\Company::newC2C($auth['user_id']);
            fn_set_notification('W', __('warning'), __('w_c2c_new_company', ['[url]' => fn_url('c2c_company.update_informations')]));
        }

        $valid_keys = ['company_description'];
        $data = array_intersect_key($_REQUEST['company_data'], array_flip($valid_keys));
        fn_update_company($data, \Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance()));
        return [CONTROLLER_STATUS_REDIRECT, 'c2c_company.update'];
    }

    return;
}

if ('update' == $mode) {
    fn_add_breadcrumb(__('w_c2c_company_update'));

    $templating = container()->get('templating');
    if ($templating->exists('@App/frontend/views/profile/c2c/description.html.twig')) {
        $company = fn_get_company_data(\Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance()));
        $html = $templating->render('@App/frontend/views/profile/c2c/description.html.twig', [
            'company' => $company,
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }
} elseif ('update_informations' == $mode) {
    fn_add_breadcrumb(__('w_c2c_company_update_informations'));
    $user_data = fn_get_user_info($auth['user_id'], true, $profile_id);
    $profile_fields = fn_get_profile_fields();

    foreach ($profile_fields['C'] as &$field) {
        $field['required'] = 'Y';
    }

    Registry::get('view')->assign('profile_fields', $profile_fields);
    Registry::get('view')->assign('user_data', $user_data);

    $templating = container()->get('templating');
    if ($templating->exists('@App/frontend/views/profile/c2c/information.html.twig')) {
        $company = fn_get_company_data(\Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance()));
        $html = $templating->render('@App/frontend/views/profile/c2c/information.html.twig', [
            'company' => $company,
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }
}
$company_data = fn_get_company_data(\Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance()));
\Wizacha\Registry::defaultInstance()->get(['view'])->assign('company_data', $company_data);
