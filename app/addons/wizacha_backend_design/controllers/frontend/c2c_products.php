<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Symfony\Component\HttpFoundation\Response;
use Tygh\Registry;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\User;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

// C2C disabled
if (!container()->getParameter('feature.enable_c2c')) {
    return array(CONTROLLER_STATUS_REDIRECT, fn_url());
}

if (empty($auth['user_id'])) {
    return array(CONTROLLER_STATUS_REDIRECT, "auth.login_form?return_url=".urlencode(Registry::get('config.current_url')));
}

if (!empty($_REQUEST['product_id']) && !fn_check_company_id('products', 'product_id', $_REQUEST['product_id'])) {
    return [CONTROLLER_STATUS_DENIED];
}

// Admin and Professional vendor can't access products within c2c
$user = new User($auth['user_id']);
if ($user->isAdmin() || $user->isProfessionalVendor()) {
    fn_set_notification('W', __('warning'), __('c2c_access_denied_text'));
    return array(CONTROLLER_STATUS_REDIRECT, fn_url(''));
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($mode == 'get_features') {

        if (defined('AJAX_REQUEST')) {
            $product_features = [];
            if (!empty($_REQUEST['category_id'])) {
                $category_id = filter_var($_REQUEST['category_id'], FILTER_SANITIZE_NUMBER_INT);
                $product_id = empty($_REQUEST['product_id'])
                    ? 0
                    : filter_var($_REQUEST['product_id'], FILTER_SANITIZE_NUMBER_INT);

                $path = explode(
                    '/',
                    \Tygh\Database::getField(
                        "SELECT id_path FROM ?:categories WHERE category_id = ?i",
                        $category_id
                    )
                );
                $params = [
                    'category_ids'  => $path,
                    'product_id'    => $product_id,
                    'statuses'      => ['A'],
                    'variants'      => true,
                    'plain'         => false,
                ];
                list($product_features) = fn_get_product_features($params);
            }

            Registry::get('view')->assign('product_features', $product_features);
            $template = Registry::get('view')->fetch('views/products/components/product_assign_features.tpl');

            Registry::get('ajax')->assignHtml('product_features', $template);
            exit;
        }
    }

    return;
}

if ('manage' == $mode) {
    fn_add_breadcrumb(__('w_c2c_products_manage'));
    $products = [];

    if ($auth['company_id']) {
        $params['extend'][] = 'companies';
        $params['company_id'] = $auth['company_id'];
        $params['approval_status'] = 'all';
        $params['c2c_vendors_products'] = true;

        list($products) = fn_get_products($params);
    }

    Registry::get('view')->assign('products', $products);

    $templating = container()->get('templating');
    if ($templating->exists('@App/frontend/views/profile/c2c/products.html.twig')) {
        $html = $templating->render('@App/frontend/views/profile/c2c/products.html.twig', [
            'products' => $products,
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }

} elseif ($mode == 'delete') {
    if (!empty($_REQUEST['product_id'])) {
        //force synchronous execution to avoid mistreatment via worker (fail on fn_check_company_id)
        container()->get('marketplace.async_dispatcher')->nextExecIsSynchronous();
        try {
            $result = fn_delete_product($_REQUEST['product_id']);
            if ($result) {
                fn_set_notification('N', __('notice'), __('text_product_has_been_deleted'));
            }
        } catch (Forbidden $e) {
            fn_set_notification('W', __('warning'), __('access_denied'));
        }
    }
    return array(CONTROLLER_STATUS_OK, "c2c_products.manage");
}
