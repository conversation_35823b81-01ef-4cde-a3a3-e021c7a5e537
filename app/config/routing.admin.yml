admin_oauth_authorize:
    path: /oauth/authorize
    defaults: { _controller: AppBundle:Backend/Auth:oauthAuthorize }
    methods: [GET]

admin_multi_vendor_product_search:
    path: /api/mvp/search
    defaults: { _controller: AppBundle:Backend/MultiVendorProduct:search }
    methods: [GET]

admin_attribute_search_brand:
    path: /api/attribute/search-brand
    defaults: { _controller: AppBundle:Backend/Attribute:searchBrand }
    methods: [GET]

admin_save_sync_rules:
    path: /api/sync-rules/save
    defaults: { _controller: AppBundle:Backend/MultiVendorProduct:saveSyncRules }
    methods: [POST]

admin_Csvfile_list:
    path: /csv/list
    defaults:  { _controller: AppBundle:Backend/Csvfile:list }

admin_Csvfile_get:
    path: /csv
    defaults:  { _controller: AppBundle:Backend/Csvfile:get }

admin_MultiVendorProduct_list:
    path: /multi-vendor-product/list
    defaults:  { _controller: AppBundle:Backend/MultiVendorProduct:list }

admin_MultiVendorProduct_syncAllFromProduct:
    path: /multi-vendor-product/sync-from-product
    defaults:  { _controller: AppBundle:Backend/MultiVendorProduct:syncAllFromProduct }

admin_MultiVendorProduct_removeLink:
    path: /multi-vendor-product/remove-link
    defaults:  { _controller: AppBundle:Backend/MultiVendorProduct:removeLink }

admin_MultiVendorProduct_delete:
    path: /multi-vendor-product/delete
    defaults:  { _controller: AppBundle:Backend/MultiVendorProduct:delete }

admin_MultiVendorProduct_batch_delete:
    path: /multi-vendor-product/batch-delete
    defaults:  { _controller: AppBundle:Backend/MultiVendorProduct:batchDelete }
    methods: [POST]

admin_MultiVendorProduct_form:
    path: /multi-vendor-product/form
    defaults:  { _controller: AppBundle:Backend/MultiVendorProduct:form }

admin_MultiVendorProduct_save:
    path: /multi-vendor-product/save
    defaults:  { _controller: AppBundle:Backend/MultiVendorProduct:save }

admin_MultiVendorProduct_productSyncFromCatalog:
    path: /multi-vendor-product/sync
    defaults:  { _controller: AppBundle:Backend/MultiVendorProduct:productSyncFromCatalog }

admin_MultiVendorProduct_approve_review:
    path: /multi-vendor-product/approve-review
    defaults:  { _controller: AppBundle:Backend/MultiVendorProduct:approveReview }

admin_MultiVendorProduct_decline_review:
    path: /multi-vendor-product/decline-review
    defaults:  { _controller: AppBundle:Backend/MultiVendorProduct:declineReview }

admin_Notification_list:
    path: /notification/list
    defaults:  { _controller: AppBundle:Backend/Notification:list }

admin_Notification_trigger:
    path: /notification/trigger
    defaults:  { _controller: AppBundle:Backend/Notification:trigger }

admin_notifications_manage:
    path: /notifications/manage
    defaults: { _controller: AppBundle:Backend/Notifications:manage }

admin_notifications_view:
    path: /notifications/view
    defaults: { _controller: AppBundle:Backend/Notifications:view }

admin_notifications_save:
    path: /notifications/save
    defaults: { _controller: AppBundle:Backend/Notifications:save }

admin_notifications_delete:
    path: /notifications/delete
    defaults: { _controller: AppBundle:Backend/Notifications:delete }

admin_Commission_manage:
    path: /commission
    defaults:  { _controller: AppBundle:Backend/Commission:manage }

admin_Commission_save:
    path: /commission/save
    defaults:  { _controller: AppBundle:Backend/Commission:save }

admin_Commission_by_category_delete:
    path: /commission/delete
    defaults: { _controller: AppBundle:Backend/Commission:delete }

admin_Discuss_list:
    path: /discuss
    defaults:  { _controller: AppBundle:Backend/Discuss:list }
    methods: [GET]

admin_Discuss_post:
    path: /discuss
    defaults:  { _controller: AppBundle:Backend/Discuss:post }
    methods: [POST]

admin_Discuss_view:
    path: /discuss/{discussionId}
    defaults:  { _controller: AppBundle:Backend/Discuss:view }

admin_Discuss_hide:
    path: /discuss/{discussionId}/hide
    defaults:  { _controller: AppBundle:Backend/Discuss:hide }

admin_api_tokens:
    path: /tokens
    defaults:  { _controller: AppBundle:Backend/ApiAccess:listTokens }

admin_organisation_admin_identity_card:
    path: /organisation/{userId}/identity-card
    defaults: { _controller: AppBundle:Backend\Organisation:identityCard }
    methods: [GET]

admin_organisation_admin_proof_of_appointment:
    path: /organisation/{userId}/proof-of-appointment
    defaults: { _controller: AppBundle:Backend\Organisation:proofOfAppointment }
    methods: [GET]

admin_organisation_list:
    path: /organisations
    defaults: { _controller: AppBundle:Backend\Organisation:list }
    methods: [GET]

admin_organisation:
    path: /organisations/{organisationId}
    defaults: { _controller: AppBundle:Backend\Organisation:organisation }
    methods: [GET]

admin_stripe_tos_acceptance:
    path: /stripe-tos-acceptance
    defaults: { _controller: marketplace.payment.stripe_controller:stripeTosAcceptanceAction }
    methods: [GET]

admin_stripe_handle_tos_acceptance:
    path: /stripe-handle-tos-acceptance
    defaults: { _controller:  marketplace.payment.stripe_controller:handleStripeTosAcceptanceAction }
    methods: [POST]

admin_stripe_update_tos_acceptance:
    path: /stripe-handle-tos-acceptance
    defaults: { _controller:  marketplace.payment.stripe_controller:handleStripeTosAcceptanceAction }
    methods: [PATCH]

admin_mail_variables_list:
    path: /mail/variables
    defaults:  { _controller: AppBundle:Backend\Mail:listVariables }

admin_sandbox_disk_usage:
    path: /sandbox-disk-usage
    defaults: { _controller: AppBundle:Backend\SandboxDiskUsage:get }
    methods: [GET]

financial_flows_history:
    path: /financial-flows-history/list
    defaults:  { _controller: AppBundle:Backend/FinancialFlowsHistory:list }

user_groups_list:
    path: /groups/list
    defaults: { _controller: AppBundle:Backend\UserGroup:list }

user_groups_update:
    path: /groups/update
    defaults: { _controller: AppBundle:Backend\UserGroup:update }
    methods: [POST]

user_groups_import:
    path: /groups/import
    defaults: { _controller: AppBundle:Backend\UserGroup:importUsers }
    methods: [POST]

user_groups_export:
    path: /groups/{groupId}/export
    defaults: { _controller: AppBundle:Backend\UserGroup:exportUsers }

admin_taxes_commision_tax_update:
    path: /international-tax/commission-tax
    defaults:  { _controller: AppBundle:Backend/Tax:updateCommissionTax }
    methods: [POST]

admin_taxes_shipping_update:
    path: /international-tax/shipping-tax
    defaults:  { _controller: AppBundle:Backend/Tax:updateShippingTax }
    methods: [POST]

admin_taxes_shipping_delete:
    path: /international-tax/shipping-tax/{countryCode}
    defaults:  { _controller: AppBundle:Backend/Tax:deleteShippingTax }
    methods: [DELETE]

admin_invoicing_settings:
    path: /invoicing-settings
    defaults:  { _controller: AppBundle:Backend/InvoicingSettings:index }

commission_monitoring:
    path: /financial-flows-history/commission-list
    defaults: { _controller: AppBundle:Backend/FinancialFlowsHistory:commissionList }

