[unix_http_server]
file=/tmp/supervisor.sock

[supervisord]
logfile=/app/var/supervisor/supervisord.log
logfile_maxbytes=10MB
logfile_backups=10
loglevel=info
minfds=1024
minprocs=200
childlogdir=/app/var/supervisor/

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:web]
command=/usr/sbin/php-fpm7.4-zts

; Traite les lignes d'import des catégories
[program:worker_csv_entities]
command=bin/console amqp:consume csv_entities --max-msg=100 --max-time=240
process_name=%(program_name)s_%(process_num)s
numprocs=1 ; ATTENTION on ne peut pas traiter plus d'une ligne à la fois
directory=/app
autorestart=true

; Traite les autres files
[program:worker_others]
command=bin/console amqp:consume csv_line product_price_import product_stock_import csv_exports emails videos products multi_vendor_products readmodel search_engine feature_products product_moderation product_price_import product_stock_import feature_available_offers user_group_import translations_import related_products_import translations_import --max-msg=1000 --max-time=240
process_name=%(program_name)s_%(process_num)s
numprocs=2
directory=/app
autorestart=true

[program:debouncer]
command=bin/debouncer.sh 30 ; delay in seconds
numprocs=1
directory=/app
autorestart=true

[program:filebeat]
command=/app/bin/filebeat -c /app/app/config/filebeat.yml
