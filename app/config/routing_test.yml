_main:
    resource: routing.yml

test_autologin:
    path: /autologin/{userId}
    defaults: { _controller: AppBundle:Test:autologin }

#----------------------------------------------------
# API System : utilisable uniquement en env de 'test'
#----------------------------------------------------
api_system_reload_data_for_sdk:
    path: /api/v1/system/reload-data-for-sdk/%route_system_token%
    defaults: { _controller: AppBundle:Api/System:reloadDataForSDK }
    methods: [POST]
api_test_delay_response:
    path: /api/v1/test/delay/{delay}
    defaults: { _controller: AppBundle:Api/Test:delay }
    methods: [GET]
wizaplace_debug_bundle:
    resource: '@WizaplaceDebugBundle/Resources/config/routing.yml'
