services:
    Wizacha\Marketplace\Payment\Processor\NoPayment:
        public: true
        autowire: true
        tags: ['marketplace.payment_processor_service']

    Wizacha\Marketplace\Payment\Processor\PaymentProcessorProvider:
        arguments:
            - !tagged marketplace.payment_processor_service

    Wizacha\Marketplace\Payment\Processor\ProcessorService:
        autowire: true
        arguments:
            $mangoPay: '@marketplace.payment.mangopay'
            $hiPay: '@marketplace.payment.hipay'
            $smoney: '@marketplace.payment.smoney'
            $lemonWay: '@marketplace.payment.lemonway'
            $stripe: '@marketplace.payment.stripe'

    Wizacha\AppBundle\Controller\CardController:
        public: true
        autowire: true

    Wizacha\Marketplace\Payment\Processor\PayoutService:
        public: true
        autowire: true
        autoconfigure: true
        arguments:
            $processors: !tagged marketplace.payment_processor_service

    Wizacha\Marketplace\Payment\Mangopay\MangoPayApiConfig:
        public: true
        arguments:
            - '%mangopay_api.client_id%'
            - '%mangopay_api.client_password%'
            - '%mangopay_api.base_url%'
            - '%mangopay_api.secure_mode%'
            - '%currency.code%'

    MangoPay\MangoPayApi:
        public: true
        calls:
            - method: setHttpClient
              arguments:
                  - '@marketplace.payment.mangopay_http_client'

    marketplace.payment.mangopay:
        alias: Wizacha\Marketplace\Payment\Processor\MangoPay
        public: true

    Wizacha\Marketplace\Payment\Processor\MangoPay:
        public: true
        autowire: true
        tags:
            - { name: kernel.event_subscriber }
            - { name: marketplace.payment_processor_service }
        arguments:
            $createLegalWalletsForCustomers: '%feature.create_legal_wallets_for_customers%'
            $userIdAgentEtablissementPaiement: '%mangopay_user_id_agent_etablissement_paiement%'
            $mangoPayLogger: '@marketplace.payment.mangopay_logger'
        calls:
            - method: setApi
              arguments:
                  - '@MangoPay\MangoPayApi'
            - method: setConfig


    Wizacha\Marketplace\Payment\LemonWay\LemonWayConfig:
        public: true
        arguments:
            - '%lemonway_api.use_buyer_wallet_for_moneyin%'
            - '%lemonway_api.tech_wallet_id%'
            - '%lemonway_api.marketplace_id%'
            - '%lemonway_api.marketplace_discount_wallet%'
            - '%lemonway_bankwire.iban%'
            - '%lemonway_bankwire.bic%'
            - '%lemonway_bankwire.holder_name%'
            - '%lemonway_bankwire.holder_address%'
            - '%currency.code%'

    marketplace.payment.lemonway:
        alias: Wizacha\Marketplace\Payment\Processor\LemonWay
        public: true

    Wizacha\Marketplace\Payment\Processor\LemonWay:
        public: true
        tags:
          - { name: marketplace.payment_processor_service }
          - { name: marketplace.payment_processor_service_deferment_payment }
        calls:
          - method: setUrlValidator
            arguments:
              - '@app.validator.url_validator'
        autowire: true


    Wizacha\Marketplace\Payment\LemonWay\LemonWayApi:
        public: true
        arguments:
            - '@Wizacha\Marketplace\Payment\LemonWay\LemonWayHttpClient'
            - '%lemonway_api.webkit_url%'

    Wizacha\Marketplace\Payment\LemonWay\LemonWayHttpClient:
        public: true
        arguments:
            - '@Wizacha\Marketplace\Payment\LemonWay\LemonWayLogger'
            - '@request_stack'
            - '%lemonway_api.directkit_url%'
            - '%lemonway_api.client_login%'
            - '%lemonway_api.client_password%'
            - '%lemonway_proxy.host%'
            - '%lemonway_proxy.port%'
            - '%lemonway_proxy.user%'
            - '%lemonway_proxy.password%'

    Wizacha\Marketplace\Payment\LemonWay\LemonWayTransactionSyncService:
        public: true
        autowire: true

    Wizacha\Marketplace\Payment\SMoney\SMoneyApi:
        public: true
        arguments:
            - '%smoney.api_base_url%'
            - '%smoney.token%'
            - '@logger'
            - '@event_dispatcher'

    marketplace.payment.smoney:
        alias: Wizacha\Marketplace\Payment\Processor\SMoney
        public: true

    Wizacha\Marketplace\Payment\Processor\SMoney:
        public: true
        autowire: true
        tags:
            - { name: marketplace.payment_processor_service }
        arguments:
            $payoutWebsiteName: '%smoney.website_name%'
        calls:
            - method: setApi
              arguments:
                  - '@Wizacha\Marketplace\Payment\SMoney\SMoneyApi'
            - method: setTransactionService

    marketplace.payment.stripe:
        alias: Wizacha\Marketplace\Payment\Processor\Stripe
        public: true

    Wizacha\Marketplace\Payment\Processor\Stripe:
        public: true
        autowire: true
        tags:
            - { name: kernel.event_subscriber }
            - { name: marketplace.payment_processor_service }
            - { name: marketplace.payment_processor_service_deferment_payment }
        arguments:
            $uuidGenerator: '@broadway.uuid.generator'
        calls:
            - method: setTransactionService
            - method: setApi
              arguments:
                  - '@Wizacha\Marketplace\Payment\Stripe\StripeApi'

    Wizacha\Marketplace\Payment\Stripe\StripeConfig:
        public: true
        arguments:
            - '%stripe.secret_token%'
            - '%currency.code%'
            - '%marketplace.project_name%'

    Wizacha\AppBundle\Controller\Backend\StripeController: ~

    Wizacha\Marketplace\Payment\Stripe\StripeApi:
        public: true
        autowire: true
        autoconfigure: true

    HiPay\Fullservice\HTTP\Configuration\Configuration:
        public: true
        arguments:
            - apiUsername: '%hipay.cashin.api_username%'
              apiPassword: '%hipay.cashin.api_password%'
        calls:
            - method: setApiEnv
              arguments:
                  - '%hipay.cashin.env%'

    marketplace.payment.hipay_client:
        class: Wizacha\Marketplace\Payment\HiPay\HiPayHTTPClient
        public: true
        arguments:
            - '@HiPay\Fullservice\HTTP\Configuration\Configuration'
        calls:
            - method: setHiPayLogger
              arguments:
                  - '@marketplace.payment.hipay_logger'

    marketplace.payment.hipay_api:
        class: HiPay\Fullservice\Gateway\Client\GatewayClient
        public: true
        arguments:
            - '@marketplace.payment.hipay_client'

    marketplace.payment.hipay_cashout_client:
        class: GuzzleHttp\Client
        public: true
        arguments:
            - base_uri: '%hipay.cashout.api_uri%'
              auth: ['%hipay.cashout.api_login%', '%hipay.cashout.api_password%']

    Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayWalletApi:
        public: true
        arguments:
            - '@marketplace.payment.hipay_cashout_client'
            - '%hipay.cashout.entity%'
            - '%hipay.cashout.merchant_group_id%'
            - '%currency.code%'
            - '@marketplace.payment.hipay_wallet_logger'

    Wizacha\Marketplace\Payment\Processor\HiPay:
        public: true
        autowire: true
        tags:
            - { name: marketplace.payment_processor_service }
            - { name: marketplace.payment_processor_service_deferment_payment }
        arguments:
            $projectName: '%marketplace.project_name%'
            $commissionRecipientId: '%hipay.commission_recipient_id%'
            $cardPaymentType: '%hipay.payment_product_list%'
        calls:
            -   method: setApi
                arguments:
                    - '@marketplace.payment.hipay_api'
            -   method: setWalletApi
            -   method: setTransactionService
            -   method: setFeatureSubscription
                arguments:
                    - '%feature.subscription%'

    marketplace.payment.hipay_event_subscriber:
        class: Wizacha\Marketplace\Payment\HiPay\HiPayEventSubscriber
        public: true
        autowire: true
        tags:
            - { name: kernel.event_subscriber, priority: 10}

    Wizacha\Marketplace\Payment\PaymentService:
        arguments:
            - '@database_connection'
            - '@service_container'
            - '@logger'
            - '@Wizacha\Marketplace\Payment\PaymentRepository'

    marketplace.payment.processor.mangopay:
        class: Wizacha\Marketplace\Payment\Mangopay\MangopayCardProcessor
        arguments: ['@marketplace.payment.mangopay']
        public: true

    marketplace.payment.processor.mangopay_bankwire:
        class: Wizacha\Marketplace\Payment\Mangopay\MangopayBankwireProcessor
        public: true
        autowire: true

    marketplace.payment.processor.lemonway_card:
        class: Wizacha\Marketplace\Payment\LemonWay\LemonWayCardProcessor
        public: true
        arguments: ['@marketplace.payment.lemonway']

    marketplace.payment.processor.lemonway_transfer:
        class: Wizacha\Marketplace\Payment\LemonWay\LemonWayTransferProcessor
        public: true
        arguments:
            - '@marketplace.payment.lemonway'
            - '@templating'
            - '@event_dispatcher'

    Wizacha\Marketplace\Payment\LemonWay\AbstractLemonWayPaymentMethod :
        parent : Wizacha\Marketplace\Payment\AbstractPaymentMethod
        abstract : true
        autowire : true
        calls :
            - [ setProcessor, [ '@marketplace.payment.lemonway' ] ]

    Wizacha\Marketplace\Payment\LemonWay\LemonWaySepaDefermentProcessor:
        parent : Wizacha\Marketplace\Payment\LemonWay\AbstractLemonWayPaymentMethod
        public : true

    Wizacha\Marketplace\Payment\LemonWay\LemonWaySepaDirectProcessor:
        parent : Wizacha\Marketplace\Payment\LemonWay\AbstractLemonWayPaymentMethod
        public : true

    marketplace.payment.processor.hipay_card:
        class: Wizacha\Marketplace\Payment\HiPay\HiPayCardProcessor
        public: true
        arguments: ['@marketplace.payment.hipay']
    marketplace.payment.processor.hipay_card_capture:
        class: Wizacha\Marketplace\Payment\HiPay\HiPayCardCaptureProcessor
        public: true
        arguments: ['@marketplace.payment.hipay']
    Wizacha\Marketplace\Payment\AbstractPaymentMethod:
        abstract: true
        autowire: true
    Wizacha\Marketplace\Payment\HiPay\AbstractHipayPaymentMethod:
        parent: Wizacha\Marketplace\Payment\AbstractPaymentMethod
        abstract: true
        autowire: true
        calls:
            - [setProcessor, ['@marketplace.payment.hipay']]
    marketplace.payment.processor.hipay_sepa_deferment:
        parent: Wizacha\Marketplace\Payment\HiPay\AbstractHipayPaymentMethod
        class: Wizacha\Marketplace\Payment\HiPay\HipaySepaDefermentProcessor
        public: true
    marketplace.payment.processor.hipay_sepa_direct:
        parent: Wizacha\Marketplace\Payment\HiPay\AbstractHipayPaymentMethod
        class: Wizacha\Marketplace\Payment\HiPay\HipaySepaDirectProcessor
        public: true

    marketplace.payment.processor.smoney_card:
        class: Wizacha\Marketplace\Payment\SMoney\SMoneyCardProcessor
        public: true
        arguments: ['@marketplace.payment.smoney']
    marketplace.payment.processor.stripe_card:
        class: Wizacha\Marketplace\Payment\Stripe\StripeCardProcessor
        public: true
        autowire: true
        arguments:
            $publicTokenStripe: '%stripe.public_token%'
    marketplace.payment.processor.stripe_sepa_deferment:
        class: Wizacha\Marketplace\Payment\Stripe\StripeSepaDefermentProcessor
        public: true
        autowire: true
        arguments:
            $publicTokenStripe: '%stripe.public_token%'
            $uuidGenerator: '@broadway.uuid.generator'
    marketplace.payment.processor.stripe_sepa_direct:
        class: Wizacha\Marketplace\Payment\Stripe\StripeSepaDirectProcessor
        public: true
        autowire: true
        arguments:
            $publicTokenStripe: '%stripe.public_token%'
            $uuidGenerator: '@broadway.uuid.generator'
    marketplace.payment.processor.nopayment:
        class: Wizacha\Marketplace\Payment\NoPayment\NoPaymentProcessor
        public: true
        autowire: true
    marketplace.payment.processor.manualpayment:
        class: Wizacha\Marketplace\Payment\NoPayment\ManualPaymentProcessor
        public: true
        parent: 'marketplace.payment.processor.nopayment'

    marketplace.payment.lemonway_event_subscriber:
        class: Wizacha\Marketplace\Payment\LemonWay\LemonWayEventSubscriber
        public: true
        autowire: true
        tags:
            - { name: kernel.event_subscriber, priority: 10}

    marketplace.payment.mangopay_event_subscriber:
        class: Wizacha\Marketplace\Payment\Mangopay\MangopayEventSubscriber
        public: true
        autowire: true
        tags:
            - { name: kernel.event_subscriber, priority: 10 }
        arguments:
            $processor: '@marketplace.payment.mangopay'

    marketplace.payment.smoney_event_subscriber:
        class: Wizacha\Marketplace\Payment\SMoney\SMoneyEventSubscriber
        public: true
        autowire: true
        tags:
            - { name: kernel.event_subscriber, priority: 10 }
        arguments:
            $processor: '@Wizacha\Marketplace\Payment\Processor\SMoney'

    marketplace.payment.stripe_event_subscriber:
        class: Wizacha\Marketplace\Payment\Stripe\StripeEventSubscriber
        public: true
        autowire: true
        tags:
            - { name: kernel.event_subscriber, priority: 10 }

    marketplace.payment.offline_event_subscriber:
        class: Wizacha\Marketplace\Payment\NoPayment\OfflinePaymentEventSubscriber
        public: true
        tags:
            - { name: kernel.event_subscriber, priority: 0}
        arguments:
            - '@marketplace.transaction.transaction_service'
            - '@marketplace.payment.lemonway_event_subscriber'

    Wizacha\Marketplace\Payment\MandateService:
        public: true
        arguments:
            - '@marketplace.payment.repository.user_payment_info'
            - '@marketplace.payment.repository.user_mandate'

    Wizacha\Marketplace\Payment\Repository\UserMandateRepository:
        arguments: ['@doctrine', Wizacha\Marketplace\Payment\UserMandate]

    Wizacha\Marketplace\Payment\Repository\UserPaymentInfoRepository:
        arguments: [ '@doctrine', Wizacha\Marketplace\Payment\UserPaymentInfo]

    marketplace.transction.transfers.failed:
        class: Wizacha\Marketplace\Transaction\TransactionTransferEventSubscriber
        public: true
        autowire: true
        autoconfigure: true
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\AppBundle\Controller\PaymentNotificationController:
        public: true
        autowire: true
        arguments:
            $hipayChargebackTestEnv: '%feature.hipay_chargeback_test_env%'

    Wizacha\AppBundle\Controller\PaymentNotification\HipaySepaNotificationController:
        public: true
        autowire: true

    Wizacha\AppBundle\Controller\PaymentNotification\LemonWaySepaNotificationController:
        public : true
        autowire : true

    Wizacha\AppBundle\Controller\PaymentNotification\SepaMandateSignatureNotificationController :
        public : true
        autowire : true

    marketplace.order.billing_number_generator:
        class: Wizacha\Marketplace\Order\OrderBillingNumberGenerator
        public: true
        arguments:
            - '@marketplace.order.order_service'
            - '%feature.activate_billing_number_auto_generation%'

    marketplace.order.rma_number_generator:
        class: Wizacha\Marketplace\Order\OrderRmaNumberGenerator
        public: true
        arguments:
            - '@marketplace.order.order_service'
            - '@marketplace.order_return.service'
            - '%feature.activate_billing_number_auto_generation%'

    marketplace.order.credit_note_reference_generator:
        class: Wizacha\Marketplace\Order\CreditNote\CreditNoteReferenceGenerator
        public: true
        arguments:
            - '@marketplace.order.refund.refund_service'
            - '%feature.activate_billing_number_auto_generation%'

    Wizacha\AppBundle\Controller\Api\ShipmentsController:
        public: true
        autowire: true
        arguments:
            - '@marketplace.order.order_service'
            - '@marketplace.order.action.mark_as_delivered'
            - '@marketplace.order.shipment_service'

    marketplace.order.shipment_service:
        class: Wizacha\Marketplace\Order\ShipmentService
        arguments:
            - '@database_connection'
            - '@marketplace.order.order_service'

    Wizacha\Marketplace\Order\OrderService:
        public: true
        arguments:
            - '@database_connection'
            - '@marketplace.payment.payment_service'
            - '@marketplace.order.workflow_service'
            - '@marketplace.user.user_service'
            - '@Wizacha\Marketplace\Company\CompanyService'
            - '@Wizacha\Marketplace\Shipping\ShippingService'
            - '@logger'
            - '@marketplace.order.action.cancel'
            - '@Wizacha\Marketplace\Order\Discount\OrderDiscountsCalculator'
            - '@Wizacha\Marketplace\Order\Repository\OrderRepository'
            - '@order_amount_repository'
            - '@marketplace.international_tax.shipping'

    Wizacha\Marketplace\Order\OrderDetailsService:
        autowire: true

    Wizacha\Marketplace\Order\Workflow\WorkflowService:
        public: true
        arguments:
            - '@database_connection'
            - '@Wizacha\Marketplace\Company\CompanyService'

    Wizacha\Marketplace\Order\Tracer\Tracer:
        arguments:
            - '@database_connection'
            - '@marketplace.user_service'
            - '@marketplace.user.user_service'

    Wizacha\Marketplace\Order\Action\:
        resource: '../../src/Marketplace/Order/Action/*'
        exclude: '../../src/Marketplace/Order/Action/{ActionName.php,Action.php}'
        public: true
        autoconfigure: true
        autowire: true

    Wizacha\Marketplace\Order\Action\Confirm:
        public: true
        autoconfigure: true
        autowire: true
        calls:
            - [ 'setStockService', [ '@marketplace.stock.domain_service' ] ]

    Wizacha\Marketplace\Order\Action\MarkAsDelivered:
        public: true
        autoconfigure: true
        autowire: true
        calls:
            - [ 'setShipmentService', [ '@marketplace.order.shipment_service' ] ]

    Wizacha\Marketplace\ReadModel\ProductRepository:
        public: true
        arguments:
            - '@database_connection'
            - '@Wizacha\Marketplace\ReadModel\Denormalizer\ReadModelDenormalizerRegistry'
            - '@Wizacha\Marketplace\Metrics\SandboxMetrics'

    Wizacha\Marketplace\ReadModel\ProductProjector:
        public: true
        arguments:
            - '@marketplace.product.repository.read_model'
            - '@marketplace.promotion.promotionservice'
            - '@marketplace.pim.video_service'
            - '@event_dispatcher'
            - '@router'
            - '@marketplace.catalog.category_service'
            - '@marketplace.catalog.company_service'
            - '@marketplace.async_dispatcher'
            - '@marketplace.multi_vendor_product.service'
            - '@marketplace.pim.product.service'
            - '@image.manager'
            - '@marketplace.search.product_index'
            - '@marketplace.review.product.service'
            - '%feature.multi_vendor_product%'
            - '@marketplace.price_tier.price_tier_service'
            - '@marketplace.product.declination_factory'
            - '@marketplace.related_product.related_product_service'

    Wizacha\Marketplace\Catalog\Product\ProductService:
        public: true
        arguments:
            - '@marketplace.product.repository.read_model'
            - '@database_connection'
            - '@event_dispatcher'
            - '%feature.catalog.show_out_of_stock_products%'
            - '%feature.catalog.show_zero_price_products%'
            - '%http_host%'
            - '@html_service'
            - '@marketplace.pim.product.service'
            - '@Wizacha\Storage\ImagesStorageService'
            - '%feature.quote_requests%'

    marketplace.pim.product_creator:
        class: Wizacha\Marketplace\PIM\Product\ProductCreator
        public: true
        arguments: ['@image.manager', '@marketplace.product.product_file_service']

    marketplace.product.product_file_service:
        class: Wizacha\Marketplace\PIM\ProductFileService
        public: true
        arguments:
            - '@database_connection'
            - '%cscart_table_prefix%'
            - '@Wizacha\Storage\DownloadsStorageService'

    marketplace.product.declination_factory:
        class: Wizacha\Marketplace\Entities\DeclinationFactory
        public: true

    marketplace.promotion.catalog_applier:
        alias: Wizacha\Marketplace\Promotion\ProductPromotionApplier
        public: true

    Wizacha\Marketplace\Promotion\ProductPromotionApplier:
        public: true

    marketplace.promotion.basket_applier:
        class: Wizacha\Marketplace\Promotion\BasketPromotionApplier
        public: true

    Wizacha\AppBundle\Controller\Api\ProductController:
        autoconfigure: true
        autowire: true
        arguments:
            - '@database_connection'
            - '%cscart_table_prefix%'
            - '@marketplace.division.service'
            - '@marketplace.division.products.service'
            - '@marketplace.pim.product.service'
            - '@marketplace.stock.domain_service'
            - '@event_dispatcher'
            - '@marketplace.divisions_settings.service'
            - '@logger'
        calls:
            - [ setContainer,[ '@service_container' ] ]

    Wizacha\Marketplace\Promotion\PromotionService:
        public: true
        arguments:
            - '@marketplace.promotion.repository'
            - '@marketplace.promotion.promotion_usage.repository'
            - '@broadway.uuid.generator'
            - '@marketplace.promotion.catalog_applier'
            - '@marketplace.promotion.basket_applier'
            - '@event_dispatcher'
            - '@marketplace.promotion.rule_engine'
            - '@marketplace.promotion.price_calendar_factory'
            - '@marketplace.promotion.serializer'
            - '@logger'
            - '@marketplace.marketplace_promotion.repository'
            - '@Wizacha\AppBundle\Controller\Api\Promotion\BasketPromotionNormalizer'
            - '@marketplace.user_group.service'

    Wizacha\Marketplace\Order\OrderReturn\OrderReturnService:
        arguments: [ '@database_connection', '@marketplace.order.order_service' ]

    marketplace.order.after_sales_service:
        class: Wizacha\Marketplace\Order\AfterSales\AfterSalesService
        public: true
        arguments: [ '@marketplace.user.user_service', '@event_dispatcher']

    marketplace.promotion.price_calendar_factory:
        class: Wizacha\Marketplace\Promotion\PriceCalendarFactory
        public: true
        arguments: [ '@marketplace.promotion.catalog_applier' ]

    marketplace.promotion.repository:
        class: Wizacha\Marketplace\Promotion\PromotionRepository
        public: true
        arguments:
            - '@doctrine.orm.entity_manager'

    marketplace.marketplace_promotion.repository:
        class: Wizacha\Marketplace\Promotion\MarketplacePromotionRepository
        public: true
        arguments:
            - '@doctrine.orm.entity_manager'

    marketplace.promotion.promotion_subscriber:
        class: Wizacha\Marketplace\Promotion\PromotionEventSubscriber
        public: true
        autowire: true
        autoconfigure: true
        tags:
            - { name: kernel.event_subscriber}

    marketplace.promotion.serializer:
        class: Wizacha\Marketplace\Promotion\PromotionSerializer
        public: true

    marketplace.promotion.rule_engine:
        class: Wizacha\Marketplace\Promotion\Rule\RuleEngine
        public: true
        arguments: [ '@rulerz', '@RulerZ\Compiler\Compiler', '@RulerZ\Target\Native\Native', '@doctrine.dbal.default_connection' ]

    marketplace.promotion.api.promotioncontroller:
        public: true
        class: Wizacha\AppBundle\Controller\Api\LegacyPromotionController
        arguments: ['@marketplace.promotion.serializer', '@marketplace.promotion.promotionservice']
        calls:
            - [ setContainer,[ '@service_container' ] ]

    Wizacha\Marketplace\Promotion\PromotionUsage\PromotionUsageRepository:
        arguments:
            - '@doctrine.orm.entity_manager'

    marketplace.basket.repository.aggregate:
        class: Broadway\EventSourcing\EventSourcingRepository
        public: true
        arguments:
            - '@broadway.event_store'
            - '@broadway.event_handling.event_bus'
            - Wizacha\Marketplace\Basket\Basket
            - '@broadway.aggregate_factory'
            - ['@broadway.metadata_enriching_event_stream_decorator']

    Wizacha\Bridge\Broadway\DatabaseRepository:
        public: true
        arguments:
            - '%marketplace.read_model.indexes.basket_short%'
            - Wizacha\Marketplace\ReadModel\Basket
    Broadway\ReadModel\RepositoryInterface: '@Wizacha\Bridge\Broadway\DatabaseRepository'

    marketplace.basket.projector:
        class: Wizacha\Marketplace\Basket\BasketProjector
        public: true
        tags:
            - { name: broadway.domain.event_listener }
        arguments:
            - '@marketplace.basket.repository.read_model'
            - '@logger'
            - '@Wizacha\Marketplace\Subscription\SubscriptionRepository'
            - '@Wizacha\Marketplace\Subscription\SubscriptionService'

    marketplace.listeners.user:
        class: Wizacha\Marketplace\User\EventListener
        public: true
        tags:
            - { name: broadway.domain.event_listener }
        arguments:
            - '@marketplace.user.domain_service'

    marketplace.basket.command_handler:
        class: Wizacha\Marketplace\Basket\BasketCommandHandler
        public: true
        tags:
            - { name: command_handler }
        arguments:
            - '@marketplace.basket.repository.aggregate'
            - '@Wizacha\Core\Concurrent\MutexService'

    Wizacha\Marketplace\Basket\BasketService:
        public: true
        arguments:
            - '@broadway.command_handling.command_bus'
            - '@broadway.uuid.generator'
            - '@marketplace.basket.repository.aggregate'
            - '@marketplace.basket.repository.read_model'
            - '@marketplace.stock.domain_service'
            - '@event_dispatcher'
            - '@marketplace.promotion.promotionservice'
            - '@marketplace.product.repository.read_model'
            - '@logger'
            - '@marketplace.user_group.service'

    Broadway\CommandHandling\CommandBusInterface:
        class: Broadway\CommandHandling\SimpleCommandBus

    broadway.command_handling.command_bus:
        alias: Broadway\CommandHandling\CommandBusInterface
        public: true

    Wizacha\AppBundle\Controller\CheckoutController:
        public: true
        autowire: true

    Wizacha\Marketplace\Basket\Checkout:
        public: true
        arguments:
            - '@marketplace.promotion.promotionservice'
            - '@marketplace.payment.payment_service'
            - '@marketplace.order.order_service'
            - '@marketplace.user.user_service'
            - '@marketplace.payment.mandate_service'
            - '@marketplace.order.action.trash'
            - '@marketplace.order.action.confirm'
            - '@event_dispatcher'
            - '@Wizacha\Marketplace\Subscription\SubscriptionService'
            - '@marketplace.order.action.commit_to'
            - '%feature.payment_deadline%'
            - '@marketplace.order.action.redirect_to_payment_processor'
            - '@Wizacha\Sentinel\AlertManager'
            - '@marketplace.order.action.cancel'
        calls:
            - [setUserPaymentInfoService, ['@marketplace.payment.user_payment_info_service']]

    marketplace.user.domain_service:
        class: Wizacha\Marketplace\DomainService\UserDomainService
        public: true
        calls:
            - [ setContainer, [ '@service_container' ] ]

    Wizacha\Marketplace\PIM\Stock\StockService:
        public: true
        arguments:
            - '@doctrine.dbal.default_connection'
            - '@app.redis.default'
            - '%redis.default.prefix%'
            - '%marketplace.stock.cart_time_limit%'

    marketplace.cms.menu_service:
        class: Wizacha\Marketplace\Cms\MenuService
        public: true
        arguments:
            - '@database_connection'

    marketplace.cms.page_service:
        class: Wizacha\Marketplace\Cms\PageService
        public: true
        autowire: true
        arguments:
            $hideLegacyPages: '%feature.disable_front_office%'

    marketplace.queue_manager_factory:
        class: Wizacha\Async\BbqFactory
        public: true
        arguments:
            - '%marketplace.queue.config%'
            - '%queue.type%'
            - '%queue.path%'
            - '@marketplace.aws.sqs.client'
            - '@marketplace.amqp.connection'

    marketplace.queue_manager:
        class: Eventio\BBQ
        public: true
        factory: ['@marketplace.queue_manager_factory', createBbq]

    Wizacha\Async\Dispatcher:
        public: true
        arguments:
            - '%marketplace.function_to_dispatch%'
            - '@marketplace.queue_manager'
            - '@logger'
            - '@marketplace.debouncer.service'
            - '%queue.type%'
        lazy: true

    marketplace.async_progress:
        class: Wizacha\Async\Progress
        public: true
        arguments:
            - '@wizacha.registry'

    worker.queue_list:
        class: Array
        factory: ["@marketplace.queue_manager", getQueues]
        public: true
        #scope: prototype

    worker.job_provider:
        class: Wizacha\Async\JobProvider
        public: true
        arguments: ['%marketplace.queue.config%', '@worker.queue_list']

    marketplace.amqp.connection:
        class: PhpAmqpLib\Connection\AMQPLazyConnection
        public: true
        arguments:
            - '%amqp.host%'
            - '%amqp.port%'
            - '%amqp.user%'
            - '%amqp.pass%'
            - '%amqp.vhost%'

    Wizacha\Search\Engine\Algolia:
        arguments:
            - '@wizacha.registry'
            - '%algolia.api_index_prefix%'
    marketplace.search_engine:
        alias: 'Wizacha\Search\Engine\Algolia'
        public: true

    Wizacha\Search\Index\AbstractIndex:
        abstract: true
        arguments:
            - '@marketplace.product.productservice'
            - '@marketplace.search_engine'
            - '@wizacha.registry'
            - '@marketplace.pim.attribute_service'
            - '@marketplace.search.product_record_factory'
            - '@marketplace.pim.category_service'
            - '%feature.enable_company_type_facet%'
            - '%feature.enable_companies_facet%'
            - '@marketplace.pim.option_service'

    marketplace.search.product_index:
        public: true
        class: 'Wizacha\Search\Product\AlgoliaProductIndex'
        parent: Wizacha\Search\Index\AbstractIndex

    Wizacha\Search\Record\AlgoliaProductRecordFactory:
        public: true
        arguments:
            - '@marketplace.multi_vendor_product.service'
            - '%feature.enable_company_type_facet%'
            - '%config.algolia_fpu_max_fp%'
            - '%feature.catalog.show_out_of_stock_products%'

    marketplace.search.product_record_factory:
        alias: Wizacha\Search\Record\AlgoliaProductRecordFactory
        public: true

    marketplace.search.category_index:
        class: Wizacha\Search\Index\CategoryIndex
        public: true
        arguments: ['@marketplace.search_engine', '@marketplace.async_dispatcher', '@wizacha.registry']

    marketplace.pim.video_repository:
        class: Wizacha\Marketplace\PIM\Video\VideoRepository
        public: true
        arguments:
            - '@doctrine.orm.entity_manager'

    #-------------------
    # Product (Doctrine)
    #-------------------
    Wizacha\Marketplace\PIM\Product\ProductService:
        public: true
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@doctrine.dbal.default_connection'
            - '@marketplace.pim.attribute_service'
            - '@marketplace.multi_vendor_product_link.service'
            - '@event_dispatcher'
            - '@broadway.uuid.generator'
            - '@Wizacha\Marketplace\PIM\Product\ProductRepository'
            - '@Wizacha\Marketplace\PIM\Option\OptionRepository'
            - '@Wizacha\Storage\ProductAttachmentsStorageService'

    #--------------------------
    # Multi vendor products API
    #--------------------------
    Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService:
        public: true
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@marketplace.multi_vendor_product_link.service'
            - '@marketplace.pim.product.service'
            - '@broadway.uuid.generator'
            - '@marketplace.seo.seo_service'
            - '@event_dispatcher'
            - '@image.manager'
            - '@marketplace.pim.category_service'
            - '@marketplace.pim.product_creator'
            - '@marketplace.pim.attribute_service'
            - '@marketplace.async_dispatcher'
            - '@marketplace.pim.video_service'
            - '@Wizacha\Core\Concurrent\MutexService'

    marketplace.multi_vendor_product_link.service:
        public: true
        class: Wizacha\Marketplace\PIM\MultiVendorProduct\LinkService
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@event_dispatcher'
            - '@Wizacha\Core\Concurrent\MutexService'

    Wizacha\Marketplace\PIM\MultiVendorProduct\ProductSynchronization\RulesService:
        autowire: true

    Wizacha\AppBundle\Controller\Api\MultiVendorProductController:
        arguments:
            - '@marketplace.multi_vendor_product.service'
            - '@app.validator.url_validator'
            - '@app.validator.video_validator'
            - '@marketplace.pim.video_service'
        calls:
            - [ setContainer, [ '@service_container' ] ]
        public: true

    marketplace.multi_vendor_product.product_synchronization.event_subscriber:
        class: Wizacha\Marketplace\PIM\MultiVendorProduct\ProductSynchronization\EventSubscriber
        public: true
        autowire: true
        calls:
            - [setIsActivated, ['%feature.pim_sync_product_from_mvp%']]
            - [setLogger, ['@logger']]
        tags:
            - { name: kernel.event_subscriber }

    #---------------
    # Moderation API
    #---------------
    Wizacha\AppBundle\Controller\Api\ModerationController:
        arguments:
            - '@marketplace.pim.product.service'
            - '@marketplace.moderation.moderation_service'

    #--------------
    # Attribute API
    #--------------
    Wizacha\AppBundle\Controller\Api\AttributeController:
        arguments:
            - '@marketplace.pim.attribute_service'

    #------------
    # Reviews API
    #------------
    marketplace.review.company.service:
        class: Wizacha\Marketplace\Review\CompanyReviewService
        public: true
        arguments:
            - '@marketplace.user.user_service'
            - '@marketplace.catalog.company_service'
            - '@marketplace.order.order_service'
            - '@database_connection'

    marketplace.review.api.controller:
        class: Wizacha\AppBundle\Controller\Api\ReviewController
        public: true
        arguments:
            - '@marketplace.review.company.service'
        calls:
            - [ setContainer, [ '@service_container' ] ]

    marketplace.review.product.service:
        class: Wizacha\Marketplace\Review\ProductReviewService
        public: true
        arguments:
            - '@database_connection'
        calls:
            - method: setLogger
              arguments:
                  - '@logger'

    marketplace.azure.client:
        factory: ['MicrosoftAzure\Storage\Blob\BlobRestProxy', createBlobService]
        class: MicrosoftAzure\Storage\Blob\BlobRestProxy
        public: true
        lazy: true
        arguments: ['DefaultEndpointsProtocol=%azure.protocol%;AccountName=%azure.account_name%;AccountKey=%azure.account_key%;%azure.local_endpoints%']
    marketplace.azure.sas_helper:
        class: MicrosoftAzure\Storage\Blob\BlobSharedAccessSignatureHelper
        public: true
        arguments: ['%azure.account_name%', '%azure.account_key%']

    #----
    # AWS
    #----
    marketplace.aws.client:
        class: Aws\Sdk
        public: true
        arguments:
            -
                region: '%aws.s3.credentials.region%'
                version: latest
                credentials:
                    key: '%aws.s3.credentials.key%'
                    secret: '%aws.s3.credentials.secret%'

    marketplace.aws.sqs.client:
        class: Aws\Sqs\SqsClient
        public: true
        arguments:
            -
                region: '%aws.sqs.credentials.region%'
                version: latest
                credentials:
                    key: '%aws.sqs.credentials.key%'
                    secret: '%aws.sqs.credentials.secret%'

    marketplace.ovh.s3.client:
        class: Aws\S3\S3Client
        public: true
        arguments:
            -
                region: '%ovh.s3.region%'
                version: latest
                endpoint: '%ovh.s3.api_url%'
                use_path_style_endpoint: true
                credentials:
                    key: '%ovh.s3.credentials.key%'
                    secret: '%ovh.s3.credentials.secret%'

    marketplace.aws.transcoding_service:
        class: Aws\Sdk
        arguments:
            -
                region: '%aws.transcoding.region%'
                version: latest
                credentials:
                    key: '%aws.s3.credentials.key%'
                    secret: '%aws.s3.credentials.secret%'

    marketplace.aws.transcoding_bucket:
        class: Aws\Sdk
        arguments:
            -
                region: '%aws.s3.credentials.region%'
                version: latest
                credentials:
                    key: '%aws.s3.credentials.key%'
                    secret: '%aws.s3.credentials.secret%'

    marketplace.pim.video_service:
        class: Wizacha\Marketplace\PIM\Video\VideoService
        public: true
        arguments:
            - '@broadway.uuid.generator'
            - '@marketplace.pim.video_repository'
            - '@marketplace.async_dispatcher'
            - '@marketplace.pim.video_storage'

    marketplace.pim.video_storage:
        class: Wizacha\Marketplace\PIM\Video\VideoStorage
        public: true
        arguments:
            - '@marketplace.aws.transcoding_service'
            - '@marketplace.aws.transcoding_bucket'
            - '@marketplace.aws.client'
            - '%aws.bucket%'
            - '%aws.video.temporary_bucket%'
            - '%aws.transcoding.region%'
            - '%aws.video.maxsize%'
            - '%aws.video.pipeline%'
            - '%aws.video.preset%'
            - '%aws.video.prefix%'
            - '%aws.video.maxduration%'
            - '@app.validator.url_validator'

    marketplace.pim.category_service:
        class: Wizacha\Marketplace\PIM\Category\CategoryService
        public: true
        arguments: ['@doctrine.orm.entity_manager', '@database_connection', '@marketplace.product.productservice']

    Wizacha\Marketplace\User\AddressBookService:
        arguments:
            - '@marketplace.user.address.book_repository'
            - '@marketplace.user.user_service'
            - '@marketplace.user.user_repository'
            - '@event_dispatcher'

    marketplace.user.address_book_event_listenner:
        class: Wizacha\Marketplace\User\AddressBookEventListener
        arguments:
            - '@broadway.uuid.generator'
        tags:
            - { name: doctrine.event_listener, event: prePersist }

    Wizacha\AppBundle\Controller\Api\UserController:
        public: true
        arguments:
            - '@event_dispatcher'
            - '@marketplace.user.user_service'
            - '@marketplace.user.api_user_serializer'
            - '@marketplace.basket.domain_service'
            - '@validator'
            - '@marketplace.user.address_service'
            - '@Wizacha\Marketplace\Country\CountryService'
            - '@Wizacha\AppBundle\Service\WhitelistDomainsService'
            - '@Wizacha\Marketplace\Company\CompanyService'
            - '@marketplace.cscart_password_encoder'
            - '%feature.notify_user_update%'
        calls:
            - [ setContainer,[ '@service_container' ] ]

    Wizacha\Marketplace\User\UserRepository:
        public: true
        arguments: ['@doctrine.orm.entity_manager']

    marketplace.user.address.book_repository:
        class: Wizacha\Marketplace\User\AddressBookRepository
        public: true
        arguments:
            - '@doctrine'
            - Wizacha\Marketplace\User\AddressBook

    Wizacha\Marketplace\User\UserService:
        public: true
        arguments:
            - '@marketplace.user.user_repository'
            - '@event_dispatcher'
            - '@Wizacha\Marketplace\Currency\CurrencyService'
            - '@Wizacha\Marketplace\Subscription\SubscriptionRepository'
            - '@Wizacha\Cscart\FnFunctions'
            - '@Wizacha\Marketplace\Country\CountryService'
            - '@app.feature_flag_service'
            - '@marketplace.ekey.repository'
            - '@Wizacha\Component\BytesGenerator\RandomBytesGenerator'
            - '%password_renewal_time_limit%'

    Wizacha\Marketplace\User\UserSecurity:
        public: true
        arguments:
            - '@marketplace.user.user_repository'
            - '@event_dispatcher'
            - '%feature.security.lock_user_accounts%'
            - '%security.lock_user_accounts.max_attempts%'

    marketplace.user.api_user_serializer:
        class: Wizacha\Marketplace\User\UserSerializer
        arguments: ['@marketplace.user.user_service', '%feature.quote_requests%']

    #-----------------------------------
    # Organisations/Groupes utilisateurs
    #-----------------------------------

    marketplace.organisation.service:
        class: Wizacha\Marketplace\Organisation\OrganisationService
        public: true
        arguments:
            - '@marketplace.basket.domain_service'
            - '@marketplace.user.user_service'
            - '@marketplace.organisation.user_group_service'
            - '@doctrine.orm.entity_manager'
            - '@marketplace.storage.organisation.identity_card'
            - '@marketplace.storage.organisation.proof_of_appointment'
            - '@event_dispatcher'

    marketplace.storage.organisation.identity_card:
        class: Wizacha\Component\Storage\Tygh
        public: true
        arguments:
            - '@Wizacha\Storage\OrganisationStorageService'
            - 'identity_card'

    marketplace.storage.organisation.proof_of_appointment:
        class: Wizacha\Component\Storage\Tygh
        public: true
        arguments:
            - '@Wizacha\Storage\OrganisationStorageService'
            - 'proof_of_appointment'

    marketplace.organisation.basket_service:
        class: Wizacha\Marketplace\Organisation\OrganisationBasketService
        public: true
        arguments:
            - '@marketplace.basket.domain_service'
            - '@marketplace.user.user_service'
            - '@doctrine.orm.entity_manager'

    marketplace.organisation.order_service:
        class: Wizacha\Marketplace\Organisation\OrganisationOrderService
        public: true
        arguments:
            - '@database_connection'
            - '@marketplace.order.order_service'
            - '@doctrine.orm.entity_manager'

    marketplace.organisation.user_group_service:
        class: Wizacha\Marketplace\Organisation\UserGroupService
        public: true
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@marketplace.storage.organisation.identity_card'
            - '@marketplace.storage.organisation.proof_of_appointment'

    #-----------
    # Price Tier
    #-----------
    Wizacha\Marketplace\PriceTier\Service\PriceTierService:
        public: true
        autowire: true
        arguments:
            $priceTierFlag: '%feature.tier_pricing%'

    Wizacha\Marketplace\PriceTier\Repository\PriceTierRepository:
        public: true
        arguments: ['@doctrine', Wizacha\Marketplace\PriceTier\PriceTier]

    Wizacha\Marketplace\ProductOptionInventory\Service\ProductOptionInventoryService:
        autowire: true

    Wizacha\Marketplace\ProductOptionInventory\Repository\ProductOptionInventoryRepository:
        public: true
        arguments: ['@doctrine', Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory]

    #---------
    # Division
    #---------
    Wizacha\Marketplace\Division\Service\DivisionService:
        public: true
        calls:
            - [featureIsEnabled, ['%feature.available_offers%']]
        arguments:
            - '@marketplace.division.division_repository'
            - '@marketplace.product.repository.read_model'

    Wizacha\Marketplace\Division\Service\DivisionProductsService:
        autowire: true
        autoconfigure: true
        tags:
            - { name: kernel.event_subscriber }
        calls:
            - [featureIsEnabled, ['%feature.available_offers%']]

    Wizacha\Marketplace\Division\Service\DivisionBlacklistsService:
        autowire: true
        autoconfigure: true
        tags:
            - { name: kernel.event_subscriber }
        calls:
            - [featureIsEnabled, ['%feature.available_offers%']]

    Wizacha\Marketplace\Division\Repository\DivisionRepository:
        arguments: ['@doctrine.orm.entity_manager']

    Wizacha\Marketplace\Division\Service\DivisionSettingsService:
        autowire: true
        autoconfigure: true
        public: true

    Wizacha\Marketplace\Division\DivisionSet:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Division\MarketplaceDivisionSettings:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Division\CompanyDivisionSettings:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Division\ProductDivisionSettings:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\Catalog\CompanyController:
        autoconfigure: true
        arguments:
            - '@marketplace.review.company.service'
            - '@marketplace.catalog.company_service'

    Wizacha\AppBundle\Controller\Api\Catalog\ProductController:
        autoconfigure: true
        arguments:
            - '%feature.catalog.show_out_of_stock_products%'
            - '@marketplace.product.productservice'
            - '@marketplace.pim.product.service'

    #---------
    # Currency
    #---------
    Wizacha\AppBundle\Controller\Api\CurrencyController:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Currency\CurrencyRepository:
        arguments: ['@doctrine', Wizacha\Marketplace\Currency\Currency, '@Wizacha\Marketplace\Currency\QueryFilters\Factory']

    Wizacha\Marketplace\Currency\CurrencyCountriesRepository:
        arguments: ['@doctrine', Wizacha\Marketplace\Currency\CurrencyCountries]

    Wizacha\Marketplace\Currency\CurrencyService:
        public: true
        autowire: true
        arguments:
            $regexpCountryCode: '%regexp_country_code%'
            $featureCurrencyAdvanced: '%feature.currency.advanced%'
            $defaultCurrencyCode: '%currency.code%'
            $defaultCurrencySymbol: '%currency.sign%'

    Wizacha\Marketplace\Currency\QueryFilters\CountryCode:
        autowire: true
        tags: ['currency.queryFilter']

    Wizacha\Marketplace\Currency\QueryFilters\Enabled:
        autowire: true
        tags: ['currency.queryFilter']

    Wizacha\Marketplace\Currency\QueryFilters\Factory:
        arguments: [!tagged currency.queryFilter]

    Wizacha\AppBundle\Controller\Backend\CurrencyController:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Currency\FixerClientApi:
        arguments:
            - '%currency.rates_provider%'
            - '%currency.fixer.api_key%'
            - '%currency.fixer.base_url%'

    #------------------
    # Chronopost client
    #------------------
    marketplace.chronopost.client:
        public: true
        class: Wizacha\Component\Chronopost\Client
        arguments:
            - '%chronopost.api.account_number%'
            - '%chronopost.api.password%'

    #---------------------
    # Mondial Relay client
    #---------------------
    Wizacha\Component\MondialRelay\Client:
        arguments:
            - '%mondial_relay.api.userid%'
            - '%mondial_relay.api.password%'
            - '%mondial_relay.api.endpoint%'

    # Mailing lists
    marketplace.mailing_list.mailing_list_service:
        public: true
        class: Wizacha\Marketplace\MailingList\MailingListService
        arguments: ['@doctrine.orm.entity_manager', '@event_dispatcher']

    marketplace.monolog.level.mailer:
        public: true
        class: Wizacha\Marketplace\MailingList\MailLogService
        arguments:
            - '@logger'
            - '%monolog.log_level.mailer%'
        tags:
            - { name: monolog.logger, channel: mailer }

    Wizacha\Marketplace\Payment\UserPaymentInfoService:
        arguments:
            - '@doctrine.orm.entity_manager'

    marketplace.commission.commission_repository:
        class: Wizacha\Marketplace\Commission\CommissionRepository
        public: true
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@broadway.uuid.generator'

    Wizacha\Marketplace\Commission\CommissionService:
        public: true
        tags:
            - { name: kernel.event_subscriber }
        arguments:
            - '@marketplace.commission.commission_repository'
            - '@database_connection'
            - '@app.setting_storage'
            - '%feature.commission.include_shipping_in_commission%'
            - '%feature.commission.add_full_shipping_to_commission%'
            - '@marketplace.pim.category_service'
            - '@logger'
            - '@marketplace.order.order_service'
            - '@order_amounts_commission_repository'

    Wizacha\Marketplace\Commission\TransactionListener:
        tags:
            - { name: kernel.event_subscriber }
        arguments:
            - '@marketplace.commission.commission_service'
            - '@database_connection'
            - '%cscart_table_prefix%'
            - '@marketplace.order.refund.refund_repository'
            - '@logger'

    Wizacha\Marketplace\Order\OrderListener:
        tags:
            - { name: kernel.event_subscriber }
        arguments:
            - '@marketplace.transaction.transaction_service'

    marketplace.banner.banner_service:
        public: true
        class: Wizacha\Marketplace\Cms\BannerService

    marketplace.product.xml_converter:
        class: Wizacha\Marketplace\PIM\Product\Exim\XmlConverter
        public: true
        arguments:
            - '@logger'

    Wizacha\Marketplace\Catalog\Company\CompanyService:
        arguments: ['@database_connection']

    Wizacha\Marketplace\Catalog\Category\CategoryService:
        public: true
        arguments: ['@database_connection']

    Wizacha\Marketplace\Catalog\AttributeService:
        public: true
        arguments: ['@database_connection']

    Wizacha\Marketplace\PIM\TransactionMode\TransactionModeService:
        public: true
        arguments:
            - '%marketplace.transaction_mode.transactional%'
            - '%marketplace.transaction_mode.affiliate%'
            - '%marketplace.transaction_mode.contact%'

    Wizacha\Marketplace\Seo\SeoService:
        public: true
        autowire: true
        arguments:
            - '@database_connection'
            - '@marketplace.seo.slug_generator'
            - '@router'
            - '@marketplace.seo.seo_repository'

    Wizacha\Marketplace\Seo\SeoRepository:
        public: true
        arguments:
            - '@database_connection'

    Wizacha\Marketplace\Favorite\FavoriteService:
        public: true
        arguments:
            - '@database_connection'
            - '@marketplace.product.productservice'

    marketplace.seo.slug_generator:
        class: Wizacha\Marketplace\Seo\Slug\SlugGenerator
        public: true

    Wizacha\Marketplace\AdminCompany:
        public: true
        autowire: true

    marketplace.oauth.provider:
        class: Wizacha\Component\OAuth\Provider\Delegate
        public: true
        arguments:
            - '%current_oauth_provider%'
            -
                okta: '@marketplace.oauth.provider.okta'
                openid: '@marketplace.oauth.provider.openid'
                google: '@marketplace.oauth.provider.google'
                somfy: '@marketplace.oauth.provider.somfy'
                azure: '@marketplace.oauth.provider.azure'

    marketplace.oauth.admin_provider:
        class: Wizacha\Component\OAuth\Provider\Delegate
        public: true
        arguments:
            - '%current_oauth_provider%'
            -
                openid: '@marketplace.oauth.provider.openid.admin'
                okta: '@marketplace.oauth.provider.okta.admin'
                azure: '@marketplace.oauth.provider.azure.admin'

    marketplace.oauth.provider.okta:
        class: Wizacha\Component\OAuth\Provider\Okta
        public: true
        arguments:
            - '@marketplace.user.user_service'
            - '@guzzle.client'
            - '%okta_authorization_server%'
            - '%okta_client_id%'
            - '%okta_client_secret%'
            - '%okta_redirect_uri%'
            - '%okta_admin_group%'
            - '%okta_vendor_group%'
            - '%okta_response_type%'

    marketplace.oauth.provider.openid:
        class: Wizacha\Component\OAuth\Provider\Openid
        public: true
        arguments:
            - '@marketplace.user.user_service'
            - '@guzzle.client'
            - '@marketplace.jwt.parser'
            - '%openid_discovery_uri%'
            - '%openid_client_id%'
            - '%openid_client_secret%'
            - '%openid_redirect_uri%'
            - '%openid_default_user_type%'

    marketplace.oauth.provider.openid.admin:
        class: Wizacha\Component\OAuth\Provider\Openid
        public: true
        arguments:
            - '@marketplace.user.user_service'
            - '@guzzle.client'
            - '@marketplace.jwt.parser'
            - '%openid_bo_discovery_uri%'
            - '%openid_bo_client_id%'
            - '%openid_bo_client_secret%'
            - '%openid_bo_redirect_uri%'

    marketplace.oauth.provider.okta.admin:
        class: Wizacha\Component\OAuth\Provider\Okta
        public: true
        arguments:
            - '@marketplace.user.user_service'
            - '@guzzle.client'
            - '%okta_bo_authorization_server%'
            - '%okta_bo_client_id%'
            - '%okta_bo_client_secret%'
            - '%okta_bo_redirect_uri%'
            - '%okta_bo_admin_group%'
            - '%okta_bo_vendor_group%'
            - '%okta_bo_response_type%'

    marketplace.oauth.provider.google:
        class: Wizacha\Component\OAuth\Provider\Google
        public: true
        arguments:
            - '@app.google.oauth_client'
            - '@marketplace.user.user_service'
            - '%google_oauth_redirect_uri%'

    marketplace.oauth.provider.somfy:
        class: Wizacha\Component\OAuth\Provider\Somfy
        public: true
        arguments:
            - '@marketplace.user.user_service'
            - '@guzzle.client'
            - '%somfy_authorization_server%'

    marketplace.oauth.provider.azure:
        class: Wizacha\Component\OAuth\Provider\Azure
        arguments:
            - '@marketplace.user.user_service'
            - '@guzzle.client'
            - '%azure_ad_discovery_uri%'
            - '%azure_ad_client_id%'
            - '%azure_ad_response_type%'
            - '%azure_ad_redirect_uri%'
            - '%azure_ad_client_secret%'
            - '%azure_ad_default_user_type%'

    marketplace.oauth.provider.azure.admin:
        class: Wizacha\Component\OAuth\Provider\Azure
        arguments:
            - '@marketplace.user.user_service'
            - '@guzzle.client'
            - '%azure_ad_bo_discovery_uri%'
            - '%azure_ad_bo_client_id%'
            - '%azure_ad_bo_response_type%'
            - '%azure_ad_bo_redirect_uri%'
            - '%azure_ad_bo_client_secret%'
            - '%azure_ad_bo_default_user_type%'

    marketplace.import.eximjoblog_handler:
        class: Wizacha\Component\Import\Handler\EximJobLogHandler
        public: true
        arguments: ['@doctrine.orm.entity_manager']

    marketplace.import.eximjob_handler:
        class: Wizacha\Component\Import\Handler\EximJobHandler
        public: true
        arguments: ['@marketplace.import.job_repository', '@marketplace.import.log_repository']

    marketplace.import.logger:
        public: true
        class: Monolog\Logger
        arguments: ['import', ['@marketplace.import.eximjoblog_handler', '@marketplace.import.eximjob_handler']]

    marketplace.import.job_repository:
        public: true
        class: Wizacha\Component\Import\EximJobRepository
        arguments: ['@doctrine', 'Wizacha\Component\Import\EximJob']

    marketplace.import.log_repository:
        class: Wizacha\Component\Import\EximJobLogRepository
        public: true
        arguments: ['@doctrine', 'Wizacha\Component\Import\EximJobLog']

    Wizacha\Component\Import\EximJobService:
        arguments:
            - '@marketplace.import.job_repository'
            - '@marketplace.import.log_repository'
            - '@Wizacha\Storage\CsvStorageService'


    Wizacha\Exim\Import\AutomatedFeedsService:
        autowire: true
        arguments:
            $registry: '@wizacha.registry'

    marketplace.transaction.transaction_repository:
        class: Wizacha\Marketplace\Transaction\TransactionRepository
        public: true
        arguments: ['@doctrine', 'Wizacha\Marketplace\Transaction\Transaction']

    Wizacha\Marketplace\Transaction\TransactionService:
        autoconfigure: true
        public: true
        arguments:
            - '@marketplace.transaction.transaction_repository'
            - '@event_dispatcher'
            - '@marketplace.order.refund.refund_repository'
            - '@logger'
            - '@Wizacha\Marketplace\Order\Repository\OrderRepository'

    Wizacha\Marketplace\Order\Refund\Repository\RefundRepository:
        arguments: ['@doctrine', 'Wizacha\Marketplace\Order\Refund\Entity\Refund']

    Wizacha\Marketplace\Order\Refund\Service\RefundService:
        autowire: true
        autoconfigure: true
        bind:
            $merchantsRefundAbility: '%feature.merchants_refund_ability%'

    Wizacha\Marketplace\Order\Refund\Service\RefundNotificationService:
        autowire: true

    Wizacha\Marketplace\Order\Refund\Checker\RefundChecker:
        autowire: true
        arguments:
            - '@marketplace.order.order_service'
            - '@marketplace.order.adjustment_service'
            - '@marketplace.order.refund.refund_amounts_calculator'
            - '%feature.order_adjustment%'

    Wizacha\Marketplace\Order\Refund\Checker\MarketplaceDiscountRefundChecker:
        autowire: true

    Wizacha\Marketplace\Order\Refund\Utils\RefundPayment:
        autowire: true

    Wizacha\Marketplace\Order\Refund\Utils\RefundConfig:
        autoconfigure: true
        arguments:
            - '%feature.refund_auto%'

    Wizacha\Marketplace\Order\Refund\Utils\RefundAmountsCalculator:
        autoconfigure: true
        arguments:
            - '@marketplace.order.adjustment_service'
            - '@marketplace.transaction.transaction_service'
            - '@marketplace.order.refund.refund_repository'
            - '%feature.order_adjustment%'

    marketplace.order.refund.refund_event_subscriber:
        class: Wizacha\Marketplace\Order\Refund\Event\RefundEventSubscriber
        public: true
        autoconfigure: true
        autowire: true

    Wizacha\Marketplace\Order\Refund\Utils\RefundCreator:
        autowire: true

    Wizacha\Marketplace\Order\Refund\Utils\RefundExecutor:
        autowire: true

    Wizacha\AppBundle\Controller\Api\RefundController:
        arguments:
            - '@router'
            - '@logger'
            - '@marketplace.order.refund.refund_service'
            - '@marketplace.order.refund.refund_notification_service'
            - '@marketplace.order.order_service'
            - '@marketplace.basket.repository.read_model'
            - '@marketplace.company_service'
        public: true

    Wizacha\Marketplace\Order\CreditNote\CreditNoteService:
        autowire: true

    Wizacha\Marketplace\Order\CreditNote\CreditNoteHelper:
        arguments:
            - '@monolog.logger'
            - '@guzzle.client'

    Wizacha\Marketplace\Order\Discount\OrderDiscountsCalculator:
        public: true
        autowire: true

    Wizacha\Marketplace\Order\Repository\OrderRepository:
        autowire: true

    marketplace.import.csv_uploader:
        class: Wizacha\Component\Import\Uploader\CsvUploader
        public: true
        arguments:
            - '@Wizacha\Storage\CsvStorageService'
            - '@marketplace.product.xml_converter'
            - '@marketplace.product.csv_converter'

    Wizacha\Component\Import\ImportFactory:
        public: true
        arguments:
            - '@marketplace.import.csv_uploader'
            - '@marketplace.import.job_service'
            - '@logger'

    Wizacha\Component\Import\ImportService:
        public: true
        arguments:
            - '@marketplace.backend_productimporter'
            - '@marketplace.async_dispatcher'
            - '@marketplace.backend_PricesImporter'
            - '@marketplace.backend_QuantitiesImporter'
            - '@marketplace.import.translation_importer'

    Wizacha\Exim\Import\TranslationImporter:
        public: true
        arguments:
            - '@marketplace.async_dispatcher'
            - '@app.translation'

    marketplace.dolist_template_repository:
        class: Wizacha\Component\Dolist\DolistTemplateRepository
        public: true
        arguments: ['@doctrine', 'Wizacha\Component\Dolist\Entities\DolistTemplate']

    marketplace.dolist_template_service:
        class: Wizacha\Component\Dolist\DolistTemplateService
        public: true
        arguments: ['@marketplace.dolist_template_repository']

    Wizacha\Marketplace\Order\Adjustment\OrderAdjustmentRepository:
        arguments: ['@doctrine', 'Wizacha\Marketplace\Order\Adjustment\OrderAdjustment']

    Wizacha\Marketplace\Order\Adjustment\OrderAdjustmentService:
        autowire: true

    marketplace.moderation.product_moderation_in_progress_repository:
        class: Wizacha\Premoderation\ProductModeration\ProductModerationInProgressRepository
        public: true
        arguments:
            - '@doctrine'
            - 'Wizacha\Premoderation\ProductModeration\ProductModerationInProgress'

    marketplace.moderation.moderation_service:
        class: Wizacha\Marketplace\PIM\Moderation\ModerationService
        public: true
        arguments:
            - '@marketplace.moderation.product_moderation_in_progress_service'
            - '@event_dispatcher'
            - '@marketplace.async_dispatcher'
            - '@database_connection'

    Wizacha\Premoderation\ProductModeration\ProductModerationInProgressService:
        public: true
        arguments:
            - '@marketplace.moderation.product_moderation_in_progress_repository'


    marketplace.jwt.parser:
        class: Lcobucci\JWT\Parser
        public: true

    app.google.oauth_client:
        class: Google_Client
        public: true
        arguments:
            -
                client_id: '%google_oauth_id%'
                client_secret: '%google_oauth_secret%'

    #--------
    # AuthLog
    #--------
    Wizacha\Component\AuthLog\AuthLogService:
        public: true
        arguments: ['@marketplace.authlog.repository', '@doctrine.orm.entity_manager']

    Wizacha\Component\AuthLog\AuthLogRepository:
        public: true
        arguments: ['@doctrine', 'Wizacha\Component\AuthLog\AuthLog', '@logger']
        tags:
            - { name: monolog.logger, channel: security }

    Wizacha\Marketplace\Company\CompanyRepository:
        autowire: true
        public: true

    Wizacha\Marketplace\Company\CompanyService:
        autowire: true
        public: true
        arguments:
            $vendorSubscriptionStorage: '@Wizacha\Storage\VendorSubscriptionStorageService'

    Wizacha\Marketplace\Company\LegacyCompanyCache:
        public: true
        autowire: true

    Wizacha\Marketplace\Shipping\ShippingService:
        autowire: true
        public: true

    Wizacha\Marketplace\PIM\Product\Template\ProductInfoFormType:
        autowire: true
        autoconfigure: true
        arguments:
            $orderAdjustmentFlag: '%feature.order_adjustment%'
            $mvpRules: '%feature.multi_vendor_product.rules%'
            $featureSubscription: '%feature.subscription%'

    Wizacha\Marketplace\PIM\Product\Template\TemplateService:
        public: true
        autowire: true
        autoconfigure: true
        arguments:
            $templates: '%marketplace.pim.product_templates%'
            $templatesConfig: '%config_product_templates%'

    Wizacha\Component\Geocoding\Geocoding:
        public: true
        arguments:
            - '%algolia.geocoding.api_identifier%'
            - '%algolia.geocoding.api_key_limited_rights%'
            - '@logger'

    marketplace.accounting.service:
        class: Wizacha\Marketplace\Accounting\AccountingService
        public: true
        arguments:
            - '@marketplace.order.order_service'

    marketplace.accounting.payment:
        public: true
        class: Wizacha\Marketplace\Accounting\AccountingPayment
        autowire: true

    Wizacha\Marketplace\Country\CountryRepository:
        arguments :
            - '@doctrine'
            - Wizacha\Marketplace\Country\Country

    Wizacha\Marketplace\Country\CountryService:
        autowire : true
        public : true

    Wizacha\AppBundle\Service\WhitelistDomainsService:
        autowire : true
        autoconfigure: true
        bind :
            $whitelistDomains : '%config.frontend_callback_whitelist%'

    Wizacha\Marketplace\ReadModel\ReadModelService:
        autowire: true
        public: true

    Wizacha\Search\Engine\AlgoliaService:
        public: true
        arguments:
            - '@marketplace.search_engine'
            - '@marketplace.search.product_index'
            - '%algolia.enable_command_update_post_deploy%'

    Wizacha\AppBundle\Controller\Api\BasketController:
        autowire: true
        autoconfigure: true
        bind:
            $featureAvailableOffers: '%feature.available_offers%'
            $stockService: '@marketplace.stock.domain_service'
            $chronopostClient: '@marketplace.chronopost.client'
            $mondialRelayClient: '@marketplace.mondial_relay.client'
            $orderAdjustmentEnabled: '%feature.order_adjustment%'
            $chronopostApiAccountNumber : '%chronopost.api.account_number%'
            $chronopostApiPassword : '%chronopost.api.password%'
            $mondialRelayApiUserId : '%mondial_relay.api.userid%'
            $mondialRelayApiPassword : '%mondial_relay.api.password%'
            $logger: '@logger'
            $functionalLogger: '@monolog.logger.functional'
            $featureQuoteRequestEnabled: '%feature.quote_requests%'

    Wizacha\Marketplace\Order\CreateOrder:
        autowire: true
        public: true

    Wizacha\AppBundle\Controller\Api\OrderTransactionsController:
        autowire: true
        autoconfigure: true


    #---------
    # Credit Card
    #---------
    Wizacha\Marketplace\CreditCard\Service\CreditCardService:
        public: true
        autowire: true

    Wizacha\Marketplace\CreditCard\Repository\CreditCardRepository:
        public: true
        arguments:
            - '@doctrine'
            - Wizacha\Marketplace\CreditCard\CreditCard

    Wizacha\AppBundle\Controller\Api\CreditCardController:
        autowire: true
        autoconfigure: true

    #---------
    # Subscription
    #---------
    Wizacha\Marketplace\Subscription\SubscriptionService:
        public: true
        autowire: true
        calls:
            -   method: setFeatureFlag
                arguments:
                    - '%feature.subscription%'
                    - '%feature.subscription.limit_renew_attempts%'
            -   method: setCreateOrder
                arguments:
                    - '@Wizacha\Marketplace\Order\CreateOrder'
            -   method: setConfiguredProcessors
                arguments:
                    - '@=service("Wizacha\\Marketplace\\Payment\\Processor\\ProcessorService").getConfiguredProcessor()'

    Wizacha\Marketplace\Subscription\SubscriptionRepository:
        public: true
        arguments: ['@doctrine', 'Wizacha\Marketplace\Subscription\Subscription']

    Wizacha\AppBundle\Controller\Api\SubscriptionController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\OrderController:
        autowire: true
        autoconfigure: true
        arguments:
            - '@Wizacha\Marketplace\Subscription\SubscriptionRepository'
            - '%feature.payment_deadline%'
            - '@logger'
            - '@marketplace.transaction.transaction_service'
            - '@marketplace.order.order_service'
            - '@marketplace.order.action.mark_as_delivered'
            - '@Wizacha\Marketplace\Order\OrderAttachment\Service\OrderAttachmentService'

    Wizacha\Marketplace\Subscription\Log\SubscriptionActionTraceRepository:
        public: true
        arguments: ['@doctrine', 'Wizacha\Marketplace\Subscription\Log\SubscriptionActionTrace']

    #---------
    # Payments
    #---------
    Wizacha\AppBundle\Controller\Api\PaymentController:
        autowire: true
        autoconfigure: true

    Wizacha\Component\Token\NumericTokenGenerator: ~

    Wizacha\Component\Token\TokenGeneratorInterface: '@Wizacha\Component\Token\NumericTokenGenerator'

    Wizacha\Marketplace\Order\Token\TokenService:
        autowire: true
        public: true

    Wizacha\Marketplace\Order\Token\TokenRepository:
        arguments:
            - '@doctrine'
            - Wizacha\Marketplace\Order\Token\Token

    Wizacha\Marketplace\Order\CheckIncompleteOrdersService:
        autowire: true
        autoconfigure: true
        arguments:
            $processors: !tagged marketplace.payment_processor_service

    Wizacha\AppBundle\Controller\Api\Payment\DirectDebitPaymentController:
        autowire: true
        autoconfigure: true

    #---------
    # Commissions
    #---------
    Wizacha\AppBundle\Controller\Api\CommissionController:
        autowire: true
        autoconfigure: true
        arguments:
            - '@marketplace.commission.commission_service'
            - '@marketplace.pim.category_service'
            - '@Wizacha\Marketplace\Company\CompanyService'
            - '@marketplace.commission.commission_repository'

    Wizacha\AppBundle\Controller\Backend\CommissionController:
        public: true
        arguments:
            - '@marketplace.commission.commission_service'

    Wizacha\AppBundle\Controller\Backend\TaxController:
        public: true
        arguments:
            - '@marketplace.international_tax.shipping'

    #---------
    # Notifications
    #---------
    Wizacha\AppBundle\Controller\Backend\NotificationsController:
        public: true
        autowire: true

    #---------
    # Date
    #---------
    Wizacha\Marketplace\Date\DateService:
        autowire: true

    Wizacha\Component\Divisions\XmlImporter:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@marketplace.division.service'
            - '%kernel.project_dir%'

    Wizacha\Marketplace\RgpdAnonymizer\RgpdCompanyAnonymizer:
        arguments:
            - '@marketplace.order.order_service'
            - '@Wizacha\Marketplace\Company\CompanyService'
            - '@Wizacha\Storage\VendorSubscriptionStorageService'
            - '@Wizacha\Marketplace\RgpdAnonymizer\RgpdUserAnonymizer'
            - '@Wizacha\Marketplace\RgpdAnonymizer\Report\CompanyReport'
        tags: ['rgpd.anonymizer']

    Wizacha\Marketplace\RgpdAnonymizer\Report\UserReport:
        autowire: true

    Wizacha\Marketplace\RgpdAnonymizer\Report\CompanyReport:
        autowire: true

    Wizacha\Marketplace\RgpdAnonymizer\RgpdUserAnonymizer:
        autowire: true
        tags: ['rgpd.anonymizer']

    Wizacha\Marketplace\RgpdAnonymizer\RgpdAnonymizerFactory:
        arguments:
            - !tagged rgpd.anonymizer

    Wizacha\Marketplace\RgpdAnonymizer\RgpdAnonymizerService:
        autowire: true
        autoconfigure: true
        public: true

    Wizacha\Marketplace\RgpdAnonymizer\RgpdDetachUserFromCompany:
        autowire: true

    Wizacha\Component\PurgeResources\Types\AbstractPurgeResource:
        abstract:  true
        arguments:
            - '@Wizacha\Component\PurgeResources\Report'
            - '%resources_purge_period.images%'

    Wizacha\Component\PurgeResources\Types\PurgeImages:
        parent: Wizacha\Component\PurgeResources\Types\AbstractPurgeResource
        autowire: true
        tags: ['purge.resource_type']

    Wizacha\Component\PurgeResources\PurgeResourceFactory:
        arguments: [!tagged purge.resource_type]

    Wizacha\Component\PurgeResources\Report: ~

    #----------------
    # OrderAttachment
    #----------------
    Wizacha\AppBundle\Controller\Api\Order\OrderAttachmentController:
        public: true
        autowire: true
        arguments:
            $orderAttachementsStorageService: '@Wizacha\Storage\OrderAttachmentsStorageService'

    Wizacha\Marketplace\Order\OrderAttachment\Repository\OrderAttachmentRepository:
        arguments: ['@doctrine', 'Wizacha\Marketplace\Order\OrderAttachment\Entity\OrderAttachment']

    Wizacha\Marketplace\Order\OrderAttachment\Service\OrderAttachmentService:
        public: true
        autowire: true

    Wizacha\AppBundle\Controller\Api\Organisation\OrganisationBasketController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\Organisation\OrganisationOrderController:
        public: true
        arguments:
            - '@Wizacha\Marketplace\Order\OrderAttachment\Service\OrderAttachmentService'
            - '@marketplace.organisation.order_service'
            - '@marketplace.order.order_service'
            - '@marketplace.organisation.service'
            - '@Wizacha\Storage\OrderAttachmentsStorageService'

    Wizacha\Marketplace\Order\OrderAttachment\Service\Validator\OrderAttachmentValidatorService:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Order\OrderAttachment\Service\Validator\OrderAttachmentTypeValidatorService:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Order\OrderAttachment\Service\UploadFileService:
        autowire: true
        autoconfigure: true
        arguments:
            $orderAttachmentsStorageService: '@Wizacha\Storage\OrderAttachmentsStorageService'

    #----------------
    # MessageAttachment
    #----------------
    Wizacha\Marketplace\MessageAttachment\MessageAttachmentService:
        class: Wizacha\Marketplace\MessageAttachment\MessageAttachmentService
        autoconfigure: true
        autowire: true
        arguments:
            - '@router'
            - '@marketplace.message_attachment.message_attachment_repository'
            - '@Wizacha\Storage\MessageAttachmentsStorageService'

    Wizacha\Marketplace\MessageAttachment\Repository\MessageAttachmentRepository:
        class: Wizacha\Marketplace\MessageAttachment\Repository\MessageAttachmentRepository
        arguments: [ '@doctrine', 'Wizacha\Marketplace\MessageAttachment\Entity\MessageAttachment' ]

    #----------------
    # OrderAction
    #----------------
    Wizacha\AppBundle\Controller\Api\Order\OrderActionController:
        public: true
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Order\OrderAction\Repository\OrderActionRepository:
        public: true
        arguments: ['@doctrine', 'Wizacha\Marketplace\Order\OrderAction\Entity\OrderAction']

    Wizacha\Marketplace\Order\OrderAction\Service\OrderActionService:
        public: true
        autowire: true
        autoconfigure: true

    #------------------
    # Read Model
    #------------------
    Wizacha\Marketplace\ReadModel\Denormalizer\ReadModelDenormalizerV1:
        tags: ['read_model_denormalizer']

    Wizacha\Marketplace\ReadModel\Denormalizer\ReadModelDenormalizerRegistry:
        arguments:
            - !tagged read_model_denormalizer

    #----
    # ETL
    #----
    Wizacha\Exim\CompanyTokenCsv:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Order\OrderMutator:
        autowire: true
        public: true

    Symfony\Component\Console\Output\OutputInterface:
        class: Symfony\Component\Console\Output\ConsoleOutput

    #----------------
    # Async Debouncer
    #----------------
    Wizacha\Async\Debouncer\DebouncerService:
        public: true
        arguments:
            - '@marketplace.debouncer.repository'
            - '@marketplace.queue_manager'
            - '@logger'
    Wizacha\Async\Debouncer\DebouncerEventSubscriber:
        public: true
        arguments:
            - '@marketplace.debouncer.repository'
        tags:
            - { name: kernel.event_subscriber }

    marketplace.debouncer.repository:
        class: Wizacha\Async\Debouncer\DebouncedJobRepository
        public: true
        autowire: true

    Wizacha\ProductManager:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Command\CreateSimpleUserCommand:
        autoconfigure: true
        arguments:
            - '@marketplace.user.user_service'
            - '%feature.security.enable_strict_password%'

    Wizacha\AppBundle\Command\CreateAdminUserCommand:
        autoconfigure: true
        arguments:
            - '@marketplace.user.user_service'
            - '%feature.security.enable_strict_password%'

    #------------------
    # CS CART wrappers
    #------------------
    Wizacha\Cscart\FnFunctions:
        autowire: true

    Wizacha\AppBundle\Command\TranslationCacheRefreshCommand:
        autoconfigure: true
        arguments:
            - '@app.translation'

    Wizacha\Sentinel\AlertManager:
        public: true
        arguments:
            - '@logger'
            - '%monolog.log_level.sentinel%'
            - '%marketplace.project_name%'
        tags:
            - { name: monolog.logger, channel: sentinel }

    marketplace.subscription.subscription_subscriber:
        class: Wizacha\Marketplace\Subscription\Event\SubscriptionEventSubscriber
        autowire: true
        public: true
        autoconfigure: true
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\Marketplace\PIM\Tax\TaxService:
        public: true
        autoconfigure: true
        autowire: true

    Wizacha\Marketplace\Order\DirectDebitService:
        autoconfigure: true
        autowire: true

    Wizacha\Component\Log\CommandLogService:
        autoconfigure: true
        autowire: true

    Wizacha\Component\Language\LanguageNormalizer:
        autoconfigure: true
        autowire: true

    Wizacha\Marketplace\Messenger\JsonEnvelopeSerializer:
        autowire: true
        autoconfigure: true
        public: true

    Wizacha\ActionLogger\ActionLoggerFactory:
        arguments:
            - '@logger'
            - '@Wizacha\AppBundle\Security\User\UserService'
        tags:
            - { name: monolog.logger, channel: audit }

    Wizacha\ActionLogger\EventSubscribers\ShippingSubscriber:
        public: true
        autowire: true
        autoconfigure: true

    Wizacha\ActionLogger\EventSubscribers\ProductSubscriber:
        public: true
        autowire: true
        autoconfigure: true

    Wizacha\ActionLogger\EventSubscribers\OrderSubscriber:
        public: true
        autowire: true
        autoconfigure: true

    Wizacha\ActionLogger\EventSubscribers\DispatchFundsFailedSubscriber:
        public: true
        autowire: true
        autoconfigure: true

    Wizacha\ActionLogger\EventSubscribers\TransactionSubscriber:
        public: true
        autowire: true
        autoconfigure: true

    Wizacha\Janus\JanusService:
        autoconfigure: true
        autowire: true

    Wizacha\Janus\JanusRepository:
        autoconfigure: true
        autowire: true

    Wizacha\Marketplace\Order\OrderAction\Event\OrderStatusUpdatedEventSubscriber:
        autowire: true
        public: true
        autoconfigure: true
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\Marketplace\Language\LanguageRepository:
        public: true
        autowire: true

    Wizacha\AppBundle\Controller\Backend\MangopayController:
        autoconfigure: true
        arguments:
            - '@marketplace.payment.mangopay'
            - '@Wizacha\Marketplace\CompanyPerson\CompanyPersonService'
            - '@logger'
            - '@Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService'

    Wizacha\Marketplace\CompanyPerson\CompanyPersonRepository:
        arguments:
            - '@doctrine'
            - Wizacha\Marketplace\CompanyPerson\CompanyPerson

    Wizacha\Marketplace\PSP\UboMangopay\UboMangopayRepository:
        arguments:
            - '@doctrine'
            - Wizacha\Marketplace\PSP\UboMangopay\UboMangopay

    Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService:
        arguments:
            - '@Wizacha\Marketplace\PSP\UboMangopay\UboMangopayRepository'
            - '@Wizacha\Marketplace\CompanyPerson\CompanyPersonRepository'

    Wizacha\Marketplace\CompanyPerson\CompanyPersonService:
        public: true
        arguments:
            - '@Wizacha\Marketplace\CompanyPerson\CompanyPersonRepository'

    Wizacha\AppBundle\Controller\Api\CompanyPerson\CompanyPersonController:
        autoconfigure: true
        arguments:
            - '@Wizacha\Marketplace\CompanyPerson\CompanyPersonService'
            - '@Wizacha\Marketplace\Company\CompanyService'
            - '@Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService'
            - '@event_dispatcher'
            - '@marketplace.payment.mangopay'

    # Repository for TVA
    Wizacha\Marketplace\Tax\InternationalTaxService:
        autoconfigure: true
        autowire: true
        arguments:
            $taxMode: '%config.tax_mode%'

    Wizacha\Marketplace\Tax\Repository\TaxRepository:
        autoconfigure: true
        autowire: true

    Wizacha\Marketplace\Tax\Repository\ShippingTaxRepository:
        arguments:
            - '@doctrine'
            - Wizacha\Marketplace\Tax\Entity\ShippingTax

    Wizacha\Marketplace\Order\AmountsCalculator\Repository\OrderAmountsRepository:
        arguments:
            - '@doctrine'
            - Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts

    order_amounts_commission_repository:
        class: Wizacha\Marketplace\Order\AmountsCalculator\Repository\OrderAmountsCommissionRepository
        public: true
        arguments:
            - '@doctrine'
            - '@doctrine.orm.entity_manager'
            - '@broadway.uuid.generator'

    Wizacha\AppBundle\Controller\Backend\MultiVendorProductController:
        public: true
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\PIM\Product\ProductRepository:
        autowire: true
        public: true
        autoconfigure: true

    Wizacha\Marketplace\PIM\Option\OptionRepository:
        autowire: true
        public: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Backend\FinancialFlowsHistoryController:
        public: true
        arguments:
            - '@marketplace.transaction.transaction_service'
            - '%currency.code%'
            - '@Wizacha\Marketplace\Order\OrderService'
            - '@marketplace.order.refund.refund_repository'
            - '@marketplace.financial_flows_history_service'

    Wizacha\Marketplace\FinancialFlowsHistory\FinancialFlowsHistoryService:
        public: true
        arguments:
            - '@marketplace.transaction.transaction_service'
            - '@marketplace.order.refund.refund_repository'
            - '@Wizacha\Marketplace\Order\OrderService'

    Wizacha\Marketplace\Transaction\Form\CommissionTransactionType:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Backend\UserGroupController:
        autoconfigure: true
        arguments:
            - '@Wizacha\Marketplace\Group\UserGroupService'
            - '@marketplace.import.csv_uploader'
            - '@Wizacha\Component\Import\EximJobService'

    Wizacha\Marketplace\Group\UserGroupService:
        arguments:
            - '@Wizacha\Marketplace\Group\GroupRepository'
            - '%feature.user_groups_enabled%'
            - '@Wizacha\Marketplace\User\UserService'
            - '@marketplace.import.csv_uploader'
            - '@marketplace.async_dispatcher'
        public: true

    Wizacha\Marketplace\Group\GroupRepository:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@database_connection'

    Wizacha\Marketplace\User\EKeyRepository:
        arguments:
            - '@database_connection'

    Wizacha\Marketplace\Metrics\SandboxMetrics:
        public: true
        autowire: true
        arguments:
            $limitDatabase: '%feature.enable_database_limit%'
            $databaseLimit: '%config.enable_database_limit_size%'

    #----
    # Related Products
    #----
    Wizacha\Marketplace\RelatedProduct\RelatedProductService:
        public: true
        arguments:
            - '@Wizacha\Marketplace\RelatedProduct\ConfigService'
            - '@Wizacha\Marketplace\RelatedProduct\RelatedProductRepository'

    Wizacha\Marketplace\RelatedProduct\ConfigService:
        public: true
        arguments:
            - '%config.related_products.types%'
            - '@logger'

    Wizacha\AppBundle\Controller\Api\RelatedProduct\RelatedProductController:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\RelatedProduct\RelatedProductRepository:
        public: true
        arguments:
            - '@doctrine'
            - Wizacha\Marketplace\RelatedProduct\RelatedProduct

    Wizacha\Marketplace\PIM\Product\ProductVisibilityReportService:
        autowire: true
        arguments:
            $productModerationInProgressRepository: '@marketplace.moderation.product_moderation_in_progress_repository'
        calls:
            - method: setShowOutOfStockProducts
              arguments:
                  - '%feature.catalog.show_out_of_stock_products%'
            - method: setShowZeroPriceProducts
              arguments:
                  - '%feature.catalog.show_zero_price_products%'

    Wizacha\Core\Concurrent\MutexService:
        public: true
        autowire: true

    marketplace.payment.logger:
        abstract: true
        class: Wizacha\Marketplace\Payment\AbstractPaymentLogger
        arguments:
            - '@monolog.logger.psp'
            - '@serializer'

    marketplace.payment.hipay_data_anonymizer:
        class: Wizacha\Marketplace\Payment\Security\HiPayDataAnonymizer

    marketplace.payment.hipay_logger:
        class: Wizacha\Marketplace\Payment\HiPay\HiPayLogger
        parent: marketplace.payment.logger
        calls:
            - method: setDataAnonymizer
              arguments:
                  - '@marketplace.payment.hipay_data_anonymizer'

    marketplace.payment.hipay_wallet_logger:
        class: Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayWalletLogger
        parent: marketplace.payment.logger
        calls:
            - method: setDataAnonymizer
              arguments:
                  - '@marketplace.payment.hipay_data_anonymizer'

    marketplace.payment.mangopay_http_client:
        class: Wizacha\Marketplace\Payment\Mangopay\MangoPayHTTPClient
        public: true
        arguments:
            - '@MangoPay\MangoPayApi'

    marketplace.payment.mangopay_data_anonymizer:
        class: Wizacha\Marketplace\Payment\Security\MangoPayDataAnonymizer

    marketplace.payment.mangopay_logger:
        class: Wizacha\Marketplace\Payment\Mangopay\MangoPayLogger
        parent: marketplace.payment.logger
        calls:
            - method: setDataAnonymizer
              arguments:
                  - '@marketplace.payment.mangopay_data_anonymizer'

    Wizacha\Marketplace\Payment\Stripe\StripeHTTPClient:
        parent: marketplace.payment.logger

    marketplace.payment.stripe_data_anonymizer:
        class: Wizacha\Marketplace\Payment\Security\StripeDataAnonymizer

    Wizacha\Marketplace\Payment\Stripe\StripeLogger:
        parent: marketplace.payment.logger
        calls:
            - method: setDataAnonymizer
              arguments:
                  - '@marketplace.payment.stripe_data_anonymizer'

    marketplace.payment.lemonway_data_anonymizer:
        class: Wizacha\Marketplace\Payment\Security\LemonWayDataAnonymizer

    Wizacha\Marketplace\Payment\LemonWay\LemonWayLogger:
        parent: marketplace.payment.logger
        calls:
            - method: setDataAnonymizer
              arguments:
                  - '@marketplace.payment.lemonway_data_anonymizer'

    Wizacha\Marketplace\User\UserPasswordHistoryRepository:
        arguments: [ '@doctrine', 'Wizacha\Marketplace\User\UserPasswordHistory' ]

    Wizacha\Marketplace\User\UserPasswordHistoryService:
        public: true
        autoconfigure: true
        arguments:
            - '@Wizacha\Marketplace\User\UserPasswordHistoryRepository'
            - '%previous_passwords_difference_limit%'

    Wizacha\ActionLogger\EventSubscribers\UserEventSubscriber:
        public: true
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Backend\InvoicingSettingsController:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\InvoicingSettings\InvoicingSettingsService:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\InvoicingSettings\InvoicingSettingsRepository:
        autowire: true
        autoconfigure: true

    #----
    # Notifications
    #----
    Wizacha\AppBundle\Notification\NotificationConfigRepository:
        public: true
        arguments:
            - '@doctrine'
            - Wizacha\AppBundle\Notification\NotificationConfig

    Wizacha\AppBundle\Notification\NotificationConfigService:
        public: true
        arguments:
            - '@Wizacha\AppBundle\Notification\NotificationConfigRepository'

    Wizacha\AppBundle\Controller\Backend\NotificationController:
        public: true
        arguments:
            - '@Wizacha\AppBundle\Notification\NotificationConfigService'

    #----
    # Webhooks
    #----
    Wizacha\Marketplace\Webhooks\Service\WebhooksService:
        autowire: true
        autoconfigure: true
        arguments:
            $httpClient: '@Wizacha\Component\Http\Client\GuzzleYavinClient'
            $uri: '%yavin.uri%'
            $yavinApiJwt: '%yavin_api_v2_jwt%'

    GuzzleHttp\Client:
        alias: guzzle.client

    Wizacha\Marketplace\Messenger\JsonMessageSerializer:
        autowire: true
        autoconfigure: true
        public: true

    Wizacha\Async\AbstractAsyncQueue:
        abstract: true

    Wizacha\Async\AmqpQueue:
        parent: Wizacha\Async\AbstractAsyncQueue
        public: true
        arguments:
            - '@marketplace.queue_manager'
            - '@marketplace.amqp.connection'
        tags: ['queue_service']

    Wizacha\Async\SqsQueue:
        parent: Wizacha\Async\AbstractAsyncQueue
        public: true
        arguments:
            - '@marketplace.aws.sqs.client'
            - '%queue.path%'
        tags: ['queue_service']

    Wizacha\Async\QueueServiceFactory:
        public: true
        arguments:
            - !tagged queue_service
            - '%queue.type%'

    #----
    # DumpAnonymizer
    #----
    Cocur\Slugify\Slugify:

    Wizacha\DumpAnonymizer\Services\OutputS3Manager:
        autoconfigure: true
        arguments:
            - '@dump_anonymizer.output.aws.client'
            - '%config.dump_anonymizer.output.s3.bucket%'
            - '%config.dump_anonymizer.output.s3.directory%'

    Wizacha\Marketplace\Payment\PaymentRepository:
        autowire: true
        autoconfigure: true

    Wizacha\Search\Engine\AlgoliaSyncService:
        autowire: true
        public: true

    #----
    # Quotes
    #----
    Wizacha\AppBundle\Controller\Api\Quotation\QuoteRequestSelectionController:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Quotation\Repository\QuoteRequestSelectionRepository:
        public: true
        arguments:
            - '@doctrine'
            - Wizacha\Marketplace\Quotation\Entity\QuoteRequestSelection

    Wizacha\Marketplace\Quotation\Repository\QuoteRequestSelectionDeclinationRepository:
        public: true
        arguments:
            - '@doctrine'
            - Wizacha\Marketplace\Quotation\Entity\QuoteRequestSelectionDeclination

    Wizacha\Marketplace\Quotation\QuoteRequestService:
        autowire: true
        public: true

    Wizacha\Component\BytesGenerator\RandomBytesGenerator:
