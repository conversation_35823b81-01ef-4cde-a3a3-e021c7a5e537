<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

if (isset($_ENV['PLATFORM_RELATIONSHIPS'])) {
    $relationships = json_decode(base64_decode($_ENV['PLATFORM_RELATIONSHIPS']), true);

    // Configure the marketplace database
    foreach ($relationships['marketplace-database'] as $endpoint) {
        if (!empty($endpoint['query']['is_master'])
            && (
                false === \array_key_exists('OVERRIDE_PLATFORM_SH_MARKETPLACE_DB', $_ENV)
                || $_ENV['OVERRIDE_PLATFORM_SH_MARKETPLACE_DB'] != '1'
            )
        ) {
            $container->setParameter('database_driver', 'pdo_'.$endpoint['scheme']);
            $container->setParameter('database_host', $endpoint['host']);
            $container->setParameter('database_port', $endpoint['port']);
            $container->setParameter('database_name', $endpoint['path']);
            $container->setParameter('database_user', $endpoint['username']);
            $container->setParameter('database_password', $endpoint['password']);
            break;
        }
    }

    // Configure the discuss database
    foreach ($relationships['discuss-database'] as $endpoint) {
        if (!empty($endpoint['query']['is_master'])
            && (
                false === \array_key_exists('OVERRIDE_PLATFORM_SH_DISCUSS_DB', $_ENV)
                || $_ENV['OVERRIDE_PLATFORM_SH_DISCUSS_DB'] != '1'
            )
        ) {
            $container->setParameter('discuss.database_driver', 'pdo_'.$endpoint['scheme']);
            $container->setParameter('discuss.database_host', $endpoint['host']);
            $container->setParameter('discuss.database_port', $endpoint['port']);
            $container->setParameter('discuss.database_dbname', $endpoint['path']);
            $container->setParameter('discuss.database_user', $endpoint['username']);
            $container->setParameter('discuss.database_password', $endpoint['password']);
            break;
        }
    }

    // Configure redis
    if (($endpoint = ($relationships['redis'][0] ?? null))
        && (
            false === \array_key_exists('OVERRIDE_PLATFORM_SH_REDIS', $_ENV)
            || $_ENV['OVERRIDE_PLATFORM_SH_REDIS'] != '1'
        )
    ) {
        $container->setParameter('redis.db', '0' );
        $container->setParameter('redis.url', $endpoint['host'].':'.$endpoint['port'].'/0');
    }

    // Configure rabbitmq
    if (($endpoint = ($relationships['rabbitmq'][0] ?? null))
        && (
            false === \array_key_exists('OVERRIDE_PLATFORM_SH_RABBITMQ', $_ENV)
            || $_ENV['OVERRIDE_PLATFORM_SH_RABBITMQ'] != '1'
        )
    ) {
        $queueType = 'amqp';
        $container->setParameter('queue.type', $queueType);
        $container->setParameter('amqp.host', $endpoint['host']);
        $container->setParameter('amqp.port', $endpoint['port']);
        $container->setParameter('amqp.user', $endpoint['username']);
        $container->setParameter('amqp.pass', $endpoint['password']);
        // configure rabbitmq DSN for messenger
        $container->setParameter(
            'yavin_messenger_transport_dsn',
            "{$queueType}://{$endpoint['username']}:{$endpoint['password']}@{$endpoint['host']}:{$endpoint['port']}/%2f/messages"
        );
    }
}

// Set a default unique secret, based on a project-specific entropy value.
if (isset($_ENV['PLATFORM_PROJECT_ENTROPY'])) {
    $container->setParameter('kernel.secret', $_ENV['PLATFORM_PROJECT_ENTROPY']);
}

// Configure host
// si http_host est configuré, alors on ne prend pas le host de platform
// mais celui configuré en dur. cette règle permet de générer des URL en dur
// avec un http_host principal même si plusieurs domaines sont configurés
// pour le projet
if (isset($_ENV['PLATFORM_ROUTES']) && $container->getParameter('http_host') == 'localhost') {
    $routes = json_decode(base64_decode($_ENV['PLATFORM_ROUTES']), true);

    foreach ($routes as $route => $routeInfo) {
        if ($routeInfo['type'] === 'upstream') {
            $host = parse_url($route, PHP_URL_HOST);
            $container->setParameter('http_host', $host);
            $container->setParameter('http_path', '');
            $container->setParameter('entrypoint.api_url', 'https://'.$host);
            break;
        }
    }
}

// Disable critical services when not in production (branch != master)
if (!empty($_ENV['PLATFORM_BRANCH']) && $_ENV['PLATFORM_BRANCH'] !== 'master') {
    // Override SQS settings. Use directory queue mode (non shared with other env)
    $container->setParameter('queue.type', 'directory');
    $container->setParameter('queue.path', '/app/var/queues/');

    // Force storage to file (disable S3)
    $container->setParameter('storage_system', 'file');

    // No emails
    $container->setParameter('mailer_transport', null);

    // No Algolia => Sql
    $container->setParameter('feature.sql_search', true);
    $container->setParameter('algolia.api_identifier', null);
    $container->setParameter('algolia.geocoding.api_identifier', null);

    // No payment
    $container->setParameter('lemonway_api.client_login', '');
    $container->setParameter('mangopay_api.client_id', '');
    $container->setParameter('hipay.cashin.api_username', '');
    $container->setParameter('hipay.cashout.api_login', '');
    $container->setParameter('smoney.token', '');
}
