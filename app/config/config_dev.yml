imports:
    - { resource: config.yml }
    - { resource: services.fixtures.yml }

framework:
    router:
        resource: "%kernel.project_dir%/app/config/routing_dev.yml"
        strict_requirements: true
    profiler: { only_exceptions: false }

web_profiler:
    toolbar: true
    intercept_redirects: false

twig:
    globals:
        isDev: true

doctrine:
    dbal:
        logging: false

monolog:
    handlers:
        main:
            type: stream
            path: '%kernel.logs_dir%/%kernel.environment%.log'
            level: debug
            channels: ["!event"]
        console:
            type:  console

parameters:
    image.serve_fake_images: true
    googlemaps.api_key: 'AIzaSyCXUF-GvpxgjwnXFbOnmGVKGpxURjxIvGE'
    google_analytics_tracking_id: 'UA-********-6'
    env(HTTP_PATH): ''
    entrypoint.api_url: "http://%http_host%/%http_path%"

services:
    marketplace.asset_manager:
        class: Wizacha\Marketplace\Theme\DevAssetManager
        public: true
        arguments:
            - '%kernel.project_dir%'
            - '@wizacha.registry'
            - '%base_url%'

    debug.tygh_queries_profiler:
        alias: doctrine.dbal.logger.profiling.default
