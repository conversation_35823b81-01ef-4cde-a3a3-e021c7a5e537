imports:
    - { resource: config.yml }
    - { resource: services.fixtures.yml }
    - { resource: services.test.yml }

parameters:
    # Use the test database
    database_name: '%database_name_tests%'

    recaptcha.private_key:
    recaptcha.public_key:

    google_maps_key:

    marketplace.transaction_mode.transactional: true
    marketplace.transaction_mode.affiliate: true
    marketplace.transaction_mode.contact: true

    system_api_user_password: $2y$12$XCBQ6SYiS/ddzYkgAj6osuuD9v7tnz1EE9Hy69Vt3NGkTRkH4tYzC # FakeSecret
    api_application_token: ~

    # HTTP Cache Feature Flag
    feature.cache_http: true

    # Yavin
    feature.enable_yavin: true
    yavin_messenger_transport_dsn: 'amqp://yavin:yavin@%yavin_messenger_vhost_test%:5672/%2f/messages?auto_setup=true' # we add auto-setup=true here because in context test we need to create the queue on init

framework:
    router:
        resource: "%kernel.project_dir%/app/config/routing_test.yml"
        strict_requirements: true
    profiler:
        collect: false
    test: ~
    # session:
    #    storage_id: session.storage.mock_file

twig:
    globals:
        isDev: true

web_profiler:
    toolbar: false
    intercept_redirects: false

monolog:
    handlers:
        applog:
            type:  stream
            path:  "%kernel.logs_dir%/test.log"
            level: debug
        console:
            type:  console

swiftmailer:
    default_mailer: fake_mailer

services:

    marketplace.asset_manager:
        class: Wizacha\Marketplace\Theme\DevAssetManager
        public: true
        arguments:
            - '%kernel.project_dir%'
            - '@wizacha.registry'
            - '%base_url%'

    marketplace.search_engine:
        class: 'Wizacha\Search\Engine\SQL'
        public: true
        arguments: ['@wizacha.registry']

    marketplace.search.product_index:
        public: true
        class: 'Wizacha\Search\Product\SqlProductIndex'
        parent: Wizacha\Search\Index\AbstractIndex
        calls:
            - [ setConnection, [ '@database_connection' ] ]

    marketplace.search.product_record_factory:
        alias: Wizacha\Search\Record\LegacyProductRecordFactory
        public: true

    Wizacha\Search\Record\LegacyProductRecordFactory:
        public: true
        autowire: true

    app.captcha:
        class: Wizacha\Marketplace\Security\Captcha\Faker\Captcha
        public: true

    # Unregister Symfony's session mock since CS Cart accesses $_SESSION directly
    test.session.listener:
        class: Wizacha\Bridge\Symfony\Test\TestSessionListener
        arguments: ['@service_container']

    # Override the admin company with a fake one
    marketplace.admin_company:
        class: Wizacha\Test\Fixture\FakeAdminCompany
        public: true

    # use fake translator in test
    test.translator:
        class: Wizacha\Bridge\Symfony\DummyTranslator
        decorates: translator
        arguments: ['@test.translator.inner']
        public: false

    # use mock videoStorage in test
    marketplace.pim.video_storage:
        class: Wizacha\Test\Mock\FakeVideoStorage


    Wizacha\Test\Mock\FakeCommissionService:
        public: true
        tags:
            - { name: kernel.event_subscriber }
        arguments:
            - '@marketplace.commission.commission_repository'
            - '@database_connection'
            - '@app.setting_storage'
            - '%feature.commission.include_shipping_in_commission%'
            - '%feature.commission.add_full_shipping_to_commission%'
            - '@marketplace.pim.category_service'
            - '@logger'
            - '@marketplace.order.order_service'
            - '@order_amounts_commission_repository'
    marketplace.commission.commission_service:
        public: true
        alias: Wizacha\Test\Mock\FakeCommissionService
