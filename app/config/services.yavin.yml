services:
    _defaults:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Messenger\BroadcastPublisher:
        public: true
        arguments:
            - '@message_bus'
            - '%yavin_messenger_transport_dsn%'
            - '@logger'
            - '%feature.enable_yavin%'

    GuzzleHttp\Client:
        autowire: true

    Wizacha\Component\Http\Client\GuzzleYavinClient:
        public: true
        autowire: true

    Wizacha\Component\Http\Factory\YavinResponseFactory:
        public: true
        autowire: true

    Wizacha\Component\Http\OptionMapper:
        public: true
        autowire: true

    Wizacha\Component\Http\Factory\ProxyRequestFactory:
        autowire: true

    Wizacha\AppBundle\Controller\ProxyController:
        autowire: true
        public: true
