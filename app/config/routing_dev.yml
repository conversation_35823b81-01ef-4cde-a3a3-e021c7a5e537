_wdt:
    resource: "@WebProfilerBundle/Resources/config/routing/wdt.xml"
    prefix:   /_wdt

_profiler:
    resource: "@WebProfilerBundle/Resources/config/routing/profiler.xml"
    prefix:   /_profiler

_errors:
    resource: "@TwigBundle/Resources/config/routing/errors.xml"
    prefix:   /_error

_main:
    resource: routing.yml

oauth:
    path: /oauth
    defaults: { _controller: AppBundle:OAuth:test }
    methods: [GET]

mangopay_hooks:
    path: /mangopay-hooks
    defaults:
        _controller: AppBundle:PaymentNotification:mangopayBankwire
        isPsp: true
    schemes:  [http]
    methods: [GET]

payment_notification_mangopay_card:
    path: /payment-notification/mangopay-card
    defaults:
        _controller: AppBundle:PaymentNotification:mangopayCard
        isPsp: true
    schemes: [http]
    methods: [GET]

payment_notification_lemonway_card:
    path: /payment-notification/lemonway-card
    schemes: [http]
    defaults:
        _controller: AppBundle:PaymentNotification:lemonwayCard
        isPsp: true

payment_notification_lemonway_bankwire:
    path: /payment-notification/lemonway-bankwire
    defaults:
        _controller: AppBundle:PaymentNotification:lemonwayBankwire
        isPsp: true
    schemes: [http]
    methods: [POST]

payment_notification_lemonway_sepa_transaction:
    path: /payment-notification/lemonway-sepa-transaction
    defaults:
        _controller: AppBundle:PaymentNotification\LemonWaySepaNotification:lemonwaySepaTransaction
        isPsp: true
    schemes: [http]
    methods: [POST]

payment_notification_lemonway_sign_document:
    path: /payment-notification/lemonway-sign-document
    defaults:
        _controller: AppBundle:PaymentNotification\SepaMandateSignatureNotification:lemonwaySignDocument
        isPsp: true
    schemes: [http]

payment_notification_hipay_card:
    path: /payment-notification/hipay-card
    schemes: [http]
    defaults:
        _controller: AppBundle:PaymentNotification:hipayCard
        isPsp: true

payment_notification_smoney_card:
    path: /payment-notification/smoney-card
    defaults:
        _controller: AppBundle:PaymentNotification:smoneyCard
        isPsp: true
    schemes: [http]
    methods: [GET, POST]

payment_notification_transfer_standby:
    path: /payment-notification/transfer-standby
    defaults:
        _controller: AppBundle:PaymentNotification:markBankTransferIsDone
        isPsp: true
    schemes: [http]

payment_notification_stripe_card:
    path: /payment-notification/stripe-card
    defaults:
        _controller: AppBundle:PaymentNotification:stripeCard
        isPsp: true
    schemes:  [http]
    methods: [POST]

payment_notification_stripe_sepa:
    path: /payment-notification/stripe-sepa
    defaults:
        _controller: AppBundle:PaymentNotification:stripeSepa
        isPsp: true
    schemes: [http]
    methods: [POST]

payment_notification_stripe_sepa_init:
    path: /payment-notification/stripe-sepa-init
    defaults:
        _controller: AppBundle:PaymentNotification:stripeSepaInit
        isPsp: true
    schemes: [http]
    methods: [POST]

payment_notification_hipay_sepa_transaction:
    path: /payment-notification/hipay-sepa-transaction
    defaults:
        _controller: AppBundle:PaymentNotification\HipaySepaNotification:transaction
        isPsp: true
    schemes: [http]
    methods: [POST]

payment_card_stripe_update_callback:
    path: /payment/card/card-updated
    defaults:
        _controller: AppBundle:Card:stripeUpdateCallback
        isPsp: true
    schemes:  [http]

payment_card_stripe_update:
    path: /payment/card/update
    defaults:
        _controller: AppBundle:Card:stripeUpdate
        isPsp: true
    schemes:  [http]

video_transcode:
    path:  /video/transcode
    defaults: { _controller: AppBundle:Video:startTranscodeJobs }
    schemes: [http] # force URL generation with http

video_copy:
    path:  /video/copy
    defaults: { _controller: AppBundle:Video:startCopyUrl }
    methods: [POST]
    schemes: [http] # force URL generation with http

wizaplace_debug_bundle:
    resource: '@WizaplaceDebugBundle/Resources/config/routing.yml'
