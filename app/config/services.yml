services:
    Wizacha\AppBundle\Controller\Api\OrderDetailsController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Backend\DiscussController :
        autowire : true
        autoconfigure : true
        arguments:
            - '@marketplace.message_attachment.message_attachment_service'

    Wizacha\AppBundle\Controller\Api\DiscussionController :
        autowire : true
        autoconfigure : true
        arguments:
            - '@marketplace.message_attachment.message_attachment_service'
            - '@purifier.default'
            - '@Wizacha\Storage\MessageAttachmentsStorageService'
            - '@app.discuss_service'

    Wizacha\AppBundle\Controller\DocumentationController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\TranslationController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\CompanyController:
        autowire: true
        autoconfigure: true
        arguments:
            - '@Wizacha\Marketplace\Company\CompanyService'
            - '@Wizacha\Marketplace\Division\Service\DivisionBlacklistsService'
            - '@Wizacha\Marketplace\Division\Service\DivisionService'
            - '@Wizacha\Marketplace\Subscription\SubscriptionRepository'
            - '@validator'
            - '@Wizacha\Marketplace\Division\Service\DivisionSettingsService'
            - '@marketplace.payment.mangopay'

    Wizacha\AppBundle\Controller\Api\Promotion\CatalogPromotionsController:
        autowire: true
        autoconfigure: true
        arguments:
            $serializer: '@serializer'

    Wizacha\AppBundle\Controller\Api\Promotion\CatalogPromotionNormalizer:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\Promotion\BasketPromotionsController:
        autowire: true
        autoconfigure: true
        arguments:
            $serializer: '@serializer'

    Wizacha\AppBundle\Controller\Api\Promotion\MarketplacePromotionsController:
        autowire: true
        autoconfigure: true
        arguments:
            $serializer: '@serializer'
            $marketplaceDiscountOptionalBonuses: '%feature.marketplace_discount_optional_bonuses%'

    Wizacha\AppBundle\Controller\Api\Promotion\MarketplacePromotionValidator:
        autowire: true
        autoconfigure: true
        arguments:
            $serializer: '@serializer'

    Wizacha\AppBundle\Controller\Api\Promotion\BasketPromotionValidator:
        autowire: true
        autoconfigure: true
        arguments:
            $serializer: '@serializer'

    Wizacha\AppBundle\Controller\Api\Promotion\RulesUserGroupValidator:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\Promotion\BasketPromotionNormalizer:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\Order\HandDeliveryController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\Order\MondialRelayController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\ImportController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\JobController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\Division\DivisionController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\LanguageController:
        autowire: true
        autoconfigure: true
        public: true

    Wizacha\AppBundle\Controller\Api\AuthLogController:
        autowire: true
        autoconfigure: true
        public: true

    Wizacha\Marketplace\Subscription\Log\SubscriptionActionTraceService:
        autowire: true
        autoconfigure: true
        public: true

    Wizacha\AppBundle\Controller\Backend\AuthController:
        public: true
        arguments:
            - '@marketplace.authlog.repository'
            - '@marketplace.oauth.admin_provider'
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\PingController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\OrderAdjustmentController:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Controller\Api\RefundController:
        autoconfigure: true
        autowire: true

    Wizacha\AppBundle\Controller\Api\CreditNoteController:
        autoconfigure: true
        autowire: true
        calls:
            - ['setCurrency', ['%currency.sign%']]

    wizacha.registry:
        class: Wizacha\Registry
        public: true
        factory:  [Wizacha\Registry, defaultInstance]

    marketplace.cache:
        public: true
        class: Tygh\Backend\Cache\ABackend
        factory: ['@wizacha.registry', cache]

    templating.mustache:
        class: Mustache_Engine
        public: true

    templating.smarty:
        class: Smarty
        public: true
        factory: [Wizacha\Smarty\SmartyFactory, create]
        arguments: ['@wizacha.registry']

    Wizacha\ImageManager:
        public: true
        arguments:
            - '@Wizacha\Storage\ImagesStorageService'
            - '%image.serve_fake_images%'

    Wizacha\CloudImageManager:
        public: true
        autowire: true
        arguments:
            $apiUrl: '%cloudimage_api_url%'
            $clientKey: '%cloudimage_client_key%'
            $versionHead: '%cloudimage_version_head%'
            $client: '@guzzle.client'

    app.captcha:
        class: Wizacha\Marketplace\Security\Captcha\Captcha
        public: true
        arguments:
            - '%recaptcha.public_key%'
            - '%recaptcha.private_key%'

    broadway.aggregate_factory:
        class: Broadway\EventSourcing\AggregateFactory\PublicConstructorAggregateFactory
    broadway.auditing.serializer:
        class: Wizacha\Bridge\Broadway\Auditing\CommandSerializer

    marketplace.api_user_provider:
        class: Wizacha\AppBundle\Security\User\ApiSecurityUserProvider
        arguments: ['@marketplace.user.user_repository']

    marketplace.api_basic_authentication_entry_point:
        class: Wizacha\AppBundle\Security\User\ApiBasicAuthenticationEntryPoint
        parent: security.authentication.basic_entry_point
        arguments:
            - '%security.authentication.basic.realmname%'
            - '@translator'

    marketplace.user_provider:
            class: Wizacha\AppBundle\Security\User\UserProvider
            arguments:
                - '@marketplace.user.user_service'
                - '@app.authenticator_throttling'

    marketplace.apikey_authenticator:
        class:  Wizacha\AppBundle\Security\User\ApiKeyAuthenticator
        public: false
        arguments:
            - '@marketplace.user.user_service'
            - '@logger'

    marketplace.cscart_password_encoder:
        class:  Wizacha\AppBundle\Security\User\CscartPasswordEncoder

    Wizacha\AppBundle\Security\User\UserService:
        public: true
        arguments:
            - '@security.token_storage'
            - '@marketplace.basket.domain_service'
            - '@session'
            - '@logger'

    Wizacha\AppBundle\Security\Listener\UserAuthenticatedListener:
        autowire: true
        autoconfigure: true
        arguments:
            $authenticatorThrottling: '@app.authenticator_throttling'

    Wizacha\AppBundle\Security\Listener\UserAuthenticationFailureListener:
        autowire: true
        autoconfigure: true
        arguments:
            $authenticatorThrottling: '@app.authenticator_throttling'

    marketplace.table_truncator:
        class: Wizacha\Bridge\Doctrine\TableTruncator
        public: true
        arguments: ['@database_connection']

    app.twig_extension:
        public: true
        class: Wizacha\AppBundle\Twig\AppExtension
        arguments:
            - '@marketplace.price.formatter'
            - '@marketplace.backend.price.formatter'
            - '@assets.packages'
            - '@session'
            - '@service_container'
            - '@basic.configuration_service'
        tags:
            - { name: twig.extension }

    crypto:
        class: Wizacha\Component\Crypto\Crypto
        arguments: ['@logger']

    purifier.default:
        class: Wizacha\Purifier\Purifier
        public: true
        arguments:
            - '@exercise_html_purifier.default'
            - '%feature.security.xss_filter%'

    app.exception_subscriber:
        class: Wizacha\AppBundle\EventSubscriber\ExceptionSubscriber
        tags:
            - { name: kernel.event_subscriber }
        autowire: true

    app.json_decoder_request_subscriber:
        class: Wizacha\AppBundle\EventSubscriber\JsonDecoderRequestSubscriber
        tags:
            - { name: kernel.event_subscriber }

    templating.engine.smarty:
        class: Wizacha\Smarty\SmartyEngine
        arguments:
            - '@templating.smarty'
            - '@twig'

    fallback_controller:
        class: Wizacha\AppBundle\Controller\FallbackController
        public: true
        arguments: ['@templating']

    less_compiler:
        public: true
        class: Wizacha\Marketplace\Theme\LessCompiler

    twig.extension.text:
        class: Twig_Extensions_Extension_Text
        tags:
            - { name: twig.extension }

    twig.extension.intl:
        class: Twig_Extensions_Extension_Intl
        tags:
            - { name: twig.extension }

    app.validator.url_validator:
        public: true
        class: Wizacha\AppBundle\Validator\UrlValidator

    app.validator.video_validator:
        class: Wizacha\AppBundle\Validator\VideoValidator
        public: true

    Wizacha\Prediggo\Exporter:
        public: true
        arguments:
            - '@prediggo.product_exporter'
            - '@prediggo.hierarchy_exporter'
            - '@prediggo.attribute_translation_exporter'
            - '@prediggo.user_exporter'
            - '@prediggo.order_exporter'
            - '@prediggo.cms_exporter'
            - '@Wizacha\Storage\PrediggoStorageService'

    prediggo.product_exporter:
        class: Wizacha\Prediggo\ProductExporter
        public: true
        arguments:
            - '@marketplace.product.productservice'
            - '@marketplace.multi_vendor_product.service'
            - '@marketplace.pim.product.service'
            - '@logger'
            - '%search_record_product_image_width%'
            - '%search_record_product_image_height%'

    prediggo.user_exporter:
        class: Wizacha\Prediggo\UserExporter
        public: true
        arguments:
            - '@marketplace.user.user_service'

    prediggo.order_exporter:
        class: Wizacha\Prediggo\OrderExporter
        public: true
        arguments:
            - '@marketplace.order.order_service'

    prediggo.cms_exporter:
        class: Wizacha\Prediggo\CMSExporter
        public: true
        arguments:
            - '@marketplace.cms.page_service'

    prediggo.hierarchy_exporter:
        class: Wizacha\Prediggo\HierarchyExporter
        public: true
        arguments:
            - '@logger'

    prediggo.attribute_translation_exporter:
        class: Wizacha\Prediggo\AttributeTranslationExporter
        public: true
        arguments:
            - '@marketplace.pim.attribute_service'
            - '@marketplace.catalog.company_service'
            - '@logger'

    guzzle.client:
        class: GuzzleHttp\Client
        public: true

    cscart.crypt:
        class: phpseclib\Crypt\Blowfish
        public: true
        arguments: [1] #Value of \phpseclib\Crypt\Base::MODE_ECB
        calls:
            - [setKey, ['%cscart.crypt_key%']]
            - [disablePadding, []]

    app.discuss_service:
        class: Wizacha\Discuss\DiscussService
        public: true
        arguments:
            - '@event_dispatcher'
            - '@marketplace.user.user_repository'
            - '@doctrine.orm.entity_manager'
            - '@app.redis.default'
            - '@marketplace.order.order_service'
            - '@Wizacha\Marketplace\Company\CompanyService'
        tags:
            - { name: kernel.event_subscriber }
        calls:
            - [setRouter, ['@router']]

    app.seo_slug_router:
        public: true
        class: Wizacha\Bridge\Symfony\SeoSlugRouter
        arguments: ['@marketplace.seo.slug_generator', '@marketplace.seo.seo_service', '%http_path%']
        tags:
            - { name: kernel.event_subscriber }

    app.google_auth_support_client:
        class: Google_Client
        public: true
        arguments:
            -
                client_id: '%google_oauth_id%'
                client_secret: '%google_oauth_secret%'
                redirect_uri: '%google_oauth_redirect_url%'

    app.feature_flag.listener:
        class: Wizacha\AppBundle\EventListener\FeatureFlagListener
        autowire: true
        tags:
            - { name: kernel.event_listener, event: kernel.controller, method: onKernelController }

    # On décore le router listener de symfony pour ajouter notre logique de routing
    # à savoir si on doit appeler fn_dispatch pour cscart ou laisser symfony
    # trouver le bon controller
    router_listener.cs_cart:
        class: Wizacha\AppBundle\EventListener\RouterListener
        public: true
        autowire: true
        arguments:
            $routerListener: '@router_listener'
        tags:
            - { name: kernel.event_subscriber }
            - { name: monolog.logger, channel: request }

    # Le check des permissions de l'admin est déplacé dans un subscriber pour alléger le fn_dispatch
    app.permissions_subscriber:
        class: Wizacha\AppBundle\EventSubscriber\CheckPermissionsSubscriber
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\AppBundle\EventSubscriber\ApiApplicationFirewall:
        autoconfigure: true
        autowire: true
        arguments:
            $expectedToken: '%api_application_token%'

    Wizacha\AppBundle\EventSubscriber\PspRequestSubscriber:
        autoconfigure: true
        autowire: true
        tags:
            - { name: monolog.logger, channel: psp }

    # Ce listener redirige vers le login si pas connecté dans le BO
    app.auth_redirect:
        class: Wizacha\AppBundle\EventSubscriber\AuthRedirectSubscriber
        tags:
            - { name: kernel.event_subscriber }

    Wizacha\Component\Html\HtmlService:
        arguments:
            - '%http_host%'

    Wizacha\Component\PdfGenerator\PdfGenerator:
        autowire: true

    form.login:
        class: Wizacha\AppBundle\Form\Type\LoginType
        tags:
            - { name: form.type, alias: login }

    form.feature:
        class: Wizacha\AppBundle\Form\Type\FeatureType
        tags:
            - { name: form.type, alias: feature }

    event.subscriber.search:
        class: Wizacha\Search\EventSubscriber
        public: true
        arguments:
            - '@marketplace.search.category_index'
            - '@event_dispatcher'
            - '@marketplace.pim.attribute_service'
            - '@marketplace.product.projector'
            - '@marketplace.pim.product.service'
            - '@marketplace.multi_vendor_product.service'
            - '@marketplace.search.product_index'
            - '@marketplace.async_dispatcher'
            - '@marketplace.product.declination_factory'
        tags:
            - { name: kernel.event_subscriber }

    event.subscriber.premoderation:
        class: Wizacha\Premoderation\EventSubscriber
        arguments: ['@marketplace.pim.product.service', '@marketplace.moderation.moderation_service' ]
        tags:
            - { name: kernel.event_subscriber }

    marketplace.catalog:
        class: Wizacha\Marketplace\Catalog\CatalogService
        public: true
        arguments:
            - '@database_connection'
            - '@marketplace.product.projector'
            - '@marketplace.async_dispatcher'

    monolog.processor.http_request:
        class: Wizacha\Bridge\Monolog\Processor\HttpRequestProcessor
        autoconfigure: true
        arguments:
            - '%request_id.header_name%'
        tags:
            - { name: monolog.processor, method: addCorrelationInformation }

    monolog.processor.console_command:
        class: Wizacha\Bridge\Monolog\Processor\ConsoleCommandProcessor
        autoconfigure: true
        tags:
            - { name: monolog.processor, method: addCorrelationInformation }

    monolog.processor.extra_context:
        class: Wizacha\Bridge\Monolog\Processor\ExtraContextProcessor
        autoconfigure: true
        tags:
            - { name: monolog.processor }

    monolog.logger.http_request:
        class: Wizacha\Bridge\Monolog\EventLogger\HttpRequestEventLogger
        autoconfigure: true
        tags:
            - { name: monolog.logger, channel: request }

    monolog.logger.console_command:
        class: Wizacha\Bridge\Monolog\EventLogger\ConsoleCommandEventLogger
        autoconfigure: true
        tags:
            - { name: monolog.logger, channel: console }

    app.file_system:
        class: Wizacha\AppBundle\Service\FileSystemService

    app.bfo_service:
        class: Wizacha\AppBundle\Service\BFOService
        autoconfigure: true
        autowire: true
        arguments:
            $bfoUrl: '%config.bfo_url%'
            $bfoEnabled: '%feature.enable_bfo%'

    app.notifier_parser:
        class: Wizacha\AppBundle\Service\NotifierParseService
        public: true
        arguments: ['@app.file_system']

    app.redis.default:
        class: Wizacha\Cache\RedisDecorator
        arguments: ['@snc_redis.default']
        lazy: true

    app.redis.cscart_sessions:
        class: Wizacha\Cache\RedisDecorator
        arguments: ['@snc_redis.cscart_sessions']
        lazy: true

    app.redis.handlers:
        class: Wizacha\Cache\RedisDecorator
        arguments: ['@snc_redis.handlers']
        lazy: true

    app.redis.locks:
        class: Wizacha\Cache\RedisDecorator
        arguments: ['@snc_redis.locks']
        lazy: true

    app.authenticator_throttling.email:
        class: Wizacha\AppBundle\Security\User\Throttling\AuthenticatorEmailThrottling
        public: true
        arguments:
            - '@app.redis.default'
            - '%authenticator_throttling.email_throttle_map%'
            - '%authenticator_throttling.email_throttle_window%'

    app.authenticator_throttling.email.api:
        class: Wizacha\AppBundle\Security\User\Throttling\AuthenticatorEmailThrottling
        public: true
        arguments:
            - '@app.redis.default'
            - '%authenticator_throttling.email_throttle_map.api%'
            - '%authenticator_throttling.email_throttle_window.api%'

    app.authenticator_throttling.ip:
        class: Wizacha\AppBundle\Security\User\Throttling\AuthenticatorIpThrottling
        public: true
        arguments:
            - '@app.redis.default'
            - '@event_dispatcher'
            - '%security.lock_ip_address.failed_limit%'
            - '%security.lock_ip_address.duration%'
            - '%authenticator_throttling.ip_throttle_window%'

    app.authenticator_throttling:
        class: Wizacha\AppBundle\Security\User\Throttling\AuthenticatorThrottlingService
        public: true
        autowire: true
        arguments:
            $authenticatorEmailThrottling: '@app.authenticator_throttling.email'
            $authenticatorEmailApiThrottling: '@app.authenticator_throttling.email.api'
            $authenticatorIpThrottling: '@app.authenticator_throttling.ip'
            $throttlingByIpEnabled: '%feature.security.lock_ip_address%'
            $throttlingByEmailApiEnabled: '%feature.security.lock_email_address.api%'

    Broadway\ReadModel\RepositoryInterface:
        public: true

    broadway_serialization.instantiator:
        class: Doctrine\Instantiator\Instantiator
        public: false

    broadway_serialization.hydrate:
        class: BroadwaySerialization\Hydration\HydrateUsingReflection
        public: false

    broadway_serialization.reconstitute:
        class: BroadwaySerialization\Reconstitution\ReconstituteUsingInstantiatorAndHydrator
        public: true
        arguments:
            - "@broadway_serialization.instantiator"
            - "@broadway_serialization.hydrate"

    Wizacha\Marketplace\Order\DirectDebitService:
        public: true

    Wizacha\Marketplace\Division\MarketplaceDivisionSettings:
        public: true

    Wizacha\Marketplace\Division\CompanyDivisionSettings:
        public: true

    Wizacha\Marketplace\Division\ProductDivisionSettings:
        public: true

    Symfony\Component\Translation\Dumper\XliffFileDumper:
        public: true

    Symfony\Component\Translation\Loader\XliffFileLoader:
        public: true

    Wizacha\AppBundle\Controller\Api\Shipments:
        public: true

    Wizacha\Component\Log\CommandLogService:
        public: true

    logger:
        alias: 'monolog.logger'
        public: true

    Tygh\Backend\Cache\File:
        public: true
        arguments:
            - '%kernel.project_dir%/var/cache/tygh'

    cscart.cache_backend:
        alias: Tygh\Backend\Cache\File

    assets.packages:
        class: Symfony\Component\Asset\Packages
        arguments: ['@assets._default_package']
        public: true

    fragment.handler:
        class: Symfony\Component\HttpKernel\DependencyInjection\LazyLoadingFragmentHandler
        public: true
        arguments:
            - '@container'
            - '@request_stack'
            - '%kernel.debug%'

    Broadway\UuidGenerator\Rfc4122\Version4Generator:
        public: true

    broadway.uuid.generator:
        alias: Broadway\UuidGenerator\Rfc4122\Version4Generator
        public: true

    Wizacha\Marketplace\Company\LegacyCompanyCache:
        public: true

    security.encoder_factory.public:
        alias: security.encoder_factory
        public: true

    basic.configuration_service:
        class: Wizacha\AppBundle\Service\ConfigurationService
        public: true
        arguments:
            - '@marketplace.theme_customizer'
            - '@marketplace.asset_manager'
            - '@app.setting_storage'
            -
    Wizacha\AppBundle\Controller\Api\TransactionTransferController:
        autoconfigure: true
        autowire: true

    Wizacha\FeatureFlag\FeatureFlagService:
        autowire: true
        public: true

    Wizacha\Marketplace\Order\AmountsCalculator\Service\OrderAmountsCalculator:
        public: true
        autowire: true
        arguments:
            - '%order_amounts.calculator.pivot_date%'
            - '@order_amount_repository'
            - '@marketplace.promotion.repository'
            - '@marketplace.order.order_service'
            - '@marketplace.commission.commission_service'
            - '@logger'
            - '@marketplace.international_tax.tax_repository'

    Wizacha\AppBundle\Controller\Api\ImageController:
        public: true
        autowire: true

    Wizacha\AppBundle\Controller\Api\GroupController:
        autowire: true
        autoconfigure: true
        arguments:
            - '@Wizacha\Marketplace\Group\UserGroupService'

    Wizacha\Marketplace\KPI\KPIPublishService:
        autowire: true
        arguments:
            $configKPIWebhookURL: '%config.kpi.webhook.url%'
            $marketplaceProjectName: '%marketplace.project_name%'
            $urlValidator: '@app.validator.url_validator'
            $processorService: '@Wizacha\Marketplace\Payment\Processor\ProcessorService'
