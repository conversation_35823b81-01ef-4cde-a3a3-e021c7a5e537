services:

    Ruler<PERSON>\Parser\Parser:
        public: true
        autowire: true

    ## FROM RULERZ SYMFONY INTEGRATION BUNDLE
    rulerz:
        class: RulerZ\RulerZ
        arguments: [ '@RulerZ\Compiler\Compiler', ['@RulerZ\Target\Native\Native', '@RulerZ\Target\DoctrineDBAL\DoctrineDBAL'] ]
        public: true

    RulerZ\Compiler\Compiler:
        arguments: [ "@rulerz.evaluator" ]
        public: true

    rulerz.evaluator:
        alias: rulerz.evaluator.file
        public: true

    rulerz.evaluator.file:
        class: RulerZ\Compiler\FileEvaluator
        public: true

    rulerz.parser:
        class: RulerZ\Parser\Parser
        public: true

    RulerZ\Target\DoctrineDBAL\DoctrineDBAL:
        public: true

    RulerZ\Target\Native\Native:
        public: true
        calls:
            - [defineOperator, ["intersects", "@rulerz.operator.array_intersect"]]

    rulerz.operator.array_intersect:
        class: Wizacha\Marketplace\Promotion\Rule\Operator\ArrayIntersects

    rulerz.datasource.object:
        class: Ruler<PERSON>\Visitor\ParameterCollectorVisitor
