path.home: /app/var/filebeat

logging.level: warning
logging.to_files: true
logging.to_syslog: false
logging.files:
  name: filebeat.log

filebeat.config:
  modules:
    enabled: false

filebeat.inputs:
- type: log
  paths:
    - /var/log/access.log
  fields:
    type: apache_access
    provider: platform.sh
    app.version: ${MARKETPLACE_VERSION}
    app.name: wizaplace
    client: ${PROJECT_NAME}
    wizaenv: ${WIZAENV:sandbox}
  tags: ["legacyfront", "back", "api", "${PROJECT_NAME}"]

- type: log
  paths:
    - /var/log/error.log
  fields:
    type: apache_error
    provider: platform.sh
    app.version: ${MARKETPLACE_VERSION}
    app.name: wizaplace
    client: ${PROJECT_NAME}
    wizaenv: ${WIZAENV:sandbox}
  tags: ["legacyfront", "back", "api", "${PROJECT_NAME}"]

- type: log
  paths:
    - /app/var/logs/prod.json
  json.keys_under_root: true
  fields:
    type: app
    provider: platform.sh
    app.version: ${MARKETPLACE_VERSION}
    app.name: wizaplace
    client: ${PROJECT_NAME}
    wizaenv: ${WIZAENV:sandbox}
  tags: ["legacyfront", "back", "api", "${PROJECT_NAME}"]

- type: log
  paths:
    - /var/log/deploy.log
  fields:
    type: deploy
    provider: platform.sh
    app.version: ${MARKETPLACE_VERSION}
    app.name: wizaplace
    client: ${PROJECT_NAME}
    wizaenv: ${WIZAENV:sandbox}
  tags: ["legacyfront", "back", "api", "${PROJECT_NAME}"]

- type: log
  paths:
    - /var/log/php.access.log
  fields:
    type: php_access
    provider: platform.sh
    app.version: ${MARKETPLACE_VERSION}
    app.name: wizaplace
    client: ${PROJECT_NAME}
    wizaenv: ${WIZAENV:sandbox}
  tags: ["legacyfront", "back", "api", "${PROJECT_NAME}"]

output.logstash:
  hosts: ["logs.wizacha.com:5044"]
