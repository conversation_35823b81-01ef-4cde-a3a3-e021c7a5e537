# Default Symfony parameters
parameters:
    marketplace.read_model.indexes.basket_short: basket_short
    broadway.saga.mongodb.storage_suffix: _prod

    cscart_table_prefix: cscart_
    doctrine_table_prefix: doctrine_
    broadway.event_store.dbal.table: cscart_broadway_events
    route_statistics_token: 3E7547EBCB6739AA965B19C356793
    route_system_token: 82F2BABAF3F177268F635A7172265

    marketplace.cookies_page_id: ''

    redis.url: '%redis_host%/%redis.db%'
    redis.default.prefix: default.
    redis.locks.prefix: locks.
    redis.handlers.prefix: handlers.
    redis.cscart_sessions.prefix: sessions.

    aws.video.maxsize: 52428800
    aws.video.preset: 1351620000001-000020
    aws.video.prefix: videos/
    aws.video.maxduration: 600
    aws.credentials.version: latest

    marketplace.queue.config:
        emails:
            priority: 100
        csv_line:
            priority: 25
        csv_entities:
            priority: 0
        csv_exports:
            priority: 0
        image_thumbnails:
            priority: 0
        cache:
            priority: 0
        search_engine:
            priority: 15
        videos:
            priority: 20
        products:
            priority: 60
        multi_vendor_products:
            priority: 30
        product_moderation:
            priority: 50
        feature_products:
            priority: 40
        readmodel:
            priority: 10
        feature_available_offers:
            priority: 20
        product_price_import:
            priority: 20
        product_stock_import:
            priority: 15
        translations_import:
            priority: 5
        user_group_import :
            priority : 0
        related_products_import:
            priority: 5

    marketplace.function_to_dispatch: []
    readonly_attributes: []

    regexp_guid: '[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}'

    image.serve_fake_images: false

    #Entrypoints
    entrypoint.api_url: "https://%http_host%/%http_path%"

    current_oauth_provider: '%env(CURRENT_OAUTH_PROVIDER)%'

    okta_authorization_server: '%env(OKTA_AUTHORIZATION_SERVER)%'
    okta_client_id: '%env(OKTA_CLIENT_ID)%'
    okta_client_secret: '%env(OKTA_CLIENT_SECRET)%'
    okta_redirect_uri: '%env(OKTA_REDIRECT_URI)%'
    okta_admin_group: '%env(OKTA_ADMIN_GROUP)%'
    okta_vendor_group: '%env(OKTA_VENDOR_GROUP)%'
    okta_response_type: '%env(OKTA_RESPONSE_TYPE)%'

    okta_bo_authorization_server: '%env(OKTA_BO_AUTHORIZATION_SERVER)%'
    okta_bo_client_id: '%env(OKTA_BO_CLIENT_ID)%'
    okta_bo_client_secret: '%env(OKTA_BO_CLIENT_SECRET)%'
    okta_bo_redirect_uri: '%env(OKTA_BO_REDIRECT_URI)%'
    okta_bo_admin_group: '%env(OKTA_BO_ADMIN_GROUP)%'
    okta_bo_vendor_group: '%env(OKTA_BO_VENDOR_GROUP)%'
    okta_bo_response_type: '%env(OKTA_BO_RESPONSE_TYPE)%'

    openid_discovery_uri: '%env(OPENID_DISCOVERY_URI)%'
    openid_client_id: '%env(OPENID_CLIENT_ID)%'
    openid_client_secret: '%env(OPENID_CLIENT_SECRET)%'
    openid_redirect_uri: '%env(OPENID_REDIRECT_URI)%'
    openid_default_user_type: '%env(OPENID_DEFAULT_USER_TYPE)%'

    openid_bo_discovery_uri: '%env(OPENID_BO_DISCOVERY_URI)%'
    openid_bo_client_id: '%env(OPENID_BO_CLIENT_ID)%'
    openid_bo_client_secret: '%env(OPENID_BO_CLIENT_SECRET)%'
    openid_bo_redirect_uri: '%env(OPENID_BO_REDIRECT_URI)%'
    openid_bo_default_user_type: '%env(OPENID_BO_DEFAULT_USER_TYPE)%'

    azure_ad_discovery_uri: '%env(AZURE_AD_DISCOVERY_URI)%'
    azure_ad_default_user_type: '%env(AZURE_AD_DEFAULT_USER_TYPE)%'
    azure_ad_client_id: '%env(AZURE_AD_CLIENT_ID)%'
    azure_ad_response_type: '%env(AZURE_AD_RESPONSE_TYPE)%'
    azure_ad_client_secret: '%env(AZURE_AD_CLIENT_SECRET)%'
    azure_ad_redirect_uri: '%env(AZURE_AD_REDIRECT_URI)%'

    azure_ad_bo_discovery_uri: '%env(AZURE_AD_BO_DISCOVERY_URI)%'
    azure_ad_bo_default_user_type: '%env(AZURE_AD_BO_DEFAULT_USER_TYPE)%'
    azure_ad_bo_client_id: '%env(AZURE_AD_BO_CLIENT_ID)%'
    azure_ad_bo_response_type: '%env(AZURE_AD_BO_RESPONSE_TYPE)%'
    azure_ad_bo_client_secret: '%env(AZURE_AD_BO_CLIENT_SECRET)%'
    azure_ad_bo_redirect_uri: '%env(AZURE_AD_BO_REDIRECT_URI)%'

    google_oauth_redirect_uri: '%env(GOOGLE_OAUTH_REDIRECT_URI)%'

    google_analytics_tracking_id: "%env(GOOGLE_ANALYTICS_TRACKING_ID)%"

    somfy_authorization_server: '%env(SOMFY_AUTHORIZATION_SERVER)%'

    # API basic authentication
    security.authentication.hide_user_not_found: false
    security.authentication.basic.realmname: 'Secured Area'

    config_product_templates:
        product: []
        service:
          hidden_fields:
              w_green_tax: '0.00'
              w_condition: 'N'
              amount: 0
              infinite_stock: 'Y'
              is_edp: 'Y'
              is_returnable: 'N'

    # Currency Codes ISO 4217
    regexp_currency_code: '^[a-zA-Z]{3}$'
    # Country Codes ISO 3166-1 alpha-2
    regexp_country_code: '^[a-zA-Z]{2}$'

    # Doc wizaplace
    url_wizaplace_documentation: 'https://docs.wizaplace.com/'

    # Time to wait after failed login
    authenticator_throttling.email_throttle_map:
        3: 60
        5: 120
        10: 180

    authenticator_throttling.email_throttle_map.api:
        5: 60
        10: 120
        15: 180

    # TTL for failed login attempt redis key
    # To reach the last email throttle map, it should be at least
    # (step1 * timeToWait1) + (step2 - step 1 * timeToWait2) + ...
    authenticator_throttling.email_throttle_window: 1320
    authenticator_throttling.email_throttle_window.api: 1320

    # One week
    authenticator_throttling.ip_throttle_window: 604800

    # Pivot Date for Legacy Order Calculation
    order_amounts.calculator.pivot_date: '20-07-2021 13:00:00'

    # Time of inactivity before logout in seconds
    session_cookie_lifetime: '%env(SESSION_COOKIE_LIFETIME)%'

    feature.approve_user_by_admin: '%env(bool:APPROVE_USER_BY_ADMIN)%'
    feature.carrier.mondial_relay: '%env(bool:ENABLE_CARRIER_MONDIAL_RELAY)%'
    feature.carrier.chronopost.chronorelais: '%env(bool:ENABLE_CARRIER_CHRONOPOST_CHRONORELAIS)%'
    feature.carrier.chronopost.chrono13: '%env(bool:ENABLE_CARRIER_CHRONOPOST_CHRONO13)%'

    feature.config_enable_auto_validate_product: '%env(bool:CONFIG_ENABLE_AUTO_VALIDATE_PRODUCT)%'

    session_backend: '%env(SESSION_BACKEND)%'
    cache_backend: '%env(CACHE_BACKEND)%'

    mailer_transport: '%env(MAILER_TRANSPORT)%'
    mailer_host: '%env(MAILER_HOST)%'
    mailer_port: '%env(MAILER_PORT)%'
    mailer_user: '%env(MAILER_USER)%'
    mailer_password: '%env(MAILER_PASSWORD)%'
    mailer_encryption: '%env(MAILER_ENCRYPTION)%'
    mailer_delivery_address: '%env(MAILER_DELIVERY_ADDRESS)%'
    mail_domain: '%env(MAIL_DOMAIN)%'

    marketplace.frontend_theme: '%env(FRONTEND_THEME)%'
    theme_bundle: '%env(ucfirst:FRONTEND_THEME)%Bundle'
    marketplace.version: '%env(MARKETPLACE_VERSION)%'
    cscart.crypt_key: '%env(CSCART_CRYPT_KEY)%'
    kernel.secret: '%env(CSCART_CRYPT_KEY)%'
    feature.sql_search: '%env(MARKETPLACE_SEARCH_FORCE_SQL)%'
    feature.global_option_allowed_for_vendors: '%env(bool:IS_GLOBAL_OPTION_ALLOWED_FOR_VENDORS)%'

    currency.sign: '%env(CURRENCY_SIGN)%'
    currency.code: '%env(CURRENCY_CODE)%'

    feature.currency.advanced: '%env(bool:FEATURE_CURRENCY_ADVANCED)%'

    currency.rates_provider: '%env(CURRENCY_RATES_PROVIDER)%'
    currency.fixer.api_key: '%env(CURRENCY_FIXER_API_KEY)%'
    currency.fixer.base_url: '%env(CURRENCY_FIXER_BASE_URL)%'

    system_api_user_password: '%env(SYSTEM_API_USER_PASSWORD)%'
    request_id.header_name: '%env(REQUEST_ID_HEADER_NAME)%'

    aws.transcoding.region: '%env(AWS_TRANSCODING_REGION)%'

    aws.video.pipeline: '%env(VIDEO_SYSTEM_PIPELINE)%'
    aws.video.temporary_bucket: '%env(VIDEO_SYSTEM_TEMPORARY_BUCKET)%'
    aws.s3.credentials.version: latest
    aws.s3.credentials.region: '%env(AWS_REGION)%'
    aws.s3.credentials.key: '%env(AWS_ACCESS_KEY_ID)%'
    aws.s3.credentials.secret: '%env(AWS_ACCESS_KEY_SECRET)%'
    aws.bucket: '%env(AWS_BUCKET)%'

    aws.sqs.credentials.version: latest
    aws.sqs.credentials.region: '%env(AWS_SQS_REGION)%'
    aws.sqs.credentials.key: '%env(AWS_SQS_ACCESS_KEY_ID)%'
    aws.sqs.credentials.secret: '%env(AWS_SQS_ACCESS_KEY_SECRET)%'

    ovh.s3.api_url: '%env(OVH_S3_API_URL)%'
    ovh.s3.region: '%env(OVH_S3_REGION)%'
    ovh.s3.credentials.key: '%env(OVH_S3_CREDENTIALS_KEY)%'
    ovh.s3.credentials.secret: '%env(OVH_S3_CREDENTIALS_SECRET)%'
    ovh.s3.public_bucket: '%env(OVH_S3_PUBLIC_BUCKET)%'
    ovh.s3.private_bucket: '%env(OVH_S3_PRIVATE_BUCKET)%'
    ovh.s3.public_url_token: '%env(OVH_S3_PUBLIC_URL_TOKEN)%'

    database_host: '%env(DATABASE_HOST)%'
    database_port: '%env(DATABASE_PORT)%'
    database_name: '%env(DATABASE_NAME)%'
    database_name_tests: '%env(DATABASE_NAME)%'
    database_user: '%env(DATABASE_USER)%'
    database_password: '%env(DATABASE_PASSWORD)%'

    discuss.database_driver: '%env(DISCUSS_DRIVER)%'
    discuss.database_user: '%env(DISCUSS_USER)%'
    discuss.database_password: '%env(DISCUSS_PASSWORD)%'
    discuss.database_dbname: '%env(DISCUSS_DBNAME)%'
    discuss.database_host: '%env(DISCUSS_HOST)%'
    discuss.database_port: '%env(DISCUSS_PORT)%'

    redis_host: '%env(REDIS_HOST)%'
    redis.db: '%env(REDIS_DB)%'

    mangopay_api.client_id: '%env(MANGOPAY_API_CLIENT_ID)%'
    mangopay_api.client_password: '%env(MANGOPAY_API_CLIENT_PASSWORD)%'
    mangopay_api.base_url: '%env(MANGOPAY_API_BASE_URL)%'
    mangopay_api.secure_mode: '%env(MANGOPAY_API_SECURE_MODE)%'
    mangopay_user_id_agent_etablissement_paiement: '%env(MANGOPAY_USER_ID_AGENT_ETABLISSEMENT_PAIEMENT)%'

    stripe.secret_token: '%env(STRIPE_SECRET_TOKEN)%'
    stripe.public_token: '%env(STRIPE_PUBLIC_TOKEN)%'

    smoney.token: '%env(SMONEY_TOKEN)%'
    smoney.api_base_url: '%env(SMONEY_API_BASE_URL)%'
    smoney.website_name: '%env(SMONEY_WEBSITE_NAME)%'

    lemonway_api.client_login: '%env(LEMONWAY_API_CLIENT_LOGIN)%'
    lemonway_api.client_password: '%env(LEMONWAY_API_CLIENT_PASSWORD)%'
    lemonway_api.directkit_url: '%env(LEMONWAY_API_DIRECTKIT_URL)%'
    lemonway_api.webkit_url: '%env(LEMONWAY_API_WEBKIT_URL)%'
    lemonway_api.marketplace_id: '%env(LEMONWAY_API_MARKETPLACE_ID)%'
    lemonway_api.use_buyer_wallet_for_moneyin: '%env(bool:LEMONWAY_API_USE_BUYER_WALLET_FOR_MONEYIN)%'
    lemonway_api.tech_wallet_id: '%env(LEMONWAY_API_TECH_WALLET_ID)%'
    lemonway_api.marketplace_discount_wallet: '%env(LEMONWAY_API_MARKETPLACE_DISCOUNT_WALLET)%'
    lemonway_proxy.host: '%env(LEMONWAY_PROXY_HOST)%'
    lemonway_proxy.port: '%env(int:LEMONWAY_PROXY_PORT)%'
    lemonway_proxy.user: '%env(LEMONWAY_PROXY_USER)%'
    lemonway_proxy.password: '%env(LEMONWAY_PROXY_PASSWORD)%'
    lemonway_bankwire.iban: '%env(LEMONWAY_BANKWIRE_IBAN)%'
    lemonway_bankwire.bic: '%env(LEMONWAY_BANKWIRE_BIC)%'
    lemonway_bankwire.holder_name: '%env(LEMONWAY_BANKWIRE_HOLDER_NAME)%'
    lemonway_bankwire.holder_address: '%env(LEMONWAY_BANKWIRE_HOLDER_ADDRESS)%'

    hipay.cashin.api_username: '%env(HIPAY_CASHIN_API_USERNAME)%'
    hipay.cashin.api_password: '%env(HIPAY_CASHIN_API_PASSWORD)%'
    hipay.cashout.api_uri: '%env(HIPAY_CASHOUT_API_URI)%'
    hipay.cashout.api_login: '%env(HIPAY_CASHOUT_API_LOGIN)%'
    hipay.cashout.api_password: '%env(HIPAY_CASHOUT_API_PASSWORD)%'
    hipay.cashout.entity: '%env(HIPAY_CASHOUT_ENTITY)%'
    hipay.cashout.merchant_group_id: '%env(int:HIPAY_CASHOUT_MERCHANT_GROUP_ID)%'
    hipay.commission_recipient_id: '%env(HIPAY_COMMISSION_RECIPIENT_ID)%'
    hipay.cashin.env: '%env(HIPAY_CASHIN_ENV)%'
    hipay.payment_product_list: '%env(HIPAY_PAYMENT_PRODUCT_LIST)%'
    #    hipay.css: @see src/AppBundle/DependencyInjection/AppExtension::load

    dolist.account_id: '%env(DOLIST_ACCOUNT_ID)%'
    dolist.authentication_key: '%env(DOLIST_AUTHENTICATION_KEY)%'
    dolist.debug: '%env(bool:DOLIST_DEBUG)%'

    googlemaps.api_key: '%env(GOOGLEMAPS_API_KEY)%'

    chronopost.api.account_number: '%env(CHRONOPOST_API_ACCOUNT_NUMBER)%'
    chronopost.api.password: '%env(CHRONOPOST_API_PASSWORD)%'

    mondial_relay.api.userid: '%env(MONDIAL_RELAY_API_USERID)%'
    mondial_relay.api.password: '%env(MONDIAL_RELAY_API_PASSWORD)%'
    mondial_relay.api.endpoint: '%env(MONDIAL_RELAY_API_ENDPOINT)%'

    entrypoint.administrator: '%env(ENTRYPOINT_ADMINISTRATOR)%'
    entrypoint.vendor: '%env(ENTRYPOINT_VENDOR)%'
    entrypoint.vendor_domain: '%env(ENTRYPOINT_VENDOR_DOMAIN)%'
    entrypoint.marketplace_domain: '%env(ENTRYPOINT_MARKETPLACE_DOMAIN)%'
    entrypoint.marketplace: '%env(ENTRYPOINT_MARKETPLACE)%'

    http_host: '%env(HTTP_HOST)%'
    http_path: '%env(HTTP_PATH)%'

    external_backoffice_url: '%env(EXTERNAL_BACKOFFICE_URL)%'

    google_oauth_id: '%env(GOOGLE_OAUTH_ID)%'
    google_oauth_secret: '%env(GOOGLE_OAUTH_SECRET)%'
    google_oauth_redirect_url: '%env(GOOGLE_OAUTH_REDIRECT_URL)%'

    locale: '%env(APP_LOCALE)%'
    external_front_office_url: '%env(EXTERNAL_FRONT_OFFICE_URL)%'
    storage_system: '%env(STORAGE_SYSTEM)%'

    queue.type: '%env(QUEUE_TYPE)%'
    queue.path: '%env(QUEUE_PATH)%'
    queue.region: '%env(QUEUE_REGION)%'
    queue.key: '%env(QUEUE_KEY)%'
    queue.secret: '%env(QUEUE_SECRET)%'
    amqp.host: '%env(AMQP_HOST)%'
    amqp.port: '%env(AMQP_PORT)%'
    amqp.user: '%env(AMQP_USER)%'
    amqp.pass: '%env(AMQP_PASS)%'
    amqp.vhost: '%env(AMQP_VHOST)%'

    mysql_version: '%env(MYSQL_VERSION)%'

    recaptcha.private_key: '%env(RECAPTCHA_PRIVATE_KEY)%'
    recaptcha.public_key: '%env(RECAPTCHA_PUBLIC_KEY)%'

    feature.prediggo_exporter: '%env(bool:PREDIGGO_ENABLE_EXPORTER)%'

    algolia.api_identifier: '%env(ALGOLIA_API_IDENTIFIER)%'
    algolia.api_index_prefix: '%env(ALGOLIA_API_INDEX_PREFIX)%'
    algolia.api_key_full_rights: '%env(ALGOLIA_API_KEY_FULL_RIGHTS)%'

    algolia.geocoding.api_identifier: '%env(ALGOLIA_GEOCODING_API_IDENTIFIER)%'
    algolia.geocoding.api_key_limited_rights: '%env(ALGOLIA_GEOCODING_API_KEY_LIMITED_RIGHTS)%'

    algolia.config.max_values_per_facet: '%env(int:ALGOLIA_CONFIG_MAX_VALUES_PER_FACET)%'

    algolia.enable_command_update_post_deploy: '%env(bool:ENABLE_POST_DEPLOY_UPDATE_ALGOLIA)%'

    azure.account_name: '%env(AZURE_ACCOUNT_NAME)%'
    azure.account_key: '%env(AZURE_ACCOUNT_KEY)%'
    azure.protocol: '%env(AZURE_PROTOCOL)%'
    azure.local_endpoints: '%env(AZURE_LOCAL_ENDPOINTS)%'

    marketplace.project_name: '%env(PROJECT_NAME)%'

    wkhtmltopdf_binary_path: '%env(WKHTMLTOPDF_BINARY_PATH)%'

    feature.sso_connection_only_bo: '%env(bool:SSO_CONNECTION_ONLY_BO)%'

    marketplace.invoice.template_url: '%env(INVOICE_TEMPLATE_URL)%'
    marketplace.invoice.header.template_url: '%env(INVOICE_TEMPLATE_HEADER_URL)%'
    marketplace.invoice.footer.template_url: '%env(INVOICE_TEMPLATE_FOOTER_URL)%'
    marketplace.invoice.options.margin.top: '%env(INVOICE_TEMPLATE_OPTIONS_MARGIN_TOP)%'
    marketplace.invoice.options.margin.bottom: '%env(INVOICE_TEMPLATE_OPTIONS_MARGIN_BOTTOM)%'

    marketplace.rma.template_url: '%env(RMA_TEMPLATE_URL)%'
    marketplace.rma.header.template_url: '%env(RMA_TEMPLATE_HEADER_URL)%'
    marketplace.rma.footer.template_url: '%env(RMA_TEMPLATE_FOOTER_URL)%'
    marketplace.rma.options.margin.top: '%env(RMA_TEMPLATE_OPTIONS_MARGIN_TOP)%'
    marketplace.rma.options.margin.bottom: '%env(RMA_TEMPLATE_OPTIONS_MARGIN_BOTTOM)%'

    marketplace.pim.product_templates: '%env(PRODUCT_TEMPLATES)%'

    feature.payment_deadline: '%env(PAYMENT_DEADLINE)%'
    feature.notify_shipment_created: '%env(bool:NOTIFY_SHIPMENT_CREATED)%'
    feature.enable_divisions: '%env(bool:FEATURE_ENABLE_DIVISIONS)%'
    feature.available_offers: '%env(bool:AVAILABLE_OFFERS)%'
    feature.green_tax_is_enabled: '%env(bool:ENABLE_GREEN_TAXE)%'
    feature.order_adjustment: '%env(bool:ORDER_ADJUSTMENT)%'
    feature.marketplace_discounts: '%env(bool:MARKETPLACE_DISCOUNTS)%'
    feature.marketplace_discount_optional_bonuses: '%env(bool:MARKETPLACE_DISCOUNT_OPTIONAL_BONUSES)%'
    feature.delivery_period: '%env(int:DELIVERY_PERIOD)%'
    feature.withdrawal_period: '%env(int:WITHDRAWAL_PERIOD)%'
    feature.tier_pricing: '%env(bool:TIER_PRICING)%'
    feature.refund_auto: '%env(REFUND_AUTO)%'
    feature.merchants_refund_ability: '%env(MERCHANTS_REFUND_ABILITY)%'
    feature.hide_client_email_from_vendor: '%env(bool:HIDE_CLIENT_EMAIL_FROM_VENDOR)%'
    clear_cache_auth_delay_month: '%env(int:CLEAR_CACHE_AUTH_DELAY_MONTH)%'
    feature.subscription: '%env(bool:SUBSCRIPTION)%'
    feature.subscription.limit_renew_attempts: '%env(int:FEATURE_SUBSCRIPTION_LIMIT_RENEW_ATTEMPTS)%'
    config.enabled_system_options: '%env(CONFIG_ENABLED_SYSTEM_OPTIONS)%'

    support_login.groups: '%env(SUPPORT_LOGIN_GROUPS)%'

    # CloudImage
    feature.cloudimage: '%env(CLOUDIMAGE)%'
    cloudimage_client_key: '%env(CLOUDIMAGE_CLIENT_KEY)%'
    cloudimage_api_url: '%env(CLOUDIMAGE_API_URL)%'
    cloudimage_version_head: '%env(CLOUDIMAGE_VERSION_HEAD)%'

    resources_purge_period.images: '%env(RESOURCES_PURGE_PERIOD_IMAGES)%'
    config.algolia_fpu_max_fp: '%env(int:ALGOLIA_FPU_MAX_FP)%'
    monolog.log_level.sentinel: '%env(MONOLOG_LOG_LEVEL_SENTINEL)%'
    config.frontend_callback_whitelist: '%env(FRONTEND_CALLBACK_WHITELIST)%'
    feature.security.enable_strict_password: '%env(bool:SECURITY_ENABLE_STRICT_PASSWORD)%'
    feature.security.request_old_password_on_change: '%env(bool:SECURITY_REQUEST_OLD_PASSWORD_ON_CHANGE)%'
    feature.security.xss_filter: '%env(bool:SECURITY_XSS_FILTER)%'
    feature.security.lock_user_accounts: '%env(bool:SECURITY_LOCK_USER_ACCOUNTS)%'
    security.lock_user_accounts.max_attempts: '%env(int:SECURITY_LOCK_USER_ACCOUNTS_FAILED_LIMIT)%'
    feature.security.lock_ip_address: '%env(bool:SECURITY_LOCK_IP_ADDRESS)%'
    security.lock_ip_address.failed_limit: '%env(int:SECURITY_LOCK_IP_ADDRESS_FAILED_LIMIT)%'
    security.lock_ip_address.duration: '%env(int:SECURITY_LOCK_IP_ADDRESS_DURATION)%'
    feature.security.lock_email_address.api: '%env(bool:SECURITY_LOCK_EMAIL_API)%'
    config.security.trusted_proxies: '%env(CLIENT_TRUSTED_PROXIES)%'
    monolog.log_level.mailer: '%env(string:MONOLOG_LOG_LEVEL_MAILER)%'
    yavin_messenger_transport_dsn: '%env(YAVIN_MESSENGER_TRANSPORT_DSN)%'
    yavin_messenger_vhost_test: '%env(YAVIN_MESSENGER_VHOST_TEST)%'
    feature.iban_validation: '%env(bool:FEATURE_IBAN_VALIDATION)%'
    feature.security.captcha: '%env(bool:FEATURE_SECURITY_CAPTCHA)%'
    frequency_cron_payout: '%env(FREQUENCY_CRON_PAYOUT)%'
    feature.enable_image_resizing: '%env(bool:FEATURE_ENABLE_IMAGE_RESIZING)%'
    feature.user_groups_enabled: '%env(bool:USER_GROUPS_ENABLED)%'
    config.kpi.webhook.url: '%env(CONFIG_KPI_WEBHOOK_URL)%'
    inactivity_time_out: '%env(int:INACTIVITY_TIME_OUT)%'
    feature.notify_user_update: '%env(bool:NOTIFY_USER_UPDATE)%'
    config.related_products.types: '%env(CONFIG_RELATED_PRODUCTS_TYPES)%'
    previous_passwords_difference_limit: '%env(int:PREVIOUS_PASSWORDS_DIFFERENCE_LIMIT)%'
    feature.password_recovery_force_change: '%env(bool:PASSWORD_RECOVERY_FORCE_CHANGE)%'
    password_renewal_time_limit: '%env(int:PASSWORD_RENEWAL_TIME_LIMIT)%'
    feature.catalog.show_zero_price_products: '%env(bool:FEATURE_SHOW_ZERO_PRICE_PRODUCTS)%'
    config.tax_mode: '%env(string:TAX_MODE)%'
    feature.catalog.show_out_of_stock_products: '%env(bool:CATALOG_SHOW_OUT_OF_STOCK_PRODUCTS)%'
    config.prismic_content: '%env(string:PRISMIC_CONTENT)%'
    config.bfo_url: '%env(string:BFO_URL)%'
    feature.enable_bfo: '%env(bool:ENABLE_BFO)%'
    feature.enable_yavin: '%env(bool:ENABLE_YAVIN)%'
    yavin.uri: '%env(YAVIN_URI)%'
    yavin_api_v2_jwt: '%env(YAVIN_API_V2_JWT)%'
    feature.discussions_allowed_for_admin: '%env(bool:DISCUSSIONS_ALLOWED_FOR_ADMINISTRATORS)%'

    # Platform technical name
    config.platform_technical_name: '%env(PLATFORM_TECHNICAL_NAME)%'

    # Hades config
    config.hades.s3.region: '%env(string:HADES_AWS_REGION)%'
    config.hades.s3.bucket: '%env(string:HADES_AWS_BUCKET)%'
    config.hades.s3.key: '%env(string:HADES_AWS_KEY)%'
    config.hades.s3.secret: '%env(string:HADES_AWS_SECRET)%'
    feature.hipay_chargeback_test_env: '%env(HIPAY_CHARGEBACK_TEST_ENV)%'

    # Dump Anonymizer
    config.dump_anonymizer.output.s3.directory: '%env(string:DUMP_ANONYMIZER_OUTPUT_S3_DIRECTORY)%'
    config.dump_anonymizer.output.s3.region: '%env(string:DUMP_ANONYMIZER_OUTPUT_S3_REGION)%'
    config.dump_anonymizer.output.s3.bucket: '%env(string:DUMP_ANONYMIZER_OUTPUT_S3_BUCKET)%'
    config.dump_anonymizer.output.s3.key: '%env(string:DUMP_ANONYMIZER_OUTPUT_S3_KEY)%'
    config.dump_anonymizer.output.s3.secret: '%env(string:DUMP_ANONYMIZER_OUTPUT_S3_SECRET)%'

    feature.enable_automated_feeds: '%env(bool:ENABLE_AUTOMATED_FEEDS)%'
    feature.enable_database_limit: '%env(bool:ENABLE_DATABASE_LIMIT)%'
    config.enable_database_limit_size: '%env(int:ENABLE_DATABASE_LIMIT_SIZE)%'
    feature.quote_requests: '%env(bool:QUOTE_REQUESTS_ENABLED)%'

    # Stock reservation time limit in cart
    marketplace.stock.cart_time_limit: '%env(int:MARKETPLACE_STOCK_CART_TIME_LIMIT)%'

    # Global token for the whole API
    # This authentication is disabled if the token is null or empty
    api_application_token: '%env(API_APPLICATION_TOKEN)%'

    feature.application_token_for_public_routes: '%env(API_SEARCH_APPLICATION_TOKEN_REQUIRED)%'

    # si activé on considère qu'on vend QUE des services
    feature.marketplace_only_sell_services: '%env(bool:FEATURE_MARKETPLACE_ONLY_SELL_SERVICES)%'

    feature.multi_vendor_product: '%env(bool:ENABLE_MVP)%'
    feature.multi_vendor_product_auto_create_if_no_match: '%env(bool:MVP_AUTO_CREATE)%'
    feature.enable_c2c: '%env(bool:ENABLE_C2C)%'
    feature.sandbox: '%env(bool:SANDBOX)%'

    # Comptabilisation des frais de ports dans la commission
    feature.commission.include_shipping_in_commission: '%env(bool:INCLUDE_SHIPPING_IN_COMMISSION)%'
    feature.commission.add_full_shipping_to_commission: '%env(bool:ADD_FULL_SHIPPING_TO_COMMISSION)%'

    # Activation des pièces jointes sur les produits
    feature.enable_product_attachments: '%env(bool:ENABLE_PRODUCT_ATTACHMENTS)%'
    feature.professional_clients: '%env(bool:ARE_CLIENTS_PROFESSIONALS)%'
    feature.create_legal_wallets_for_customers: '%env(bool:CREATE_LEGAL_WALLETS_FOR_CUSTOMERS)%'
    feature.activate_billing_number_auto_generation: '%env(bool:ACTIVATE_BILLING_NUMBER_AUTO_GENERATOR)%'
    feature.activate_workflow_translation: '%env(bool:ACTIVATE_WORKFLOW_TRANSLATION)%'
    feature.allow_mvp_sync_from_products: '%env(bool:ALLOW_MVP_SYNC_FROM_PRODUCTS)%'
    feature.organisations_enabled: '%env(bool:ORGANISATIONS_ENABLED)%'
    feature.multi_vendor_product.rules: '%env(MULTI_VENDOR_PRODUCT_RULES)%'
    marketplace.transaction_mode.affiliate: '%env(bool:ENABLE_AFFILIATE)%'

    # HTTP Cache Feature Flag
    feature.cache_http: '%env(bool:FEATURE_CACHE_HTTP)%'

    # click&collect
    click_and_collect_shipping_id: '%env(int:CLICK_AND_COLLECT_SHIPPING_ID)%'

