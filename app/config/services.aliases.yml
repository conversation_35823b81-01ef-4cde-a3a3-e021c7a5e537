#--------------
# This file only contains service aliases
# A service itself is declared in services declaration files (such as services.marketplace.yml, services.yml, etc.)
#--------------

services:
    _defaults:
        public: true

    #-------------------------
    #-------------------------
    # services.marketplace.yml
    #-------------------------
    #-------------------------

    marketplace.product.productservice:
        alias: Wizacha\Marketplace\Catalog\Product\ProductService

    marketplace.order.order_service:
        alias: Wizacha\Marketplace\Order\OrderService

    marketplace.order.order_details_service:
        alias: Wizacha\Marketplace\Order\OrderDetailsService

    marketplace.division.service:
        alias: Wizacha\Marketplace\Division\Service\DivisionService

    marketplace.division.blacklists.service:
        alias: Wizacha\Marketplace\Division\Service\DivisionBlacklistsService

    marketplace.promotion.promotionservice:
        alias: Wizacha\Marketplace\Promotion\PromotionService

    marketplace.promotion.marketplace_promotion_validator:
        alias: Wizacha\AppBundle\Controller\Api\Promotion\MarketplacePromotionValidator

    marketplace.divisions_settings.service:
        alias: Wizacha\Marketplace\Division\Service\DivisionSettingsService

    marketplace.import.import_service:
        alias: Wizacha\Component\Import\ImportService

    marketplace.import.translation_importer:
        alias: Wizacha\Exim\Import\TranslationImporter

    marketplace.mondial_relay.client:
        alias: Wizacha\Component\MondialRelay\Client

    marketplace.import.import_factory:
        alias: Wizacha\Component\Import\ImportFactory

    marketplace.import.job_service:
        alias: Wizacha\Component\Import\EximJobService
        public: true

    marketplace.order.refund.refund_service:
        alias: Wizacha\Marketplace\Order\Refund\Service\RefundService
        public: true

    marketplace.order.refund.refund_notification_service:
        alias: Wizacha\Marketplace\Order\Refund\Service\RefundNotificationService
        public: true

    marketplace.order.credit_note.credit_note_service:
        alias: Wizacha\Marketplace\Order\CreditNote\CreditNoteService
        public: true

    marketplace.order.credit_note.credit_note_helper:
        alias: Wizacha\Marketplace\Order\CreditNote\CreditNoteHelper

    marketplace.transaction.transaction_service:
        alias: Wizacha\Marketplace\Transaction\TransactionService
        public: true

    marketplace.multi_vendor_product.service:
        alias: Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService

    marketplace.payment.hipay:
        alias: Wizacha\Marketplace\Payment\Processor\HiPay

    marketplace.payment.lemonway:
        alias: Wizacha\Marketplace\Payment\Processor\LemonWay

    marketplace.payment.processor.lemonway_sepa_direct:
        alias: Wizacha\Marketplace\Payment\LemonWay\LemonWaySepaDirectProcessor

    marketplace.payment.processor.lemonway_sepa_deferment:
        alias: Wizacha\Marketplace\Payment\LemonWay\LemonWaySepaDefermentProcessor

    marketplace.payment.repository.user_payment_info:
        alias: Wizacha\Marketplace\Payment\Repository\UserPaymentInfoRepository

    marketplace.payment.repository.user_mandate:
        alias: Wizacha\Marketplace\Payment\Repository\UserMandateRepository

    marketplace.payment.mandate_service:
        alias: Wizacha\Marketplace\Payment\MandateService

    marketplace.payment.payout_service:
        alias: Wizacha\Marketplace\Payment\Processor\PayoutService
        public: true

    marketplace.pim.product.service:
        alias: Wizacha\Marketplace\PIM\Product\ProductService

    marketplace.division.products.service:
        alias: Wizacha\Marketplace\Division\Service\DivisionProductsService
        public: true

    marketplace.product.repository.read_model:
        alias: Wizacha\Marketplace\ReadModel\ProductRepository

    marketplace.catalog.company_service:
        alias: Wizacha\Marketplace\Catalog\Company\CompanyService

    marketplace.multi_vendor_product.product_synchronization.rules_service:
        alias: Wizacha\Marketplace\PIM\MultiVendorProduct\ProductSynchronization\RulesService

    marketplace.price_tier.price_tier_repository:
        alias: Wizacha\Marketplace\PriceTier\Repository\PriceTierRepository

    marketplace.price_tier.product_option_inventory_repository:
        alias: Wizacha\Marketplace\ProductOptionInventory\Repository\ProductOptionInventoryRepository

    marketplace.order.refund.refund_repository:
        alias: Wizacha\Marketplace\Order\Refund\Repository\RefundRepository

    marketplace.order.refund.refund_checker:
        alias: Wizacha\Marketplace\Order\Refund\Checker\RefundChecker

    marketplace.order.tracer:
        alias: Wizacha\Marketplace\Order\Tracer\Tracer

    marketplace.order.workflow_service:
        alias: Wizacha\Marketplace\Order\Workflow\WorkflowService

    marketplace.order.refund.utils.creator:
        alias: Wizacha\Marketplace\Order\Refund\Utils\RefundCreator

    marketplace.product.projector:
        alias: Wizacha\Marketplace\ReadModel\ProductProjector

    marketplace.async_dispatcher:
        alias: Wizacha\Async\Dispatcher

    marketplace.price_tier.product_option_inventory_service:
        alias: Wizacha\Marketplace\ProductOptionInventory\Service\ProductOptionInventoryService

    marketplace.authlog.service:
        alias: Wizacha\Component\AuthLog\AuthLogService

    marketplace.promotion.promotion_usage.repository:
        alias: Wizacha\Marketplace\Promotion\PromotionUsage\PromotionUsageRepository

    marketplace.division.division_repository:
        alias: Wizacha\Marketplace\Division\Repository\DivisionRepository

    marketplace.order.refund.utils.executor:
        alias: Wizacha\Marketplace\Order\Refund\Utils\RefundExecutor

    marketplace.commission.commission_service:
        alias: Wizacha\Marketplace\Commission\CommissionService

    order_amounts_commission_repository:
        alias: Wizacha\Marketplace\Order\AmountsCalculator\Repository\OrderAmountsCommissionRepository
        public: true

    marketplace.payment.provider:
        alias: Wizacha\Marketplace\Payment\Processor\PaymentProcessorProvider

    marketplace.order.refund.refund_config:
        alias: Wizacha\Marketplace\Order\Refund\Utils\RefundConfig

    marketplace.order.refund.refund_amounts_calculator:
        alias: Wizacha\Marketplace\Order\Refund\Utils\RefundAmountsCalculator

    marketplace.order.refund.refund_payment:
        alias: Wizacha\Marketplace\Order\Refund\Utils\RefundPayment

    marketplace.order.refund.mp_discount_refund_checker:
        alias: Wizacha\Marketplace\Order\Refund\Checker\MarketplaceDiscountRefundChecker

    marketplace.order.adjustment_repository:
        alias: Wizacha\Marketplace\Order\Adjustment\OrderAdjustmentRepository
        public: true

    marketplace.transaction_mode.service:
        alias: Wizacha\Marketplace\PIM\TransactionMode\TransactionModeService

    marketplace.payment.payment_service:
        alias: Wizacha\Marketplace\Payment\PaymentService
        public: true

    marketplace.stock.domain_service:
        alias: Wizacha\Marketplace\PIM\Stock\StockService

    marketplace.basket.domain_service:
        alias: Wizacha\Marketplace\Basket\BasketService

    marketplace.order.order_attachment.repository:
        alias: Wizacha\Marketplace\Order\OrderAttachment\Repository\OrderAttachmentRepository

    marketplace.seo.seo_service:
        alias: Wizacha\Marketplace\Seo\SeoService

    marketplace.seo.seo_repository:
        alias: Wizacha\Marketplace\Seo\SeoRepository

    marketplace.order_return.service:
        alias: Wizacha\Marketplace\Order\OrderReturn\OrderReturnService

    marketplace.debouncer.service:
        alias: Wizacha\Async\Debouncer\DebouncerService

    marketplace.authlog.repository:
        alias: Wizacha\Component\AuthLog\AuthLogRepository

    marketplace.basket.checkout:
        public: true
        alias: Wizacha\Marketplace\Basket\Checkout

    marketplace.price_tier.price_tier_service:
        alias: Wizacha\Marketplace\PriceTier\Service\PriceTierService

    marketplace.order.adjustment_service:
        alias: Wizacha\Marketplace\Order\Adjustment\OrderAdjustmentService

    marketplace.user.address_service:
        alias: Wizacha\Marketplace\User\AddressBookService
        public: true

    marketplace.user.user_repository:
        alias: Wizacha\Marketplace\User\UserRepository

    marketplace.payment.user_payment_info_service:
        alias: Wizacha\Marketplace\Payment\UserPaymentInfoService

    marketplace.user.user_service:
        alias: Wizacha\Marketplace\User\UserService

    marketplace.user.user_security:
        alias: Wizacha\Marketplace\User\UserSecurity

    marketplace.admin_company:
        alias: Wizacha\Marketplace\AdminCompany

    marketplace.basket.repository.read_model:
        alias: Wizacha\Bridge\Broadway\DatabaseRepository

    app.notification.user_notifier:
        alias: Wizacha\AppBundle\Notification\UserNotifier

    marketplace.order.order_action.order_action_service:
        alias: Wizacha\Marketplace\Order\OrderAction\Service\OrderActionService

    marketplace.order.order_action.order_action_repository:
        alias: Wizacha\Marketplace\Order\OrderAction\Repository\OrderActionRepository

    marketplace.related_product.related_product_service:
        alias: Wizacha\Marketplace\RelatedProduct\RelatedProductService

    marketplace.related_product.config_service:
        alias: Wizacha\Marketplace\RelatedProduct\ConfigService

    marketplace.related_product.related_product_repository:
        alias: Wizacha\Marketplace\RelatedProduct\RelatedProductRepository

    marketplace.international_tax.tax_repository:
        alias: Wizacha\Marketplace\Tax\Repository\TaxRepository

    marketplace.international_tax.shipping:
        alias: Wizacha\Marketplace\Tax\InternationalTaxService

    #--------------
    # Order Actions
    #--------------

    marketplace.order.action.confirm:
        alias: Wizacha\Marketplace\Order\Action\Confirm

    marketplace.order.action.accept:
        alias: Wizacha\Marketplace\Order\Action\Accept

    marketplace.order.action.refuse:
        alias: Wizacha\Marketplace\Order\Action\Refuse

    marketplace.order.action.mark_payment_deferment_as_authorized:
        alias: Wizacha\Marketplace\Order\Action\MarkPaymentDefermentAsAuthorized
        public: true

    marketplace.order.action.declare_parcel_lost:
        alias: Wizacha\Marketplace\Order\Action\DeclareParcelLost

    marketplace.order.action.mark_payment_deferment_as_refused:
        alias: Wizacha\Marketplace\Order\Action\MarkPaymentDefermentAsRefused
        public: true

    marketplace.order.action.mark_as_delivered:
        alias: Wizacha\Marketplace\Order\Action\MarkAsDelivered
        public: true

    marketplace.order.action.dispatch_funds:
        alias: Wizacha\Marketplace\Order\Action\DispatchFunds
        public: true

    marketplace.order.action.dispatch_funds_failed:
        alias: Wizacha\Marketplace\Order\Action\DispatchFundsFailed

    marketplace.order.action.provide_invoice_number:
        alias: Wizacha\Marketplace\Order\Action\ProvideInvoiceNumber

    marketplace.order.action.declare_invoice_number_generated_elsewhere:
        alias: Wizacha\Marketplace\Order\Action\DeclareInvoiceNumberGeneratedElsewhere

    marketplace.order.action.redirect_to_payment_processor:
        alias: Wizacha\Marketplace\Order\Action\RedirectToPaymentProcessor
        public: true

    marketplace.order.action.mark_payment_as_refused:
        alias: Wizacha\Marketplace\Order\Action\MarkPaymentAsRefused

    marketplace.order.action.end_withdrawal_period:
        alias: Wizacha\Marketplace\Order\Action\EndWithdrawalPeriod
        public: true

    marketplace.order.action.mark_as_shipped:
        alias: Wizacha\Marketplace\Order\Action\MarkAsShipped

    marketplace.order.action.mark_as_paid:
        alias: Wizacha\Marketplace\Order\Action\MarkAsPaid

    marketplace.order.action.mark_as_finished:
        alias: Wizacha\Marketplace\Order\Action\MarkAsFinished

    marketplace.order.action.trash:
        alias: Wizacha\Marketplace\Order\Action\Trash

    marketplace.order.action.dispatch_funds_succeeded:
        alias: Wizacha\Marketplace\Order\Action\DispatchFundsSucceeded

    marketplace.order.action.cancel:
        alias: Wizacha\Marketplace\Order\Action\Cancel

    marketplace.order.action.commit_to:
        alias: Wizacha\Marketplace\Order\Action\CommitTo

    marketplace.order.action.mark_payment_authorization_captured:
        alias: Wizacha\Marketplace\Order\Action\MarkPaymentAuthorizationAsCaptured

    marketplace.order.action.mark_payment_authorization_refused:
        alias: Wizacha\Marketplace\Order\Action\MarkPaymentAuthorizationAsRefused

    marketplace.order.action.transfer_marketplace_discount_failed:
        alias: Wizacha\Marketplace\Order\Action\TransferMarketplaceDiscountFailed

    marketplace.catalog.attribute_service:
        alias: Wizacha\Marketplace\Catalog\AttributeService

    marketplace.catalog.category_service:
        alias: Wizacha\Marketplace\Catalog\Category\CategoryService
        public: true

    marketplace.multi_vendor_product.api.controller:
        alias: Wizacha\AppBundle\Controller\Api\MultiVendorProductController

    marketplace.user.api.usercontroller:
        alias: Wizacha\AppBundle\Controller\Api\UserController

    marketplace.payment.stripe_controller:
        alias: Wizacha\AppBundle\Controller\Backend\StripeController

    marketplace.product.api.productcontroller:
        alias: Wizacha\AppBundle\Controller\Api\ProductController

    marketplace.moderation.api.controller:
        alias: Wizacha\AppBundle\Controller\Api\ModerationController

    marketplace.attribute.api.controller:
        alias: Wizacha\AppBundle\Controller\Api\AttributeController

    marketplace.moderation.product_moderation_in_progress_service:
        alias: Wizacha\Premoderation\ProductModeration\ProductModerationInProgressService

    marketplace.message_attachment.message_attachment_service:
        alias: Wizacha\Marketplace\MessageAttachment\MessageAttachmentService
        public: true

    marketplace.message_attachment.message_attachment_repository:
        alias: Wizacha\Marketplace\MessageAttachment\Repository\MessageAttachmentRepository
        public: true

    marketplace.pim.option_service:
        alias: Wizacha\Marketplace\PIM\Option\OptionService
        public: true

    marketplace.pim.tax_service:
        alias: Wizacha\Marketplace\PIM\Tax\TaxService

    cscart.product_manager:
        alias: Wizacha\ProductManager
        public: true


    app.subscription_actions_traces_service:
        alias: Wizacha\Marketplace\Subscription\Log\SubscriptionActionTraceService
        public: true

    marketplace.commission.transaction_listener:
        alias: Wizacha\Marketplace\Commission\TransactionListener

    marketplace.commission.order_listener:
        alias: Wizacha\Marketplace\Order\OrderListener

    #-------------
    #-------------
    # services.yml
    #-------------
    #-------------

    pdf_generator:
        alias: Wizacha\Component\PdfGenerator\PdfGenerator

    image.manager:
        alias: Wizacha\ImageManager

    app.notification.moderation_notifier:
        alias: Wizacha\AppBundle\Notification\ModerationNotifier

    app.notification.order_notifier:
        alias: Wizacha\AppBundle\Notification\OrderNotifier

    html_service:
        alias: Wizacha\Component\Html\HtmlService

    prediggo.exporter:
        alias: Wizacha\Prediggo\Exporter

    #------------------------------
    #------------------------------
    # Resources/config/services.yml
    #------------------------------
    #------------------------------

    marketplace.catalog.exporter:
        alias: Wizacha\Marketplace\Catalog\ExporterService

    app.translation:
        alias: Wizacha\AppBundle\Service\TranslationService

    marketplace.user_service:
        alias: Wizacha\AppBundle\Security\User\UserService

    marketplace.ekey.repository:
        alias: Wizacha\Marketplace\User\EKeyRepository

    marketplace.favorite.favorite_service:
        alias: Wizacha\Marketplace\Favorite\FavoriteService

    translation.public.reader:
        alias: translation.reader
        public: true

    exercise_html_purifier.default.public:
        alias : exercise_html_purifier.default
        public: true

    app.order.amount.calculator:
        alias: Wizacha\Marketplace\Order\AmountsCalculator\Service\OrderAmountsCalculator
        public: true

    app.feature_flag_service:
        alias: Wizacha\FeatureFlag\FeatureFlagService
        public: true

    marketplace.user_group.service:
        alias: Wizacha\Marketplace\Group\UserGroupService
        public: true

    marketplace.company_service:
        alias: Wizacha\Marketplace\Company\CompanyService

    marketplace.invoicing_settings_service:
        alias: Wizacha\Marketplace\InvoicingSettings\InvoicingSettingsService

    marketplace.product.product_visibility_report_service:
        alias: Wizacha\Marketplace\PIM\Product\ProductVisibilityReportService

    order_amount_repository:
        alias: Wizacha\Marketplace\Order\AmountsCalculator\Repository\OrderAmountsRepository

    marketplace.financial_flows_history_service:
        alias: Wizacha\Marketplace\FinancialFlowsHistory\FinancialFlowsHistoryService
        public: true
