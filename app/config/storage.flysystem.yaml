services:
  ## Clients :
  aws.client:
    class: Aws\S3\S3Client
    arguments:
      - version: '2006-03-01'
        region: '%aws.s3.credentials.region%'
        credentials:
          key: '%aws.s3.credentials.key%'
          secret: '%aws.s3.credentials.secret%'
  ovh.client:
    class: Aws\S3\S3Client
    arguments:
      - version: '2006-03-01'
        use_path_style_endpoint: true
        region: '%ovh.s3.region%'
        credentials:
          key: '%ovh.s3.credentials.key%'
          secret: '%ovh.s3.credentials.secret%'
        endpoint: '%ovh.s3.api_url%'
        S3:
          version: '2006-03-01'
          endpoint_url: '%ovh.s3.api_url%'
          signature_version: 's3v4'
          addressing_style: 'virtual'
        S3API:
          endpoint_url: '%ovh.s3.api_url%'

  ## Dump Anonymizer Output Storage:
  dump_anonymizer.output.aws.client:
    class: Aws\S3\S3Client
    arguments:
      - version: '2006-03-01'
        region: '%config.dump_anonymizer.output.s3.region%'
        credentials:
          key: '%config.dump_anonymizer.output.s3.key%'
          secret: '%config.dump_anonymizer.output.s3.secret%'

  ## Hades:
  hades.aws.client:
    class: Aws\S3\S3Client
    arguments:
      - version: '2006-03-01'
        region: '%config.hades.s3.region%'
        credentials:
          key: '%config.hades.s3.key%'
          secret: '%config.hades.s3.secret%'

  ## invokable adapters :
  Wizacha\Storage\Adapter\Local:
    arguments:
      - '%kernel.project_dir%'

  Wizacha\Storage\Adapter\Azure:
    arguments:
      - '@marketplace.azure.client'

  Wizacha\Storage\Adapter\Aws:
    arguments:
      - '@aws.client'
      - '%aws.bucket%'

  Wizacha\Storage\Adapter\Ovh:
    arguments:
      - '@ovh.client'
      - '%ovh.s3.private_bucket%'
      - '%ovh.s3.public_bucket%'

  ## Factory
  Wizacha\Storage\StorageFactory:
    autowire: true
    arguments:
      $localRootPath: '%kernel.project_dir%'
      $storageType: '%storage_system%'

  ## configured storage services
  Wizacha\Storage\ElfinderStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'elfinder'
      -
        'prefix' : 'elfinder'
        'dir' : '/images'
        'max-age' : 604800

  Wizacha\Storage\ImagesStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'images'
      -
        'prefix' : 'images'
        'dir' : '/'
        'max-age' : 604800

  Wizacha\Storage\VendorSubscriptionStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'vendor_subscription'
      -
        'prefix' : 'vendor_subscriptions'
        'dir' : '/images'
        'secured' : true

  Wizacha\Storage\OrganisationStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'organisation'
      -
        'prefix' : 'organisation'
        'dir' : '/images'
        'secured' : true

  Wizacha\Storage\DownloadsStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'downloads'
      -
        'prefix' : 'downloads'
        'dir' : '/var'
        'secured' : true

  Wizacha\Storage\ProductAttachmentsStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'product_attachments'
      -
        'prefix' : 'attachments'
        'dir' : '/var'
        'secured' : true

  Wizacha\Storage\StaticsStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'statics'
      -
        'prefix' : 'statics'
        'dir' : 'var/cache/misc'
        'max-age' : 604800

  Wizacha\Storage\CsvStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'csv'
      -
        'prefix' : 'csv_files'
        'dir' : '/var'
        'secured' : true

  Wizacha\Storage\SitemapStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'sitemap'
      -
        'prefix' : 'sitemap_files'
        'dir' : '/images'

  Wizacha\Storage\CustomFilesStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'custom_files'
      -
        'prefix' : 'custom_files'
        'dir' : '/var'

  Wizacha\Storage\PrediggoStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'prediggo'
      -
        'prefix' : 'prediggo'
        'dir' : '/var'

  Wizacha\Storage\CatalogExportStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'catalog_export'
      -
        'prefix' : 'catalog_export'
        'dir' : '/var'

  Wizacha\Storage\CsvExportStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'csv_export'
      -
        'prefix' : 'csv_export'
        'dir' : '/var'
        'secured' : true

  Wizacha\Storage\AssetsStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'assets'
      -
        'prefix' : 'assets'
        'dir' : '/assets'

  Wizacha\Storage\OrderAttachmentsStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'order_attachments'
      -
        'prefix' : 'order_attachments'
        'dir' : '/var'
        'secured' : true

  Wizacha\Storage\MessageAttachmentsStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'message_attachments'
      -
        'prefix' : 'message_attachments'
        'dir' : '/var'
        'secured' : true

  Wizacha\Storage\HadesStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'hades_backup'
      -
        'prefix' : 'hades_backup'
        'dir' : '/var'
        'secured' : true
