security:
    role_hierarchy:
        ROLE_VENDOR: ROLE_USER
        ROLE_ADMIN:  ROLE_USER

    providers:
        in_memory:
            memory:
                users:
                    system: # system user, here for API which requires authentication from automated systems (crons, deploy scripts, etc.)
                        password: '%system_api_user_password%'
                        roles: 'ROLE_SYSTEM'
        # Give instance of ApiSecurityUser, from an email. It can also give an email from an apiKey
        cscart_api:
            id: marketplace.api_user_provider
        # Give instance of SecurityUser, from an email
        cscart:
            id: marketplace.user_provider

    encoders:
        Wizacha\AppBundle\Security\User\ApiSecurityUser: plaintext
        Wizacha\AppBundle\Security\User\SecurityUser:
            id: marketplace.cscart_password_encoder
        Symfony\Component\Security\Core\User\User:
            algorithm: bcrypt
            cost: 12

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        system: # only here to authenticate the system user defined in_memory
            anonymous: true
            pattern: ^/api/v1/translations/front # we only use it for this route now
            http_basic: true
            stateless: true
            provider: in_memory

        # API: Basic auth with email/passwd to get <PERSON><PERSON><PERSON><PERSON>
        api_get_key:
            anonymous: false
            http_basic: true
            pattern: ^/api/v1/users/authenticate
            stateless: true
            provider: cscart
            entry_point: marketplace.api_basic_authentication_entry_point

        # API: Basic auth with email/ApiKey or HTTP Header Authorization: token key
        default:
            anonymous: ~
            http_basic: ~
            # No cookie/session is needed since we use HTTP authentication http://symfony.com/doc/current/book/security.html#stateless-authentication
            stateless: true
            provider: cscart_api
            simple_preauth:
                authenticator: marketplace.apikey_authenticator


    access_control:
        # Callbacks PSP
        - { path: '^/api/v1/callbacks', role: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        # Available offers
        - { path: '^/api/v1/divisions-tree', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/divisions', roles: [ ROLE_ADMIN ] }
        # Currency
        - { path: '^/api/v1/currencies', role: [ IS_AUTHENTICATED_ANONYMOUSLY ], methods: [ GET ] }
        - { path: '^/api/v1/currencies', role: [ ROLE_ADMIN ] }
        # Promotions
        - { path: '^/api/v1/promotions/catalog', roles: [ ROLE_VENDOR ] }
        - { path: '^/api/v1/promotions/basket', roles: [ ROLE_VENDOR ] }
        - { path: '^/api/v1/promotions/marketplace', roles: [ ROLE_ADMIN ] }

        - { path: '^/api/v1/catalog/search/products', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        # require ROLE_VENDOR for the API
        - { path: '^/api/v1/user/oauth-token', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/user/oauth/authorize-url', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/user/oauth/admin-authorize-url', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/user/oauth/logout', roles: [ ROLE_USER ] }
        - { path: '^/api/v1/user/revoke', roles: [ ROLE_USER ] }
        - { path: '^/api/v1/users/authenticate', roles: [ ROLE_USER ] }
        - { path: '^/api/v1/users/password/recover', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/users/password/change-with-token', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/users/[\d]+', roles: [ ROLE_USER ] }
        - { path: '^/api/v1/users$', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ], methods: [POST] }
        - { path: ^/api/v1/users/set-vendor, roles: [ ROLE_VENDOR, ROLE_ADMIN ], methods: [ POST ] }
        - { path: '^/api/v1/users', roles: [ ROLE_ADMIN ] }
        - { path: '^/api/v1/user', roles: [ ROLE_USER ] }
        - { path: '^/api/v1/user/orders/[0-9]+/credit-note', roles: [ROLE_USER]}
        - { path: '^/api/v1/user/orders/[0-9]+/credit-note/[0-9]+', roles: [ROLE_USER]}
        - { path: '^/api/v1/user/orders/[0-9]+/refunds', roles: [ROLE_USER]}
        - { path: '^/api/v1/user/orders/[0-9]+/refunds/[0-9]+', roles: [ROLE_USER]}
        - { path: '^/api/v1/user/orders/[0-9]+/mark-as-paid', roles: [ROLE_ADMIN], methods: [PUT] }
        - { path: '^/api/v1/prediggo', roles: [ ROLE_ADMIN ] }
        - { path: '^/api/v1/catalog/products/[^\/]+/reviews', roles: [ ROLE_USER ], methods: [POST] }
        - { path: '^/api/v1/catalog/products/[^\/]+/report', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ], methods: [POST] }
        - { path: '^/api/v1/catalog/companies/[0-9]+/reviews', roles: [ ROLE_USER ], methods: [POST] }
        - { path: '^/api/v1/catalog', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/basket/[^\/]+/chronorelais-pickup-point$', roles: [ ROLE_USER ], methods: [POST] }
        - { path: '^/api/v1/basket/[^\/]+/order$', roles: [ ROLE_USER ] }
        - { path: '^/api/v1/basket/[^\/]+/shipping-price$', roles: [ ROLE_ADMIN ], methods: [POST, DELETE] }
        - { path: '^/api/v1/basket', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/orders/[\d]+/adjustments', roles: [ ROLE_VENDOR , ROLE_ADMIN ] }
        - { path: '^/api/v1/orders/returns/reasons', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/orders/[0-9]+/actions', roles: [ ROLE_ADMIN ], methods: [ GET ] }
        - { path: '^/api/v1/orders/[0-9]+/transactions', roles: [ ROLE_VENDOR, ROLE_ADMIN ], methods: [ GET ] }
        - { path: '^/api/v1/orders/[0-9]+/pdf-invoice', roles: [ ROLE_VENDOR, ROLE_ADMIN ]}
        - { path: '^/api/v1/orders/[0-9]+/refunds', roles: [ ROLE_VENDOR, ROLE_ADMIN] }
        - { path: '^/api/v1/orders/[0-9]+/refunds/[0-9]+', roles: [ROLE_VENDOR, ROLE_ADMIN] }
        - { path: '^/api/v1/orders/[0-9]+/credit-note', roles: [ROLE_VENDOR, ROLE_ADMIN]}
        - { path: '^/api/v1/orders/[0-9]+/credit-note/[0-9]+', roles: [ROLE_VENDOR, ROLE_ADMIN]}
        - { path: '^/api/v1/orders/[0-9]+/attachments', roles: [ROLE_VENDOR, ROLE_ADMIN] }
        - { path: '^/api/v1/orders', roles: [ ROLE_USER ] }
        - { path: '^/api/v1/mailinglists/[0-9]+/subscription$', roles: [ ROLE_USER ] }
        - { path: '^/api/v1/mailinglists', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/image/[\d]+', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/cms', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/seo/slugs', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/translations/front', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ], methods: [ GET ] }
        - { path: '^/api/v1/translations/front', roles: [ ROLE_SYSTEM ], methods: [ POST ] } # see system user provided in_memory
        - { path: '^/api/v1/doc', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/pim/multi-vendor-products', roles: [ ROLE_ADMIN ] }
        - { path: '^/api/v1/pim/moderation', roles: [ ROLE_ADMIN ] }
        - { path: '^/api/v1/pim/attributes', roles: [ ROLE_VENDOR, ROLE_ADMIN ], methods: [ GET ] }
        - { path: '^/api/v1/pim/attributes', roles: [ ROLE_ADMIN ], methods: [ POST, PUT, DELETE ] }
        - { path: '^/api/v1/statistics', role: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/system', role: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/test', role: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/discussions', role: [ ROLE_USER ] }
        - { path: '^/api/v1/chronopost/points-relais', role: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/mondial-relay/points-relais$', role: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/mondial-relay/points-relais/[0-9]+$', roles: [ ROLE_USER ] }
        - { path: '^/api/v1/mondial-relay/brand-code', role: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/companies/c2c', role: [ ROLE_USER ] }
        - { path: '^/api/v1/companies/[\d]+/commissions', role: [ ROLE_ADMIN ] }
        - { path: '^/api/v1/companies/[\d]+', role: [ ROLE_USER ], methods: [ GET ] }
        - { path: '^/api/v1/companies', role: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/contact-request', role: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/organisations/registration', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/organisations/', roles: [ ROLE_USER ] }
        - { path: '^/api/v1/languages', roles: [ ROLE_ADMIN ] }
        - { path: '^/api/v1/security/', role: [ ROLE_ADMIN ] }
        - { path: '^/api/v1/reports/transactions', role: [ ROLE_VENDOR, ROLE_ADMIN ], methods: [ GET ] }
        - { path: '^/api/v1/subscriptions', role: [ ROLE_USER ] }
        - { path: '^/api/v1/ping', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/payments', roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/api/v1/commissions', role: [ ROLE_ADMIN ] }
        - { path: '^/api/v1/categories/[\d]+/commissions', role: [ ROLE_ADMIN ] }
        - { path: '^/api/v1/orders/[\d]+/shipments', role: [ ROLE_VENDOR, ROLE_ADMIN ], methods: [ GET ]}
        - { path: '^/api/v1/shipments/[ 0-9 ]+/mark-as-delivered', roles: [ ROLE_USER ], methods: [ PUT ] }
        - { path: '^/api/v1/orders/[ 0-9 ]+/mark-as-delivered', roles: [ ROLE_USER ], methods: [ PUT ] }
        - { path: '^/api/v1/quote-request-selections', role: [ ROLE_USER ] }
        - { path: '^/api/v1/', roles: [ ROLE_VENDOR , ROLE_ADMIN ] }
        - { path: '^/api/v1/products/[0-9]+/related', role: [ ROLE_VENDOR , ROLE_ADMIN ], methods: [ POST, DELETE ] }
        - { path: '^/api/v1/quote-request-selections', role: [ ROLE_USER ] }
        # BO internal API
        - { path: '^/admin/api', role: [ ROLE_VENDOR , ROLE_ADMIN ] }
        - { path: '^/admin/oauth/authorize', role: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: '^/dev/publish', role: [ ROLE_ADMIN ] }
