services:
    _defaults:
        autowire: true
        autoconfigure: true

    Wizacha\Marketplace\Payment\Processor\ProcessorCallbackRegistry:
        arguments:
            - !tagged psp.callback.handler

    #----------
    # Controllers
    #----------
    Wizacha\AppBundle\Controller\Api\StripeCallbackController:
        autowire: true
        public: true

    #----------
    # Handlers
    #----------
    Wizacha\Marketplace\Payment\Stripe\Handler\:
        resource: '../../src/Marketplace/Payment/Stripe/Handler'
