framework:
  messenger:
    transports:
      async_yavin:
        dsn: '%yavin_messenger_transport_dsn%'
        serializer: 'Wizacha\Marketplace\Messenger\JsonEnvelopeSerializer'
        options:
          confirm_timeout: 10
          auto_setup: false
          exchange:
            name: main
            type: topic
          queues:
            debug:
              binding_keys:
                - event.debug.message
            whk:
              binding_keys:
                - event.order.created
            agw:
              binding_keys:
                - event.user.typeChanged
                - event.user.activated
                - event.user.created
                - event.user.updated
                - event.user.deactivated
                - event.user.companyChanged
    routing:
      'Wizacha\Marketplace\Messenger\EventMessage': ['async_yavin']
