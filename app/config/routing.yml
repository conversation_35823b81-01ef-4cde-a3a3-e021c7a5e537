proxy:
    path: /proxy/{apiUri}
    defaults: { _controller: AppBundle:Proxy:sendRequest}
    requirements:
        apiUri: .+

api:
    resource: '@AppBundle/Resources/config/routes/api.yml'
    prefix:   /api

home:
    path: /
    defaults: { _controller: AppBundle:Index:index }
    methods: [GET]

category:
    path: /{categoryPath}
    defaults: { _controller: AppBundle:Index:index }
    methods: [GET]
    condition: "request.attributes.get('categoryId') > 0"
    requirements:
        categoryPath: ".+"

vendor:
    path: /{slug}
    defaults: { _controller: AppBundle:Index:index }
    methods: [GET]
    condition: "request.attributes.get('companyId') > 0"

# Legacy URL
variant_legacy:
    path: /{slug}.html
    defaults:
        _controller: FrameworkBundle:Redirect:redirect
        route: variant
        permanent: true
    methods: [GET]
    condition: "request.attributes.get('variantId') > 0"
variant:
    path: /{slug}
    defaults: { _controller: AppBundle:Index:index }
    methods: [GET]
    condition: "request.attributes.get('variantId') > 0"
    options:
        expose: true

product:
    path: /{categoryPath}/{productSlug}.html
    defaults: { _controller: AppBundle:Product:view }
    methods: [GET]
    requirements:
        categoryPath: ".+"
        productSlug: "[^/]+"
    condition: "request.attributes.get('productId') > 0"

affiliate_link:
    path: /affiliate-link/{declinationId}
    defaults: { _controller: AppBundle:Product:affiliateLink }
    methods: [GET]
    requirements:
        declinationId: "[^/]+"

# Company

# Dummy route, needed by Wizacha\AppBundle\Twig\AppExtension::getRouteCreateProduct()
# https://github.com/wizaplace/wizaplace/blob/e2192cc42b6d22b59c8c600d82d08014d41c20a5/src/AppBundle/Twig/AppExtension.php#L336
company_new:
    path: /the/void
    defaults: { _controller:  AppBundle:Api/Ping:ping }

company_terms:
    path: /{company}/cgv
    defaults: { _controller: AppBundle:Company:terms }

company_mangopay_inscription:
    path: /mangopay/inscription
    defaults: { _controller: AppBundle:Backend\Mangopay:mangoPayInscription }

company_delete_mangopay_inscription:
    path: /mangopay/delete/{id}
    defaults: { _controller: AppBundle:Backend\Mangopay:deleteMangoPayInscription }
    methods: [GET]
    requirements:
        id: '\d+'

company_submit_mangopay_ubo:
    path: /submit/mangopay/ubo/{companyId}
    defaults: { _controller: AppBundle:Backend\Mangopay:submitMangoPayUBO }
    requirements:
        companyId: '\d+'

# Product
product_new:
    path:   /proposer-une-annonce.html
    defaults: { _controller: AppBundle:Product:create }

product_update:
    path:   /maj-produit.html
    defaults: { _controller: AppBundle:Product:update }

product_report:
    path:   /report-product
    defaults: { _controller: AppBundle:Product:handleReport }
    methods: [POST]

picker_subcategories:
    path:  /categories/picker
    defaults: { _controller: AppBundle:Category:subCategoriesPicker }

picker_category:
    path:  /categories/picker-info
    defaults: { _controller: AppBundle:Category:categoryInfo }

features_for_form:
    path: /features/form
    defaults: { _controller: AppBundle:Feature:form }

favorites_add_to_basket:
    path: /favorites/add_to_basket
    defaults: { _controller: AppBundle:Favorite:addToBasket }

login:
    path: /login
    defaults: { _controller: AppBundle:Profile:login }

recover_password:
    path: /recover-password
    defaults: { _controller: AppBundle:Profile:recoverPassword }
    methods: [GET,POST]

notifications_display:
    path: /notifications/display
    defaults: { _controller: AppBundle:Notification:display }

basket_view:
    path: /basket
    defaults: { _controller: AppBundle:Basket:view }

basket_add_product:
    path: /basket/add_product
    defaults: { _controller: AppBundle:Basket:addProduct }
    methods: [POST]

basket_modify_product_quantity:
    path: /basket/modify_product_quantity
    defaults: { _controller: AppBundle:Basket:modifyProductQuantity }
    methods: [POST]

basket_select_shipping:
    path: /basket/select_shipping
    defaults: { _controller: AppBundle:Basket:selectShipping }
    methods: [POST]

basket_add_promotion:
    path: /basket/add_promotion
    defaults: { _controller: AppBundle:Basket:addCartPromotion }

basket_remove_promotion:
    path: /basket/remove_promotion
    defaults: { _controller: AppBundle:Basket:removeCartPromotion }

# Checkout
checkout_login:
    path: /checkout/login
    defaults:  { _controller: AppBundle:Checkout:login }
    methods: [GET]
checkout_addresses:
    path: /checkout/addresses
    defaults:  { _controller: AppBundle:Checkout:addresses }
    methods: [GET]
checkout_addresses_update:
    path: /checkout/addresses/update
    defaults:  { _controller: AppBundle:Checkout:updateAddresses }
    methods: [POST]
checkout_pickup_points:
    path: /checkout/pickup-points
    defaults:  { _controller: AppBundle:Checkout:pickupPoints }
    methods: [GET]
checkout_pickup_points_update:
    path: /checkout/pickup-points/update
    defaults:  { _controller: AppBundle:Checkout:updatePickupPoints }
    methods: [POST]
checkout_payment:
    path: /checkout/payment
    defaults:  { _controller: AppBundle:Checkout:payment }
    methods: [GET]
checkout_payment_update:
    path: /checkout/payment/update
    defaults:  { _controller: AppBundle:Checkout:updateSelectedPayment }
    methods: [POST]
checkout_complete:
    path: /checkout/complete/{orderId}
    defaults:  { _controller: AppBundle:Checkout:complete }
    methods: [GET]
    requirements:
        orderId: '\d+'

sitemap_categories:
    path:  /sitemap_categories.xml
    defaults: { _controller: AppBundle:Category:sitemap }

video_transcode:
    path:  /video/transcode
    defaults: { _controller: AppBundle:Video:startTranscodeJobs }

video_copy:
    path:  /video/copy
    defaults: { _controller: AppBundle:Video:startCopyUrl }
    methods: [POST]

mailing_list_subscribe:
    path: /mailinglist/subscribe
    defaults: { _controller: AppBundle:MailingList:subscribe }
    methods: [POST]
mailing_list_unsubscribe:
    path: /mailinglist/unsubscribe
    defaults: { _controller: AppBundle:MailingList:unsubscribe }
    methods: [POST]

contact:
    path:  /contact
    defaults: { _controller: AppBundle:Contact:send }
    methods: [POST]
    options: { overrideLegacySlug: true } # see \Wizacha\Marketplace\Seo\SeoService::registerSlugLegacy

# Import specific bundle routes
bundle_import:
    resource: 'routing.bundle.php'

page:
    path: /{page}.html
    defaults: { _controller: AppBundle:Page:view }
    methods: [GET]
    condition: "request.attributes.has('pageId')"

page_form:
    path: /page-form
    defaults: { _controller: AppBundle:Page:handleForm }
    methods: [POST]

robots:
    path: /robots.txt
    defaults: { _controller: AppBundle:RootAsset:robotsTxt }
    methods: [GET]

sitemap:
    path: /sitemap.xml
    defaults: { _controller: AppBundle:RootAsset:sitemapXml }
    methods: [GET]
    schemes:  [https] # force URL generation with https:// to prevent redirection

version:
    path: /version.txt
    defaults: { _controller: AppBundle:RootAsset:versionTxt }
    methods: [GET]

subscribe_newsletter:
    path: /newsletter
    defaults: { _controller: AppBundle:MailingList:subscribeNewsletter }
    methods: [POST]

fos_js_routing:
    resource: "@FOSJsRoutingBundle/Resources/config/routing/routing.xml"

support_login:
    path:  /support-login
    defaults: { _controller: AppBundle:Support:login }

support_login_vendor:
    path:  /support-login-vendor
    defaults: { _controller: AppBundle:Support:loginVendor }

support_login_front:
    path:  /support-login-front
    defaults: { _controller: AppBundle:Support:loginFront }

mangopay_hooks:
    path: /mangopay-hooks
    defaults:
        _controller: AppBundle:PaymentNotification:mangopayBankwire
        isPsp: true
    schemes:  [https] # force URL generation with https:// to prevent redirection
    methods: [GET]

payment_notification_lemonway_card:
    path: /payment-notification/lemonway-card
    defaults:
        _controller: AppBundle:PaymentNotification:lemonwayCard
        isPsp: true
    schemes:  [https] # force URL generation with https:// to prevent redirection

payment_notification_lemonway_sepa_transaction:
    path: /payment-notification/lemonway-sepa-transaction
    defaults:
        _controller: AppBundle:PaymentNotification\LemonWaySepaNotification:lemonwaySepaTransaction
        isPsp: true
    schemes: [https] # force URL generation with https:// to prevent redirection
    methods: [POST]

payment_notification_lemonway_sign_document:
    path: /payment-notification/lemonway-sign-document
    defaults:
        _controller: AppBundle:PaymentNotification\SepaMandateSignatureNotification:lemonwaySignDocument
        isPsp: true
    schemes: [ https ] # force URL generation with https:// to prevent redirection

payment_notification_lemonway_bankwire:
    path: /payment-notification/lemonway-bankwire
    defaults:
        _controller: AppBundle:PaymentNotification:lemonwayBankwire
        isPsp: true
    schemes: [https] # force URL generation with https:// to prevent redirection
    methods: [POST]

payment_notification_mangopay_card:
    path: /payment-notification/mangopay-card
    defaults:
        _controller: AppBundle:PaymentNotification:mangopayCard
        isPsp: true
    schemes: [https] # force URL generation with https:// to prevent redirection

payment_notification_hipay_card:
    path: /payment-notification/hipay-card
    defaults:
        _controller: AppBundle:PaymentNotification:hipayCard
        isPsp: true
    schemes:  [https] # force URL generation with https:// to prevent redirection

payment_notification_smoney_card:
    path: /payment-notification/smoney-card
    defaults:
        _controller: AppBundle:PaymentNotification:smoneyCard
        isPsp: true
    methods: [GET, POST]
    schemes:  [https] # force URL generation with https:// to prevent redirection

payment_notification_transfer_standby:
    path: /payment-notification/transfer-standby
    defaults:
        _controller: AppBundle:PaymentNotification:markBankTransferIsDone
        isPsp: true
    schemes:  [https] # force URL generation with https:// to prevent redirection

payment_notification_stripe_card:
    path: /payment-notification/stripe-card
    defaults:
        _controller: AppBundle:PaymentNotification:stripeCard
        isPsp: true
    methods: [POST]
    schemes:  [https] # force URL generation with https:// to prevent redirection

payment_notification_stripe_sepa:
    path: /payment-notification/stripe-sepa
    defaults:
        _controller: AppBundle:PaymentNotification:stripeSepa
        isPsp: true
    methods: [POST]
    schemes:  [https] # force URL generation with https:// to prevent redirection

payment_notification_stripe_sepa_init:
    path: /payment-notification/stripe-sepa-init
    defaults:
        _controller: AppBundle:PaymentNotification:stripeSepaInit
        isPsp: true
    methods: [POST]
    schemes:  [https] # force URL generation with https:// to prevent redirection

payment_notification_hipay_sepa_transaction:
    path: /payment-notification/hipay-sepa-transaction
    defaults:
        _controller: AppBundle:PaymentNotification\HipaySepaNotification:transaction
        isPsp: true
    methods: [POST]
    schemes:  [https] # force URL generation with https:// to prevent redirection

payment_card_stripe_update_callback:
    path: /payment/card/card-updated
    defaults:
        _controller: AppBundle:Card:stripeUpdateCallback
        isPsp: true
    schemes:  [https] # force URL generation with https:// to prevent redirection

payment_card_stripe_update:
    path: /payment/card/update
    defaults:
        _controller: AppBundle:Card:stripeUpdate
        isPsp: true
    schemes: [ https ] # force URL generation with https:// to prevent redirection

admin:
    prefix: /admin
    resource: 'routing.admin.yml'

# Currency
currency_management:
    path: /currency/list
    defaults:  { _controller: AppBundle:Backend/Currency:list }

currency_update_status:
    path: /currency/change-status/{code}/{status}
    defaults:  { _controller: AppBundle:Backend/Currency:updateStatus }
    methods: [GET]

#ToRemoveOnlyForDev TODO delete before prod
test_tva:
    path: /test-tva/{orderId}
    defaults: { _controller: AppBundle:Api\Order\TestTvaDev:getTestCalculator }
    methods: [GET]
