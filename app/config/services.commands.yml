services:
    _defaults:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Command\AuthLogPurgeCommand:
        arguments:
            - '%clear_cache_auth_delay_month%'
            - '@marketplace.authlog.repository'

    Wizacha\AppBundle\Command\AuthLogClearCommand:
        arguments:
            - '@marketplace.authlog.repository'

    Wizacha\AppBundle\Command\ClearEximJobsCommand:
        arguments:
            - '@Wizacha\Component\Import\EximJobService'

    Wizacha\AppBundle\Command\UserAccessToken:
        arguments:
            - '@marketplace.user.user_service'

    Wizacha\AppBundle\Command\PushVendorInfoToPspCommand:
        arguments:
            - '@marketplace.user.user_service'
            - '@event_dispatcher'

    Wizacha\AppBundle\Command\SMoneyCheckKycCommand:
        arguments:
            - '@marketplace.payment.smoney'
            - '@Wizacha\Marketplace\Company\CompanyService'

    Wizacha\AppBundle\Command\MagentoPasswordHashUpdateCommand:

    Wizacha\AppBundle\Command\ImportMiraklCompanyStatusCommand:

    Wizacha\AppBundle\Command\MangoPayCheckKycCommand:
        arguments:
            - '@marketplace.payment.mangopay'
            - '@Wizacha\Marketplace\Company\CompanyService'

    Wizacha\AppBundle\Command\LemonWayCheckKycCommand:
        arguments:
            - '@marketplace.payment.lemonway'
            - '@Wizacha\Marketplace\Company\CompanyService'

    Wizacha\AppBundle\Command\HipayCheckKycCommand:

    Wizacha\AppBundle\Command\CheckValidityMarketplaceDiscountCommand:
        arguments:
            - '@marketplace.promotion.promotionservice'

    Wizacha\AppBundle\Command\RgpdUserAnonymizerCommand:

    Wizacha\AppBundle\Command\RgpdCompanyAnonymizerCommand:

    Wizacha\AppBundle\Command\OrderCancelRollbackCommand:
        public: true

    Wizacha\AppBundle\Command\DeleteOrdersCommand:

    Wizacha\AppBundle\Command\DeleteAllOrdersCommand:

    Wizacha\AppBundle\Command\Readmodel\ClearReadmodelCommand:

    Wizacha\AppBundle\Command\Readmodel\CreateReadmodelCommand:

    Wizacha\AppBundle\Command\Readmodel\UpdateReadmodelCommand:

    Wizacha\AppBundle\Command\Readmodel\DumpReadmodelCommand:

    Wizacha\AppBundle\Command\Readmodel\CleanReadmodelCommand:

    Wizacha\AppBundle\Command\SeoRefreshNamesCommand:

    Wizacha\AppBundle\Command\Algolia\ClearAlgoliaIndexCommand:

    Wizacha\AppBundle\Command\Algolia\CreateAlgoliaIndexCommand:

    Wizacha\AppBundle\Command\Algolia\PushAlgoliaConfigCommand:

    Wizacha\AppBundle\Command\Algolia\PushAlgoliaProductsCommand:

    Wizacha\AppBundle\Command\Algolia\UpdateAlgoliaProductsCommand:
        arguments:
            - '@marketplace.search.product_index'

    Wizacha\AppBundle\Command\ProductRefreshCacheCommand:

    Wizacha\AppBundle\Command\CheckMangopayUserIdCommand:
        arguments:
            - '@marketplace.payment.mangopay'
            - '@database_connection'

    Wizacha\AppBundle\Command\DropMarketplaceDatabaseCommand:
        arguments:
            - '@database_connection'
            - '%feature.sandbox%'

    Wizacha\AppBundle\Command\UpdateTranslationOrderStatusCommand:
        arguments:
            - '@database_connection'

    Wizacha\AppBundle\Command\FixDuplicateVariationsCommand:
        arguments:
            - '@database_connection'

    Wizacha\AppBundle\Command\CreateUserFromCSVCommand:

    Wizacha\AppBundle\Command\MiraklCreateCompanyAdministrators:

    Wizacha\AppBundle\Command\MiraklOrderForceCompletedCommand:

    Wizacha\AppBundle\Command\MiraklOrderForceFromProcessingToProcessedCommand:

    Wizacha\AppBundle\Command\UpdateTranslationFromCsvCommand:

    Wizacha\AppBundle\Command\BasketsDeleteAllCommand:
        arguments:
            - '@marketplace.basket.repository.read_model'
            - '@marketplace.user.user_repository'

    Wizacha\AppBundle\Command\DeployRemoveAssetsAwsCacheCommand:
        arguments:
            - '%marketplace.version%'
            - '@Wizacha\Storage\StaticsStorageService'

    Wizacha\AppBundle\Command\DeployAlgoliaUpdateCommand:

    Wizacha\AppBundle\Command\DeployOnceCompanyEmailSettingsCommand:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '%mail_domain%'

    Wizacha\AppBundle\Command\EndBlockedDispatchedOrdersCommand:

    Wizacha\AppBundle\Command\RenewSubscriptionCommand:
        arguments:
            - '@Wizacha\Marketplace\Subscription\SubscriptionService'
            - '@Wizacha\Marketplace\Subscription\SubscriptionRepository'
            - '@Wizacha\Marketplace\Date\DateService'
            - '@marketplace.user.user_service'
            - '@logger'

    Wizacha\AppBundle\Command\DeployMigrateOrderTransactionsCommand:

    Wizacha\AppBundle\Command\DeployMigrateOrderTransactionsIsBackCommand:

    Wizacha\AppBundle\Command\PriceTiers\DeleteComplexPriceTiersCommand:

    Wizacha\AppBundle\Command\DeployMigrateRefunds:

    Wizacha\AppBundle\Command\PriceTiers\CleanAndRealignBasicPriceTiersCommand:

    Wizacha\AppBundle\Command\CheckIncompleteOrdersCommand:

    Wizacha\AppBundle\Command\FixOrderInvoiceDataCommand:
        arguments:
            $assetsStorageService: '@Wizacha\Storage\AssetsStorageService'

    Wizacha\AppBundle\Command\Division\ImportDivisionProductsCommand:

    Wizacha\AppBundle\Command\PayPaymentDefermentOrdersCommand:
        arguments:
            - '@marketplace.order.order_service'
            - '@marketplace.order.action.cancel'
            - '@logger'
            - '@marketplace.order.action.redirect_to_payment_processor'
            - !tagged marketplace.payment_processor_service_deferment_payment

    Wizacha\AppBundle\Command\PspSendKycCommand:

    Wizacha\AppBundle\Command\MangoPaySendKycCommand:
        arguments:
            - '@marketplace.payment.mangopay'

    Wizacha\AppBundle\Command\LemonwaySendKycCommand:
        arguments:
            - '@marketplace.payment.lemonway'

    Wizacha\AppBundle\Command\SMoneySendKycCommand:
        arguments:
            - '@marketplace.payment.smoney'
            - '@Wizacha\Marketplace\Company\CompanyService'
            - '@logger'
            - '@event_dispatcher'

    Wizacha\AppBundle\Command\ClearTemporaryResourcesCommand:

    Wizacha\AppBundle\Command\ListAmqpQueuesCommand:
        arguments:
            - '%marketplace.queue.config%'

    Wizacha\AppBundle\Command\DeployMigrateDivisionCommand:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@Wizacha\Component\Divisions\XmlImporter'
            - '%feature.enable_divisions%'

    Wizacha\AppBundle\Command\Moderation\PurgeProductInProgressCommand:
        arguments:
            - '@marketplace.moderation.moderation_service'

    Wizacha\AppBundle\Command\CommissionsRecalculateCommand:

    Wizacha\AppBundle\Command\PurgeResourcesCommand:
        arguments:
            - '@Wizacha\Component\PurgeResources\PurgeResourceFactory'
            - '@Wizacha\Component\PurgeResources\Report'
            - '%resources_purge_period.images%'

    Wizacha\AppBundle\Command\ExportCompanyTokenCommand:

    Wizacha\AppBundle\Command\CommissionsCheckConfigCommand:

    Wizacha\AppBundle\Command\AmqpDebouncerTriggerCommand:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Command\AmqpDebouncerStatsCommand:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Command\AmqpDebouncerBenchmarkCommand:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Command\CreateAmqpQueuesCommand:

    Wizacha\AppBundle\Command\ConsumeAmqpMessagesCommand:

    Wizacha\AppBundle\Command\FixOrdersEmailCommand:

    Wizacha\AppBundle\Command\UserUpdateCommand:

    Wizacha\AppBundle\Command\PrediggoExportCommand:
        arguments:
            - '@Wizacha\Prediggo\Exporter'
            - '%feature.prediggo_exporter%'

    Wizacha\AppBundle\Command\UpdateCurrencyRatesCommand:
        autowire: true
        autoconfigure: true

    Wizacha\AppBundle\Command\ResetProgressForProductInModeration:
    Wizacha\AppBundle\Command\MigrateDiscussCommand:
    Wizacha\AppBundle\Command\LoadFixtureCommand:
    Wizacha\AppBundle\Command\AssetsInstallCommand:
        arguments:
            - '@less_compiler'
            - '@basic.configuration_service'
            - '%theme_bundle%'
            - '%kernel.project_dir%'
    Wizacha\AppBundle\Command\ListAllFeaturesCommand:
    Wizacha\AppBundle\Command\RenameProductAttachmentCommand:
    Wizacha\AppBundle\Command\AdminInstallCommand:
    Wizacha\AppBundle\Command\DeployPostActionsCommand:
    Wizacha\AppBundle\Command\DeployRunCommand:
    Wizacha\AppBundle\Command\UpdateOrdersToCompletedCommand:
    Wizacha\AppBundle\Command\MarkOrdersAsDeliveredCommand:
    Wizacha\AppBundle\Command\EndOrdersWithdrawalPeriodCommand:
        arguments:
            - '%feature.withdrawal_period%'
    Wizacha\AppBundle\Command\DispatchFundsCommand:
    Wizacha\AppBundle\Command\ImportAutomatedFeedsCommand:
        public: true
        arguments:
            $enableAutomatedFeeds: '%feature.enable_automated_feeds%'
    Wizacha\AppBundle\Command\GenerateSitemapCommand:
    Wizacha\AppBundle\Command\RecountProductInCategoriesCommand:
    Wizacha\AppBundle\Command\RefreshNewPromotionsInSearchCommand:
    Wizacha\AppBundle\Command\TrashAbandonedOrdersCommand:
    Wizacha\AppBundle\Command\PayoutCompaniesCommand:
        autowire: true
        autoconfigure: true
        arguments:
            $processors: !tagged marketplace.payment_processor_service
            $frequencyPayout: '%frequency_cron_payout%'
            $payoutService: '@marketplace.payment.payout_service'

    Wizacha\AppBundle\Command\OrderCheckAcceptationDelayCommand:
    Wizacha\AppBundle\Command\PspCheckKycCommand:
    Wizacha\AppBundle\Command\ExportCatalogCommand:

    Wizacha\AppBundle\Command\FixDeclinationInventoryCommand:

    Wizacha\AppBundle\Command\RemovePromotionsOnIrrelevantOrdersCommand:

    Wizacha\AppBundle\Command\OrdersResetDispatchFundsCommand:

    Wizacha\AppBundle\Command\RollbackProcessedOrdersCommand:

    Wizacha\AppBundle\Command\OrderRefundOfflineMarkAsPaidCommand:

    Wizacha\AppBundle\Command\CatalogJsonExporterCommand:

    Wizacha\AppBundle\Command\FixOrderDecryptPaymentInformation:

    Wizacha\AppBundle\Command\DebugRegistryCommand:

    Wizacha\AppBundle\Command\DebugVariablesCommand:
        arguments:
            - '%kernel.project_dir%'

    Wizacha\AppBundle\Command\DeleteAllMultiVendorProductCommand:
        arguments:
            - '@database_connection'

    Wizacha\AppBundle\Command\Janus\JanusExportCommand:

    Wizacha\AppBundle\Command\Janus\JanusImportCommand:

    Wizacha\AppBundle\Command\FixOrderCommissionSyncCommand:

    Wizacha\AppBundle\Command\ListEximImportMessages:

    Wizacha\AppBundle\Command\DeleteOrdersAmountsCommand:

    Wizacha\AppBundle\Command\StripeRemoveConsumedMandatesCommand:

    Wizacha\AppBundle\Command\DeploySetTransactionCompanyIdAndCurrencyCommand:
        autoconfigure: true
        arguments:
            - '@marketplace.transaction.transaction_repository'
            - '%currency.code%'
            - '@doctrine.orm.entity_manager'

    Wizacha\AppBundle\Command\Benchmark\BenchmarkDatabaseCommand:

    Wizacha\AppBundle\Command\Benchmark\BenchmarkRedisCommand:
        arguments:
            - '@snc_redis.default'

    Wizacha\AppBundle\Command\Benchmark\BenchmarkQueueCommand:
        arguments:
            - '%queue.type%'
            - '@marketplace.queue_manager'
            -
                region: '%aws.sqs.credentials.region%'
                key: '%aws.sqs.credentials.key%'
                secret: '%aws.sqs.credentials.secret%'
            -
                host: '%amqp.host%'
                port: '%amqp.port%'
                user: '%amqp.user%'
                pass: '%amqp.pass%'
                vhost: '%amqp.vhost%'

    Wizacha\AppBundle\Command\Benchmark\BenchmarkAlgoliaCommand:
        arguments:
            - '@marketplace.search_engine'

    Wizacha\AppBundle\Command\Benchmark\BenchmarkStorageCommand:
        arguments:
            $staticsStorageService: '@Wizacha\Storage\StaticsStorageService'

    Wizacha\AppBundle\Command\LemonwayTransactionSyncCommand:

    Wizacha\AppBundle\Command\CreateCompanyWalletCommand:

    Wizacha\AppBundle\Command\ClearPaymentDataBeforeProductionCommand:

    Wizacha\AppBundle\Command\CreateIndexAlgoliaCommand:

    Wizacha\AppBundle\Command\DeleteOrganisationsCommand:

    Wizacha\AppBundle\Command\TranslationMissingKeysCommand:

    Wizacha\AppBundle\Command\KPIPublishCommand:

    Wizacha\AppBundle\Command\BenchmarkFixtureCommand:
        arguments:
            $version: '%marketplace.version%'

    Wizacha\AppBundle\Command\ProductVisibilityCommand:

    Wizacha\AppBundle\Command\HadesBackupCommand:
        arguments:
            $platformTechnicalName: '%config.platform_technical_name%'
            $s3Client: '@hades.aws.client'
            $bucket: '%config.hades.s3.bucket%'

    Wizacha\AppBundle\Command\OrderExecuteActionCommand:

    Wizacha\AppBundle\Command\OrderListActionsCommand:
        arguments:
            - !tagged marketplace.workflow.action

    Wizacha\AppBundle\Command\DeploySetUsersPasswordsHistoryCommand:
        arguments:
            - '@event_dispatcher'
            - '@doctrine.orm.entity_manager'
            - '@Wizacha\Marketplace\User\UserService'
            - '%previous_passwords_difference_limit%'

    Wizacha\AppBundle\Command\DebugUnserializeCommand:

    Wizacha\AppBundle\Command\DeploySetTransactionOriginAndDestinationCommand:
        autoconfigure: true
        arguments:
            - '@marketplace.transaction.transaction_repository'
            - '@Wizacha\Marketplace\Company\CompanyService'
            - '@marketplace.payment.mangopay'
            - '@marketplace.payment.lemonway'
            - '@doctrine.orm.entity_manager'
            - '@marketplace.payment.payout_service'
            -  !tagged marketplace.payment_processor_service

    Wizacha\AppBundle\Command\FixDuplicatedMainImageLinksCommand:

    Wizacha\AppBundle\Command\Yavin\UploadUsersToAuthGateway:

    Wizacha\AppBundle\Command\ListEximJobsImport:

    Wizacha\AppBundle\Command\CancelEximJobsImport:

    Wizacha\AppBundle\Command\DumpAnonymizerCommand:
        arguments:
            $platformTechnicalName: '%config.platform_technical_name%'

    Wizacha\AppBundle\Command\OrderAmountsRecalculateCommand:

    Wizacha\AppBundle\Command\DeployDebugCommand:

    Wizacha\AppBundle\Command\OrderCompletedStatsCommand:

    Wizacha\AppBundle\Command\OrderCreationStatsCommand:

    Wizacha\AppBundle\Command\WorkflowToGraphvizCommand:

    Wizacha\AppBundle\Command\ImportThemeTranslationsCommand:
        autowire: true
        tags:
            - { name: console.command }

    Wizacha\AppBundle\Command\FixCorruptedSerializedDataCommand:
