parameters:
    marketplace.function_to_dispatch:
        'Wizacha\Exim\Product::handleCSVProduct':
            queue: csv_line
            payload: '\Wizacha\Async\FunctionPayload'
            env: [['runtime','company_id']]
        'Wizacha\Exim\Import\Entities\EntitiesImporter::import':
            queue: csv_entities
            payload: '\Wizacha\Async\FunctionPayload'
            env: [['runtime','company_id']]
        'marketplace.moderation.moderation_service::moderateProduct':
            queue: product_moderation
            payload: '\Wizacha\Async\ServicePayload'
        #SearchEngine
        'marketplace.search.category_index::updateByIds':
            queue: search_engine
            payload: '\Wizacha\Async\ServicePayload'
        'fn_delete_product':
            queue: products
            payload: '\Wizacha\Async\FunctionPayload'
            env: [['runtime','company_id'],[\Wizacha\Config::REG_AREA]]
        'marketplace.product.projector::projectProduct':
            queue: readmodel
            payload: '\Wizacha\Async\ServicePayload'
            force_async: true
        'marketplace.product.projector::projectMultiVendorProduct':
            queue: readmodel
            payload: '\Wizacha\Async\ServicePayload'
            force_async: false
        'marketplace.catalog::rebuild':
            queue: readmodel
            payload: '\Wizacha\Async\ServicePayload'
            force_async: true
        'marketplace.pim.video_service::startImportFromUrl':
            queue: videos
            payload: '\Wizacha\Async\ServicePayload'
        'app.mailer::send':
            queue: emails
            payload: '\Wizacha\Async\ServicePayload'
        'app.dolist_client::sendMessageRequest':
            queue: emails
            payload: '\Wizacha\Async\ServicePayload'
        'marketplace.multi_vendor_product.linker::attachProductIdToMatchingMvp':
            queue: multi_vendor_products
            payload: '\Wizacha\Async\ServicePayload'
        'marketplace.multi_vendor_product.service::updateFromFirstProduct':
            queue: multi_vendor_products
            payload: '\Wizacha\Async\ServicePayload'
        'marketplace.multi_vendor_product.linker::attachMatchingProductsToMvpId':
            queue: multi_vendor_products
            payload: '\Wizacha\Async\ServicePayload'
        'fn_export':
            queue: csv_exports
            payload: '\Wizacha\Async\FunctionPayload'
            env: [['runtime','company_id']]
        'event.subscriber.search::onProductUpdateAsync':
            queue: feature_products
            payload: '\Wizacha\Async\ServicePayload'
        'marketplace.divisions_settings.service::onAsyncMarketplaceDivisionSettingsUpdate':
            queue: feature_available_offers
            payload: '\Wizacha\Async\ServicePayload'
        'marketplace.divisions_settings.service::onAsyncCompanyDivisionSettingsDispatch':
            queue: feature_available_offers
            payload: '\Wizacha\Async\ServicePayload'
        'marketplace.divisions_settings.service::onAsyncCompanyDivisionSettingsUpdate':
            queue: feature_available_offers
            payload: '\Wizacha\Async\ServicePayload'
        'marketplace.divisions_settings.service::onAsyncProductDivisionSettingsUpdate':
            queue: feature_available_offers
            payload: '\Wizacha\Async\ServicePayload'
        'Wizacha\Exim\ProductPrices::ImportProductPrices':
            queue: product_price_import
            payload: '\Wizacha\Async\FunctionPayload'
            env: [['runtime','company_id']]
        'Wizacha\Exim\ProductStock::ImportProductStocks':
            queue: product_stock_import
            payload: '\Wizacha\Async\FunctionPayload'
            env: [['runtime','company_id']]
        'Wizacha\Exim\Import\TranslationImporter::import':
            queue: translations_import
            payload: '\Wizacha\Async\ServicePayload'
        'marketplace.user_group.service::importUsers' :
            queue : user_group_import
            payload : '\Wizacha\Async\ServicePayload'
        'Wizacha\Exim\RelatedProducts::put':
            queue: related_products_import
            payload: '\Wizacha\Async\FunctionPayload'
            env: [['runtime','company_id'],['user_info']]
        'Wizacha\CloudImageManager::invalidateCloudImageCache':
            queue: videos
            payload: '\Wizacha\Async\ServicePayload'
