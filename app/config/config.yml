imports:
    - { resource: parameters.env.yml }
    - { resource: parameters.default.yml }
    - { resource: parameters.yml }
    - { resource: parameters.doctrine.yml }
    - { resource: parameters.queues.yml }
    - { resource: parameters.platform_sh.php }
    - { resource: security.yml }
    - { resource: services.yml }
    - { resource: services.marketplace.yml }
    - { resource: services.commands.yml }
    - { resource: services.psp_callbacks.yml }
    - { resource: services.system_options.yml }
    - { resource: services.rulerz.yml }
    - { resource: '@AppBundle/Resources/config/services.cache_redis.yml' }
    - { resource: parameters.swiftmailer.php}
    - { resource: packages/messenger.yaml }
    - { resource: services.yavin.yml }
    - { resource: storage.flysystem.yaml }

framework:
    router:
        resource: "%kernel.project_dir%/app/config/routing.yml"
        strict_requirements: ~
    form:
    csrf_protection:
    validation:
    session:
        handler_id: app.session_handler
        storage_id: session.storage.cscart_bridge
        cookie_lifetime: '%session_cookie_lifetime%'
        gc_divisor: 10
    serializer:
    templating:
        engines: ['smarty', 'twig']
    assets:
        base_path: '/assets'
        version: "%marketplace.version%"
    translator: { fallbacks: ["%locale%"] }
    default_locale: "%locale%"

monolog:
    channels: ['sentinel','worker','audit','psp','functional']

# Twig Configuration
twig:
    globals:
        search_prefix: "%algolia.api_index_prefix%"
        isDev: false
        currencySign: '%currency.sign%'
        currencyCode: '%currency.code%'
        googleMapsApiKey: '%googlemaps.api_key%'
        messageAttachmentService: "@marketplace.message_attachment.message_attachment_service"
    debug:            "%kernel.debug%"
    strict_variables: "%kernel.debug%"
    paths:
        "%kernel.project_dir%/src/%theme_bundle%/Resources/views": App
    form_themes:
        - '@App/Form/feature_row.html.twig'
        - '@App/Form/checkbox_row.html.twig'
        - '@App/Form/choice_widget_expanded.html.twig'

doctrine:
    dbal:
        server_version: "%mysql_version%"
        host:     "%database_host%"
        port:     "%database_port%"
        dbname:   "%database_name%"
        user:     "%database_user%"
        password: "%database_password%"
        charset:  utf8mb4
        logging: false
        driver: pdo_mysql
        mapping_types:
            enum: string
        # Doctrine should care only about tables starting with the doctrine table prefix
        schema_filter: "/^(?=%doctrine_table_prefix%).+|%cscart_table_prefix%(%doctrine.mapped_cscart_tables.regex%)$/"
        options:
            # 1002 = \PDO::MYSQL_ATTR_INIT_COMMAND
            1002: "SET sql_mode = ''; SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;"
        types:
            money:
                class: Wizacha\Money\MoneyDoctrineType
            precise_money:
                class: Wizacha\Money\PreciseMoneyDoctrineType
            exchange_rate:
                class: Wizacha\Marketplace\Currency\ExchangeRateDoctrineType
            php_enum_promotion_type:
                class: Wizacha\Marketplace\Promotion\PromotionTypeEnumType
            php_enum_price_fields:
                class: Wizacha\Marketplace\Price\PriceFieldsEnumType
            bonus_target:
                class: Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetDoctrineType
            datetime_utc:
                class: Wizacha\Bridge\Doctrine\UTCDateTimeType
            multi_vendor_product_status:
                class: Wizacha\Marketplace\PIM\MultiVendorProduct\Status\StatusType
            locale:
                class: Wizacha\Component\Locale\Bridge\Doctrine\LocaleType
            organisation_status:
                class: Wizacha\Marketplace\Organisation\Doctrine\OrganisationStatusType
            user_type:
                class: Wizacha\Marketplace\User\Doctrine\UserType
            ramsey_uuid:
                class: Rhumsaa\Uuid\Doctrine\UuidType
            php_enum_transaction_type:
                class: Wizacha\Marketplace\Transaction\DoctrineType\TransactionTypeEnumType
            php_enum_transaction_status:
                class: Wizacha\Marketplace\Transaction\DoctrineType\TransactionStatusEnumType
            php_enum_order_refund_status:
                class: Wizacha\Marketplace\Order\Refund\DoctrineType\OrderRefundStatusEnumType
            php_enum_refund_status:
                class: Wizacha\Marketplace\Order\Refund\DoctrineType\RefundStatusEnumType
            php_enum_order_attachment_type:
                class: Wizacha\Marketplace\Order\OrderAttachment\DoctrineType\OrderAttachmentEnumType
            php_enum_tax_rate_type:
                class: Wizacha\Marketplace\PIM\Tax\DoctrineType\TaxRateTypeEnumType
            php_enum_order_status:
                class: Wizacha\Marketplace\Order\DoctrineType\OrderStatusEnumType
            php_enum_mandate_status:
                class: Wizacha\Marketplace\Payment\DoctrineType\MandateStatusEnumType
            php_enum_company_person_type:
                class: Wizacha\Marketplace\CompanyPerson\DoctrineType\CompanyPersonEnumType
            php_enum_related_products_type:
                class: Wizacha\Marketplace\RelatedProduct\DoctrineType\RelatedProductsEnumType

    orm:
        auto_generate_proxy_classes: "%kernel.debug%"
        naming_strategy: doctrine.orm.naming_strategy.underscore
        auto_mapping: false
        mappings:
            tree:
                type: annotation
                dir: "%kernel.project_dir%/vendor/gedmo/doctrine-extensions/lib/Gedmo/Tree/Entity"
                is_bundle: false
                prefix: Gedmo\Tree\Entity
                alias: Gedmo
            Promotion:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Promotion/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Promotion
                alias: Promotion
            Video:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PIM/Video/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PIM\Video
                alias: Video
            MailingList:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/MailingList/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\MailingList
                alias: MailingList
            Commission:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Commission/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Commission
                alias: Commission
            Payment:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Payment/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Payment
                alias: Payment
            MultiVendorProduct:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PIM/MultiVendorProduct/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PIM\MultiVendorProduct
                alias: MultiVendorProduct
            MultiVendorProductSyncRules:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PIM/MultiVendorProduct/ProductSynchronization/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PIM\MultiVendorProduct\ProductSynchronization
                alias: MultiVendorProductSyncRules
            Link:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PIM/MultiVendorProduct/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PIM\MultiVendorProduct
                alias: Link
            Organisation:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Organisation/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Organisation
            Division:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Division/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Division
            EximJob:
                type: yml
                dir: "%kernel.project_dir%/src/Component/Import/Mapping"
                is_bundle: false
                prefix: Wizacha\Component\Import
            EximJobLog:
                type: yml
                dir: "%kernel.project_dir%/src/Component/Import/Mapping"
                is_bundle: false
                prefix: Wizacha\Component\Import
            DolistTemplate:
                type: yml
                dir: "%kernel.project_dir%/src/Component/Dolist/Mapping"
                is_bundle: false
                prefix: Wizacha\Component\Dolist\Entities
            PriceTier:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PriceTier/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PriceTier
            Currency:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Currency/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Currency
            Transaction:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Transaction/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Transaction
            Token:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Order/Token/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Order\Token
            AuthLog:
                type: yml
                dir: "%kernel.project_dir%/src/Component/AuthLog/Mapping"
                is_bundle: false
                prefix: Wizacha\Component\AuthLog
            CreditCard:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/CreditCard/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\CreditCard
                alias: CreditCard
            Subscription:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Subscription/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Subscription
                alias: Subscription
            subscriptionActionTrace:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Subscription/Log/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Subscription\Log
                alias: Subscription
            Refund:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Order/Refund/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Order\Refund\Entity
            ProductModerationInProgress:
                type: yml
                dir: "%kernel.project_dir%/src/Premoderation/ProductModeration/Mapping"
                is_bundle: false
                prefix: Wizacha\Premoderation\ProductModeration
            OrderAttachment:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Order/OrderAttachment/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Order\OrderAttachment\Entity
            OrderAmounts:
                type: annotation
                dir: "%kernel.project_dir%/src/Marketplace/Order/AmountsCalculator/Entity"
                is_bundle: false
                prefix: Wizacha\Marketplace\Order\AmountsCalculator\Entity
            AmountItem:
                type: annotation
                dir: "%kernel.project_dir%/src/Marketplace/Order/AmountsCalculator/Entity"
                is_bundle: false
                prefix: Wizacha\Marketplace\Order\AmountsCalculator\Entity
            ShippingAmounts:
                type: annotation
                dir: "%kernel.project_dir%/src/Marketplace/Order/AmountsCalculator/Entity"
                is_bundle: false
                prefix: Wizacha\Marketplace\Order\AmountsCalculator\Entity
            OrderItemData:
                type: annotation
                dir: "%kernel.project_dir%/src/Marketplace/Order/AmountsCalculator/Entity"
                is_bundle: false
                prefix: Wizacha\Marketplace\Order\AmountsCalculator\Entity
            MessageAttachment:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/MessageAttachment/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\MessageAttachment\Entity
            OrderAction:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Order/OrderAction/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Order\OrderAction\Entity
            UserMandate:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Payment/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Payment
                alias: UserMandate
            UserPaymentInfo:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Payment/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Payment
                alias: UserPaymentInfo
            CompanyPerson:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/CompanyPerson/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\CompanyPerson
            UboMangopay:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PSP/UboMangopay/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PSP\UboMangopay
            RelatedProduct:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/RelatedProduct/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\RelatedProduct
            ShippingTax:
                type: annotation
                dir: "%kernel.project_dir%/src/Marketplace/Tax"
                is_bundle: false
                prefix: Wizacha\Marketplace\Tax
            NotificationConfig:
                type: yml
                dir: "%kernel.project_dir%/src/AppBundle/Notification/Mapping"
                is_bundle: false
                prefix: Wizacha\AppBundle\Notification
            # Cs cart tables
            User:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/User/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\User
                alias: User
            UserPasswordHistory:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/User/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\User
                alias: User
            Category:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PIM/Category/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PIM\Category
                alias: Category
            CategoryDescription:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PIM/Category/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PIM\Category
                alias: CategoryDescription
            Product:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PIM/Product/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PIM\Product
                alias: Product
            Company:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PIM/Company/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PIM\Company
                alias: Company
            Tax:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PIM/Tax/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PIM\Tax
                alias: Tax
            ProductOptionInventory:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/ProductOptionInventory/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\ProductOptionInventory
            TaxDescription:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PIM/Tax/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PIM\Tax
                alias: TaxDescription
            TaxRate:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/PIM/Tax/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\PIM\Tax
                alias: TaxRate
            OrderAdjustment:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Order/Adjustment/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Order\Adjustment
                alias: OrderAdjustment
            DebouncedJob:
                type: yml
                dir: "%kernel.project_dir%/src/Async/Debouncer/Mapping"
                is_bundle: false
                prefix: Wizacha\Async\Debouncer
                alias: DebouncedJob
            Country :
                type : yml
                dir : "%kernel.project_dir%/src/Marketplace/Country/Mapping"
                is_bundle : false
                prefix : Wizacha\Marketplace\Country
                alias : Country
            CountryDescriptions :
                type : yml
                dir : "%kernel.project_dir%/src/Marketplace/Country/Mapping"
                is_bundle : false
                prefix : Wizacha\Marketplace\Country
                alias : CountryDescriptions
            Group:
                type: yml
                dir: "%kernel.project_dir%/src/Marketplace/Group/Mapping"
                is_bundle: false
                prefix: Wizacha\Marketplace\Group
                alias: Group
            QuoteRequestSelection:
                type: annotation
                dir: "%kernel.project_dir%/src/Marketplace/Quotation/Entity"
                is_bundle: false
                prefix: Wizacha\Marketplace\Quotation\Entity
            QuoteRequestSelectionDeclination:
                type: annotation
                dir: "%kernel.project_dir%/src/Marketplace/Quotation/Entity"
                is_bundle: false
                prefix: Wizacha\Marketplace\Quotation\Entity
            Discussion:
                type: yml
                dir: "%kernel.project_dir%/src/Discuss/Entity/Mapping"
                is_bundle: false
                prefix: Wizacha\Discuss\Entity
            DiscussionInternal:
                type: yml
                dir: "%kernel.project_dir%/src/Discuss/Internal/Entity/Mapping"
                is_bundle: false
                prefix: Wizacha\Discuss\Internal\Entity

broadway:
    command_handling:
        logger: logger

services:
    session.storage.cscart_bridge:
        class: Wizacha\Bridge\Symfony\CsCartSessionStorage
        arguments:
            - "%session.storage.options%"
            - "@session.handler"
            - "@session.storage.metadata_bag"

    # on injecte la définition du translator de Symfony en le décorant, pour
    # pouvoir fallback sur le catalogue des traductions de Symfony quand on
    # ne trouve pas de correspondance dans CsCart.
    # Voir : http://symfony.com/doc/current/service_container/service_decoration.html
    decorating_translator:
        class: Wizacha\Bridge\Symfony\Translator
        decorates: translator
        arguments:
            - "@decorating_translator.inner" # alias to old @translator
            - "@doctrine.dbal.default_connection"
            - "%kernel.cache_dir%/translations"
            - "%kernel.debug%"
        public: false
        tags:
            - { name: monolog.logger, channel: translation }

    # ce loader sert pour savoir les traductions qui manquent dans le profiler
    translation.loader.database:
        class: Wizacha\AppBundle\Translation\DatabaseLoader
        arguments:
            - "@doctrine.dbal.default_connection"
        tags:
            - { name: translation.loader, alias: database }

    translation.extractor.cscart:
        class: Wizacha\AppBundle\Translation\CsCartExtractor
        tags:
            - { name: translation.extractor, alias: cscart }

    translation.extractor.smarty:
        class: Wizacha\AppBundle\Translation\SmartyExtractor
        arguments:
            - "@templating.smarty"
            - "%kernel.project_dir%"
        tags:
            - { name: translation.extractor, alias: smarty }

    marketplace.doctrine_table_prefixer:
        class: Wizacha\Bridge\Doctrine\TablePrefixer
        arguments:
            - '%doctrine_table_prefix%'
            - '%cscart_table_prefix%'
            - "/^(%doctrine.mapped_cscart_tables.regex%)$/"
        tags:
            - { name: doctrine.event_subscriber }

    gedmo.listener.tree:
        class: Gedmo\Tree\TreeListener
        tags:
            - { name: doctrine.event_subscriber, connection: default, priority: 10 }

knp_snappy:
    pdf:
        enabled:    true
        binary:     "%wkhtmltopdf_binary_path%"
        options:    []
    image:
        enabled:    false

swiftmailer:
    default_mailer: mailer
    mailers:
        mailer:
            transport: '%mailer_transport%'
            host:      '%mailer_host%'
            port:      '%mailer_port%'
            username:  '%mailer_user%'
            password:  '%mailer_password%'
            encryption: '%mailer_encryption%'
        fake_mailer:
            disable_delivery: true
            logging: true

parameters:
    doctrine.mapped_cscart_tables.regex: "users|products|product_descriptions|product_options_inventory|categories|category_descriptions|products_categories|companies|taxes|tax_descriptions|tax_rates|order_adjustment|countries|country_descriptions|user_profiles"
    base_url: "//%http_host%%http_path%/"
    router.request_context.host: '%http_host%'
    router.request_context.scheme: https
    router.request_context.base_url: '%http_path%'

nelmio_cors:
    paths:
        '^/api/':
            allow_origin: ['*']
            allow_methods: ['POST', 'PUT', 'GET', 'DELETE', 'PATCH']
            allow_headers: ['Authorization', 'Content-Type']
            forced_allow_origin_value: '*'

snc_redis:
    clients:
        default:
            type: phpredis
            alias: default
            dsn: redis://%redis.url%
            logging: "%kernel.debug%"
            options:
                prefix: '%redis.default.prefix%'
        cscart_sessions:
            type: phpredis
            alias: cscart_sessions
            dsn: redis://%redis.url%
            logging: "%kernel.debug%"
            options:
                prefix: '%redis.cscart_sessions.prefix%'
        handlers:
            type: phpredis
            alias: handlers
            dsn: redis://%redis.url%
            logging: "%kernel.debug%"
            options:
                prefix: '%redis.handlers.prefix%'
        locks:
            type: phpredis
            alias: locks
            dsn: redis://%redis.url%
            logging: "%kernel.debug%"
            options:
                prefix: '%redis.locks.prefix%'

# full configuration reference: http://htmlpurifier.org/live/configdoc/plain.html
exercise_html_purifier:
    default_cache_serializer_path: '%kernel.cache_dir%/htmlpurifier'
    html_profiles:
        default:
            config:
                Core.Encoding: 'UTF-8'
                HTML.AllowedAttributes:
                    - a.href
                    - a.target
                    - img.src
                    - img.width
                    - img.height
                    - '*.style'
                    - '*.alt'
                    - '*.class'
                HTML.AllowedElements:
                    - a
                    - b
                    - br
                    - code
                    - div
                    - em
                    - h1
                    - h2
                    - h3
                    - h4
                    - h5
                    - h6
                    - i
                    - img
                    - li
                    - ol
                    - p
                    - span
                    - sub
                    - sup
                    - strong
                    - tr
                    - tbody
                    - td
                    - th
                    - thead
                    - table
                    - ul
                    - u
                Attr.AllowedFrameTargets: '_self,_blank,'
                URI.AllowedSchemes:
                    - data
                    - http
                    - https
