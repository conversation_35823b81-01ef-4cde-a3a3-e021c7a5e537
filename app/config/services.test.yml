parameters:
    env(MULTI_VENDOR_PRODUCT_RULES): 'ean,supplier_reference'
    marketplace.pim.product_templates: 'product,service'
    feature.subscription: false

services:
    test.redis_mock:
        class: M6Web\Component\RedisMock\RedisMock
        public: true

    # Make the service use the Redis mock
    marketplace.stock.domain_service:
        public: true
        class: Wizacha\Marketplace\PIM\Stock\StockService
        arguments:
            - '@doctrine.dbal.default_connection'
            - '@test.redis_mock'
            - '%redis.default.prefix%'
            - '%marketplace.stock.cart_time_limit%'

    marketplace.stock.domain_service.real:
        class: Wizacha\Marketplace\PIM\Stock\StockService
        public: true
        arguments:
            - '@doctrine.dbal.default_connection'
            - '@app.redis.default'
            - '%redis.default.prefix%'
            - '%marketplace.stock.cart_time_limit%'

    Wizacha\Marketplace\Payment\LemonWay\LemonWayConfig:
        arguments:
            - false
            - 'MKP'
            - 'wizaplace'
            - 'LemonWay Fake Iban'
            - 'LemonWay Fake Bic'
            - 'LemonWay Holder Name'
            - 'LemonWay Holder Address'
            - 'SUBVENTIONS'
            - 'EUR'

    Wizacha\Marketplace\Payment\Stripe\StripeConfig:
        class: Wizacha\Marketplace\Payment\Stripe\StripeConfig
        arguments:
            - 'sk_test_some_key'
            - 'EUR'
            - 'marketplace'

    HiPay\Fullservice\HTTP\Configuration\Configuration:
        arguments:
            - apiUsername: 'wizaplace'
              apiPassword: 'password'
              apiEnv: 'stage'

    marketplace.payment.hipay_cashout_client:
        class: GuzzleHttp\Client
        arguments:
            - base_uri: 'http://test.hipay.loc/api/'
              auth: ['wizaplace', 'password']

    marketplace.payment.hipay_api:
        class: Wizacha\Test\Mock\FakeHiPayApi
        arguments:
            - '@marketplace.payment.hipay_client'

    Wizacha\Marketplace\Payment\SMoney\SMoneyApi:
        arguments:
            - ''
            - ''
            - '@logger'
            - '@event_dispatcher'

    marketplace.order.refund.refund_config:
        class: Wizacha\Marketplace\Order\Refund\Utils\RefundConfig
        autoconfigure: true
        arguments:
            - 'NONE'

    marketplace.payment.mangopay_config:
        class: Wizacha\Marketplace\Payment\Mangopay\MangoPayApiConfig
        arguments:
            - ''
            - ''
            - ''
            - 'FORCE'
            - 'EUR'

    basic.configuration_service:
        class: Wizacha\AppBundle\Service\ConfigurationService
        public: true
        arguments:
            - '@marketplace.theme_customizer'
            - '@marketplace.asset_manager'
            - '@app.setting_storage'

    app.validator.url_validator:
        public: true
        class: Wizacha\Test\Mock\FakeUrlValidator

    Wizacha\Marketplace\Order\AmountsCalculator\Service\OrderAmountsCalculator:
        public: true
        autowire: true
        arguments:
            - '30-05-2021 00:00:00'
            - '@order_amount_repository'
            - '@marketplace.promotion.repository'
            - '@marketplace.order.order_service'
            - '@marketplace.commission.commission_service'
            - '@logger'
            - '@marketplace.international_tax.tax_repository'

    Wizacha\Marketplace\Order\Repository\OrderRepository:
        public: true
        autowire: true

    Wizacha\Marketplace\KPI\KPIPublishService:
        public: true
        autowire: true
        arguments:
            $configKPIWebhookURL: '%config.kpi.webhook.url%'
            $marketplaceProjectName: '%marketplace.project_name%'
            $urlValidator: '@app.validator.url_validator'
            $processorService: '@Wizacha\Marketplace\Payment\Processor\ProcessorService'

    marketplace.payment.logger:
        public: true
        abstract: true
        class: Wizacha\Marketplace\Payment\AbstractPaymentLogger
        arguments:
            - '@monolog.logger.psp'
            - '@serializer'

    Wizacha\Test\PHPUnit\Component\BytesGenerator\StaticBytesGenerator:
        public: true

    Wizacha\Marketplace\User\UserService:
        public: true
        arguments:
            - '@marketplace.user.user_repository'
            - '@event_dispatcher'
            - '@Wizacha\Marketplace\Currency\CurrencyService'
            - '@Wizacha\Marketplace\Subscription\SubscriptionRepository'
            - '@Wizacha\Cscart\FnFunctions'
            - '@Wizacha\Marketplace\Country\CountryService'
            - '@app.feature_flag_service'
            - '@marketplace.ekey.repository'
            - '@Wizacha\Test\PHPUnit\Component\BytesGenerator\StaticBytesGenerator'
            - '%password_renewal_time_limit%'
