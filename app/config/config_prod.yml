imports:
    - { resource: config.yml }
    - { resource: parameters.monolog.php }

framework:
    session:
        cookie_secure: true

monolog:
    handlers:
        verbose: # these channels write all their logs
            type:         stream
            path:         "%monolog.path%"
            level:        DEBUG
            formatter:    monolog.formatter.logstash
            channels:     ['mailer','sentinel','psp','functional']
            bubble:       false
        main: # the main channels (app, request, console, security...) write their logs when an error is encountered
            type:         fingers_crossed
            action_level: ERROR
            handler:      logstash
            buffer_size:  50
        logstash:
            type:         stream
            path:         "%monolog.path%"
            level:        DEBUG
            formatter:    monolog.formatter.logstash
