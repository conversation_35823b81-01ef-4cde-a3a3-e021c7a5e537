services:

    Wizacha\AppBundle\Fixture\:
        resource: '../../src/AppBundle/Fixture/*'
        autowire: true
        public: true

    Faker\Generator:
        class: Faker\Generator
        public: true
        factory: ['Faker\Factory', create]
        arguments: ['fr_FR']
        calls:
            # Seed fix pour du fixed random
            - [seed, [42]]

    Wizacha\Test\Fixture\User\UserLoader:
        arguments: ['@marketplace.test.company_loader']
    marketplace.test.user_loader:
        alias: Wizacha\Test\Fixture\User\UserLoader
        public: true
    Wizacha\Test\Fixture\Company\CompanyLoader:
        arguments: ['@database_connection']
    marketplace.test.company_loader:
        alias: Wizacha\Test\Fixture\Company\CompanyLoader
        public: true
    Wizacha\Test\Fixture\Order\OrderLoader:
        arguments:
            - '@marketplace.test.user_loader'
            - '@marketplace.test.payment_loader'
            - '@marketplace.test.company_loader'
            - '@marketplace.test.product_loader'
            - '@marketplace.test.shipping_loader'
            - '@marketplace.basket.domain_service'
            - '@marketplace.basket.checkout'
            - '@marketplace.payment.payment_service'
            - '@marketplace.test.promotion_loader'
            - '@Wizacha\Test\Fixture\MultiVendorProduct\MultiVendorProductLoader'
            - '@marketplace.test.multi_vendor_product_link_loader'
            - '@marketplace.test.category_loader'
            - '@marketplace.test.option_loader'
            - '@marketplace.test.transaction_loader'
            - '@service_container'
    marketplace.test.order_loader:
        alias: Wizacha\Test\Fixture\Order\OrderLoader
        public: true
    marketplace.test.order_return_loader:
        class: Wizacha\Test\Fixture\Order\OrderReturnLoader
        public: true
        arguments:
            - '@marketplace.test.order_loader'
            - '@marketplace.order.order_service'
            - '@marketplace.order_return.service'
    marketplace.test.order_adjustment_loader:
        class: Wizacha\Test\Fixture\Order\OrderAdjustmentLoader
        autowire: true
        public: true

    Wizacha\Test\Fixture\Payment\PaymentLoader:
        arguments: ['@database_connection']
    marketplace.test.payment_loader:
        alias: Wizacha\Test\Fixture\Payment\PaymentLoader
        public: true
    Wizacha\Test\Fixture\Category\CategoryLoader:
    marketplace.test.category_loader:
        alias: Wizacha\Test\Fixture\Category\CategoryLoader
        public: true
    Wizacha\Test\Fixture\Product\ProductLoader:
        arguments:
            - '@marketplace.test.category_loader'
            - '@marketplace.test.company_loader'
            - '@database_connection'
            - '@marketplace.pim.product.service'
            - '@doctrine.orm.entity_manager'
            - '@marketplace.product.productservice'
            - '@marketplace.review.product.service'
            - '@marketplace.test.division_loader'
            - '@marketplace.test.price_tier_loader'
            - '@event_dispatcher'
    marketplace.test.product_loader:
        alias: Wizacha\Test\Fixture\Product\ProductLoader
        public: true
    Wizacha\Test\Fixture\Image\ImageLoader:
        arguments: ['@image.manager', '%kernel.project_dir%']
    marketplace.test.image_loader:
        alias: Wizacha\Test\Fixture\Image\ImageLoader
        public: true
    Wizacha\Test\Fixture\Promotion\PromotionLoader:
        arguments: ['@marketplace.promotion.promotionservice']
    marketplace.test.promotion_loader:
        alias: Wizacha\Test\Fixture\Promotion\PromotionLoader
        public: true
    Wizacha\Test\Fixture\Tax\TaxLoader: ~
    marketplace.test.tax_loader:
        alias: Wizacha\Test\Fixture\Tax\TaxLoader
        public: true
    marketplace.test.option_loader:
        class: Wizacha\Test\Fixture\Product\Option\OptionLoader
        public: true
    Wizacha\Test\Fixture\Product\Attribute\AttributeLoader:
        public: true
        arguments:
            - '@database_connection'
            - '@marketplace.pim.attribute_service'
            - '@marketplace.test.image_loader'
    marketplace.test.attribute_loader:
        alias: Wizacha\Test\Fixture\Product\Attribute\AttributeLoader
        public: true
    marketplace.test.banner_loader:
        class: Wizacha\Test\Fixture\Banner\BannerLoader
        public: true
    Wizacha\Test\Fixture\Shipping\ShippingLoader:
        public: true
    marketplace.test.shipping_loader:
        alias: Wizacha\Test\Fixture\Shipping\ShippingLoader
        public: true
    marketplace.test.shipment_loader:
        class: Wizacha\Test\Fixture\Shipment\ShipmentLoader
        public: true
    marketplace.test.page_loader:
        class: Wizacha\Test\Fixture\CMS\PageLoader
        public: true

    Wizacha\Test\Fixture\MultiVendorProduct\MultiVendorProductLoader:
        public: true
        arguments:
            - '@marketplace.multi_vendor_product.service'
            - '@marketplace.test.category_loader'
            - '@marketplace.product.productservice'
            - '@marketplace.review.product.service'
            - '@event_dispatcher'
    marketplace.test.video_loader:
        class: Wizacha\Test\Fixture\Video\VideoLoader
        public: true
        arguments:
            - '@marketplace.pim.video_repository'
    marketplace.test.multi_vendor_product_link_loader:
        class: Wizacha\Test\Fixture\MultiVendorProduct\LinkLoader
        public: true
        arguments:
            - '@marketplace.multi_vendor_product_link.service'
            - '@Wizacha\Test\Fixture\MultiVendorProduct\MultiVendorProductLoader'
            - '@marketplace.test.product_loader'
    marketplace.test.thread_loader:
        class: Wizacha\Test\Fixture\Review\ThreadLoader
        public: true
    marketplace.test.review_loader:
        class: Wizacha\Test\Fixture\Review\ReviewLoader
        public: true
        arguments: ['@marketplace.user.user_service']
    marketplace.test.exim_job_loader:
        class: Wizacha\Test\Fixture\Exim\EximJobLoader
        public: true
        arguments:
            - '@marketplace.import.job_service'
            - '@marketplace.test.user_loader'
            - '@marketplace.test.company_loader'
    marketplace.test.division_loader:
        class: Wizacha\Test\Fixture\Division\DivisionLoader
        public: true
        arguments:
            - '@marketplace.division.division_repository'
            - '@marketplace.division.service'
            - '@marketplace.division.products.service'
            - '@marketplace.division.blacklists.service'
            - '@event_dispatcher'
    marketplace.test.price_tier_loader:
        class: Wizacha\Test\Fixture\PriceTier\PriceTierLoader
        public: true
        arguments: ['@database_connection']
        autowire: true

    Wizacha\Test\Fixture\CreditCard\CreditCardLoader:
        autowire: true
        public: true
    marketplace.test.credit_card_loader:
        alias: Wizacha\Test\Fixture\CreditCard\CreditCardLoader
        public: true

    Wizacha\Test\Fixture\Currency\CurrencyLoader:
        autowire: true
        public: true

    marketplace.test.language_loader:
        class: Wizacha\Test\Fixture\Language\LanguageLoader
        public: true

    Wizacha\Test\Fixture\Transaction\TransactionLoader:
        autowire: true
    marketplace.test.transaction_loader:
        alias: Wizacha\Test\Fixture\Transaction\TransactionLoader
        public: true

    marketplace.test.subscription_loader:
        class: Wizacha\Test\Fixture\Subscription\SubscriptionLoader
        autowire: true
        public: true

    marketplace.test.refund_loader:
        class: Wizacha\Test\Fixture\Refund\RefundLoader
        autowire: true
        public: true

    marketplace.test.related_product_loader:
        class: Wizacha\Test\Fixture\RelatedProduct\RelatedProductLoader
        autowire: true
        public: true

    marketplace.test.notification_config_loader:
        class: Wizacha\Test\Fixture\NotificationConfig\NotificationConfigLoader
        autowire: true
        public: true
