<?php

use Broadway\Bundle\BroadwayBundle\BroadwayBundle;
use BroadwaySerialization\SymfonyIntegration\BroadwaySerializationBundle;
use Doctrine\Bundle\DoctrineBundle\DoctrineBundle;
use Exercise\HTMLPurifierBundle\ExerciseHTMLPurifierBundle;
use FOS\JsRoutingBundle\FOSJsRoutingBundle;
use Knp\Bundle\SnappyBundle\KnpSnappyBundle;
use Monolog\ErrorHandler;
use Nelmio\CorsBundle\NelmioCorsBundle;
use Psr\Log\LogLevel;
use Snc\RedisBundle\SncRedisBundle;
use Symfony\Bundle\FrameworkBundle\FrameworkBundle;
use Symfony\Bundle\MonologBundle\MonologBundle;
use Symfony\Bundle\SecurityBundle\SecurityBundle;
use Symfony\Bundle\SwiftmailerBundle\SwiftmailerBundle;
use Symfony\Bundle\TwigBundle\TwigBundle;
use Symfony\Component\Config\Loader\LoaderInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpKernel\Kernel;
use Symfony\Component\Messenger\DependencyInjection\MessengerPass;
use Wizacha\AppBundle\AppBundle;
use Wizacha\Bridge\Symfony\BundleWithDependencies;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\Action\OrderWorkflowActionInterface;
use Wizacha\Marketplace\Payment\Handler\CallbackHandlerInterface;
use Wizacha\Marketplace\PIM\Option\Handler\SystemOptionsHandlerInterface;

class AppKernel extends Kernel
{
    public function registerBundles()
    {
        $bundles = [
            new FrameworkBundle(),
            new SecurityBundle(),
            new BroadwayBundle(),
            new DoctrineBundle(),
            new MonologBundle(),
            new SncRedisBundle(),
            new BroadwaySerializationBundle(),
            new TwigBundle(),
            new KnpSnappyBundle(),
            new FOSJsRoutingBundle(),
            new SwiftmailerBundle(),
            new NelmioCorsBundle(),
            new AppBundle(),
            new ExerciseHTMLPurifierBundle(),
        ];

        require_once __DIR__ . '/config/theme.php';
        $theme = ucfirst(THEME);
        if ($theme) {
            $clientBundle = sprintf('Wizacha\%sBundle\%sBundle', $theme, $theme);
            $clientBundle = new $clientBundle();

            if ($clientBundle instanceof BundleWithDependencies) {
                $bundles = array_merge($bundles, $clientBundle->getRequiredBundles());
            }

            $bundles[] = $clientBundle;
        }

        if (in_array($this->getEnvironment(), ['dev', 'test'], true)) {
            $bundles[] = new Symfony\Bundle\DebugBundle\DebugBundle();
            $bundles[] = new Symfony\Bundle\WebProfilerBundle\WebProfilerBundle();
            $bundles[] = new Wizacha\WizaplaceDebugBundle\WizaplaceDebugBundle();
        }

        return $bundles;
    }

    public function boot()
    {
        if (true === $this->booted) {
            return;
        }

        parent::boot();

        if (true === $this->isDebug()){
            ErrorHandler::register(
                $this->getContainer()->get('logger'),
                [
                    E_DEPRECATED => LogLevel::WARNING,
                    E_USER_DEPRECATED => LogLevel::WARNING,
                ],
                LogLevel::ERROR,
                LogLevel::ALERT
            );
        }

        GlobalState::init(
            new Locale($this->getContainer()->getParameter('locale')),
            new Locale($this->getContainer()->getParameter('locale')),
            $this->getContainer()->get('translator')
        );
    }

    /**
     * @deprecated
     * @see getProjectDir
     */
    public function getRootDir()
    {
        return __DIR__;
    }

    public function getProjectDir()
    {
        return dirname(__DIR__);
    }

    public function getCacheDir()
    {
        return dirname(__DIR__) . '/var/cache/' . $this->environment;
    }

    public function getLogDir()
    {
        return dirname(__DIR__) . '/var/logs';
    }

    public function registerContainerConfiguration(LoaderInterface $loader)
    {
        $loader->load($this->getProjectDir() . '/app/config/config_' . $this->getEnvironment() . '.yml');
    }

    protected function build(ContainerBuilder $container)
    {
        // Our system does not allow to use _instanceof in services.yaml
        // To avoid this, we register how Symfony need to use autoconfigure flag with class who implement this interface
        $container
            ->registerForAutoconfiguration(CallbackHandlerInterface::class)
            ->addTag('psp.callback.handler')
        ;

        $container
            ->registerForAutoconfiguration(SystemOptionsHandlerInterface::class)
            ->addTag('system_options.handler')
        ;

        $container
            ->registerForAutoconfiguration(OrderWorkflowActionInterface::class)
            ->addTag('marketplace.workflow.action')
        ;

        $container->addCompilerPass(new MessengerPass());
    }
}
