<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ne<PERSON>    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;

include_once(Registry::get('config.dir.schemas') . 'exim/orders.functions.php');

$schema = array(
    'section' => 'orders',
    'pattern_id' => 'orders',
    'name' => __('orders'),
    'key' => array('order_id'),
    'order' => 0,
    'table' => 'orders',
    'condition' => array(
        'conditions' => array('is_parent_order' => 'N'),
        'use_company_condition' => true,
    ),
    'range_options' => array(
        'selector_url' => 'orders.manage',
        'object_name' => __('orders'),
    ),
    'options' => array(
        'attachment_delimiter' => array(
            'title' => 'attachment_delimiter',
            'description' => 'text_attachment_delimiter',
            'type' => 'input',
            'default_value' => '///'
        ),
    ),
    'export_fields' => array(
        'Order ID' => array(
            'db_field' => 'order_id',
            'alt_key' => true,
            'required' => true,
        ),
        'E-mail' => array(
            'db_field' => 'email',
            'required' => true,
        ),
        'User ID' => array(
            'db_field' => 'user_id'
        ),
        'Total' => array(
            'process_get' => array('\Wizacha\Exim\Order::getTotal', '#key'),
            'linked' => false,
        ),
        'Subtotal' => array(
            'process_get' => array('\Wizacha\Exim\Order::getSubTotal', '#key'),
            'linked' => false,
        ),
        'Discount' => array(
            'process_get' => array('\Wizacha\Exim\Order::getDiscount', '#key'),
            'linked' => false,
        ),
        'Promotion ID' => array(
            'process_get' => array('\Wizacha\Exim\Order::getOrderPromotionId', '#key'),
            'linked' => false,
        ),
        'Promotion name' => array(
            'process_get' => array('\Wizacha\Exim\Order::getOrderPromotionName', '#key'),
            'linked' => false,
        ),
        'Marketplace promotion ID' => array(
            'process_get' => array('\Wizacha\Exim\Order::getOrderMarketplacePromotionId', '#key'),
            'linked' => false,
        ),
        'Marketplace promotion code' => array(
            'process_get' => array('\Wizacha\Exim\Order::getOrderMarketplacePromotionCoupon', '#key'),
            'linked' => false,
        ),
        'Total marketplace promotion' => array(
            'process_get' => array('\Wizacha\Exim\Order::getMarketplaceDiscountTotal', '#key'),
            'linked' => false,
        ),
        'Total paid by customer' => array(
            'process_get' => array('\Wizacha\Exim\Order::getCustomerTotal', '#key'),
            'linked' => false,
        ),
        'Payment surcharge' => array(
            'db_field' => 'payment_surcharge'
        ),
        'Due date' => array(
            'db_field' => 'commitment_date'
        ),
        'Shipping cost' => array(
            'process_get' => array('\Wizacha\Exim\Order::getShippingCost', '#key'),
            'linked' => false,
        ),
        'Date' => array(
            'db_field' => 'timestamp',
            'process_get' => array('fn_timestamp_to_date', '#this'),
            'convert_put' => array('fn_date_to_timestamp'),
        ),
        'Status' => array(
            'db_field' => 'status',
        ),
        'Notes' => array(
            'db_field' => 'notes',
        ),
        'Payment ID' => array(
            'db_field' => 'payment_id',
        ),
        'IP address' => array(
            'db_field' => 'ip_address',
        ),
        'Details' => array(
            'db_field' => 'details',
        ),
        'Payment information' => array(
            'linked' => false,
            'process_get' => array('fn_exim_orders_get_data', '#key', 'P'),
            'process_put' => array('fn_exim_orders_set_data', '#key', '#this', 'P')
        ),
        'Taxes' => array(
            'linked' => false,
            'process_get' => array('fn_exim_orders_get_data', '#key', 'T'),
            'process_put' => array('fn_exim_orders_set_data', '#key', '#this', 'T')
        ),
        'Coupons' => array(
            'linked' => false,
            'process_get' => array('fn_exim_orders_get_data', '#key', 'C'),
            'process_put' => array('fn_exim_orders_set_data', '#key', '#this', 'C')
        ),
        'Shipping' => array(
            'linked' => false,
            'process_get' => array('fn_exim_orders_get_data', '#key', 'L'),
            'process_put' => array('fn_exim_orders_set_data', '#key', '#this', 'L')
        ),
        'Invoice ID' => array(
            'linked' => false,
            'process_get' => array('fn_exim_orders_get_docs', '#key', 'I'),
            'process_put' => array('fn_exim_orders_set_docs', '#key', '#this', 'I')
        ),
        'Credit memo ID' => array(
            'linked' => false,
            'process_get' => array('fn_exim_orders_get_docs', '#key', 'C'),
            'process_put' => array('fn_exim_orders_set_docs', '#key', '#this', 'C')
        ),
        'First name' => array(
            'db_field' => 'firstname'
        ),
        'Last name' => array(
            'db_field' => 'lastname'
        ),
        'Company' => array(
            'db_field' => 'company_id',
            'process_get' => array('fn_get_company_name', '#this'),
            'convert_put' => array('fn_get_company_id_by_name', '#this'),
        ),
        'Fax' => array(
            'db_field' => 'fax'
        ),
        'Phone' => array(
            'db_field' => 'phone'
        ),
        'Web site' => array(
            'db_field' => 'url'
        ),
        'Tax exempt' => array(
            'db_field' => 'tax_exempt'
        ),
        'Language' => array(
            'db_field' => 'lang_code'
        ),
        'Documents' => array(
            'process_get' => array(
                '\Wizacha\Marketplace\Order\OrderService::getAttachments', '#key', '@attachment_delimiter'
            ),
            'linked' => false,
        ),
        'Customer: Is professional' => array(
            'db_field' => 'is_customer_professional',
        ),
        'Customer: Company' => array(
            'db_field' => 'customer_company',
        ),
        'Customer: Legal identifier' => array(
            'db_field' => 'customer_legal_identifier',
        ),
        'Customer: Intra european community VAT' => array(
            'db_field' => 'customer_intra_european_community_vat',
        ),
        'Customer: Job title' => array(
            'db_field' => 'customer_job_title',
        ),
        'Customer: Account comment' => array(
            'db_field' => 'customer_account_comment',
        ),
        'Customer: External identifier' => array(
            'db_field' => 'customer_external_identifier',
        ),
        'Billing: company name' => array(
            'db_field' => 'b_company',
        ),
        'Billing: first name' => array(
            'db_field' => 'b_firstname',
        ),
        'Billing: title' => array(
            'db_field' => 'b_title',
        ),
        'Billing: last name' => array(
            'db_field' => 'b_lastname',
        ),
        'Billing: address' => array(
            'db_field' => 'b_address',
        ),
        'Billing: address (line 2)' => array(
            'db_field' => 'b_address_2',
        ),
        'Billing: city' => array(
            'db_field' => 'b_city',
        ),
        'Billing: state' => array(
            'db_field' => 'b_state',
        ),
        'Billing: country' => array(
            'db_field' => 'b_country',
        ),
        'Billing: zipcode' => array(
            'db_field' => 'b_zipcode',
        ),
        'Shipping: company name' => array(
            'db_field' => 's_company',
        ),
        'Shipping: title' => array(
            'db_field' => 's_title',
        ),
        'Shipping: first name' => array(
            'db_field' => 's_firstname',
        ),
        'Shipping: last name' => array(
            'db_field' => 's_lastname',
        ),
        'Shipping: address' => array(
            'db_field' => 's_address',
        ),
        'Shipping: address (line 2)' => array(
            'db_field' => 's_address_2',
        ),
        'Shipping: city' => array(
            'db_field' => 's_city',
        ),
        'Shipping: state' => array(
            'db_field' => 's_state',
        ),
        'Shipping: country' => array(
            'db_field' => 's_country',
        ),
        'Shipping: zipcode' => array(
            'db_field' => 's_zipcode',
        ),
        'Extra fields' => array(
            'linked' => false,
            'process_get' => array('fn_exim_orders_get_extra_fields', '#key', '#lang_code'),
            'process_put' => array('fn_exim_orders_set_extra_fields', '#this', '#key', '#lang_code')
        ),
        'Subscription Id' => array(
            'db_field' => 'subscription_id',
            'export_only' => true,
        ),
        'Totals: excluding_taxes' => array(
            'process_get' => array('\Wizacha\Exim\Order::getTotalExcludingTaxes', '#key'),
            'linked' => false,
        ),
        'Billing: phone' => array(
            'db_field' => 'b_phone',
        ),
        'Shipping: phone' => array(
            'db_field' => 's_phone',
        ),
        'Extra data' => array(
            'process_get' => array('\Wizacha\Exim\Order::fnEximOrdersGetExtraData', '#key'),
            'linked' => false,
        ),
    ),
);
return $schema;
