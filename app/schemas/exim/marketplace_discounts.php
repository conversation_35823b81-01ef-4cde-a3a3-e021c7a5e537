<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;

$schema = [
    'section' => 'marketplace_discounts',
    'name' => __('marketplace_discounts'),
    'pattern_id' => 'marketplace_discounts',
    'key' => ['id'],
    'table' => 'doctrine_marketplace_promotion',
    'references' => [
        'doctrine_promotion' => [
            'reference_fields' => ['id' => '#key'],
            'join_type' => 'LEFT'
        ],
    ],
    'options' => [
        'product_ids_delimiter' => [
            'title' => 'product_ids_delimiter',
            'description' => 'text_product_ids_delimiter',
            'type' => 'input',
            'default_value' => ','
        ],
    ],
    'export_fields' => [
        'Id' => [
            'db_field' => 'id',
            'alt_key' => true,
            'required' => true,
            'help_desc' => 'id',
        ],
        'Name' => [
            'db_field' => 'name',
            'table' => 'doctrine_promotion',
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getName', '#key'],
            'required' => true,
            'help_desc' => 'name',
        ],
        'Active' => [
            'db_field' => 'active',
            'table' => 'doctrine_promotion',
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getActive', '#key'],
            'required' => true,
            'help_desc' => 'active',
        ],
        'Start date' => [
            'db_field' => 'start_time',
            'table' => 'doctrine_promotion',
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getStartDate', '#key'],
            'required' => true,
            'help_desc' => 'start_time',
        ],
        'End date' => [
            'db_field' => 'end_time',
            'table' => 'doctrine_promotion',
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getEndDate', '#key'],
            'required' => true,
            'help_desc' => 'end_time',
        ],
        'Coupon' => [
            'db_field' => 'coupon',
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getCoupon', '#key'],
            'required' => true,
            'help_desc' => 'coupon',
        ],
        'Rule basket has product in list' => [
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getRuleBasketHasProductInList', '#key'],
            'required' => false,
            'linked' => false,
            'help_desc' => 'basket_has_product_in_list',
        ],
        'Rule product list' => [
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getRuleProductList', '#key', '@product_ids_delimiter'],
            'required' => false,
            'linked' => false,
            'help_desc' => 'product_list',
        ],
        'Rule basket price superior to' => [
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getRuleBasketPriceSuperiorTo', '#key'],
            'required' => false,
            'linked' => false,
            'help_desc' => 'basket_price_superior_to',
        ],
        'Rule basket price inferior to' => [
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getRuleBasketPriceInferiorTo', '#key'],
            'required' => false,
            'linked' => false,
            'help_desc' => 'basket_price_inferior_to',
        ],
        'Rule basket price superior or equal to' => [
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getRuleBasketPriceSuperiorOrEqualTo', '#key'],
            'required' => false,
            'linked' => false,
            'help_desc' => 'basket_price_superior_or_equal_to',
        ],
        'Rule basket price inferior or equal to' => [
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getRuleBasketPriceInferiorOrEqualTo', '#key'],
            'required' => false,
            'linked' => false,
            'help_desc' => 'basket_price_inferior_or_equal_to',
        ],
        'Max usage count' => [
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getRuleMaxUsageCount', '#key'],
            'required' => false,
            'linked' => false,
            'help_desc' => 'max_usage_count',
        ],
        'Max usage count per user' => [
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getRuleMaxUsageCountPerUser', '#key'],
            'required' => false,
            'linked' => false,
            'help_desc' => 'max_usage_count_per_user',
        ],
        'Discount type' => [
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getDiscountType', '#key'],
            'required' => false,
            'linked' => false,
            'help_desc' => 'discount_type',
        ],
        'Discount Value' => [
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getDiscountValue', '#key'],
            'required' => false,
            'linked' => false,
            'help_desc' => 'discount_value',
        ],
        'Bonus applied on' => [
            'process_get' => ['\Wizacha\Exim\MarketplaceDiscounts::getBonusAppliedOn', '#key'],
            'required' => false,
            'linked' => false,
            'help_desc' => 'bonus_applied_on',
        ],
    ],
];

return $schema;
