<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;

$schema = [
    'section' => 'related_products',
    'pattern_id' => 'related_products',
    'name' => __('related_products'),
    'key' => ['id'],
    'table' => 'doctrine_related_products',
    'references' => [
        'products' => [
            'reference_fields' => ['product_id' => '&from_product_id'],
            'join_type' => 'LEFT'
        ],
    ],
    'order_by' => [
        'related_products.id',
    ],
    'condition' => [
        'use_company_condition' => true,
    ],
    'options' => [],
    'import_process_data' => [
        'process_data' => [
            'function' => '\Wizacha\Exim\RelatedProducts::put',
            'args' => ['$object', '$jobId'],
            'import_only' => true,
        ],
    ],
    'import_skip_db_processing' => true,
    'import_skip_get_primary_object_id' => true,
    'import_skip_check_alt_keys' => true,
    'export_fields' => [
        'Company id' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\RelatedProducts::getField', '#key' , 'companyId'],
            'required' => true,
        ],
        'Product code' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\RelatedProducts::getField', '#key' , 'productCode'],
            'required' => true,
        ],
        'Type' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\RelatedProducts::getField', '#key' , 'type'],
        ],
        'Related product' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\RelatedProducts::getField', '#key' , 'relatedProduct'],
        ],
        'Related company' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\RelatedProducts::getField', '#key', 'relatedCompany'],
        ],
        'Description' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\RelatedProducts::getField', '#key' , 'description'],
        ],
        'Extra' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\RelatedProducts::getField', '#key' , 'extra'],
        ],
        'Delete' => [
            'linked' => false,
            'import_only' => true,
        ],
    ],
];

if (!Registry::get('runtime.company_id')) {
    $schema['export_fields']['Company id']['required'] = true;
}

return $schema;
