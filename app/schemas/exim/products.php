<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ne<PERSON>    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Wizacha\Marketplace\PriceTier\PriceTier;

include_once(Registry::get('config.dir.schemas') . 'exim/products.functions.php');
include_once(Registry::get('config.dir.schemas') . 'exim/features.functions.php');

$schema = array(
    'section' => 'products',
    'name' => __('products'),
    'pattern_id' => 'products',
    'key' => array('product_id'),
    'order' => 0,
    'table' => 'products',
    'group_by' => [
        'products.product_code',
        'products.company_id',
        'product_descriptions.lang_code',
        'product_options_inventory.combination',
    ],
    'order_by' => [
        'products.product_id',
        'product_options_inventory.combination',
    ],
    'references' => array(
        'product_descriptions' => array(
            'reference_fields' => array('product_id' => '#key', 'lang_code' => '#lang_code'),
            'join_type' => 'LEFT'
        ),
        'product_prices' => array(
            'reference_fields' => array('product_id' => '#key', 'lower_limit' => 1),
            'join_type' => 'LEFT'
        ),
        'images_links' => array(
            'reference_fields' => array('object_id' => '#key.to_char', 'object_type' => 'product', 'type' => 'M'),
            'join_type' => 'LEFT'
        ),
        'companies' => array(
            'reference_fields' => array('company_id' => '&company_id'),
            'join_type' => 'LEFT',
            'import_skip_db_processing' => true
        )
    ),
    'condition' => array(
        'use_company_condition' => true,
    ),
    'pre_processing' => array(
        'reset_inventory' => array(
            'function' => 'fn_exim_reset_inventory',
            'args' => array('@reset_inventory'),
        ),
        'check_product_code' => array(
            'function' => 'fn_check_product_code',
            'args' => array('$import_data'),
            'import_only' => true,
        )
    ),
    'post_processing' => array(
    ),
    'range_options' => array(
        'selector_url' => 'products.manage',
        'object_name' => __('products'),
    ),
    'notes' => array(
        'text_exim_import_options_note',
        'text_exim_import_features_note',
        'text_exim_import_images_note',
        'text_exim_import_files_note',
        'text_exim_import_date_format_note',
    ),
    'options' => array(
        'lang_code' => array(
            'title' => 'language',
            'type' => 'languages',
            'default_value' => array(DEFAULT_LANGUAGE),
        ),
        'category_delimiter' => array(
            'title' => 'category_delimiter',
            'description' => 'text_category_delimiter',
            'type' => 'input',
            'default_value' => '///'
        ),
        'features_delimiter' => array(
            'title' => 'features_delimiter',
            'description' => 'text_features_delimiter',
            'type' => 'input',
            'default_value' => '///'
        ),
        'images_path' => array(
            'title' => 'images_directory',
            'description' => 'text_images_directory',
            'type' => 'input',
            'default_value' => Registry::get('config.dir.exim') . 'backup/images/'
        ),
        'files_path' => array(
            'title' => 'files_directory',
            'description' => 'text_files_directory',
            'type' => 'input',
            'default_value' => Registry::get('config.dir.exim') . 'backup/downloads/'
        ),
        'delete_files' => array(
            'title' => 'drop_existing_data',
            'type' => 'checkbox',
            'import_only' => true
        ),
        'reset_inventory' => array(
            'title' => 'reset_inventory',
            'description' => 'text_reset_inventory_description',
            'type' => 'checkbox',
            'import_only' => true
        ),
        'price_dec_sign_delimiter' => array(
            'title' => 'price_dec_sign_delimiter',
            'description' => 'text_price_dec_sign_delimiter',
            'type' => 'input',
            'default_value' => '.',
            'readonly' => true
        ),
        'attachment_delimiter' => array(
            'title' => 'attachment_delimiter',
            'description' => 'text_attachment_delimiter',
            'type' => 'input',
            'default_value' => '///'
        ),
        'field_delimiter' => array(
            'title' => 'field_delimiter',
            'description' => 'text_field_delimiter',
            'type' => 'input',
            'default_value' => '|'
        ),
    ),
    'export_fields' => array(
        'Product code' => array(
            'db_field' => 'product_code',
            'alt_key' => true,
            'required' => true,
            'alt_field' => 'product_id',
            'multilang' => true,
        ),
        'Language' => array(
            'table' => 'product_descriptions',
            'db_field' => 'lang_code',
            'type' => 'languages',
            'multilang' => true,
        ),
        'Product id' => array(
            'db_field' => 'product_id',
            'multilang' => true,
        ),
        'Category' => array(
            'process_get' => array('fn_exim_get_product_categories', '#key', 'M', '@category_delimiter', '#lang_code'),
            'multilang' => true,
            'linked' => false, // this field is not linked during import-export
            'default' => 'Products' // default value applies only when we creating new record
        ),
        'List price' => array(
            'db_field' => 'list_price',
            'convert_put' => array('fn_exim_import_price', '#this', '@price_dec_sign_delimiter'),
            'process_get' => array('fn_exim_export_price', '#this', '@price_dec_sign_delimiter'),
            'multilang' => true,
        ),
        'Price' => array(
            'table' => 'product_prices',
            'db_field' => 'price',
            'convert_put' => array('fn_exim_import_price', '#this', '@price_dec_sign_delimiter'),
            'process_get' => array('fn_exim_export_price', '#this', '@price_dec_sign_delimiter'),
            'multilang' => true,
        ),
        'Price tiers' => [
            'process_get' => array(
                'Wizacha\Exim\Product::getPriceTiers',
                '#key',
                '#row',
                '@attachment_delimiter'
            ),
            'linked' => false,
            'multilang' => true,
        ],
        'Status' => array(
            'db_field' => 'status',
            'multilang' => true,
        ),
        'Crossed out Price' => [
            'db_field'=> 'crossed_out_price',
            'multilang' => true,
        ],
        'Video' => [
            'process_get' => array('fn_exim_get_video_url', '#key'),
            'linked' => false,
            'multilang' => true,
        ],
        'Affiliate link' => [
            'db_field'=> 'affiliate_link',
            'multilang' => true,
        ],
        'Quantity' => array(
            'db_field' => 'amount',
            'multilang' => true,
        ),
        'Weight' => array(
            'db_field' => 'weight',
            'multilang' => true,
        ),
        'Min quantity' => array(
            'db_field' => 'min_qty',
            'multilang' => true,
        ),
        'Shipping freight' => array(
            'db_field' => 'shipping_freight',
            'convert_put' => array('fn_exim_import_price', '#this', '@price_dec_sign_delimiter'),
            'process_get' => array('fn_exim_export_price', '#this', '@price_dec_sign_delimiter'),
            'multilang' => true,
        ),
        'Date added' => array(
            'db_field' => 'timestamp',
            'process_get' => array('fn_timestamp_to_date', '#this'),
            'convert_put' => array('fn_date_to_timestamp', '#this'),
            'return_result' => true,
            'multilang' => true,
        ),
        'Downloadable' => array(
            'db_field' => 'is_edp',
            'multilang' => true,
        ),
        'Files' => array(
            'process_get' => array('fn_exim_export_file', '#key', '@files_path'),
            'linked' => false, // this field is not linked during import-export
            'multilang' => true,
        ),
        'Ship downloadable' => array(
            'db_field' => 'edp_shipping',
            'multilang' => true,
        ),
        'Inventory tracking' => array(
            'db_field' => 'tracking',
            'multilang' => true,
        ),
        'Out of stock actions' => array(
            'db_field' => 'out_of_stock_actions',
            'multilang' => true,
        ),
        'Free shipping' => array(
            'db_field' => 'free_shipping',
            'multilang' => true,
        ),
        'Zero price action' => array(
            'db_field' => 'zero_price_action',
        ),
        'Thumbnail' => array(
            'table' => 'images_links',
            'db_field' => 'image_id',
            'use_put_from' => '%Detailed image%',
            'process_get' => array('fn_export_image', '#this', 'product', '@images_path'),
            'multilang' => true,
        ),
        'Detailed image' => array(
            'db_field' => 'detailed_id',
            'table' => 'images_links',
            'process_get' => array('fn_export_image', '#this', 'detailed', '@images_path'),
            'multilang' => true,
        ),
        'Product name' => array(
            'table' => 'product_descriptions',
            'db_field' => 'product',
            'multilang' => true,
        ),
        'Description' => array(
            'table' => 'product_descriptions',
            'db_field' => 'full_description',
            'multilang' => true,
        ),
        'Short description' => array(
            'table' => 'product_descriptions',
            'db_field' => 'short_description',
            'multilang' => true,
        ),
        'Meta keywords' => array(
            'table' => 'product_descriptions',
            'db_field' => 'meta_keywords',
            'multilang' => true,
        ),
        'Meta description' => array(
            'table' => 'product_descriptions',
            'db_field' => 'meta_description',
            'multilang' => true,
        ),
        'Search words' => array(
            'table' => 'product_descriptions',
            'db_field' => 'search_words',
            'multilang' => true,
        ),
        'Available since' => [
            'process_get' => array('date', 'Y-m-d', '#this'),
            'db_field'=> 'avail_since',
            'multilang' => true,
        ],
        'Page title' => array(
            'table' => 'product_descriptions',
            'db_field' => 'page_title',
            'multilang' => true,
        ),
        'Taxes' => array(
            'db_field' => 'tax_ids',
            'process_get' => array('fn_exim_get_taxes', '#this', '#lang_code'),
            'multilang' => true,
            'return_result' => true
        ),
        'Features' => array(
            'process_get' => array('fn_exim_get_product_features', '#key', '@features_delimiter', '#lang_code'),
            'linked' => false, // this field is not linked during import-export
            'multilang' => true,
        ),
        'Options' => array(
            'process_get' => array('fn_exim_get_product_options', '#key', '#lang_code'),
            'linked' => false, // this field is not linked during import-export
            'multilang' => true,
        ),
        'Secondary categories' => array(
            'process_get' => array('fn_exim_get_product_categories', '#key', 'A', '@category_delimiter', '#lang_code'),
            'multilang' => true,
            'linked' => false, // this field is not linked during import-export
        ),
        'Product URL' => array(
            'process_get' => array('fn_exim_get_product_url', '#key', '#lang_code'),
            'multilang' => true,
            'linked' => false,
            'export_only' => true,
        ),
        'Image URL' => array(
            'process_get' => array('fn_exim_get_image_url', '#key', 'product', 'M', true, false, '#lang_code'),
            'multilang' => true,
            'db_field' => 'image_id',
            'table' => 'images_links',
            'export_only' => true,
        ),
        'Detailed image URL' => array(
            'process_get' => array('fn_exim_get_detailed_image_url', '#key', 'product', 'M', '#lang_code'),
            'db_field' => 'detailed_id',
            'table' => 'images_links',
            'export_only' => true,
            'multilang' => true,
        ),
        'Items in box' => array(
            'process_get' => array('fn_exim_get_items_in_box', '#key'),
            'linked' => false, // this field is not linked during import-export
            'multilang' => true,
        ),
        'Box size' => array(
            'process_get' => array('fn_exim_get_box_size', '#key'),
            'linked' => false, // this field is not linked during import-export
            'multilang' => true,
        ),
        'Attachments' => array(
            'process_get' => array('fn_exim_export_attachments', '#key', '@attachment_delimiter', '@field_delimiter'),
            'linked' => false, // this field is not linked during import-export
            'multilang' => true,
        ),
        'Moderation status' => array(
            'db_field' => 'approved',
            'multilang' => true,
        ),
        'Template' => array(
            'db_field' => 'product_template_type',
            'multilang' => true,
        ),
        'Infinite stock' => array(
            'process_get' => array('Wizacha\Exim\Misc::getLegacyTrue', '#this'),
            'db_field' => 'infinite_stock',
            'multilang' => true,
        ),
        'Divisions included' => array(
            'process_get' => array(
                'Wizacha\Exim\Product::getIncludedDivisions',
                '#key',
            ),
            'db_field' => 'divisions_included',
            'multilang' => true,
        ),
        'Divisions excluded' => array(
            'process_get' => array(
                'Wizacha\Exim\Product::getExcludedDivisions',
                '#key',
            ),
            'db_field' => 'divisions_excluded',
            'multilang' => true,
        ),
        'SEO Name' => [
            'process_get' => array('fn_seo_get_name', 'p', '#key'),
            'db_field' => 'seo_name',
            'linked' => false,
            'multilang' => true,
        ],
        'Company id' => [
            'db_field' => 'company_id',
            'multilang' => true,
        ],
        'Latitude' => array(
            'process_get' => array(
                'Wizacha\Exim\Product::getLatitude',
                '#key',
            ),
            'linked' => false,
            'multilang' => true,
        ),
        'Longitude' => array(
            'process_get' => array(
                'Wizacha\Exim\Product::getLongitude',
                '#key',
            ),
            'linked' => false,
            'multilang' => true,
        ),
        'createdAt' => [
            'db_field' => 'timestamp',
            'process_get' => array('fn_convert_timestamp_to_date_with_specific_format', '#this'),
            'export_only' => true,
        ],
        'SEO Page Title' => [
            'table' => 'product_descriptions',
            'db_field' => 'page_title',
            'multilang' => true,
        ],
        'SEO Description' => [
            'table' => 'product_descriptions',
            'db_field' => 'meta_description',
            'multilang' => true,
        ],
        'SEO Keywords' => [
            'table' => 'product_descriptions',
            'db_field' => 'meta_keywords',
            'multilang' => true,
        ]
    ),
);

$schema['export_fields']['Localizations'] = array(
    'db_field' => 'localization',
    'process_get' => array('fn_exim_get_localizations', '#this', '#lang_code'),
    'return_result' => true,
    'multilang' => true,
);

$company_schema = array(
    'table' => 'companies',
    'db_field' => 'company',
    'multilang' => true,
);

$schema['export_fields']['Vendor'] = $company_schema;
if (!Registry::get('runtime.company_id')) {
    $schema['export_fields']['Vendor']['required'] = true;
    $schema['export_fields']['Company id']['required'] = true;
}
$schema['post_processing']['check_failure_category'] = [
    'function' => 'fn_w_exim_check_failure_category',
    'import_only' => true,
    'args' => []
];

if (container()->getParameter('feature.order_adjustment') === true) {
    $schema['export_fields']['Max price adjustment'] = [
        'db_field' => 'max_price_adjustment',
    ];
}

if (container()->getParameter('feature.subscription') === true) {
    $schema['export_fields']['Is subscription'] = [
        'process_get' => array('Wizacha\Exim\Misc::getLegacyTrue', '#this'),
        'db_field' => 'is_subscription',
        'multilang' => true,
    ];

    $schema['export_fields']['Is renewable'] = [
        'process_get' => array('Wizacha\Exim\Misc::getLegacyTrue', '#this'),
        'db_field' => 'is_renewable',
        'multilang' => true,
    ];
}

if (Registry::get('runtime.company_id') > 0) {
    $schema['export_fields']['SEO Page Title']['export_only'] = true;
    $schema['export_fields']['SEO Description']['export_only'] = true;
    $schema['export_fields']['SEO Keywords']['export_only'] = true;
    $schema['export_fields']['SEO Name']['export_only'] = true;
}

return $schema;
