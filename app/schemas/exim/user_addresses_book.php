<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

return [
    'section' => 'users',
    'pattern_id' => 'user_addresses_book',
    'name' => __('address_books'),
    'key' => array('user_id'),
    'order' => 1,
    'table' => 'doctrine_address_book',
    'references' => array(
        'users' => array(
            'reference_fields' => array('user_id' => '&user_id'),
            'join_type' => 'LEFT',
            'alt_key' => array('user_id')
        ),
    ),
    'export_fields' => array(
        'Id' => array(
            'db_field' => 'id',
            'required' => true,
        ),
        'E-mail' => array(
            'db_field' => 'email',
            'alt_key' => true,
            'required' => true,
            'table' => 'users',
        ),
        'Label' => array(
            'db_field' => 'label',
        ),
        'Title' => array(
            'db_field' => 'title',
        ),
        'Firstname' => array(
            'db_field' => 'firstname',
        ),
        'Lastname' => array(
            'db_field' => 'lastname',
        ),
        'Company' => array(
            'db_field' => 'company',
        ),
        'Address' => array(
            'db_field' => 'address',
        ),
        'Address (line 2)' => array(
            'db_field' => 'address_2',
        ),
        'City' => array(
            'db_field' => 'city',
        ),
        'State' => array(
            'db_field' => 'state',
        ),
        'Country' => array(
            'db_field' => 'country',
        ),
        'Zipcode' => array(
            'db_field' => 'zipcode',
        ),
        'Phone' => array(
            'db_field' => 'phone',
        ),
        'Division_code' => array(
            'db_field' => 'division_code',
        ),
        'Comment' => array(
            'db_field' => 'comment',
        ),
    ),
];
