<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Tygh\Registry;

include_once(Registry::get('config.dir.schemas') . 'exim/features.functions.php');

$schema = [
    'section' => 'product_attributes',
    'pattern_id' => 'product_attributes',
    'name' => __('product_attributes'),
    'key' => ['feature_id', 'product_id', 'variant_id'],
    'table' => 'product_features_values',
    'options' => [
        'lang_code' => [
            'title' => 'language',
            'type' => 'languages',
            'default_value' => [DEFAULT_LANGUAGE],
        ],
    ],
    'export_custom_function' => '\Wizacha\Exim\ProductAttributes::getData',
    'import_process_data' => [
        'process_data' => [
            'function' => '\Wizacha\Exim\ProductAttributes::put',
            'args' => ['$object', '$jobId'],
            'import_only' => true,
        ],
    ],
    'import_skip_db_processing' => true,
    'import_skip_get_primary_object_id' => true,
    'import_skip_check_alt_keys' => true,
    'export_fields' => [
        'Language' => [
            'linked' => false,
        ],
        'Product code' => [
            'required' => true,
        ],
        'Company id' => [
            'linked' => false,
        ],
        'Free' => [
            'linked' => false,
        ],
        'Id' => [
            'linked' => false,
        ],
        'Name' => [
            'linked' => false,
        ],
        'Value' => [
            'linked' => false,
        ],
    ],
];

if (!Registry::get('runtime.company_id')) {
    $schema['export_fields']['Company id']['required'] = true;
}

return $schema;
