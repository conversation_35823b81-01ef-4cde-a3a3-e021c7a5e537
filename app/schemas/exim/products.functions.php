<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Wizacha\Cscart\Exim;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Product\Attachment;

/**
 * Import product company
 *
 * @param integer $product_id Product ID
 * @param string $company_name Company name
 * @return boolean
 */
function fn_exim_set_product_company($product_id, $company_name)
{
    $company_id = fn_exim_set_company('products', 'product_id', $product_id, $company_name);

    if ($company_id) {
        // Assign company_id to all product options
        $options_ids = db_get_fields('SELECT option_id FROM ?:product_options WHERE product_id = ?i', $product_id);
        if ($options_ids) {
            db_query("UPDATE ?:product_options SET company_id = ?s WHERE option_id IN (?a)", $company_id, $options_ids);
        }
    }

    return $company_id;
}

/**
 * Creates categories tree by path
 *
 * @param integer $product_id Product ID
 * @param string $link_type M - main category, A - additional
 * @param string $categories_data categories path
 * @param string $category_delimiter Delimiter in categories path
 * @param string $store_name Store name (is used for saving category company_id)
 * @return boolean True if any categories were updated.
 */
function fn_exim_set_product_categories($product_id, $link_type, $categories_data, $category_delimiter, $store_name = '')
{
    if (fn_is_empty($categories_data)) {
        return false;
    }

    $set_delimiter = ';';

    $paths = array();
    $updated_categories = array();

    foreach ($categories_data as $lang => $data) {
        // Check if array is provided
        if (strpos($data, $set_delimiter) !== false) {
            $_paths = explode($set_delimiter, $data);
            array_walk($paths, 'fn_trim_helper');
        } else {
            $_paths = array($data);
        }

        foreach ($_paths as $k => $cat_path) {
            $category = (strpos($cat_path, $category_delimiter) !== false) ? explode($category_delimiter, $cat_path) : array($cat_path);
            foreach ($category as $key_cat => $cat) {
                $paths[$k][$key_cat][$lang] = $cat;
            }
        }
    }

    if (!fn_is_empty($paths)) {
        $category_condition = '';
        $joins = '';

        $cat_ids = array();
        $old_data = db_get_hash_array("SELECT * FROM ?:products_categories $joins WHERE product_id = ?i AND link_type = ?s $category_condition", 'category_id', $product_id, $link_type);
        foreach ($old_data as $k => $v) {
            if ($v['link_type'] == $link_type) {
                $updated_categories[] = $k;
            }
            $cat_ids[] = $v['category_id'];
        }
        if (!empty($cat_ids)) {
            db_query("DELETE FROM ?:products_categories WHERE product_id = ?i AND category_id IN (?a)", $product_id, $cat_ids);
        }
    }

    foreach ($paths as $key_path => $categories) {

        if (!empty($categories)) {
            $lang = reset(array_keys(reset($categories)));
            $category_id = \Wizacha\Exim\Category::getIdFromPath(array_column($categories, $lang));

            fn_wizacha_backend_design_w_check_csv_category_pre($category_id, implode($category_delimiter, array_column($categories, $lang)));

            $data = array(
                'product_id' => $product_id,
                'category_id' => $category_id,
                'link_type' => $link_type,
            );

            if (!empty($old_data) && !empty($old_data[$category_id])) {
                $data = fn_array_merge($old_data[$category_id], $data);
            }

            db_query("REPLACE INTO ?:products_categories ?e", $data);

            $updated_categories[] = $category_id;
        }
    }

    if (!empty($updated_categories)) {
        container()->get('marketplace.pim.category_service')->updateProductCount($updated_categories);

        return true;
    }

    return false;
}

/**
 * Export product categories
 *
 * @param int $product_id product ID
 * @param string $link_type M - main category, A - additional
 * @param string $category_delimiter path delimiter
 * @param string $lang_code 2 letters language code
 * @return string
 */
function fn_exim_get_product_categories($product_id, $link_type, $category_delimiter, $lang_code = '')
{
    $set_delimiter = '; ';

    $joins = ' JOIN ?:categories ON ?:categories.category_id = ?:products_categories.category_id ';

    $category_ids = db_get_fields("SELECT ?:products_categories.category_id FROM ?:products_categories $joins WHERE product_id = ?i AND link_type = ?s", $product_id, $link_type);

    $result = array();
    foreach ($category_ids as $c_id) {
        $result[] = fn_get_category_path($c_id, $lang_code, $category_delimiter);
    }

    return implode($set_delimiter, $result);
}

//
// Export product taxes
// Parameters:
// @product_id - product ID
// @lang_code - language code

function fn_exim_get_taxes($product_taxes, $lang_code = '')
{
    static $taxes = [];

    // Tableau qui a en clé les id de taxes et en valeur osef
    $taxIds = array_flip(explode(',', $product_taxes));
    // Vérifie que $taxes a les clés de $taxIds
    if (count(array_intersect_key($taxes, $taxIds)) !== count($taxIds)) {
        // Il nous manque des taxes, on les fetch et on merge avec le cache
        $taxes += db_get_hash_single_array("SELECT tax_id, tax FROM ?:tax_descriptions WHERE FIND_IN_SET(tax_id, ?s) AND lang_code = ?s", ['tax_id', 'tax'], $product_taxes, $lang_code);
    }

    // On implode l'intersection du cache et des clés voulues
    return implode(', ', array_intersect_key($taxes, $taxIds));
}

//
// Import product taxes
// Parameters:
// @product_id - product ID
// @data - comma delimited list of taxes

function fn_exim_set_taxes($product_id, $data)
{
    if (empty($data)) {
        db_query("UPDATE ?:products SET tax_ids = '' WHERE product_id = ?i", $product_id);

        return true;
    }

    $multi_lang = array_keys($data);
    $main_lang = reset($multi_lang);

    $tax_ids = db_get_fields("SELECT tax_id FROM ?:tax_descriptions WHERE tax IN (?a) AND lang_code = ?s", fn_explode(',', $data[$main_lang]), $main_lang);

    $_data = array(
        'tax_ids' => fn_create_set($tax_ids)
    );

    db_query('UPDATE ?:products SET ?u WHERE product_id = ?i', $_data, $product_id);

    return true;
}

//
// Export product features
// Parameters:
// @product_id - product ID
// @lang_code - language code
function fn_exim_get_product_features($product_id, $features_delimiter, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    static $features;

    if (!isset($features[$lang_code])) {
        list($features[$lang_code]) = fn_get_product_features(array('plain' => true), 0, $lang_code);
    }

    $main_category = db_get_field('SELECT category_id FROM ?:products_categories WHERE product_id = ?i AND link_type = ?s', $product_id, 'M');

    $product = array(
        'product_id' => $product_id,
        'main_category' => $main_category
    );

    $product_features = fn_get_product_features_list($product, 'A', $lang_code);

    $pair_delimiter = ':';
    $set_delimiter = '; ';

    $result = array();

    if (!empty($product_features)) {
        foreach ($product_features as $f) {
            $parent = '';
            if (!empty($f['parent_id'])) {
                $parent = '(' . $features[$lang_code][$f['parent_id']]['description'] . ') ';
            }
            $f['value_int'] = (empty($f['value_int']))? 0 : floatval($f['value_int']);
            if (!empty($f['value']) || !empty($f['value_int'])) {
                $result[] = $parent . "{$f['description']}{$pair_delimiter} {$f['feature_type']}[" . (!empty($f['value']) ? $f['value'] : $f['value_int']) . ']';
            } else {
                $_params = array(
                    'feature_id' => $f['feature_id'],
                    'product_id' => $product_id,
                    'feature_type' => $f['feature_type'],
                    'selected_only' => true
                );

                list($variants) = fn_get_product_feature_variants($_params, 0, $lang_code);

                if ($variants) {
                    $values = array();
                    foreach ($variants as $v) {
                        $values[] = $v['variant'];
                    }

                    $result[] = $parent . "{$f['description']}{$pair_delimiter} {$f['feature_type']}[" . implode($features_delimiter, $values) . ']';
                }
            }
        }
    }

    return !empty($result) ? implode($set_delimiter, $result) : '';
}

/**
 * Import product features
 *
 * @param int $product_id Product ID
 * @param string $data string of a product feature and its value
 * @param string $features_delimiter Delimiter symbol
 * @param string $lang_code
 * @param bool|null $is_brand
 * @param int[]|null $categoryIds product's categories path
 *
 * @return boolean Always true
 */
function fn_exim_set_product_features(int $product_id, string $data, string $features_delimiter, string $lang_code, bool $is_brand = null, array $categoryIds = null): bool
{
    //for compatibility with the old format
    $data = preg_replace('{\{\d*\}}', '', $data);

    $variants = array();
    $conditions = '';
    if (isset($is_brand)) {
        $feature_id = fn_w_get_brand_id($categoryIds);
        $conditions = $is_brand?"=":"!=";
        $conditions = db_quote(" AND feature_id $conditions ?i", $feature_id);
    }


    $products_features = db_get_array("SELECT feature_id, variant_id FROM ?:product_features_values WHERE product_id = ?s ".$conditions, $product_id);
    foreach ($products_features as $key => $variant) {
        $variants[$variant['feature_id']] = '';
    }

    if (!fn_is_empty($data)) {
        $data = fn_exim_parse_data($data, $features_delimiter);

        $company_id = 0;

        foreach ($data as $feature) {

            // import features
            if (!empty($feature['group_name'])) {
                $group_id = fn_exim_check_feature_group($feature['group_name'], $company_id, $lang_code);
            } else {
                $group_id = 0;
            }

            $condition = db_quote("WHERE description = ?s AND lang_code = ?s AND feature_type = ?s", $feature['name'], $lang_code, $feature['type']);

            // On filtre sur le parent_id uniquement si le groupe_id est défini
            if ($group_id) {
                $condition .= db_quote(" AND parent_id = ?i", $group_id);
            }

            if (is_int($feature_id) === false) {
                $feature_id = db_get_field(
                    'SELECT ?:product_features.feature_id FROM ?:product_features_descriptions ' .
                    'LEFT JOIN ?:product_features ON ?:product_features.feature_id = ?:product_features_descriptions.feature_id ' . $condition
                );
            }

            if (empty($feature_id)) {
                $feature_data = array(
                    'product_id'    => $product_id,
                    'description' => $feature['name'],
                    'company_id' => $company_id,
                    'feature_type' => $feature['type'],
                    'parent_id' => $group_id
                );
                $feature_id = fn_update_product_feature($feature_data, 0, $lang_code);
            }

            $variants = Exim::exim_product_feature_variants($feature, $feature_id, $variants, $lang_code, $categoryIds);
        }

        fn_update_product_features_value($product_id, $variants, array(), $lang_code);
    }

    return true;
}

/**
 * If feature group exists return it id, else create such groups for all available langs
 *
 * @param string $group Group name
 * @param int $company_id Company identifier
 * @param string $lang_code 2-letter language code
 *
 * @return integer ID of group
 */
function fn_exim_check_feature_group($group, $company_id, $lang_code)
{
    return \Wizacha\Cscart\Exim::exim_check_feature_group($group, $company_id, $lang_code);
}

//
// Export product options
// Parameters:
// @product_id - product ID
// @lang_code - language code
function fn_exim_get_product_options($product_id, $lang_code = '')
{
    $pair_delimiter = ':';
    $set_delimiter = '; ';
    $vars_delimiter = ',';

    $result = array();
    $options = fn_get_product_options($product_id, $lang_code);
    if (!empty($options)) {
        foreach ($options as $o) {

            $glob_opt = db_get_field("SELECT option_id FROM ?:product_global_option_links WHERE option_id = ?i AND product_id = ?i", $o['option_id'], $product_id);

            $prefix = '';
            if (!empty($o['company_id'])) {
                $company_name = fn_get_company_name($o['company_id']);
                $prefix = '(' . $company_name . ') ';
            }

            $str = $prefix . "$o[option_name]$pair_delimiter $o[option_type]" . (empty($glob_opt) ? '' : 'G');

            $variants = array();
            if (!empty($o['variants'])) {
                foreach ($o['variants'] as $v) {
                    $variants[] = $v['variant_name'];
                }
                $str .= '[' .implode($vars_delimiter, $variants). ']';
            }

            $result[] = $str;
        }
    }

    return !empty($result) ? implode($set_delimiter, $result) : '';
}

//
// Import product options
// Parameters:
// @product_id - product ID
// @data - delimited list of product options and their values
function fn_exim_set_product_options($product_id, $data, $lang_code)
{
    //for compatibility with the old format
    $data = preg_replace('{\{\d*\}}', '', $data);

    if (!fn_is_empty($data)) {

        $data = fn_exim_parse_data($data);

        $updated_ids = array(); // store updated ids, delete other (if exist)

        foreach ($data as $option_key => $option) {

            $global_option = (isset($option['global'])) ? $option['global'] : false;

            if (!empty($option['group_name'])) {
                $company_id = fn_get_company_id_by_name($option['group_name']);
            }

            $option_id = db_get_field("SELECT o.option_id FROM ?:product_options_descriptions as d INNER JOIN ?:product_options as o ON o.option_id = d.option_id AND o.product_id = ?i WHERE d.option_name = ?s AND d.lang_code = ?s LIMIT 1", ($global_option) ? 0 : $product_id, $option['name'], $lang_code);

            $variant_ids = array();
            $option['variants'] = isset($option['variants']) ? $option['variants'] : array();
            foreach ($option['variants'] as $variant_pos => $variant) {
                $variant_ids[$variant_pos] = db_get_field("SELECT d.variant_id FROM ?:product_option_variants_descriptions as d INNER JOIN ?:product_option_variants as o ON o.variant_id = d.variant_id AND o.option_id = ?i WHERE d.variant_name = ?s AND d.lang_code = ?s LIMIT 1", $option_id, $variant, $lang_code);
            }

            $option_data = fn_exim_build_option_data($option, $option_id, $variant_ids, $lang_code);
            $option_data['company_id'] = (!empty($company_id)) ? $company_id : 0;

            if (empty($option_id)) {

                $option_data['product_id'] = !empty($global_option) ? 0 : $product_id;
                $option_data['position'] = $option_key;

                $updated_id = fn_update_product_option($option_data, 0, $lang_code);

                // Option is exist, update it
            } else {
                $updated_id = fn_update_product_option($option_data, $option_id, $lang_code);
            }

            if ($global_option) {
                $glob_link = array(
                    'option_id' => $updated_id,
                    'product_id' => $product_id
                );
                db_query('REPLACE INTO ?:product_global_option_links ?e', $glob_link);
            }

            $variant_ids = array();
            foreach ($option['variants'] as $variant_pos => $variant) {
                $variant_ids[$variant_pos] = db_get_field("SELECT d.variant_id FROM ?:product_option_variants_descriptions as d INNER JOIN ?:product_option_variants as o ON o.variant_id = d.variant_id AND o.option_id = ?i WHERE d.variant_name = ?s AND d.lang_code = ?s LIMIT 1", $updated_id, $variant, $lang_code);
            }

            $updated_ids[] = $updated_id;
        }

        // Delete all other options
        if (!empty($updated_ids)) {
            $obsolete_ids = db_get_fields("SELECT option_id FROM ?:product_options WHERE option_id NOT IN (?n) AND product_id = ?i", $updated_ids, $product_id);
            if (!empty($obsolete_ids)) {
                foreach ($obsolete_ids as $o_id) {
                    fn_delete_product_option($o_id, $product_id);
                }
            }
        }
    }

    return true;
}

function fn_exim_parse_data($data, $variants_delimiter = ',')
{
    $pair_delimiter = ':';
    $set_delimiter = ';';
    $_data = array();
    $o_position = 0;

    $options = array_filter(explode($set_delimiter, $data));

    foreach ($options as $option) {
        $o_position += 10;
        $pair = explode($pair_delimiter, $option);

        //Hotfix : Feature's group can contains parenthesis
        preg_match('/\(((?:[^\)\(]*(?:\([^\)]*\))?)+)\)\s*(.+)/', $pair[0], $group);
        if (!empty($group[2])) {
            $pair[0] = $group[2];
        }

        $variants = array();

        if (is_array($pair)) {
            array_walk($pair, 'fn_trim_helper');
            $_data[$o_position]['type'] = substr($pair[1], 0, 1);
            if (substr($pair[1], 1, 1) == 'G') {
                $_data[$o_position]['global'] = true;
            }

            $_data[$o_position]['name'] = $pair[0];

            if (!empty($group[1])) {
                $_data[$o_position]['group_name'] = $group[1];
            }

            if (($pos = strpos($pair[1], '[')) !== false) { // option has variants
                $variants = substr($pair[1], $pos + 1, strlen($pair[1]) - $pos - 2);
                $variants = explode($variants_delimiter, $variants);
            }

            $position = 0;

            foreach ($variants as $variant) {
                $position += 10;
                $_data[$o_position]['variants'][$position] = trim($variant);
            }
        }
    }

    return $_data;
}

function fn_exim_build_option_data($option, $option_id, $variant_ids, $lang_code)
{
    $variants = array();

    if ($option['type'] == 'C') {

        if (!empty($option_id)) { // check if variant exist
            $variants = db_get_array("SELECT * FROM ?:product_option_variants WHERE option_id = ?i AND position = 1", $option_id);
        }

        // If not, generate default variant
        if (empty($variants)) {
            $variants = array(
                array(
                    'position' => 1,
                ),
            );
        }

    } else {

        foreach ($option['variants'] as $variant_pos => $variant) {
            $variants[$variant_pos] = array(
                'variant_name' => $variant,
                'position' => $variant_pos,
            );

            if (!empty($variant_ids[$variant_pos])) {
                $variants[$variant_pos]['variant_id'] = $variant_ids[$variant_pos];
            }
        }
    }

    $option_data = array(
        'option_name' => $option['name'],
        'option_type' => $option['type'],
        'variants' => $variants,
    );

    return $option_data;
}

//
// Export product files
// @product_id 0- product ID
// @path - path to store files
//
function fn_exim_export_file($product_id, $path)
{
    $files = db_get_array("SELECT file_path, preview_path, pfolder.folder_id FROM ?:product_files as pfiles"
                          ." LEFT JOIN ?:product_file_folders as pfolder ON pfolder.folder_id = pfiles.folder_id"
                          ." WHERE pfiles.product_id = ?i", $product_id);

    if (!empty($files)) {
        // If backup path is set, check if it exists and copy files there
        if (!empty($path)) {
            if (!fn_mkdir($path)) {
                fn_set_notification('E', __('error'), __('text_cannot_create_directory', array(
                    '[directory]' => $path
                )));

                return '';
            }
        }

        $_data = array();
        $downloadsStorageService = container()->get('Wizacha\Storage\DownloadsStorageService');
        foreach ($files as $file) {
            $downloadsStorageService->export($product_id . '/' . $file['file_path'], $path . '/' . $file['file_path']);

            if (!empty($file['preview_path'])) {
                $downloadsStorageService->export($product_id . '/' . $file['preview_path'], $path . '/' . $file['preview_path']);
            }

            $file_data = $file['file_path'];

            if (!empty($file['folder_id'])) {
                $file_data = $file['folder_id'].'/'.$file_data;
            }

            if (!empty($file['preview_path'])) {
                $file_data = $file_data.'#'.$file['preview_path'];
            }

            $_data[] = $file_data;

        }

        return implode(', ', $_data);
    }

    return '';
}

//
// Inserts ID of element to the csv file
// @item_id - id of an item
//
//function fn_exim_post_item_id($item_id)
//{
//    return '{' . $item_id . '}';
//}

//
// Gets element ID and updates the element name
// @element - element
//
function fn_exim_get_item_id(&$element)
{
    $item_id = '';
    if ($item_id = substr($element, 0, strpos($element, '}'))) {
        $element = substr($element, strpos($element, '}') + 1);
        $item_id = substr($item_id, 1);
    }

    return $item_id;
}

// Import preprocessor
function fn_exim_reset_inventory($reset_inventory)
{
    // Reset inventory to zero before import
    if ($reset_inventory == 'Y') {
        if (Registry::get('runtime.company_id')) {
            $i = 0;
            $step = 1000;
            while ($product_ids = db_get_fields("SELECT product_id FROM ?:products WHERE company_id = ?i LIMIT $i, $step", Registry::get('runtime.company_id'))) {
                $i += $step;
                db_query("UPDATE ?:products SET amount = 0 WHERE product_id IN (?a)", $product_ids);
                db_query("UPDATE ?:product_options_inventory SET amount = 0 WHERE product_id IN (?a)", $product_ids);
            }
        } else {
            db_query("UPDATE ?:products SET amount = 0");
            db_query("UPDATE ?:product_options_inventory SET amount = 0");
        }
    }
}

/**
 * Assign localizations to the product
 *
 * @param string $localization_ids - comma delimited list of localization IDs
 * @param string $lang_code - language code
 * @return string  - comma delimited list of localization names
 */
function fn_exim_get_localizations($localization_ids, $lang_code = '')
{
    $locs = db_get_fields("SELECT localization FROM ?:localization_descriptions WHERE FIND_IN_SET(localization_id, ?s) AND lang_code = ?s", $localization_ids, $lang_code);

    return implode(', ', $locs);
}

/**
 * Assign localizations to the product
 *
 * @param int $product_id Product ID
 * @param string $data - comma delimited list of localizations
 * @return boolean always true
 */
function fn_exim_set_localizations($product_id, $data)
{
    if (empty($data)) {
        db_query("UPDATE ?:products SET localization = ''");

        return true;
    }

    $multi_lang = array_keys($data);
    $main_lang = reset($multi_lang);

    $loc_ids = db_get_fields("SELECT localization_id FROM ?:localization_descriptions WHERE localization IN (?a) AND lang_code = ?s", fn_explode(',', $data[$main_lang]), $main_lang);

    $_data = array(
        'localization' => fn_create_set($loc_ids)
    );

    db_query('UPDATE ?:products SET ?u WHERE product_id = ?i', $_data, $product_id);

    return true;
}


function fn_exim_get_items_in_box($product_id)
{
    $shipping_params = db_get_field('SELECT shipping_params FROM ?:products WHERE product_id = ?i', $product_id);
    if (!empty($shipping_params)) {
        $shipping_params = unserialize($shipping_params);

        return 'min:' . (empty($shipping_params['min_items_in_box']) ? 0 : $shipping_params['min_items_in_box']) . ';max:' . (empty($shipping_params['max_items_in_box']) ? 0 : $shipping_params['max_items_in_box']);
    }

    return 'min:0;max:0';
}

function fn_exim_put_items_in_box($product_id, $data)
{
    if (empty($data)) {
        return false;
    }

    $min = $max = 0;
    $params = explode(';', $data);
    foreach ($params as $param) {
        $elm = explode(':', $param);
        if ($elm[0] == 'min') {
            $min = intval($elm[1]);
        } elseif ($elm[0] == 'max') {
            $max = intval($elm[1]);
        }
    }

    $shipping_params = db_get_field('SELECT shipping_params FROM ?:products WHERE product_id = ?i', $product_id);
    if (!empty($shipping_params)) {
        $shipping_params = unserialize($shipping_params);
    }

    $shipping_params['min_items_in_box'] = $min;
    $shipping_params['max_items_in_box'] = $max;

    db_query('UPDATE ?:products SET shipping_params = ?s WHERE product_id = ?i', serialize($shipping_params), $product_id);

    return true;
}

function fn_exim_get_box_size($product_id)
{
    $shipping_params = db_get_field('SELECT shipping_params FROM ?:products WHERE product_id = ?i', $product_id);

    if (!empty($shipping_params)) {
        $shipping_params = unserialize($shipping_params);

        return 'length:' . (empty($shipping_params['box_length']) ? 0 : $shipping_params['box_length']) . ';width:' . (empty($shipping_params['box_width']) ? 0 : $shipping_params['box_width']) . ';height:' . (empty($shipping_params['box_height']) ? 0 : $shipping_params['box_height']);
    }

    return 'length:0;width:0;height:0';
}

function fn_exim_put_box_size($product_id, $data)
{
    if (empty($data)) {
        return false;
    }

    $length = $width = $height = 0;
    $params = explode(';', $data);
    foreach ($params as $param) {
        $elm = explode(':', $param);
        if ($elm[0] == 'length') {
            $length = intval($elm[1]);
        } elseif ($elm[0] == 'width') {
            $width = intval($elm[1]);
        } elseif ($elm[0] == 'height') {
            $height = intval($elm[1]);
        }
    }

    $shipping_params = db_get_field('SELECT shipping_params FROM ?:products WHERE product_id = ?i', $product_id);
    if (!empty($shipping_params)) {
        $shipping_params = unserialize($shipping_params);
    }

    $shipping_params['box_length'] = $length;
    $shipping_params['box_width'] = $width;
    $shipping_params['box_height'] = $height;

    db_query('UPDATE ?:products SET shipping_params = ?s WHERE product_id = ?i', serialize($shipping_params), $product_id);

    return true;
}

function fn_import_unset_product_id(&$object)
{
    unset($object['product_id']);
}

function fn_check_product_code($data)
{
    if (!empty($data)) {
        $cutted_product_codes = "";

        foreach ($data as $key => $product_data) {
            if (!empty($product_data['product_code'])) {
                if (strlen($product_data['product_code']) > 128) {
                    $cutted_product_codes .= substr($product_data['product_code'], 0, 128) . "... ";
                }
            }
        }

        if (!empty($cutted_product_codes)) {
            $msg = __('cutted_product_codes') . '<br>' . $cutted_product_codes . '<br>';
            fn_set_notification('W', __('warning'), $msg);
        }
    }
}

/**
 * Updates product price for a storefront. Used on product import.
 *
 * @param integer $product_id Product ID
 * @param float $price Price
 * @param boolean $is_create True if the product has been created
 * @param string $store Comany name
 * @return boolean
 */
function fn_import_product_price($product_id, $price, $is_create, $store ='')
{
    fn_update_product_prices($product_id, array('price' => $price, 'create' => $is_create));
}

/**
 * @param int $productId
 * @return string
 */
function fn_exim_get_video_url($productId)
{
    if (empty($productId)) {
        // Bugfix, sometimes the export give null id
        return '';
    }

    $videoService = container()->get('marketplace.pim.video_service');
    $videoStorage = container()->get('marketplace.pim.video_storage');
    $video = $videoService->findOneByProductId((int) $productId);
    return $video !== null ? $videoStorage->generatePublicLinks($video)['path'] : '';
}

/**
 * @param int $productId
 * @param string $attachmentDelimiter Separate attachments
 * @param string $fieldDelimiter Separate attachment infos
 * @return array
 */
function fn_exim_export_attachments($productId, $attachmentDelimiter, $fieldDelimiter)
{
    return implode($attachmentDelimiter, array_map(function (Attachment $attachment) use ($fieldDelimiter) {
        return $attachment->export($fieldDelimiter);
    }, container()->get('marketplace.pim.product.service')->getAttachments((int) $productId)));
}
