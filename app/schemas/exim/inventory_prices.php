<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

use Tygh\Registry;
use Wizacha\Marketplace\PriceTier\PriceTier;

$schema = array(
    'section' => 'inventory_prices',
    'name' => __('inventory_prices'),
    'pattern_id' => 'inventory_prices',
    'key' => array('product_id'),
    'order' => 0,
    'table' => 'products',
    'references' => array(
        'product_prices' => array(
            'reference_fields' => array('product_id' => '#key', 'lower_limit' => 1),
            'join_type' => 'LEFT'
        ),
        'product_descriptions' => array(
            'reference_fields' => array('product_id' => '#key', 'lang_code' => '#lang_code'),
            'join_type' => 'LEFT'
        ),
        'companies' => array(
            'reference_fields' => array('company_id' => '&company_id'),
            'join_type' => 'LEFT',
            'import_skip_db_processing' => true
        )
    ),
    'condition' => array(
        'use_company_condition' => true,
    ),
    'options' => array(
        'lang_code' => array(
            'title' => 'language',
            'type' => 'languages',
            'default_value' => array(DEFAULT_LANGUAGE),
        ),
    ),
    'export_fields' => array(
        'Product code' => array(
            'db_field' => 'product_code',
            'alt_key' => true,
            'required' => true,
            'alt_field' => 'product_id',
            'multilang' => true,
        ),
        'Language' => array(
            'table' => 'product_descriptions',
            'db_field' => 'lang_code',
            'type' => 'languages',
            'multilang' => true,
        ),
        'Price' => array(
            'table' => 'product_prices',
            'db_field' => 'price',
            'convert_put' => array('fn_exim_import_price', '#this', '@price_dec_sign_delimiter'),
            'process_get' => array('fn_exim_export_price', '#this', '@price_dec_sign_delimiter'),
            'multilang' => true,
        ),
        'Price tiers' => [
            'process_get' => array(
                'Wizacha\Exim\Product::getPriceTiers',
                '#key',
                '#row',
                '@attachment_delimiter'
            ),
            'linked' => false,
            'multilang' => true,
        ],
        'Crossed out Price' => [
            'db_field'=> 'crossed_out_price',
            'multilang' => true,
        ],
        'Combination' => [
            'linked' => false,
            'multilang' => true
        ],
        'Combination Code' => [
            'linked' => false,
            'multilang' => true
        ],
        'Company id' => [
            'db_field' => 'company_id',
            'multilang' => true,
            'required' =>  Registry::get('runtime.company_id') > 0 ? false : true,
        ],
    ),
);

return $schema;
