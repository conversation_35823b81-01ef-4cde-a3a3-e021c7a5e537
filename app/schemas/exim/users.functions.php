<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

function fn_exim_users_get_extra_data(int $user_id): string
{
    $fields = db_get_field("SELECT u.extra as extra FROM ?:users u WHERE u.user_id = ?i", $user_id);
    $extra = \json_decode($fields ?? '' , true);

    if (\is_array($extra) === true && \count($extra) > 0) {
        $return = [];
        foreach($extra as $key => $field){
            $return[] = $key . ': ' . $field;
        }

        return \implode('; ', $return);
    }

    return '';
}
