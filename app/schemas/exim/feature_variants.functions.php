<?php
/***************************************************************************
 *                                                                          *
 *   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON> Shalnev    *
 *                                                                          *
 * This  is  commercial  software,  only  users  who have purchased a valid *
 * license  and  accept  to the terms of the  License Agreement can install *
 * and use this program.                                                    *
 *                                                                          *
 ****************************************************************************
 * PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
 * "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
 ****************************************************************************/

use Tygh\Registry;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Exim\Import\ImportReportMessage\AttributeVariantMessage;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Attribute\AttributeService;
use Wizacha\Events\IterableEvent;

/**
 * Warning: this function is a little bit incoherent, especially because the input variant_id may be wrong and can be silently wrongly corrected by the script.
 * Also, the required field 'Feature name' should not be always required
 * In conclusion, this could be highly refactored to be logical (and optimized) but it will lead to a BC break
 */
function fn_import_feature_variant($data, $options, &$skip_record, string $jobId)
{
    $skip_record = true;
    $variant = reset($data);

    if (empty($variant['Feature name'])) {
        return false;
    }

    static $features;

    if ($features === null) {
        list($features) = fn_get_product_features(array('plain' => true), 0, (string) GlobalState::fallbackLocale());
    }

    if (!empty($variant['Feature group'])) {
        $feature_group = fn_exim_get_feature_data_by_name($variant['Feature group'], '');
        $variant['parent_id'] = $feature_group['feature_id'];
    } else {
        $variant['parent_id'] = 0;
    }

    $feature = fn_exim_get_feature_data_by_name($variant['Feature name'], $variant['Feature group']);

    if (count($feature) === 0) {
        EximJobService::error(AttributeVariantMessage::EXIM_VARIANTS_CREATE_FAILED, $jobId, (string) $variant['line'], null, $variant['variant']);

        return false;
    }

    $feature_id = $feature['feature_id'];
    $company_id = $feature['company_id'];

    if (Registry::get('runtime.company_id') && Registry::get('runtime.company_id') != $company_id) {
        EximJobService::error(AttributeVariantMessage::EXIM_VARIANTS_CREATE_FAILED, $jobId, (string) $variant['line'], null, $variant['variant']);

        return false;
    }

    $variant_id = $variant['variant_id'] ?? null;
    $new_variant_id = fn_update_product_feature_variant($feature_id, $features[$feature_id]['feature_type'], $variant, $variant['lang_code']);

    container()->get('event_dispatcher')->dispatch(
        (new IterableEvent)->setElement($feature_id),
        AttributeService::EVENT_UPDATE
    );

    if ((int) $variant_id === (int) $new_variant_id) {
        EximJobService::info(AttributeVariantMessage::EXIM_VARIANTS_UPDATE, $jobId, (string) $variant['line'], $variant_id, $variant['variant']);
    } else {
        EximJobService::info(AttributeVariantMessage::EXIM_VARIANTS_CREATE, $jobId, (string) $variant['line'], $variant_id, $variant['variant']);
        $variant_id = $new_variant_id;
    }

    if (!empty($variant['image_id'])) {
        $uploaded  = fn_import_images($options['images_path'], $variant['image_id'],  $variant['image_id'],0, 'V', $variant_id, 'feature_variant');

        if (!$uploaded) {
            EximJobService::warning(AttributeVariantMessage::EXIM_FAILED_TO_PROCESS_IMAGE, $jobId, (string) $variant['line'], $variant_id);
        }
    }

    return $variant_id;
}

function fn_exim_get_feature_data_by_name($feature_name, $parent_name): array
{
    $join = "INNER JOIN ?:product_features AS pf ON pf.feature_id = pfd.feature_id";

    if (!empty($parent_name)) {
        $parent = fn_exim_get_feature_data_by_name($parent_name, '');
        $parent_id = $parent['feature_id'];

        // try a case-sensitive query
        $output = db_get_row("SELECT pfd.feature_id, company_id FROM ?:product_features_descriptions AS pfd $join WHERE BINARY description = ?s AND parent_id = ?i", $feature_name, $parent_id);

        // fallback to the case-insensitive to avoid BC-break
        if (count($output) === 0) {
            $output = db_get_row("SELECT pfd.feature_id, company_id FROM ?:product_features_descriptions AS pfd $join WHERE description = ?s AND parent_id = ?i", $feature_name, $parent_id);
        }
    } else {
        // try a case-sensitive query
        $output = db_get_row("SELECT pfd.feature_id, company_id FROM ?:product_features_descriptions AS pfd $join WHERE BINARY description = ?s", $feature_name);

        // fallback to the case-insensitive to avoid BC-break
        if (count($output) === 0) {
            $output = db_get_row("SELECT pfd.feature_id, company_id FROM ?:product_features_descriptions AS pfd $join WHERE description = ?s ", $feature_name);
        }
    }

    return $output;
}

function fn_exim_get_feature_name($variant_id, $lang_code)
{
    $join = "INNER JOIN ?:product_feature_variants AS pfv ON pfd.feature_id = pfv.feature_id";

    return db_get_field("SELECT description FROM ?:product_features_descriptions AS pfd $join WHERE variant_id = ?i AND lang_code = ?s", $variant_id, $lang_code);
}

function fn_exim_get_product_feature_group_name($variant_id, $lang_code)
{
    $join = " INNER JOIN ?:product_features AS pf ON pfd.feature_id = pf.parent_id ";
    $join .= " INNER JOIN ?:product_feature_variants AS pfv ON pf.feature_id = pfv.feature_id ";

    return db_get_field("SELECT description FROM ?:product_features_descriptions AS pfd $join WHERE variant_id = ?i AND lang_code = ?s", $variant_id, $lang_code);
}

/**
 * Import image pair
 *
 * @param string $prefix path prefix
 * @param string $image_file thumbnail path or filename
 * @param string $detailed_file detailed image path or filename
 * @param string $position image position
 * @param string $type pair type
 * @param int $object_id ID of object to attach images to
 * @param string $object name of object to attach images to
 * @return array|bool True if images were imported
 */
function fn_import_images($prefix, $image_file, $detailed_file, $position, $type, $object_id, $object)
{
    static $updated_products = array();

    if (empty($object_id)) {
        return false;
    }

    fn_delete_image_pairs($object_id, $object, $type);

    $_REQUEST["server_import_image_icon"] = '';
    $_REQUEST["type_import_image_icon"] = '';

    // Get image alternative text if exists
    if (!empty($image_file) && strpos($image_file, '#') !== false) {
        list ($image_file, $image_alt) = explode('#', $image_file);
    }

    if (!empty($detailed_file) && strpos($detailed_file, '#') !== false) {
        list ($detailed_file, $detailed_alt) = explode('#', $detailed_file);
    }

    if (!empty($image_alt)) {
        preg_match_all('/\[([A-Za-z]+?)\]:(.*?);/', $image_alt, $matches);
        if (!empty($matches[1]) && !empty($matches[2])) {
            $image_alt = array_combine(array_values($matches[1]), array_values($matches[2]));
        }
    }

    if (!empty($detailed_alt)) {
        preg_match_all('/\[([A-Za-z]+?)\]:(.*?);/', $detailed_alt, $matches);
        if (!empty($matches[1]) && !empty($matches[2])) {
            $detailed_alt = array_combine(array_values($matches[1]), array_values($matches[2]));
        }
    }

    $type_image_detailed = (strpos($detailed_file, '://') === false) ? 'server' : 'url';
    $type_image_icon = (strpos($image_file, '://') === false) ? 'server' : 'url';

    $_REQUEST["type_import_image_icon"] = array($type_image_icon);
    $_REQUEST["type_import_image_detailed"] = array($type_image_detailed);

    $image_file = fn_find_file($prefix, $image_file);
    if ($image_file !== false) {
        if (strpos($image_file, Registry::get('config.dir.root')) === 0) {
            $_REQUEST["type_import_image_icon"] = ['server'];
            $_REQUEST["file_import_image_icon"] = array(str_ireplace(Registry::get('config.dir.root'), '', $image_file));

        } else {
            fn_set_notification('E', __('error'), __('error_images_need_located_root_dir'));
            $_REQUEST["file_import_image_detailed"] = array();
        }
    } else {
        $_REQUEST["file_import_image_icon"] = array();
    }

    $detailed_file = fn_find_file($prefix, $detailed_file);
    if ($detailed_file !== false) {
        if (strpos($detailed_file, Registry::get('config.dir.root')) === 0) {
            $_REQUEST["type_import_image_detailed"] = ['server'];
            $_REQUEST["file_import_image_detailed"] = array(str_ireplace(Registry::get('config.dir.root'), '', $detailed_file));
        } else {
            fn_set_notification('E',  __('error'), __('error_images_need_located_root_dir'));
            $_REQUEST["file_import_image_detailed"] = array();
        }

    } else {
        $_REQUEST["file_import_image_detailed"] = array();
    }

    $_REQUEST['import_image_data'] = array(
        array(
            'type' => $type,
            'image_alt' => empty($image_alt) ? '' : $image_alt,
            'detailed_alt' => empty($detailed_alt) ? '' : $detailed_alt,
            'position' => empty($position) ? 0 : $position,
        )
    );

    return fn_attach_image_pairs('import', $object, $object_id);
}

/**
 * Finds file and return real path to it
 *
 * @param string $prefix path to search in
 * @param string $file Filename, can be URL, absolute or relative path
 * @return mixed String path to the file or false if file is not found.
 */
function fn_find_file($prefix, $file)
{
    $file = \Tygh\Bootstrap::stripSlashes($file);

    // Absolute path
    if (is_file($file)) {
        return realpath($file);
    }

    // Path is relative to prefix
    if (is_file($prefix . '/' . $file)) {
        return realpath($prefix . '/' . $file);
    }

    // Url
    if (strpos($file, '://') !== false) {
        $content = fn_get_contents($file);
        list($file,) = explode('?' , $file);
        if (!empty($content)) {
            $ext = (new SplFileInfo($file))->getExtension();
            $fname = fn_create_temp_file($ext);
            if (fn_put_contents($fname, $content)) {
                return $fname;
            }
        }
    }

    return false;
}
