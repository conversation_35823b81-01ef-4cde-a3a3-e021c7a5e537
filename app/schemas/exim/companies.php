<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

$schema = [
    'section' => 'companies',
    'pattern_id' => 'companies',
    'name' => __('companies'),
    'key' => ['company_id'],
    'order' => 0,
    'table' => 'companies',
    'order_by' => [
        'companies.company_id',
    ],
    'options' => [
        'extra_delimiter' => [
            'title' => 'extra_delimiter',
            'description' => 'text_extra_delimiter',
            'type' => 'input',
            'default_value' => '///'
        ],
    ],
    'export_fields' => [
        'ID' => [
            'db_field' => 'company_id',
            'alt_key' => true,
            'required' => true,
        ],
        'Name' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'name'],
            'required' => true,
        ],
        'Corporate Name' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'corporateName'],
        ],
        'Status' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getStatus', '#key'],
        ],
        'Language Code' => [
            'db_field' => 'lang_code'
        ],
        'Slug' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'slug'],
        ],
        'E-mail' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'email'],
        ],
        'Description' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'description'],
        ],
        'Zip Code' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'zipcode'],
        ],
        'Address' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'address'],
        ],
        'City' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'city'],
        ],
        'State' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'state'],
        ],
        'Country' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'country'],
        ],
        'Phone Number' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'phoneNumber'],
        ],
        'Legal Status' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'legalStatus'],
        ],
        'Siret Number' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'siretNumber'],
        ],
        'Vat Number' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'vatNumber'],
        ],
        'Capital' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'capital'],
        ],
        'RCS' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'rcs'],
        ],
        'Fax' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'fax'],
        ],
        'URL' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'url'],
        ],
        'IBAN' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'iban'],
        ],
        'BIC' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'bic'],
        ],
        'Legal Representative First Name' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'legalRepresentativeFirstName'],
        ],
        'Legal Representative Last Name' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'legalRepresentativeLastName'],
        ],
        'Extra' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getextraField', '#key' ,'@extra_delimiter'],
        ],
        'Naf Code' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getField', '#key' , 'nafCode'],
        ],
        'Meta Title' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getMetaField', '#key' , 'title'],
        ],
        'Meta Keywords' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getMetaField', '#key' , 'keywords'],
        ],
        'Meta Description' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getMetaField', '#key' , 'description'],
        ],
        'Invoicing Disabled' => [
            'linked' => false,
            'process_get' => ['\Wizacha\Exim\Companies::getInvoicingDisabled', '#key'],
        ]
    ],
];

return $schema;
