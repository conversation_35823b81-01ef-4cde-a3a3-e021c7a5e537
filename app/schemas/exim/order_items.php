<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ne<PERSON>    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;

include_once(Registry::get('config.dir.schemas') . 'exim/order_items.functions.php');

return array(
    'section' => 'orders',
    'pattern_id' => 'order_items',
    'name' => __('order_items'),
    'key' => array('item_id', 'order_id'),
    'order' => 1,
    'table' => 'order_details',
    'references' => array(
        'orders' => array(
            'reference_fields' => array('order_id' => '&order_id'),
            'join_type' => 'LEFT',
            'alt_key' => array('order_id')
        ),
        'products' => array(
            'reference_fields' => array('product_id' => '&product_id'),
            'join_type' => 'LEFT',
        ),
    ),
    'condition' => array(
        'conditions' => array('&orders.is_parent_order' => 'N'),
        'use_company_condition' => true,
    ),
    'range_options' => array(
        'selector_url' => 'orders.manage',
        'object_name' => __('orders'),
    ),
    'export_fields' => array(
        'Order ID' => array(
            'db_field' => 'order_id',
            'alt_key' => true,
            'required' => true,
        ),
        'Item ID' => array(
            'db_field' => 'item_id',
            'alt_key' => true,
            'required' => true,
        ),
        'Product ID' => array(
            'db_field' => 'product_id'
        ),
        'Supplier reference' => array(
            'db_field' => 'supplier_reference'
        ),
        'Product code' => array(
            'db_field' => 'product_code'
        ),
        'Price' => array(
            'db_field' => 'price'
        ),
        'Quantity' => array(
            'db_field' => 'amount'
        ),
        'Extra' => array(
            'linked' => true,
            'process_get' => array('fn_exim_orders_get_extra', '#this'),
            'process_put' => array('fn_exim_orders_set_extra', '#key', '#this')
        ),
    ),
);
