<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>nev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;

function fn_exim_get_product_combination($product_id, $combination, $lang_code = null)
{
    return \Wizacha\Cscart\Exim::exim_get_product_combination($product_id, $combination, $lang_code ?? (string) GlobalState::interfaceLocale());
}

function fn_exim_put_product_combination($product_id, $product_name, $combination_code, $combination, $amount, &$counter, $price = null, $supplier_reference = null)
{
    return \Wizacha\Cscart\Exim::exim_put_product_combination($product_id, $product_name, $combination_code, $combination, $amount, $counter, $price, null, null, null, null, null, $supplier_reference);
}

function fn_import_check_product_combination_company_id(&$primary_object_id, &$object, &$pattern, &$options, &$processed_data, &$processing_groups, &$skip_record)
{
    if (Registry::get('runtime.company_id')) {
        if (empty($primary_object_id) && empty($object['product_id'])) {
            $processed_data['S']++;
            $skip_record = true;

            return false;
        }

        if (!empty($primary_object_id)) {
            $field = key($primary_object_id);
            $value = current($primary_object_id);

            $company_id = db_get_field('SELECT company_id FROM ?:products WHERE ' . $field . ' = ?s', $value);
        } else {
            $company_id = db_get_field('SELECT company_id FROM ?:products WHERE product_id = ?i', $object['product_id']);
        }

        if ($company_id != Registry::get('runtime.company_id')) {
            $processed_data['S']++;
            $skip_record = true;
        }
    }
}
