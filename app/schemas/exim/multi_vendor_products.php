<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;

$schema = [
    'section' => 'multi_vendor_products',
    'name' => __('multi_vendor_products'),
    'pattern_id' => 'multi_vendor_products',
    'key' => ['id'],
    'order' => 0,
    'table' => 'doctrine_multi_vendor_product',
    'import_skip_get_primary_object_id' => true,
    'import_skip_db_processing' => true,
    'show_help_structure' => false,
    'references' => [
        'doctrine_multi_vendor_product_translations' => [
            'reference_fields' => ['multi_vendor_product_id' => '#key', 'locale' => '#lang_code'],
            'join_type' => 'LEFT'
        ],
    ],
    'import_process_data' => [
        'process_data' => [
            'function' => '\Wizacha\Exim\MultiVendorProduct::put',
            'args' => ['$object', '$jobId', '$options'],

            'import_only' => true,
        ],
    ],
    'notes_template' => "multi_vendor_products",
    'notes' => [
        'exim_help_mvp_multilang',
        'exim_help_mvp_ean_code',
    ],
    'options' => [
        'lang_code' => [
            'title' => 'language',
            'type' => 'languages',
            'default_value' => [DEFAULT_LANGUAGE],
        ],
        'category_delimiter' => [
            'title' => 'category_delimiter',
            'description' => 'text_category_delimiter',
            'type' => 'input',
            'default_value' => '///'
        ],
        'attributes_delimiter' => [
            'title' => 'attributes_delimiter',
            'description' => 'text_attributes_delimiter',
            'type' => 'input',
            'default_value' => '///'
        ],
        'images_delimiter' => [
            'title' => 'w_images_delimiter',
            'description' => 'w_images_delimiter',
            'type' => 'input',
            'default_value' => '####'
        ],
    ],
    'export_fields' => [
        'Id' => [
            'db_field' => 'id',
            'alt_key' => true,
            'multilang' => true,
        ],
        'Language' => [
            'table' => 'doctrine_multi_vendor_product_translations',
            'db_field' => 'locale',
            'multilang' => true,
        ],
        'Name' => [
            'table' => 'doctrine_multi_vendor_product_translations',
            'db_field' => 'name',
            'required' => true,
            'multilang' => true,
        ],
        'Code' => [
            'db_field' => 'code',
            'multilang' => true,
        ],
        'Supplier reference' => [
            'db_field' => 'supplier_reference',
            'multilang' => true,
        ],
        'Slug' => [
            'db_field' => 'slug',
            'process_get' => ['\Wizacha\Exim\MultiVendorProduct::getMvpSlug', '#key'],
            'linked' => false,
            'multilang' => true,
        ],
        'Short description' => [
            'table' => 'doctrine_multi_vendor_product_translations',
            'db_field' => 'short_description',
            'multilang' => true,
        ],
        'Description' => [
            'table' => 'doctrine_multi_vendor_product_translations',
            'db_field' => 'description',
            'multilang' => true,
        ],
        'Seo title' => [
            'table' => 'doctrine_multi_vendor_product_translations',
            'db_field' => 'seo_title',
            'multilang' => true,
        ],
        'Seo description' => [
            'table' => 'doctrine_multi_vendor_product_translations',
            'db_field' => 'seo_description',
            'multilang' => true,
        ],
        'Seo keywords' => [
            'table' => 'doctrine_multi_vendor_product_translations',
            'db_field' => 'seo_keywords',
            'multilang' => true,
        ],
        'Status' => [
            'db_field' => 'status',
            'required' => true,
            'multilang' => true,
        ],
        'Category' => [
            'process_get' => ['\Wizacha\Exim\MultiVendorProduct::getMvpCategoryPath', '#key', '@category_delimiter', '#lang_code'],
            'export_only' => true,
            'multilang' => true,
            'linked' => false,
        ],
        'Category id' => [
            'db_field' => 'category_id',
            'required' => true,
            'multilang' => true,
        ],
        'Image ids' => [
            'db_field' => 'image_ids',
            'export_only' => true,
            'multilang' => true,
        ],
        'Attributes' =>[
            'db_field' => 'attributes',
            'process_get' => ['\Wizacha\Exim\MultiVendorProduct::getMvpAttributes', '#key', '@attributes_delimiter', '#lang_code'],
            'multilang' => true,
            'linked' => false,
        ],
        'Free attributes' =>[
            'db_field' => 'freeAttributes',
            'process_get' => ['\Wizacha\Exim\MultiVendorProduct::getMvpFreeAttributes', '#key', '@attributes_delimiter', '#lang_code'],
            'multilang' => true,
            'linked' => false,
        ],
        'Template' => [
            'db_field' => 'product_template_type',
            'required' => true,
            'multilang' => true,
        ],
        'Product links' =>[
            'process_get' => ['\Wizacha\Exim\MultiVendorProduct::getMvpProductLinks', '#key'],
            'export_only' => true,
            'multilang' => true,
            'linked' => false,
        ],
        'Images urls' =>[
            'db_field' => 'images_urls',
            'process_get' => ['\Wizacha\Exim\MultiVendorProduct::getMvpImagesUrls', '#key', '@images_delimiter'],
            'multilang' => true,
            'linked' => false,
        ],
        'Video' => [
            'db_field' => 'video',
            'process_get' => ['\Wizacha\Exim\MultiVendorProduct::getMvpVideo', '#key'],
            'linked' => false,
            'multilang' => true,
        ],
        'Alt Text' => array(
            'process_get' => ['\Wizacha\Exim\MultiVendorProduct::getMvpImagesAltText', '#key', '@images_delimiter', '#lang_code'],
            'multilang' => true,
            'linked' => false,
        ),
    ],
];

return $schema;
