<?php
declare(strict_types=1);
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

$schema = [
    'section' => 'accounting',
    'name' => __('accounting'),
    'pattern_id' => 'accounting',
    'key' => ['order_id'],
    'order' => 0,
    'table' => 'orders',
    'condition' => [
        'conditions' => [
            'is_parent_order' => 'N',
            'status' => 'H'
        ],
        'use_company_condition' => true,
        'use_date_range_condition' => true,
    ],
    'references' => [
        'payment_descriptions' => [
            'reference_fields' => ['payment_id' => '&payment_id', 'lang_code' => '#lang_code'],
            'join_type' => 'LEFT'
        ],
    ],
    'options' => [
        'lang_code' => [
            'title' => 'language',
            'type' => 'languages',
            'default_value' => [DEFAULT_LANGUAGE],
        ],
        'from_date' => [
            'title' => 'date_from',
            'type' => 'date',
        ],
        'to_date' => [
            'title' => 'date_to',
            'type' => 'date',
        ],
    ],
    'export_fields' => [
        'Language' => [
            'table' => 'payment_descriptions',
            'db_field' => 'lang_code',
            'type' => 'languages',
            'multilang' => true,
            'required' => true,
        ],
        'Order id' => [
            'db_field' => 'order_id',
            'alt_key' => true,
            'required' => true,
        ],
        'Company id' => [
            'db_field' => 'company_id',
            'required' => true,
        ],
        'Company Name' => [
            'process_get' => ['\Wizacha\Exim\Accounting::getCompanyName', '#key'],
            'linked' => false,
        ],
        'Payment Type' => [
            'table' => 'payment_descriptions',
            'db_field' => 'payment',
            'multilang' => true,
        ],
        'Totals: including taxes' => [
            'db_field' => 'total',
            'required' => true,
        ],
        'Totals: taxes' => [
            'process_get' => ['\Wizacha\Exim\Accounting::getTotalTaxes', '#key'],
            'linked' => false,
        ],
        'Totals: excluding taxes' => [
            'process_get' => ['\Wizacha\Exim\Accounting::getTotalExcludingTaxes', '#key'],
            'linked' => false,
        ],
        'Commissions: including taxes' => [
            'process_get' => ['\Wizacha\Exim\Accounting::getCommissionsIncludingTaxes', '#key'],
            'required' => true,
            'linked' => false,
        ],
        'Commissions: taxes' => [
            'process_get' => ['\Wizacha\Exim\Accounting::getCommissionsTaxes', '#key'],
            'linked' => false,
        ],
        'Commissions: excluding taxes' => [
            'process_get' => ['\Wizacha\Exim\Accounting::getCommissionsExcludingTaxes', '#key'],
            'linked' => false,
        ],
        'VendorShare: including taxes' => [
            'process_get' => ['\Wizacha\Exim\Accounting::getVendorShareIncludingTaxes', '#key'],
            'required' => true,
            'linked' => false,
        ],
        'Shipping costs: including taxes' => [
            'db_field' => 'shipping_cost',
            'required' => true,
        ],
        'Shipping costs: taxes' => [
            'process_get' => ['\Wizacha\Exim\Accounting::getShippingCostTaxes', '#key'],
            'linked' => false,
        ],
        'Shipping costs: excluding taxes' => [
            'process_get' => ['\Wizacha\Exim\Accounting::getShippingCostExcludingTaxes', '#key'],
            'linked' => false,
        ],
        'Promotion marketplace code' => [
            'process_get' => ['\Wizacha\Exim\Accounting::getMarketplaceDiscountCode', '#key'],
            'linked' => false,
        ],
        'Promotion marketplace amount' => [
            'process_get' => ['\Wizacha\Exim\Accounting::getMarketplaceDiscountTotal', '#key'],
            'linked' => false,
        ],
        'Order Completed On' => [
            'db_field' => 'w_last_status_change',
            'alt_key' => true,
        ],
    ],
];

return $schema;
