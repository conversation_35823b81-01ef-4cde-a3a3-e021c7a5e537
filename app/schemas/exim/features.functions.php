<?php
/***************************************************************************
 *                                                                          *
 *   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
 *                                                                          *
 * This  is  commercial  software,  only  users  who have purchased a valid *
 * license  and  accept  to the terms of the  License Agreement can install *
 * and use this program.                                                    *
 *                                                                          *
 ****************************************************************************
 * PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
 * "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
 ****************************************************************************/

use Tygh\Languages\Languages;
use Tygh\Registry;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Exim\Import\ImportReportMessage\AttributeMessage;
use Wizacha\Marketplace\GlobalState\GlobalState;

function fn_exim_get_product_feature_categories($data, $lang_code)
{

    $categories = '';
    if (empty($data['Group'])) {

        $categories = fn_get_category_name($data['Categories'], $lang_code);

        if (is_array($categories)) {
            $categories = implode(',', $categories);
        }
    }

    return $categories;
}

function fn_exim_set_product_feature_categories($feature_id, $feature_data, $lang_code)
{
    static $categories_ids;

    if (!empty($feature_data['parent_id'])) {

        $categories_path = '';
        $parent_feature = fn_get_product_feature_data($feature_data['parent_id']);

        if (!empty($parent_feature['categories_path'])) {
            $categories_path = $parent_feature['categories_path'];
        }

    } else {
        if (!empty($feature_data['categories_path'])) {
            $categories_path = array();

            if (!isset($categories_ids)) {
                $categories_ids = db_get_hash_single_array('SELECT category, category_id FROM ?:category_descriptions WHERE lang_code = ?s', array('category', 'category_id'), $lang_code);
            }

            $categories = explode(',', $feature_data['categories_path']);

            if (!empty($categories)) {
                foreach ($categories as $category) {
                    if (!empty($categories_ids[$category])) {
                        $categories_path[] = $categories_ids[$category];
                    }
                }
            }
            $categories_path = implode(',', $categories_path);

        } else {
            $categories_path = '';
        }
    }

    db_query("UPDATE ?:product_features SET categories_path = ?s WHERE feature_id = ?i", $categories_path, $feature_id);

    if ($feature_data['feature_type'] == 'G') {
        db_query("UPDATE ?:product_features SET categories_path = ?s WHERE parent_id = ?i", $categories_path, $feature_id);
    }

    return true;
}

function fn_exim_get_product_feature_group($group_id, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $group_name = false;

    if (!empty($group_id)) {
        $group_name = db_get_field('SELECT description FROM ?:product_features_descriptions WHERE feature_id = ?i AND lang_code = ?s', $group_id, $lang_code);
    }

    return $group_name;
}

function fn_exim_get_product_feature_group_id($group_name, $company_id, &$new_groups, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $group_id = false;

    if (!empty($group_name)) {
        $group_id = db_get_field('SELECT feature_id FROM ?:product_features_descriptions WHERE description = ?s AND lang_code = ?s', $group_name, $lang_code);

        if (empty($group_id)) {
            $group_data = array(
                'feature_id' => 0,
                'description' => $group_name,
                'lang_code' => $lang_code,
                'feature_type' => 'G',
                'status' => 'A',
                'company_id' => $company_id
            );

            $group_id = fn_update_product_feature($group_data, 0, $lang_code);
            $new_groups[] = $group_id;
        }
    }

    return $group_id;
}

function fn_import_get_feature_id(&$primary_object_id, $object, &$skip_get_primary_object_id)
{

    $feature_id = db_get_field('SELECT feature_id FROM ?:product_features_descriptions WHERE description = ?s AND lang_code = ?s', $object['description'], $object['lang_code']);

    if ($feature_id) {
        $primary_object_id = array(
            'feature_id' => $feature_id
        );
        $skip_get_primary_object_id = true;
    }
}

function fn_import_feature($data, &$skip_record, string $jobId)
{
    static $new_groups = array();

    $skip_record = true;

    $feature = reset($data);
    $main_lang = (reset(Languages::getAll())['lang_code']);

    if (!empty($feature['used_for_recommendations']) && !in_array($feature['used_for_recommendations'] , ['Y', 'N'])) {
        EximJobService::warning(
            AttributeMessage::EXIM_ATTRIBUTES_IGNORED,
            $jobId,
            $feature['line'],
            null,
            'used_for_recommendations'
        );
        unset($feature['used_for_recommendations']);
    }

    if (Registry::get('runtime.company_id')) {
        $company_id = Registry::get('runtime.company_id');

    } else {

        if (!empty($feature['company'])) {
            $company_id = fn_get_company_id_by_name($feature['company']);
        } else {
            $company_id = isset($feature['company_id']) ? $feature['company_id'] : Registry::get('runtime.company_id');
        }
    }

    if (!empty($feature['feature_id'])) {
        $feature_id = db_get_field('SELECT ?:product_features.feature_id FROM ?:product_features WHERE feature_id = ?i', $feature['feature_id']);
    }

    $parent_id = fn_exim_get_product_feature_group_id($feature['parent_id'], $company_id, $new_groups, $feature['lang_code']);

    $feature['company_id'] = $company_id;
    $feature['parent_id'] = $parent_id;
    $feature['variants'] = [];
    $variants = [];

    if (!empty($feature['Variants'])) {
        $variants = fn_explode(',', $feature['Variants']);

        list($origin_variants) = fn_get_product_feature_variants(array('feature_id' => $feature_id), 0, $main_lang);
        $feature['original_var_ids'] = implode(',', array_keys($origin_variants));

        foreach ($variants as $variant) {
            $feature['variants'][]['variant'] = $variant;
        }
    }

    if (empty($feature_id)) {
        $feature_id = fn_update_product_feature($feature, 0, $feature['lang_code']);

        // created
        EximJobService::info(AttributeMessage::EXIM_ATTRIBUTES_CREATE, $jobId, (string) $feature['line'], $feature_id, $feature['description']);

    } else {
        if (!fn_check_company_id('product_features', 'feature_id', $feature_id)) {
            // skip
            EximJobService::error(AttributeMessage::EXIM_ATTRIBUTES_CREATE_FAILED, $jobId, (string) $feature['line'], $feature_id, $feature['description']);

            return $feature_id;
        } else {
            // Legacy function doesn't handle correctly variants update from CSV.
            // Go to the end to see the hack for variants update.
            fn_update_product_feature($feature, $feature_id, $feature['lang_code'], false);

            if (in_array($feature_id, $new_groups)) {
                EximJobService::info(AttributeMessage::EXIM_ATTRIBUTES_CREATE, $jobId, (string) $feature['line'], $feature_id, $feature['description']);
            } else {
                EximJobService::info(AttributeMessage::EXIM_ATTRIBUTES_UPDATE, $jobId, (string) $feature['line'], $feature_id, $feature['description']);
            }
        }
    }

    //on recalcule les ids puisqu'ils sont maintenant en base à coup sûr
    [$origin_variants] = fn_get_product_feature_variants(['feature_id' => $feature_id], 0, $feature['lang_code']);

    ksort($origin_variants);
    $original_var_ids = array_keys($origin_variants);

    foreach ($data as $lang_code => $feature_data) {

        unset($feature_data['feature_id']);
        db_query('UPDATE ?:product_features_descriptions SET ?u WHERE feature_id = ?i AND lang_code = ?s', $feature_data, $feature_id, $lang_code);

        if (!empty($feature_data['Variants'])) {
            // On retire les espaces blancs et la virgule qui pourrait
            // trainer à la fin du champs sans être suivi d'une variante.
            // (ça pose des souci lorsqu'on compte le nombre de variante.
            // J'ai failli faire un array_filter sans callback, mais
            // on n'aurait plus pu importer de variantes qui sont converties
            // en false en PHP (particulièrement la valeur 0)
            $variants = trim($feature_data['Variants']);
            $variants = trim($variants, ',');
            $variants = fn_explode(',', $variants);

            // Dans un contexte de création multilangue, ça se passe bien. La premiere langue importée génére le bon
            // nombre de variants, dans le même ordre que la seconde ligne.
            // Dans un contexte de modification, il y a un import de variants qui existe et qui est celui qui doit être
            // utilisé. C'est pourquoi on ne touche pas à ce comportement pour l'instant.
            if (count($variants) <= count($original_var_ids)) {
                foreach ($variants as $key => $variant) {
                    db_query(
                        'UPDATE ?:product_feature_variant_descriptions SET variant = ?s WHERE variant_id = ?i AND lang_code = ?s',
                        $variant,
                        $original_var_ids[$key],
                        $lang_code
                    );
                }
            } elseif ($feature['lang_code'] !== null) {
                    fn_update_product_feature($feature, $feature_id, $feature['lang_code']);
            }
        }
    }

    return $feature_id;
}

function fn_exim_get_product_features_variants($feature_id, $lang_code)
{
    list($feature_variants) = fn_get_product_feature_variants(array('feature_id' => $feature_id), 0, $lang_code);
    //les variantes sont triées par nom par defaut mais pour l'import on a besoin
    //de les trier par ids pour garde rle même order de variante entre les langues
    //sinon on va avoir "Bleu, Noir"/"Black, Blue" au lieu de "Bleu, Noir"/"Blue, Black"
    ksort($feature_variants);

    $variants = array();
    foreach ($feature_variants as $variant) {
        $variants[] = $variant['variant'];
    }

    $variants = implode(', ', $variants);

    return $variants;
}
