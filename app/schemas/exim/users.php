<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ne<PERSON>    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;

include_once(Registry::get('config.dir.schemas') . 'exim/users.functions.php');

$schema = array(
    'section' => 'users',
    'pattern_id' => 'users',
    'name' => __('users'),
    'key' => array('user_id'),
    'order' => 0,
    'table' => 'users',
    'references' => array(
        'user_profiles' => array(
            'reference_fields' => array('user_id' => '#key', 'profile_type' => 'P'),
            'join_type' => 'LEFT'
        ),
    ),
    'range_options' => array(
        'selector_url' => 'profiles.manage',
        'object_name' => __('users'),
    ),
    'export_fields' => array(
        'E-mail' => array(
            'db_field' => 'email',
            'alt_key' => true,
            'required' => true,
        ),
        'Login' => array(
            'db_field' => 'user_login'
        ),
        'User type' => array(
            'db_field' => 'user_type'
        ),
        'Status' => array(
            'db_field' => 'status'
        ),
        'Title' => array(
            'db_field' => 'title'
        ),
        'First name' => array(
            'db_field' => 'firstname'
        ),
        'Last name' => array(
            'db_field' => 'lastname'
        ),
        'Company' => array(
            'db_field' => 'company'
        ),
        'Fax' => array(
            'db_field' => 'fax'
        ),
        'Phone' => array(
            'db_field' => 'phone'
        ),
        'Web site' => array(
            'db_field' => 'url'
        ),
        'Tax exempt' => array(
            'db_field' => 'tax_exempt'
        ),
        'Registration date' => array(
            'db_field' => 'timestamp',
            'process_get' => array('fn_timestamp_to_date', '#this'),
            'convert_put' => array('fn_date_to_timestamp'),
            'default' => array('time')
        ),
        'Language' => array(
            'db_field' => 'lang_code'
        ),
        'Billing: address id' => array(
            'db_field' => 'b_address_id',
            'table' => 'user_profiles',
        ),
        'Billing: label' => array(
            'db_field' => 'b_label',
            'table' => 'user_profiles',
        ),
        'Billing: title' => array(
            'db_field' => 'b_title',
            'table' => 'user_profiles',
        ),
        'Billing: first name' => array(
            'db_field' => 'b_firstname',
            'table' => 'user_profiles',
        ),
        'Billing: last name' => array(
            'db_field' => 'b_lastname',
            'table' => 'user_profiles',
        ),
        'Billing: company' => array(
            'db_field' => 'b_company',
            'table' => 'user_profiles',
        ),
        'Billing: address' => array(
            'db_field' => 'b_address',
            'table' => 'user_profiles',
        ),
        'Billing: address (line 2)' => array(
            'db_field' => 'b_address_2',
            'table' => 'user_profiles',
        ),
        'Billing: city' => array(
            'db_field' => 'b_city',
            'table' => 'user_profiles',
        ),
        'Billing: state' => array(
            'db_field' => 'b_state',
            'table' => 'user_profiles',
        ),
        'Billing: country' => array(
            'db_field' => 'b_country',
            'table' => 'user_profiles',
        ),
        'Billing: zipcode' => array(
            'db_field' => 'b_zipcode',
            'table' => 'user_profiles',
        ),
        'Billing: division code' => [
            'db_field' => 'b_division_code',
            'table' => 'user_profiles',
        ],
        'Billing: phone' => array(
            'db_field' => 'b_phone',
            'table' => 'user_profiles',
        ),
        'Billing: comment' => array(
            'db_field' => 'b_comment',
            'table' => 'user_profiles',
        ),
        'Shipping: address id' => array(
            'db_field' => 's_address_id',
            'table' => 'user_profiles',
        ),
        'Shipping: label' => array(
            'db_field' => 's_label',
            'table' => 'user_profiles',
        ),
        'Shipping: title' => array(
            'db_field' => 's_title',
            'table' => 'user_profiles',
        ),
        'Shipping: first name' => array(
            'db_field' => 's_firstname',
            'table' => 'user_profiles',
        ),
        'Shipping: last name' => array(
            'db_field' => 's_lastname',
            'table' => 'user_profiles',
        ),
        'Shipping: company' => array(
            'db_field' => 's_company',
            'table' => 'user_profiles',
        ),
        'Shipping: address' => array(
            'db_field' => 's_address',
            'table' => 'user_profiles',
        ),
        'Shipping: address (line 2)' => array(
            'db_field' => 's_address_2',
            'table' => 'user_profiles',
        ),
        'Shipping: city' => array(
            'db_field' => 's_city',
            'table' => 'user_profiles',
        ),
        'Shipping: state' => array(
            'db_field' => 's_state',
            'table' => 'user_profiles',
        ),
        'Shipping: country' => array(
            'db_field' => 's_country',
            'table' => 'user_profiles',
        ),
        'Shipping: zipcode' => array(
            'db_field' => 's_zipcode',
            'table' => 'user_profiles',
        ),
        'Shipping: division code' => [
            'db_field' => 's_division_code',
            'table' => 'user_profiles',
        ],
        'Shipping: phone' => array(
            'db_field' => 's_phone',
            'table' => 'user_profiles',
        ),
        'Shipping: comment' => [
            'db_field' => 's_comment',
            'table' => 'user_profiles',
        ],
        'Currency' => array(
            'db_field' => 'currency_code',
            'export_only' => true,
        ),
        'Is professional' => array(
            'db_field' => 'is_professional',
            'table' => 'user_profiles',
            'process_get' => ['Wizacha\Exim\Misc::getLegacyTrue', '#this'],
            'export_only' => true,
        ),
        'Job Title' => array(
            'db_field' => 'job_title',
            'table' => 'user_profiles',
            'export_only' => true,
        ),
        'Legal identifier' => array(
            'db_field' => 'legal_identifier',
            'table' => 'user_profiles',
            'export_only' => true,
        ),
        'Intra European Community VAT number' => array(
            'db_field' => 'intra_european_community_vat',
            'table' => 'user_profiles',
            'export_only' => true,
        ),
        'External identifier' => array(
            'db_field' => 'external_identifier',
            'table' => 'user_profiles',
            'export_only' => true,
        ),
        'Comment' => array(
            'db_field' => 'comment',
            'table' => 'user_profiles',
            'export_only' => true,
        ),
        'Loyalty identifier' => array(
            'db_field' => 'loyalty_identifier',
            'export_only' => true,
        ),
        'Birthdate' => array(
            'db_field' => 'birthday',
            'process_get' => ['Wizacha\Exim\Misc::ConvertDateFormat', '#this'],
            'export_only' => true,
        ),
        'ID' => array(
            'db_field' => 'user_id',
            'export_only' => true,
        ),
        'Extra' => array(
            'db_field' => 'extra',
            'process_get' => array('fn_exim_users_get_extra_data', '#key'),
            'export_only' => true,
        ),
    ),
);

return $schema;
