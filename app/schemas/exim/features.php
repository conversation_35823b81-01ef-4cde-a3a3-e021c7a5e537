<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ne<PERSON>    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;

include_once(Registry::get('config.dir.schemas') . 'exim/features.functions.php');

$schema = array(
    'section' => 'features',
    'name' => __('features'),
    'warning' => 'features_import_warning',
    'pattern_id' => 'features',
    'key' => array('feature_id'),
    'order' => 0,
    'table' => 'product_features',
    'order_by' => [
        'feature_id',
        'lang_code',
    ],
    'references' => array(
        'product_features_descriptions' => array(
            'reference_fields' => array('feature_id' => '#key', 'lang_code' => '#lang_code'),
            'join_type' => 'LEFT'
        ),
        'companies' => array(
            'reference_fields' => array('company_id' => '&company_id'),
            'join_type' => 'LEFT',
            'import_skip_db_processing' => true
        ),
    ),
    'options' => array(
        'lang_code' => array(
            'title' => 'language',
            'type' => 'languages',
            'default_value' => array(DEFAULT_LANGUAGE),
        )
    ),
    'condition' => array(
        'use_company_condition' => true,
    ),
    'export_fields' => array(
        'Feature name' => array(
            'table' => 'product_features_descriptions',
            'db_field' => 'description',
            'multilang' => true,
            'required' => true,
        ),
        'Feature ID' => array(
            'db_field' => 'feature_id',
            'alt_key' => true,
            'required' => true,
            'multilang' => true,
        ),
        'Language' => array(
            'table' => 'product_features_descriptions',
            'db_field' => 'lang_code',
            'type' => 'languages',
            'required' => true,
            'multilang' => true,
        ),
        'Type' => array(
            'db_field' => 'feature_type',
            'required' => true,
            'multilang' => true,
        ),
        'Feature code' => array(
            'db_field' => 'feature_code',
            'multilang' => true,
        ),
        'Group' => array(
            'db_field' => 'parent_id',
            'process_get' => array('fn_exim_get_product_feature_group', '#this', '#lang_code'),
            'return_result' => true,
            'multilang' => true,
        ),
        'Feature Description' => array(
            'table' => 'product_features_descriptions',
            'db_field' => 'full_description',
            'multilang' => true,
        ),
        'Categories' => array(
            'db_field' => 'categories_path',
            'multilang' => true,
        ),
        'Variants' => array(
            'process_get' => array('fn_exim_get_product_features_variants', '#key', '#lang_code'),
            'linked' => false, // this field is not linked during import-export
            'multilang' => true,
        ),
        'Show on the features tab' => array(
            'db_field' => 'display_on_product',
            'multilang' => true,
        ),
        'Show in faceting' => array(
            'db_field' => 'display_on_faceting',
            'multilang' => true,
        ),
        'Is searchable' => array(
            'db_field' => 'is_searchable',
            'multilang' => true,
        ),
        'Used for recommendations' => array(
            'db_field' => 'used_for_recommendations',
            'multilang' => true,
        ),
        'Position' => array(
            'db_field' => 'position',
            'multilang' => true,
        ),
        'Status' => array(
            'db_field' => 'status',
            'multilang' => true,
        ),
    ),
);

$schema['import_process_data'] = array(
    'import_feature' => array(
        'function' => 'fn_import_feature',
        'args' => array('$data', '$skip_record', '$jobId'),
        'import_only' => true,
    ),
);

return $schema;
