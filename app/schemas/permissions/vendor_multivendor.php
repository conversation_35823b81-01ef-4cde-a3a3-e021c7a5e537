<?php
/***************************************************************************
 *                                                                          *
 *   (c) 2004 <PERSON> V<PERSON>, <PERSON>ey <PERSON>, <PERSON>ya <PERSON>nev    *
 *                                                                          *
 * This  is  commercial  software,  only  users  who have purchased a valid *
 * license  and  accept  to the terms of the  License Agreement can install *
 * and use this program.                                                    *
 *                                                                          *
 ****************************************************************************
 * PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
 * "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
 ****************************************************************************/

use Wizacha\FeatureFlag\FeatureFlagService;

$schema = [
    'default_permission' => false,
    'controllers' => [
        'w_links_vendor_categories' => ['permissions' => true],
        'auth' => [
            'permissions' => true,
        ],
        'feedback' => [
            'permissions' => false,
        ],
        'index' => [
            'permissions' => true,
        ],
        'elf_connector' => [
            'modes' => [
                'files' => [
                    'permissions' => false,
                ],
            ],
            'permissions' => true,
        ],

        'profiles' => [
            'modes' => [
                'update_cards' => [
                    'permissions' => false
                ],
                'delete_profile' => [
                    'permissions' => false
                ],
                'delete_card' => [
                    'permissions' => false
                ],
                'manage' => [
                    'param_permissions' => [
                        'user_type' => [
                            'P' => false,
                        ],
                        'default_permission' => true,
                    ],
                    'condition' => [
                        'user_type' => [
                            'A' => [
                                'operator' => 'and',
                                'function' => ['fn_check_permission_manage_profiles', 'A'],
                            ],
                            'V' => [
                                'operator' => 'and',
                                'function' => ['fn_check_permission_manage_profiles', 'V'],
                            ],
                        ]
                    ],
                ],
                'act_as_user' => [
                    'permissions' => false,
                    'condition' => [
                        'operator' => 'or',
                        'function' => ['fn_check_permission_act_as_user'],
                    ],
                ]
            ],
            'permissions' => true,
        ],
        'companies' => [
            'modes' => [
                'add' => [
                    'permissions' => false
                ],
                'delete' => [
                    'permissions' => false
                ],
                'update_status' => [
                    'permissions' => false
                ],
                'm_activate' => [
                    'permissions' => false
                ],
                'm_disable' => [
                    'permissions' => false
                ],
                'm_delete' => [
                    'permissions' => false
                ],
            ],
            'permissions' => true,
        ],
        'profile_fields' => [
            /*'modes' => array (
                'manage' => array (
                    'permissions' => true
                ),
            ),*/
            'permissions' => false,
        ],
        'sales_reports' => [
            'modes' => [
                'view' => [
                    'permissions' => true,
                ],
                'set_report_view' => [
                    'permissions' => true,
                ],
            ],
            'permissions' => false,
        ],

        'categories' => [
            'modes' => [
                'delete' => [
                    'permissions' => false
                ],
                // Why .add was true ???
                'add' => [
                    'permissions' => false
                ],
                'm_add' => [
                    'permissions' => false
                ],
                'm_update' => [
                    'permissions' => false
                ],
                'picker' => [
                    'permissions' => true
                ],
            ],
            'permissions' => ['GET' => true, 'POST' => false],
        ],

        'taxes' => [
            'modes' => [
                'update' => [
                    'permissions' => ['GET' => true, 'POST' => false],
                ],
                'manage' => [
                    'permissions' => ['GET' => true, 'POST' => false],
                ],
            ],
            'permissions' => false,
        ],

        'image' => [
            'modes' => [
                'barcode' => [
                    'permissions' => true,
                ],
                'delete_image' => [
                    'permissions' => true,
                ],
                'thumbnail' => [
                    'permissions' => true,
                ],
            ],
            'permissions' => false,
        ],

        'search' => [
            'modes' => [
                'results' => [
                    'permissions' => true,
                ],
            ],
            'permissions' => false,
        ],

        'states' => [
            'modes' => [
                'manage' => [
                    'permissions' => true,
                ],
            ],
            'permissions' => false,
        ],

        'countries' => [
            'modes' => [
                'manage' => [
                    'permissions' => ['GET' => true, 'POST' => false],
                ],
            ],
            'permissions' => false,
        ],

        'destinations' => [
            'modes' => [
                'update' => [
                    'permissions' => ['GET' => true, 'POST' => false],
                ],
                'manage' => [
                    'permissions' => ['GET' => true, 'POST' => false],
                ],
            ],
            'permissions' => false,
        ],

        'localizations' => [
            /*'modes' => array(
                'update' => array(
                    'permissions' => array ('GET' => true, 'POST' => false),
                ),
                'manage' => array(
                    'permissions' => true,
                ),
            ),*/
            'permissions' => false,
        ],

        'languages' => [
            /*'modes' => array(
                'manage' => array(
                    'permissions' => true,
                ),
            ),*/
            'permissions' => false,
        ],

        'product_features' => [
            'modes' => [
                'update' => [
                    'permissions' => ['GET' => true, 'POST' => false],
                ],
                'manage' => [
                    'permissions' => ['GET' => true, 'POST' => false],
                ],
                'get_feature_variants_list' => [
                    'permissions' => true,
                ],
                'get_variants' => [
                    'permissions' => ['GET' => true, 'POST' => false],
                ]
            ],
            'permissions' => false,
        ],

        'statuses' => [
            /*'modes' => array(
                'update' => array(
                    'permissions' => array ('GET' => true, 'POST' => false),
                ),
                'manage' => array(
                    'permissions' => true,
                ),
            ),*/
            'permissions' => false,
        ],

        'currencies' => [
            'modes' => [
                'update' => [
                    'permissions' => ['GET' => true, 'POST' => false],
                ],
                'manage' => [
                    'permissions' => true,
                ],
            ],
            'permissions' => false,
        ],
        'exim' => [
            'modes' => [
                'export' => [
                    'permissions' => true,
                ],
                'import' => [
                    'permissions' => true,
                ],
            ],
            'permissions' => true,
        ],

        'exim_jobs' => [
            'modes' => [
                'manage' => [
                    'permissions' => true,
                ],
                'update' => [
                    'permissions' => true,
                ],
            ],
            'permissions' => true,
        ],

        'auth_logs' => [
            'modes' => [
                'manage' => [
                    'permissions' => false,
                ],
            ],
            'permissions' => false
        ],
        'product_filters' => [
            'modes' => [
                'update' => [
                    'permissions' => ['GET' => true, 'POST' => false],
                ],
                'manage' => [
                    'permissions' => ['GET' => true, 'POST' => false],
                ],
                'delete' => [
                    'permissions' => false,
                ],
            ],
            'permissions' => true,
        ],

        'orders' => [
            'modes' => [
                'details' => [
                    'permissions' => true,
                ],
                'delete' => [
                    'permissions' => false,
                ],
                'delete_orders' => [
                    'permissions' => false,
                ],
                'manage' => [
                    'permissions' => true,
                ],
            ],
            'permissions' => true,
        ],

        'bundle' => [
            'permissions' => true,
            'modes' => [
                'csvfile' => [
                    'permissions' => false,
                ]
            ]
        ],

        'shippings' => [
            'permissions' => true,
        ],

        'tags' => [
            'modes' => [
                'list' => [
                    'permissions' => true,
                ],
            ],
            'permissions' => false,
        ],

        'pages' => [
            'modes' => [
                /*'m_add' => array(
                    'permissions' => false,
                ),
                'm_update' => array(
                    'permissions' => false,
                ),*/
            ],
            'permissions' => true,
        ],

        'products' => [
            'modes' => [
            ],
            'permissions' => true,
        ],

        'product_options' => [
            'permissions' => true,
        ],

        'promotions' => [
            'permissions' => false,
        ],

        'shipments' => [
            'permissions' => true,
        ],

        'attachments' => [
            'permissions' => true,
        ],

        'block_manager' => [
            'permissions' => false,
        ],

        'tools' => [
            'modes' => [
                'update_position' => [
                    'param_permissions' => [
                        'table' => [
                            'images_links' => true,
                        ]
                    ]
                ],
                'update_status' => [
                    'param_permissions' => [
                        'table' => [
                            'shippings' => true,
                            'products' => true,
                            'product_options' => true,
                            'attachments' => true,
                            'product_files' => true,
                            'pages' => 'true',
                        ]
                    ]
                ],
                'cleanup_history' => [
                    'permissions' => true
                ],
            ]
        ],
        'logs' => [
            'permissions' => true,
        ],

        'available_offers' => [
            'modes' => [
                'ajax_load' => [
                    'permissions' => true,
                ],
            ],
        ],

        'subscriptions' => [
            'modes' => [
                'manage' => [
                    'permissions' => true,
                ],
                'update' => [
                    'permissions' => true,
                ],
            ],
            'permissions' => true,
        ],
    ],

    'addons' => [
        'affiliate' => [
            'permission' => false,
        ],
        'suppliers' => [
            'permission' => false,
        ],
        'access_restrictions' => [
            'permission' => false,
        ],
        'age_verification' => [
            'permission' => false,
        ],
        'anti_fraud' => [
            'permission' => false,
        ],
        'banners' => [
            'permission' => false,
        ],
        'bestsellers' => [
            'permission' => false,
        ],
        'customers_also_bought' => [
            'permission' => false,
        ],
        'form_builder' => [
            'permission' => false,
        ],
        'gift_registry' => [
            'permission' => false,
        ],
        'google_analytics' => [
            'permission' => false,
        ],
        'google_sitemap' => [
            'permission' => false,
        ],
        'hot_deals_block' => [
            'permission' => false,
        ],
        'quickbooks' => [
            'permission' => false,
        ],
        'reward_points' => [
            'permission' => false,
        ],
        'social_buttons' => [
            'permission' => false,
        ],
        'sms_notifications' => [
            'permission' => false,
        ],
    ],
    'export' => [
        'sections' => [
            'translations' => [
                'permission' => false,
            ],
            'users' => [
                'permission' => false,
            ],
            'features' => [
                'permission' => false,
            ],
            'marketplace_discounts' => [
                'permission' => false,
            ],
            'companies' => [
                'permission' => false,
            ],
        ],
        'patterns' => [
            'google' => [
                'permission' => false,
            ],
        ],
    ],
    'import' => [
        'sections' => [
            'translations' => [
                'permission' => false,
            ],
            'orders' => [
                'permission' => false,
            ],
            'users' => [
                'permission' => false,
            ],
            'features' => [
                'permission' => false,
            ],
            'multi_vendor_products' => [
                'permission' => false,
            ],
            'marketplace_discounts' => [
                'permission' => false,
            ],
            'inventory_prices' => [
                'permission' => true,
            ],
            'inventory_quantities' => [
                'permission' => true,
            ],
        ],
        'patterns' => [
        ],
    ],
];

if (Tygh\Registry::get('config.sandbox')) {
    $schema['controllers']['w_order_management'] = [
        'permissions' => true,
        'mode' =>[
            'place_order' => true,
        ]
    ];

    $schema['controllers']['rma']['modes']['create_return'] = ['permissions' => true];
    $schema['controllers']['rma']['modes']['add_return'] = ['permissions' => true];
}
if (
    container()->getParameter('are_shippings_editable_by_vendors')
    || ACCOUNT_TYPE == 'admin' //Allow admin account to perform shippings rate management for vendors
) {

    $schema['controllers']['w_shippings'] = array(
        'modes' => array(
            'update_status' => array(
                'permissions' => true
            )
        )
    );
    $schema['controllers']['w_product_rate'] = array(
        'modes' => array(
            'update' => array(
                'permissions' => true
            ),
            'update_status' => array(
                'permissions' => true
            ),
            'delete' => array(
                'permissions' => true
            ),
            'manage' => array(
                'permissions' => true
            )
        )
    );
} else {
    $schema['controllers']['shippings'] = [
        'modes' => [
            'manage' => [
                'permissions' => false,
            ],
            'update' => [
                'permissions' => false,
            ]
        ]
    ];
}

$schema['controllers']['rma'] = [
    'permissions' => false,
    'modes' => [
        'returns' => ['permissions' => true],
        'details' => ['permissions' => true],
        'update_status' => ['permissions' => true],
        'print_rma' => ['permissions' => true],
    ]
];

if (container()->get(FeatureFlagService::class)->get('feature.discussions_allowed_for_admin') === true) {
    $schema['controllers']['discuss'] = [
        'permissions' => true,
    ];
}

$schema['tools']['modes']['update_status']['param_permissions']['table']['rma_properties'] = 'manage_rma';

return $schema;
