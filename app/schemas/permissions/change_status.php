<?php
use Wizacha\OrderStatus;
use Wizacha\Marketplace\Order\RmaStatus;

/*
 * if $schema[$type][$status_from][$status_to] is === true echo the name of status in bouton, if it's equivalent to true,
 * echo the variable and if it's false, remove the button.
 */

$schema['orders'] = [
    OrderStatus::STANDBY_VENDOR => [
        OrderStatus::PROCESSING_SHIPPING => __("approve"),
        OrderStatus::VENDOR_DECLINED => __("decline"),
        OrderStatus::PROCESSED  => __("treated"),
    ],
    OrderStatus::STANDBY_SUPPLYING => [
        OrderStatus::PROCESSING_SHIPPING  => __("approve"),
        OrderStatus::VENDOR_DECLINED => __("decline"),
    ],
    OrderStatus::PROCESSING_SHIPPING => [
        OrderStatus::PROCESSED  => __("treated"),
    ],
];

$schema['rma'] = [
    RmaStatus::REQUESTED => [RmaStatus::RECEIVED => true],
];

return $schema;
