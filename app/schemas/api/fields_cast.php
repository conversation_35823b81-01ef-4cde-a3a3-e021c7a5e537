<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

const W_INT     = 'intval';
const W_FLOAT   = 'floatval';
const W_STRING  = 'strval';
const W_BOOL    = 'boolval';

$date_rfc3339 = function(string $value): string {
    $dateTime = new \DateTime($value, new DateTimeZone('UTC'));
    $dateTime->setTimezone(new DateTimeZone(date_default_timezone_get()));

    return $dateTime->format(\DateTime::RFC3339);
};

$int_list       = function($value) {
    return empty($value)?[]:array_map(W_INT,(array)explode(',',$value));
};

$nullable_int   = function ($value) {
    return is_null($value) ? null : (int) $value;
};

$nullable_float = function ($value) {
    return is_null($value) ? null : floatval($value);
};

return [
    'category_id'   => W_INT,
    'discount'      => W_FLOAT,
    'main_category' => W_INT,
    'parent_id'     => W_INT,
    'categories_path'   => $int_list,
    'feature_id'    => W_INT,
    'variant_id'    => W_INT,
    'product_id'    => W_INT,
    'amount'        => W_INT,
    'w_green_tax'   => W_FLOAT,
    'option_id'     => W_INT,
    'order_id'      => W_INT,
    'price'         => W_FLOAT,
    'avail_since'   => W_INT,
    'tax_id'        => W_INT,
    'priority'      => W_INT,
    'timestamp'     => W_INT,
    'updated_timestamp' => W_INT,
    'promotion_id'  => W_INT,
    'company_id'    => W_INT,
    'is_fillable'   => W_BOOL,
    'shipment_id'   => W_INT,
    'shipment_timestamp'=> W_INT,
    'shipping_cost' => W_FLOAT,
    'shipping_id'   => W_INT,
    'specific_rate' => W_BOOL,
    'tax_subtotal'  => W_FLOAT,
    'rate_value'    => W_FLOAT,
    'to_date'       => W_INT,
    'total'         => W_FLOAT,
    'from_date'     => W_INT,
    'user_id'       => W_INT,
    'value'         => W_FLOAT,
    'value_str'     => W_STRING,
    'weight'        => W_FLOAT,
    'w_invoice_number' => W_STRING,
    'invoice_date' => W_STRING,
    'w_price'       => W_FLOAT,
    'crossed_out_price' => W_FLOAT,
    'affiliate_link' => W_STRING,
    'infinite_stock' => W_BOOL,
    'w_last_status_change' => $date_rfc3339,
    'max_price_adjustment' => $nullable_int,
    'position' => $nullable_int,
    'is_subscription' => W_BOOL,
    'is_renewable' => W_BOOL,
    'is_paid' => W_BOOL,
    'carriage_paid' => W_BOOL,
    'carriage_paid_threshold' => $nullable_float,
    'refund_status' => W_INT,
    'balance' => W_FLOAT,
    'is_customer_professional' => W_BOOL,
    'quote_requests_min_quantity' => W_INT,
    'is_exclusive_to_quote_requests' => W_BOOL,
];
