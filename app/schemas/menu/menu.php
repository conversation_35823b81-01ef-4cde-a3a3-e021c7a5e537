<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>nev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use \Tygh\Registry;
use Wizacha\Misc;

/*
    Every item can has any additional attributes.
    The base HTML struct of menu item is:
        <li class="some classes">
            <a href="some.html">Title</a>
        </li>

    So you can use the following array structure to specify your attrs:
    'addons' => array(
        'href' => 'addons.manage',
        'position' => 100,
        'attrs' => array(
            'class' => 'test-addon-class',
            'main' => array( // Attributes for <li>
                'custom-li-attr' => 'my-li-attr',
            ),
            'href' => array( // Attributes for <a>
                'custom-a-attr' => 'my-a-attr',
            ),
        ),
    ),

    As a result you will get the following HTML code:
    <li class="some classes test-addon-class" custom-li-attr="my-li-attr">
        <a href="some.html" custom-a-attr="my-a-attr">Title</a>
    </li>
*/

$schema = [
    'top' => [
        'addons' => [
            'items' => [
                'dolist' => [
                    'href'     => 'dolist.manage',
                    'position' => 400,
                    'superadmin_only' => true,
                ],
            ],
            'position' => 100,
        ],
        'administration' => [
            'items' => [
                'addons_divider' => [
                    'type' => 'divider',
                    'position' => 110,
                ],
                'discuss_menu' => [
                    'href' => 'discuss.list',
                    'position' => 150,
                    'count_function' => 'getDiscussCount',
                ],
                'payment_methods' => [
                    'href' => 'payments.manage',
                    'position' => 200,
                ],
                'shipping_methods' => [
                    'href' => 'shippings.manage',
                    'position' => 100,
                ],
                'taxes' => [
                    'href' => 'taxes.manage',
                    'position' => 200,
                ],
                'menu_i18n' => [
                    'href' => 'states.manage',
                    'type' => 'title',
                    'position' => 300,
                    'superadmin_only' => true,
                    'subitems' => [
                        'states' => [
                            'href' => 'states.manage',
                            'position' => 300,
                            'superadmin_only' => true,
                        ],
                        'countries' => [
                            'href' => 'countries.manage',
                            'position' => 400,
                            'superadmin_only' => true,
                        ],
                        'locations' => [
                            'href' => 'destinations.manage',
                            'position' => 500,
                            'superadmin_only' => true,
                        ],
                        'localizations' => [
                            'href' => 'localizations.manage',
                            'position' => 600,
                            'superadmin_only' => true,
                        ],
                    ],
                ],
                'order_statuses' => [
                    'href' => 'statuses.manage?type=O',
                    'position' => 400,
                ],
                'order_statuses_divider' => [
                    'type' => 'divider',
                    'position' => 410,
                ],
                'translations' => [
                    'href' => 'languages.translations',
                    'position' => 700,
                ],
                'languages' => [
                    'href' => 'languages.manage',
                    'position' => 700,
                    'superadmin_only' => true,
                ],
                'languages_divider' => [
                    'type' => 'divider',
                    'position' => 710,
                ],
                'import_data' => [
                    'href' => 'exim.import',
                    'position' => 1200,
                    'subitems' => [
                        'products' => [
                            'href' => 'exim.import?section=products',
                            'position' => 200,
                        ],
                        'inventory_prices' => [
                            'href' => 'exim.import?section=inventory_prices',
                            'position' => 300,
                        ],
                        'inventory_quantities' => [
                            'href' => 'exim.import?section=inventory_quantities',
                            'position' => 400,
                        ],
                        'csvFiles' => [
                            'href' => 'admin_Csvfile_list',
                            'symfony' => true,
                            'position' => 50,
                            'superadmin_only' => true,
                        ],
                        'w_images_hosting' => [
                            'href' => 'exim.import&open_image',
                            'position' => 900,
                        ],
                        'w_links_vendor_categories' => [
                            'href' => 'w_links_vendor_categories.update',
                            'position' => 1000,
                        ],
                        'translations' => [
                            'href' => 'exim.import?section=translations',
                            'position' => 1200,
                        ],
                    ],
                ],
                'export_data' => [
                    'href' => 'exim.export',
                    'position' => 1300,
                    'subitems' => [
                        'orders' => [
                            'href' => 'exim.export?section=orders',
                            'position' => 100,
                        ],
                        'products' => [
                            'href' => 'exim.export?section=products',
                            'position' => 200,
                        ],
                        'features' => [
                            'href' => 'exim.export?section=features',
                            'position' => 300,
                        ],
                        'translations' => [
                            'href' => 'exim.export?section=translations',
                            'position' => 400,
                        ],
                        'newsletter' => [
                            'href' => 'subscribers.export',
                            'position' => 500,
                        ],
                        'accounting' => [
                            'href' => 'exim.export?section=accounting',
                            'position' => 600,
                        ],
                        'users' => [
                            'href' => 'exim.export?section=users',
                            'position' => 1600,
                        ],
                    ],
                ],
                'exim_jobs' => [
                    'href' => 'exim_jobs.manage',
                    'position' => 1400,
                    'subitems' => [
                        'exim_jobs_import' => [
                            'href' => 'exim_jobs.manage?section=import',
                            'position' => 100,
                        ],
                        'exim_jobs_export' => [
                            'href' => 'exim_jobs.manage?section=export',
                            'position' => 200,
                        ],
                    ],
                ],
                'auth_logs' => [
                    'href' => 'auth_logs.manage',
                    'position' => 1450,
                ],
                'export_data_divider' => [
                    'type' => 'divider',
                    'position' => 1500,
                ],
                'feature_flags' => [
                    'href' => 'settings.flags',
                    'position' => 1600,
                    'type' => 'setting',
                    'superadmin_only' => true,
                ],
                'container_parameters' => [
                    'href'     => 'container_parameters.manage',
                    'position' => 1700,
                    'superadmin_only' => true,
                ]
            ],
            'position' => 600,
        ],
        'settings' => [
            'items' => [
                'General' => [
                    'href' => 'settings.manage?section_id=General',
                    'position' => 100,
                    'type' => 'setting',
                    'superadmin_only' => true,
                ],
                'Appearance' => [
                    'href' => 'settings.manage?section_id=Appearance',
                    'position' => 200,
                    'type' => 'setting',
                    'superadmin_only' => true,
                ],
                'Appearance_divider' => [
                    'type' => 'divider',
                    'position' => 300,
                    'superadmin_only' => true,
                ],
                'settings_sections' => [
                    'href' => 'settings.sections',
                    'position' => 100,
                    'superadmin_only' => true,
                ],
                'Company' => [
                    'href' => 'settings.manage?section_id=Company',
                    'position' => 500,
                    'type' => 'setting',
                ],
                'settings_descriptions' => [
                    'href' => 'settings.descriptions?section_id=Company',
                    'position' => 500,
                    'type' => 'setting',
                ],
                'Security' => [
                    'href' => 'settings.manage?section_id=Security',
                    'position' => 1200,
                    'type' => 'setting',
                    'superadmin_only' => true,
                ],
                'Reports' => [
                    'href' => 'settings.manage?section_id=Reports',
                    'position' => 1600,
                    'type' => 'setting',
                    'superadmin_only' => true,
                ],
                'Vendors' => [
                    'href' => 'settings.manage?section_id=vendors',
                    'position' => 950,
                    'type' => 'setting',
                    'superadmin_only' => true,
                ],
            ],
            'position' => 700,
        ],
    ],

    'central' => [
        'orders' => [
            'items' => [
                'view_orders' => [
                    'href' => 'orders.manage',
                    'alt' => 'order_management',
                    'position' => 100,
                    'count_function' => 'getOrderCount',
                ],
                'w_rma_standby_refund' => [
                    'href' =>'?is_search=Y&period=A&action=0&request_status[]=A&dispatch[rma.returns]=Recherche+rapide&hint_new_view=Nom',
                    'position' => 200,
                    'count_function' => 'getRmaReceivedCount',
                ],
                'return_requests' => [
                    'href' => 'rma.returns',
                    'position' => 300,
                    'count_function' => 'getRmaRequestedCount',
                ],
                'rma_reasons' => [
                    'href' => 'rma.properties?property_type=R',
                    'position' => 400,
                ],
                'rma_actions' => [
                    'href' => 'rma.properties?property_type=A',
                    'superadmin_only' => true,
                    'position' => 500,
                ],
                'rma_request_statuses' => [
                    'href' => 'statuses.manage?type=R',
                    'superadmin_only' => true,
                    'position' => 600,
                ],
            ],
            'position' => 100,
        ],
        'catalog' => [
            'items' => [
                'categories' => [
                    'href' => 'categories.manage',
                    'position' => 250,
                ],
                'products' => [
                    'href' => 'products.manage',
                    'alt' => 'product_options.inventory,product_options.exceptions',
                    'position' => 100,
                ],
                'features' => [
                    'href' => 'product_features.manage',
                    'position' => 300,
                ],
                'options' => [
                    'href' => 'product_options.manage',
                    'position' => 500,
                ],
            ],
            'position' => 200,
            'href' => 'products.manage',

        ],
        'users_accounts' => [
            'items' => [
                'administrators' => [
                    'href' => 'profiles.manage?user_type=A',
                    'alt' => 'profiles.update?user_type=A',
                    'position' => 200,
                ],
                'customers' => [
                    'href' => 'profiles.manage?user_type=C',
                    'alt' => 'profiles.update?user_type=C',
                    'position' => 300,
                ],
                'users' => [
                    'href' => 'profiles.manage',
                    'alt' => 'profiles.update',
                    'position' => 100,
                ],
                'vendor_administrators' => [
                    'href' => 'profiles.manage?user_type=V',
                    'alt' => 'profiles.update?user_type=V',
                    'position' => 250,
                ],
            ],
            'position' => 300,
        ],
        'content' => [
            'items' => [
                'content_pages' => [
                    'href' => 'pages.manage?get_tree=multi_level',
                    'position' => 100,
                ],
                'comments_and_reviews' => [
                    'href' => 'discussion_manager.manage',
                    'position' => 300,
                    'count_function' => 'getDiscussionNewCount',
                ],
                'discussion_title_home_page' => [
                    'superadmin_only' => true,
                    'href' => 'discussion.update?discussion_type=E',
                    'position' => 602
                ],
                'menus' => [
                    'href' => 'menus.manage',
                    'alt' => 'static_data.manage?section=A',
                    'position' => 200,
                ]
            ],
            'position' => 500,
        ],
        'marketing' => [
            'items' => [
                'w_promotions_catalog' => [
                    'href' => 'promotions.manage?zone=catalog',
                    'position' => 100
                ],
                'w_promotions_basket' => [
                    'href' => 'promotions.manage?zone=basket',
                    'position' => 200
                ],
                'banners' => [
                    'href' => 'banners.manage',
                    'position' => 500
                ],
                'newsletters' => [
                    'href' => 'subscribers.export',
                    'position' => 600,
                ],
            ],
            'position' => 400,
        ],
        'vendors' => [//item renamed 'my_account' for vendors
            'items' => [
                'vendors' => [
                    'href' => 'companies.manage',
                    'alt' => 'companies.add,companies.update',
                    'position' => 100,
                    'count_function' => 'getCompaniesNewCount',
                ],
                'report' => [
                    'href' => 'financial_flows_history',
                    'position' => 175,
                    'symfony' => true,
                    'subitems' => [
                        'financial_flows_history' => [
                            'href' => 'financial_flows_history',
                            'symfony' => true,
                            'position' => 100,
                        ],
                        'commission_monitoring' => [
                            'href' => 'commission_monitoring',
                            'symfony' => true,
                            'position' => 300,
                        ],
                    ]
                ],
                'vendor_account_balance_ttc' => [
                    'href' => 'exim.export&section=accounting',
                    'position' => 210,
                ],
                'vendor_account_balance_ht' => [
                    'href' => 'exim.export&section=accounting',
                    'position' => 220,
                ],
                'moderation_products' => [
                    'href' => 'premoderation.products_approval?approval_status=P&companies_status[]=A',
                    'position' => 300,
                    'count_function' => 'getApprovalProductsCount',
                ],
                'moderation_options' => [
                    'href'     => 'premoderation.options_approval',
                    'position' => 400,
                    'count_function' => 'getApprovalOptionsCount',
                ],
            ],
            'position' => 600,
        ],
    ],
];

if (Registry::get('runtime.company_id')) {
    unset($schema['central']['products']['items']['option']);

    $schema['top']['administration']['items']['import_data'] = [
        'href' => 'exim.import',
        'position' => 1200,
        'subitems' => [
            'products' => [
                'href' => 'exim.import?section=products',
                'position' => 200,
            ],
            'inventory_prices' => [
                'href' => 'exim.import?section=inventory_prices',
                'position' => 300,
            ],
            'inventory_quantities' => [
                'href' => 'exim.import?section=inventory_quantities',
                'position' => 400,
            ],
            'w_images_hosting' => [
                'href' => 'exim.import&open_image',
                'position' => 900,
            ],
            'w_links_vendor_categories' => [
                'href' => 'w_links_vendor_categories.update',
                'position' => 1000,
            ],
        ],
    ];

    $schema['top']['administration']['items']['export_data'] = [
        'href' => 'exim.export',
        'position' => 1300,
        'subitems' => [
            'orders' => [
                'href' => 'exim.export?section=orders',
                'position' => 100,
            ],
            'products' => [
                'href' => 'exim.export?section=products',
                'position' => 200,
            ],
            'accounting' => [
                'href' => 'exim.export?section=accounting',
                'position' => 300,
            ],
        ],
    ];

    //Move last customer options into Vendor account tab
    $schema['central']['vendors']['items']['vendor_administrators'] = $schema['central']['users_accounts']['items']['vendor_administrators'];

    //Then remove users_accounts tab
    unset($schema['central']['users_accounts']);

    //Rename 'vendors' menu to 'my_account'
    $schema['central']['my_account'] = $schema['central']['vendors'];
    unset($schema['central']['vendors']);
    unset($schema['central']['my_account']['items']['vendors']);
    $schema['central']['my_account']['items']['w_vendor_account_informations'] = [
        'href' => 'companies.update?company_id='.Registry::get('runtime.company_id'),
        'position' => 100,
    ];

    //Simplify shipping modes entry, since there is only this item (thank to permissions)
    unset($schema['top']['administration']['items']['shippings_taxes']);
    $schema['top']['administration']['items']['shipping_methods'] = [
        'href'  => 'shippings.manage',
        'position'  => 300,
    ];

    $schema['top']['administration']['items']['import_data']['subitems']['w_automated_feeds'] = [
        'href' => 'companies.w_automated_feeds',
        'position' => 1000,
    ];

} else{
    $schema['top']['administration']['items']['help_divider'] = [
        'type'      => 'divider',
        'position'  => 1700,
    ];

    $schema['top']['administration']['items']['import_data']['subitems']['categories'] = [
        'href' => 'exim.import?section=categories',
        'position' => 999,
    ];

    $schema['top']['administration']['items']['export_data']['subitems']['categories'] = [
        'href' => 'exim.export?section=categories',
        'position' => 999,
    ];

    //Display link only if admin must approve new users.
    if (container()->getParameter('feature.approve_user_by_admin')) {
        $schema['central']['users_accounts']['items']['users_approval'] = [
            'href' => 'profiles.manage&status=P',
            'position' => 900,
            'count_function' => 'getUserNewCount',
        ];
    }

    $schema['central']['vendors']['items']['commissions'] = [
        'href' => 'admin_Commission_manage',
        'position' => 150,
        'symfony' => true,
    ];

    $schema['central']['vendors']['items']['invoicing_settings'] = [
        'href' => 'admin_invoicing_settings',
        'position' => 150,
        'symfony' => true,
    ];

    $schema['central']['vendors']['items']['report'] = [
        'href' => 'financial_flows_history',
        'position' => 150,
        'symfony' => true,
        'subitems' => [
            'financial_flows_history' => [
                'href' => 'financial_flows_history',
                'symfony' => true,
                'position' => 100,
            ],
            'commission_monitoring' => [
                'href' => 'commission_monitoring',
                'symfony' => true,
                'position' => 300,
            ],
        ]
    ];
}

unset($schema['top']['administration']['items']['shippings_taxes']['subitems']['localizations']);

if (\Tygh\Registry::get('config.sandbox')) {
    $schema['central']['w_sandbox'] = [
        'items' => [
            'w_create_order' => [
                'href' => 'w_order_management.update',
                'position' => 100,
            ],
        ],
        'position' => 10000
    ];
}

$prismicContent = container()->getParameter('config.prismic_content');
if (container()->getParameter('feature.enable_bfo') && \is_null($prismicContent) === false && $prismicContent !== '') {
    $schema['central']['content'] = [
        'items' => [
            [
                'href' => $prismicContent,
                'icon_class' => 'wiz-content',
                'menu_class' => 'prismic-content',
                'position' => 100,
                'target' => '_blank'
            ],
        ],
        'position' => 500
    ];
    unset($schema['central']['marketing']['items']['banners']);
}


if (true === boolval(container()->getParameter('feature.sandbox'))) {
    $schema['top']['administration']['items']['sandbox_disk_space'] = [
        'href' => 'admin_sandbox_disk_usage',
        'position' => 1475,
        'symfony' => true,
    ];
}

return $schema;
