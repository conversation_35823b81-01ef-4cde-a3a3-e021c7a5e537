<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>nev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Wizacha\Marketplace\Seo\Slug\SlugGenerator;

$d = SlugGenerator::DELIMITER;

$schema = !empty($schema) ? $schema : array();

$general = array(
    '\'' => '',
    '"' => '',
    '&' => $d . 'and' . $d,
    '?' => '-',
    ' ' => '-',
    '/' => '-',
    '(' => '-',
    ')' => '-',
    '[' => '-',
    ']' => '-',
    '%' => '-',
    '#' => '-',
    ',' => '-',
    ':' => '-',
);

return array_merge($schema, $general);
