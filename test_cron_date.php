<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

namespace Wizacha\Marketplace\Subscription;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use Wizacha\Component\Pagination\Pagination;
use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Exception\OrderCorruptedException;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\User\User;
use Wizacha\Money\Money;

Class TestCronDate extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry, $entityClass)
    {
        parent::__construct($registry, $entityClass);
    }

    public function checkCronDates(): array
    {
        $today = (new \DateTimeImmutable())->setTime(0, 0, 0);
        $tomorrow = $today->modify("+1 day");
        $queryBuilder = $this->createQueryBuilder("subscription");

        return $queryBuilder
            ->where(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->in('subscription.status', ':renewTodayStatus'),
                        $queryBuilder->expr()->gte('subscription.nextPaymentAt', ':today'),
                        $queryBuilder->expr()->lt('subscription.nextPaymentAt', ':tomorrow')
                    ),
                    $queryBuilder->expr()->eq("subscription.status", ":defaulted")
                )
            )
            ->andWhere($queryBuilder->expr()->orX(
                $queryBuilder->expr()->eq('subscription.isAutoRenew', 1),
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->eq('subscription.isAutoRenew', 0),
                    $queryBuilder->expr()->gte('subscription.commitmentEndAt', ':today')
                )
            ))
            ->setParameters([
                ':renewTodayStatus' => [
                    SubscriptionStatus::ACTIVE()->getValue(),
                    SubscriptionStatus::DEFAULTED()->getValue(),
                    SubscriptionStatus::WAITING_RENEW()->getValue(),
                ],
                ':defaulted' => SubscriptionStatus::DEFAULTED()->getValue(),
                ':today' => $today->format("Y-m-d H:i:s"),
                ':tomorrow' => $tomorrow->format("Y-m-d H:i:s"),
            ])
            ->getQuery()
            ->getResult()
            ;
    }
}

$test = new TestCronDate();

//var_dump($test->checkCronDates());
