##### GENERAL ####

# Required metadata
sonar.projectKey=wizaplace_wizaplace
sonar.organization=wizaplace
sonar.projectName=Wizaplace Back-End V1
sonar.projectDescription=Wizaplace Back-End Application V1

# Encoding of the source files
sonar.sourceEncoding=UTF-8

# Sources
sonar.sources=.
sonar.exclusions=out/**,docs/**,vendor/**,var/**,images/**,.circleci/**,.github/**,.platform/**,tests/**,php-sdk/tests/**,migrations/**,src/AppBundle/Smarty/**
sonar.tests=tests/,php-sdk/tests
sonar.pullrequest.github.summary_comment=true
