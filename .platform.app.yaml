# .platform.app.yaml

# The name of this application, which must be unique within a project.
name: 'wizaplace'

# The type key specifies the language and version for your application.
type: 'php:7.4'

# On PHP, there are multiple build flavors available. Pretty much everyone
# except Dr<PERSON>al 7 users will want the composer flavor.
# on ne peut pas utiliser la "flavor" composer ou symfony dans ce cas
# car on a besoin d'écrire la config avec phing avant
build:
    flavor: none

# The relationships of the application with services or other applications.
# The left-hand side is the name of the relationship as it will be exposed
# to the application in the PLATFORM_RELATIONSHIPS variable. The right-hand
# side is in the form `<service name>:<endpoint name>`.
relationships:
    marketplace-database: 'mysql:marketplace'
    discuss-database: 'mysql:discuss'
    redis: 'redis:redis'
    rabbitmq: 'rabbitmq:rabbitmq'

# The configuration of the application when it is exposed to the web.
web:
    commands:
        start: |
            supervisord -n -c app/config/supervisord.conf
    locations:
        '/app':
            root: 'app'
            allow: false
            scripts: false
        '/bin':
            root: 'bin'
            allow: false
            scripts: false
        '/build':
            root: 'build'
            allow: false
            scripts: false
        '/design':
            root: 'design'
            allow: true
            scripts: false
            rules:
                '\.([tT][pP][lL]|[pP][hH][pP].?)$':
                    allow: false
        '/docs':
            root: 'docs'
            allow: false
            scripts: false
        '/migrations':
            root: 'migrations'
            allow: false
            scripts: false
        '/src':
            root: 'src'
            allow: false
            scripts: false
        '/var':
            root: 'var'
            allow: false
            scripts: false
        '/var/cache':
            root: 'var/cache'
            allow: true
            scripts: false
        '/vendor':
            root: 'vendor'
            allow: false
            scripts: false
        '/.global':
            root: '.global'
            allow: false
            scripts: false
        '/.subversion':
            root: '.subversion'
            allow: false
            scripts: false
        # API
        '/api/v1':
            root: ''
            passthru: '/web/app.php'
        # CSV
        '/csv/':
            root: ''
            passthru: '/web/app.php'
        # admin
        '/admin/':
            root: ''
            passthru: '/web/app.php'
        # support-login
        '/support-login':
            root: ''
            passthru: '/web/app.php'
        '/':
            headers:
                X-Frame-Options: SAMEORIGIN
            # The public directory of the application relative to its root.
            root: ''
            # The front-controller script which determines where to send
            # non-static requests.
            passthru: '/index.php'
            allow: true
            rules:
                '^/(\..*|Makefile|phinx\.php|worker\.php|build\.properties|build\.xml|config\.php|init\.php)$':
                    allow: false
                    scripts: false
                # Provide a longer TTL (2 weeks) for aggregated assets files.
                '^/assets':
                    expires: 2w

# The size of the persistent disk of the application (in MB).
disk: 512

# The 'mounts' describe writable, persistent filesystem mounts in the application.
# The keys are directory paths relative to the application root. The values are
# strings such as 'shared:files/NAME' where NAME is just a unique name for the mount.
mounts:
    '/var': 'shared:files/var'
    '/images': 'shared:files/images'

# The hooks that will be triggered when the package is deployed.
hooks:
    # Build hooks can modify the application files on disk but not access any services like databases.
    build: |
        set -eu
        buildID=$(cat /proc/sys/kernel/random/uuid)
        echo "[`date --iso-8601=seconds`] build_start ${PLATFORM_PROJECT}.${PLATFORM_TREE_ID}.${buildID}"
        echo "${buildID}" > /app/app/buildID

        # config
        # Comment these 8 lines if you already have the correct content for app/config/parameters.yml, app/config/parameters.doctrine.yml and app/config/theme.php
        for i in $(seq 0 $((${PHING_PROPERTIES_LENGTH:-1} - 1))); do \
            if [ $i -eq 0 ]; then \
                CONFIG_BASE64=${PHING_PROPERTIES}; \
            else \
                var_name="PHING_PROPERTIES_${i}"; \
                CONFIG_BASE64="${CONFIG_BASE64}$(eval echo \$${var_name})"; \
            fi \
        done

        # Comment these 2 lines if you already have the correct content for app/config/parameters.yml, app/config/parameters.doctrine.yml and app/config/theme.php
        echo ${CONFIG_BASE64} | base64 -d > build.properties
        phing -S config < /dev/null

        # node version
        wget -q https://nodejs.org/dist/v${NODE_VERSION}/node-v${NODE_VERSION}-linux-x64.tar.gz
        tar xfz node-v${NODE_VERSION}-linux-x64.tar.gz
        rm -f node-v${NODE_VERSION}-linux-x64.tar.gz
        mv node-v${NODE_VERSION}-linux-x64 .node
        export PATH="${HOME}/.node/bin:${PATH}"

        # fixed composer version
        FIXED_COMPOSER_DIR=${HOME}/fixed_composer
        FIXED_COMPOSER_PATH=${FIXED_COMPOSER_DIR}/composer
        mkdir -p ${FIXED_COMPOSER_DIR}
        curl -fsSL https://getcomposer.org/composer.phar -o ${FIXED_COMPOSER_PATH}
        chmod +x ${FIXED_COMPOSER_PATH}
        export PATH="${FIXED_COMPOSER_DIR}:${PATH}"

        # vendor
        # Comment this line if your composer doesn't need access to Wizacha private repositories
        echo -e "machine github.com\n  login $GITHUB_TOKEN" >> ~/.netrc
        composer self-update --1
        composer --no-ansi --no-interaction install --no-progress --prefer-dist --optimize-autoloader --classmap-authoritative --no-dev
        npm install --production --no-save --quiet

        # post-install
        phing -S symfonyConsoleEnvProd symfony.cache < /dev/null
        bin/console theme:assets:install --bypass-legacy-init --off-line --no-ansi
        FRONTEND_THEME=remotefront node_modules/.bin/gulp back:style
        FRONTEND_THEME=remotefront node_modules/.bin/gulp back:script:prod
        node_modules/swagger-cli/swagger-cli.js bundle -o docs/api/swagger.json docs/api/swagger.yaml

        bin/console admin:install --env=prod --no-ansi

        # cleanup
        find -maxdepth 1 -path './.*' -type f ! -name '.platform.app.yaml' -delete
        rm -rf composer.* phpunit.xml.dist gulpfile.js package.json package-lock.json README.md build.properties.dist copyright.txt tests .github web/app_dev.php node_modules php-sdk

        # filebeat
        wget -q https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-${FILEBEAT_VERSION}-linux-x86_64.tar.gz
        tar xfz filebeat-${FILEBEAT_VERSION}-linux-x86_64.tar.gz
        cp filebeat-${FILEBEAT_VERSION}-linux-x86_64/filebeat bin/filebeat
        rm -rf filebeat-${FILEBEAT_VERSION}-linux-x86_64 filebeat-${FILEBEAT_VERSION}-linux-x86_64.tar.gz

        # platform.sh cli
        curl -sS https://platform.sh/cli/installer | php

        echo 'export PATH="${HOME}/.node/bin:${PATH}"' >> .environment
        echo "[`date --iso-8601=seconds`] build_end ${PLATFORM_PROJECT}.${PLATFORM_TREE_ID}.${buildID}"
    # Deploy hooks can access services but the file system is now read-only.
    deploy: |
        set -eu
        deployID="$(cat /app/app/buildID).$(cat /proc/sys/kernel/random/uuid)"
        echo "[`date --iso-8601=seconds`] deploy_start ${PLATFORM_PROJECT}.${PLATFORM_TREE_ID}.${deployID}"

        # supervisor logs
        mkdir -p var/supervisor

        # cache clear
        mv var/cache/prod var/cache/old
        bin/console cache:clear --no-ansi --bypass-legacy-init
        rm -rf var/cache/old

        # migration and post-deploy
        phing -S symfonyConsoleEnvProd symfonyConsoleNoAnsi migration.migrate < /dev/null
        bin/console deploy:post-actions --no-ansi
        echo "[`date --iso-8601=seconds`] deploy_end ${PLATFORM_PROJECT}.${PLATFORM_TREE_ID}.${deployID}"

runtime:
    extensions:
        - redis
        - amqp
    sizing_hints:
        # average request memory, not the maximum memory available for a request
        # in a php-fpm worker
        request_memory: 30

dependencies:
    ruby:
        # Outil en CLI pratique pour debug rabbitmq
        amqp-utils: "0.5.1"
    php:
        phing/phing: "2.*"
        wizaplace/php-fpm-status-cli: "^1.0"
    python:
        supervisor: "*"

timezone: 'Europe/Paris'

variables:
    env:
        REQUEST_ID_HEADER_NAME: 'x-correlation-id'
        FILEBEAT_VERSION: '7.2.0'
        NODE_VERSION: '10.19.0'
    php:
        max_input_vars: 6000
        precision: -1
        serialize_precision: -1

crons:
    clear-authlogs: # only on platformsh
        spec: '0 1 * * *' # “At 01:00.”
        cmd: 'bin/console clear:authlogs -n -q'
    clear-eximjobs: # only on platformsh
        spec: '0 12 * * 0' # “At 12:00 on Sunday.”
        cmd: 'bin/console clear:exim-jobs -n -q'
    update-orders-to-completed:
        spec: '0 0 * * *'
        cmd: 'bin/console orders:update-to-completed'
    mark-orders-as-delivered:
        spec: '20 * * * *'
        cmd: 'bin/console orders:mark-as-delivered'
    end-orders-withdrawal-period:
        spec: '30 * * * *'
        cmd: 'bin/console orders:end-withdrawal-period'
    dispatch-funds:
        spec: '40 * * * *'
        cmd: 'bin/console orders:dispatch-funds'
# not on platformsh
#    import-automated-feeds:
#        spec: '0 3 * * *'
#        cmd: 'bin/console csv:import-automated-feeds'
    generate-sitemap:
        spec: '0 8 * * *'
        cmd: 'bin/console sitemap:generate'
    prediggo-export:
        spec: '15 23 * * *'
        cmd: 'bin/console prediggo:export'
    recount-products:
        spec: '0 7 * * *'
        cmd: 'bin/console categories:recount-products'
    promotions:
        spec: '0 0 * * *'
        cmd: 'bin/console search:refresh-promotions'
    daily_logrotate:
        spec: '0 4 * * *'
        cmd: 'cd var/logs && gzip -c prod.json > prod.json.$(date "+%Y-%m-%d_%H-%M-%S").gz && echo -n > prod.json && find . -type f -name "*.gz" -mtime +7 -delete'
    trash-abandoned-orders:
        spec: '13 1 * * *'
        cmd: 'bin/console orders:trash-abandoned'
    payout-companies:
        spec: '0 1 * * *'
        cmd: 'bin/console company:payout:trigger'
    pay-payment-deferment-orders:
        spec: '0 * * * *'
        cmd: 'bin/console orders:pay-payment-deferment-orders'
    check-acceptation-delay:
        spec: '*/10 * * * *'
        cmd: 'bin/console orders:check-acceptation-delay'
    psp_check_kyc:
        spec: '0 23 * * *'
        cmd: 'bin/console psp:check-kyc'
    snapshot:
        # Take a snapshot automatically every night at 3 am (UTC).
        spec: '15 2 * * *'
        cmd: |
            if [ "$PLATFORM_BRANCH" = master ]; then
                platform snapshot:create --yes --no-wait
            fi
    renewcert:
        # Force a redeploy at 9 am (UTC) on the 14th of every month.
        spec: '0 9 14 * *'
        cmd: |
            if [ "$PLATFORM_BRANCH" = master ]; then
                platform redeploy --yes --no-wait
            fi
    export-catalog:
        spec: '0 6 * * *'
        cmd: 'bin/console export:catalog'
    purge-auth-log:
        spec: '30 3 * * *'
        cmd: 'bin/console purge:authlogs'
    renew-subscriptions:
        spec: '0 12 * * *'
        cmd: 'bin/console subscription:renew'
    psp-check-incomplete-orders:
        spec: '0 * * * *'
        cmd: 'bin/console psp:check-incomplete-orders'
    discounts-marketplace-update:
        spec: '30 23 * * *'
        cmd: 'bin/console discounts:marketplace:update'
    refresh-translation-cache:
        spec: '*/10 * * * *'
        cmd: 'bin/console translation:cache:refresh'
    commissions-check-config:
        spec: '30 * * * *'
        cmd: 'bin/console commissions:check:config'
    currency-rates-update:
        spec: '15 3 * * *'
        cmd: 'bin/console currency:rates:update'
    algolia-config-push:
        spec: '0 */1 * * *'
        cmd: 'bin/console algolia:config:push'
    kpi-publish:
        spec: '0 12 1 * *'
        cmd: 'bin/console kpi:publish'
